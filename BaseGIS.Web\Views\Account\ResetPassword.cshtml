﻿@model BaseGIS.Web.ViewModels.ResetPasswordViewModel
@{
    ViewData["Title"] = "بازنشانی رمز عبور";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}
<div class="container-fluid login-container">
    <div class="row min-vh-100">
        <!-- Left Section -->
        <div class="col-md-6 d-flex align-items-center justify-content-center text-white p-5">
            <div>
                <h4 class="mb-3">
                    <i class="fas fa-cube me-2"></i> LandInquiryApp
                </h4>
                <h1 class="mb-4">بازنشانی رمز عبور</h1>
                <p class="lead mb-4">
                    رمز عبور جدید خود را وارد کنید تا حساب کاربری شما به‌روزرسانی شود.
                </p>
                <a href="#" class="text-white text-decoration-none fw-bold">بیشتر بدانید <i class="fas fa-arrow-right ms-2"></i></a>
                <footer class="mt-5 text-white-50 small">
                    1404 © LandInquiryApp
                </footer>
            </div>
        </div>

        <!-- Right Section (Reset Password Form) -->
        <div class="col-md-6 d-flex align-items-center justify-content-center p-5">
            <div class="card shadow-lg login-card">
                <div class="card-body p-5">
                    <div class="text-end mb-4">
                        <a asp-action="Login" class="text-muted small">بازگشت به ورود</a>
                    </div>
                    <form asp-action="ResetPassword" method="post">
                        <input type="hidden" asp-for="Code" />
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        <div class="mb-4">
                            <label asp-for="Email" class="form-label"></label>
                            <input asp-for="Email" class="form-control" placeholder="ایمیل شما" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>
                        <div class="mb-4">
                            <label asp-for="Password" class="form-label"></label>
                            <input asp-for="Password" class="form-control" placeholder="رمز عبور جدید" />
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>
                        <div class="mb-4">
                            <label asp-for="ConfirmPassword" class="form-label"></label>
                            <input asp-for="ConfirmPassword" class="form-control" placeholder="تأیید رمز عبور" />
                            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                        </div>
                        <div class="d-flex gap-2">
                            <a asp-action="Login" class="btn btn-secondary">لغو</a>
                            <button type="submit" class="btn btn-primary flex-fill">بازنشانی رمز عبور</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}