/**
 * Chart Manager Module
 * مدیریت نمودارها
 */

class ChartManager {
    constructor() {
        this.charts = new Map();
        this.defaultOptions = this.getDefaultOptions();
    }

    /**
     * تنظیمات پیش‌فرض نمودار
     */
    getDefaultOptions() {
        return {
            grid: { top: 30, right: 20, bottom: 30, left: 20 },
            tooltip: { trigger: 'axis' },
            legend: { type: 'scroll', orient: 'horizontal', left: 'center', top: 'bottom' },
            toolbox: {
                feature: {
                    saveAsImage: { title: 'ذخیره تصویر' },
                    dataView: { title: 'نمایش داده‌ها', readOnly: false },
                    magicType: { title: 'تغییر نوع', type: ['line', 'bar'] },
                    restore: { title: 'بازگردانی' }
                }
            }
        };
    }

    /**
     * ایجاد نمودار
     */
    createChart(containerId, chartConfig) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`Container with id ${containerId} not found`);
            return null;
        }

        // Initialize ECharts instance
        const chart = echarts.init(container);
        
        // Generate chart options
        const options = this.generateChartOptions(chartConfig);
        
        // Set options
        chart.setOption(options, true);
        
        // Bind events
        this.bindChartEvents(chart, chartConfig);
        
        // Store chart instance
        this.charts.set(containerId, chart);
        
        // Handle resize
        this.handleResize(chart);
        
        return chart;
    }

    /**
     * تولید تنظیمات نمودار
     */
    generateChartOptions(config) {
        const { type, data, labels, series, colors, showValues, grid, angle } = config;
        
        let options = { ...this.defaultOptions };
        
        // Set grid
        if (grid) {
            options.grid = this.parseGrid(grid);
        }
        
        // Set colors
        if (colors && colors.length > 0) {
            options.color = colors;
        }
        
        switch (type) {
            case 'line':
                return this.generateLineChart(options, config);
            case 'bar':
                return this.generateBarChart(options, config);
            case 'pie':
                return this.generatePieChart(options, config);
            case 'gauge':
                return this.generateGaugeChart(options, config);
            case 'treemap':
                return this.generateTreemapChart(options, config);
            default:
                return this.generateLineChart(options, config);
        }
    }

    /**
     * تولید نمودار خطی
     */
    generateLineChart(baseOptions, config) {
        const { labels, series, showValues, angle } = config;
        
        return {
            ...baseOptions,
            xAxis: {
                type: 'category',
                data: labels,
                axisLabel: {
                    rotate: angle || 0,
                    interval: 0
                }
            },
            yAxis: {
                type: 'value'
            },
            series: series.map(s => ({
                name: s.name,
                type: 'line',
                data: s.data,
                label: {
                    show: showValues || false,
                    position: 'top',
                    formatter: (params) => this.formatNumber(params.value)
                }
            }))
        };
    }

    /**
     * تولید نمودار ستونی
     */
    generateBarChart(baseOptions, config) {
        const { labels, series, showValues, angle } = config;
        
        return {
            ...baseOptions,
            xAxis: {
                type: 'category',
                data: labels,
                axisLabel: {
                    rotate: angle || 0,
                    interval: 0
                }
            },
            yAxis: {
                type: 'value'
            },
            series: series.map(s => ({
                name: s.name,
                type: 'bar',
                data: s.data,
                label: {
                    show: showValues || false,
                    position: 'top',
                    formatter: (params) => this.formatNumber(params.value)
                }
            }))
        };
    }

    /**
     * تولید نمودار دایره‌ای
     */
    generatePieChart(baseOptions, config) {
        const { labels, data, showValues } = config;
        
        const pieData = labels.map((label, index) => ({
            name: label,
            value: data[0].data[index]
        }));
        
        return {
            ...baseOptions,
            series: [{
                type: 'pie',
                radius: '50%',
                data: pieData,
                label: {
                    show: showValues || false,
                    formatter: '{b}: {c} ({d}%)'
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        };
    }

    /**
     * تولید نمودار سرعت‌سنج
     */
    generateGaugeChart(baseOptions, config) {
        const { data, labels } = config;
        
        return {
            ...baseOptions,
            series: [{
                type: 'gauge',
                data: data.map((value, index) => ({
                    value: value,
                    name: labels[index] || `مقدار ${index + 1}`
                }))
            }]
        };
    }

    /**
     * تولید نمودار مستطیلی
     */
    generateTreemapChart(baseOptions, config) {
        const { labels, data } = config;
        
        const treemapData = labels.map((label, index) => ({
            name: label,
            value: data[0].data[index]
        }));
        
        return {
            ...baseOptions,
            series: [{
                type: 'treemap',
                data: treemapData
            }]
        };
    }

    /**
     * اتصال رویدادهای نمودار
     */
    bindChartEvents(chart, config) {
        // Click event
        chart.on('click', (params) => {
            this.handleChartClick(params, config);
        });
        
        // Double click event
        chart.on('dblclick', (params) => {
            this.handleChartDoubleClick(params, config);
        });
    }

    /**
     * مدیریت کلیک روی نمودار
     */
    handleChartClick(params, config) {
        if (config.onItemClick && typeof config.onItemClick === 'function') {
            config.onItemClick(params, config);
        }
        
        // Default behavior - load child panels
        if (config.panelId && config.hasChildren) {
            const panelData = {
                panelId: config.panelId,
                input: config.idLabels ? config.idLabels[params.dataIndex] : '',
                input1: config.titleLabels ? config.titleLabels[params.dataIndex] : '',
                input2: config.valueLabels ? config.valueLabels[params.dataIndex] : '',
                input3: config.valueLabels1 ? config.valueLabels1[params.dataIndex] : '',
                input4: config.valueLabels2 ? config.valueLabels2[params.dataIndex] : '',
                input5: config.valueLabels3 ? config.valueLabels3[params.dataIndex] : '',
                input6: config.valueLabels4 ? config.valueLabels4[params.dataIndex] : '',
                input7: config.valueLabels5 ? config.valueLabels5[params.dataIndex] : '',
                level: config.level || 1
            };
            
            if (window.dashboardCore) {
                window.dashboardCore.loadChildPanels(panelData);
            }
        }
    }

    /**
     * مدیریت دابل کلیک روی نمودار
     */
    handleChartDoubleClick(params, config) {
        // Custom double click behavior
        if (config.onItemDoubleClick && typeof config.onItemDoubleClick === 'function') {
            config.onItemDoubleClick(params, config);
        }
    }

    /**
     * مدیریت تغییر اندازه
     */
    handleResize(chart) {
        const resizeObserver = new ResizeObserver(() => {
            chart.resize();
        });
        
        resizeObserver.observe(chart.getDom());
        
        // Also handle window resize
        window.addEventListener('resize', () => {
            chart.resize();
        });
    }

    /**
     * تجزیه تنظیمات Grid
     */
    parseGrid(gridString) {
        const parts = gridString.split(',');
        return {
            top: parseInt(parts[0]) || 30,
            right: parseInt(parts[1]) || 20,
            bottom: parseInt(parts[2]) || 30,
            left: parseInt(parts[3]) || 20
        };
    }

    /**
     * فرمت کردن اعداد
     */
    formatNumber(value) {
        if (typeof value !== 'number') return value;
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }

    /**
     * بروزرسانی نمودار
     */
    updateChart(containerId, newConfig) {
        const chart = this.charts.get(containerId);
        if (chart) {
            const options = this.generateChartOptions(newConfig);
            chart.setOption(options, true);
        }
    }

    /**
     * حذف نمودار
     */
    destroyChart(containerId) {
        const chart = this.charts.get(containerId);
        if (chart) {
            chart.dispose();
            this.charts.delete(containerId);
        }
    }

    /**
     * تغییر اندازه تمام نمودارها
     */
    resizeAllCharts() {
        this.charts.forEach(chart => {
            chart.resize();
        });
    }

    /**
     * دریافت نمودار
     */
    getChart(containerId) {
        return this.charts.get(containerId);
    }

    /**
     * دریافت تمام نمودارها
     */
    getAllCharts() {
        return Array.from(this.charts.values());
    }
}

// Global instance
window.chartManager = new ChartManager();
