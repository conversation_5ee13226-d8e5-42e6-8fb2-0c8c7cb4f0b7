!function(e){"function"==typeof define&&define.amd?define(["exports"],e):"object"==typeof exports&&"string"!=typeof exports.nodeName?e(exports):e({})}(function(e){var a,r={time:{month:["Janeiro","Fevereiro","Mar<PERSON><PERSON>","<PERSON>bril","<PERSON><PERSON>","Jun<PERSON>","Jul<PERSON>","Agosto","Setembro","Outubro","Novembro","Dezembro"],monthAbbr:["<PERSON>","<PERSON>v","<PERSON>","Abr","<PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","Set","Out","Nov","Dez"],dayOfWeek:["Domingo","Segunda-feira","Terça-feira","Quarta-feira","Quinta-feira","Sexta-feira","Sábado"],dayOfWeekAbbr:["<PERSON>","Seg","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON><PERSON>"]},legend:{selector:{all:"Todas",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Seleção retangular",polygon:"<PERSON>leção em laço",lineX:"Selecionar horizontalmente",lineY:"Selecionar verticalmente",keep:"Manter seleções",clear:"Limpar seleções"}},dataView:{title:"Exibição de dados",lang:["Exibição de dados","Fechar","Atualizar"]},dataZoom:{title:{zoom:"Zoom",back:"Restaurar Zoom"}},magicType:{title:{line:"Trocar para gráfico de linhas",bar:"Trocar para gráfico de barras",stack:"Empilhar",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Salvar como imagem",lang:["Clique com o botão direito para salvar imagem"]}},series:{typeNames:{pie:"Gráfico de pizza",bar:"Gráfico de barras",line:"Gráfico de linhas",scatter:"Gráfico de dispersão",effectScatter:"Gráfico de dispersão ondulado",radar:"Gráfico radar",tree:"Árvore",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"Gráfico K line",heatmap:"Mapa de calor",map:"Mapa",parallel:"Coordenadas paralelas",lines:"Gráfico de linhas",graph:"Relationship graph",sankey:"Gráfico Sankey",funnel:"Gráfico de funil",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst"}},aria:{general:{withTitle:'Este é um gráfico entitulado "{title}"',withoutTitle:"Este é um gráfico"},series:{single:{prefix:"",withName:" do tipo {seriesType} nomeada/nomeado como {seriesName}.",withoutName:" do tipo {seriesType}."},multiple:{prefix:". Consiste de {seriesCount} séries.",withName:" A {seriesId} série é um/uma {seriesType} representando {seriesName}.",withoutName:" A {seriesId} series é um/uma {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"Os dados são: ",partialData:"As primeiros {displayCnt} itens são: ",withName:"os dados para {name} são {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}};for(a in r)r.hasOwnProperty(a)&&(e[a]=r[a])});