@using BaseGIS.Core.Entities
@using Microsoft.AspNetCore.Http
@model List<IGrouping<int?, DashboardPanel>>

@{
    // ===== تنظیمات صفحه =====
    Layout = "~/Views/Shared/_Dashboard.cshtml";
    ViewBag.Title = "Dashboard";

    // ===== متغیرهای کاربری =====
    ApplicationUser applicationUser = ViewBag.CurrentUser;
    var message = ViewBag.Message;
    var userGuid = Context.Session.GetString("UserGuid");
    bool? isAdmin = ViewBag.isAdmin ?? false;

    // ===== داده‌های داشبورد =====
    List<Dashboard> Dashboards = ViewBag.Dashboards ?? new List<Dashboard>();
    Dashboard Dashboard = ViewBag.Dashboard ?? new Dashboard();
    string DashboardID = ViewBag.ID ?? "0";

    // ===== داده‌های پنل‌ها =====
    Dictionary<int, string> Panels = ViewBag.Panels ?? new Dictionary<int, string>();
    var PanelsKey = Panels.Keys.ToList();

    // ===== داده‌های کمکی =====
    List<DashboardColor> colors = ViewBag.colors ?? new List<DashboardColor>();
    List<DashboardSQL> SQLs = ViewBag.SQL ?? new List<DashboardSQL>();
    List<int> UsedIDs = ViewBag.UsedIDs ?? new List<int>();
    List<FilterField> FilterByFields = ViewBag.FilterByFields ?? new List<FilterField>();
}

@section styles {
    <!-- ===== کتابخانه‌های CSS از LibMan ===== -->
    <link rel="stylesheet" href="~/lib/select2/css/select2.min.css">
    <link rel="stylesheet" href="~/lib/sweetalert2/sweetalert2.min.css">
    <link rel="stylesheet" href="~/lib/bootstrap-icons/font/bootstrap-icons.css">

    <style>
        /* ===== استایل‌های کلی ===== */
        .activeSelect {
            background-color: cyan !important;
        }

        .PNL {
            margin: 0px !important;
            padding: 0px !important;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .fdd {
            padding: 0 5px;
            margin-bottom: -10px;
        }

        .panel-hdr {
            border-bottom: 0px;
        }

        .table td, th {
            text-align: center;
        }

        /* ===== استایل‌های Header و Navigation ===== */
        .nav-function-top .page-content-wrapper {
            margin-top: 48px;
        }

        .nav-function-top .page-header {
            height: 48px;
            background-color: #1976d2;
        }

        .header-function-fixed .page-content {
            margin-top: 0;
        }

        /* ===== استایل‌های Select2 ===== */
        .select2-container {
            z-index: 10000;
        }

        .select2-selection__choice {
            margin-right: 5px !important;
            margin-left: 1px !important;
            padding: 0 5px 0 0 !important;
        }

        .select2-selection__choice__remove {
            margin: 0 5px 0 0 !important;
            border-left: 0px solid rgba(0, 0, 0, 0.1) !important;
            border-right: 1px solid rgba(0, 0, 0, 0.1);
        }

        .select2-selection__arrow {
            left: 0.5rem;
            right: auto;
        }

        .select2-selection {
            box-shadow: 4px 4px 4px -4px rgb(0 0 0 / 84%);
            opacity: .5;
        }

        .select2-selection:hover {
            opacity: 1;
        }

        /* ===== استایل‌های فیلتر ===== */
        .filter {
            box-shadow: 4px 4px 4px -4px rgb(0 0 0 / 84%);
            opacity: .5;
        }

        .filter:hover {
            opacity: 1;
        }

        .dataTables_filter {
            padding: 10px 10px 0;
        }

        /* ===== استایل‌های کمکی ===== */
        #topBtn {
            display: none;
            position: fixed !important;
            bottom: 20px;
            right: 30px;
            z-index: 99;
        }

        sup {
            top: 33px;
            position: absolute;
            right: 7px;
            font-size: 85%;
        }

        /* ===== انیمیشن شناور ===== */
        .floating {
            animation-name: floating;
            animation-duration: 1s;
            animation-iteration-count: 1;
            animation-timing-function: ease-in-out;
            margin-top: 5px;
        }

        @@keyframes floating {
            0% { transform: translate(0, 0px); }
            50% { transform: translate(0, 30px); }
            100% { transform: translate(0, -0px); }
        }

        /* ===== CSS سفارشی داشبورد ===== */
        @if (!string.IsNullOrWhiteSpace(Dashboard.CssClass))
        {
            @Html.Raw(Dashboard.CssClass)
        }
    </style>
}

<!-- ===== Loading Spinner ===== -->
<div id="loadingImg" style="display:none; position: fixed; left: 0; right: 0; width: 100px; z-index: 999999; margin: auto; text-align: center; top: 50%; margin-top: -42px;">
    <i class="fal fa-spinner fa-10x fa-spin text-info"></i>
</div>

<!-- ===== محتوای اصلی صفحه ===== -->
<main id="js-page-content" role="main" class="page-content">

    <!-- ===== بخش فیلترها ===== -->
    @if (FilterByFields.Count > 0)
    {
        <div class="row" id="rowFilter">
            <!-- دکمه تغییر وضعیت فیلتر -->
            <a href="javascript:void(0);"
               class="btn btn-default ml-auto waves-effect waves-themed btn-xs"
               onclick="toggleFilter()"
               style="padding: 0 2px; position: fixed; top: 54px; left: 12px; z-index: 10001;">
                <i id="iFilter" class="fal fa-filter" style="margin: 5px;"></i>
            </a>

            <!-- پنل فیلترها -->
            <div id="DivPanelFilter"
                 class="col-lg-12"
                 style="padding:0 5px; z-index:10000; display:@(Dashboard.FilterType == 2 || Dashboard.FilterType == 1 ? "block" : "none")">

                <div class="panel" style="background-color: #edf2f9;">
                    <div class="panel-container">
                        <div class="panel-content" style="padding: 10px !important; max-height: 143px; overflow-y: auto; direction: ltr;">
                            <div class="form-row" style="margin-bottom: 0px; direction: rtl;">

                                <!-- فیلدهای فیلتر -->
                                <div class="col-md-10">
                                    <div class="form-row" style="margin-bottom: 0px">
                                        @* ===== تولید فیلدهای فیلتر ===== *@
                                        @for (int i = 0; i < FilterByFields.Count; i++)
                                        {
                                            var f = FilterByFields[i];
                                            if (f != null && f.TableInfo != null)
                                            {
                                                var fieldName = (f.TableInfo.Name + "." + f.Name).ToLower();
                                                var fieldLabel = f.Alias ?? f.AliasName;

                                                @* فیلتر Domain (لیست از پیش تعریف شده) *@
                                                if (f.FieldType == "domain")
                                                {
                                                    <div class="col-md-3 colFilter" id="col@fieldName">
                                                        <label class="form-label">@fieldLabel</label>
                                                        <select class="select2 form-control filter" name="@fieldName" multiple>
                                                            @if (f.DomainInfos != null)
                                                            {
                                                                @for (int j = 0; j < f.DomainInfos.Count; j++)
                                                                {
                                                                    <option value="@f.DomainInfos[j].Name">@f.DomainInfos[j].Name</option>
                                                                }
                                                            }
                                                        </select>
                                                    </div>
                                                }
                                                @* فیلتر Boolean (دارد/ندارد) *@
                                                else if (f.FieldType == "bool")
                                                {
                                                    <div class="col-md-3 colFilter" id="col@fieldName">
                                                        <label class="form-label">@fieldLabel</label>
                                                        <select class="select2 form-control filter" name="@fieldName">
                                                            <option></option>
                                                            <option value="1">دارد</option>
                                                            <option value="0">ندارد</option>
                                                        </select>
                                                    </div>
                                                }
                                                @* فیلتر عددی (از - تا) *@
                                                else if (f.FieldType == "int" || f.FieldType == "float")
                                                {
                                                    <div class="col-md-3 colFilter" id="col@fieldName">
                                                        <label class="form-label">@fieldLabel</label>
                                                        <div class="input-group">
                                                            <input type="text" aria-label="از" class="form-control filter"
                                                                   placeholder="از" name="min__@fieldName"
                                                                   list='suggestions_@fieldName'>
                                                            <input type="text" aria-label="تا" class="form-control filter"
                                                                   placeholder="تا" name="max__@fieldName"
                                                                   list='suggestions_@fieldName'>
                                                        </div>
                                                        @* لیست پیشنهادات *@
                                                        @if (f.SuggestionList != null && f.SuggestionList.Count > 0)
                                                        {
                                                            <datalist id="suggestions_@fieldName">
                                                                @for (int ii = 0; ii < f.SuggestionList.Count; ii++)
                                                                {
                                                                    <option value="@f.SuggestionList[ii]"></option>
                                                                }
                                                            </datalist>
                                                        }
                                                    </div>
                                                }
                                                @* فیلتر متنی (Ajax Select) *@
                                                else if (f.FieldType == "nvarchar")
                                                {
                                                    <div class="col-md-3 colFilter" id="col@fieldName">
                                                        <label class="form-label">@fieldLabel</label>
                                                        <select class="selectAjax form-control filter" name="@fieldName" multiple>
                                                        </select>
                                                    </div>
                                                }
                                            }
                                        }
                                    </div>
                                </div>

                                <!-- دکمه‌های کنترل فیلتر -->
                                <div class="col-md-2" style="max-height: 125px; position: static;">
                                    <!-- دکمه پاک کردن فیلتر -->
                                    <a href="javascript:void(0);"
                                       class="btn btn-danger ml-auto"
                                       onclick="ClearFilter();"
                                       style="left: 0; margin: 5px; padding: 5px 20px; position: absolute; top: 30px;">
                                        <i class="fal fa-minus"></i> پاک کردن
                                    </a>

                                    <!-- دکمه اعمال فیلتر -->
                                    <a href="javascript:void(0);"
                                       class="btn btn-primary ml-auto"
                                       onclick="Filter();"
                                       style="left: 60px; margin: 5px; padding: 5px 20px; position: absolute; top: 30px;">
                                        <i class="fal fa-filter"></i> فیلتر
                                    </a>

                                    <!-- دکمه فیلترهای بیشتر -->
                                    <a href="javascript:void(0);"
                                       class="btn btn-default ml-auto"
                                       id="moreFilter"
                                       onclick="moreFilter();"
                                       style="left: 0px; margin: 5px; padding: 5px 20px; position: absolute; top: 70px; display:none">
                                        بیشتر
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <br />
    }

    <!-- ===== محل نمایش پنل‌های داشبورد ===== -->
    <div class="row" id="PanelsDiv">
        <!-- پنل‌ها به صورت داینامیک از طریق JavaScript بارگذاری می‌شوند -->
    </div>
</main>


<!-- ===== Modal فیلتر ===== -->
<div id="FilterModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-right modal-sm">
        <div class="modal-content">
            <!-- Header Modal -->
            <div class="modal-header">
                <h5 class="modal-title h4 text-white">
                    <i class="fal fa-filter mr-2"></i>فیلتر پیشرفته
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                </button>
            </div>

            <!-- Body Modal -->
            <div class="modal-body">
                <form class="needs-validation" method="post" action="#" id="FilterForm">
                    <div class="panel-content">
                        <div class="form-row">
                            <!-- فیلدهای فیلتر اضافی در صورت نیاز اینجا اضافه می‌شوند -->
                            <div class="col-12">
                                <p class="text-muted">
                                    <i class="fal fa-info-circle mr-1"></i>
                                    از فیلترهای بالای صفحه برای جستجوی سریع استفاده کنید.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Footer Modal -->
                    <div class="panel-content border-faded border-left-0 border-right-0 border-bottom-0 d-flex flex-row align-items-center">
                        <a href="javascript:void(0);" class="btn btn-primary ml-auto" onclick="Filter();">
                            <i class="fal fa-filter mr-1"></i>اعمال فیلتر
                        </a>
                        <a href="javascript:void(0);" class="btn btn-danger ml-auto" onclick="ClearFilter();">
                            <i class="fal fa-times mr-1"></i>پاک کردن
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<!-- ===== Modal ویرایش پنل ===== -->
<div id="PanelModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-right modal-sm">
        <div class="modal-content">
            <!-- Header Modal -->
            <div class="modal-header">
                <h5 class="modal-title h4 text-white">
                    <i class="fal fa-edit mr-2"></i>ویرایش پنل
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                </button>
            </div>

            <!-- Body Modal -->
            <div class="modal-body">
                <form class="needs-validation" method="post" action="/Dashboard/EditPanel" id="PanelForm">
                    @Html.AntiForgeryToken()

                    <div class="panel-content">
                        <div class="form-row">
                            <!-- فیلدهای مخفی -->
                            <input id="ID" name="ID" type="hidden" />
                            <input id="DashboardID" name="DashboardID" type="hidden" value="@DashboardID" />

                            <!-- اطلاعات اصلی پنل -->
                            <div class="col-md-12 mb-3">
                                <label class="form-label" for="Name">
                                    <i class="fal fa-tag mr-1"></i>نام پنل <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="Name" name="Name"
                                       placeholder="نام پنل را وارد کنید" value="" required>
                            </div>

                            <div class="col-md-12 mb-3">
                                <label class="form-label" for="Name2">
                                    <i class="fal fa-tags mr-1"></i>نام دوم <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="Name2" name="Name2"
                                       placeholder="نام دوم پنل را وارد کنید" value="" required>
                            </div>
                            <!-- تنظیمات چیدمان -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label" for="Group">
                                    <i class="fal fa-layer-group mr-1"></i>گروه
                                </label>
                                <input type="number" class="form-control" id="Group" name="Group"
                                       placeholder="شماره گروه" value="" min="0">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label" for="Order">
                                    <i class="fal fa-sort-numeric-down mr-1"></i>ترتیب
                                </label>
                                <input type="number" class="form-control" id="Order" name="Order"
                                       placeholder="ترتیب نمایش" value="" min="1">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label" for="Size">
                                    <i class="fal fa-expand-arrows-alt mr-1"></i>عرض پنل
                                </label>
                                <select class="form-control" name="Size" id="Size">
                                    <option value="2">کوچک (2/12)</option>
                                    <option value="3">کوچک+ (3/12)</option>
                                    <option value="4">متوسط- (4/12)</option>
                                    <option value="6">متوسط (6/12)</option>
                                    <option value="8">بزرگ- (8/12)</option>
                                    <option value="9">بزرگ (9/12)</option>
                                    <option value="10">بزرگ+ (10/12)</option>
                                    <option value="12">تمام عرض (12/12)</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label" for="SizeH">
                                    <i class="fal fa-arrows-alt-v mr-1"></i>ارتفاع پنل
                                </label>
                                <select class="form-control" name="SizeH" id="SizeH">
                                    <option value="">عادی (1x)</option>
                                    <option value="0.5">کوتاه (0.5x)</option>
                                    <option value="2">دوبرابر (2x)</option>
                                    <option value="3">سه‌برابر (3x)</option>
                                    <option value="4">چهار‌برابر (4x)</option>
                                </select>
                            </div>

                            <div class="col-md-12 mb-3">
                                <label class="form-label" for="Padding">
                                    <i class="fal fa-border-style mr-1"></i>فاصله داخلی (px)
                                </label>
                                <input type="number" class="form-control" id="Padding" name="Padding"
                                       placeholder="فاصله از لبه‌ها" value="" min="0" max="50">
                            </div>
                            <!-- نوع و محتویات پنل -->
                            <div class="col-md-12 mb-3">
                                <label class="form-label" for="Type1">
                                    <i class="fal fa-cog mr-1"></i>نوع پنل <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" name="Type1" id="Type1" onchange="ChangeType1();" required>
                                    <option value="">انتخاب کنید...</option>
                                    <option value="1">📊 تکی (Single)</option>
                                    <option value="2">📋 لیست (List)</option>
                                    <option value="3">🗺️ نقشه (Map)</option>
                                </select>
                            </div>

                            <div class="col-md-12 mb-3 colType1" style="display: none;">
                                <label class="form-label" for="Type2">
                                    <i class="fal fa-chart-bar mr-1"></i>نوع محتوا <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" name="Type2" id="Type2">
                                    <option value="">انتخاب کنید...</option>
                                    <option value="1">📈 نمودار</option>
                                    <option value="2">📊 جدول</option>
                                    <option value="3">📊📈 نمودار و جدول</option>
                                </select>
                            </div>

                            <div class="col-md-12 mb-3 colType1" style="display: none;">
                                <label class="form-label" for="SQL">
                                    <i class="fal fa-database mr-1"></i>منبع داده (SQL) <span class="text-danger">*</span>
                                </label>
                                <select class="form-control" name="SQL" id="SQL">
                                    <option value="">انتخاب کنید...</option>
                                    @for (int i = 0; i < SQLs.Count; i++)
                                    {
                                        <option value="@SQLs[i].ID">@SQLs[i].Name</option>
                                    }
                                </select>
                            </div>
                            <!-- پنل‌های والد -->
                            <div class="col-md-12 mb-3">
                                <label class="form-label" for="Panels">
                                    <i class="fal fa-sitemap mr-1"></i>پنل‌های والد
                                </label>
                                <select class="select2 form-control" name="Panels" id="Panels" multiple>
                                    @for (int i = 0; i < PanelsKey.Count; i++)
                                    {
                                        <option value="@PanelsKey[i]">@Panels[PanelsKey[i]]</option>
                                    }
                                </select>
                                <small class="form-text text-muted">پنل‌هایی که این پنل به آن‌ها وابسته است</small>
                            </div>

                            <!-- آدرس وب -->
                            <div class="col-md-12 mb-3">
                                <label class="form-label" for="URL">
                                    <i class="fal fa-link mr-1"></i>آدرس وب (URL)
                                </label>
                                <input type="url" class="form-control" id="URL" name="URL"
                                       placeholder="https://example.com" value="">
                                <small class="form-text text-muted">برای پنل‌های وب‌سایت خارجی</small>
                            </div>

                            <!-- پیام و رنگ پنل -->
                            <div class="col-md-8 mb-3">
                                <label class="form-label" for="MessagePanel">
                                    <i class="fal fa-comment mr-1"></i>پیام پنل
                                </label>
                                <input type="text" class="form-control" id="MessagePanel" name="MessagePanel"
                                       placeholder="پیام نمایشی پنل" value="">
                            </div>

                            <div class="col-md-4 mb-3">
                                <label class="form-label" for="MessageColor">
                                    <i class="fal fa-palette mr-1"></i>نوع پیام
                                </label>
                                <select class="form-control" id="MessageColor" name="MessageColor">
                                    <option value="">عادی</option>
                                    <option value="success">✅ موفقیت</option>
                                    <option value="error">❌ خطا</option>
                                    <option value="warning">⚠️ هشدار</option>
                                    <option value="info">ℹ️ اطلاعات</option>
                                </select>
                            </div>
                            <!-- تنظیمات پیشرفته -->
                            <div class="col-md-12 mb-3">
                                <label class="form-label" for="FilterByField">
                                    <i class="fal fa-filter mr-1"></i>فیلتر بر اساس فیلد
                                </label>
                                <textarea class="form-control" id="FilterByField" name="FilterByField"
                                          rows="2" style="text-align: left; direction: ltr;"
                                          placeholder="table.field1,table.field2"></textarea>
                                <small class="form-text text-muted">فیلدهای قابل فیلتر را با کاما جدا کنید</small>
                            </div>

                            <div class="col-md-8 mb-3">
                                <label class="form-label" for="BackgroundColor">
                                    <i class="fal fa-fill-drip mr-1"></i>رنگ پس‌زمینه
                                </label>
                                <input type="color" class="form-control" id="BackgroundColor" name="BackgroundColor"
                                       value="#ffffff" style="height: 38px;">
                            </div>

                            <div class="col-md-4 mb-3 d-flex align-items-center">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="HideHeader" name="HideHeader">
                                    <label class="custom-control-label" for="HideHeader">
                                        <i class="fal fa-eye-slash mr-1"></i>مخفی کردن هدر
                                    </label>
                                </div>
                            </div>

                            <div class="col-md-12 mb-3">
                                <label class="form-label" for="InputMapping">
                                    <i class="fal fa-code mr-1"></i>نقشه‌برداری ورودی‌ها (JSON)
                                </label>
                                <textarea class="form-control" id="InputMapping" name="InputMapping"
                                          rows="2" style="text-align: left; direction: ltr;"
                                          placeholder='{"input1": "field1", "input2": "field2"}'></textarea>
                                <small class="form-text text-muted">نقشه‌برداری فیلدهای ورودی به فرمت JSON</small>
                            </div>

                            <div class="col-12 mb-3">
                                <label class="form-label" for="HTML" id="HTMLLbl">
                                    <i class="fal fa-code mr-1"></i>کد HTML <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control" id="HTML" name="HTML"
                                          rows="8" style="text-align: left; direction: ltr; font-family: 'Courier New', monospace;"
                                          placeholder="<div>محتوای HTML پنل...</div>" required></textarea>
                                <small class="form-text text-muted">کد HTML برای نمایش محتوای پنل</small>
                            </div>
                        </div>


                    </div>

                    <!-- Footer Modal -->
                    <div class="panel-content border-faded border-left-0 border-right-0 border-bottom-0 d-flex flex-row align-items-center">
                        <button type="submit" class="btn btn-primary ml-auto">
                            <i class="fal fa-save mr-1"></i>ذخیره تغییرات
                        </button>
                        <a href="javascript:void(0);" class="btn btn-danger ml-auto" onclick="DeletePanel();">
                            <i class="fal fa-trash mr-1"></i>حذف پنل
                        </a>
                        <button type="button" class="btn btn-secondary ml-auto" data-dismiss="modal">
                            <i class="fal fa-times mr-1"></i>انصراف
                        </button>
                    </div>
                </form>
            </div>

        </div>
    </div>
</div>

<!-- ===== Modal ویرایش داشبورد ===== -->
<div id="DashboardModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-right modal-sm">
        <div class="modal-content">
            <!-- Header Modal -->
            <div class="modal-header">
                <h5 class="modal-title h4 text-white">
                    <i class="fal fa-tachometer-alt mr-2"></i>تنظیمات داشبورد
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                </button>
            </div>

            <!-- Body Modal -->
            <div class="modal-body">
                <form class="needs-validation" method="post" action="/Dashboard/EditDashboard" id="DashboardForm">
                    @Html.AntiForgeryToken()

                    <div class="panel-content">
                        <div class="form-row">
                            <!-- فیلد مخفی -->
                            <input id="Dashboard_ID" name="Dashboard_ID" type="hidden" value="@DashboardID" />

                            <!-- نام داشبورد -->
                            <div class="col-md-12 mb-3">
                                <label class="form-label" for="Dashboard_Name">
                                    <i class="fal fa-tag mr-1"></i>نام داشبورد <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="Dashboard_Name" name="Dashboard_Name"
                                       placeholder="نام داشبورد را وارد کنید" value="" required>
                            </div>

                            <!-- وضعیت عمومی -->
                            <div class="col-md-12 mb-3">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="Dashboard_IsPublic" name="Dashboard_IsPublic">
                                    <label class="custom-control-label" for="Dashboard_IsPublic">
                                        <i class="fal fa-globe mr-1"></i>داشبورد عمومی
                                        <small class="text-muted d-block">قابل دسترسی برای همه کاربران</small>
                                    </label>
                                </div>
                            </div>

                            <!-- CSS سفارشی -->
                            <div class="col-md-12 mb-3">
                                <label class="form-label" for="Dashboard_CssClass">
                                    <i class="fal fa-code mr-1"></i>CSS سفارشی
                                </label>
                                <textarea class="form-control" id="Dashboard_CssClass" name="Dashboard_CssClass"
                                          rows="4" style="text-align: left; direction: ltr; font-family: 'Courier New', monospace;"
                                          placeholder=".custom-style { color: #333; }"></textarea>
                                <small class="form-text text-muted">کدهای CSS برای سفارشی‌سازی ظاهر داشبورد</small>
                            </div>
                        </div>
                    </div>

                    <!-- Footer Modal -->
                    <div class="panel-content border-faded border-left-0 border-right-0 border-bottom-0 d-flex flex-row align-items-center">
                        <button type="submit" class="btn btn-success ml-auto">
                            <i class="fal fa-save mr-1"></i>ذخیره تغییرات
                        </button>
                        <button type="button" class="btn btn-secondary ml-auto" data-dismiss="modal">
                            <i class="fal fa-times mr-1"></i>انصراف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<div id="ParameterModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog  ">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title h4 text-white">داشبورد</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-row" style="padding:10px;">
                    <input type="hidden" id="IDP" />
                    <input type="hidden" id="InputP" />
                    <input type="hidden" id="Input1P" />
                    <input type="hidden" id="Input2P" />
                    <input type="hidden" id="Input3P" />
                    <input type="hidden" id="Input4P" />
                    <input type="hidden" id="Input5P" />
                    <input type="hidden" id="Input6P" />
                    <input type="hidden" id="Input7P" />
                    <div class="col-md-6 mb-3">
                        <label class="form-label" for="Name"> نوع نمودار </label>
                        <select id="ChartTypeP" class="form-control" onchange="   $('.twobarType').hide(); if ($('#ChartTypeP').val() == 'twobar') $('.twobarType').show();">
                            <option value="line">خطی</option>
                            <option value="bar">ستونی</option>
                            <option value="hbar">افقی</option>
                            <option value="area">ناحیه ای</option>
                            <option value="pie">دایره ای</option>
                            <option value="rose">گلبرگ</option>
                            <option value="polar">شعاعی</option>
                            <option value="twobar">دومحوره</option>
                            <option value="treemap">مستطیلی</option>
                            <option value="gauge">سرعت سنج</option>
                            <option value="stack">پشته</option>

                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label" for="Name"> رنگ نمودار </label>
                        <div class="input-group">
                            <select id="ChartColorP" class="form-control">
                                <option value="-1"> بر اساس عنوان </option>
                                @for (var ii = 0; ii < colors.Count; ii++)
                                {
                                    <option value="@ii"> @(colors[ii].Name) </option>
                                }
                            </select>
                            <div class="input-group-append">
                                <a href="#" class="btn btn-outline-default" data-toggle="dropdown"><i class="fal fa-paint-brush"></i></a>
                                <div class="dropdown-menu dropdown-menu-animated w-auto h-auto">
                                    <div class="dropdown-header bg-trans-gradient d-flex justify-content-center align-items-center rounded-top p-2">
                                        <h4 class="m-0 text-center color-white">
                                            <small class="mb-0 opacity-80">رنگ بندی</small>
                                        </h4>
                                    </div>
                                    <div class="custom-scroll h-100">
                                        <table class="table table-sm m-0 w-100">
                                            <thead class="bg-info-500">
                                                <tr>
                                                    <th>مقدار</th>
                                                    <th>رنگ</th>
                                                    <th></th>
                                                </tr>
                                                <tr>
                                                    <td><input id="cclr-val" type="text" class="form-control" placeholder="مقدار" onclick="" /></td>
                                                    <td>  <input id="cclr-clr" type="color" class="form-control p-1" value="" style="width:50px;" /></td>
                                                    <td><a href="javascript:void(0);" class="btn btn-xs btn-success waves-effect waves-themed" onclick="addColor();"><i class="fal fa-plus"></i></a></td>
                                                </tr>
                                            </thead>
                                            <tbody id="cclr-tbl">
                                            </tbody>

                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label" for="AngleXP">زاویه محور   </label>
                        <input type="text" class="form-control" id="AngleXP" name="AngleXP" placeholder="زاویه محور ">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label" for="AngleP">زاویه برچسب   </label>
                        <input type="text" class="form-control" id="AngleP" name="AngleP" placeholder="زاویه برچسب">
                    </div>
                    <div class="col-md-6 mb-3  ">
                        <label class="form-label" for="Name"> نمایش مقادیر </label>
                        <select id="ShowHelpP" class="form-control" onchange="var v = this.value; $('.LableP').hide(); if (v == 1) $('.LableP').show();">
                            <option value="0">خیر</option>
                            <option value="1">بلی</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3 LableP">
                        <label class="form-label" for="SizeP">اندازه فونت   </label>
                        <input type="text" class="form-control" id="SizeP" name="SizeP" placeholder="اندازه فونت">
                    </div>

                    <div class="col-md-6 mb-3 LableP ">
                        <label class="form-label" for="ShowDecimalP"> نمایش اعشار </label>
                        <select id="ShowDecimalP" class="form-control">
                            <option value="0">خیر</option>
                            <option value="1">بلی</option>
                        </select>
                    </div>

                    <div class="col-md-6 mb-3">

                        <label class="form-label" for="GridP1">فاصله از بالا   </label>
                        <input type="number" class="form-control" id="GridP1" placeholder="از 0 تا 200">

                    </div>
                    <div class="col-md-6 mb-3">

                        <label class="form-label" for="GridP1">فاصله از راست   </label>
                        <input type="number" class="form-control" id="GridP2" placeholder="از 0 تا 200">

                    </div>
                    <div class="col-md-6 mb-3">

                        <label class="form-label" for="GridP1">فاصله از پایین   </label>
                        <input type="number" class="form-control" id="GridP3" placeholder="از 0 تا 200">

                    </div>
                    <div class="col-md-6 mb-3">

                        <label class="form-label" for="GridP1">فاصله از چپ   </label>
                        <input type="number" class="form-control" id="GridP4" placeholder="از 0 تا 200">

                    </div>

                    <div class="col-md-6 mb-3 trgt ">
                        <label class="form-label" for="TargetShowP"> نمایش تارگت </label>
                        <select id="TargetShowP" class="form-control">
                            <option value="0">خیر</option>
                            <option value="1">بلی</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3 trgt">
                        <label class="form-label" for="TargetColorP"> رنگ تارگت </label>
                        <select id="TargetColorP" class="form-control">
                            @for (var ii = 0; ii < colors.Count; ii++)
                            {
                                <option value="@ii"> @(colors[ii].Name) </option>
                            }
                        </select>
                    </div>
                    <div class="col-md-6 mb-3 trgt">
                        <label class="form-label" for="TargetBaseColorP"> رنگ تارگت اصلی </label>
                        <select id="TargetBaseColorP" class="form-control">

                            @for (var ii = 0; ii < colors.Count; ii++)
                            {
                                <option value="@ii"> @(colors[ii].Name) </option>
                            }
                        </select>
                    </div>
                    <div class="col-md-6 mb-3 trgt ">
                        <label class="form-label" for="TargetLabelP"> نمایش بر چسب تارگت </label>
                        <select id="TargetLabelP" class="form-control">
                            <option value="0">خیر</option>
                            <option value="1">بلی</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3 trgt">
                        <label class="form-label" for="TargetAngleP">زاویه  تارگت   </label>
                        <input type="text" class="form-control" id="TargetAngleP" name="TargetAngleP" placeholder="زاویه  تارگت ">
                    </div>
                    <div class="col-md-6 mb-3  trgt">
                        <label class="form-label" for="TargetSizeP"> اندازه فونت تارگت   </label>
                        <input type="text" class="form-control" id="TargetSizeP" name="TargetSizeP" placeholder="12">
                    </div>
                    <div class="col-md-6 mb-3  trgt">
                        <label class="form-label" for="TargetWidthP">عرض تارگت    </label>
                        <input type="text" class="form-control" id="TargetWidthP" name="TargetWidthP" placeholder="30">
                    </div>
                    <div class="col-md-6 mb-3  trgt ">
                        <label class="form-label" for="TargetShowDecimalP"> نمایش اعشار تارگت </label>
                        <select id="TargetShowDecimalP" class="form-control">
                            <option value="0">خیر</option>
                            <option value="1">بلی</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3  trgt ">
                        <label class="form-label" for="TargetHeightP">  ارتفاع تارگت </label>
                        <input type="text" class="form-control" id="TargetHeightP" name="TargetHeightP" placeholder="1">

                    </div>


                    <div class="col-md-6 mb-3 twobarType">
                        <label class="form-label" for="Name"> نوع نمودار </label>
                        <select id="ChartTypeTwoBarP" class="form-control">
                            <option value="t1">خط و ستون</option>
                            <option value="t2">دو ستون</option>
                            <option value="t3">دو خط</option>


                        </select>
                    </div>
                    <hr />
                    <div class="col-md-12 mb-3">


                        <a class="btn btn-success" href="javascript:void(0);" onclick="SaveParameter();"><i class="fal fa-save"></i> اعمال و ذخیره</a>
                        <a class="btn btn-danger" href="javascript:void(0);" onclick="ResetParameter();"><i class="fal fa-times"></i> بازگشت به تنظیمات پیش فرض</a>

                    </div>


                </div>

            </div>


        </div>
    </div>
</div>


<div class="modal fade" id="PanelsModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    پنل ها
                    @*<small class="m-0 text-muted">
                            لطفا انتخاب نمایید
                        </small>*@
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                </button>
            </div>
            <div class="modal-body">
                <div class="custom-scroll h-100">
                    <div class="app-list bg-success-900 rounded bg-info-gradient" style=" padding: 0px;  ">
                        <div class="d-flex position-relative py-3 px-4">
                            <i class="fal fa-search color-success-700 position-absolute pos-left fs-lg px-3 py-2 mt-1 ml-4"></i>
                            <input type="text" id="js_nested_list_filter" class="form-control shadow-inset-1 pl-6 border-success" placeholder=" لطفا انتخاب نمایید">
                        </div>
                        <ul id="js_nested_list" class="nav-menu nav-menu-reset nav-menu-compact bg-success-900 bg-info-gradient mb-sm-4 mb-md-0 rounded" data-nav-accordion="true">

                            @for (int i = 0; i < PanelsKey.Count; i++)
                            {
                                <li>
                                    <a href="javascript:void(0)" data-filter-tags="@Panels[PanelsKey[i]]" onclick="$('#PanelsModal').modal('hide'); setTimeout(function (){GetPanel(@PanelsKey[i]);}, 500);">
                                        <span class="nav-link-text"> @Panels[PanelsKey[i]] </span>
                                        <strong class="dl-ref bg-primary-500">&nbsp;@PanelsKey[i]&nbsp;</strong>
                                    </a>
                                </li>
                            }

                        </ul>
                        <div class="filter-message js-filter-message m-0 text-left pl-4 py-3 fw-500"></div>
                    </div>


                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" onclick="$('#PanelsModal').modal('hide');setTimeout(function (){NewPanel();}, 500);">ایجاد پنل جدید</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">بستن</button>

            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="DashboardsModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">
                    داشبوردها
                    <small class="m-0 text-muted">
                        لطفا انتخاب نمایید
                    </small>
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"><i class="fal fa-times"></i></span>
                </button>
            </div>
            <div class="modal-body">
                <div class="custom-scroll h-100">
                    <ul class="app-list">

                        @for (int i = 0; i < Dashboards.Count; i++)
                        {
                            <li>
                                <a href="/Dashboard?id=@Dashboards[i].ID" class="app-list-item hover-white">
                                    <span class="icon-stack">
                                        <i class="base-18 icon-stack-3x color-info-700"></i>
                                        <span class="position-absolute pos-top pos-left pos-right color-white fs-md mt-2 fw-400">@Dashboards[i].ID</span>
                                    </span>
                                    <span class="app-list-name">
                                        @Dashboards[i].Name
                                    </span>
                                </a>
                            </li>
                        }


                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">بستن</button>

            </div>
        </div>
    </div>
</div>

@section shortcutmenu {
    <nav class="shortcut-menu d-none d-sm-block">
        <input type="checkbox" class="menu-open" name="menu-open" id="menu_open" />
        <label for="menu_open" class="menu-open-button ">
            <span class="app-shortcut-icon d-block"></span>
        </label>

        @if (isAdmin == true)
        {


            <a id="btnPanel" href="javascript:void(0);" class="menu-item btn" onclick="NewDashboard();" data-toggle="tooltip" data-placement="left" title="ایجاد داشبورد جدید">
                <i class="fal fa-plus"></i>
            </a>
            <a href="javascript:void(0);" class="menu-item btn" onclick="GetDashboard(@DashboardID);" data-toggle="tooltip" data-placement="left" title="ویرایش اطلاعات داشبورد ">
                <i class="fal fa-edit"></i>
            </a>
            <a href="javascript:void(0);" class="menu-item btn" onclick="$('#PanelsModal').modal('show')" data-toggle="tooltip" data-placement="left" title="ویرایش پنل ها ">
                <i class="fal fa-file-edit"></i>
            </a>
            @*<a id="btnPanel" href="javascript:void(0);" class="menu-item btn" onclick="NewPanel();" data-toggle="tooltip" data-placement="left" title="ایجاد پنل جدید">
                    <i class="fal fa-file-plus"></i>
                </a>*@

        }
        else
        {
            <a href="javascript:void(0);" class="menu-item btn" onclick="printCanvas();" data-toggle="tooltip" data-placement="left" title="Print">
                <i class="fal fa-print"></i>
            </a>
        }
        @if (Dashboard.FilterType != 2)
        {
            <a href="javascript:void(0);" class="menu-item btn" onclick="toggleFilter();" data-placement="left" title="فیلتر">
                <i class="fal fa-search"></i>
            </a>
        }

        <a href="#" class="menu-item btn" data-toggle="modal" data-target="#DashboardsModal" data-placement="left" title="داشبورد ها">
            <i class="fal fa-cube"></i>
        </a>



    </nav>
}

<a href="javascript:void(0);" onclick="topFunction();" id="topBtn" class="menu-item btn waves-effect waves-themed" data-toggle="tooltip" data-placement="right" title="" data-original-title="رفتن به بالا">
    <i class="fal fa-arrow-up"></i>
</a>

@section scripts {
    <!-- ===== کتابخانه‌های JavaScript از LibMan ===== -->
    <script src="~/lib/echarts/echarts.min.js"></script>
    <script src="~/lib/select2/js/select2.min.js"></script>
    <script src="~/lib/select2/js/i18n/fa.js"></script>
    <script src="~/lib/sweetalert2/sweetalert2.min.js"></script>

    <!-- ===== ماژول‌های داشبورد ===== -->
    <script src="~/js/dashboard/dashboard-core.js"></script>
    <script src="~/js/dashboard/chart-manager.js"></script>
    <script src="~/js/dashboard/table-manager.js"></script>

    <!-- ===== کتابخانه‌های محلی ===== -->
    <script src="~/js/site.js"></script>

    <!-- ===== تنظیمات اولیه ===== -->
    <script>
        // تنظیمات سراسری
        window.dashboardId = @DashboardID;
        window.isAdmin = @(isAdmin.ToString().ToLower());
        window.dashboardColors = [@Html.Raw(string.Join(",", colors.Select(c => c.Value)))];

        // مقداردهی اولیه
        $(document).ready(function() {
            // نمایش پیام شروع
            @if (!string.IsNullOrEmpty(message))
            {
                <text>
                var message = "@Html.Raw(message)";
                toastr.warning(message);

                $.connection.hub.start().done(function () {
                    metaChat.server.send('@userGuid', "ورود کاربر", "@applicationUser.Id");
                });
                </text>
            }
        });
    </script>
    <script>
        // ===== مقداردهی اولیه صفحه =====
        $(document).ready(function () {
            showStartMessage();
            initializePage();
        });

        function showStartMessage() {
            @if (!string.IsNullOrEmpty(message))
            {
                <text>
                var message = "@Html.Raw(message)";
                toastr['warning'](message);

                $.connection.hub.start().done(function () {
                    metaChat.server.send('@userGuid', "ورود کاربر", "@applicationUser.Id");
                });
                </text>
            }
        }

        function initializePage() {
            // مقداردهی اولیه کامپوننت‌ها
            $('.select2').select2({
                language: 'fa',
                dir: 'rtl'
            });
        }
        // ===== متغیرهای سراسری =====
        var activePanelID = 0;
        var colorDefaults = [];
        var initCharts = [];
        var initOptions = [];

        // ===== تنظیمات رنگ‌ها =====
        @for (int ii = 0; ii < colors.Count; ii++)
        {
            <text>colorDefaults.push([@Html.Raw(colors[ii].Value)]);</text>
        }

        // ===== توابع مدیریت داشبورد =====
        function NewDashboard() {
            // پاک کردن فرم
            $("#Dashboard_ID").val('');
            $("#Dashboard_Name").val('');
            $("#Dashboard_IsPublic").prop("checked", false);
            $("#Dashboard_CssClass").val('');

            $("#DashboardModal").modal("show");
        }

        function GetDashboard(id) {
            $("#loadingImg").show();

            $.ajax({
                type: "GET",
                url: '/Dashboard/GetDashboard?ID=' + id,
                success: function (result) {
                    if (result.ok) {
                        var item = result.data;
                        $("#Dashboard_ID").val(id);
                        $("#Dashboard_Name").val(item.Name);
                        $("#Dashboard_IsPublic").prop("checked", item.IsPublic);
                        $("#Dashboard_CssClass").val(item.CssClass || '');

                        $("#DashboardModal").modal("show");
                    } else {
                        toastr.error(result.msg, "خطا");
                    }
                },
                error: function () {
                    toastr.error("خطا در بارگذاری اطلاعات داشبورد", "خطا");
                },
                complete: function () {
                    $("#loadingImg").hide();
                }
            });
        }

        // ===== ارسال فرم داشبورد =====
        $("#DashboardForm").submit(function (e) {
            e.preventDefault();

            var form = $(this);
            var url = form.attr('action');

            $("#loadingImg").show();

            $.ajax({
                type: "POST",
                url: url,
                data: form.serialize(),
                success: function (result) {
                    if (result.ok) {
                        toastr.success("داشبورد با موفقیت ذخیره شد", "موفقیت");
                        setTimeout(function() {
                            location.href = "/dashboard?id=" + result.id;
                        }, 1000);
                    } else {
                        toastr.error(result.msg, "خطا");
                    }
                },
                error: function () {
                    toastr.error("خطا در ذخیره اطلاعات", "خطا");
                },
                complete: function () {
                    $("#loadingImg").hide();
                }
            });
        });

        // ===== توابع مدیریت پنل =====
        function ChangeType1() {
            var type1 = $("#Type1").val();
            if (type1 == "1") {
                $(".colType1").show();
                $("#HTMLLbl").text("HTML");
            } else if (type1 == "2") {
                $(".colType1").hide();
                $("#HTMLLbl").text("HTML لیست");
            } else if (type1 == "3") {
                $(".colType1").hide();
                $("#HTMLLbl").text("HTML نقشه");
            } else {
                $(".colType1").hide();
            }
        }

        function NewPanel() {
            // پاک کردن تمام فیلدهای فرم
            const fields = [
                'ID', 'Name', 'Name2', 'Type1', 'Type2', 'SQL', 'HTML',
                'Size', 'SizeH', 'Padding', 'Group', 'Order', 'URL',
                'MessagePanel', 'MessageColor', 'FilterByField',
                'InputMapping', 'BackgroundColor'
            ];

            fields.forEach(field => $("#" + field).val(''));

            // پاک کردن فیلدهای خاص
            $("#Panels").val('').trigger('change');
            $("#HideHeader").prop('checked', false);

            ChangeType1();
            $("#PanelModal").modal("show");
        }

        function GetPanel(id) {
            $("#loadingImg").show();

            $.ajax({
                type: "GET",
                url: '/Dashboard/GetPanel?ID=' + id,
                success: function (result) {
                    if (result.ok) {
                        var item = result.data;

                        // پر کردن فیلدهای اصلی
                        $("#ID").val(id);
                        $("#Name").val(item.Name || '');
                        $("#Name2").val(item.Name2 || '');
                        $("#Type1").val(item.Type1 || '');
                        $("#Type2").val(item.Type2 || '');
                        $("#SQL").val(item.SQL ? item.SQL.ID : '');
                        $("#HTML").val(item.HTML || '');

                        // تنظیمات چیدمان
                        $("#Size").val(item.Size || '6');
                        $("#SizeH").val(item.SizeH || '');
                        $("#Padding").val(item.Padding || '');
                        $("#Group").val(item.Group || '0');
                        $("#Order").val(item.Order || '1');

                        // تنظیمات اضافی
                        $("#URL").val(item.URL || '');
                        $("#MessagePanel").val(item.MessagePanel || '');
                        $("#MessageColor").val(item.MessageColor || '');
                        $("#FilterByField").val(item.FilterByField || '');
                        $("#InputMapping").val(item.InputMapping || '');
                        $("#BackgroundColor").val(item.BackgroundColor || '#ffffff');
                        $("#HideHeader").prop('checked', item.HideHeader || false);

                        // پنل‌های والد
                        if (item.Panels) {
                            $("#Panels").val(item.Panels.split(',')).trigger('change');
                        }

                        ChangeType1();
                        $("#PanelModal").modal("show");
                    } else {
                        toastr.error(result.msg, "خطا");
                    }
                },
                error: function () {
                    toastr.error("خطا در بارگذاری اطلاعات پنل", "خطا");
                },
                complete: function () {
                    $("#loadingImg").hide();
                }
            });
        }

        // ===== ارسال فرم پنل =====
        $("#PanelForm").submit(function (e) {
            e.preventDefault();

            var form = $(this);
            var url = form.attr('action');

            $("#loadingImg").show();

            $.ajax({
                type: "POST",
                url: url,
                data: form.serialize(),
                success: function (result) {
                    if (result.ok) {
                        toastr.success("پنل با موفقیت ذخیره شد", "موفقیت");
                        $("#PanelModal").modal("hide");
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        toastr.error(result.msg, "خطا");
                    }
                },
                error: function () {
                    toastr.error("خطا در ذخیره اطلاعات پنل", "خطا");
                },
                complete: function () {
                    $("#loadingImg").hide();
                }
            });
        });

        // ===== حذف پنل =====
        function DeletePanel() {
            var panelId = $("#ID").val();
            if (!panelId) {
                toastr.error("پنل انتخاب نشده است", "خطا");
                return;
            }

            Swal.fire({
                title: 'حذف پنل',
                text: 'آیا از حذف این پنل اطمینان دارید؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'بله، حذف کن',
                cancelButtonText: 'انصراف'
            }).then((result) => {
                if (result.isConfirmed) {
                    $("#loadingImg").show();

                    $.ajax({
                        type: "POST",
                        url: '/Dashboard/DeletePanel',
                        data: { id: panelId },
                        success: function (result) {
                            if (result.ok) {
                                toastr.success("پنل با موفقیت حذف شد", "موفقیت");
                                $("#PanelModal").modal("hide");
                                setTimeout(function() {
                                    location.reload();
                                }, 1000);
                            } else {
                                toastr.error(result.msg, "خطا");
                            }
                        },
                        error: function () {
                            toastr.error("خطا در حذف پنل", "خطا");
                        },
                        complete: function () {
                            $("#loadingImg").hide();
                        }
                    });
                }
            });
        }

        // ===== توابع فیلتر =====
        function toggleFilter() {
            $("#DivPanelFilter").toggle();
            var icon = $("#iFilter");
            if ($("#DivPanelFilter").is(":visible")) {
                icon.removeClass("fa-filter").addClass("fa-filter-slash");
            } else {
                icon.removeClass("fa-filter-slash").addClass("fa-filter");
            }
        }

        function Filter() {
            // اعمال فیلتر
            var filterData = {};
            $(".filter").each(function() {
                var name = $(this).attr('name');
                var value = $(this).val();
                if (value && value.length > 0) {
                    filterData[name] = value;
                }
            });

            // بارگذاری مجدد پنل‌ها با فیلتر
            LoadPanels(filterData);
        }

        function ClearFilter() {
            // پاک کردن تمام فیلترها
            $(".filter").val('').trigger('change');

            // بارگذاری مجدد پنل‌ها بدون فیلتر
            LoadPanels({});
        }

        function LoadPanels(filterData) {
            $("#loadingImg").show();

            $.ajax({
                type: "GET",
                url: '/Dashboard/GetDashboardPanels',
                data: {
                    DashboardID: '@DashboardID',
                    ...filterData
                },
                success: function (result) {
                    $("#PanelsDiv").html(result);
                },
                error: function () {
                    toastr.error("خطا در بارگذاری پنل‌ها", "خطا");
                },
                complete: function () {
                    $("#loadingImg").hide();
                }
            });
        }


        function GetDataChart(id, input, input1, input2, input3, input4, input5, input6, input7, isSave,where) {
            $("#pnl" + id).css("mix-blend-mode", "overlay");
            $("#EditParameter" + id).hide();
            //$("#ContentPanel" + id).html('');
            var clr = $("#ChartColor" + id).val();
            var clrTitle = $("#ChartColorTitle" + id).val();
            var shw = $("#ShowHelp" + id).is(":checked");

            var angle = $("#Angle" + id).val();
            var angleX = $("#AngleX" + id).val();
            var grid = $("#Grid" + id).val();
            var shwD = $("#ShowDecimal" + id).is(":checked");
            var sizelable = $("#Size" + id).val();
            var shwDT = $("#TargetShowDecimal" + id).is(":checked");


            var targetP = {};
            targetP.TargetColor = $("#TargetColor" + id).val();
            targetP.TargetBaseColor = $("#TargetBaseColor" + id).val();
            targetP.TargetShow = $("#TargetShow" + id).is(":checked");
            targetP.TargetLabel = $("#TargetLabel" + id).is(":checked");
            targetP.TargetAngle = $("#TargetAngle" + id).val();
            targetP.TargetSize = $("#TargetSize" + id).val();
            targetP.TargetWidth = $("#TargetWidth" + id).val();
            targetP.TargetShowDecimal = $("#TargetShowDecimal" + id).is(":checked");
            targetP.TargetHeight = $("#TargetHeight" + id).val();

            $.ajax({
                type: "Get",
                url: '/Dashboard/GetDataChart?ID=' + id + "&input=" + input + "&input1=" + input1 + "&input2=" + input2 + "&input3=" + input3 + "&input4=" + input4 + "&input5=" + input5 + "&input6=" + input6 + "&input7=" + input7 + "&where=" + where + (isSave ? "&type=" + $("#ChartType" + id).val() : "") + (isSave ? "&typeTwoBar=" + $("#ChartTypeTwoBar" + id).val() : "") + (isSave ? "&color=" + clr + "&colorTitle=" + escape(clrTitle) + "&showhelp=" + shw + "&grid=" + grid + "&angle=" + angle + "&angleX=" + angleX + "&showdecimal=" + shwD + "&sizelabel=" + sizelable + "&TargetColor=" + targetP.TargetColor + "&TargetBaseColor=" + targetP.TargetBaseColor + "&TargetShow=" + targetP.TargetShow + "&TargetLabel=" + targetP.TargetLabel + "&TargetAngle=" + targetP.TargetAngle + "&TargetSize=" + targetP.TargetSize + "&TargetWidth=" + targetP.TargetWidth + "&TargetShowDecimal=" + targetP.TargetShowDecimal + "&TargetHeight=" + targetP.TargetHeight : "") /*+ (lable ? "&lable=" + lable : "")*/,
                success: function (result) {
                    $("#pnl" + id).css("mix-blend-mode", "");
                    if (result.ok) {

                        $("#ContentPanel" + id).html('');

                        var sizeh = parseFloat(result.SizeH);

                        sizeh = sizeh * 200 + (sizeh - 1) * 75;
                        var sizeh2 = parseFloat(sizeh / 2);
                        var items = JSON.parse(result.items);
                        if (items.length == 0)
                            return;
                        var containid = false;
                        var containtitle = false;
                        var containvalue = false;
                        var containvalue1 = false;
                        var containvalue2 = false;
                        var containvalue3 = false;
                        var containvalue4 = false;
                        var containvalue5 = false;
                        var containtarget = false;
                        var headers = [];
                        for (var k in items[0]) {
                            if (k.toLocaleLowerCase() == 'id')
                                containid = true;
                            else if (k.toLocaleLowerCase() == 'title')
                                containtitle = true;
                            else if (k.toLocaleLowerCase() == 'value')
                                containvalue = true;
                            else if (k.toLocaleLowerCase() == 'value1')
                                containvalue1 = true;
                            else if (k.toLocaleLowerCase() == 'value2')
                                containvalue2 = true;
                            else if (k.toLocaleLowerCase() == 'value3')
                                containvalue3 = true;
                            else if (k.toLocaleLowerCase() == 'value4')
                                containvalue4 = true;
                            else if (k.toLocaleLowerCase() == 'value5')
                                containvalue5 = true;
                            else if (k.toLocaleLowerCase() == 'target')
                                containtarget = true;
                            else
                                headers.push(k);


                        }

                        if (containtarget)
                            $("#Target" + id).prop('checked', true);
                        var isList = result.isList;



                        var charttype = result.type;
                        var charttwobartype = result.typeTwoBar;
                        var chkLbl = result.label;



                        if (!isList) {


                            for (var i = 0; i < items.length; i++) {

                                var lable = [];
                                var data = [];//y;//[1, 2];
                                var seriLable = [];// ["تعداد","جمع
                                var idLable = [];
                                var titleLable = [];
                                var valueLable = [];
                                var valueLable1 = [];
                                var valueLable2 = [];
                                var valueLable3 = [];
                                var valueLable4 = [];
                                var valueLable5 = [];
                                var targetLable = [];

                                if (containid)
                                    idLable.push(items[i]['id']);
                                if (containtitle)
                                    titleLable.push(items[i]['title']);
                                if (containvalue)
                                    valueLable.push(items[i]['value']);
                                if (containvalue1)
                                    valueLable1.push(items[i]['value1']);
                                if (containvalue2)
                                    valueLable2.push(items[i]['value2']);
                                if (containvalue3)
                                    valueLable3.push(items[i]['value3']);
                                if (containvalue4)
                                    valueLable4.push(items[i]['value4']);
                                if (containvalue5)
                                    valueLable5.push(items[i]['value5']);
                                if (containtarget)
                                    targetLable.push(parseFloat(items[i]['target']).toFixed(shwDT ? 2 : 0));

                                for (var j = 0; j < headers.length; j++) {

                                    seriLable.push(headers[j]);
                                    if (charttype == 'gauge') {
                                        if (j == 0)
                                            lable.push((items[i][headers[j]]));
                                        else {
                                            data[j - 1] = [];
                                            data[j - 1].push(parseFloat(items[i][headers[j]]).toFixed(shwD ? 2 : 0));
                                        }
                                    }
                                    else {
                                        data[j] = [];
                                        data[j].push(parseFloat(items[i][headers[j]]).toFixed(shwD ? 2 : 0));
                                    }
                                }

                                $("#ContentPanel" + id).append("<div id='ContentDetail" + id + "_" + i + "' class='cc col-md-" + (items.length > 1 ? "6" : "12") + "' data-H='" + sizeh + "'  style='height:" + (items.length > 2 ? sizeh2 : sizeh) + "px;'></div>");
                                Chart_Draw(id, 'ContentDetail' + id + '_' + i, charttype, charttwobartype, lable, data, seriLable, idLable, titleLable, valueLable, targetLable, chkLbl, clr, clrTitle, items[i][headers[0]], shw, grid, angle, angleX, sizelable, targetP, valueLable1, valueLable2, valueLable3, valueLable4, valueLable5);


                            }



                        } else {
                            var lable = [];
                            var data = [];//y;//[1, 2];
                            var seriLable = [];// ["تعداد","جمع
                            var idLable = [];
                            var titleLable = [];
                            var valueLable = [];
                            var valueLable1 = [];
                            var valueLable2 = [];
                            var valueLable3 = [];
                            var valueLable4 = [];
                            var valueLable5 = [];
                            var targetLable = [];

                            for (var j = 1; j < headers.length; j++) {
                                data[j - 1] = [];
                                seriLable.push(headers[j]);
                            }
                            for (var i = 0; i < items.length; i++) {
                                lable.push(items[i][headers[0]]);
                                for (var j = 0; j < data.length; j++)
                                    data[j].push(parseFloat(items[i][headers[j + 1]]).toFixed(shwD ? 2 : 0));

                                if (containid)
                                    idLable.push(items[i]['id']);
                                if (containtitle)
                                    titleLable.push(items[i]['title']);
                                if (containvalue)
                                    valueLable.push(items[i]['value']);
                                if (containvalue1)
                                    valueLable1.push(items[i]['value1']);
                                if (containvalue2)
                                    valueLable2.push(items[i]['value2']);
                                if (containvalue3)
                                    valueLable3.push(items[i]['value3']);
                                if (containvalue4)
                                    valueLable4.push(items[i]['value4']);
                                if (containvalue5)
                                    valueLable5.push(items[i]['value5']);

                                if (containtarget)
                                    targetLable.push(parseFloat(items[i]['target']).toFixed(shwDT ? 2 : 0));
                            }


                            $("#ContentPanel" + id).html("<div id='ContentDetail" + id + "_1'  class='cc col-md-12'   data-H='" + sizeh + "' style=' height:" + sizeh + "px;'></div>");
                            Chart_Draw(id, 'ContentDetail' + id + "_1", charttype, charttwobartype, lable, data, seriLable, idLable, titleLable, valueLable, targetLable, chkLbl, clr, clrTitle, undefined, shw, grid, angle, angleX, sizelable, targetP, valueLable1, valueLable2, valueLable3, valueLable4, valueLable5);

                        }


                        $("#DivPanel" + id).fadeIn(400, function () { resizeAllCharts(); });



                    }
                    else {
                        toastr.error(result.msg, "خطا");
                        $("#DivPanel" + id).fadeIn(400);
                    }
                    // show response from the php script.
                }
            });
        }



        // ===== توابع رسم نمودار =====
        function Chart_Draw(panelID, divID, type, charttwobartype, lable, data, seriLable, idLable, titleLable, valueLable, targetLables, checkLable, color, colorTitle, chartTitle, showhelp, grid, angle, angleX, sizelable, targetP, valueLable1, valueLable2, valueLable3, valueLable4, valueLable5) {

            // تنظیمات پیش‌فرض
            if (!angleX) angleX = angle;
            if (grid == undefined || grid == "") grid = "30,20,30,20";
            if (data.length == 0) return;

            var colorDefault = colorDefaults[color];
            var dataJson = [];


            var e = document.getElementById(divID),
                a = echarts.init(e);

            var cs = $("#" + divID).closest('.panel').attr("class").split(/\s+/);
            var lvl = cs[cs.length - 1];
            a.lvl = lvl;



            if (type == "line" || type == "area") {

                for (var j = 0; j < data.length; j++) {
                    var json = {
                        name: seriLable[j],//???????/
                        type: 'line',
                        data: data[j],//????????/
                        label: {
                            show: showhelp,//(checkLable.ToString().ToLower()),?????
                            position: 'top',
                            formatter: function (d) { var c = ""; try { c = d.data.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','); } catch { } return c; },//'{c}',
                            textStyle: {
                                color: 'black'
                            },
                            fontSize: sizelable ? sizelable : 12


                        },
                       // animationEasing: 'backOut',
                        animationDelay: function (idx) { return idx * 10;},
                        itemStyle: {
                            emphasis: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)',
                                color: "#00FFFF",
                                borderColor: "#00FFFF"
                            }
                        }
                    };
                    if (type == "area") {
                        json.areaStyle = {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: 'rgb(255, 158, 68)'
                                }, {
                                    offset: 1,
                                    color: 'rgb(255, 70, 131)'
                                }])
                            }
                        };
                    }
                    dataJson.push(json);//????????/
                }

                var t = {
                    grid: GetGrid(grid),
                    title: { text: chartTitle ? chartTitle : ' ' },
                    legend: {
                        show: false,
                        data: showhelp ? seriLable : 'none'
                    },
                    color: colorDefault,
                    toolbox: {

                    },
                    textStyle: {
                        fontFamily: 'BYekan'
                    },

                    tooltip: {},
                    xAxis: {
                        data: lable,//????
                        silent: false,
                        splitLine: {
                            show: false
                        }
                    },
                    yAxis: {
                    },
                    series: dataJson,//????
                    animation:true,
                    animationEasing: 'elasticOut',
                    animationDuration: 1000,
                    animationDelayUpdate: function (idx) { return idx * 5; }
                    //animationEasing: 'backOut',
                    //animationDelayUpdate: function (idx) { return idx * 10; }
                };
                a.setOption(t, !0);
                a.resize();

            }
            else if (type == "pie") {



                var series = [];
                for (var k = 0; k < data.length; k++) {
                    var seriesData = [];
                    for (var i = 0; i < data[k].length; i++) {
                        var o = {
                            name: lable[i],//+ (seriLable[k] ? " - " + seriLable[k] : ""),
                            value: data[k][i]
                        };
                        var cclr = getColor(colorTitle, lable[i]);
                        if (cclr)
                            o.itemStyle= { color: cclr };
                        seriesData.push(o);
                    }
                    series.push({
                        name: ' ',
                        type: 'pie',
                        radius: [((k * 20) + (k == 0 ? 0 : 20)) + '%', ((k * 20) + 50) + '%'],
                        center: ['50%', '50%'],
                        data: seriesData,
                        label: {
                            show: showhelp,
                            formatter: function (d) { var c = ""; try { c = d.data.name + " \n\n  " + d.data.value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','); } catch { } return c; },//'{b} \n\n {c}',
                            distanceToLabelLine: 1,
                            margin: 50,
                            fontSize: sizelable ? sizelable : 12


                        },
                        itemStyle: {
                            emphasis: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)',
                                color:"#00FFFF"
                            }
                        }
                    });
                }

                var t = {
                    title: { text: chartTitle ? chartTitle : ' ' },
                    color: colorDefault,
                    tooltip: {
                        trigger: 'item',
                        formatter: "{a} <br/>{b} : {c} ({d}%)"
                    },
                    toolbox: {
                    },
                    legend: {
                        show: false
                    },

                    textStyle: {
                        fontFamily: 'BYekan'
                    },
                    series: series
                };

                a.setOption(t, !0);
                a.resize();

            }
            else if (type == "rose") {

                var series = [];
                for (var k = 0; k < data.length; k++) {
                    var seriesData = [];
                    for (var i = 0; i < data[k].length; i++) {
                        var o = {
                            name: lable[i],//+ (seriLable[k] ? " - " + seriLable[k] : ""),
                            value: data[k][i]
                        };
                        var cclr = getColor(colorTitle, lable[i]);
                        if (cclr)
                            o.itemStyle = { color: cclr };
                        seriesData.push(o);
                    }
                    series.push({
                        name: ' ',
                        type: 'pie',
                        radius: [((k * 20) + (/*k == 0 ? 0 : 20*/ 3)) + '%', ((k * 20) + 80) + '%'],
                        center: ['50%', '50%'],
                        roseType: 'area',
                        data: seriesData,
                        label: {
                            show: showhelp,
                            formatter: function (d) { var c = ""; try { c = d.data.name + " \n\n " + d.data.value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','); } catch { } return c; },//'{b} \n\n {c}',
                            distanceToLabelLine: 1,
                            margin: 50,
                            fontSize: sizelable ? sizelable : 12


                        },
                        itemStyle: {
                            borderRadius: 80,
                            emphasis: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)',
                                color: "#00FFFF",
                                borderColor: "#00FFFF"
                            }
                        }
                    });
                }

                var t = {
                    title: { text: chartTitle ? chartTitle : ' ' },
                    color: colorDefault,
                    tooltip: {
                        trigger: 'item',
                        formatter: "{a} <br/>{b} : {c} ({d}%)"
                    },
                    toolbox: {
                    },
                    legend: {
                        show: false
                    },

                    textStyle: {
                        fontFamily: 'BYekan'
                    },
                    series: series
                };

                a.setOption(t, !0);
                a.resize();



            }

            else if (type == "radar") {

                var indicator = [];

                for (var ii = 0; ii < lable.length; ii++)
                    indicator.push({ name: lable[ii] });
                var t = {
                    title: { text: chartTitle ? chartTitle : ' ' },
                    legend: { show: false, data: showhelp ? lable : 'none'  /*data: lable*/ },
                    color: colorDefault,

                    radar: {
                        // shape: 'circle',
                        name: {
                            textStyle: {
                                color: '#000',
                                backgroundColor: '#fff',
                                borderRadius: 3,
                                padding: [3, 5]
                            }
                        },
                        indicator: indicator
                    },
                    toolbox: {
                    },

                    textStyle: {
                        fontFamily: 'BYekan'
                    },
                    series: [{
                        name: ' ',
                        type: 'radar',
                        // areaStyle: {normal: {}},
                        data: [
                            {
                                value: data[0],
                                name: ' نمودار',
                                label: {
                                    show: showhelp,
                                    formatter: function (params) {
                                        return params.value;
                                    }
                                }
                            }
                        ]
                    }]
                };
                a.setOption(t, !0);
                a.resize();

            }
            else if (type == "bar") {




                //----------
                if (targetLables && targetLables.length > 0 && targetP && targetP.TargetShow == true) {




                    if (targetP && targetP.TargetHeight > 0) {





                        var max = Math.max.apply(null, targetLables);
                        for (var ii = 0; ii < targetLables.length; ii++) {
                            var dta = targetLables.concat();
                            dta.forEach(function (part, index, theArray) {
                                theArray[index] = "-";
                            });
                            dta[ii] = targetLables[ii];

                            var h = (targetLables[ii] - data[0][ii]) * (175 / max);
                            var isRect = h > 1;

                            h = h * targetP.TargetHeight  ;
                            var json = {
                                name: seriLable[0],//???????/
                                type: 'line',
                                symbol: isRect ? 'path://M 0 0 L 200 0 L 200 200 L 0 200 L 0 0' : 'path://M  86 0 L 85 10 ',
                                symbolSize: isRect ? [targetP && targetP.TargetWidth ? targetP.TargetWidth : 30, h * 1.9] : (targetP && targetP.TargetWidth ? targetP.TargetWidth : 30) * .8,
                                symbolRotate: isRect ? 0 : -45,
                                symbolOffset: isRect ? [0, h] : [0, 0],
                                lineStyle: {
                                    color: 'transparent'
                                },
                                itemStyle: {
                                    borderWidth: 3,
                                    borderColor: targetP && targetP.TargetBaseColor ? colorDefaults[targetP.TargetBaseColor][0] : 'red',
                                    color: targetP && targetP.TargetBaseColor ? colorDefaults[targetP.TargetBaseColor][0] : 'red',
                                    //borderType: 'dotted'
                                },
                                data: dta,//????????/
                                label: {
                                    show: targetP && targetP.TargetLabel == true ? true : false,//checkLable,
                                    formatter: function (d) { var c = ""; try { c = d.data.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','); } catch { } return c; },//'{c}',
                                    //fontFamily: 'tahoma',
                                    rotate: targetP && targetP.TargetAngle ? targetP.TargetAngle : 0,
                                    //align: 'left',
                                    verticalAlign: 'middle',
                                    position: 'top',
                                    //distance: (targetLables && targetLables.length ? -10 : 15),
                                    textStyle: {
                                        //color: 'black'
                                    },
                                    fontSize: targetP && targetP.TargetSize ? targetP.TargetSize : 12
                                },
                                animationDelay: function (idx) {
                                    return idx * 10;
                                }
                            };
                            dataJson.push(json);//????????/
                        }
                    }


                    var json = {
                        name: seriLable[0],//???????/
                        type: 'line',
                        symbol: 'path://M  86 0 L 85 10 ',
                        symbolSize: (targetP && targetP.TargetWidth ? targetP.TargetWidth : 30) * .8,
                        symbolRotate: -45,
                        lineStyle: {
                            color: 'transparent'
                        },
                        itemStyle: {
                            borderWidth: 3,
                            borderColor: targetP && targetP.TargetColor ? colorDefaults[targetP.TargetColor][0] : 'red',
                            //borderType: 'dotted'
                        },
                        data: targetLables,//????????/

                        label: {
                            show: targetP && (targetP.TargetLabel == true && targetP.TargetHeight == '') ? true : false,//checkLable,
                            formatter: function (d) { var c = ""; try { c = d.data.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','); } catch { } return c; },//'{c}',
                            //fontFamily: 'tahoma',
                            rotate: targetP && targetP.TargetAngle ? targetP.TargetAngle : 0,
                            //align: 'left',
                            verticalAlign: 'middle',
                            position: 'top',
                            //distance: (targetLables && targetLables.length ? -10 : 15),
                            textStyle: {
                                //color: 'black'
                            },
                            fontSize: targetP && targetP.TargetSize ? targetP.TargetSize : 12
                        },
                        animationDelay: function (idx) {
                            return idx * 10;
                        }
                    };
                    dataJson.push(json);
                }

                for (var j = 0; j < data.length; j++) {



                    var data2 = [];
                    for (var k = 0; k < lable.length; k++) {
                        var cclr = getColor(colorTitle, lable[k]);
                        if (cclr)
                            data2.push({ value: data[j][k], itemStyle: { color: cclr } });
                        else
                            data2.push(data[j][k]);//{ value: data[j][k], itemStyle: { color: [new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: colorDefault[0] }, { offset: 1, color: colorDefault[1] }])] } });
                    }
                    var json = {
                        name: seriLable[j],//???????/
                        type: 'bar',
                        data: data2,// data[j],//????????/
                        label: {
                            show: showhelp,//checkLable,
                            formatter: function (d) { var c = ""; try { var u = d.data.value ? d.data.value : d.data; c = u.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','); } catch { } return c; },//'{c}',
                            //fontFamily: 'tahoma',
                            rotate: angle ? angle : 0,
                            //align: 'left',
                            verticalAlign: 'middle',
                            position: 'top',
                            distance: (targetLables && targetLables.length?-10:15),
                            textStyle: {
                                //color: 'black'
                            },
                            fontSize: sizelable ? sizelable : 12
                        },
                        animationDelay: function (idx) {
                            return idx * 10;
                        },
                        itemStyle: {
                            emphasis: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)',
                                color: "#00FFFF",
                                borderColor: "#00FFFF"
                            }
                            ///????????????????????????????> Set color value
                        }
                    };
                    dataJson.push(json);//????????/
                }

                t = {
                    grid: GetGrid(grid),
                    title: { text: '' },
                    legend: {
                        data: seriLable,
                        show: false // showhelp
                        // align: 'left'
                    },
                    color: [new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: colorDefault[0] }, { offset: 1, color: colorDefault[1] }])],
                    toolbox: {
                        show: false,
                        orient: 'vertical',
                        left: 'right',
                        top: 'center',
                        feature: {
                            mark: { show: true },
                            dataView: { show: true, readOnly: false },
                            magicType: { show: true, type: ['line', 'bar', 'stack', 'tiled'] },
                            restore: { show: true },
                            saveAsImage: { show: true }
                        }
                    },
                    textStyle: {
                        fontFamily: 'BYekan'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: "{b} : {c}"
                    },
                    xAxis: {
                        data: lable,
                        axisLabel: {
                            interval: 0,
                            rotate: angleX ? angleX : 0
                        },
                        silent: false,
                        splitLine: {
                            show: false
                        }
                    },
                    yAxis: {

                    },
                    series: dataJson,
                    animationEasing: 'elasticOut',
                    animationDelayUpdate: function (idx) {
                        return idx * 5;
                    }
                };
                a.setOption(t, !0);
                a.resize();

            }
            else if (type == "hbar") {

                //----------
                for (var j = 0; j < data.length; j++) {
                    var data2 = [];
                    for (var k = 0; k < lable.length; k++) {
                        var cclr = getColor(colorTitle, lable[k]);
                        if (cclr)
                            data2.push({ value: data[j][k], itemStyle: { color: cclr } });
                        else
                            data2.push(data[j][k]);//{ value: data[j][k], itemStyle: { color: [new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: colorDefault[0] }, { offset: 1, color: colorDefault[1] }])] } });
                    }

                    var json = {
                        name: seriLable[j],//???????/
                        type: 'bar',
                        data: data2,// data[j],//????????/

                        label: {

                            show: showhelp,
                            formatter: function (d) { var c = ""; try { var u = d.data.value ? d.data.value : d.data; c = u.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','); } catch { } return c; },// '{c}',

                            fontFamily: 'BYekan',
                            rotate: angle ? angle : 0,
                            align: 'left',
                            verticalAlign: 'middle',
                            position: 'right',
                            distance: 15,
                            textStyle: {
                                //color: 'black'
                            },
                            fontSize: sizelable ? sizelable : 12

                        },

                        animationDelay: function (idx) {
                            return idx * 5;
                        },
                        itemStyle: {
                            emphasis: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)',
                                color: "#00FFFF",
                                borderColor: "#00FFFF"
                            }
                        }
                    };
                    dataJson.push(json);//????????/
                }
                //---------------

                t = {
                    grid: GetGrid(grid),
                    title: { text: chartTitle ? chartTitle : ' ' },
                    legend: {
                        show: false,//showhelp,
                        //data: seriLable
                         data: showhelp ? seriLable : 'none'
                    },
                    color: [new echarts.graphic.LinearGradient(1, 0, 0, 0, [{ offset: 0, color: colorDefault[0] }, { offset: 1, color: colorDefault[1] }])],

                    toolbox: {
                        // y: 'bottom',

                        /*feature: {

                            dataZoom: {
                                yAxisIndex: false
                            },
                            magicType: {
                                type: ['stack', 'tiled']
                            },
                            dataView: {},
                            saveAsImage: {
                                pixelRatio: 2
                            }
                        }*/
                    },
                    textStyle: {
                        fontFamily: 'BYekan'
                    },
                    tooltip: {},
                    xAxis: {

                    },
                    yAxis: {

                        data: lable,
                        axisLabel: {
                            interval: 0,
                            rotate: angleX ? angleX : 0
                        },
                        silent: false,
                        splitLine: {
                            show: false
                        }
                    },
                    series: dataJson,
                    animationEasing: 'elasticOut',
                    animationDelayUpdate: function (idx) {
                        return idx * 5;
                    }
                };
                a.setOption(t, !0);
                a.resize();



            }

            else if (type == "polar") {
                var dataJson = [];
                for (var j = 0; j < data.length; j++) {
                    var data2 = [];
                    for (var k = 0; k < lable.length; k++) {
                        var cclr = getColor(colorTitle, lable[k]);
                        if (cclr)
                            data2.push({ value: data[j][k], itemStyle: { color: cclr } });
                        else
                            data2.push(data[j][k]);//{ value: data[j][k], itemStyle: { color: [new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: colorDefault[0] }, { offset: 1, color: colorDefault[1] }])] } });
                    }

                    var json = {
                        type: 'bar',
                        data: data2,//data[j],
                        coordinateSystem: 'polar',
                        label: {
                            show: showhelp,
                            formatter: function (d) { var c = ""; try { var u = d.data.value ? d.data.value : d.data; c = u.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','); } catch { } return c; },//'{c}',
                            position: 'insideEnd',
                            fontSize: sizelable ? sizelable : 12
                        },
                        name: seriLable[j],
                        stack: 'a' + j.toString(),
                        itemStyle: {
                            emphasis: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)',
                                color: "#00FFFF",
                                borderColor: "#00FFFF"
                            }
                        }
                    };

                    dataJson.push(json);
                }

                var t = {
                    title: { text: chartTitle ? chartTitle : ' ' },
                    angleAxis: {
                        type: 'category',
                        data: lable,
                        z: 10
                    },

                    tooltip: {
                        trigger: 'item',
                        formatter: "{b} <br/> {c}"
                    },

                    color: colorDefault,
                    toolbox: {

                    },
                    textStyle: {
                        fontFamily: 'BYekan'
                    },
                    radiusAxis: {
                    },
                    polar: {
                        radius: '70%',
                        //center: ['50%', '40%']
                    },
                    series: dataJson,
                    legend: {
                        show: false,
                        //data: seriLable
                        data: showhelp ? seriLable : 'none'
                    },
                    animationDuration: 20000
                };
                a.setOption(t, !0);
                a.resize();
            }
            else if (type == "twobar") {



                if (data.length == 2) {


                    var t = {
                        grid: GetGrid(grid),
                        title: { text: chartTitle ? chartTitle : ' ' },
                        legend: {
                            show: true,
                            data: showhelp ? seriLable : 'none'
                        },
                        color: colorDefault,
                        toolbox: {

                        },
                        textStyle: {
                            fontFamily: 'BYekan'
                        },

                        tooltip: {},
                        xAxis: {
                            data: lable,
                            axisLabel: {
                                interval: 0,
                                rotate: angleX ? angleX : 0
                            },
                            silent: false,
                            splitLine: {
                                show: false
                            }
                        },
                        yAxis: [
                            {
                                type: 'value',
                                splitLine: {
                                    lineStyle: {
                                        type: 'dashed',
                                        color: colorDefault[0],
                                        opacity: .3
                                    }
                                }

                            },
                            {
                                type: 'value',
                                splitLine: {
                                    lineStyle: {
                                        type: 'dashed',
                                        color: colorDefault[1],
                                        opacity: .3
                                    }
                                }

                            }
                        ],
                        series: [
                            {
                                name: seriLable[0],
                                type: charttwobartype != "t3" ? 'bar' : "line",
                                data: data[0],
                                itemStyle: {
                                    emphasis: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)',
                                        color: "#00FFFF",
                                        borderColor: "#00FFFF"
                                    }
                                },
                                label: {
                                    show: showhelp,//checkLable,
                                    formatter: function (d) {  var c = ""; try { c = d.data.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','); } catch { } return c; },//'{c}',
                                    //fontFamily: 'tahoma',
                                    rotate: angle ? angle : 0,
                                    //align: 'left',
                                    verticalAlign: 'middle',
                                    position: 'top',
                                    distance: (targetLables && targetLables.length ? -10 : 15),
                                    textStyle: {
                                        //color: 'black'
                                    },
                                    fontSize: sizelable ? sizelable : 12
                                },
                            },

                            {
                                name: seriLable[1],
                                type: charttwobartype != "t2" ? 'line' : "bar",
                                yAxisIndex: 1,
                                data: data[1],
                                itemStyle: {
                                    emphasis: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)',
                                        color: "#00FFFF",
                                        borderColor: "#00FFFF"
                                    }
                                },
                                label: {
                                    show: showhelp,//checkLable,
                                    formatter: function (d) {   var c = ""; try { c = d.data.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','); } catch { } return c; },//'{c}',
                                    //fontFamily: 'tahoma',
                                    rotate: angle ? angle : 0,
                                    //align: 'left',
                                    verticalAlign: 'middle',
                                    position: 'top',
                                    distance: (targetLables && targetLables.length ? -10 : 15),
                                    textStyle: {
                                        //color: 'black'
                                    },
                                    fontSize: sizelable ? sizelable : 12
                                },
                            }
                        ]
                        //animationEasing: 'backOut',
                        //animationDelayUpdate: function (idx) { return idx * 10; }
                    };


                    a.setOption(t, !0);
                    a.resize();
                }
                else {
                    toastr.error('برای این گزارش حتما باید دو فیلد انتخاب شود', 'خطا')
                }
            }
            else if (type == "treemap") {



                var nodes = [];
                for (var j = 0; j < lable.length; j++) {
                    var newNode = {
                        name: lable[j],//???????/
                        value: data[0][j]
                    };
                    var cclr = getColor(colorTitle, lable[j]);
                    if (cclr)
                        newNode.itemStyle = { color: cclr };
                    nodes.push(newNode);

                }

                var t = {
                    title: { text: chartTitle ? chartTitle : ' ' },
                    tooltip: {
                        trigger: 'item',
                        formatter: function (d) { var c = ""; try { c = d.data.name + ": " + d.data.value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','); } catch { } return c; },//"{b}: {c}"
                    },
                    toolbox: {
                        show: false
                    },
                    calculable: false,
                    textStyle: {
                        fontFamily: 'BYekan'
                    },
                    series: [
                        {
                            name: seriLable[0],
                            type: 'treemap',
                            itemStyle: {
                                normal: {
                                    label: {
                                        show: true,
                                        formatter: "{b}"
                                    },
                                    borderWidth: 1
                                },
                                emphasis: {
                                    label: {
                                        show: true
                                    },
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)',
                                    color: "#00FFFF",
                                    borderColor: "#00FFFF"
                                }
                            },

                            data: nodes
                        }
                    ]
                };



                a.setOption(t, !0);
                a.resize();
            }
            else if (type == "gauge") {





                var clrs = [];
                var firstLabel = lable[0];
                var firstValue = 0;

                var minValue = 0;
                var maxValue = 100;

                if (data[0].length == 1) {

                    firstValue = parseFloat(data[0]);

                    if (data.length > 1)
                        minValue = parseFloat(data[1]);
                    if (data.length > 2)
                        maxValue = parseFloat(data[2]);
                    for (var ik = 0; ik < data.length; ik++) {
                        if (ik >2) {
                            var v = parseFloat(data[ik]);
                            if (v != 0)
                                clrs.push([v, colorDefault.length > ik ? colorDefault[ik] : colorDefault[0]]);
                      ID }
                    }
                    clrs.push([maxValue, colorDefault[data.length]]);

                }
                else {
                    firIDstValue = parseFloat(data[0][0]);

                    if (data[0].length > 1)
                        minValue = parseFloat(data[0][1]);
                    if (data[0].length > 2)
                        maxValue = parseFloat(data[0][2]);



                    for (var ik = 0; ik < data[0].length; ik++) {
                         if (ik != 0 && ik != 1 && ik != 2) {
                            var v = parseFloat(data[0][ik]);
                            if (v != 0)
                                clrs.push([v, colorDefault.length > ik ? colorDefault[ik] : colorDefault[0]]);
                        }
                    }

                    clrs.push([maxValue, colorDefault[data[0].length]]);


                }


                var d = maxValue - minValue;


                clrs = clrs.sort((a, b) => a[0] - b[0]);
                var clrsRaw = clrs;
                for (var j = 0; j < clrs.length; j++) {
                    clrs[j][0] = (clrs[j][0] - minValue) / d;
                }




                var t = {
                    //grid: GetGrid(grid),
                    tooltiPaddingdingp: {
                        formatter: "{a} <br/>{c} : {b}"
                    },
                    toolbox: {
                        left: 'left',
                        top: 'top',
                        feature: {
                             saveAsImage: {
                                title: 'خروجی',
                                pixelRatio: 2
                            }
                        },
                        show: false
                    },
                    textStyle: {
                        fontFamily: 'BYekan'
                    },
                    series: [
                        {
                            name: '',
                            type: 'gauge',
                            z: 3,
                            min: minValue,
                            max: maxValue,
                            splitNumber: 1,
                            radius: '100%',
                            axisLine: {
                                lineStyle: {
                                    color: clrs,
                                    width: 10,
                                }
                            },
                            axisTick: {
                                length: 15,
                                lineStyle: {
                                    color: 'auto'
                                }
                            },
                            splitLine: {
                                length: 20,
                                lineStyle: {
                                    color: 'auto'
                                }
                            },
                            axisLabel: {
                                show: true,
                                color: '#464646',
                                fontSize: 20,
                                distance: 0,
                                fontFamily: 'BYekan'

                            },
                            title: {
                                fontFamily: 'BYekan',

                                fontSize: 13,
                                fontStyle: 'normal'
                            },
                            detail: {
                                formatter: function (value) {
                                    return value;
                                    //value = (value + '').split('.');
                                    //value.length < 2 && (value.push('00'));
                                    //return ('00' + value[0]).slice(-2)
                                    //    + '.' + (value[1] + '00').slice(0, 2);
                                },
                                fontWeight: 'bolder',
                                //borderRadius: 30,
                                //backgroundColor: '#fff',
                                //borderColor: '#aaa',
                                //shadowBlur: 3,
                                //shadowColor: '#888',
                                //shadowOffsetX: 0,
                                //shadowOffsetY: 3,
                                //borderWidth: 2,
                                //textBorderColor: '#000',
                                textBorderWidth: 2,
                                //textShadowBlur: 2,
                                textShadowColor: '#fff',
                                //textShadowOffsetX: 0,
                                //textShadowOffsetY: 0,
                                fontFamily: 'BYekan',
                                fontSize: 20,
                                width: 100,
                                color: '#1565c0',
                                rich: {}
                            },
                            data: [{ value: firstValue, name: firstLabel }]
                        },
                    ]
                };
                a.setOption(t, !0);
                a.resize();
            }
            else if (type == "stack") {



                //----------
                for (var j = 0; j < data.length; j++) {
                    var data2 = [];
                    for (var k = 0; k < lable.length; k++) {
                        var cclr = getColor(colorTitle, lable[k]);
                        if (cclr)
                            data2.push({ value: data[j][k], itemStyle: { color: cclr } });
                        else
                            data2.push(data[j][k]);//{ value: data[j][k], itemStyle: { color: [new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: colorDefault[0] }, { offset: 1, color: colorDefault[1] }])] } });
                    }

                    var json = {
                        name: seriLable[j],
                        type: 'bar',
                        stack: 'Total',
                        data:data2,// data[j],

                        label: {

                            show: showhelp,//checkLable,
                            formatter: function (d) { var c = ""; try { var u = d.data.value ? d.data.value : d.data; c = u.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','); } catch { } return c; },// '{c}',
                            //fontFamily: 'tahoma',
                            rotate: angle ? angle : 0,
                            align: 'left',
                            verticalAlign: 'middle',
                            position: 'top',
                            distance: 0,
                            textStyle: {
                                //color: 'black'
                            },
                            fontSize: sizelable ? sizelable : 12

                        },

                        animationDelay: function (idx) {
                            return idx * 10;
                        },
                        itemStyle: {
                            emphasis: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)',
                                color: "#00FFFF",
                                borderColor: "#00FFFF"
                            }
                        }
                    };
                    dataJson.push(json);//????????/
                }

                t = {
                    grid: GetGrid(grid),
                    title: {
                        text: ''
                    },
                    legend: {
                        data: seriLable,
                        show: true
                    },
                    color: colorDefault,//[new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: colorDefault[0] }, { offset: 1, color: colorDefault[1] }])],
                    toolbox: {
                        show: false
                    },
                    textStyle: {
                        fontFamily: 'BYekan'
                    },
                    tooltip: {
                        trigger: 'axis',
                    },
                    xAxis: {
                        type: 'category',
                        data: lable/*,
                        axisLabel: {
                            interval: 0,
                            rotate: angle
                        },
                        silent: false,
                        splitLine: {
                            show: false
                        }*/
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: dataJson,
                    /*animationEasing: 'elasticOut',
                    animationDelayUpdate: function (idx) {
                        return idx * 5;
                    }*/
                };
                a.setOption(t, !0);
                a.resize();

            }

            a.on('click', function (params) {


                if ((idLable && idLable.length > 0) || (titleLable && titleLable.length > 0) || (valueLable && valueLable.length > 0)) {
                    if (type == "treemap")
                        LoadPanel(panelID, idLable[params.dataIndex - 1], titleLable[params.dataIndex - 1], valueLable[params.dataIndex - 1], valueLable1[params.dataIndex - 1], valueLable2[params.dataIndex - 1], valueLable3[params.dataIndex - 1], valueLable4[params.dataIndex - 1], valueLable5[params.dataIndex - 1]);
                    else
                        LoadPanel(panelID, idLable[params.dataIndex], titleLable[params.dataIndex], valueLable[params.dataIndex], valueLable1[params.dataIndex], valueLable2[params.dataIndex], valueLable3[params.dataIndex], valueLable4[params.dataIndex], valueLable5[params.dataIndex]);
                }
                else { LoadPanel(panelID);}



                ClearSelected(lvl);

                a.dispatchAction({
                    type: 'highlight',
                    seriesIndex: type == "twobar" ? [0, 1] : params.seriesIndex,
                    dataIndex: params.dataIndex
                });


            });
            initOptions.push(t);
            initCharts.push(a);

        }



        function ClearSelected(lvl) {
            $("." + lvl + " .trsss").removeClass('activeSelect');//.css('background-color', '');
            $('.' + lvl).removeClass('activeSelect');//.css('background-color', '');

            for (var i = 0; i < initCharts.length; i++) {
                var a = initCharts[i];
                if (a.lvl == lvl) {
                    var t = initOptions[i];
                    a.clear();
                    a.setOption(t, !0);
                    a.resize();
                }
            }
        }

        function GetGrid(str) {
            var c = str.split(',');
            try {
                var top = c.length > 0 ? parseInt(c[0]) : 30;
                var right = c.length > 1 ? parseInt(c[1]) : 20;
                var bottom = c.length > 2 ? parseInt(c[2]) : 30;
                var left = c.length > 3 ? parseInt(c[3]) : 20;

                return { top: top, bottom: bottom, right: right, left: left };
            }
            catch (err) {
                return { top: 0, bottom: 0, right: 0, left: 0 };
            }

        }

        function GetGridDataTable(id, input, input1, input2, input3, input4, input5, input6, input7,where) {
            $("#pnl" + id).css("mix-blend-mode", "overlay");
            $("#DivPanel" + id).fadeIn(400, function () {

                $.ajax({
                    type: "Get",
                    url: "/Dashboard/GetDataGrid?id=" + id + "&input=" + input + "&input1=" + input1 + "&input2=" + input2 + "&input3=" + input3 + "&input4=" + input4 + "&input5=" + input5 + "&input6=" + input6 + "&input7=" + input7 + "&where=" + where,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json"
                }).done(function (result) {

                    $("#pnl" + id).css("mix-blend-mode", "");
                    var head = '';
                    var body = '';
                    var response = result.data;

                    for (var jj = 0; jj < response.length; jj++) {
                        var names = Object.keys(response[jj]);

                        var tr = "";
                        var obj = {};
                        var colid = undefined;
                        var coltitle = undefined;
                        var colvalue = undefined;
                        var colvalue1 = undefined;
                        var colvalue2 = undefined;
                        var colvalue3 = undefined;
                        var colvalue4 = undefined;
                        var colvalue5 = undefined;
                        for (var ii = 0; ii < names.length; ii++) {
                            if (names[ii].toLocaleLowerCase() == 'id')
                                colid = response[jj][names[ii]];
                            else if (names[ii].toLocaleLowerCase() == 'title')
                                coltitle = response[jj][names[ii]];
                            else if (names[ii].toLocaleLowerCase() == 'value')
                                colvalue = response[jj][names[ii]];
                            else if (names[ii].toLocaleLowerCase() == 'value1')
                                colvalue1 = response[jj][names[ii]];
                            else if (names[ii].toLocaleLowerCase() == 'value2')
                                colvalue2 = response[jj][names[ii]];
                            else if (names[ii].toLocaleLowerCase() == 'value3')
                                colvalue3 = response[jj][names[ii]];
                            else if (names[ii].toLocaleLowerCase() == 'value4')
                                colvalue4 = response[jj][names[ii]];
                            else if (names[ii].toLocaleLowerCase() == 'value5')
                                colvalue5 = response[jj][names[ii]];
                            else {
                                if (response[jj][names[ii]] != null && response[jj][names[ii]] != "" && response[jj][names[ii]].toString().match(/^\d+$/) != null)
                                    obj[names[ii]] = parseFloat(response[jj][names[ii]]).toFixed(0).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                                else
                                    obj[names[ii]] = response[jj][names[ii]];

                                if (jj == 0)
                                    head += "<th>" + names[ii] + "</th>";
                                tr += "<td>" + obj[names[ii]] + "</td>";
                            }



                        }
                        body += "<tr  class='trsss' " + (colid != undefined ? " onclick='LoadPanel(\"" + id + "\", \"" + colid + "\", \"" + coltitle + "\", \"" + colvalue + "\", \"" + colvalue1 + "\", \"" + colvalue2 + "\", \"" + colvalue3 + "\", \"" + colvalue4 + "\", \"" + colvalue5 + "\");setRowClicked(this);' style='cursor: pointer;'" : "") + ">" + tr + "</tr>";
                        // newResponse.push(obj);
                    }

                    var html = '<thead class="bg-warning-200"><tr>' + head + ' </tr></thead><tbody>' + body + '</tbody>';
                    $("#TablePanel" + id).html(html);

                    $("#TablePanel" + id).dataTable({
                        destroy: true,
                        searching: true,
                        responsive: true,
                        paging: false,
                        info: false,
                        scrollY: 340,
                        //scrollCollapse: true,
                        //scroller: true,
                        fixedHeader: true,
                        "language": {
                            "lengthMenu": "نمایش _MENU_ رکورد",
                            "zeroRecords": "هیچ داده ای یافت نشد",
                            "info": "نمایش صفحه _PAGE_ از _PAGES_",
                            "infoEmpty": "هیچ رکوردی یافت نشد",
                            "infoFiltered": "(فیلتر شده از _MAX_ کل رکوردها)",
                            "search": "",
                            "searchPlaceholder":"جستجو"
                        }
                    });

                });
            });



        }
        function GetGridSimple(id, input, input1, input2, input3, input4, input5, input6, input7,where) {
            $("#pnl" + id).css("mix-blend-mode", "overlay");
            $("#DivPanel" + id).fadeIn(400, function () {
                $.ajax({
                    type: "Get",
                    url: "/Dashboard/GetDataGrid?id=" + id + "&input=" + input + "&input1=" + input1 + "&input2=" + input2 + "&input3=" + input3 + "&input4=" + input4 + "&input5=" + input5 + "&input6=" + input6 + "&input7=" + input7 + "&where=" + where,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json"
                }).done(function (result) {

                    $("#pnl" + id).css("mix-blend-mode", "");

                    var body = '';
                    var response = result.data;

                    if (response.length > 0) {
                        var jj = 0;
                        var names = Object.keys(response[jj]);
                        body += "<tr>";
                        for (var ii = 0; ii < names.length; ii++)
                            body += '<td class="bg-warning-' + 100 * (ii + 1) + '">' + names[ii] + '</td>';
                        body += "</tr>";

                    }
                    for (var jj = 0; jj < response.length; jj++) {
                        var names = Object.keys(response[jj]);

                        var tr = "";
                        var obj = {};
                        var colid = undefined;
                        var coltitle = undefined;
                        var colvalue = undefined;
                        var colvalue1 = undefined;
                        var colvalue2 = undefined;
                        for (var ii = 0; ii < names.length; ii++) {
                            if (names[ii].toLocaleLowerCase() == "id")
                                colid = response[jj][names[ii]];
                            else if (names[ii].toLocaleLowerCase() == "title")
                                coltitle = response[jj][names[ii]];
                            else if (names[ii].toLocaleLowerCase() == "value")
                                colvalue = response[jj][names[ii]];
                            else if (names[ii].toLocaleLowerCase() == "value1")
                                colvalue1 = response[jj][names[ii]];
                            else if (names[ii].toLocaleLowerCase() == "value2")
                                colvalue2 = response[jj][names[ii]];
                            else
                            {
                                if (response[jj][names[ii]] != null && response[jj][names[ii]] != "" && response[jj][names[ii]].toString().match(/^\d+$/) != null)
                                    obj[names[ii]] = parseFloat(response[jj][names[ii]]).toFixed(0).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                                else
                                    obj[names[ii]] = response[jj][names[ii]];
                            }




                            tr += "<td>" + obj[names[ii]] + "</td>";

                        }
                        body += "<tr  class='trsss' " + (colid != undefined ? " onclick='LoadPanel(\"" + id + "\", \"" + colid + "\", \"" + coltitle + "\", \"" + colvalue + "\", \"" + colvalue1 + "\", \"" + colvalue2 + "\");setRowClicked(this);' style='cursor: pointer;'" : "") + ">" + tr + "</tr>";
                    }

                    var html = '<tbody>' + body + '</tbody>';
                    $("#TablePanel" + id).html(html);

                    $("#TablePanel" + id).dataTable({
                        destroy: true,
                        searching: true,
                        responsive: true,
                        paging: false,
                        info: false,
                        scrollY: 200,
                        scrollCollapse: true,
                        scroller: true,
                        fixedHeader: {
                            header: true,
                            footer: true
                        },
                        "language": {
                            "lengthMenu": "نمایش _MENU_ رکورد",
                            "zeroRecords": "هیچ داده ای یافت نشد",
                            "info": "نمایش صفحه _PAGE_ از _PAGES_",
                            "infoEmpty": "هیچ رکوردی یافت نشد",
                            "infoFiltered": "(فیلتر شده از _MAX_ کل رکوردها)",
                            "search": "",
                            "searchPlaceholder": "جستجو"
                        }
                    });

                });
            });




        }

        function setRowClicked(tr) {


            var cs = $(tr).closest('.panel').attr("class").split(/\s+/);
            ClearSelected(cs[cs.length - 1]);

            $(tr).addClass('activeSelect');//.css('background-color', 'cyan');
        }
        function setPanelClicked(id) {

            var cs = $('#pnl' + id).attr("class").split(/\s+/);
            ClearSelected(cs[cs.length - 1]);

            $('#pnl' + id).addClass('activeSelect');//.css('background-color', 'cyan');
        }


        function ShowHtml(id, type1, input, input1, input2, input3, input4, input5, input6, input7, where, force) {
            $("#pnl" + id).css("mix-blend-mode", "overlay");
            if (where || force) {
                $.ajax({
                    type: "Get",
                    url: "/Dashboard/GetDataHtml?id=" + id + "&input=" + input + "&input1=" + input1 + "&input2=" + input2 + "&input3=" + input3 + "&input4=" + input4 + "&input5=" + input5+ "&input6=" + input6 + "&input7=" + input7 + "&where=" + where,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json"
                }).done(function (result) {
                    if (result.ok) {
                        if (type1 == 3)
                            $("#ContentPanel" + id).attr("src", "/Service/map?" + result.data);
                        else
                            $("#ContentPanel" + id).html(result.data);

                    }
                    $("#pnl" + id).css("mix-blend-mode", "");
                });
            }
            else {
                $("#DivPanel" + id).fadeIn(40);
                $("#pnl" + id).css("mix-blend-mode", "");
            }
        }

        $('document').ready(function () {

            LoadPanel();
            $('.htm').slimScroll({
                position: 'left',
                height: '198px',
                railVisible: true,
                 alwaysVisible: false,
                 opacity: 0
             });
            $('.htm05').slimScroll({
                position: 'left',
                height: '99px',
                railVisible: true,
                alwaysVisible: false,
                opacity: 0
            });
            $('.htm05').css("height", '100px');

            $('.htm1').slimScroll({
                position: 'left',
                height: '198px',
                railVisible: true,
                alwaysVisible: false,
                opacity: 0
            });
            $('.htm1').css("height", '200px');

            $('.htm2').slimScroll({
                position: 'left',
                height: '473px',
                railVisible: true,
                alwaysVisible: false,
                opacity: 0
            });
            $('.htm2').css("height", '475px');
            $('.htm3').slimScroll({
                position: 'left',
                height: '748px',
                railVisible: true,
                alwaysVisible: false,
                opacity: 0
            });
            $('.htm3').css("height", '750px');

            $('.select2').select2({
                dir: "rtl"

            });
          //  $('.select2').on("select2:unselect", function (e) {   $(e.target).select2("close");});
            $(".select2").on('select2:unselecting', function (e) {
                $(".select2").on('select2:opening', function (ev) {
                    ev.preventDefault();
                    $(".select2").off('select2:opening');
                });
            });

            $('.selectAjax').select2({
                dir: "rtl",
                ajax: {
                    url: function () {
                        return getURL(this)
                    },
                    dataType: 'json'
                    // Additional AJAX parameters go here; see the end of this chapter for the full code of this example
                },
               // allowClear:true
            });
            function getURL(e) {

                return '/dashboard/SearchDataField?field=' + $(e).attr('name');
            };


        });

        var isLoadingPanel = false;
        function LoadPanel(PanelID, input, input1, input2, input3, input4, input5, input6, input7) {

            if (!isLoadingPanel) {
                isLoadingPanel = true;
                var lvl = 1;



                if (!PanelID)
                    PanelID = '';
                else {
                    lvl = parseInt($("#pnl" + PanelID).attr('data-lvl')) + 1;

                }
                if (!input)
                    input = '';
                if (!input1)
                    input1 = '';
                if (!input2)
                    input2 = '';
                if (!input3) input3 = '';
                if (!input4) input4 = '';
                if (!input5) input5 = '';
                if (!input6) input6 = '';
                if (!input7) input7 = '';



                RemovePanel(lvl);
                if (lvl == 1)
                    $("#loadingImg").show();
                $.ajax({
                    type: "Get",
                    url: '/Dashboard/GetDashboardPanels?DashboardID=@DashboardID&PanelID=' + PanelID + '&input=' + input + '&input1=' + input1 + '&input2=' + input2 + '&input3=' + input3 + '&input4=' + input4 + '&input5=' + input5 + '&input6=' + input6 + '&input7=' + input7,
                    success: function (result) {

                        if (result.ok) {

                            var data = result.data;
                            if (data && data.length > 0) {
                                for (var i = 0; i < data.length; i++) {
                                    var items = data[i].Items;
                                    if (items && items.length > 0) {
                                        var item2 = items[0];
                                        for (var j = 0; j < items.length; j++) {
                                            var item = items[j];
                                            var sizeh = item.SizeH ? item.SizeH : 1;
                                            sizeh = sizeh < 0.5 ? 1 : sizeh;
                                            DrawPanel(item2.ID, item2.Size, item.ID, item.Name, item.Name2, @(isAdmin == true ? "true" : "false"), sizeh, item.Padding, item.HTML, item.Type1, item.Type2, item.Parameters, lvl, input, input1, input2, input3, input4, input5, input6, input7, item.FilterByField, item.InputMapping, item.BackgroundColor, item.HideHeader);
                                            LoadPanelData(item.ID, item.HTML ? true : false, item.Type1, item.Type2, input, input1, input2, input3, input4, input5, input6, input7, GetWhere(item.ID));


                                            if (lvl > 1) {
                                                $([document.documentElement, document.body]).animate({
                                                    scrollTop: $(".lvl" + lvl).offset().top
                                                }, 100);
                                            }
                                        }
                                    }

                                }


                                ShowHideFilter();
                            }
                            else {
                                if (result.msg && result.msg.MessagePanel && result.msg.MessagePanel!='')
                                    eval('toastr.' + (result.msg.MessageColor && result.msg.MessageColor != '' ? result.msg.MessageColor : 'error') + '(result.msg.MessagePanel, "");');
                                else
                                    toastr.error("هیچ پنلی وجود ندارد", "");
                            }



                            if (result.url && result.url.trim() != '')
                                window.open(result.url, '_blank');


                        }
                        else {
                            toastr.error(result.msg, "خطا");
                        }
                        $("#loadingImg").hide();
                        isLoadingPanel = false;
                    },
                    error: function () {
                        $("#loadingImg").hide();
                        isLoadingPanel = false;
                    }
                });
            }
        }
        function DrawPanel(parentid, parentsize, id, Name, Name2, isAdmin, sizeh, Padding, HTML, Type1, Type2, Parameter, lvl, input, input1, input2, input3, input4, input5, input6, input7, FilterByField, InputMapping, BackgroundColor, HideHeader) {


            var SSIZE = (sizeh * 200 + (sizeh - 1) * 75) + (HideHeader ? 48 : 0);
            if (!lvl)
                lvl = 1;
            var Parameters = {};
            if (Parameter)
                Parameters = JSON.parse(Parameter);
            var colors = [];@for (int i = 0; i < colors.Count; i++)
        {
            <text>colors.push('@colors[i].Name');</text>
        }

            var html = '';
            var isHideOverflow = false;
            if ((HTML && Type1 == 3) || (Type2 != 2 && Type2 != 3))
                isHideOverflow = true;

            //html += '<div class="panel" data-panel-close="false" data-panel-color="false" data-panel-locked="false" data-panel-refresh="false" data-panel-reset="false" data-panel-fullscreen="false" data-panel-collapsed="false">';
            html += '   <div class="panel lvl' + lvl + '" id="pnl' + id + '"  style="background-color:' + BackgroundColor + '"  data-lvl="' + lvl + '" data-input="' + input + '" data-input1="' + input1 + '" data-input2="' + input2 + '" data-input3="' + input3 + '" data-input4="' + input4 + '"  data-input5="' + input5 + '"  data-input6="' + input6 + '"  data-input7="' + input7 + '" data-type1="' + Type1 + '" data-type2="' + Type2 + '" data-isHtml="' + (HTML ? true : false) + '" data-filter="' + FilterByField + '" data-InputMapping="' + InputMapping + '" data-panel-close="false" data-panel-color="false" data-panel-locked="false" data-panel-refresh="false" data-panel-reset="false" data-panel-fullscreen="false" data-panel-collapsed="false" onmouseover="actviePanelID=' + id + ';">';
            html += '       <div class="panel-hdr" ' + (HideHeader ? 'style="display:none"':'') + '>';
            html += '           <h2 style=" white-space: nowrap;"><b>' + Name + '</b>' + (Name2 && Name2 != '' ? '<sup><span class="badge badge-warning ml-2">' + Name2 + '</span></sup>' : '') + '</h2>';
            html += '           <div class="panel-toolbar mr-3">';
            html += '               <button class="btn btn-panel bg-transparent fs-xl w-auto h-auto rounded-0" data-action="panel-fullscreen" data-toggle="tooltip" data-original-title="بزرگ کردن" onclick="setTimeout(function () { fullscrn(' + id + '); }, 100)"><i id="if' + id + '" class="ni ni-size-fullscreen"></i></button>';
            //html += '               <button class="btn btn-panel bg-transparent fs-xl w-auto h-auto rounded-0" data-action="panel-collapse" data-toggle="tooltip" data-original-title="کوچک کردن" onclick="setTimeout(function () { minscrn(' + id + '); }, 1000)"><i id="im' + id + '" class="fal fa-minus"></i></button> ';
            html += '               <button class="btn btn-panel bg-transparent fs-xl w-auto h-auto rounded-0" data-toggle="tooltip" data-offset="0,10" data-original-title="تنظیمات نمایش نمودار" onclick="SetParsmeter(' + id + ',\'' + input + '\',\'' + input1 + '\',\'' + input2 + '\',\'' + input3 + '\',\'' + input4 + '\',\'' + input5 + '\',\'' + input6 + '\',\'' + input7 + '\');"><i class="fal fa-chart-bar"></i></button>';
            html += isAdmin ? '     <button class="btn btn-toolbar-master" data-toggle="dropdown" id="btn' + id + '"><i class="fal fa-ellipsis-v"></i></button><div class="dropdown-menu dropdown-menu-animated dropdown-menu-left"> <div class="dropdown-divider m-0"></div> <button class="dropdown-item  " onclick="GetPanel(' + id + ');"> <i class="fal fa-edit"></i> تنظیمات ساختار </button> <button class="dropdown-item  " onclick="DeletePanel(' + id + ');"> <i class="fal fa-trash "></i> حذف </button> </div>' : '';
            html += '           </div>';
            html += '       </div>';
            html += '       <div class="panel-container show">';
            html += '           <div class="panel-content htm' + sizeh.toString().replace(".","") + '  PNL" style="height:' + SSIZE + 'px;' + (Padding != null ? "padding:" + Padding + "px !important;" : "") + (isHideOverflow ? "overflow-y: hidden !important;" : "") + '">';



            if (HTML) {
                if (Type1 == 3)
                    html += '        <iframe id="ContentPanel' + id + '" src="/Service/map?' + HTML + '" style="width:100%;height:100%;"></iframe>';
                else
                    html += '       <div id="ContentPanel' + id + '"  class="form-row" style="margin-bottom: 0px">' + HTML + '</div>';
            }
            else if (Type2 == 2) {
                html += '<table id="TablePanel' + id + '" class="table table-bordered table-hover table-striped w-100"> </table>';
            }
            else if (Type2 == 3) {
                var type = "line";
                var color = 0;
                var colorTitle = '';
                var showhelp = false;
                var grid = "";
                var angle = 0;
                var angleX = '';
                var sizelabel = "";
                var showdecimal = true;
                var TargetColor = 0;
                var TargetBaseColor = 0;
                var TargetShow = false;
                var targetLabel = false;
                var TargetAngle = 0;
                var TargetSize = 12;
                var TargetWidth = 30;
                var TargetShowDecimal = false;
                var TargetHeight = 1;
                if (Parameters) {
                    type = Parameters.Type;
                    color = Parameters.Color ? Parameters.Color : 0;
                    colorTitle = Parameters.ColorTitle ? Parameters.ColorTitle : '';
                    showhelp = Parameters.ShowHelp ? true : false;
                    angle = Parameters.Angle ? Parameters.Angle : 0;
                    angleX = Parameters.AngleX ? Parameters.AngleX : '';
                    grid = Parameters.Grid ? Parameters.Grid : "";
                    sizelabel = Parameters.SizeLabel ? Parameters.SizeLabel : "";
                    showdecimal = Parameters.ShowDecimal ? true : false;
                    TargetColor = Parameters.TargetColor ? Parameters.TargetColor : 0;
                    TargetBaseColor = Parameters.TargetBaseColor ? Parameters.TargetBaseColor : 0;
                    TargetShow = Parameters.TargetShow ? true : false;
                    targetLabel = Parameters.TargetLabel ? true : false;
                    TargetAngle = Parameters.TargetAngle ? Parameters.TargetAngle : 0;
                    TargetSize = Parameters.TargetSize ? Parameters.TargetSize :  12;
                    TargetWidth = Parameters.TargetWidth ? Parameters.TargetWidth :  30;
                    TargetShowDecimal = Parameters.TargetShowDecimal ? true : false;
                    TargetHeight = Parameters.TargetHeight ? Parameters.TargetHeight : 1;
                    ChartTypeTwoBar = Parameters.TypeTwoBar;
                }
                html += '          <div class="form-row" id="EditParameter' + id + '" style="display:none;padding:10px;">';
                html += '               <div class="col-md-6 mb-3">';
                html += '                   <label class="form-label" for="Name"> نوع نمودار </label>';
                html += '                   <select id="ChartType' + id + '" class="form-control" >';
                html += '                       <option value="line" ' + (type == "line" ? "selected" : "") + '>خطی</option>';
                html += '                       <option value="bar" ' + (type == "bar" ? "selected" : "") + '>ستونی</option>';
                html += '                       <option value="hbar" ' + (type == "hbar" ? "selected" : "") + '>افقی</option>';
                html += '                       <option value="area" ' + (type == "area" ? "selected" : "") + '>ناحیه ای</option>';
                html += '                       <option value="pie" ' + (type == "pie" ? "selected" : "") + '>دایره ای</option>';
                html += '                       <option value="rose" ' + (type == "rose" ? "selected" : "") + '>گلبرگ</option>';
                html += '                       <option value="polar" ' + (type == "polar" ? "selected" : "") + '>شعاعی</option>';
                html += '                       <option value="twobar" ' + (type == "twobar" ? "selected" : "") + '>دومحوره</option>';
                html += '                       <option value="treemap" ' + (type == "treemap" ? "selected" : "") + '>مستطیلی</option>';
                html += '                       <option value="gauge" ' + (type == "gauge" ? "selected" : "") + '>سرعت سنج</option>';
                html += '                       <option value="stack" ' + (type == "stack" ? "selected" : "") + '>پشته</option>';
                html += '                   </select>';
                html += '                   <input type="text" class="custom-control-input" id="ChartTypeTwoBar' + id + '"  value="' + ChartTypeTwoBar + '" >';
                html += '               </div>';
                html += '               <div class="col-md-6 mb-3">';
                html += '                   <label class="form-label" for="Name"> رنگ نمودار </label>';
                html += '                   <select id="ChartColor' + id + '" class="form-control" >';
                for (var ii = 0; ii < colors.length; ii++) {
                    html += '                   <option value="' + ii + '" ' + (color == ii ? "selected" : "") + '>' + (colors[ii]) + '</option>';
                }
                html += '                   </select><input type="hidden" id="ChartColorTitle' + id + '" value="' + colorTitle + '" />"';
                html += '               </div>';
                html += '               <div class="col-md-12 mb-3">';
                html += '                   <label class="form-label" for="Grid' + id + '">فاصله از طرفین   </label>';
                html += '                   <input type="text" class="form-control" id="Grid' + id + '" name="Grid' + id + '" placeholder="top,right,bottom,left" value="' + grid + '" >';
                html += '               </div>';
                html += '               <div class="col-md-6 mb-3">';
                html += '                   <label class="form-label" for="Angle' + id + '">زاویه   </label>';
                html += '                   <input type="text" class="form-control" id="Angle' + id + '" name="Angle' + id + '" placeholder="زاویه" value="' + angle + '"  >';
                html += '               </div>';
                html += '               <div class="col-md-6 mb-3">';
                html += '                   <label class="form-label" for="AngleX' + id + '">زاویه   </label>';
                html += '                   <input type="text" class="form-control" id="AngleX' + id + '" name="AngleX' + id + '" placeholder="زاویه" value="' + angleX + '"  >';
                html += '               </div>';
                html += '               <div class="col-md-6 mb-3  ">';
                html += '                   <div class="custom-control custom-checkbox">';
                html += '                       <input type="checkbox" class="custom-control-input" id="ShowHelp' + id + '" ' + (showhelp ? "checked" : "") + '  >';
                html += '                       <label class="custom-control-label" for="ShowHelp' + id + '">نمایش مقادیر</label>';
                html += '                   </div>';
                html += '               </div>';
                html += '               <div class="col-md-6 mb-3">';
                html += '                   <label class="form-label" for="Size' + id + '">اندازه   </label>';
                html += '                   <input type="text" class="form-control" id="Size' + id + '" name="Size' + id + '" placeholder="اندازه" value="' + sizelabel + '"  >';
                html += '               </div>';
                html += '               <div class="col-md-6 mb-3  ">';
                html += '                   <div class="custom-control custom-checkbox">';
                html += '                       <input type="checkbox" class="custom-control-input" id="ShowDecimal' + id + '" ' + (showdecimal ? "checked" : "") + '  >';
                html += '                       <label class="custom-control-label" for="ShowDecimal' + id + '">نمایش اعشار</label>';
                html += '                   </div>';
                html += '               </div>';
                html += '           </div>';
                html += '           <div class="row">';
                html += '               <div class="col-md-6">';
                html += '                    <table id="TablePanel' + id + '" class="table m-0 table-hover table-striped table-light"></table>';
                html += '               </div>';
                html += '               <div class="col-md-6">';
                html += '                   <div id="ContentPanel' + id + '" class="form-row">';
                html += '                       <div style="height:' + SSIZE + 'px;"></div>';
                html += '                    </div>';
                html += '               </div>';
                html += '            </div>';

                html += '<select id="TargetColor' + id + '" class="form-control" >';
                for (var ii = 0; ii < colors.length; ii++) {
                    html += '<option value="' + ii + '" ' + (TargetColor == ii ? "selected" : "") + '>' + (colors[ii]) + '</option>';
                }
                html += '</select>';
                html += '<select id="TargetBaseColor' + id + '" class="form-control" >';
                for (var ii = 0; ii < colors.length; ii++) {
                    html += '<option value="' + ii + '" ' + (TargetBaseColor == ii ? "selected" : "") + '>' + (colors[ii]) + '</option>';
                }
                html += '</select>';
                html += '<input type="checkbox" class="custom-control-input" id="TargetShow' + id + '" ' + (TargetShow ? "checked" : "") + '  >';
                html += '<input type="checkbox" class="custom-control-input" id="TargetLabel' + id + '" ' + (targetLabel ? "checked" : "") + '  >';
                html += '<input type="checkbox" class="custom-control-input" id="Target' + id + '"   >';
                html += '<input type="text" class="custom-control-input" id="TargetAngle' + id + '"  value="' + TargetAngle + '"  >';
                html += '<input type="text" class="custom-control-input" id="TargetSize' + id + '"   value="' + TargetSize + '"  >';
                html += '<input type="text" class="custom-control-input" id="TargetWidth' + id + '"   value="' + TargetWidth + '"  >';
                html += '<input type="text" class="custom-control-input" id="TargetHeight' + id + '"   value="' + TargetHeight + '"  >';
                html += '<input type="checkbox" class="custom-control-input" id="TargetShowDecimal' + id + '" ' + (TargetShowDecimal ? "checked" : "") + '  >';


            }
            else {
                var type = "line";
                var color = 0;
                var colorTitle = '';
                var showhelp = false;
                var grid = "";
                var angle = 0;
                var angleX = '';
                var sizelabel = "";
                var showdecimal = true;
                var TargetColor = 0;
                var TargetBaseColor = 0;
                var TargetShow = false;
                var targetLabel = false;
                var TargetAngle = 0;
                var TargetSize = 12;
                var TargetWidth = 30;
                var TargetShowDecimal = false;
                var TargetHeight = 1;
                if (Parameters) {
                    type = Parameters.Type;
                    color = Parameters.Color ? Parameters.Color : 0;
                    colorTitle = Parameters.ColorTitle ? Parameters.ColorTitle : '';
                    showhelp = Parameters.ShowHelp ? true : false;
                    angle = Parameters.Angle ? Parameters.Angle : 0;
                    angleX = Parameters.AngleX ? Parameters.AngleX : '';
                    grid = Parameters.Grid ? Parameters.Grid : "";
                    sizelabel = Parameters.SizeLabel ? Parameters.SizeLabel : "";
                    showdecimal = Parameters.ShowDecimal ? true : false;
                    TargetBaseColor = Parameters.TargetBaseColor ? Parameters.TargetBaseColor : 0;
                    TargetShow = Parameters.TargetShow ? true : false;
                    targetLabel = Parameters.TargetLabel ? true : false;
                    TargetAngle = Parameters.TargetAngle ? Parameters.TargetAngle : 0;
                    TargetSize = Parameters.TargetSize ? Parameters.TargetSize : 12;
                    TargetWidth = Parameters.TargetWidth ? Parameters.TargetWidth : 30;
                    TargetShowDecimal = Parameters.TargetShowDecimal ? true : false;
                    TargetHeight = Parameters.TargetHeight ? Parameters.TargetHeight : 1;
                    ChartTypeTwoBar = Parameters.TypeTwoBar;
                }



                html += '           <div class="form-row" id="EditParameter' + id + '" style="display:none;padding:10px;">';
                html += '               <div class="col-md-6 mb-3">';
                html += '                   <label class="form-label" for="Name"> نوع نمودار </label>';
                html += '                   <select id="ChartType' + id + '" class="form-control"  >';
                html += '                       <option value="line" ' + (type == "line" ? "selected" : "") + '>خطی</option>';
                html += '                       <option value="bar" ' + (type == "bar" ? "selected" : "") + '>ستونی</option>';
                html += '                       <option value="hbar" ' + (type == "hbar" ? "selected" : "") + '>افقی</option>';
                html += '                       <option value="area" ' + (type == "area" ? "selected" : "") + '>ناحیه ای</option>';
                html += '                       <option value="pie" ' + (type == "pie" ? "selected" : "") + '>دایره ای</option>';
                html += '                       <option value="rose" ' + (type == "rose" ? "selected" : "") + '>گلبرگ</option>';
                html += '                       <option value="polar" ' + (type == "polar" ? "selected" : "") + '>شعاعی</option>';
                html += '                       <option value="twobar" ' + (type == "twobar" ? "selected" : "") + '>دومحوره</option>';
                html += '                       <option value="treemap" ' + (type == "treemap" ? "selected" : "") + '>مستطیلی</option>';
                html += '                       <option value="gauge" ' + (type == "gauge" ? "selected" : "") + '>سرعت سنج</option>';
                html += '                       <option value="stack" ' + (type == "stack" ? "selected" : "") + '>پشته</option>';
                html += '                   </select>';
                html += '                   <input type="text" class="custom-control-input" id="ChartTypeTwoBar' + id + '"  value="' + ChartTypeTwoBar + '" >';
                html += '               </div>';
                html += '               <div class="col-md-6 mb-3">';
                html += '                   <label class="form-label" for="Name"> رنگ نمودار </label>';
                html += '                   <select id="ChartColor' + id + '" class="form-control"  >';
                for (var ii = 0; ii < colors.length; ii++) {
                    html += '                   <option value="' + ii + '" ' + (color == ii ? "selected" : "") + '>' + (colors[ii]) + '</option>';
                }
                html += '                   </select><input type="hidden" id="ChartColorTitle' + id + '" value="' + colorTitle + '"/>';
                html += '               </div>';
                html += '               <div class="col-md-12 mb-3">';
                html += '                   <label class="form-label" for="Grid' + id + '">فاصله از طرفین   </label>';
                html += '                   <input type="text" class="form-control" id="Grid' + id + '" name="Grid' + id + '" placeholder="top,right,bottom,left" value="' + grid + '"  >';
                html += '               </div>';
                html += '               <div class="col-md-6 mb-3">';
                html += '                   <label class="form-label" for="Angle' + id + '">زاویه   </label>';
                html += '                   <input type="text" class="form-control" id="Angle' + id + '" name="Angle' + id + '" placeholder="زاویه" value="' + angle + '"  >';
                html += '               </div>';
                html += '               <div class="col-md-6 mb-3">';
                html += '                   <label class="form-label" for="AngleX' + id + '">زاویه   </label>';
                html += '                   <input type="text" class="form-control" id="AngleX' + id + '" name="AngleX' + id + '" placeholder="زاویه" value="' + angleX + '"  >';
                html += '               </div>';
                html += '               <div class="col-md-6 mb-3  ">';
                html += '                   <div class="custom-control custom-checkbox">';
                html += '                       <input type="checkbox" class="custom-control-input" id="ShowHelp' + id + '" ' + (showhelp ? "checked" : "") + '  >';
                html += '                       <label class="custom-control-label" for="ShowHelp' + id + '">نمایش مقادیر</label>';
                html += '                   </div>';
                html += '               </div>';
                html += '               <div class="col-md-6 mb-3">';
                html += '                   <label class="form-label" for="Size' + id + '">اندازه   </label>';
                html += '                   <input type="text" class="form-control" id="Size' + id + '" name="Size' + id + '" placeholder="اندازه" value="' + sizelabel + '"  >';
                html += '               </div>';
                html += '               <div class="col-md-6 mb-3  ">';
                html += '                   <div class="custom-control custom-checkbox">';
                html += '                       <input type="checkbox" class="custom-control-input" id="ShowDecimal' + id + '" ' + (showdecimal ? "checked" : "") + '  >';
                html += '                       <label class="custom-control-label" for="ShowDecimal' + id + '">نمایش اعشار</label>';
                html += '                   </div>';
                html += '               </div>';
                html += '           </div>';
                html += '           <div id="ContentPanel' + id + '" class="form-row">';
                html += '               <div style="height:' + SSIZE + 'px; "></div>';
                html += '           </div><br/><br/><br/><br/>';
                html += '<select id="TargetColor' + id + '" class="form-control" >';
                for (var ii = 0; ii < colors.length; ii++) {
                    html += '<option value="' + ii + '" ' + (TargetColor == ii ? "selected" : "") + '>' + (colors[ii]) + '</option>';
                }
                html += '</select>';
                html += '<select id="TargetBaseColor' + id + '" class="form-control" >';
                for (var ii = 0; ii < colors.length; ii++) {
                    html += '<option value="' + ii + '" ' + (TargetBaseColor == ii ? "selected" : "") + '>' + (colors[ii]) + '</option>';
                }
                html += '</select>';
                html += '<input type="checkbox" class="custom-control-input" id="TargetShow' + id + '" ' + (TargetShow ? "checked" : "") + '  >';
                html += '<input type="checkbox" class="custom-control-input" id="TargetLabel' + id + '" ' + (targetLabel ? "checked" : "") + '  >';
                html += '<input type="checkbox" class="custom-control-input" id="Target' + id + '"   >';
                html += '<input type="text" class="custom-control-input" id="TargetAngle' + id + '"  value="' + TargetAngle + '"  >';
                html += '<input type="text" class="custom-control-input" id="TargetSize' + id + '"   value="' + TargetSize + '"  >';
                html += '<input type="text" class="custom-control-input" id="TargetWidth' + id + '"   value="' + TargetWidth + '"  >';
                html += '<input type="checkbox" class="custom-control-input" id="TargetShowDecimal' + id + '" ' + (TargetShowDecimal ? "checked" : "") + '  >';
                html += '<input type="text" class="custom-control-input" id="TargetHeight' + id + '"   value="' + TargetHeight + '"  >';


            }
            html += '           </div>';
            html += '       </div>';
            html += '   </div>';
           // html += '</div>';


            if ($("#DivPanel" + parentid).length == 0)
                $("#PanelsDiv").append('<div id = "DivPanel' + parentid + '" class="fdd col-lg-' + parentsize + ' lvl' + lvl + '"   ></div>');


            $("#DivPanel" + parentid).append(html).fadeIn(999);





        }

        function LoadPanelData(ID, isHtml, Type1, Type2, input, input1, input2, input3, input4, input5, input6, input7, Where, force) {

            if (isHtml) {
                ShowHtml(ID, Type1, input, input1, input2, input3, input4, input5, input6, input7, Where,force);
            }
            else if (Type2 == 2) {
                GetGridDataTable(ID, input, input1, input2, input3, input4, input5, input6, input7, Where);
            }
            else if (Type2 == 3) {
                GetGridSimple(ID, input, input1, input2, input3, input4, input5, input6, input7, Where);
                GetDataChart(ID, input, input1, input2, input3, input4, input5, input6, input7, undefined, Where);
            }
            else {
                GetDataChart(ID, input, input1, input2, input3, input4, input5, input6, input7, undefined, Where);
            }

        }

          function RemovePanel(lvl) {

            if (lvl > 1) {
                for (var i = 0; i < 10; i++)
                    $(".lvl" + (lvl + i)).remove();
            }
        }

        function SetParsmeter(id, input, input1, input2, input3, input4, input5, input6, input7) {

            $("#ChartTypeP").val($("#ChartType" + id).val());
            $("#ChartTypeTwoBarP").val($("#ChartTypeTwoBar" + id).val());

            $("#ChartColorP").val($("#ChartColor" + id).val());

            $("#cclr-tbl").html('');
            var clrTitle = $("#ChartColorTitle" + id).val().split(',');
            for (var i = 0; i < clrTitle.length; i++) {
                var clrTitleSplt = clrTitle[i].split(':');
                if (clrTitleSplt.length == 2)
                    addColor(clrTitleSplt[0], clrTitleSplt[1]);
            }

            $("#AngleP").val($("#Angle" + id).val());
            $("#AngleXP").val($("#AngleX" + id).val());
            $("#ShowHelpP").val($("#ShowHelp" + id).is(':checked') ? 1 : 0);

            $(".LableP").hide();
            if ($("#ShowHelp" + id).is(':checked'))
                $(".LableP").show();

            $("#ShowDecimalP").val($("#ShowDecimal" + id).is(':checked') ? 1 : 0);
            $("#SizeP").val($("#Size" + id).val());


            var grids = $("#Grid" + id).val().split(',');
            if (grids.length > 0)
                $("#GridP1").val(grids[0]);
            if (grids.length > 1)
                $("#GridP2").val(grids[1]);
            if (grids.length > 2)
                $("#GridP3").val(grids[2]);
            if (grids.length > 3)
                $("#GridP4").val(grids[3]);

            $("#IDP").val(id);
            $("#InputP").val(input);
            $("#Input1P").val(input1);
            $("#Input2P").val(input2);
            $("#Input3P").val(input3);
            $("#Input4P").val(input4);
            $("#Input5P").val(input5);
            $("#Input6P").val(input6);
            $("#Input7P").val(input7);

            $("#TargetColorP").val($("#TargetColor" + id).val());
            $("#TargetBaseColorP").val($("#TargetBaseColor" + id).val());
            $("#TargetShowP").val($("#TargetShow" + id).is(':checked') ? 1 : 0);
            $("#TargetLabelP").val($("#TargetLabel" + id).is(':checked') ? 1 : 0);
            $("#TargetAngleP").val($("#TargetAngle" + id).val());
            $("#TargetSizeP").val($("#TargetSize" + id).val());
            $("#TargetWidthP").val($("#TargetWidth" + id).val());
            $("#TargetShowDecimalP").val($("#TargetShowDecimal" + id).is(':checked') ? 1 : 0);
            $("#TargetHeightP").val($("#TargetHeight" + id).val());


            $(".trgt").hide();
            if ($("#Target" + id).is(':checked'))
                $(".trgt").show();


            $('.twobarType').hide();
            if ($("#ChartTypeP").val() == 'twobar')
                $(".twobarType").show();


            $("#ParameterModal").modal('show');
        }
        function SaveParameter() {
            var id = $("#IDP").val();
            var input = $("#InputP").val();
            var input1 = $("#Input1P").val();
            var input2 = $("#Input2P").val();
            var input3 = $("#Input3P").val();
            var input4 = $("#Input4P").val();
            var input5 = $("#Input5P").val();
            var input6 = $("#Input6P").val();
            var input7 = $("#Input7P").val();

            $("#ChartType" + id).val($("#ChartTypeP").val());
            $("#ChartTypeTwoBar" + id).val($("#ChartTypeTwoBarP").val());


            $("#ChartColor" + id).val($("#ChartColorP").val());

            var ChartColorTitle = [];
            var cclrVal = $("#cclr-tbl .cclr-val");
            var cclrClr = $("#cclr-tbl .cclr-clr");
            for (var i = 0; i < cclrVal.length; i++)
                ChartColorTitle.push($(cclrVal[i]).html() + ":" + $(cclrClr[i]).html() );

            $("#ChartColorTitle" + id).val(ChartColorTitle.toString());

            $("#Angle" + id).val($("#AngleP").val());
            $("#AngleX" + id).val($("#AngleXP").val());
            $("#ShowHelp" + id).prop('checked', $("#ShowHelpP").val() == '1');
            $("#Size" + id).val($("#SizeP").val());
            $("#ShowDecimal" + id).prop('checked', $("#ShowDecimalP").val() == '1');


            $("#TargetColor" + id).val($("#TargetColorP").val());
            $("#TargetBaseColor" + id).val($("#TargetBaseColorP").val());
            $("#TargetShow" + id).prop('checked', $("#TargetShowP").val() == '1');
            $("#TargetLabel" + id).prop('checked', $("#TargetLabelP").val() == '1');
            $("#TargetAngle" + id).val($("#TargetAngleP").val());
            $("#TargetSize" + id).val($("#TargetSizeP").val());
            $("#TargetWidth" + id).val($("#TargetWidthP").val());
            $("#TargetShowDecimal" + id).prop('checked', $("#TargetShowDecimalP").val() == '1');
            $("#TargetHeight" + id).val($("#TargetHeightP").val());


            var grids = "";
            if ($("#GridP1").val() != "") {
                var grids = [];
                grids.push($("#GridP1").val());
                grids.push($("#GridP2").val());
                grids.push($("#GridP3").val());
                grids.push($("#GridP4").val());

                $("#Grid" + id).val(grids.toString());
            }
            else {
                $("#Grid" + id).val("");
            }



            GetDataChart(id, input, input1, input2, input3, input4, input5, input6, input7, true);
            $("#ParameterModal").modal('hide');
        }
        function ResetParameter() {
              bootbox.confirm({
                title: "<i class='fal fa-exclamation-triangle text-warning mr-2'></i> اخطار",
                message: "<span>آیا شما مطمئن به ریست این پنل می باشید</span>",
                centerVertical: true,
                swapButtonOrder: true,
                buttons: {
                    confirm: {
                        label: 'بلی',
                        className: 'btn-warning shadow-0'
                    },
                    cancel: {
                        label: 'خیر',
                        className: 'btn-success'
                    }
                },
                className: "modal-alert",
                closeButton: false,
                callback: function (result) {
                    if (result == true) {
                        var id = $("#IDP").val();

                        $.ajax({
                            type: "Get",
                            url: "/Dashboard/ResetPanel?DashboardID=@DashboardID&PanelID=" + id,
                            contentType: "application/json; charset=utf-8",
                            dataType: "json"
                        }).done(function (result) {


                            if (result.ok) {
                                window.location.reload();
                            }
                            else {

                                toastr.error(result.data, "خطا");

                            }

                        });
                    }
                }
            });

        }

        function ResetDashboard() {
            bootbox.confirm({
                title: "<i class='fal fa-exclamation-triangle text-warning mr-2'></i> اخطار",
                message: "<span>آیا شما مطمئن به ریست این داشبورد می باشید</span>",
                centerVertical: true,
                swapButtonOrder: true,
                buttons: {
                    confirm: {
                        label: 'بلی',
                        className: 'btn-warning shadow-0'
                    },
                    cancel: {
                        label: 'خیر',
                        className: 'btn-success'
                    }
                },
                className: "modal-alert",
                closeButton: false,
                callback: function (result) {
                    if (result == true) {
                        $.ajax({
                            type: "Get",
                            url: "/Dashboard/ResetPanel?DashboardID=@DashboardID&PanelID=" + 0,
                            contentType: "application/json; charset=utf-8",
                            dataType: "json"
                        }).done(function (result) {


                            if (result.ok) {
                                window.location.reload();
                            }
                            else {

                                toastr.error(result.data, "خطا");

                            }

                        });
                    }
                }
            });

        }

        function resizeAllCharts() {
            for (var i = 0; i < initCharts.length; i++)
                initCharts[i].resize();
        }

        $(window).on('resize', function () {
            resizeAllCharts();
        });

        function fullscrn(id) {

            var p = $('#ContentPanel' + id).closest('.panel');
            var ok = p.hasClass("panel-fullscreen");
            var divs = p.find('.cc');
            var h = 0;
            for (var i = 0; i < divs.length; i++) {
                var div = $(p.find('.cc')[i]);
                h = parseInt(div.attr("data-H"));
                var h2 = parseInt(h / 2);
                div.css("height", (ok ? (divs.length > 2 ? "45vh" : "90vh") : (divs.length > 2 ? h2 : h) + "px"));
            }

            resizeAllCharts();
            $("#if" + id).removeClass('ni-size-fullscreen')
            $("#if" + id).removeClass('ni-size-actual');
            $("#if" + id).addClass(ok ? 'ni-size-actual' : 'ni-size-fullscreen');

            if (h > 0) {
                $('#ContentPanel' + id).closest('.panel-content').css("height", (ok ? "90vh" : (parseInt(h) - 2) + "px"));
                $('#ContentPanel' + id).closest('.slimScrollDiv').css("height", (ok ? "90vh" : (parseInt(h) - 2) + "px"));
            }

        }
        function minscrn(id) {

            var p = $('#ContentPanel' + id).closest('.panel');
            var ok = p.hasClass("panel-collapsed");

            $("#im" + id).removeClass('fa-minus')
            $("#im" + id).removeClass('fa-plus');
            $("#im" + id).addClass(ok ? 'fa-plus' : 'fa-minus');
            var btn = $("#if" + id).closest('button');
            if (ok)
                btn.hide();
            else
                btn.show();

            resizeAllCharts();

        }



        function ChangeType1() {
            var valtype1 = $("#Type1").val();
            $("#HTMLLbl").html("HTML");
            $('.colType1').show();
            if (valtype1 == '3') {
                $('.colType1').hide();
                $("#HTMLLbl").html("متن سرویس نقشه");
            }


        }

        function SetDefaultBtn(id) {
            $('#btn' + id).dropdown('hide');
            $('#btn' + id).attr('aria-expanded', 'false');
        }



        function DeletePanel(id) {
            bootbox.confirm({
                title: "<i class='fal fa-exclamation-triangle text-warning mr-2'></i> اخطار",
                message: "<span>آیا شما مطمئن به حذف این آیتم می باشید</span>",
                centerVertical: true,
                swapButtonOrder: true,
                buttons: {
                    confirm: {
                        label: 'بلی',
                        className: 'btn-warning shadow-0'
                    },
                    cancel: {
                        label: 'خیر',
                        className: 'btn-success'
                    }
                },
                className: "modal-alert",
                closeButton: false,
                callback: function (result) {
                    if (result == true) {

                        $.ajax({
                            type: "Get",
                            url: "/Dashboard/DeletePanel?id=" + id,
                            contentType: "application/json; charset=utf-8",
                            dataType: "json"
                        }).done(function (result) {


                            if (result.ok) {
                                toastr.success(result.data, "موفق");
                                window.location.reload();
                            }
                            else {

                                toastr.error(result.data, "خطا");

                            }

                        });
                    }
                }
            });


        }


        var mybutton = document.getElementById("topBtn");

        // When the user scrolls down 20px from the top of the document, show the button
        window.onscroll = function () { scrollFunction() };

        function scrollFunction() {
            if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                mybutton.style.display = "block";
            } else {
                mybutton.style.display = "none";
            }
        }

        // When the user clicks on the button, scroll to the top of the document
        function topFunction() {
            document.body.scrollTop = 0;
            document.documentElement.scrollTop = 0;
        }


        function printCanvas() {
            $("#loadingImg").show();
            /*html2canvas($("#PanelsDiv")[0]).then((canvas) => {
                $("#loadingImg").hide();
                //$("#out_image").append(canvas);
                canvas.toBlob(function (blob) {
                    saveAs(blob, "Dashboard.png");
                });
            });*/

            html2canvas(document.getElementById("PanelsDiv"), {
                onrendered: function (canvas) {
                    $("#loadingImg").hide();
                    var imgData = canvas.toDataURL(
                        'image/png');

                    var doc = new jspdf.jsPDF('p', 'mm');
                    /*var width = doc.internal.pageSize.getWidth();
                    var height = doc.internal.pageSize.getHeight();*/

                    var imgProps = doc.getImageProperties(imgData);
                    var width = doc.internal.pageSize.getWidth();
                    var height = (imgProps.height * width) / imgProps.width;

                    doc.addImage(imgData, 'PNG' , 0, 0, width, height);//, $("#PanelsDiv").width(), $("#PanelsDiv").height());
                    doc.save('Dashboard.pdf');
                    /*canvas.toBlob(function (blob) {
                        saveAs(blob, "Dashboard.png");
                    });*/
                }
            });
        }


        var ActiveFilter = {};
        function Filter() {


            var names = [];
            var vals = [];
            var filters = $(".filter");
            for (var i = 0; i < filters.length; i++) {
                var val = $(filters[i]).val();

                if (val && val != '') {
                    var name = $(filters[i]).attr("name");
                    names.push(name.toLocaleLowerCase());
                    vals.push(val);
                }
            }
            if (names.length > 0) {
                ActiveFilter.Names = names;
                ActiveFilter.Vals = vals;
                ApplyFilter();
            }
            else { ClearFilter(); }
            $("#FilterModal").modal('hide');

        }

        function ApplyFilter() {

            var names = ActiveFilter.Names;
            var vals = ActiveFilter.Vals;
            if (names && vals) {
                var panels = $(".panel");
                for (var i = 0; i < panels.length; i++) {
                    var p = $(panels[i]);
                    var f = p.attr("data-filter");
                    if (f) {
                        var where = "";
                        var splt = f.split(',');

                        for (var j = 0; j < splt.length; j++) {
                            var item = splt[j].split(':')[0];
                            var indx = names.indexOf(item.toLocaleLowerCase());
                            if (indx >= 0 && vals[indx] != '') {
                                    where += " AND " + item + " in (N'" + vals[indx].toString().replaceAll(",", "',N'") + "')";
                            }

                            indx = names.indexOf("min__" + item.toLocaleLowerCase());
                            if (indx >= 0 && vals[indx] != '')
                                where += " AND " + item.replace('min__', '') + " >= '" + vals[indx].toString() + "'";

                            indx = names.indexOf("max__" + item.toLocaleLowerCase());
                            if (indx >= 0 && vals[indx] != '')
                                where += " AND " + item.replace('max__', '') + " <= '" + vals[indx].toString() + "'";
                        }
                        if (where) {
                            var id = p.attr("id").replace("pnl", "");

                            LoadPanelData(id, p.attr("data-isHtml") == 'true' ? true : false, p.attr("data-type1"), p.attr("data-type2"), p.attr("data-input"), p.attr("data-input1"), p.attr("data-input2"), p.attr("data-input3"), p.attr("data-input4"), p.attr("data-input5"), p.attr("data-input6"), p.attr("data-input7"), where);

                        }

                    }
                }
            }
        }

        function ClearFilter() {

            var names = ActiveFilter.Names;
            var vals = ActiveFilter.Vals;
            if (names && vals) {
                var panels = $(".panel");
                for (var i = 0; i < panels.length; i++) {
                    var p = $(panels[i]);
                    var f = p.attr("data-filter");
                    if (f) {
                        var where = "";
                        var splt = f.split(',');
                        for (var j = 0; j < splt.length; j++) {
                            var item = splt[j].split(':')[0];
                            var indx = names.indexOf(item.toLocaleLowerCase())
                            if (indx >= 0 && vals[indx] != '') {
                                where += " AND " + item + " in (N'" + vals[indx].toString().replaceAll(",", "',N'") + "')";
                            }

                            indx = names.indexOf("min__" + item.toLocaleLowerCase());
                            if (indx >= 0 && vals[indx] != '')
                                where += " AND " + item.replace('min__', '') + " >= '" + vals[indx].toString() + "'";

                            indx = names.indexOf("max__" + item.toLocaleLowerCase());
                            if (indx >= 0 && vals[indx] != '')
                                where += " AND " + item.replace('max__', '') + " <= '" + vals[indx].toString() + "'";
                        }
                        if (where) {
                            var id = p.attr("id").replace("pnl", "");
                            LoadPanelData(id, p.attr("data-isHtml") == 'true' ? true : false, p.attr("data-type1"), p.attr("data-type2"), p.attr("data-input"), p.attr("data-input1"), p.attr("data-input2"), p.attr("data-input3"), p.attr("data-input4"), p.attr("data-input5"), p.attr("data-input6"), p.attr("data-input7"),'',true);

                        }

                    }
                }
            }

            ActiveFilter.Names = [];
            ActiveFilter.Vals = [];
            $(".filter").val('').trigger('change');

        }

        function toggleFilter() {
            $('#DivPanelFilter').slideToggle();
            $("#iFilter").toggleClass("fa-times fa-filter");

            //doAnimation();
        }

        function doAnimation() {

            var views = [];
            for (var i = 0; i < $(".colFilter").length; i++)
                if (!$($(".colFilter")[i]).is(":hidden"))
                    views.push(i);


            $(".colFilter").removeClass('floating')
            $(".colFilter").hide();

            j = 0;
            for (var i = 0; i < views.length; i++) {

                var oo = $($(".colFilter")[views[i]]);
                _doAnimation(oo, j);
                j++;
            }

        }
        function _doAnimation(oo,j) {
            setTimeout(function () {

                oo.addClass('floating');
                oo.fadeIn();
            }, 0 + (500 * j));
        }




        function GetWhere(panelid) {
            var where="";


            var names = ActiveFilter.Names;
            var vals = ActiveFilter.Vals;
            if (names && vals) {
                var panels = $(".panel");
                for (var i = 0; i < panels.length; i++) {
                    var p = $(panels[i]);
                    var f = p.attr("data-filter");
                    if (f) {
                        var id = p.attr("id").replace("pnl", "");
                        if (id == panelid) {

                            var splt = f.split(',');
                            for (var j = 0; j < splt.length; j++) {
                                var item = splt[j].split(':')[0];
                                var indx = names.indexOf(item.toLocaleLowerCase())
                                if (indx >= 0 && vals[indx] != '') {
                                    where += " AND " + item + " in (N'" + vals[indx].toString().replaceAll(",", "',N'") + "')";
                                }

                                indx = names.indexOf("min__" + item.toLocaleLowerCase());
                                if (indx >= 0 && vals[indx] != '')
                                    where += " AND " + item.replace('min__', '') + " >= '" + vals[indx].toString() + "'";

                                indx = names.indexOf("max__" + item.toLocaleLowerCase());
                                if (indx >= 0 && vals[indx] != '')
                                    where += " AND " + item.replace('max__', '') + " <= '" + vals[indx].toString() + "'";
                            }
                            break;
                        }


                    }
                }
            }
            return where;
        }

        $(window).scroll(function (e) {
            var $el = $('#DivPanelFilter');
            var isPositionFixed = ($el.css('position') == 'fixed');
            if ($(this).scrollTop() > 50 && !isPositionFixed) {
                $el.css({ 'position': 'fixed', 'top': '52px','left':'0','padding':'0' });

            }
            if ($(this).scrollTop() < 50 && isPositionFixed) {
                $el.css({ 'position': 'static', 'top': ' ', 'left': '', 'padding': '0 5px' });

            }

        });


        var allShow = false;
        function ShowHideFilter() {
            $("#moreFilter").hide();
            $("#moreFilter").html("بیشتر");
            var allShow = false;
            var datafilters = [];
            var panels = $(".panel");
            for (var i = 0; i < panels.length; i++) {
                var p = $(panels[i]);
                var f = p.attr("data-filter");
                if (f) {

                    var splt = f.split(',');
                    for (var j = 0; j < splt.length; j++) {
                        var item = splt[j].split(':')[0].toLocaleLowerCase();
                        if (item != '' && datafilters.indexOf(item)<0) {
                            datafilters.push(item);
                        }

                    }
                }
            }

            var ShowFilter = false;
            var filters = $(".colFilter");
            var cnttt = 0;
            for (var i = 0; i < filters.length; i++) {

                if (datafilters.indexOf($(filters[i]).attr("id").replace("col", "").toLocaleLowerCase()) >= 0) {
                    if (cnttt <= 7)
                        $(filters[i]).show();
                    else {
                        $(filters[i]).hide();
                        $("#moreFilter").show();
                        $("#moreFilter").html("بیشتر");
                    }

                    ShowFilter = true;
                    $(filters[i]).addClass("colFilterShow");
                    $(filters[i]).removeClass("colFilterHide");

                    cnttt++;
                }
                else {
                    $(filters[i]).hide();

                    $(filters[i]).addClass("colFilterHide");
                    $(filters[i]).removeClass("colFilterShow");
                }


            }

            if (ShowFilter)
                $("#rowFilter").show();
            else
                $("#rowFilter").hide();


        }

        function moreFilter() {

            $("#moreFilter").html("کمتر");
            if (allShow) {
                var filters = $('.colFilterShow');
                for (var i = 0; i < filters.length; i++) {
                    if (i <= 7)
                        $(filters[i]).show();
                    else
                        $(filters[i]).hide();
                }
                $("#moreFilter").html("بیشتر");

            }
            else
                $('.colFilterShow').show();

                allShow = !allShow;

        }



        window.addEventListener('message', e => {
            //console.log(e);
            var pid = actviePanelID;
            var mp = $("#pnl" + actviePanelID).attr("data-InputMapping");
            mp = mp == "null" ? "" : mp;

            if (mp && mp != '') {

                var id = "";
                var title = "";
                var value = "";
                var value1 = "";
                var value2 = "";
                var value3 = "";
                var value4 = "";
                var value5 = "";

                var mps = mp.split(',');

                var f = e.source.fieldsIdentify;
                var table = f.LayerName;
                var fields = f.Fields;
                for (var i = 0; i < fields.length; i++) {
                    for (var j = 0; j < mps.length; j++) {
                        var item = mps[j].split(':');
                        if (item.length > 1 && item[1].toLocaleLowerCase() == (table + "." + fields[i].Name).toLocaleLowerCase()) {
                            if (item[0].toLocaleLowerCase() == "id") id = fields[i].Value;
                            else if (item[0].toLocaleLowerCase() == "title") title = fields[i].Value;
                            else if (item[0].toLocaleLowerCase() == "value") value = fields[i].Value;
                            else if (item[0].toLocaleLowerCase() == "value1") value1 = fields[i].Value;
                            else if (item[0].toLocaleLowerCase() == "value2") value2 = fields[i].Value;
                            else if (item[0].toLocaleLowerCase() == "value3") value3 = fields[i].Value;
                            else if (item[0].toLocaleLowerCase() == "value4") value4 = fields[i].Value;
                            else if (item[0].toLocaleLowerCase() == "value5") value5 = fields[i].Value;
                        }
                    }
                }



                if (id != '' || title != '' || value != '' || value1 != '' || value2 != '' || value3 != '' || value4 != '' || value5 != '')
                    LoadPanel(pid, id, title, value, value1, value2, value3, value4, value5);
                else
                    toastr.error("هیچ پنلی بدلیل عدم تطابق Mapping یافت نشد", "خطا");
            }




        }, false);


        // nested list filter
        initApp.listFilter($('#js_nested_list'), $('#js_nested_list_filter'));
        //init navigation
        initApp.buildNavigation($('#js_nested_list'));

        function addColor(val,clr) {
            if (!val) val = $('#cclr-val').val();
            if (!clr) clr = $('#cclr-clr').val();

            $('#cclr-tbl').append('<tr  style="background-color:' + clr + '"><td class="cclr-val">' + val + '</td><td class="cclr-clr">' + clr + '</td><td><a href="javascript:void(0);" class="btn btn-xs btn-danger waves-effect waves-themed" onclick="$(this).closest(\'tr\').remove();"><i class="fal fa-trash"></i></a></td></tr>');

        }
        function getColor(colorTitle, title) {
            var colorTitles = colorTitle.split(',');
            for (var i = 0; i < colorTitles.length; i++) {
                var tt = colorTitles[i].split(':');
                if (tt.length == 2 && title.includes(tt[0]))
                    return tt[1];
            }
            return undefined;
        }
    </script>
}
