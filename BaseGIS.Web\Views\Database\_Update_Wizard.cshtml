﻿@using BaseGIS.Core.Entities
@* @using NPOI.SS.UserModel *@

@{
    var id = Context.Request.Query["id"];
    var pathFile = Context.Request.Query["path"];
    var fieldInfos = ViewBag.FieldInfos as List<FieldInfo>;
    var shapeFileColumns = ViewBag.ShapeFileColumns as List<string>;
    var sheetXLS = ViewBag.SheetXLS;// as ISheet;
    int counter = 1;
}

@Html.AntiForgeryToken()
<div>
    <div class="card border-danger mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="card-title mb-0">اعتبارسنجی @(string.IsNullOrEmpty(id) ? "لایه" : $"لایه {id}")</h5>
        </div>
        <div class="card-body">
            <div class="wizard-container">
                <!-- Nav tabs for wizard steps -->
                <ul class="nav nav-pills nav-justified mb-4" id="wizardSteps" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link @(string.IsNullOrEmpty(pathFile) ? "active" : "")" id="step1-tab" data-bs-toggle="pill" data-bs-target="#step1" type="button" role="tab" aria-controls="step1" aria-selected="@(string.IsNullOrEmpty(pathFile))">
                            <span class="badge rounded-pill bg-primary">1</span> آپلود فایل
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link @(string.IsNullOrEmpty(pathFile) ? "" : "active")" id="step2-tab" data-bs-toggle="pill" data-bs-target="#step2" type="button" role="tab" aria-controls="step2" aria-selected="@(!string.IsNullOrEmpty(pathFile))">
                            <span class="badge rounded-pill bg-primary">2</span> شرط
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="step3-tab" data-bs-toggle="pill" data-bs-target="#step3" type="button" role="tab" aria-controls="step3" aria-selected="false">
                            <span class="badge rounded-pill bg-primary">3</span> تناظر فیلدها
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="step4-tab" data-bs-toggle="pill" data-bs-target="#step4" type="button" role="tab" aria-controls="step4" aria-selected="false">
                            <span class="badge rounded-pill bg-primary">4</span> پایان
                        </button>
                    </li>
                </ul>

                <!-- Tab content -->
                <div class="tab-content" id="wizardContent">
                    <!-- Step 1: File Upload -->
                    <div class="tab-pane fade @(string.IsNullOrEmpty(pathFile) ? "show active" : "")" id="step1" role="tabpanel" aria-labelledby="step1-tab">
                        <h4 class="mb-4"><strong>آپلود فایل</strong></h4>
                        <form dir="ltr" action="#" class="dropzone" id="mydropzone"></form>
                    </div>

                    <!-- Step 2: Analysis -->
                    <div class="tab-pane fade @(string.IsNullOrEmpty(pathFile) ? "" : "show active")" id="step2" role="tabpanel" aria-labelledby="step2-tab">
                        <h4 class="mb-4"><strong>تحلیل فایل</strong></h4>
                        <input type="hidden" name="filePath" id="filePath" value="@pathFile" />
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th><i class="fa fa-building"></i> فایل اصلی</th>
                                        <th><i class="fa fa-calendar"></i> معادل در لایه</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (fieldInfos != null)
                                    {
                                        foreach (var item in fieldInfos)
                                        {
                                            if (!string.Equals(item.Name, "gcode", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "area", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "length", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "objectid", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "user", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "time", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "usergroup", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "org", StringComparison.OrdinalIgnoreCase))
                                            {
                                                <tr>
                                                    <td>@(counter++)</td>
                                                    <td>@item.AliasName</td>
                                                    <td>
                                                        <select class="form-select select2" id="<EMAIL>" name="<EMAIL>">
                                                            <option value=""></option>
                                                            @if (shapeFileColumns != null)
                                                            {
                                                                foreach (var col in shapeFileColumns)
                                                                {
                                                                    if (string.Equals(item.Name, col, StringComparison.OrdinalIgnoreCase))
                                                                    {
                                                                        <option value="@col" selected>@col</option>
                                                                    }
                                                                    else
                                                                    {
                                                                        <option value="@col">@col</option>
                                                                    }
                                                                }
                                                            }
                                                            else if (sheetXLS != null && sheetXLS.GetRow(0) != null)
                                                            {
                                                                foreach (var cell in sheetXLS.GetRow(0).Cells)
                                                                {
                                                                    var value = cell.StringCellValue;
                                                                    if (string.Equals(item.Name, value, StringComparison.OrdinalIgnoreCase))
                                                                    {
                                                                        <option value="@value" selected>@value</option>
                                                                    }
                                                                    else
                                                                    {
                                                                        <option value="@value">@value</option>
                                                                    }
                                                                }
                                                            }
                                                        </select>
                                                    </td>
                                                </tr>
                                            }
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Step 3: Field Mapping -->
                    <div class="tab-pane fade" id="step3" role="tabpanel" aria-labelledby="step3-tab">
                        <h4 class="mb-4"><strong>تناظر فیلدها</strong></h4>
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th><i class="fa fa-building"></i> فایل اصلی</th>
                                        <th><i class="fa fa-calendar"></i> معادل در لایه</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @{ counter = 1; }
                                    @if (fieldInfos != null)
                                    {
                                        foreach (var item in fieldInfos)
                                        {
                                            if (!string.Equals(item.Name, "gcode", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "area", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "length", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "objectid", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "user", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "time", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "usergroup", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "org", StringComparison.OrdinalIgnoreCase))
                                            {
                                                <tr>
                                                    <td>@(counter++)</td>
                                                    <td>@item.AliasName</td>
                                                    <td>
                                                        <select class="form-select select2" id="@item.Name" name="<EMAIL>">
                                                            <option value=""></option>
                                                            @if (shapeFileColumns != null)
                                                            {
                                                                foreach (var col in shapeFileColumns)
                                                                {
                                                                    if (string.Equals(item.Name, col, StringComparison.OrdinalIgnoreCase))
                                                                    {
                                                                        <option value="@col" selected>@col</option>
                                                                    }
                                                                    else
                                                                    {
                                                                        <option value="@col">@col</option>
                                                                    }
                                                                }
                                                            }
                                                            else if (sheetXLS != null && sheetXLS.GetRow(0) != null)
                                                            {
                                                                foreach (var cell in sheetXLS.GetRow(0).Cells)
                                                                {
                                                                    var value = cell.StringCellValue;
                                                                    if (string.Equals(item.Name, value, StringComparison.OrdinalIgnoreCase))
                                                                    {
                                                                        <option value="@value" selected>@value</option>
                                                                    }
                                                                    else
                                                                    {
                                                                        <option value="@value">@value</option>
                                                                    }
                                                                }
                                                            }
                                                        </select>
                                                    </td>
                                                </tr>
                                            }
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Step 4: Finish -->
                    <div class="tab-pane fade" id="step4" role="tabpanel" aria-labelledby="step4-tab">
                        <h4 class="text-center mb-4">برای اتمام دکمه ذخیره را فشار دهید...</h4>
                        <div id="finished" class="my-5"></div>
                    </div>
                </div>

                <!-- Wizard Navigation Buttons -->
                <div class="d-flex justify-content-between mt-4">
                    <button type="button" class="btn btn-primary btn-prev" id="prevBtn">
                        <i class="fa fa-arrow-right me-2"></i>قبلی
                    </button>
                    <button type="button" class="btn btn-success btn-next" id="nextBtn">
                        بعدی<i class="fa fa-arrow-left ms-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function () {
        // Initialize Select2 for dropdowns
        $('.select2').select2({
            width: '100%',
            dir: 'rtl',
            placeholder: 'انتخاب کنید',
            allowClear: true,
            language: {
                noResults: function () {
                    return "یافت نشد";
                }
            }
        });

        // Initialize Dropzone
        $("#mydropzone").dropzone({
            url: "/Database/_FileUpload?id=@id",
            addRemoveLinks: true,
            maxFiles: 1,
            maxFilesize: 100,
            acceptedFiles: "application/zip,.zip",
            dictDefaultMessage: '<div class="text-center"><div class="mb-3"><i class="fa fa-cloud-upload fa-3x text-muted"></i></div><h5>فایل فشرده جهت آپلود را در اینجا بیندازید</h5><p class="text-muted">(یا کلیک کنید)</p></div>',
            dictResponseError: 'خطا در آپلود فایل!',
            init: function () {
                this.on("success", function (file, data) {
                    if (data.success && data.responseText) {
                        $("#filePath").val(data.responseText);
                        window.location.href = '/Database/Update?id=@id&path=' + data.responseText;
                    } else {
                        $.notify(data.responseText, { className: 'error', position: 'top center' });
                    }
                });
                this.on("sending", function (file, xhr, formData) {
                    formData.append("__RequestVerificationToken", $("input[name='__RequestVerificationToken']").val());
                });
                this.on("error", function (file, errorMessage) {
                    $.notify(errorMessage, { className: 'error', position: 'top center' });
                });
            }
        });

        // Initialize Bootstrap 5 wizard
        initWizard();
    });

    function initWizard() {
        let currentStep = @(!string.IsNullOrEmpty(pathFile) ? 1 : 0);
        const totalSteps = 4;
        const wizardTabs = document.querySelectorAll('#wizardSteps .nav-link');
        const wizardContent = document.querySelectorAll('.tab-pane');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        function updateButtons() {
            prevBtn.style.display = currentStep === 0 ? 'none' : 'block';
            if (currentStep === totalSteps - 1) {
                nextBtn.innerHTML = 'ذخیره<i class="fa fa-save ms-2"></i>';
                nextBtn.classList.remove('btn-success');
                nextBtn.classList.add('btn-primary');
            } else {
                nextBtn.innerHTML = 'بعدی<i class="fa fa-arrow-left ms-2"></i>';
                nextBtn.classList.remove('btn-primary');
                nextBtn.classList.add('btn-success');
            }
        }

        function goToStep(stepIndex) {
            wizardContent.forEach(pane => pane.classList.remove('show', 'active'));
            wizardTabs.forEach(tab => {
                tab.classList.remove('active');
                tab.setAttribute('aria-selected', 'false');
            });

            wizardContent[stepIndex].classList.add('show', 'active');
            wizardTabs[stepIndex].classList.add('active');
            wizardTabs[stepIndex].setAttribute('aria-selected', 'true');

            currentStep = stepIndex;
            updateButtons();
        }

        prevBtn.addEventListener('click', function () {
            if (currentStep > 0) {
                goToStep(currentStep - 1);
            }
        });

        nextBtn.addEventListener('click', function () {
            if (currentStep < totalSteps - 1) {
                goToStep(currentStep + 1);
            } else {
                finishWizard();
            }
        });

        wizardTabs.forEach((tab, index) => {
            tab.addEventListener('click', function (e) {
                e.preventDefault();
                goToStep(index);
            });
        });

        updateButtons();

        function finishWizard() {
            @if (!string.IsNullOrEmpty(id) && !string.IsNullOrEmpty(pathFile))
            {
                <text>
                let fieldSource = '';
                let fieldDest = '';
                let fieldCheck = '';
                </text>

                foreach (var item in fieldInfos)
                {
                    if (!string.Equals(item.Name, "gcode", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "area", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "length", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "objectid", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "user", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "time", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "usergroup", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "org", StringComparison.OrdinalIgnoreCase))
                    {
                        <text>
                        const <EMAIL> = $('#@item.Name').val();
                        const <EMAIL> = $('#<EMAIL>').val();
                        if (<EMAIL>) {
                            fieldDest += <EMAIL> + ',';
                            fieldSource += '@item.Name' + ',';
                        }
                        if (<EMAIL>) {
                            fieldCheck += <EMAIL> + ',';
                        }
                        </text>
                    }
                }

                <text>
                const obj = {
                    id: '@id',
                    path: '@pathFile',
                    FieldSource: fieldSource,
                    FieldDest: fieldDest,
                    FieldCheck: fieldCheck,
                    __RequestVerificationToken: $("input[name='__RequestVerificationToken']").val()
                };

                $("#finished").html('<div class="text-center"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">در حال پردازش...</p></div>');

                $.ajax({
                    url: '/Database/PublishData',
                    method: 'POST',
                    data: obj,
                    success: function (data) {
                        if (data.success) {
                            $("#finished").html('<div class="alert alert-success" role="alert"><i class="fa fa-check me-2"></i> تبریک! <br><small>' + data.responseText + '</small></div>');
                            $.notify(data.responseText, { className: 'success', position: 'top center' });
                            setTimeout(() => {
                                window.location.href = '/Database/Update?id=@id';
                            }, 2000);
                        } else {
                            $("#finished").html('<div class="alert alert-danger" role="alert"><i class="fa fa-exclamation-circle me-2"></i> خطا! <br><small>' + data.responseText + '</small></div>');
                            $.notify(data.responseText, { className: 'error', position: 'top center' });
                        }
                    },
                    error: function (xhr, status, error) {
                        const errorMessage = xhr.responseJSON?.responseText || 'خطا در ارتباط با سرور';
                        $("#finished").html('<div class="alert alert-danger" role="alert"><i class="fa fa-exclamation-circle me-2"></i> خطا! <br><small>' + errorMessage + '</small></div>');
                        $.notify(errorMessage, { className: 'error', position: 'top center' });
                    }
                });
                </text>
            }
        }
    }
</script>