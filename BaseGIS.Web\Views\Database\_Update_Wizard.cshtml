@using BaseGIS.Core.Entities
@model BaseGIS.Web.Models.UpdateWizardViewModel

@{
    var id = Model?.Id ?? Context.Request.Query["id"];
    var pathFile = Model?.PathFile ?? Context.Request.Query["path"];
    var fieldInfos = Model?.FieldInfos ?? ViewBag.FieldInfos as List<FieldInfo>;
    var shapeFileColumns = Model?.ShapeFileColumns ?? ViewBag.ShapeFileColumns as List<string>;
    var excelColumns = Model?.ExcelColumns ?? ViewBag.ExcelColumns as List<string>;
    int counter = 1;
}

@Html.AntiForgeryToken()
<div class="card border-danger mb-3 samanFont" id="wid-id-43">
    <div class="card-header bg-danger text-white">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <h5 class="card-title samanFont mb-0">
                    <i class="fa fa-edit me-2"></i>
                    بروزرسانی لایه @(string.IsNullOrEmpty(id) ? "" : $"#{id}")
                </h5>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <!-- SmartWizard -->
        <div id="wizard" class="sw-main sw-theme-arrows">
            <ul class="nav nav-progress">
                <li class="nav-item">
                    <a class="nav-link" href="#step-1">
                        <div class="num">1</div>
                        آپلود فایل
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#step-2">
                        <div class="num">2</div>
                        تناظر فیلدها
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#step-3">
                        <div class="num">3</div>
                        پایان
                    </a>
                </li>
            </ul>

            <div class="tab-content">
                <!-- Step 1: File Upload -->
                <div id="step-1" class="tab-pane" role="tabpanel">
                    <div class="p-4">
                        <h4 class="mb-4">
                            <i class="fa fa-cloud-upload text-primary me-2"></i>
                            آپلود فایل
                        </h4>
                        <div class="alert alert-info" role="alert">
                            <i class="fa fa-info-circle me-2"></i>
                            <strong>فرمت‌های پشتیبانی شده:</strong>
                            <ul class="mb-0 mt-2">
                                <li><strong>Shapefile:</strong> فایل ZIP حاوی .shp, .dbf, .shx</li>
                                <li><strong>Excel:</strong> فایل‌های .xls و .xlsx</li>
                            </ul>
                        </div>
                        <input type="hidden" name="filePath" id="filePath" value="@pathFile" />
                        <form action="#" class="dropzone" id="mydropzone">
                            <div class="dz-message">
                                <div class="text-center">
                                    <i class="fa fa-cloud-upload fa-3x text-muted mb-3"></i>
                                    <h5>فایل‌ها را اینجا بکشید یا کلیک کنید</h5>
                                    <p class="text-muted">ZIP (Shapefile) یا Excel فایل</p>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Step 2: Field Mapping -->
                <div id="step-2" class="tab-pane" role="tabpanel">
                    <div class="p-4">
                        <h4 class="mb-4">
                            <i class="fa fa-exchange text-primary me-2"></i>
                            تناظر فیلدها
                        </h4>
                        <div class="alert alert-warning" role="alert">
                            <i class="fa fa-exclamation-triangle me-2"></i>
                            <strong>توجه:</strong> لطفاً تناظر فیلدهای لایه با ستون‌های فایل آپلود شده را مشخص کنید.
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="10%">#</th>
                                        <th width="40%">
                                            <i class="fa fa-database me-1"></i>
                                            فیلد لایه
                                        </th>
                                        <th width="50%">
                                            <i class="fa fa-file me-1"></i>
                                            ستون فایل
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (fieldInfos != null)
                                    {
                                        foreach (var item in fieldInfos)
                                        {
                                            if (!string.Equals(item.Name, "gcode", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "area", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "length", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "objectid", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "user", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "time", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "usergroup", StringComparison.OrdinalIgnoreCase) &&
                                                !string.Equals(item.Name, "org", StringComparison.OrdinalIgnoreCase))
                                            {
                                                <tr>
                                                    <td class="text-center">
                                                        <span class="badge bg-primary">@(counter++)</span>
                                                    </td>
                                                    <td>
                                                        <strong>@item.AliasName</strong>
                                                        <br>
                                                        <small class="text-muted">(@item.Name)</small>
                                                    </td>
                                                    <td>
                                                        <select class="form-select select2" id="<EMAIL>" name="<EMAIL>">
                                                            <option value="">انتخاب کنید...</option>
                                                            @if (shapeFileColumns != null)
                                                            {
                                                                foreach (var col in shapeFileColumns)
                                                                {
                                                                    if (string.Equals(item.Name, col, StringComparison.OrdinalIgnoreCase))
                                                                    {
                                                                        <option value="@col" selected>@col</option>
                                                                    }
                                                                    else
                                                                    {
                                                                        <option value="@col">@col</option>
                                                                    }
                                                                }
                                                            }
                                                            @if (excelColumns != null)
                                                            {
                                                                foreach (var col in excelColumns)
                                                                {
                                                                    if (string.Equals(item.Name, col, StringComparison.OrdinalIgnoreCase))
                                                                    {
                                                                        <option value="@col" selected>@col</option>
                                                                    }
                                                                    else
                                                                    {
                                                                        <option value="@col">@col</option>
                                                                    }
                                                                }
                                                            }
                                                        </select>
                                                    </td>
                                                </tr>
                                            }
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Finish -->
                <div id="step-3" class="tab-pane" role="tabpanel">
                    <div class="p-4">
                        <h4 class="mb-4">
                            <i class="fa fa-check-circle text-success me-2"></i>
                            تکمیل فرآیند
                        </h4>
                        <div class="text-center">
                            <div id="finished" class="my-5">
                                <div class="alert alert-info" role="alert">
                                    <i class="fa fa-info-circle me-2"></i>
                                    برای تکمیل فرآیند بروزرسانی، دکمه "ذخیره" را فشار دهید.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function () {
        initializeWizard();
        initializeDropzone();
        initializeSelect2();
    });

    // تابع مقداردهی اولیه ویزارد
    function initializeWizard() {
        // مقداردهی اولیه ویزارد
        $('#wizard').smartWizard({
            theme: 'arrows',
            lang: {
                next: 'بعدی',
                previous: 'قبلی'
            },
            justified: true,
            enableUrlHash: false,
            toolbar: {
                showNextButton: true,
                showPreviousButton: true,
                extraHtml: '<button class="btn btn-success" onclick="publishData()">ذخیره</button>'
            },
            transition: {
                animation: 'fade',
                speed: '400'
            },
            anchor: {
                enableNavigation: true,
                enableNavigationAlways: false
            }
        });

        // اضافه کردن event handler برای leaveStep
        $('#wizard').on('leaveStep', function (e, anchorObject, currentStepIndex, nextStepIndex, stepDirection) {
            if (stepDirection === 'forward' && currentStepIndex === 0) {
                if (!$('#filePath').val()) {
                    showToast('لطفاً ابتدا فایل را آپلود کنید.', 'danger', 4000);
                    return false;
                }
            }
            return true;
        });

        @if (!string.IsNullOrEmpty(pathFile))
        {
            <text>
            // اگر فایل آپلود شده، به مرحله دوم برو
            setTimeout(function() {
                $('#wizard').smartWizard('goToStep', 1);
            }, 100);
            </text>
        }
    }

    function initializeDropzone() {

        // مقداردهی اولیه Dropzone
        $("#mydropzone").dropzone({
            url: "/Database/_FileUploadUpdate?id=@id",
            addRemoveLinks: true,
            maxFiles: 1,
            maxFilesize: 100,
            acceptedFiles: ".zip,.xls,.xlsx",
            dictDefaultMessage: '<div class="text-center"><div class="mb-3"><i class="fa fa-cloud-upload fa-3x text-muted"></i></div><h5>فایل‌ها را اینجا بکشید یا کلیک کنید</h5><p class="text-muted">ZIP (Shapefile) یا Excel</p></div>',
            dictResponseError: 'خطا در آپلود فایل!',
            init: function () {
                this.on("success", function (file, data) {
                    if (data.success && data.responseText) {
                        $("#filePath").val(data.responseText);

                        // جایگزینی کل ویزارد با نسخه جدید
                        $.ajax({
                            url: '@Url.Action("_Update_Wizard", "Database")',
                            type: 'GET',
                            data: { id: '@id', path: data.responseText },
                            success: function (html) {
                                // جایگزینی کل ویزارد با نسخه جدید
                                $('#wid-id-43').replaceWith(html);

                                // مقداردهی اولیه مجدد ویزارد جدید
                                setTimeout(function() {
                                    initializeWizardAfterUpload();
                                }, 100);
                            }
                        });
                    } else {
                        showToast(data.responseText || 'خطا در آپلود فایل', 'danger', 4000);
                    }
                });
                this.on("sending", function (file, xhr, formData) {
                    formData.append("__RequestVerificationToken", $("input[name='__RequestVerificationToken']").val());
                });
                this.on("error", function (file, errorMessage) {
                    showToast(errorMessage || 'خطا در آپلود فایل', 'danger', 4000);
                });
            }
        });
    }

    // تابع مقداردهی اولیه ویزارد بعد از آپلود فایل
    function initializeWizardAfterUpload() {
        // مقداردهی اولیه ویزارد
        $('#wizard').smartWizard({
            theme: 'arrows',
            lang: {
                next: 'بعدی',
                previous: 'قبلی'
            },
            justified: true,
            enableUrlHash: false,
            toolbar: {
                showNextButton: true,
                showPreviousButton: true,
                extraHtml: '<button class="btn btn-success" onclick="publishData()">ذخیره</button>'
            },
            transition: {
                animation: 'fade',
                speed: '400'
            },
            anchor: {
                enableNavigation: true,
                enableNavigationAlways: false
            }
        });

        // مقداردهی اولیه Select2
        $('.select2').select2({
            theme: 'bootstrap-5',
            language: 'fa',
            dir: 'rtl',
            placeholder: 'انتخاب کنید',
            allowClear: true,
            width: '100%'
        });

        // اضافه کردن event handler برای leaveStep
        $('#wizard').on('leaveStep', function (e, anchorObject, currentStepIndex, nextStepIndex, stepDirection) {
            if (stepDirection === 'forward' && currentStepIndex === 0) {
                if (!$('#filePath').val()) {
                    showToast('لطفاً ابتدا فایل را آپلود کنید.', 'danger', 4000);
                    return false;
                }
            }
            return true;
        });

        // رفتن به مرحله دوم
        $('#wizard').smartWizard('goToStep', 1);

        // نمایش پیام موفقیت
        showToast('فایل با موفقیت آپلود شد. لطفاً تناظر فیلدها را بررسی کنید.', 'success', 4000);
    }

    function initializeSelect2() {
        // مقداردهی اولیه Select2
        $('.select2').select2({
            theme: 'bootstrap-5',
            language: 'fa',
            dir: 'rtl',
            placeholder: 'انتخاب کنید',
            allowClear: true,
            width: '100%'
        });
    }

    function publishData() {
        @if (!string.IsNullOrEmpty(id))
        {
            <text>
            let fieldSource = '';
            let fieldDest = '';
            let fieldMapping = '';
            </text>

            if (fieldInfos != null)
            {
                foreach (var item in fieldInfos)
                {
                    if (!string.Equals(item.Name, "gcode", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "area", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "length", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "objectid", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "user", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "time", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "usergroup", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "org", StringComparison.OrdinalIgnoreCase))
                    {
                        <text>
                        const <EMAIL> = $('#<EMAIL>').val();
                        if (<EMAIL>) {
                            fieldMapping += <EMAIL> + ',';
                            fieldSource += '@item.Name' + ',';
                        }
                        </text>
                    }
                }
            }

            <text>
            const obj = {
                id: '@id',
                path: '@pathFile',
                FieldSource: fieldSource,
                FieldDest: fieldMapping,
                FieldCheck: fieldMapping,
                __RequestVerificationToken: $("input[name='__RequestVerificationToken']").val()
            };

            $("#finished").html('<div class="text-center"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">در حال پردازش...</p></div>');

            $.ajax({
                url: '/Database/UpdateData',
                method: 'POST',
                data: obj,
                success: function (data) {
                    if (data.success) {
                        $("#finished").html('<div class="alert alert-success" role="alert"><i class="fa fa-check me-2"></i> تبریک! <br><small>' + data.responseText + '</small></div>');
                        showToast(data.responseText, 'success', 4000);
                        setTimeout(() => {
                            window.location.href = '/Database/Update';
                        }, 2000);
                    } else {
                        $("#finished").html('<div class="alert alert-danger" role="alert"><i class="fa fa-exclamation-circle me-2"></i> خطا! <br><small>' + data.responseText + '</small></div>');
                        showToast(data.responseText, 'danger', 4000);
                    }
                },
                error: function (xhr, status, error) {
                    const errorMessage = xhr.responseJSON?.responseText || 'خطا در ارتباط با سرور';
                    $("#finished").html('<div class="alert alert-danger" role="alert"><i class="fa fa-exclamation-circle me-2"></i> خطا! <br><small>' + errorMessage + '</small></div>');
                    showToast(errorMessage, 'danger', 4000);
                }
            });
            </text>
        }
    }

    // تابع نمایش Toast
    function showToast(message, type, duration) {
        // ایجاد toast container اگر وجود ندارد
        if (!$('#toast-container').length) {
            $('body').append('<div id="toast-container" class="position-fixed top-0 end-0 p-3" style="z-index: 11"></div>');
        }

        const toastId = 'toast-' + Date.now();
        const bgClass = type === 'success' ? 'bg-success' : type === 'danger' ? 'bg-danger' : 'bg-info';

        const toastHtml = `
            <div id="${toastId}" class="toast ${bgClass} text-white" role="alert">
                <div class="toast-body">
                    <i class="fa fa-${type === 'success' ? 'check' : type === 'danger' ? 'exclamation-triangle' : 'info'} me-2"></i>
                    ${message}
                </div>
            </div>
        `;

        $('#toast-container').append(toastHtml);

        const toastElement = new bootstrap.Toast(document.getElementById(toastId), {
            delay: duration || 3000
        });

        toastElement.show();

        // حذف toast بعد از مخفی شدن
        document.getElementById(toastId).addEventListener('hidden.bs.toast', function () {
            $(this).remove();
        });
    }
</script>