﻿namespace BaseGIS.Web.Services
{
    /// <summary>
    /// Handles Web Mercator (EPSG:3857) conversions.
    /// Provides methods to convert between Lat/Lon, Web Mercator meters, and pixel coordinates.
    /// Default tile size for calculations is 512 pixels.
    /// </summary>
    public static class WebMercatorHandler
    {
        private const int EarthRadius = 6378137;
        private const double EarthCircumference = 2.0 * Math.PI * EarthRadius;
        public const int DefaultTileSize = 512;

        /// <summary>
        /// Converts Latitude/Longitude to Web Mercator meters (EPSG:3857).
        /// </summary>
        public static (double x, double y) LatLonToMeters(double lat, double lon)
        {
            double x = lon * EarthRadius * Math.PI / 180.0;
            double y = Math.Log(Math.Tan((90.0 + lat) * Math.PI / 360.0)) * EarthRadius;
            return (x, y);
        }

        /// <summary>
        /// Converts Web Mercator meters (EPSG:3857) to Latitude/Longitude.
        /// </summary>
        public static (double longitude, double latitude) MetersToLatLon((double x, double y) m)
        {
            double lon = m.x / EarthRadius * 180.0 / Math.PI;
            double lat = Math.Atan(Math.Exp(m.y / EarthRadius)) * 360.0 / Math.PI - 90.0;
            lat = Math.Min(Math.Max(lat, -85.0511), 85.0511); // Clamp latitude to valid Web Mercator range
            return (lon, lat);
        }

        [Obsolete("MetersToPixels is deprecated, please use FromMetersToPixels instead.")]
        public static (double x, double y) MetersToPixels((double x, double y) m, int zoom, int tileSize = DefaultTileSize)
        {
            double res = Resolution(zoom, tileSize);
            return FromMetersToPixels(m, res); // Call the non-obsolete double version
        }

        [Obsolete("Use overload that takes resolution directly or the double-returning FromMetersToPixels(valueTuple, res)")]
        public static (long x, long y) FromMetersToPixels((double x, double y) m, int zoom, int tileSize = DefaultTileSize)
        {
            double res = Resolution(zoom, tileSize);
            // This will immediately round to long, so consider using the double version for intermediate steps
            return ((long)Math.Round((m.x + EarthCircumference / 2.0) / res),
                    (long)Math.Round((EarthCircumference / 2.0 - m.y) / res));
        }

        [Obsolete("MetersToPixels is deprecated, please use FromMetersToPixels instead.")]
        public static (double x, double y) MetersToPixels((double x, double y) m, double res)
        {
            double x = (m.x + EarthCircumference / 2.0) / res;
            double y = (EarthCircumference / 2.0 - m.y) / res;
            return (x, y);
        }

        /// <summary>
        /// Converts Web Mercator meters to global pixel coordinates at a given resolution.
        /// The origin (0,0) of this pixel system is the top-left corner of the global map (at zoom 0).
        /// Returns double for precision.
        /// </summary>
        /// <param name="m">The coordinate in Web Mercator meters.</param>
        /// <param name="res">The resolution for the current zoom level.</param>
        /// <returns>Global pixel coordinates (double for precision).</returns>
        public static (double x, double y) FromMetersToPixels((double x, double y) m, double res)
        {
            double x = (m.x + EarthCircumference / 2.0) / res;
            double y = (EarthCircumference / 2.0 - m.y) / res;
            return (x, y);
        }

        [Obsolete("PixelsToMeters is deprecated, please use FromPixelsToMeters instead.")]
        public static (double x, double y) PixelsToMeters((double x, double y) p, int zoom, int tileSize = DefaultTileSize)
        {
            double res = Resolution(zoom, tileSize);
            return FromPixelsToMeters(p, res); // Call the non-obsolete double version
        }

        [Obsolete("Use overload that takes resolution directly or the double-returning FromPixelsToMeters(valueTuple, res)")]
        public static (double x, double y) FromPixelsToMeters((long x, long y) p, int zoom, int tileSize = DefaultTileSize)
        {
            double res = Resolution(zoom, tileSize);
            // This casts to long prematurely if used for intermediate steps.
            // Consider using the double version for intermediate.
            return FromPixelsToMeters(((double)p.x, (double)p.y), res);
        }

        [Obsolete("PixelsToMeters is deprecated, please use FromPixelsToMeters instead.")]
        public static (double x, double y) PixelsToMeters((double x, double y) p, double res)
        {
            double x = p.x * res - EarthCircumference / 2.0;
            double y = EarthCircumference / 2.0 - p.y * res;
            return (x, y);
        }

        /// <summary>
        /// Converts global pixel coordinates to Web Mercator meters.
        /// Accepts double for pixel coordinates to maintain precision.
        /// </summary>
        public static (double x, double y) FromPixelsToMeters((double x, double y) p, double res)
        {
            double x = p.x * res - EarthCircumference / 2.0;
            double y = EarthCircumference / 2.0 - p.y * res;
            return (x, y);
        }

        /// <summary>
        /// Calculates the resolution (meters per pixel) for a given zoom level and tile size.
        /// </summary>
        public static double Resolution(int zoom, int tileSize = DefaultTileSize)
        {
            return EarthCircumference / (tileSize * Math.Pow(2, zoom)); // Using Math.Pow for 2^zoom
        }

        /// <summary>
        /// Calculates the initial resolution (meters per pixel at zoom 0) for a given tile size.
        /// </summary>
        public static double InitialResolution(int tileSize = DefaultTileSize)
        {
            return EarthCircumference / tileSize;
        }

        /// <summary>
        /// Calculates the Web Mercator meter coordinates of the top-left corner of a tile.
        /// </summary>
        /// <param name="tileX">Tile X index.</param>
        /// <param name="tileY">Tile Y index.</param>
        /// <param name="zoom">Zoom level.</param>
        /// <param name="tileSize">The tile size (e.g., 256 or 512).</param>
        /// <returns>A tuple (xMeter, yMeter) for the top-left corner.</returns>
        public static (double xMeter, double yMeter) GetTileTopLeftMeters(int tileX, int tileY, int zoom, int tileSize = DefaultTileSize)
        {
            double resolution = Resolution(zoom, tileSize);

            // Calculate global pixel coordinates for the tile's top-left corner
            // These should be double to maintain precision before converting to meters
            double globalPxX = (double)tileX * tileSize;
            double globalPxY = (double)tileY * tileSize;

            // Use the FromPixelsToMeters overload that takes double inputs
            return FromPixelsToMeters((globalPxX, globalPxY), resolution);
        }
    }
}