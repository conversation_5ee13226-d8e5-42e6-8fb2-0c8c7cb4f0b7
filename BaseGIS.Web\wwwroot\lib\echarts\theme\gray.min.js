!function(e,o){"function"==typeof define&&define.amd?define(["exports","echarts"],o):"object"==typeof exports&&"string"!=typeof exports.nodeName?o(0,require("echarts/lib/echarts")):o(0,e.echarts)}(this,function(e,o){o?o.registerTheme("gray",{color:["#757575","#c7c7c7","#dadada","#8b8b8b","#b5b5b5","#e9e9e9"],title:{textStyle:{fontWeight:"normal",color:"#757575"}},dataRange:{color:["#636363","#dcdcdc"]},toolbox:{color:["#757575","#757575","#757575","#757575"]},tooltip:{backgroundColor:"rgba(0,0,0,0.5)",axisPointer:{type:"line",lineStyle:{color:"#757575",type:"dashed"},crossStyle:{color:"#757575"},shadowStyle:{color:"rgba(200,200,200,0.3)"}}},dataZoom:{dataBackgroundColor:"#eee",fillerColor:"rgba(117,117,117,0.2)",handleColor:"#757575"},grid:{borderWidth:0},categoryAxis:{axisLine:{lineStyle:{color:"#757575"}},splitLine:{lineStyle:{color:["#eee"]}}},valueAxis:{axisLine:{lineStyle:{color:"#757575"}},splitArea:{show:!0,areaStyle:{color:["rgba(250,250,250,0.1)","rgba(200,200,200,0.1)"]}},splitLine:{lineStyle:{color:["#eee"]}}},timeline:{lineStyle:{color:"#757575"},controlStyle:{color:"#757575",borderColor:"#757575"}},candlestick:{itemStyle:{color:"#8b8b8b",color0:"#dadada"},lineStyle:{width:1,color:"#757575",color0:"#c7c7c7"},areaStyle:{color:"#757575",color0:"#e9e9e9"}},map:{itemStyle:{color:"#c7c7c7"},areaStyle:{color:"ddd"},label:{color:"#c12e34"}},graph:{itemStyle:{color:"#e9e9e9"},linkStyle:{color:"#757575"}},chord:{padding:4,itemStyle:{color:"#e9e9e9",borderWidth:1,borderColor:"rgba(128, 128, 128, 0.5)"},lineStyle:{color:"rgba(128, 128, 128, 0.5)"},areaStyle:{color:"#757575"}},gauge:{axisLine:{lineStyle:{color:[[.2,"#b5b5b5"],[.8,"#757575"],[1,"#5c5c5c"]],width:8}}}}):"undefined"!=typeof console&&console&&console.error&&console.error("ECharts is not Loaded")});