using BaseGIS.Core.Entities;
using BaseGIS.Core.Interfaces;
using BaseGIS.Application.DTOs;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BaseGIS.Application.UseCases
{
    public class GetTableInfoUseCase
    {
        private readonly ITableInfoRepository _repository;

        public GetTableInfoUseCase(ITableInfoRepository repository)
        {
            _repository = repository;
        }

        public async Task<TableInfoDto> GetByIdAsync(int id)
        {
            var tableInfo = await _repository.GetByIdAsync(id);
            if (tableInfo == null)
            {
                return null;
            }

            return new TableInfoDto
            {
                Id = tableInfo.Id,
                Name = tableInfo.Name,
                AliasName = tableInfo.AliasName,
                GroupInfoId = tableInfo.GroupInfoId,
                DatasetType = tableInfo.DatasetType,
                ShortName = tableInfo.ShortName,
                ValidationRule = tableInfo.ValidationRule,
                MinScale = tableInfo.MinScale,
                MaxScale = tableInfo.MaxScale,
                MinLabelScale = tableInfo.MinLabelScale,
                MaxLabelScale = tableInfo.MaxLabelScale,
                SimplifyFactor = tableInfo.SimplifyFactor
            };
        }

        public async Task<List<TableInfoDto>> GetAllAsync()
        {
            var tableInfos = await _repository.GetAllAsync();
            return tableInfos.Select(t => new TableInfoDto
            {
                Id = t.Id,
                Name = t.Name,
                AliasName = t.AliasName,
                GroupInfoId = t.GroupInfoId,
                DatasetType = t.DatasetType,
                ShortName = t.ShortName,
                ValidationRule = t.ValidationRule,
                MinScale = t.MinScale,
                MaxScale = t.MaxScale,
                MinLabelScale = t.MinLabelScale,
                MaxLabelScale = t.MaxLabelScale,
                SimplifyFactor = t.SimplifyFactor
            }).ToList();
        }
    }
}