﻿@using BaseGIS.Core.Entities
@{
    List<TableInfo> tableInfos = Model;

    List<TableInfo> tblInf = tableInfos.ToList();
    var GroupList = tblInf.Select(x => x.GroupInfo).Distinct();
    string id = Context.Request.Query["id"];
}
<article class="col-sm-12 col-md-12 col-lg-12" id="P_PersonelList">
    <div class="jarviswidget jarviswidget-color-red jarviswidget-sortable" id="wid-id-44" data-widget-colorbutton="false" data-widget-editbutton="false" role="widget">
        <header role="heading">
            <div class="jarviswidget-ctrls" role="menu">
                <a href="javascript:void(0);" class="button-icon jarviswidget-toggle-btn" rel="tooltip" title="" data-placement="bottom" data-original-title="Collapse"><i class="fa fa-minus "></i></a>
                <a href="javascript:void(0);" class="button-icon jarviswidget-fullscreen-btn" rel="tooltip" title="" data-placement="bottom" data-original-title="تمام صفحه"><i class="fa fa-expand "></i></a>
            </div>
            <h2 class="samanFont">فهرست لایه ها</h2>
            <span class="jarviswidget-loader"><i class="fa fa-refresh fa-spin"></i></span>
            


        </header>
        <!-- widget div-->
        <div role="content">
            <!-- widget content -->
            <div class="widget-body no-padding">
                <div class="widget-body-toolbar">
                    <div class="row">
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                <input class="form-control" placeholder="جستجو" type="text" id="FilterTree1">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="custom-scroll  tree" id="treeLayer" style="height: 350px; overflow-y: scroll;">
                    <ul>
                        <li>
                            <span class="label label-success"><i class="fa fa-lg fa-minus-circle"></i>  لایه ها</span>
                            <ul>
                                @if (GroupList.Count() > 0)
                                {
                                    foreach (var group in GroupList)
                                    {
                                        var list = tblInf.Where(a => a.GroupInfo == group);

                                        if (list.Count() > 0)
                                        {
                                            <li>
                                                @if (group != null)
                                                {
                                                    <span class="label label-success"><i class="fa fa-lg fa-plus-circle"></i> @group.AliasName</span>
                                                }
                                                else
                                                {
                                                    <span class="label label-success"><i class="fa fa-lg fa-plus-circle"></i>بدون گروه بندی</span>
                                                }
                                                <ul class="layer">
                                                    @foreach (var item in list)
                                                    {
                                                        string lbl = "";
                                                        if (id == item.Id.ToString())
                                                        {
                                                            lbl = "background-color:cyan";
                                                        }
                                                        string symol = "fa-table txt-color-yellow";
                                                        if (item.DatasetType.ToLower() == "point")
                                                        { symol = "fa-map-marker txt-color-red"; }
                                                        else if (item.DatasetType.ToLower() == "polyline")
                                                        { symol = "fa-flash txt-color-green"; }
                                                        else if (item.DatasetType.ToLower() == "polygon")
                                                        { symol = "fa-square-o txt-color-blue"; }
                                                        <li style="display:none">
                                                            <span class="" style="@lbl"><i class="fa @symol "></i> @item.AliasName</span>
                                                            <a data-toggle="modal" href="~/database/_LayerUserGroup_Managment?id=@item.Id" data-target=" #ModalLayerManagment" title="ویرایش" class="editBtn btn  btn-xs  "><i class="fa fa-edit"></i></a>


                                                        </li>


                                                    }
                                                </ul>
                                            </li>
                                        }
                                    }
                                }
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
            <!-- end widget content -->
        </div>
        <!-- end widget div -->
    </div>
</article>


<div class="modal fade" id="ModalLayerManagment" tabindex="-1" role="dialog" aria-labelledby="remoteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">

        </div>
    </div>
</div>

<!-- end row -->
<script type="text/javascript">
    $(document).ready(function () {
        refreshlisttree();

        $('#ModalLayerManagment').on('hidden.bs.modal', function () {
            Refresh_Layer_List();
            Refresh_Layer_Fields(@Context.Request.Query["id"]);

        });
    });
  
    function Refresh_Layer_List() {
        $("#_LayerUserGroup_List").load('@Url.Action("_LayerUserGroup_List", "database")');
        refreshlisttree();
    }
    function refreshlisttree() {
        var mytreebranch = $("#treeLayer").find("li:has(ul)").addClass("parent_li").attr("role", "treeitem").find(" > span").attr("title", "Collapse this branch");
            $("#treeLayer > ul").attr("role", "tree").find("ul").attr("role", "group"), mytreebranch.on("click", function (a) {

                var b = $(this).parent("li.parent_li").find(" > ul > li");

                if (b.is(":visible")) {
                    (b.hide("fast"), $(this).attr("title", "Expand this branch").find(" > i").addClass("icon-plus-sign").removeClass("icon-minus-sign"))
                }
                else {

                    (b.show("fast"), $(this).attr("title", "Collapse this branch").find(" > i").addClass("icon-minus-sign").removeClass("icon-plus-sign"));//, a.stopPropagation()
                }
                a.stopPropagation();

            });

    }

    jQuery("#FilterTree1").keyup(function () {
        var filter = pureArabic4(jQuery(this).val());
        jQuery("#treeLayer ul li").each(function () {
            var itemText = pureArabic4(jQuery(this).text());
            if (itemText.search(new RegExp(filter, "i")) < 0) {
                jQuery(this).hide();
            } else {
                jQuery(this).show()
            }
        });
    });

</script>



