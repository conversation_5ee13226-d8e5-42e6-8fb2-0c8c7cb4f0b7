is_global = true
build_property.TargetFramework = net9.0
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = BaseGIS.Web
build_property.RootNamespace = BaseGIS.Web
build_property.ProjectDir = D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Account/AccessDenied.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxBY2Nlc3NEZW5pZWQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Account/ForgotPassword.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxGb3Jnb3RQYXNzd29yZC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Account/ForgotPasswordConfirmation.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxGb3Jnb3RQYXNzd29yZENvbmZpcm1hdGlvbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Account/Login.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxMb2dpbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Account/ResetPassword.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxSZXNldFBhc3N3b3JkLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Account/ResetPasswordConfirmation.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxSZXNldFBhc3N3b3JkQ29uZmlybWF0aW9uLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Account/TwoFactorLogin.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxUd29GYWN0b3JMb2dpbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/Delete.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcRGVsZXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/GenerateTile.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcR2VuZXJhdGVUaWxlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/ImportShapefile.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcSW1wb3J0U2hhcGVmaWxlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/ImportShapefileMapping.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcSW1wb3J0U2hhcGVmaWxlTWFwcGluZy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/Insert.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcSW5zZXJ0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/LayerSetting.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcTGF5ZXJTZXR0aW5nLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/LayerUserGroup.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcTGF5ZXJVc2VyR3JvdXAuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/SymbologyManager.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcU3ltYm9sb2d5TWFuYWdlci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/Update.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcVXBkYXRlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/_Delete_List.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcX0RlbGV0ZV9MaXN0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/_Delete_Query.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcX0RlbGV0ZV9RdWVyeS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/_Insert_List.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcX0luc2VydF9MaXN0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/_Insert_Wizard.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcX0luc2VydF9XaXphcmQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/_LayerUserGroup_List.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcX0xheWVyVXNlckdyb3VwX0xpc3QuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/_LayerUserGroup_Managment.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcX0xheWVyVXNlckdyb3VwX01hbmFnbWVudC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/_Layer_Field.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcX0xheWVyX0ZpZWxkLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/_Layer_Fields.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcX0xheWVyX0ZpZWxkcy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/_Layer_Field_Domain.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcX0xheWVyX0ZpZWxkX0RvbWFpbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/_Layer_Group.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcX0xheWVyX0dyb3VwLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/_Layer_Managment.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcX0xheWVyX01hbmFnbWVudC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/_Layer_Relation.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcX0xheWVyX1JlbGF0aW9uLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/_Layer_Symbols.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcX0xheWVyX1N5bWJvbHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/_Update_List.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcX1VwZGF0ZV9MaXN0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Database/_Update_Wizard.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGF0YWJhc2VcX1VwZGF0ZV9XaXphcmQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/GeoMap/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcR2VvTWFwXEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/GeoMap/Index2.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcR2VvTWFwXEluZGV4Mi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/GeoMap/Property.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcR2VvTWFwXFByb3BlcnR5LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Home/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxQcml2YWN5LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Shared/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXEVycm9yLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Shared/_AdminMenu.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9BZG1pbk1lbnUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Shared/_Layout1.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQxLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Shared/_LayoutMap.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXRNYXAuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Shared/_LoginLayout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9Mb2dpbkxheW91dC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Shared/_Sidebar.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9TaWRlYmFyLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/Symbology/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU3ltYm9sb2d5XEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/User/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlclxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/Backups/BaseProject/BaseGIS/BaseGIS.Web/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 
