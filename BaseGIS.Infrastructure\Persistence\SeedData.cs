﻿using BaseGIS.Core.Entities;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace BaseGIS.Infrastructure.Persistence
{
    public class SeedData
    {
        public static async Task Initialize(IServiceProvider serviceProvider, ILogger logger)
        {
            try
            {
                using (var scope = serviceProvider.CreateScope())
                {
                    var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                    var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();
                    var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();

                    logger.LogInformation("Starting database seeding");

                    string[] roles = { "Admin", "User" };
                    foreach (var role in roles)
                    {
                        if (!await roleManager.RoleExistsAsync(role))
                        {
                            logger.LogInformation($"Creating role: {role}");
                            await roleManager.CreateAsync(new IdentityRole(role));
                        }
                    }

                    // Create default admin user
                    var adminUser = new ApplicationUser
                    {
                        UserName = "admin",
                        FullName = "ادمین",
                        PhoneNumber = "09124013941",
                        PhoneNumberConfirmed = true,
                        Email = "<EMAIL>",
                        EmailConfirmed = true,
                        Enabled = true,
                        UserGroup = "Admin"
                    };

                    if (await userManager.FindByEmailAsync(adminUser.Email) == null)
                    {
                        var result = await userManager.CreateAsync(adminUser, "qwer4321");
                        if (result.Succeeded)
                        {
                            await userManager.AddToRoleAsync(adminUser, "Admin");
                        }
                        else
                        {
                            var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                        }
                    }
                    var users = new[]
                    {
                        new ApplicationUser { UserName = "user", FullName="کاربر",Email = "<EMAIL>",  PhoneNumber = "09124013941",Enabled = true, UserGroup = "User" },
                    };

                    foreach (var user in users)
                    {
                        if (await userManager.FindByNameAsync(user.UserName) == null)
                        {
                            await userManager.CreateAsync(user, "qwer1234");
                            if (user.UserName == "user") await userManager.AddToRoleAsync(user, "User");
                        }
                    }

                    await dbContext.SaveChangesAsync();
                    logger.LogInformation("Finished database seeding");
                    if (!dbContext.AppSettings.Any(s => s.Name == "CompanyName"))
                    {
                        dbContext.AppSettings.Add(new AppSetting { Name = "CompanyName", Config = "راه آهن" });
                    }

                    await dbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while seeding the database");
                // Don't throw the exception - log it and continue
            }
        }
    }
}
