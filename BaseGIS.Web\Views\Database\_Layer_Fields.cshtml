﻿@using BaseGIS.Core.Entities
@{
    string id = Context.Request.Query["id"];
    List<FieldInfo> lisFields = Model;
    int counter = 1;
}

<div >
    <div class="card border-danger mb-3 samanFont" id="wid-id-40">
        <div class="card-header bg-danger text-white">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h5 class="card-title samanFont mb-0">مشخصات فیلدها</h5>
                    <span class="spinner-border spinner-border-sm text-light d-none ms-2"></span>
                </div>
                <div>
                    <a data-bs-toggle="modal" href="~/database/_Layer_Field?tblid=@id" data-bs-target="#ModalLayerField" 
                       class="btn btn-sm btn-outline-light" title="ایجاد فیلد جدید">
                        <i class="fa fa-plus"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Content area -->
        <div class="collapse show" id="fieldsContent">
            <div class="card-body p-0">
                <div class="table-responsive" style="height: 399px; overflow-y: auto;">
                    <table class="table table-striped table-hover align-middle mb-0">
                        <thead class="table-light">
                            <tr>
                                <th scope="col" style="width: 50px">#</th>
                                <th scope="col">نام فیلد</th>
                                <th scope="col">نام فارسی</th>
                                <th scope="col">واحد <i class="fa fa-tags"></i></th>
                                <th scope="col">نوع <i class="fa fa-code"></i></th>
                                <th scope="col">طول <i class="fa fa-code"></i></th>
                                <th scope="col">اجباری <i class="fa fa-file-text-o"></i></th>
                                <th scope="col">منحصربفرد <i class="fa fa-file-o"></i></th>
                                <th scope="col">قابل ویرایش <i class="fa fa-pencil-square-o"></i></th>
                                <th scope="col" style="width: 120px">عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (lisFields.Any())
                            {
                                foreach (var item in lisFields)
                                {
                                    <tr>
                                        <td>@(counter++)</td>
                                        <td>@item.Name</td>
                                        <td>@item.AliasName</td>
                                        <td>@item.UnitName</td>
                                        <td>@(item.FieldType == "datetime" ? "date" : item.FieldType)</td>
                                        <td>@item.FieldLength</td>
                                        <td>@(item.IsRequired ? "بلی" : "خیر")</td>
                                        <td>@(item.IsUnique ? "بلی" : "خیر")</td>
                                        <td>@(item.Editable ? "بلی" : "خیر")</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                @if (item.Name.ToLower() != "gcode" && item.Name.ToLower() != "area" && 
                                                    item.Name.ToLower() != "length" && item.Name.ToLower() != "objectid" && 
                                                    item.Name.ToLower() != "creatoruser" && item.Name.ToLower() != "createtime" && 
                                                    item.Name.ToLower() != "lastuser" && item.Name.ToLower() != "lasttime" && 
                                                    item.Name.ToLower() != "usergroup" && item.Name.ToLower() != "org")
                                                {
                                                    <a data-bs-toggle="modal" href="~/database/_Layer_Field?tblid=@id&id=@item.Id" 
                                                       data-bs-target="#ModalLayerField" class="btn btn-outline-primary" title="ویرایش">
                                                        <i class="fa fa-pencil"></i>
                                                    </a>
                                                    <button type="button" onclick="frmLayerFieldDel(@item.Id);" 
                                                            class="btn btn-outline-danger" title="حذف">
                                                        <i class="fa fa-times"></i>
                                                    </button>
                                                    if (item.FieldType.Contains("domain"))
                                                    {
                                                        <a data-bs-toggle="modal" href="~/database/_Layer_Field_Domain?tblid=@id&id=@item.Id" 
                                                           data-bs-target="#ModalLayerField" class="btn btn-outline-success" title="دامنه">
                                                            <i class="fa fa-th-list"></i>
                                                        </a>
                                                    }
                                                }
                                                else
                                                {
                                                    <a data-bs-toggle="modal" href="~/database/_Layer_Field?tblid=@id&id=@item.Id"
                                                       data-bs-target="#ModalLayerField" class="btn btn-outline-primary" title="ویرایش">
                                                        <i class="fa fa-pencil"></i>
                                                    </a>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            }
                            else
                            {
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fa fa-info-circle me-1"></i>هیچ فیلدی یافت نشد
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="ModalLayerField" tabindex="-1" aria-labelledby="ModalLayerFieldLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <!-- Modal content will be loaded here -->
        </div>
    </div>
</div>
 @section Scripts {
<script type="text/javascript">
    $(document).ready(function () {
        // Handle modal loading
        $('a[data-bs-toggle="modal"][data-bs-target="#ModalLayerField"]').on('click', function (e) {
            e.preventDefault();
            var url = $(this).attr('href');

            $.ajax({
                url: url,
                cache: false,
                success: function (data) {
                    $("#ModalLayerField .modal-content").html(data);
                    var modalElement = document.getElementById('ModalLayerField');
                    var modal = new bootstrap.Modal(modalElement);
                    modal.show();
                },
                error: function (data) {
                    console.error("Error loading modal content:", data);
                    showToast('خطا در بارگذاری محتوا', 'danger');
                }
            });
        });

        // Handle modal close event
        $('#ModalLayerField').on('hidden.bs.modal', function () {
            Refresh_Layer_Fields(@Context.Request.Query["id"]);

            // Remove backdrop manually if it's still present
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
            // Also remove modal-open class from body if needed
            document.body.classList.remove('modal-open');
            // And remove inline styles that Bootstrap might have added
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        });
    });

    function Refresh_Layer_Fields(id) {
        if (id)
            $("#_Layer_Fields").load('../database/_Layer_Fields?id=' + id);
    }

    function frmLayerFieldDel(idField) {
        if (idField) {
            Swal.fire({
                title: 'تأیید حذف',
                html: "<b class='samanFont'>آیا مطمئن به حذف فیلد انتخاب شده هستید؟</b>",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545', // Bootstrap 5 danger color
                cancelButtonColor: '#6c757d', // Bootstrap 5 secondary color
                confirmButtonText: 'حذف',
                cancelButtonText: 'لغو',
                reverseButtons: true,
                customClass: {
                    popup: 'samanFont',
                    title: 'samanFont',
                    htmlContainer: 'samanFont',
                    confirmButton: 'samanFont btn btn-danger',
                    cancelButton: 'samanFont btn btn-secondary'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        type: 'POST',
                        url: "../database/_Layer_FieldDel",
                        data: { id: idField },
                        dataType: 'json',
                        success: function (data) {
                            if (data.success) {
                                Refresh_Layer_Fields(@Context.Request.Query["id"]);
                                Swal.fire({
                                    title: 'موفق',
                                    html: data.responseText,
                                    icon: 'success',
                                    timer: 2000
                                });
                            } else {
                                Swal.fire({
                                    title: 'خطا',
                                    html: data.responseText,
                                    icon: 'error'
                                });
                            }
                        },
                        error: function (data) {
                            Swal.fire({
                                title: 'خطا',
                                html: data.statusText,
                                icon: 'error'
                            });
                        }
                    });
                }
            });
        } else {
            Swal.fire({
                title: 'لطفا یک فیلد را انتخاب نمایید',
                html: data.statusText,
                icon: 'error'
            });
        }
    }
     
</script>
 }