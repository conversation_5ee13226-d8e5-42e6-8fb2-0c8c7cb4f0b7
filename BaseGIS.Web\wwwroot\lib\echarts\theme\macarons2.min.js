!function(e,o){"function"==typeof define&&define.amd?define(["exports","echarts"],o):"object"==typeof exports&&"string"!=typeof exports.nodeName?o(0,require("echarts/lib/echarts")):o(0,e.echarts)}(this,function(e,o){o?o.registerTheme("macarons2",{color:["#ed9678","#e7dac9","#cb8e85","#f3f39d","#c8e49c","#f16d7a","#f3d999","#d3758f","#dcc392","#2e4783","#82b6e9","#ff6347","#a092f1","#0a915d","#eaf889","#6699FF","#ff6666","#3cb371","#d5b158","#38b6b6"],title:{textStyle:{fontWeight:"normal",color:"#cb8e85"}},dataRange:{color:["#cb8e85","#e7dac9"],textStyle:{color:"#333"}},bar:{barMinHeight:0,barGap:"30%",barCategoryGap:"20%",label:{show:!1},itemStyle:{barBorderColor:"#fff",barBorderRadius:0,barBorderWidth:1},emphasis:{itemStyle:{barBorderColor:"rgba(0,0,0,0)",barBorderRadius:0,barBorderWidth:1},label:{show:!1}}},line:{label:{show:!1},itemStyle:{},emphasis:{label:{show:!1}},lineStyle:{width:2,type:"solid",shadowColor:"rgba(0,0,0,0)",shadowBlur:5,shadowOffsetX:3,shadowOffsetY:3},symbolSize:2,showAllSymbol:!1},candlestick:{itemStyle:{color:"#fe9778",color0:"#e7dac9"},lineStyle:{width:1,color:"#f78766",color0:"#f1ccb8"},areaStyle:{color:"#e7dac9",color0:"#c8e49c"}},pie:{center:["50%","50%"],radius:[0,"75%"],clockWise:!1,startAngle:90,minAngle:0,selectedOffset:10,label:{show:!0,position:"outer",color:"#1b1b1b",lineStyle:{color:"#1b1b1b"}},itemStyle:{borderColor:"#fff",borderWidth:1},labelLine:{show:!0,length:20,lineStyle:{width:1,type:"solid"}}},map:{itemStyle:{color:"#ddd",borderColor:"#fff",borderWidth:1},areaStyle:{color:"#f3f39d"},label:{show:!1,color:"rgba(139,69,19,1)"},showLegendSymbol:!0},graph:{itemStyle:{color:"#d87a80"},linkStyle:{strokeColor:"#a17e6e"},nodeStyle:{brushType:"both",strokeColor:"#a17e6e"},label:{show:!1}},gauge:{axisLine:{lineStyle:{color:[[.2,"#ed9678"],[.8,"#e7dac9"],[1,"#cb8e85"]],width:8}}}}):"undefined"!=typeof console&&console&&console.error&&console.error("ECharts is not Loaded")});