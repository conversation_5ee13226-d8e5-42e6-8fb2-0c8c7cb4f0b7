using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BaseGIS.Core.Entities;

namespace BaseGIS.Infrastructure.Persistence.Configurations
{
    public class GroupInfoConfiguration : IEntityTypeConfiguration<GroupInfo>
    {
        public void Configure(EntityTypeBuilder<GroupInfo> builder)
        {
            builder.HasKey(g => g.Id);
            builder.Property(g => g.Name).IsRequired().HasMaxLength(50);
            builder.Property(g => g.AliasName).IsRequired().HasMaxLength(100);
            builder.HasMany(g => g.TableInfos)
                   .WithOne(t => t.GroupInfo)
                   .HasForeignKey(t => t.GroupInfoId);
        }
    }
} 