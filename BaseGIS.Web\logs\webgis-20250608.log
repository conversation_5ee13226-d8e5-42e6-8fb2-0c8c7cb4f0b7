2025-06-08 18:07:24.725 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-08 18:07:26.594 +03:30 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-08 18:07:26.730 +03:30 [INF] Executed DbCommand (106ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-08 18:07:26.787 +03:30 [INF] Starting database seeding
2025-06-08 18:07:27.363 +03:30 [INF] Executed DbCommand (24ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-08 18:07:27.459 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-08 18:07:27.489 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedEmail_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0
2025-06-08 18:07:27.560 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedUserName] = @__normalizedUserName_0
2025-06-08 18:07:27.617 +03:30 [INF] Finished database seeding
2025-06-08 18:07:27.660 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [AppSettings] AS [a]
        WHERE [a].[Name] = N'CompanyName') THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-08 18:07:27.672 +03:30 [INF] Database seeding completed
2025-06-08 18:07:28.798 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-08 18:07:28.801 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-08 18:07:28.890 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-08 18:07:28.892 +03:30 [INF] Hosting environment: Development
2025-06-08 18:07:28.893 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-08 18:07:28.898 +03:30 [INF] The application has started
2025-06-08 18:07:36.344 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-08 18:07:37.124 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-08 18:07:37.189 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__user_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ClaimType], [a].[ClaimValue], [a].[UserId]
FROM [AspNetUserClaims] AS [a]
WHERE [a].[UserId] = @__user_Id_0
2025-06-08 18:07:37.221 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-08 18:07:37.229 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-08 18:07:37.242 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__role_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0
2025-06-08 18:07:37.252 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-08 18:07:37.267 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-08 18:07:37.478 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-08 18:07:37.485 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.906ms.
2025-06-08 18:07:37.500 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-08 18:07:37.534 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-08 18:07:37.618 +03:30 [INF] Executed ViewResult - view Index executed in 117.1243ms.
2025-06-08 18:07:37.628 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 356.6354ms
2025-06-08 18:07:37.631 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-08 18:07:37.643 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 1309.606ms
2025-06-08 18:07:37.700 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - null null
2025-06-08 18:07:37.700 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-08 18:07:37.715 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-08 18:07:37.700 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - null null
2025-06-08 18:07:37.715 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-08 18:07:37.768 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - null null
2025-06-08 18:07:38.119 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-08 18:07:38.119 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-08 18:07:37.779 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - null null
2025-06-08 18:07:37.779 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/avatars/male.png - null null
2025-06-08 18:07:38.198 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 499 2750 image/jpeg 498.0316ms
2025-06-08 18:07:38.199 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 499 3398 image/jpeg 484.4301ms
2025-06-08 18:07:38.195 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - null null
2025-06-08 18:07:38.382 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - null null
2025-06-08 18:07:38.368 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - null null
2025-06-08 18:07:38.195 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - null null
2025-06-08 18:07:38.192 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-08 18:07:38.197 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-08 18:07:38.323 +03:30 [INF] Sending file. Request path: '/assets/img/AdminLTELogo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\AdminLTELogo.png'
2025-06-08 18:07:38.452 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-08 18:07:38.457 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-08 18:07:38.460 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - null null
2025-06-08 18:07:38.464 +03:30 [INF] Sending file. Request path: '/lib/source-sans-3/index.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\source-sans-3\index.css'
2025-06-08 18:07:38.465 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.js - null null
2025-06-08 18:07:38.482 +03:30 [INF] Sending file. Request path: '/css/site.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site.css'
2025-06-08 18:07:38.483 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - 200 2520 text/css 116.8588ms
2025-06-08 18:07:38.466 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 499 4983 image/jpeg 751.2363ms
2025-06-08 18:07:38.474 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - null null
2025-06-08 18:07:38.472 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - 499 2637 image/png 692.7456ms
2025-06-08 18:07:38.474 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/avatars/male.png - 499 268 image/png 695.2687ms
2025-06-08 18:07:38.496 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.css'
2025-06-08 18:07:38.499 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - 200 7707 text/css 799.3887ms
2025-06-08 18:07:38.500 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/styles/overlayscrollbars.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\styles\overlayscrollbars.min.css'
2025-06-08 18:07:38.515 +03:30 [INF] Sending file. Request path: '/lib/popper.js/dist/umd/popper.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\popper.js\dist\umd\popper.min.js'
2025-06-08 18:07:38.516 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 319.0396ms
2025-06-08 18:07:38.525 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.css'
2025-06-08 18:07:38.526 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\browser\overlayscrollbars.browser.es6.min.js'
2025-06-08 18:07:38.529 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - 200 5267 text/css 760.3945ms
2025-06-08 18:07:38.536 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.js'
2025-06-08 18:07:38.536 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.js'
2025-06-08 18:07:38.538 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - 200 13202 text/css 343.1443ms
2025-06-08 18:07:38.541 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - 200 20122 text/javascript 81.1864ms
2025-06-08 18:07:38.545 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/bootstrap-icons.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\bootstrap-icons.min.css'
2025-06-08 18:07:38.546 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - 200 23932 text/css 192.4759ms
2025-06-08 18:07:38.548 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - 200 29334 text/javascript 353.3622ms
2025-06-08 18:07:38.553 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - 200 46668 text/javascript 79.8548ms
2025-06-08 18:07:38.557 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.js - 200 67484 text/javascript 91.2447ms
2025-06-08 18:07:38.568 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - 200 85875 text/css 868.2168ms
2025-06-08 18:07:38.576 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 118.9658ms
2025-06-08 18:07:38.602 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-08 18:07:38.723 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-08 18:07:38.811 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-08 18:07:38.818 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 499 45276 application/font-woff 216.4875ms
2025-06-08 18:07:38.812 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-08 18:07:38.820 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-08 18:07:38.945 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-08 18:07:38.946 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - 200 130396 font/woff2 225.4041ms
2025-06-08 18:07:38.949 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 499 5430 image/x-icon 137.819ms
2025-06-08 18:07:45.125 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/map/index - null null
2025-06-08 18:07:45.194 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.MapController.Index (BaseGIS.Web)'
2025-06-08 18:07:45.202 +03:30 [INF] Route matched with {action = "Index", controller = "Map"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.MapController (BaseGIS.Web).
2025-06-08 18:07:45.247 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.MapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-08 18:07:45.320 +03:30 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-08 18:07:45.415 +03:30 [INF] Executed DbCommand (49ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [g].[Id], [g].[AliasName], [g].[Name], [s].[Id], [s].[AliasName], [s].[DatasetType], [s].[GroupInfoId], [s].[MaxLabelScale], [s].[MaxScale], [s].[MinLabelScale], [s].[MinScale], [s].[Name], [s].[ShortName], [s].[SimplifyFactor], [s].[ValidationRule], [s].[Id0], [s].[AliasName0], [s].[CalcPeriod], [s].[DisableRule], [s].[Editable], [s].[FieldIndex], [s].[FieldLength], [s].[FieldType], [s].[IsDisplay], [s].[IsRequired], [s].[IsUnique], [s].[Name0], [s].[SQLCalc], [s].[ShpFieldName], [s].[TableInfoId], [s].[UnitName], [s].[Updated], [s].[ValidationRule0], [s].[WebService_Period], [s].[WebService_URL]
FROM [GroupInfos] AS [g]
LEFT JOIN (
    SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [f0].[Id] AS [Id0], [f0].[AliasName] AS [AliasName0], [f0].[CalcPeriod], [f0].[DisableRule], [f0].[Editable], [f0].[FieldIndex], [f0].[FieldLength], [f0].[FieldType], [f0].[IsDisplay], [f0].[IsRequired], [f0].[IsUnique], [f0].[Name] AS [Name0], [f0].[SQLCalc], [f0].[ShpFieldName], [f0].[TableInfoId], [f0].[UnitName], [f0].[Updated], [f0].[ValidationRule] AS [ValidationRule0], [f0].[WebService_Period], [f0].[WebService_URL]
    FROM [TableInfos] AS [t0]
    LEFT JOIN [FieldInfos] AS [f0] ON [t0].[Id] = [f0].[TableInfoId]
) AS [s] ON [g].[Id] = [s].[GroupInfoId]
WHERE EXISTS (
    SELECT 1
    FROM [TableInfos] AS [t]
    WHERE [g].[Id] = [t].[GroupInfoId] AND EXISTS (
        SELECT 1
        FROM [FieldInfos] AS [f]
        WHERE [t].[Id] = [f].[TableInfoId] AND LOWER([f].[FieldType]) LIKE N'%geometry%'))
ORDER BY [g].[Id], [s].[Id]
2025-06-08 18:07:45.513 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.MapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 263.227ms.
2025-06-08 18:07:45.520 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-08 18:07:45.529 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-08 18:07:45.535 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-08 18:07:45.588 +03:30 [INF] Executed ViewResult - view Index executed in 68.3953ms.
2025-06-08 18:07:45.594 +03:30 [INF] Executed action BaseGIS.Web.Controllers.MapController.Index (BaseGIS.Web) in 387.924ms
2025-06-08 18:07:45.596 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.MapController.Index (BaseGIS.Web)'
2025-06-08 18:07:45.599 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/map/index - 200 null text/html; charset=utf-8 474.7639ms
2025-06-08 18:07:45.653 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css - null null
2025-06-08 18:07:45.656 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jqueryui/themes/base/jquery-ui.min.css - null null
2025-06-08 18:07:45.661 +03:30 [INF] Sending file. Request path: '/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery.fancytree\skin-win8\ui.fancytree.min.css'
2025-06-08 18:07:45.663 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-08 18:07:45.664 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-08 18:07:45.664 +03:30 [INF] Sending file. Request path: '/lib/jqueryui/themes/base/jquery-ui.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jqueryui\themes\base\jquery-ui.min.css'
2025-06-08 18:07:45.672 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js - null null
2025-06-08 18:07:45.667 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jqueryui/jquery-ui.min.js - null null
2025-06-08 18:07:45.672 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/esri-leaflet/dist/esri-leaflet.js - null null
2025-06-08 18:07:45.666 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css - 200 17671 text/css 13.1887ms
2025-06-08 18:07:45.672 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/jquery.fancytree-all.min.js - null null
2025-06-08 18:07:45.684 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 19.6428ms
2025-06-08 18:07:45.686 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 22.7531ms
2025-06-08 18:07:45.686 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jqueryui/themes/base/jquery-ui.min.css - 200 30724 text/css 29.9425ms
2025-06-08 18:07:45.691 +03:30 [INF] Sending file. Request path: '/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\esri-leaflet-renderers\dist\esri-leaflet-renderers.js'
2025-06-08 18:07:45.698 +03:30 [INF] Sending file. Request path: '/lib/esri-leaflet/dist/esri-leaflet.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\esri-leaflet\dist\esri-leaflet.js'
2025-06-08 18:07:45.698 +03:30 [INF] Sending file. Request path: '/lib/jqueryui/jquery-ui.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jqueryui\jquery-ui.min.js'
2025-06-08 18:07:45.707 +03:30 [INF] Sending file. Request path: '/lib/jquery.fancytree/jquery.fancytree-all.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery.fancytree\jquery.fancytree-all.min.js'
2025-06-08 18:07:45.720 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js - 200 16208 text/javascript 48.3858ms
2025-06-08 18:07:45.723 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/esri-leaflet/dist/esri-leaflet.js - 200 67663 text/javascript 50.9072ms
2025-06-08 18:07:45.725 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jqueryui/jquery-ui.min.js - 200 253669 text/javascript 58.077ms
2025-06-08 18:07:45.727 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - null null
2025-06-08 18:07:45.727 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/jquery.fancytree-all.min.js - 200 125100 text/javascript 54.9381ms
2025-06-08 18:07:46.000 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-08 18:07:46.001 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/icons.gif - null null
2025-06-08 18:07:46.015 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/webfonts/fa-solid-900.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\webfonts\fa-solid-900.woff2'
2025-06-08 18:07:46.025 +03:30 [INF] Sending file. Request path: '/lib/jquery.fancytree/skin-win8/icons.gif'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery.fancytree\skin-win8\icons.gif'
2025-06-08 18:07:46.027 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - 499 158220 font/woff2 299.8401ms
2025-06-08 18:07:46.029 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/icons.gif - 200 5510 image/gif 28.8076ms
2025-06-08 18:07:46.048 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-08 18:07:46.063 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-08 18:07:46.107 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-08 18:07:46.137 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-08 18:07:46.146 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 36.681ms.
2025-06-08 18:07:46.152 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType26`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType27`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType25`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-08 18:07:46.187 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 121.5328ms
2025-06-08 18:07:46.189 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-08 18:07:46.191 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 238.839ms
2025-06-08 18:07:48.196 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4588667.682015701%2C4534856.014102939%2C7318386.836135917%2C3172442.4219479547&size=1116%2C557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Arv&f=image - null null
2025-06-08 18:07:48.202 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-08 18:07:48.205 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-08 18:07:48.232 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-08 18:07:48.238 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.RedirectToActionResult in 2.0116ms.
2025-06-08 18:07:48.243 +03:30 [INF] Executing RedirectResult, redirecting to /rest/export?bbox=4588667.682015701,4534856.014102939,7318386.836135917,3172442.4219479547&size=1116,557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Arv&f=image.
2025-06-08 18:07:48.245 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 37.7876ms
2025-06-08 18:07:48.246 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-08 18:07:48.248 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4588667.682015701%2C4534856.014102939%2C7318386.836135917%2C3172442.4219479547&size=1116%2C557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Arv&f=image - 302 0 null 52.1069ms
2025-06-08 18:07:48.255 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/export?bbox=4588667.682015701,4534856.014102939,7318386.836135917,3172442.4219479547&size=1116,557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Arv&f=image - null null
2025-06-08 18:07:48.258 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web)'
2025-06-08 18:07:48.262 +03:30 [INF] Route matched with {action = "ExportMapImage", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ExportMapImage(System.String, System.String, System.String, System.String, System.String, System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-08 18:07:48.283 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web) - Validation state: "Valid"
2025-06-08 18:07:48.324 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals2_identifier_0='?' (Size = 3)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[ShortName] = @__8__locals2_identifier_0
2025-06-08 18:07:48.343 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-08 18:07:48.361 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-08 18:07:52.398 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 4111.2731ms.
2025-06-08 18:07:52.403 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-08 18:07:52.416 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web) in 4148.8357ms
2025-06-08 18:07:52.419 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web)'
2025-06-08 18:07:52.421 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/export?bbox=4588667.682015701,4534856.014102939,7318386.836135917,3172442.4219479547&size=1116,557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Arv&f=image - 200 180289 image/png 4166.1972ms
2025-06-08 18:07:55.671 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4588667.682015701%2C4534856.014102939%2C7318386.836135917%2C3172442.4219479547&size=1116%2C557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Avl%2Crv&f=image - null null
2025-06-08 18:07:55.678 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-08 18:07:55.680 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-08 18:07:55.708 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-08 18:07:55.711 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.RedirectToActionResult in 0.1615ms.
2025-06-08 18:07:55.713 +03:30 [INF] Executing RedirectResult, redirecting to /rest/export?bbox=4588667.682015701,4534856.014102939,7318386.836135917,3172442.4219479547&size=1116,557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Avl,rv&f=image.
2025-06-08 18:07:55.714 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 30.312ms
2025-06-08 18:07:55.715 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-08 18:07:55.716 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4588667.682015701%2C4534856.014102939%2C7318386.836135917%2C3172442.4219479547&size=1116%2C557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Avl%2Crv&f=image - 302 0 null 44.7729ms
2025-06-08 18:07:55.724 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/export?bbox=4588667.682015701,4534856.014102939,7318386.836135917,3172442.4219479547&size=1116,557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Avl,rv&f=image - null null
2025-06-08 18:07:55.728 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web)'
2025-06-08 18:07:55.729 +03:30 [INF] Route matched with {action = "ExportMapImage", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ExportMapImage(System.String, System.String, System.String, System.String, System.String, System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-08 18:07:55.747 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web) - Validation state: "Valid"
2025-06-08 18:07:55.753 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_identifier_0='?' (Size = 3)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[ShortName] = @__8__locals2_identifier_0
2025-06-08 18:07:55.757 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-08 18:07:55.774 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-08 18:07:56.730 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_identifier_0='?' (Size = 3)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[ShortName] = @__8__locals2_identifier_0
2025-06-08 18:07:56.736 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-08 18:07:56.739 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-08 18:08:01.367 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 5615.6543ms.
2025-06-08 18:08:01.371 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-08 18:08:01.378 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web) in 5646.8764ms
2025-06-08 18:08:01.381 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web)'
2025-06-08 18:08:01.383 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/export?bbox=4588667.682015701,4534856.014102939,7318386.836135917,3172442.4219479547&size=1116,557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Avl,rv&f=image - 200 784520 image/png 5658.2777ms
2025-06-08 18:08:03.007 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4588667.682015701%2C4534856.014102939%2C7318386.836135917%2C3172442.4219479547&size=1116%2C557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Arg%2Cvl%2Crv&f=image - null null
2025-06-08 18:08:03.011 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-08 18:08:03.012 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-08 18:08:03.039 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-08 18:08:03.041 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.RedirectToActionResult in 0.047ms.
2025-06-08 18:08:03.044 +03:30 [INF] Executing RedirectResult, redirecting to /rest/export?bbox=4588667.682015701,4534856.014102939,7318386.836135917,3172442.4219479547&size=1116,557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Arg,vl,rv&f=image.
2025-06-08 18:08:03.046 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 29.493ms
2025-06-08 18:08:03.047 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-08 18:08:03.049 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4588667.682015701%2C4534856.014102939%2C7318386.836135917%2C3172442.4219479547&size=1116%2C557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Arg%2Cvl%2Crv&f=image - 302 0 null 42.1528ms
2025-06-08 18:08:03.056 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/export?bbox=4588667.682015701,4534856.014102939,7318386.836135917,3172442.4219479547&size=1116,557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Arg,vl,rv&f=image - null null
2025-06-08 18:08:03.060 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web)'
2025-06-08 18:08:03.062 +03:30 [INF] Route matched with {action = "ExportMapImage", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ExportMapImage(System.String, System.String, System.String, System.String, System.String, System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-08 18:08:03.084 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web) - Validation state: "Valid"
2025-06-08 18:08:03.088 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__8__locals2_identifier_0='?' (Size = 3)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[ShortName] = @__8__locals2_identifier_0
2025-06-08 18:08:03.093 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-08 18:08:03.097 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-08 18:08:03.796 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4588667.682015701%2C4534856.014102939%2C7318386.836135917%2C3172442.4219479547&size=1116%2C557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Arg%2Crv&f=image - null null
2025-06-08 18:08:03.801 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-08 18:08:03.803 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-08 18:08:03.826 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-08 18:08:03.829 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.RedirectToActionResult in 0.0545ms.
2025-06-08 18:08:03.832 +03:30 [INF] Executing RedirectResult, redirecting to /rest/export?bbox=4588667.682015701,4534856.014102939,7318386.836135917,3172442.4219479547&size=1116,557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Arg,rv&f=image.
2025-06-08 18:08:03.834 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 27.9574ms
2025-06-08 18:08:03.836 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-08 18:08:03.837 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4588667.682015701%2C4534856.014102939%2C7318386.836135917%2C3172442.4219479547&size=1116%2C557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Arg%2Crv&f=image - 302 0 null 41.1519ms
2025-06-08 18:08:03.843 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/export?bbox=4588667.682015701,4534856.014102939,7318386.836135917,3172442.4219479547&size=1116,557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Arg,rv&f=image - null null
2025-06-08 18:08:03.848 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web)'
2025-06-08 18:08:03.849 +03:30 [INF] Route matched with {action = "ExportMapImage", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ExportMapImage(System.String, System.String, System.String, System.String, System.String, System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-08 18:08:03.894 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web) - Validation state: "Valid"
2025-06-08 18:08:03.913 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_identifier_0='?' (Size = 3)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[ShortName] = @__8__locals2_identifier_0
2025-06-08 18:08:03.920 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-08 18:08:03.924 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-08 18:08:05.573 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_identifier_0='?' (Size = 3)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[ShortName] = @__8__locals2_identifier_0
2025-06-08 18:08:05.582 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-08 18:08:05.587 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-08 18:08:06.399 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_identifier_0='?' (Size = 3)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[ShortName] = @__8__locals2_identifier_0
2025-06-08 18:08:06.417 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-08 18:08:06.419 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__8__locals2_identifier_0='?' (Size = 3)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[ShortName] = @__8__locals2_identifier_0
2025-06-08 18:08:06.421 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-08 18:08:06.424 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-08 18:08:06.430 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-08 18:08:09.932 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 6034.3736ms.
2025-06-08 18:08:09.936 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-08 18:08:09.940 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web) in 6085.3408ms
2025-06-08 18:08:09.942 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web)'
2025-06-08 18:08:09.945 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/export?bbox=4588667.682015701,4534856.014102939,7318386.836135917,3172442.4219479547&size=1116,557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Arg,rv&f=image - 200 497351 image/png 6101.7681ms
2025-06-08 18:08:13.232 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 10144.7201ms.
2025-06-08 18:08:13.235 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-08 18:08:13.243 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web) in 10177.0767ms
2025-06-08 18:08:13.247 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web)'
2025-06-08 18:08:13.250 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/export?bbox=4588667.682015701,4534856.014102939,7318386.836135917,3172442.4219479547&size=1116,557&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3Arg,vl,rv&f=image - 200 850313 image/png 10193.8346ms
