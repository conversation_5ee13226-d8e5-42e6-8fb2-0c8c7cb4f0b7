﻿namespace BaseGIS.Web.Helper
{
    public static class ExtensionMethods
    {
        public static string ToArabicSQL(this string statement)
        {
            if (!string.IsNullOrEmpty(statement))
            {
                statement = statement.Replace("ی", "ي");
                statement = statement.Replace("ی", "[یي]");
                statement = statement.Replace("ک", "ك");
                statement = statement.Replace("ک", "[کك]");
            }
            return statement;
        }

        public static string ToPersianSQL(this string statement)
        {
            statement = statement.Replace("ي", "ی");
            statement = statement.Replace("[یي]", "ی");
            statement = statement.Replace("ك", "ک");
            statement = statement.Replace("[کك]", "ک");
            return statement;
        }

    }
}
