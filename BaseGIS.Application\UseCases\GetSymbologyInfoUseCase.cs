using BaseGIS.Core.Entities;
using BaseGIS.Core.Interfaces;
using BaseGIS.Application.DTOs;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BaseGIS.Application.UseCases
{
    public class GetSymbologyInfoUseCase
    {
        private readonly ISymbologyInfoRepository _symbologyInfoRepository;

        public GetSymbologyInfoUseCase(ISymbologyInfoRepository symbologyInfoRepository)
        {
            _symbologyInfoRepository = symbologyInfoRepository;
        }

        public async Task<List<SymbologyInfoDto>> GetByTableInfoIdAsync(int tableInfoId)
        {
            var symbologyInfos = await _symbologyInfoRepository.GetByTableInfoIdAsync(tableInfoId);
            return symbologyInfos.Select(s => new SymbologyInfoDto
            {
                Id = s.Id,
                Type = s.Type.ToString(),
                TableInfoId = s.TableInfoId,
                Name = s.Name,
                IsDefault = s.IsDefault,
                <PERSON>son = s.<PERSON>son,
                <PERSON><PERSON><PERSON><PERSON> = s.<PERSON>,
                FieldName = s.FieldName
            }).ToList();
        }
    }
}