using BaseGIS.Core.Entities;

namespace BaseGIS.Web.Services
{
    /// <summary>
    /// سرویس مدیریت نقشه جغرافیایی
    /// </summary>
    public interface IGeoMapService
    {
        /// <summary>
        /// دریافت ساختار درختی لایه‌ها
        /// </summary>
        Task<ServiceResult<LayerTreeViewModel>> GetLayerTreeAsync(string layerDefs, string userId);

        /// <summary>
        /// دریافت اطلاعات لایه
        /// </summary>
        Task<ServiceResult<LayerInfoViewModel>> GetLayerInfoAsync(string layerId, string userId);

        /// <summary>
        /// دریافت فیلدهای لایه
        /// </summary>
        Task<ServiceResult<IEnumerable<LayerFieldViewModel>>> GetLayerFieldsAsync(string layerId, string userId);

        /// <summary>
        /// جستجو در جداول
        /// </summary>
        Task<ServiceResult<IEnumerable<TableSearchResultViewModel>>> SearchTablesAsync(string searchTerm, string userId);

        /// <summary>
        /// دریافت تنظیمات نمادشناسی
        /// </summary>
        Task<ServiceResult<IEnumerable<SymbologyViewModel>>> GetSymbologiesAsync(int tableId, string userId);

        /// <summary>
        /// جستجوی سریع
        /// </summary>
        Task<ServiceResult<IEnumerable<QuickSearchResultViewModel>>> QuickSearchAsync(QuickSearchRequest request, string userId);

        /// <summary>
        /// جستجوی مکانی
        /// </summary>
        Task<ServiceResult<SpatialSearchResultViewModel>> SpatialSearchAsync(SpatialSearchRequest request, string userId);

        /// <summary>
        /// جستجوی توصیفی
        /// </summary>
        Task<ServiceResult<DescriptiveSearchResultViewModel>> DescriptiveSearchAsync(DescriptiveSearchRequest request, string userId);

        /// <summary>
        /// تولید گزارش آماری
        /// </summary>
        Task<ServiceResult<StatisticalReportViewModel>> GenerateStatisticalReportAsync(StatisticalReportRequest request, string userId);

        /// <summary>
        /// ایمپورت فایل Shapefile
        /// </summary>
        Task<ServiceResult<ImportResultViewModel>> ImportShapefileAsync(ImportShapefileRequest request, string userId);

        /// <summary>
        /// اعتبارسنجی دسترسی به لایه
        /// </summary>
        Task<bool> ValidateLayerAccessAsync(string layerId, string userId);

        /// <summary>
        /// دریافت تنظیمات نقشه کاربر
        /// </summary>
        Task<UserMapSettingsViewModel?> GetUserMapSettingsAsync(string userId);

        /// <summary>
        /// ذخیره تنظیمات نقشه کاربر
        /// </summary>
        Task<ServiceResult> SaveUserMapSettingsAsync(UserMapSettingsViewModel settings, string userId);
    }

    /// <summary>
    /// مدل ساختار درختی لایه‌ها
    /// </summary>
    public class LayerTreeViewModel
    {
        public string Html { get; set; } = string.Empty;
        public string LayerDefs { get; set; } = string.Empty;
        public List<LayerGroupViewModel> Groups { get; set; } = new();
    }

    /// <summary>
    /// مدل گروه لایه
    /// </summary>
    public class LayerGroupViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string AliasName { get; set; } = string.Empty;
        public bool IsExpanded { get; set; } = true;
        public List<LayerItemViewModel> Layers { get; set; } = new();
    }

    /// <summary>
    /// مدل آیتم لایه
    /// </summary>
    public class LayerItemViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string AliasName { get; set; } = string.Empty;
        public string ShortName { get; set; } = string.Empty;
        public string DatasetType { get; set; } = string.Empty;
        public bool IsVisible { get; set; }
        public bool IsEditable { get; set; }
        public string? SymbolId { get; set; }
        public string? LabelField { get; set; }
        public string? QueryDef { get; set; }
        public int TableId { get; set; }
    }

    /// <summary>
    /// مدل اطلاعات لایه
    /// </summary>
    public class LayerInfoViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string AliasName { get; set; } = string.Empty;
        public string DatasetType { get; set; } = string.Empty;
        public string GeometryType { get; set; } = string.Empty;
        public int FeatureCount { get; set; }
        public BoundingBoxViewModel? Extent { get; set; }
        public List<SymbologyViewModel> Symbologies { get; set; } = new();
    }

    /// <summary>
    /// مدل فیلد لایه
    /// </summary>
    public class LayerFieldViewModel
    {
        public string Name { get; set; } = string.Empty;
        public string AliasName { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public int Length { get; set; }
        public bool IsDisplay { get; set; }
        public bool IsSearchable { get; set; }
        public int FieldIndex { get; set; }
    }

    /// <summary>
    /// مدل نتیجه جستجوی جدول
    /// </summary>
    public class TableSearchResultViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
        public string Company { get; set; } = string.Empty;
        public string DatasetType { get; set; } = string.Empty;
    }

    /// <summary>
    /// مدل نمادشناسی
    /// </summary>
    public class SymbologyViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsDefault { get; set; }
        public string SymbolType { get; set; } = string.Empty;
        public string StyleData { get; set; } = string.Empty;
    }

    /// <summary>
    /// مدل محدوده جغرافیایی
    /// </summary>
    public class BoundingBoxViewModel
    {
        public double MinX { get; set; }
        public double MinY { get; set; }
        public double MaxX { get; set; }
        public double MaxY { get; set; }
    }

    /// <summary>
    /// درخواست جستجوی سریع
    /// </summary>
    public class QuickSearchRequest
    {
        public string SearchTerm { get; set; } = string.Empty;
        public string? LayerId { get; set; }
        public string? FieldName { get; set; }
        public int MaxResults { get; set; } = 50;
        public bool ExactMatch { get; set; } = false;
    }

    /// <summary>
    /// نتیجه جستجوی سریع
    /// </summary>
    public class QuickSearchResultViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string DisplayText { get; set; } = string.Empty;
        public string LayerName { get; set; } = string.Empty;
        public Dictionary<string, object> Attributes { get; set; } = new();
        public GeometryViewModel? Geometry { get; set; }
    }

    /// <summary>
    /// درخواست جستجوی مکانی
    /// </summary>
    public class SpatialSearchRequest
    {
        public GeometryViewModel SearchGeometry { get; set; } = new();
        public string SpatialRelation { get; set; } = "intersects"; // intersects, contains, within, etc.
        public List<string> LayerIds { get; set; } = new();
        public double BufferDistance { get; set; } = 0;
        public int MaxResults { get; set; } = 100;
    }

    /// <summary>
    /// نتیجه جستجوی مکانی
    /// </summary>
    public class SpatialSearchResultViewModel
    {
        public List<SpatialFeatureViewModel> Features { get; set; } = new();
        public int TotalCount { get; set; }
        public BoundingBoxViewModel? ResultExtent { get; set; }
    }

    /// <summary>
    /// درخواست جستجوی توصیفی
    /// </summary>
    public class DescriptiveSearchRequest
    {
        public string LayerId { get; set; } = string.Empty;
        public List<SearchCriteriaViewModel> Criteria { get; set; } = new();
        public string LogicalOperator { get; set; } = "AND"; // AND, OR
        public int MaxResults { get; set; } = 100;
        public bool ReturnGeometry { get; set; } = true;
    }

    /// <summary>
    /// نتیجه جستجوی توصیفی
    /// </summary>
    public class DescriptiveSearchResultViewModel
    {
        public List<DescriptiveFeatureViewModel> Features { get; set; } = new();
        public int TotalCount { get; set; }
        public List<LayerFieldViewModel> Fields { get; set; } = new();
    }

    /// <summary>
    /// معیار جستجو
    /// </summary>
    public class SearchCriteriaViewModel
    {
        public string FieldName { get; set; } = string.Empty;
        public string Operator { get; set; } = string.Empty; // =, !=, >, <, >=, <=, LIKE, IN
        public string Value { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
    }

    /// <summary>
    /// درخواست گزارش آماری
    /// </summary>
    public class StatisticalReportRequest
    {
        public string LayerId { get; set; } = string.Empty;
        public List<string> GroupByFields { get; set; } = new();
        public List<StatisticalFieldViewModel> StatisticalFields { get; set; } = new();
        public string? WhereClause { get; set; }
    }

    /// <summary>
    /// نتیجه گزارش آماری
    /// </summary>
    public class StatisticalReportViewModel
    {
        public List<StatisticalRecordViewModel> Records { get; set; } = new();
        public List<ChartDataViewModel> Charts { get; set; } = new();
        public StatisticalSummaryViewModel Summary { get; set; } = new();
    }

    /// <summary>
    /// فیلد آماری
    /// </summary>
    public class StatisticalFieldViewModel
    {
        public string FieldName { get; set; } = string.Empty;
        public string StatisticType { get; set; } = string.Empty; // SUM, AVG, COUNT, MIN, MAX
        public string Alias { get; set; } = string.Empty;
    }

    /// <summary>
    /// رکورد آماری
    /// </summary>
    public class StatisticalRecordViewModel
    {
        public Dictionary<string, object> GroupValues { get; set; } = new();
        public Dictionary<string, object> StatisticValues { get; set; } = new();
    }

    /// <summary>
    /// خلاصه آماری
    /// </summary>
    public class StatisticalSummaryViewModel
    {
        public int TotalRecords { get; set; }
        public int GroupCount { get; set; }
        public Dictionary<string, object> OverallStatistics { get; set; } = new();
    }

    /// <summary>
    /// درخواست ایمپورت Shapefile
    /// </summary>
    public class ImportShapefileRequest
    {
        public IFormFile ShapeFile { get; set; } = null!;
        public IFormFile? DbfFile { get; set; }
        public IFormFile? ShxFile { get; set; }
        public IFormFile? PrjFile { get; set; }
        public string TargetTableName { get; set; } = string.Empty;
        public string? TargetSchema { get; set; }
        public bool OverwriteExisting { get; set; } = false;
        public int? TargetSRID { get; set; }
    }

    /// <summary>
    /// نتیجه ایمپورت
    /// </summary>
    public class ImportResultViewModel
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int ImportedFeatures { get; set; }
        public string? NewLayerId { get; set; }
        public List<string> Warnings { get; set; } = new();
        public List<string> Errors { get; set; } = new();
    }

    /// <summary>
    /// مدل هندسه
    /// </summary>
    public class GeometryViewModel
    {
        public string Type { get; set; } = string.Empty; // Point, LineString, Polygon, etc.
        public object Coordinates { get; set; } = new();
        public int? SRID { get; set; }
        public string? WKT { get; set; }
    }

    /// <summary>
    /// مدل فیچر مکانی
    /// </summary>
    public class SpatialFeatureViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string LayerName { get; set; } = string.Empty;
        public Dictionary<string, object> Attributes { get; set; } = new();
        public GeometryViewModel? Geometry { get; set; }
    }

    /// <summary>
    /// مدل فیچر توصیفی
    /// </summary>
    public class DescriptiveFeatureViewModel
    {
        public string Id { get; set; } = string.Empty;
        public Dictionary<string, object> Attributes { get; set; } = new();
        public GeometryViewModel? Geometry { get; set; }
    }

    /// <summary>
    /// تنظیمات نقشه کاربر
    /// </summary>
    public class UserMapSettingsViewModel
    {
        public string UserId { get; set; } = string.Empty;
        public string DefaultExtent { get; set; } = string.Empty;
        public string DefaultBasemap { get; set; } = string.Empty;
        public List<string> FavoriteLayers { get; set; } = new();
        public Dictionary<string, object> CustomSettings { get; set; } = new();
        public DateTime LastModified { get; set; }
    }
}
