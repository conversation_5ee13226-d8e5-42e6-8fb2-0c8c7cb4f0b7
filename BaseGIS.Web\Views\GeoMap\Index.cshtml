@using System.Text.Json
@using BaseGIS.Core.Entities
@using BaseGIS.Web.ViewModels
@model IEnumerable<BaseGIS.Core.Entities.GroupInfo>
@{
    ViewData["Title"] = "نقشه";
    Layout = "~/Views/Shared/_LayoutMap.cshtml";

    string treeData = ViewBag.TreeData;
    List<BaseMapViewModel> ListBaseMap = ViewBag.ListBaseMap ?? new List<BaseMapViewModel>();
    int BaseMapID = ViewBag.BaseMapID;
    var Theme = ViewBag.Theme;
    BaseMapConfigViewModel BaseMapConfig = ViewBag.BaseMapConfig ?? new BaseMapConfigViewModel();
    Dictionary<string, string> Themes = new Dictionary<string, string>();  

    if (Theme != null && Theme != "")
    {
        var tms = Theme.Split(',');
        for (int i = 0; i < tms.Length; i++)
        {
            var tms1 = tms[i].Split(':');
            if (tms1.Length > 1)
            {
                // Store the HTML color string directly
                Themes.Add(tms1[0].ToLower(), tms1[1]);
            }
        }
    }
}

@section Styles {
    <!-- ===== کتابخانه‌های CSS از LibMan ===== -->
    <link href="~/lib/jqueryui/themes/base/jquery-ui.min.css" rel="stylesheet" />
    <link href="~/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css" rel="stylesheet" />
    <link href="~/lib/ion-rangeslider/css/ion.rangeslider.min.css" rel="stylesheet" />
    <link href="~/lib/leaflet-dialog/Leaflet.Dialog.css" rel="stylesheet" />
    <link href="~/lib/leaflet-measure/leaflet-measure.css" rel="stylesheet" />
    <link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
    <link href="~/lib/sweetalert2/sweetalert2.min.css" rel="stylesheet" />

    <!-- ===== استایل‌های کامپوننت‌ها ===== -->
    <link href="~/css/components.css" rel="stylesheet" />
    <link href="~/css/components/measurement-tools.css" rel="stylesheet" />
    <link href="~/css/components/spatial-analysis.css" rel="stylesheet" />
    <link href="~/css/components/goto-xy.css" rel="stylesheet" />
    <link href="~/css/components/drawing-tools.css" rel="stylesheet" />
    <link href="~/css/components/property-tools.css" rel="stylesheet" />

    <style>
        /* نوار ابزار بالا سمت راست */
        .toolbar-container {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            padding: 8px;
        }

        .toolbar-menu {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .toolbar-btn {
            font-size: 12px;
            padding: 6px 12px;
            border-radius: 4px;
            white-space: nowrap;
            min-width: 100px;
        }

        .toolbar-btn i {
            margin-left: 5px;
        }

        /* صفحه ابزار تمام صفحه */
        .tool-page-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            display: flex;
            flex-direction: column;
        }

        .tool-page-header {
            background: #343a40;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #495057;
        }

        .tool-page-header h4 {
            margin: 0;
            font-size: 18px;
        }

        .tool-page-content {
            flex: 1;
            background: white;
            overflow: auto;
            padding: 0;
        }

        /* Responsive */
        @@media (max-width: 768px) {
            .toolbar-container {
                top: 5px;
                right: 5px;
                padding: 5px;
            }

            .toolbar-menu {
                flex-direction: column;
                gap: 3px;
            }

            .toolbar-btn {
                font-size: 11px;
                padding: 5px 8px;
                min-width: 80px;
            }
        }

        /* منوی dropdown */
        .dropdown-menu {
            direction: rtl;
            text-align: right;
        }

        .dropdown-item {
            padding: 8px 15px;
            font-size: 13px;
        }

        .dropdown-item i {
            margin-left: 8px;
            width: 16px;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }
    </style>
    <style>

        .map-container {
            display: flex;
            height: calc(100vh - 62px);
            direction: rtl;
        }

        #sidebar {
            width: 250px;
            height: 100%;
            transition: width 0.35s ease;
            overflow: hidden;
            background: #fff;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--primary-color);
        }

            #sidebar.hidden {
                width: 0;
                display: block;
            }

        .sidebar-wrapper {
            width: 100%;
            height: 100%;
        }

        .active-layer-container {
            padding: 6px;
            border-bottom: 1px solid #ddd;
            background: #f8f9fa;
        }

        #activeLayerInput {
            background-color: #fff;
            cursor: default;
        }

        #tree {
            height: 100%;
            max-height: calc(100vh - 200px); /* تنظیم ارتفاع با توجه به Textbox و Tab */
            overflow-y: auto;
            padding: 6px;
            background-color: var(--background-color);
        }

        /* ========================================
           استایل‌های Tab Control
        ======================================== */
        .tab-container {
            height: calc(100vh - 120px);
            display: flex;
            flex-direction: column;
        }

        .tab-container .nav-tabs {
            border-bottom: 1px solid #dee2e6;
            background: #f8f9fa;
            margin: 0;
            padding: 0 6px;
            flex-shrink: 0;
        }

        .tab-container .nav-tabs .nav-link {
            border: none;
            border-radius: 0;
            padding: 8px 12px;
            font-size: 12px;
            color: #6c757d;
            background: transparent;
        }

        .tab-container .nav-tabs .nav-link:hover {
            border-color: transparent;
            background: rgba(0,123,255,0.1);
            color: #007bff;
        }

        .tab-container .nav-tabs .nav-link.active {
            color: #007bff;
            background: #fff;
            border-bottom: 2px solid #007bff;
            font-weight: 500;
        }

        .tab-container .tab-content {
            flex: 1;
            overflow: hidden;
            background: #fff;
        }

        .tab-container .tab-pane {
            height: 100%;
            overflow-y: auto;
        }

        /* ========================================
           استایل‌های نقشه‌های ذخیره شده
        ======================================== */
        .views-toolbar {
            padding: 8px;
            border-bottom: 1px solid #dee2e6;
            background: #f8f9fa;
            display: flex;
            gap: 5px;
        }

        .views-toolbar .btn {
            font-size: 11px;
            padding: 4px 8px;
        }

        .views-list {
            padding: 8px;
            height: calc(100% - 50px);
            overflow-y: auto;
        }

        .view-item {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            margin-bottom: 8px;
            padding: 8px;
            background: #fff;
            cursor: pointer;
            transition: all 0.2s;
        }

        .view-item:hover {
            border-color: #007bff;
            box-shadow: 0 2px 4px rgba(0,123,255,0.1);
        }

        .view-item.selected {
            border-color: #007bff;
            background: #f0f8ff;
        }

        .view-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .view-item-title {
            font-weight: 500;
            font-size: 13px;
            color: #333;
            margin: 0;
        }

        .view-item-actions {
            display: flex;
            gap: 3px;
        }

        .view-item-actions .btn {
            padding: 2px 6px;
            font-size: 10px;
            line-height: 1;
        }

        .view-item-meta {
            font-size: 11px;
            color: #6c757d;
            margin-bottom: 4px;
        }

        .view-item-description {
            font-size: 11px;
            color: #666;
            max-height: 30px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .loading-placeholder {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            font-size: 12px;
        }

        .no-views-message {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            font-size: 12px;
        }

        .view-group {
            margin-bottom: 15px;
        }

        .view-group-title {
            font-size: 12px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            padding: 4px 8px;
            background: #e9ecef;
            border-radius: 3px;
            border-left: 3px solid #007bff;
        }

        #map {
            width: calc(100% - 250px);
            height: 100%;
            position: relative;
            order: 1;
        }

            #map.full-width {
                width: 100%;
            }

        /* دکمه سایدبار حذف شد - حالا از header استفاده می‌شود */

        /* استایل‌های نوار ابزار */
        #standardToolbar {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

       /*  #standardToolbar .btn {
            width: 40px;
            height: 40px;
            border: 1px solid #ccc;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        #standardToolbar .btn:hover {
            background: rgba(0, 123, 255, 0.1);
            border-color: #007bff;
            color: #007bff;
        } */

        /* استایل dropdown ها */
        .dropdown-menu {
            display: none !important;
            position: absolute !important;
            left: 45px !important;
            top: 0 !important;
            background: rgba(255, 255, 255, 0.95) !important;
            border: 1px solid #ccc !important;
            border-radius: 4px !important;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
            padding: 4px !important;
            z-index: 1000 !important;
        }

        .dropdown-menu.show {
            display: flex !important;
            flex-direction: row !important;
            gap: 2px !important;
        }

        .dropdown-menu .btn {
            width: 36px !important;
            height: 36px !important;
            font-size: 14px !important;
            margin: 0 !important;
        }

        /* استایل‌های دیالوگ‌ها */
        .measurement-dialog, .gotoxy-dialog {
            font-family: 'Vazir', sans-serif;
            direction: rtl;
            text-align: right;
        }

        .measurement-dialog .btn-group .btn,
        .gotoxy-dialog .btn-group .btn {
            border-radius: 0;
        }

        .measurement-dialog .btn-group .btn:first-child,
        .gotoxy-dialog .btn-group .btn:first-child {
            border-top-right-radius: 0.375rem;
            border-bottom-right-radius: 0.375rem;
        }

        .measurement-dialog .btn-group .btn:last-child,
        .gotoxy-dialog .btn-group .btn:last-child {
            border-top-left-radius: 0.375rem;
            border-bottom-left-radius: 0.375rem;
        }

        .measurement-marker, .gotoxy-marker {
            background: transparent;
            border: none;
        }

        /* استایل نشانگرهای اندازه‌گیری */
        .leaflet-div-icon.measurement-marker,
        .leaflet-div-icon.gotoxy-marker {
            background: transparent !important;
            border: none !important;
        }

        /* استایل دیالوگ Leaflet */
        .leaflet-control-dialog {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            overflow: hidden;
        }

        .leaflet-control-dialog .leaflet-control-dialog-contents {
            padding: 15px;
            background: white;
        }

        .leaflet-control-dialog .leaflet-control-dialog-grabber {
            background: #007bff;
            height: 30px;
            cursor: move;
        }

        .leaflet-control-dialog .leaflet-control-dialog-close {
            background: #dc3545;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            font-size: 16px;
            cursor: pointer;
        }

        .leaflet-control-dialog .leaflet-control-dialog-close:hover {
            background: #c82333;
        }


        span.fancytree-node.custom > span.fancytree-title {
            color: maroon;
            font-family: "SamanFont";
        }

        span.fancytree-node.plan > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/Gallery16.png");
        }

        span.fancytree-node.group > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/group.png");
        }

        span.fancytree-node.polyline > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/polyline.png");
        }

        span.fancytree-node.point > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/point.png");
        }

        span.fancytree-node.polygon > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/polygon.png");
        }
         
        .fancytree-container,
        span.fancytree-focused span.fancytree-title {
            outline: 0 !important;
        }
         
        @@media (max-width: 768px) {
            #sidebar {
                width: 200px;
            }

            #map {
                width: calc(100% - 200px);
            }

            #sidebar.hidden + #map {
                width: 100%;
            }
        }
    </style>
    <style>

        .btnView {
            text-decoration: none;
            width: 44px !important;
            height: 40px;
        @if (Themes.Keys.Contains("view_backcolor"))
        {
            var color = Themes["view_backcolor"];
            <text> background-color: rgba(255,255,255,.5);
                border: 1px solid @(color);
            </text>
        }
        else
        {
            <text> background-color: rgba(0,176,240,.5);
                border: 1px solid rgb(0,176,240);
            </text>
        }
        @if (Themes.Keys.Contains("view_color"))
        {
            var color = Themes["view_color"];
            <text> color: @(color);
            </text>
        }
        else
        {
            <text> color: white;
            </text>
        }
        }


            .btnView:hover {
                color: #ce2a21;
                border: 1px solid;
        @if (Themes.Keys.Contains("view_backcolor"))
        {
            var color = Themes["view_backcolor"];
            <text> 
            box-shadow: inset 0 0 20px @(color) 80, 0 0 20px @(color) 33;
            text-shadow: 1px 1px 2px @(color);
            </text>
        }
        else
        {
            <text> 
            box-shadow: inset 0 0 20px rgba(0,176,240, .5), 0 0 20px rgba(0,176,240, .2);
            text-shadow: 1px 1px 2px rgb(0,176,240);
            </text>
        } outline-color: rgb(0,176,240);
                outline-offset: 15px;
                z-index: 999;
                transform: scale(1.3,1.3);
                -webkit-transform: scale(1.3,1.3);
                -moz-transform: scale(1.3,1.3);
            }


        .btnEdit {
        @if (Themes.Keys.Contains("edit_backcolor"))
        {
            var color = Themes["edit_backcolor"];
            <text> background-color: @(color) 80;
            </text>
        }
        @if (Themes.Keys.Contains("edit_color"))
        {
            var color = Themes["edit_color"];
            <text> color: @(color);
            </text>
        }
        else
        {
            <text> color:red;
            </text>
        }
            /*background-color: rgba(240,176,0,.5); */
        }

            .btnEdit:hover {
                /* border: 1px solid;
                        box-shadow: inset 0 0 20px rgba(240,176,0, .5), 0 0 20px rgba(240,176,0, .2);
                        outline-color: rgba(240,176,0, 1);
                        outline-offset: 15px;
                        text-shadow: 1px 1px 2px rgb(240,176,0);
                           */
            }

        @@keyframes fadeblinking {
            from {
                opacity: 0.1;
            }
        }

        .blinking {
            animation: fadeblinking 0.3s infinite alternate;
        }
    </style>
}

<div class="map-container">
    <!-- سایدبار -->
    <div id="sidebar">
        <div class="sidebar-wrapper">
            <div class="active-layer-container">
                <input type="text" id="activeLayerLable" class="form-control" value="" placeholder="لایه فعال">
                <input type="text" id="activeLayer" class="d-none">
            </div>

            <!-- Tab Control -->
            <div class="tab-container">
                <ul class="nav nav-tabs" id="sidebarTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="layers-tab" data-bs-toggle="tab" data-bs-target="#layers-panel"
                                type="button" role="tab" aria-controls="layers-panel" aria-selected="true">
                            <i class="fa fa-layer-group"></i> لایه‌ها
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="views-tab" data-bs-toggle="tab" data-bs-target="#views-panel"
                                type="button" role="tab" aria-controls="views-panel" aria-selected="false">
                            <i class="fa fa-map"></i> نقشه‌ها
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="sidebarTabContent">
                    <!-- تب لایه‌ها -->
                    <div class="tab-pane fade show active" id="layers-panel" role="tabpanel" aria-labelledby="layers-tab">
                        <!-- کامپوننت درخت لایه‌ها -->
                        <div id="layer-tree-container"
                             data-component="layer-tree"
                             data-layer-groups='@Html.Raw(Json.Serialize(ViewBag.LayerGroups ?? new List<object>()))'
                             data-show-checkboxes="true"
                             data-show-context-menu="true">
                            <!-- Fallback HTML for non-JS users -->
                            <div id="tree">
                                <ul id="treeData" style="display:none;">
                                    @Html.Raw(treeData)
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- تب نقشه‌های ذخیره شده -->
                    <div class="tab-pane fade" id="views-panel" role="tabpanel" aria-labelledby="views-tab">
                        <div id="map-views-container">
                            <!-- نوار ابزار نقشه‌ها -->
                            <div class="views-toolbar">
                                <button type="button" class="btn btn-primary btn-sm" onclick="showSaveMapViewDialog()">
                                    <i class="fa fa-plus"></i> ذخیره نقشه جدید
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm" onclick="refreshMapViews()">
                                    <i class="fa fa-refresh"></i> بروزرسانی
                                </button>
                            </div>

                            <!-- لیست نقشه‌های ذخیره شده -->
                            <div id="map-views-list" class="views-list">
                                <div class="loading-placeholder">
                                    <i class="fa fa-spinner fa-spin"></i> در حال بارگذاری...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- نقشه -->
    <div id="map" class="map">
        <div style="position:absolute; left:50%; bottom:0px;z-index: 1001;">
            <div style="text-align:center">
                <span class="" id="scaleText" style="color: white;  text-shadow: 1px 1px 2px black, 0 0 25px blue, 0 0 5px darkblue;"></span>
                <br /> <span class="" id="positionText" style="color: white;  text-shadow: 1px 1px 2px black, 0 0 25px blue, 0 0 5px darkblue;"></span>
                <br /> <span class="" id="positionUTMText" style="color: white;  text-shadow: 1px 1px 2px black, 0 0 25px blue, 0 0 5px darkblue;"></span>
            </div>
        </div>
    </div>

    <!-- نوار ابزار بالا سمت راست -->
    <div id="toolbar" class="toolbar-container">
        <div class="toolbar-menu">
            <!-- منوی جستجو -->
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle toolbar-btn" type="button" id="searchMenu" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-search"></i> جستجو
                </button>
                <ul class="dropdown-menu" aria-labelledby="searchMenu">
                    <li><a class="dropdown-item" href="#" onclick="openToolPage('quick-search')">
                        <i class="fa fa-bolt"></i> جستجوی سریع
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="openToolPage('spatial-search')">
                        <i class="fa fa-map-marker"></i> جستجوی مکانی
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="openToolPage('descriptive-search')">
                        <i class="fa fa-list-alt"></i> جستجوی توصیفی
                    </a></li>
                </ul>
            </div>

            <!-- منوی گزارش -->
            <div class="dropdown">
                <button class="btn btn-success dropdown-toggle toolbar-btn" type="button" id="reportMenu" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-chart-bar"></i> گزارش
                </button>
                <ul class="dropdown-menu" aria-labelledby="reportMenu">
                    <li><a class="dropdown-item" href="#" onclick="openToolPage('statistical-report')">
                        <i class="fa fa-pie-chart"></i> گزارش آماری
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="openToolPage('report-builder')">
                        <i class="fa fa-cogs"></i> گزارش ساز
                    </a></li>
                </ul>
            </div>

            <!-- منوی ورود/خروجی -->
            <div class="dropdown">
                <button class="btn btn-warning dropdown-toggle toolbar-btn" type="button" id="ioMenu" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-exchange-alt"></i> ورود/خروجی
                </button>
                <ul class="dropdown-menu" aria-labelledby="ioMenu">
                    <li><a class="dropdown-item" href="#" onclick="openToolPage('import-shp')">
                        <i class="fa fa-upload"></i> ایمپورت SHP
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="openToolPage('print-tools')">
                        <i class="fa fa-print"></i> ابزار چاپ
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- صفحه ابزار تمام صفحه -->
    <div id="toolPage" class="tool-page-overlay" style="display: none;">
        <div class="tool-page-header">
            <h4 id="toolPageTitle">عنوان ابزار</h4>
            <button type="button" class="btn btn-danger btn-sm" onclick="closeToolPage()">
                <i class="fa fa-times"></i> بستن
            </button>
        </div>
        <div id="toolPageContent" class="tool-page-content">
            <!-- محتوای ابزار اینجا لود می‌شود -->
        </div>
    </div>
</div>

<div class="modal" id="dialog-Property" tabindex="-1" role="dialog" aria-labelledby="remoteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        </div>
    </div>
</div>

<div id="panel-Identify" style="position:absolute; display:none; top:50px; bottom:10px; right: 0px; width:400px; height:calc(100vh - 50px); background-color:#fff;border:1px solid gray; z-index:1000;" class=" ">
</div>

<!-- BaseMap panel is now handled by BaseMapManager -->
@section Scripts {
    <!-- ===== کتابخانه‌های JavaScript از LibMan ===== -->
    <script src="~/lib/jqueryui/jquery-ui.min.js"></script>
    <script src="~/lib/jquery.fancytree/jquery.fancytree-all.min.js"></script>
    <script src="~/lib/esri-leaflet/dist/esri-leaflet.js"></script>
    <script src="~/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js"></script>
    <script src="~/lib/ion-rangeslider/js/ion.rangeslider.min.js"></script>

    <!-- ===== کتابخانه‌های ترسیم ===== -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.css" />

    <!-- ===== ماژول‌های سفارشی ===== -->
    <script src="~/js/modules/baseMapManager.js"></script>
    <script src="~/lib/select2/js/select2.min.js"></script>
    <script src="~/lib/sweetalert2/sweetalert2.min.js"></script>

    <!-- ===== کامپوننت‌های پایه ===== -->
    <script src="~/js/components/base-component.js"></script>
    <script src="~/js/components/component-factory.js"></script>

    <!-- ===== کامپوننت‌های GeoMap ===== -->
    <script src="~/js/geomap/geomap-core.js"></script>
    <script src="~/js/geomap/layer-tree.js"></script>
    <script src="~/js/geomap/measurement-tools.js"></script>
    <script src="~/js/geomap/spatial-analysis.js"></script>
    <script src="~/js/geomap/goto-xy.js"></script>
    <script src="~/js/geomap/drawing-tools.js"></script>
    <script src="~/js/geomap/property-tools.js"></script>
    <script src="~/js/geomap/search-tools.js"></script>
    <script src="~/lib/proj4/dist/proj4.js"></script>
    <script src="~/lib/leaflet-dialog/Leaflet.Dialog.js"></script>
    <script src="~/lib/leaflet-measure/leaflet-measure.js"></script>
    
    <script>
        // ========================================
        // متغیرهای سراسری
        // ========================================
        var baseUrl = '@ViewBag.BaseUrl';
        var URLBASE = "@Url.Content("~")";
        var map;
        var mapIndex = "1";
        var lyrs = [mapIndex];
        var lyrCo = [];
        var layerdef = '';
        var latLngCenter = [32.702222, 51.979378];
        var polygcreate;
        var isIdentify = false;
        var highlightStyle = {
            "color": "#FF00FF"
        };
        var isGraphicSketch = false;
        var isSketch = false;
        var identifyTool;
        var identifyResults = [];
        var currentIdentifyIndex = 0;
        var totalPages = 0;
        var currentPage = 1;
        var currentFeatureCollection = null;

        // BaseMap Manager instance
        var baseMapManager = null;

        // ========================================
        // متغیرهای ابزارهای ترسیم و انتخاب
        // ========================================
        var editableLayers;          // لایه‌های قابل ویرایش
        var drawControl;             // کنترل ترسیم
        var markerLayers;            // لایه مارکرها
        var selecttool = false;      // وضعیت ابزار انتخاب
        var selectBoxtool = false;   // وضعیت ابزار انتخاب مستطیلی
         
        $(document).ready(function() {
            // ========================================
            // مقداردهی اولیه نقشه
            // ========================================
            initializeMap();
            initializeMapLayers();
            initializeTOC();
            setupHeaderButtons();
            setupMapControls();
            setupMapEvents();
            initializeMapViews();
            initializeMeasurementTools();
            initializeSpatialAnalysis();
            initializeGoToXY();
            initializeDrawingTools();
            initializePropertyTools();

        });
        function initializeTOC(){
             initializeFancytree();
        
            $('#ActiveLayerLable').autocomplete({
                source: function (request, response) {
                    $.ajax({
                        type: "Get",
                        url: "/GeoMap/SearchTable",
                        dataType: "json",
                        data: { search: request.term },
                        success: function (data) {
                            
                            response($.map(data, function (item) {
                                return { label: item.Lable, value: item.Lable, id: item.ID };
                            }));
                            $(".ui-autocomplete").css("width", "220px");
                            $(".ui-autocomplete").css("background-color", "lightgray");
                            $(".ui-autocomplete").attr("dir", "rtl");
                            $(".ui-autocomplete").css("padding", "10px;");
                        }
                    });
                },
                minLength: 2,
                select: function (event, ui) {
                    $('#ActiveLayerLable').val(ui.item.label); // Fixed: ui.item.lable should be ui.item.label
                    $('#ActiveLayer').val(ui.item.id);
                    console.log(ui.item.id);
                    var treeSl = $('#tree').fancytree('getTree');
                    var nodeSl = treeSl.getNodeByKey(ui.item.id);
                    if (nodeSl) {
                        nodeSl.parent.setExpanded(true);
                        nodeSl.setActive();
                    }
                }
            });
        } 
        
        function initializeMap() {
            map = L.map('map', {
                zoom: 5,
                center: latLngCenter,
                zoomControl: false,
                attributionControl: false
            });

            // اضافه کردن لایه پایه OpenStreetMap
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: ''
            }).addTo(map);
        }

        function initializeMapLayers() {
            // تنظیم لایه‌های ESRI
            lyrCo[0] = L.esri.dynamicMapLayer({
                url: baseUrl + '/rest/services/map/mapserver',
                opacity: 1,
                layers: [],
                f: 'image',
                format: 'png32',
                transparent: true,
                crs: L.CRS.EPSG3857
            }).addTo(map);

            lyrCo[0].idMap = mapIndex.toString();

            // رویدادهای لایه
            lyrCo[0].on("loading", function (e) {
                // می‌توان loading indicator اضافه کرد
            });

            lyrCo[0].on("load", function (e) {
                // می‌توان loading indicator را حذف کرد
            });
        }

        function setupMapControls() {
            // ========================================
            // کنترل‌های نقشه
            // ========================================

            // مقداردهی لایه‌های قابل ویرایش
            editableLayers = new L.FeatureGroup();
            map.addLayer(editableLayers);

            // مقداردهی لایه مارکرها
            markerLayers = new L.FeatureGroup();
            map.addLayer(markerLayers);

            // تنظیم کنترل ترسیم
            drawControl = new L.Control.Draw({
                position: 'topright',
                draw: {
                    polygon: {
                        allowIntersection: false,
                        drawError: {
                            color: '#e1e100',
                            message: '<strong>خطا!</strong> نمی‌توانید خطوط را قطع کنید!'
                        },
                        shapeOptions: {
                            color: '#820091',
                            weight: 3,
                            fillColor: '#820091',
                            fillOpacity: 0.2
                        }
                    },
                    polyline: {
                        shapeOptions: {
                            color: '#820091',
                            weight: 3
                        }
                    },
                    rectangle: {
                        shapeOptions: {
                            color: '#820091',
                            weight: 3,
                            fillColor: '#820091',
                            fillOpacity: 0.2
                        }
                    },
                    circle: {
                        shapeOptions: {
                            color: '#820091',
                            weight: 3,
                            fillColor: '#820091',
                            fillOpacity: 0.2
                        }
                    },
                    marker: true
                },
                edit: {
                    featureGroup: editableLayers,
                    remove: true
                }
            });

            // نوار ابزار اصلی
            L.control.custom({
                position: 'topleft',
                content: createToolbarContent(),
                classes: '',
                style: {
                    'z-index': '100',
                    margin: '2px',
                    cursor: 'pointer'
                },
                events: {
                    click: function (data) {
                        handleToolbarClick(data);
                    }
                }
            }).addTo(map);

            // مقداردهی BaseMap Manager
            if (window.BaseMapManager) {
                baseMapManager = new window.BaseMapManager(map, {
                    position: 'bottomright',
                    controlSize: 70,
                    panelId: 'panel-satImages'
                });
            }

            setupDrawEvents();
        }

        function createToolbarContent() {
            return '<div id="standardToolbar" style="width:44px;min-height:450px; z-index:902;"> ' +
                    // دکمه جابجایی
                    '<button type="button" class="btn tool2 btnView" title="جابجایی" onclick="resetTools();"> ' +
                        '<i class="fa fa-hand"></i> ' +
                    '</button>' +
                    // دکمه شناسایی
                    '<button id="info" type="button" class="btn tool2 btnView" title="شناسایی" onclick="activateIdentifyTool();"> ' +
                        '<i class="fa fa-info"></i> ' +
                    '</button>' +
                    // دکمه رفتن به موقعیت
                    '<button type="button" class="btn tool2 btnView" title="رفتن به موقعیت خاص" onclick="togglegotoxy();"> ' +
                        '<i class="fa fa-street-view"></i> ' +
                    '</button>' +
                    // دکمه اندازه‌گیری (کامپوننت جدید)
                    '<div id="measurement-tools-container" data-component="measurement-tools" style="display: inline-block;"></div>' +

                    // دکمه تحلیل مکانی (کامپوننت جدید)
                    '<div id="spatial-analysis-container" data-component="spatial-analysis" style="display: inline-block;"></div>' +

                    // دکمه رفتن به مختصات (کامپوننت جدید)
                    '<div id="goto-xy-container" data-component="goto-xy" style="display: inline-block;"></div>' +

                    // دکمه ابزارهای ترسیم (کامپوننت جدید)
                    '<div id="drawing-tools-container" data-component="drawing-tools" style="display: inline-block;"></div>' +

                    // دکمه ابزارهای خصوصیات (کامپوننت جدید)
                    '<div id="property-tools-container" data-component="property-tools" style="display: inline-block;"></div>' +
                    // منوی ترسیم
                    '<div class="btn-group-container" style="position:relative;">' +
                        '<button onclick="toggleBtn(\'dpDraw\');" title="ترسیم گرافیک" type="button" class="btn tool2 btnView">' +
                            '<i class="fa fa-pencil"></i>' +
                        '</button>' +
                        '<div class="dropdown-menu" id="dpDraw" style="display:none;">' +
                            '<button type="button" class="btn btnView" title="ترسیم نقطه" onclick="activateDrawTool(\'marker\');"><i class="fa fa-map-marker"></i> نقطه</button>' +
                            '<button type="button" class="btn btnView" title="ترسیم خط" onclick="activateDrawTool(\'polyline\');"><i class="fa fa-minus"></i> خط</button>' +
                            '<button type="button" class="btn btnView" title="ترسیم چندضلعی" onclick="activateDrawTool(\'polygon\');"><i class="fa fa-square-o"></i> چندضلعی</button>' +
                            '<button type="button" class="btn btnView" title="ترسیم مستطیل" onclick="activateDrawTool(\'rectangle\');"><i class="fa fa-square"></i> مستطیل</button>' +
                            '<button type="button" class="btn btnView" title="ترسیم دایره" onclick="activateDrawTool(\'circle\');"><i class="fa fa-circle-o"></i> دایره</button>' +
                            '<button type="button" class="btn btnView" title="ترسیم حریم" onclick="activateBufferTool();"><i class="fa fa-dot-circle-o"></i> حریم</button>' +
                            '<button type="button" class="btn btnView" title="حذف کلیه گرافیک ها" onclick="clearAllGraphics();"><i class="fa fa-trash"></i> حذف همه</button>' +
                        '</div>' +
                    '</div>' +
                    // منوی انتخاب
                    '<div class="btn-group-container" style="position:relative;">' +
                        '<button onclick="toggleBtn(\'dpSelect\');" title="انتخاب" type="button" class="btn tool2 btnView">' +
                            '<i class="fa fa-search"></i>' +
                        '</button>' +
                        '<div class="dropdown-menu" id="dpSelect" style="display:none;">' +
                            '<button type="button" class="btn btnView" title="انتخاب مستطیلی" onclick="activateSelectTool(\'rectangle\');"><i class="fa fa-square-o"></i> مستطیل</button>' +
                            '<button type="button" class="btn btnView" title="انتخاب دایره‌ای" onclick="activateSelectTool(\'circle\');"><i class="fa fa-circle-o"></i> دایره</button>' +
                            '<button type="button" class="btn btnView" title="انتخاب چندضلعی" onclick="activateSelectTool(\'polygon\');"><i class="fa fa-draw-polygon"></i> چندضلعی</button>' +
                            '<hr style="margin: 5px 0;">' +
                            '<button type="button" class="btn btnView" title="حذف انتخاب فعال" onclick="clearActiveSelection();"><i class="fa fa-eraser"></i> حذف انتخاب فعال</button>' +
                            '<button type="button" class="btn btnView" title="تغییر وضعیت هایلایت" onclick="invertSelection();"><i class="fa fa-eye"></i> تغییر هایلایت</button>' +
                            '<button type="button" class="btn btnView" title="حذف همه انتخاب‌ها" onclick="clearAllSelections();"><i class="fa fa-trash-o"></i> حذف همه انتخاب‌ها</button>' +
                            '<hr style="margin: 5px 0;">' +
                            '<button type="button" class="btn btnView" title="نمایش اطلاعات انتخاب‌ها" onclick="showSelectionInfo();"><i class="fa fa-info-circle"></i> اطلاعات انتخاب‌ها</button>' +
                        '</div>' +
                    '</div>' +
                '</div>';
        }

        function handleToolbarClick(data) {
            var id = data.target.id;
            if (data.path != undefined)
                id = data.path[1].id;
            if (id == "locate")
                map.locate({ setView: true });
        }

        function setupMapEvents() {
            // ========================================
            // رویدادهای نقشه
            // ========================================

            // رویداد حرکت نقشه
            map.on("moveend", function (e) {
                // می‌توان عملیات مورد نیاز را اضافه کرد
            });

            // رویداد حرکت موس روی نقشه
            map.on("mousemove", function (e) {
                var arrlatlng = Project(e.latlng);
                var label = "   " + arrlatlng[0].toFixed(4) + ", " + arrlatlng[1].toFixed(4);
                $("#positionText").html(label);

                var labelUTM = getUTMLabel(arrlatlng);
                $("#positionUTMText").html(labelUTM);
            });

            // رویداد کلیک روی نقشه
            map.on('click', function (e) {
                if (isIdentify)
                    identifyLayers(e);
                setGraphicSketchClicked(e);
            });

            // رویداد دابل کلیک
            map.on('dblclick', function (e) {
                if (polygcreate && polygcreate._enabled)
                    polygcreate.completeShape();
            });

            // رویداد تغییر زوم - تنظیم مقیاس
            map.on("zoomend", function (e) {
                setScale();
            });
        }

        function setupDrawEvents() {
            // ========================================
            // رویدادهای ترسیم
            // ========================================

            // رویداد شروع ترسیم
            map.on(L.Draw.Event.DRAWSTART, function (e) {
                console.log('شروع ترسیم:', e.layerType);
            });

            // رویداد اتمام ترسیم
            map.on(L.Draw.Event.CREATED, function (e) {
                var type = e.layerType;
                var layer = e.layer;

                // تشخیص نوع ابزار (ترسیم یا انتخاب)
                var toolType = selectBoxtool ? "selectBoxtool" : "selecttool";

                // اضافه کردن ویژگی‌های سفارشی
                layer.tool = toolType;
                layer.type = type;

                // اضافه کردن popup مناسب
                if (toolType === "selectBoxtool") {
                    layer.bindPopup(
                        '<div class="text-center">' +
                            '<button class="btn btn-success btn-sm me-1" onclick="performSelectionByLayer(' + layer._leaflet_id + ');">' +
                                '<i class="fa fa-search"></i> انتخاب' +
                            '</button>' +
                            '<button class="btn btn-danger btn-sm" onclick="removeDrawnLayer(' + layer._leaflet_id + ');">' +
                                '<i class="fa fa-trash"></i> حذف' +
                            '</button>' +
                        '</div>'
                    );
                } else {
                    layer.bindPopup(
                        '<div class="text-center">' +
                            '<button class="btn btn-danger btn-sm" onclick="removeDrawnLayer(' + layer._leaflet_id + ');">' +
                                '<i class="fa fa-trash"></i> حذف' +
                            '</button>' +
                        '</div>'
                    );
                }

                // اضافه کردن به لایه‌های قابل ویرایش
                editableLayers.addLayer(layer);

                console.log('ترسیم کامل شد:', type, 'ابزار:', toolType);

                // اگر ابزار انتخاب است، خودکار انتخاب انجام شود
                if (toolType === "selectBoxtool") {
                    setTimeout(function() {
                        deactiveSelectBox();
                    }, 100);
                }
            });

            // رویداد ویرایش
            map.on(L.Draw.Event.EDITED, function (e) {
                var layers = e.layers;
                layers.eachLayer(function (layer) {
                    console.log('لایه ویرایش شد:', layer);
                });
            });

            // رویداد حذف
            map.on(L.Draw.Event.DELETED, function (e) {
                var layers = e.layers;
                layers.eachLayer(function (layer) {
                    console.log('لایه حذف شد:', layer);
                });
            });
        }

        function setupHeaderButtons() {
            // دکمه تغییر حالت سایدبار
            $('#sidebar_toggle').on('click', function(e) {
                e.preventDefault();
                animateSidebar();
            });

            // دکمه page_toggle - رفتن به صفحه اصلی
            $('#page_toggle').on('click', function(e) {
                e.preventDefault();
                window.location.href = '@Url.Action("Index", "Home")';
            });
        }

        // ========================================
        // تنظیمات FancyTree (درخت لایه‌ها)
        // ========================================
        function initializeFancytree() {
            $("#tree").fancytree({
                checkbox: true,
                selectMode: 2,
                rtl: true,
                init: function (event, data) {
                    // Set key from first part of title (just for this demo output)
                    data.tree.visit(function (n) {
                        //n.key = n.title.split(" ")[0];
                        if (n.key[0] != "L") {
                            n.checkbox = false;
                        }
                    });
                },
                select: function (event, data) {
                    var node = data.node;

                    // اگر گره‌ای انتخاب شد و یک لایه (L) است، والد آن را انتخاب کن
                    if (node.isSelected() && node.key && node.key[0] == "L") {
                        if (node.getParent()) {
                            node.getParent().setSelected(true, { noEvents: true }); // بدون فعال‌سازی رویداد
                        }
                    }

                    // اگر گره‌ای از حالت انتخاب خارج شد و یک گره والد (گروه) است، فرزندان را uncheck کن
                    if (!node.isSelected() && node.key && node.key[0] == "G") {
                        node.visit(function (child) {
                            child.setSelected(false, { noEvents: true }); // بدون فعال‌سازی رویداد
                        });
                    }

                    // فراخوانی refreshTree فقط یک بار در انتها
                    refreshTree();
                },
                beforeExpand: function (event, data) {
                    var kkey = data.node.key;
                    if (kkey.length > 7 && data.node.key[0] == "L")
                    {
                        setSymbologyByID(kkey.substring(1, 12), -1);
                    }
                },

                // The following options are only required, if we have more than one tree on one page:
                cookieId: "fancytree-Cb2",
                idPrefix: "fancytree-Cb2-",
                click: function (event, data) {
                    if (data && data.node && data.node.isSelected() && data.node.key[0] == "L" && data.node.key.length == 12) {
                        $("#activeLayerLable").val(data.node.li.innerText.split('\n')[0]);
                        $("#activeLayer").val(data.node.key);
                    }
                },
                dblclick: function (event, data) {
                    if (data && data.node && data.node.key[0] == "L" && data.node.key.length == 12) {
                        SettingLayer(data.node.key.substring(1, 80));
                    }
                },
            });
        }

        // ========================================
        // توابع مدیریت لایه‌ها
        // ========================================
        function SettingLayer(layerid) {
            settinglayerid = layerid;
            var url = baseUrl + '/GeoMap/_Property?Layerid=' + layerid;
            
            $.ajax({
                url: url,
                cache: false,
                success: function(data) {
                    $("#dialog-Property .modal-content").html(data);
                    var modalElement = document.getElementById('dialog-Property');
                    var modal = new bootstrap.Modal(modalElement);
                    modal.show();
                    setPropertyProps(layerid);
                    modalElement.addEventListener('hidden.bs.modal', function() {
                        const backdrop = document.querySelector('.modal-backdrop');
                        if (backdrop) {
                            backdrop.remove();
                        }
                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';
                    });
                },
                error: function(data) {
                    console.error("Error loading modal content:", data);
                }
            });
            return false;
        }

        function setPropertyProps(layerid) {
            $("#propertyLayerId").val(layerid);
            getFieldinLable("L" + layerid);

            var chk = $("#C" + layerid).is(':checked');
            $("#layerchecked").prop('checked', chk);
            var op = parseInt($("#O" + layerid).val());
            if (op > 0) {
                op = 100 - op;
            } else {
                op = 0;
            }
            $("#layeropacity").ionRangeSlider({
                min: 0,
                max: 100,
                from: op,
                step: 2,
                grid: true,
                prefix: "%"
            });
            var lbl = $("#La" + layerid).val().replace(/@@/g, ':').replace(/\\n/g, '\n');
            var arrLabels = lbl.split(':');
            var joinLabel = '';
            if (arrLabels.length > 1) {
                joinLabel = arrLabels[arrLabels.length - 1];
            }
            var baseLabel = '';
            if (joinLabel.split('___').length > 1) {
                baseLabel = lbl.replace(joinLabel, '');
                baseLabel = baseLabel.substring(0, baseLabel.length - 1);
            } else {
                baseLabel = lbl;
            }
            $("#layerlabledd").val(baseLabel);
            $("#layerlablejoindd").val(joinLabel);

            var sym = $("#Sym" + layerid).val();
            $("input[name='radio_Sym'][value='" + sym + "']").prop("checked");
            getLegend(sym);
        }

        function setlableopacity() {
            var chk = $("#layerchecked").is(':checked');
            $("#C" + settinglayerid).prop('checked', chk);

            var op = $("#layeropacity").val();
            $("#O" + settinglayerid).val(100 - op);

            var ll = $("#layerlabledd").val().replace(/\n/g, '\\n').replace(/\:/g, '@@');
            var llj = $("#layerlablejoindd").val().replace(/\n/g, '\\n').replace(/\:/g, '@@');
            if ($("#layerlablejoindd").val() && $("#layerlablejoindd").val().trim() != '') {
                if ($("#layerlabledd").val() && $("#layerlabledd").val().trim() != '') {
                    ll = ll + '@@' + llj;
                }
            }
            $("#La" + settinglayerid).val(ll);
            refreshTree();
        }

        function setSymbology() {
            var sym = $("input:radio[name='radio_Sym']:checked").val();
            $("#Sym" + settinglayerid).val(sym);
            setSymbologyByID(settinglayerid, sym);
            refreshTree();
        }

        function setSymbologyByID(layerid, sym) {
            
            sym = $("#Sym" + layerid).val();
            if (!sym) sym = -1;
            var childNode = $("#tree").fancytree("getTree").getNodeByKey("L" + layerid);
            if (childNode) {
                childNode.removeChildren();
            }
            
            $.ajax({
                type: 'POST',
                url: "/Rest/Legend",
                data: { "layerId": "L" + layerid, "symbologyId": sym },
                dataType: 'json',
                success: function(data) {
                    for (var i = 0; i < data.length; i++) {
                        var nodett = childNode.addChildren({
                            active: data[i].active,
                            checkbox: data[i].checkbox,
                            data: data[i].data,
                            expanded: data[i].expanded,
                            focus: data[i].focus,
                            folder: data[i].folder,
                            icon: data[i].icon,
                            key: data[i].key,
                            lazy: data[i].lazy,
                            selected: data[i].selected,
                            title: data[i].title,
                            tooltip: data[i].tooltip
                        });
                        if (data[i].iconHeight && data[i].iconWidth) {
                            $(nodett.span).children('img').height(data[i].iconHeight).width(data[i].iconWidth);
                        }
                    }
                    if (data.length >= 10) {
                        var nodeOther = childNode.addChildren({
                            checkbox: false,
                            icon: false,
                            folder: false,
                            key: "L" + layerid + "_otherLegend",
                            lazy: false,
                            title: "<a href='#' onclick='getLegendOther(1,\"" + layerid + "\");'>...</a>",
                            tooltip: "ادامه..."
                        });
                    }
                },
                error: function(data) {
                    console.error("Error in setSymbologyByID:", data);
                }
            });
        }

        function refreshTree() {
            var selNodes = $("#tree").fancytree('getTree').getSelectedNodes();
            for (var i = 0; i < selNodes.length; i++) {
                if (selNodes[i].getParent() && selNodes[i].getParent().getParent() != null) {
                    selNodes[i].getParent().setSelected(true);
                }
            }
            var r = [];
            for (var j = 0; j < lyrs.length; j++) {
                if (lyrs[j] != "") {
                    for (var i = 0; i < selNodes.length; i++) {
                        var id = selNodes[i].key;
                        if (id[0] == 'L') {
                            if (selNodes[i].getParent() && selNodes[i].getParent().isSelected()) {
                                if (lyrs[j] == parseInt(id.substring(2, 8)).toString()) {
                                    var ids = id.substr(1);
                                    if ($("#C" + ids).is(':checked')) {
                                        ids = ids + ";" + $("#La" + ids).val() + ";" + $("#O" + ids).val() + ";" + $("#Sym" + ids).val();
                                    } else {
                                        ids = ids + ";false;" + $("#O" + ids).val() + ";" + $("#Sym" + ids).val();
                                    }
                                    r.push(ids);
                                }
                            }
                        }
                    }
                    
                    //basemap.bringToBack();
                    lyrCo[j] = lyrCo[j].setLayers(r);
                }
            }
        }

        function animateSidebar() {
            var sidebar = document.getElementById('sidebar');
            var mapDiv = document.getElementById('map');

            sidebar.classList.toggle('hidden');
            mapDiv.classList.toggle('full-width');

            // به‌روزرسانی نقشه بعد از انیمیشن
            setTimeout(function() {
                map.invalidateSize();
            }, 350);
        }

        // ========================================
        // توابع کمکی مختصات و مقیاس
        // ========================================
        function getUTMLabel(arrlatlng) {
            var index = 0;
            var utmZone = '38';
            if (arrlatlng[0] > 42 && arrlatlng[0] < 48) {
                utmZone = '38';
            }
            else if (arrlatlng[0] > 48 && arrlatlng[0] < 54) {
                utmZone = '39';
            }
            else if (arrlatlng[0] > 54 && arrlatlng[0] < 60) {
                utmZone = '40';
            }
            else if (arrlatlng[0] > 60 && arrlatlng[0] < 66) {
                utmZone = '41';
            }

            switch (utmZone) {
                case '38':
                    index = 2;
                    break;
                case '39':
                    index = 3;
                    break;
                case '40':
                    index = 4;
                    break;
                case '41':
                    index = 5;
                    break;
                default:
            }

            var arrUTM = arrlatlng;
            if (index > 0) {
                arrUTM = proj4(projectSystems[index]).forward(arrlatlng);
            }
            var labelUTM = "UTM" + utmZone + ": " + arrUTM[0].toFixed(0) + ", " + arrUTM[1].toFixed(0);
            return labelUTM;
        }

        function setScale()
        {
            var prj = 'PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0],AUTHORITY["ESRI","102100"]]';
            var bnd = map.getBounds();
            var xy1 = proj4(prj).forward([bnd._northEast.lng, bnd._northEast.lat]);
            var xy2 = proj4(prj).forward([bnd._southWest.lng, bnd._southWest.lat]);
            var label = (Math.abs(xy1[0] - xy2[0]) / ((map.getSize().x * 0.0254) / 96)).toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ",");

            //var metres = scale._getRoundNum(map.containerPointToLatLng([0, map.getSize().y / 2]).distanceTo(map.containerPointToLatLng([scale.options.maxWidth, map.getSize().y / 2])))
            //var label = metres < 1000 ? metres + ' m' : (metres / 1000) + ' km';
            $("#scaleText").html("1:" + label);
        }


        // ========================================
        // سیستم‌های مختصات و تبدیل
        // ========================================
        var projectIndex = 0;
        var projectSystems = [
                '',
                'PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0],AUTHORITY["ESRI","102100"]]',
                'PROJCS["WGS 84 / UTM zone 38N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",45],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32638"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["WGS 84 / UTM zone 39N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",51],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32639"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["WGS 84 / UTM zone 40N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",57],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32640"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["WGS 84 / UTM zone 41N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",63],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32641"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["\Iran Lambert\",GEOGCS["\GCS_WGS_1984\",DATUM["\D_WGS_1984\",SPHEROID["\WGS_1984\",6378137.0,298.257223563]],PRIMEM["\Greenwich\",0.0],UNIT["\Degree\",0.0174532925199433]],PROJECTION["\Lambert_Conformal_Conic\"],PARAMETER["\False_Easting\",5000000.0],PARAMETER["\False_Northing\",30000000.0],PARAMETER["\Central_Meridian\",54.0],PARAMETER["\Standard_Parallel_1\",30.0],PARAMETER["\Standard_Parallel_2\",36.0],PARAMETER["\Central_Parallel\",24.0],UNIT["\Meter\",1.0]]'
        ];
        function setproject(index) {
            projectIndex = index;            
        }
        function Project(latlng) {
            if (projectIndex == 0)
                return [latlng.lng, latlng.lat];
            return proj4(projectSystems[projectIndex]).forward([latlng.lng, latlng.lat]);
        }
        function ProjectInverse(x, y) {
            if (projectIndex == 0)
                return [x, y];
            return proj4(projectSystems[projectIndex]).inverse([x, y]);
        }


        function enableTool() {
            map.dragging.enable();

            isIdentify = false;
            isGraphicSketch = false;
            isSketch = false;

            document.getElementById('map').style.cursor = '';

            if (identifiedFeature)
                map.removeLayer(identifiedFeature);
            if (highlighFeature)
                map.removeLayer(highlighFeature);

            if (toolname == "info") {
                document.getElementById('map').style.cursor = 'help';
                isIdentifySaman = true;
            }
             
            if (toolname == "sketchGraphic") {
                document.getElementById('map').style.cursor = 'copy';
                isGraphicSketch = true;
            }
        }

        function toggleBtn(id) {
            // بستن سایر dropdown ها
            $('.dropdown-menu').removeClass('show');

            // تغییر وضعیت dropdown فعلی
            var dropdown = $('#' + id);
            if (dropdown.hasClass('show')) {
                dropdown.removeClass('show');
            } else {
                dropdown.addClass('show');
            }
        }

        function toggleBtnShow(id) {
            $('#' + id).addClass('show');
        }

        function toggleBtnHide(id) {
            $('#' + id).removeClass('show');
        }

        // بستن dropdown ها هنگام کلیک روی نقشه
        $(document).on('click', function(e) {
            if (!$(e.target).closest('#standardToolbar').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });

        // تابع toggle برای dropdown ها
        function toggleBtn(id) {
            var dropdown = document.getElementById(id);
            if (dropdown) {
                var isVisible = dropdown.style.display === 'block';
                // بستن تمام dropdown ها
                $('.dropdown-menu').hide();
                $('.dropdown-menu').removeClass('show');

                if (!isVisible) {
                    dropdown.style.display = 'block';
                    $(dropdown).addClass('show');
                }
            }
        }

        // ========================================
        // توابع نوار ابزار
        // ========================================

        function resetTools() {
            // ریست کردن تمام ابزارها
            toolname = '';
            enableTool();
            isIdentify = false;
            isGraphicSketch = false;
            isSketch = false;
            selecttool = false;
            selectBoxtool = false;

            // غیرفعال کردن ابزارهای ترسیم
            if (drawControl && drawControl._toolbars && drawControl._toolbars.draw) {
                var toolbar = drawControl._toolbars.draw;
                for (var toolName in toolbar._modes) {
                    var tool = toolbar._modes[toolName];
                    if (tool.handler && tool.handler.enabled()) {
                        tool.handler.disable();
                    }
                }
            }

            // غیرفعال کردن polygcreate
            if (polygcreate && polygcreate._enabled) {
                polygcreate.disable();
            }

            document.getElementById('map').style.cursor = 'grab';

            // بستن تمام dropdown ها
            $('.dropdown-menu').removeClass('show');

            console.log('تمام ابزارها ریست شدند');
        }

        function activateIdentifyTool() {
            resetTools();
            toolname = 'info';
            isIdentify = true;
            document.getElementById('map').style.cursor = 'help';
        }

        function showMeasurementDialog() {
            resetTools();

            // ایجاد محتوای دیالوگ اندازه‌گیری
            var dialogContent = `
                <div class="measurement-dialog">
                    <h5 class="mb-3">ابزار اندازه‌گیری</h5>
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label">نوع اندازه‌گیری:</label>
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary" id="measurePoint" onclick="startMeasurement('point')">
                                    <i class="fa fa-circle"></i> نقطه
                                </button>
                                <button type="button" class="btn btn-outline-primary" id="measureLine" onclick="startMeasurement('line')">
                                    <i class="fa fa-minus"></i> خط
                                </button>
                                <button type="button" class="btn btn-outline-primary" id="measurePolygon" onclick="startMeasurement('polygon')">
                                    <i class="fa fa-square-o"></i> چندضلعی
                                </button>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">واحد اندازه‌گیری:</label>
                            <select class="form-select" id="measurementUnit">
                                <option value="meters">متر</option>
                                <option value="kilometers">کیلومتر</option>
                                <option value="feet">فوت</option>
                                <option value="miles">مایل</option>
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="alert alert-info" id="measurementResult" style="display: none;">
                                <strong>نتیجه:</strong> <span id="measurementValue"></span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="btn-group w-100">
                                <button type="button" class="btn btn-success" onclick="clearMeasurements()">
                                    <i class="fa fa-trash"></i> پاک کردن
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="closeMeasurementDialog()">
                                    <i class="fa fa-times"></i> بستن
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // ایجاد دیالوگ
            if (window.measurementDialog) {
                window.measurementDialog.close();
            }

            window.measurementDialog = L.control.dialog({
                size: [350, 300],
                minSize: [300, 250],
                maxSize: [400, 350],
                anchor: [50, 50],
                position: 'topleft',
                initOpen: true
            })
            .setContent(dialogContent)
            .addTo(map);
        }

        // ========================================
        // توابع اندازه‌گیری
        // ========================================

        var currentMeasurement = null;
        var measurementLayers = [];

        function startMeasurement(type) {
            // پاک کردن انتخاب قبلی
            $('.measurement-dialog .btn').removeClass('btn-primary').addClass('btn-outline-primary');
            $('#measure' + type.charAt(0).toUpperCase() + type.slice(1)).removeClass('btn-outline-primary').addClass('btn-primary');

            // پاک کردن اندازه‌گیری قبلی
            clearCurrentMeasurement();

            var unit = $('#measurementUnit').val();

            switch(type) {
                case 'point':
                    startPointMeasurement();
                    break;
                case 'line':
                    startLineMeasurement(unit);
                    break;
                case 'polygon':
                    startPolygonMeasurement(unit);
                    break;
            }
        }

        function startPointMeasurement() {
            map.once('click', function(e) {
                var coords = Project(e.latlng);
                var result = `مختصات: ${coords[0].toFixed(4)}, ${coords[1].toFixed(4)}`;

                // نمایش نتیجه
                $('#measurementValue').text(result);
                $('#measurementResult').show();

                // اضافه کردن نشانگر
                var marker = L.marker(e.latlng, {
                    icon: L.divIcon({
                        className: 'measurement-marker',
                        html: '<i class="fa fa-map-marker" style="color: red; font-size: 20px;"></i>',
                        iconSize: [20, 20],
                        iconAnchor: [10, 20]
                    })
                }).addTo(map);

                measurementLayers.push(marker);
                currentMeasurement = marker;
            });

            map.getContainer().style.cursor = 'crosshair';
        }

        function startLineMeasurement(unit) {
            var polyline = L.polyline([], {
                color: 'red',
                weight: 3,
                opacity: 0.8
            }).addTo(map);

            measurementLayers.push(polyline);
            currentMeasurement = polyline;

            var points = [];
            var totalDistance = 0;

            function onMapClick(e) {
                points.push(e.latlng);
                polyline.addLatLng(e.latlng);

                if (points.length > 1) {
                    var lastDistance = points[points.length - 2].distanceTo(points[points.length - 1]);
                    totalDistance += lastDistance;

                    var result = formatDistance(totalDistance, unit);
                    $('#measurementValue').text(`طول کل: ${result}`);
                    $('#measurementResult').show();
                }

                // اضافه کردن نشانگر برای هر نقطه
                var marker = L.circleMarker(e.latlng, {
                    color: 'red',
                    fillColor: 'white',
                    fillOpacity: 1,
                    radius: 4,
                    weight: 2
                }).addTo(map);

                measurementLayers.push(marker);
            }

            function onMapDblClick(e) {
                map.off('click', onMapClick);
                map.off('dblclick', onMapDblClick);
                map.getContainer().style.cursor = '';
            }

            map.on('click', onMapClick);
            map.on('dblclick', onMapDblClick);
            map.getContainer().style.cursor = 'crosshair';
        }

        function startPolygonMeasurement(unit) {
            var polygon = L.polygon([], {
                color: 'red',
                weight: 3,
                opacity: 0.8,
                fillColor: 'red',
                fillOpacity: 0.2
            }).addTo(map);

            measurementLayers.push(polygon);
            currentMeasurement = polygon;

            var points = [];

            function onMapClick(e) {
                points.push(e.latlng);
                polygon.addLatLng(e.latlng);

                if (points.length >= 3) {
                    var area = L.GeometryUtil.geodesicArea(points);
                    var result = formatArea(area, unit);
                    $('#measurementValue').text(`مساحت: ${result}`);
                    $('#measurementResult').show();
                }

                // اضافه کردن نشانگر برای هر نقطه
                var marker = L.circleMarker(e.latlng, {
                    color: 'red',
                    fillColor: 'white',
                    fillOpacity: 1,
                    radius: 4,
                    weight: 2
                }).addTo(map);

                measurementLayers.push(marker);
            }

            function onMapDblClick(e) {
                map.off('click', onMapClick);
                map.off('dblclick', onMapDblClick);
                map.getContainer().style.cursor = '';

                // بستن چندضلعی
                if (points.length >= 3) {
                    polygon.addLatLng(points[0]);
                }
            }

            map.on('click', onMapClick);
            map.on('dblclick', onMapDblClick);
            map.getContainer().style.cursor = 'crosshair';
        }

        function clearCurrentMeasurement() {
            if (currentMeasurement) {
                map.removeLayer(currentMeasurement);
                currentMeasurement = null;
            }

            map.off('click');
            map.off('dblclick');
            map.getContainer().style.cursor = '';
        }

        function clearMeasurements() {
            // پاک کردن تمام لایه‌های اندازه‌گیری
            measurementLayers.forEach(layer => {
                map.removeLayer(layer);
            });
            measurementLayers = [];
            currentMeasurement = null;

            // پاک کردن نتیجه
            $('#measurementResult').hide();
            $('#measurementValue').text('');

            // ریست کردن دکمه‌ها
            $('.measurement-dialog .btn').removeClass('btn-primary').addClass('btn-outline-primary');

            map.off('click');
            map.off('dblclick');
            map.getContainer().style.cursor = '';
        }

        function closeMeasurementDialog() {
            clearMeasurements();
            if (window.measurementDialog) {
                window.measurementDialog.close();
                window.measurementDialog = null;
            }
        }

        function formatDistance(distance, unit) {
            switch(unit) {
                case 'kilometers':
                    return (distance / 1000).toFixed(2) + ' کیلومتر';
                case 'feet':
                    return (distance * 3.28084).toFixed(2) + ' فوت';
                case 'miles':
                    return (distance / 1609.34).toFixed(2) + ' مایل';
                default:
                    return distance.toFixed(2) + ' متر';
            }
        }

        function formatArea(area, unit) {
            switch(unit) {
                case 'kilometers':
                    return (area / 1000000).toFixed(2) + ' کیلومتر مربع';
                case 'feet':
                    return (area * 10.7639).toFixed(2) + ' فوت مربع';
                case 'miles':
                    return (area / 2589988.11).toFixed(2) + ' مایل مربع';
                default:
                    return area.toFixed(2) + ' متر مربع';
            }
        }

        // ========================================
        // توابع ابزارهای ترسیم بهبود یافته
        // ========================================
        function activateDrawTool(type) {
            resetTools();
            hideAllDropdowns();

            switch(type) {
                case 'marker':
                    activateMarkerDraw();
                    break;
                case 'polyline':
                    activatePolylineDraw();
                    break;
                case 'polygon':
                    activatePolygonDraw();
                    break;
                case 'rectangle':
                    activateRectangleDraw();
                    break;
                case 'circle':
                    activateCircleDraw();
                    break;
                default:
                    console.warn('نوع ابزار ترسیم نامعتبر:', type);
            }
        }

        function activateMarkerDraw() {
            if (drawControl && drawControl._toolbars && drawControl._toolbars.draw) {
                var markerTool = drawControl._toolbars.draw._modes.marker;
                if (markerTool && markerTool.handler) {
                    markerTool.handler.enable();
                    document.getElementById('map').style.cursor = 'crosshair';
                    console.log('ابزار ترسیم نقطه فعال شد');
                }
            } else {
                // Fallback method
                new L.Draw.Marker(map, {
                    icon: new L.Icon.Default()
                }).enable();
                document.getElementById('map').style.cursor = 'crosshair';
            }
        }

        function activatePolylineDraw() {
            if (drawControl && drawControl._toolbars && drawControl._toolbars.draw) {
                var polylineTool = drawControl._toolbars.draw._modes.polyline;
                if (polylineTool && polylineTool.handler) {
                    polylineTool.handler.enable();
                    document.getElementById('map').style.cursor = 'crosshair';
                    console.log('ابزار ترسیم خط فعال شد');
                }
            } else {
                // Fallback method
                new L.Draw.Polyline(map, {
                    shapeOptions: {
                        color: '#820091',
                        weight: 3
                    }
                }).enable();
                document.getElementById('map').style.cursor = 'crosshair';
            }
        }

        function activatePolygonDraw() {
            if (drawControl && drawControl._toolbars && drawControl._toolbars.draw) {
                var polygonTool = drawControl._toolbars.draw._modes.polygon;
                if (polygonTool && polygonTool.handler) {
                    polygonTool.handler.enable();
                    document.getElementById('map').style.cursor = 'crosshair';
                    console.log('ابزار ترسیم چندضلعی فعال شد');
                }
            } else {
                // Fallback method
                polygcreate = new L.Draw.Polygon(map, {
                    shapeOptions: {
                        color: '#820091',
                        weight: 3,
                        fillColor: '#820091',
                        fillOpacity: 0.2
                    }
                });
                polygcreate.enable();
                document.getElementById('map').style.cursor = 'crosshair';
            }
        }

        function activateRectangleDraw() {
            if (drawControl && drawControl._toolbars && drawControl._toolbars.draw) {
                var rectangleTool = drawControl._toolbars.draw._modes.rectangle;
                if (rectangleTool && rectangleTool.handler) {
                    rectangleTool.handler.enable();
                    document.getElementById('map').style.cursor = 'crosshair';
                    console.log('ابزار ترسیم مستطیل فعال شد');
                }
            } else {
                // Fallback method
                new L.Draw.Rectangle(map, {
                    shapeOptions: {
                        color: '#820091',
                        weight: 3,
                        fillColor: '#820091',
                        fillOpacity: 0.2
                    }
                }).enable();
                document.getElementById('map').style.cursor = 'crosshair';
            }
        }

        function activateCircleDraw() {
            if (drawControl && drawControl._toolbars && drawControl._toolbars.draw) {
                var circleTool = drawControl._toolbars.draw._modes.circle;
                if (circleTool && circleTool.handler) {
                    circleTool.handler.enable();
                    document.getElementById('map').style.cursor = 'crosshair';
                    console.log('ابزار ترسیم دایره فعال شد');
                }
            } else {
                // Fallback method
                new L.Draw.Circle(map, {
                    shapeOptions: {
                        color: '#820091',
                        weight: 3,
                        fillColor: '#820091',
                        fillOpacity: 0.2
                    }
                }).enable();
                document.getElementById('map').style.cursor = 'crosshair';
            }
        }

        function activateBufferTool() {
            resetTools();
            if (typeof bufferwindows === 'function') {
                bufferwindows();
            } else {
                console.warn('bufferwindows function not found');
                alert('ابزار ترسیم حریم در حال توسعه است');
            }
        }

        function clearAllGraphics() {
            try {
                // پاک کردن لایه‌های قابل ویرایش
                if (editableLayers) {
                    editableLayers.clearLayers();
                    console.log('تمام لایه‌های قابل ویرایش پاک شدند');
                }

                // پاک کردن مارکرها
                if (markerLayers) {
                    markerLayers.clearLayers();
                    console.log('تمام مارکرها پاک شدند');
                }

                // پاک کردن عارضه‌های شناسایی شده
                if (identifiedFeature) {
                    map.removeLayer(identifiedFeature);
                    identifiedFeature = null;
                }

                if (highlighFeature) {
                    map.removeLayer(highlighFeature);
                    highlighFeature = null;
                }

                // پاک کردن سایر لایه‌های گرافیکی
                for (var item in map._layers) {
                    var layer = map._layers[item];
                    if (layer.tool === "selecttool") {
                        map.removeLayer(layer);
                    }
                }

                console.log('تمام گرافیک‌ها پاک شدند');

            } catch (error) {
                console.error('خطا در پاک کردن گرافیک‌ها:', error);
            }
        }

        // تابع کمکی برای حذف لایه ترسیم شده
        function removeDrawnLayer(layerId) {
            try {
                if (editableLayers) {
                    var layer = editableLayers.getLayer(layerId);
                    if (layer) {
                        editableLayers.removeLayer(layer);
                        console.log('لایه حذف شد:', layerId);
                    }
                }
            } catch (error) {
                console.error('خطا در حذف لایه:', error);
            }
        }

        /**
         * انجام انتخاب بر اساس لایه خاص
         */
        async function performSelectionByLayer(layerId) {
            try {
                var layer = editableLayers.getLayer(layerId);
                if (!layer) {
                    console.error('لایه یافت نشد:', layerId);
                    return;
                }

                var activeLayerId = $("#activeLayer").val();
                if (!activeLayerId || activeLayerId.length <= 1) {
                    showToast('لطفاً ابتدا یک لایه را انتخاب کنید', 'warning', 3000);
                    return;
                }

                var targetLayerId = activeLayerId.substring(1);

                // تبدیل geometry به WKT
                var geoJson = layer.toGeoJSON();
                var wkt = convertGeoJsonToWKT(geoJson.geometry);

                if (!wkt) {
                    showToast('خطا در تبدیل geometry', 'danger', 3000);
                    return;
                }

                // انجام انتخاب
                var result = await selectFeaturesByGeometry(wkt, [`L${targetLayerId}`], {
                    spatialRel: 'esriSpatialRelIntersects',
                    returnGeometry: false,
                    returnIdsOnly: false,
                    maxRecordCount: 1000
                });

                if (result && result.totalCount > 0) {
                    showToast(`${result.totalCount} عارضه انتخاب شد`, 'success', 3000);
                    refreshMapLayer(targetLayerId);
                } else {
                    showToast('هیچ عارضه‌ای در این محدوده یافت نشد', 'info', 3000);
                }

                // حذف لایه انتخاب
                editableLayers.removeLayer(layer);

            } catch (error) {
                console.error('خطا در انجام انتخاب:', error);
                showToast('خطا در انجام انتخاب', 'danger', 3000);
            }
        }

        // تابع سراسری برای استفاده در onclick handlers
        window.performSelectionByLayer = performSelectionByLayer;

        // ========================================
        // توابع ابزارهای انتخاب بهبود یافته
        // ========================================
        function activateSelectTool(type) {
            resetTools();
            hideAllDropdowns();

            switch(type) {
                case 'rectangle':
                    activeSelectBox();
                    break;
                case 'circle':
                    activeSelectCircle();
                    break;
                case 'polygon':
                    activeSelectPolygon();
                    break;
                default:
                    console.warn('نوع ابزار انتخاب نامعتبر:', type);
            }
        }

        /**
         * انجام انتخاب بر اساس geometry ترسیم شده
         */
        async function performSelectionByGraphic(toolType, spatialRel, layerId) {
            try {
                // پیدا کردن لایه‌های ترسیم شده با ابزار مشخص
                var drawnLayers = [];
                editableLayers.eachLayer(function(layer) {
                    if (layer.tool === toolType) {
                        drawnLayers.push(layer);
                    }
                });

                if (drawnLayers.length === 0) {
                    console.warn('هیچ geometry ترسیم شده‌ای یافت نشد');
                    return;
                }

                // تبدیل geometry به WKT
                var geometries = drawnLayers.map(layer => {
                    var geoJson = layer.toGeoJSON();
                    return convertGeoJsonToWKT(geoJson.geometry);
                });

                // انجام انتخاب برای هر geometry
                for (var i = 0; i < geometries.length; i++) {
                    await selectFeaturesByGeometry(geometries[i], [`L${layerId}`], {
                        spatialRel: spatialRel,
                        returnGeometry: false,
                        returnIdsOnly: false,
                        maxRecordCount: 1000
                    });
                }

                showToast('انتخاب با موفقیت انجام شد', 'success', 3000);

                // بروزرسانی نقشه برای نمایش انتخاب‌ها
                refreshMapLayer(layerId);

            } catch (error) {
                console.error('خطا در انجام انتخاب:', error);
                showToast('خطا در انجام انتخاب', 'danger', 3000);
            }
        }

        /**
         * حذف انتخاب فعال برای لایه جاری
         */
        async function clearActiveSelection() {
            try {
                var activeLayerId = $("#activeLayer").val();
                if (!activeLayerId || activeLayerId.length <= 1) {
                    showToast('لطفاً ابتدا یک لایه را انتخاب کنید', 'warning', 3000);
                    return;
                }

                var layerId = activeLayerId.substring(1);
                var success = await clearUserSelections(layerId);

                if (success) {
                    showToast('انتخاب فعال حذف شد', 'success', 3000);
                } else {
                    showToast('خطا در حذف انتخاب فعال', 'danger', 3000);
                }
            } catch (error) {
                console.error('خطا در حذف انتخاب فعال:', error);
                showToast('خطا در حذف انتخاب فعال', 'danger', 3000);
            }
        }

        /**
         * معکوس کردن انتخاب (تغییر وضعیت هایلایت)
         */
        async function invertSelection() {
            try {
                var activeLayerId = $("#activeLayer").val();
                if (!activeLayerId || activeLayerId.length <= 1) {
                    showToast('لطفاً ابتدا یک لایه را انتخاب کنید', 'warning', 3000);
                    return;
                }

                var layerId = activeLayerId.substring(1);

                // دریافت وضعیت فعلی هایلایت
                var selections = await getUserSelections(layerId);
                var currentHighlight = selections.length > 0 ? selections[0].IsHighlight : false;

                // تغییر وضعیت هایلایت
                var success = await toggleUserSelectionsHighlight(layerId, !currentHighlight);

                if (success) {
                    showToast(`هایلایت ${!currentHighlight ? 'فعال' : 'غیرفعال'} شد`, 'success', 3000);
                } else {
                    showToast('خطا در تغییر وضعیت هایلایت', 'danger', 3000);
                }
            } catch (error) {
                console.error('خطا در معکوس کردن انتخاب:', error);
                showToast('خطا در معکوس کردن انتخاب', 'danger', 3000);
            }
        }

        /**
         * حذف تمام انتخاب‌ها از تمام لایه‌ها
         */
        async function clearAllSelections() {
            try {
                // دریافت تمام لایه‌های قابل مشاهده
                var visibleLayers = getVisibleLayerIds();

                if (visibleLayers.length === 0) {
                    showToast('هیچ لایه قابل مشاهده‌ای یافت نشد', 'warning', 3000);
                    return;
                }

                var clearedCount = 0;

                // حذف انتخاب‌ها از هر لایه
                for (var i = 0; i < visibleLayers.length; i++) {
                    var layerId = visibleLayers[i].substring(1); // حذف 'L' از ابتدا
                    var success = await clearUserSelections(layerId);
                    if (success) {
                        clearedCount++;
                    }
                }

                if (clearedCount > 0) {
                    showToast(`انتخاب‌ها از ${clearedCount} لایه حذف شد`, 'success', 3000);
                } else {
                    showToast('هیچ انتخابی برای حذف یافت نشد', 'info', 3000);
                }
            } catch (error) {
                console.error('خطا در حذف تمام انتخاب‌ها:', error);
                showToast('خطا در حذف تمام انتخاب‌ها', 'danger', 3000);
            }
        }

        /**
         * تبدیل GeoJSON geometry به WKT
         */
        function convertGeoJsonToWKT(geometry) {
            try {
                switch (geometry.type) {
                    case 'Point':
                        return `POINT(${geometry.coordinates[0]} ${geometry.coordinates[1]})`;

                    case 'LineString':
                        var coords = geometry.coordinates.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
                        return `LINESTRING(${coords})`;

                    case 'Polygon':
                        var rings = geometry.coordinates.map(ring => {
                            var coords = ring.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
                            return `(${coords})`;
                        }).join(', ');
                        return `POLYGON(${rings})`;

                    case 'MultiPoint':
                        var points = geometry.coordinates.map(coord => `(${coord[0]} ${coord[1]})`).join(', ');
                        return `MULTIPOINT(${points})`;

                    case 'MultiLineString':
                        var lines = geometry.coordinates.map(line => {
                            var coords = line.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
                            return `(${coords})`;
                        }).join(', ');
                        return `MULTILINESTRING(${lines})`;

                    case 'MultiPolygon':
                        var polygons = geometry.coordinates.map(polygon => {
                            var rings = polygon.map(ring => {
                                var coords = ring.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
                                return `(${coords})`;
                            }).join(', ');
                            return `(${rings})`;
                        }).join(', ');
                        return `MULTIPOLYGON(${polygons})`;

                    default:
                        throw new Error(`نوع geometry پشتیبانی نمی‌شود: ${geometry.type}`);
                }
            } catch (error) {
                console.error('خطا در تبدیل GeoJSON به WKT:', error);
                return null;
            }
        }

        /**
         * دریافت لایه‌های قابل مشاهده
         */
        function getVisibleLayerIds() {
            try {
                var visibleLayers = [];
                var selectedNodes = $("#tree").fancytree('getTree').getSelectedNodes();

                selectedNodes.forEach(function(node) {
                    if (node.key && node.key.startsWith('L') && node.key.length === 12) {
                        var layerId = node.key.substring(1);
                        // بررسی اینکه لایه در نقشه نمایش داده می‌شود
                        if ($("#C" + layerId).is(':checked')) {
                            visibleLayers.push(node.key);
                        }
                    }
                });

                return visibleLayers;
            } catch (error) {
                console.error('خطا در دریافت لایه‌های قابل مشاهده:', error);
                return [];
            }
        }

        // تابع شناسایی لایه‌ها
        function identifyLayers(e) {
            if (!isIdentify) return;

            // نمایش پنل شناسایی
            $("#panel-Identify").show().html('<div class="p-3"><i class="fa fa-spinner fa-spin"></i> در حال شناسایی...</div>');

            // فراخوانی سرویس شناسایی
            L.esri.identifyFeatures({
                url: baseUrl + '/rest/services/map/mapserver'
            })
           .on(map)
            .at(e.latlng)
            .tolerance(2)
            .layers(function() {
                var visibleLayers = [];
                if (lyrCo && lyrCo[0] && lyrCo[0].getLayers) {
                    var layers = lyrCo[0].getLayers();
                    layers.forEach(function(layer) {
                        var parts = layer.split(';');
                        if (parts.length >= 3) {
                            var layerId = parts[0];
                            var isVisible = parts[2] !== 'false';
                            if (isVisible) {
                                visibleLayers.push('show:' + layerId);
                            }
                        }
                    });
                }
                var result = visibleLayers.join(',');
                return result;
            }())
            .layerDef(function() {
                var layerDefs = {};
                if (lyrCo && lyrCo[0] && lyrCo[0].getLayers) {
                    layerDefs = lyrCo[0].getLayerDefs();
                }
                var jsonStr = JSON.stringify(layerDefs);
                return jsonStr;
            }())
            .run(function(error, featureCollection) {
                if (error) {
                    console.error('خطا در شناسایی:', error);
                    $("#panel-Identify").html('<div class="alert alert-danger m-3">خطا در شناسایی عارضه‌ها</div>');
                    return;
                }

                if (featureCollection.features && featureCollection.features.length > 0) {
                    initializeIdentifyPanel(featureCollection);
                } else {
                    $("#panel-Identify").html('<div class="alert alert-info m-3">هیچ عارضه‌ای در این موقعیت یافت نشد</div>');
                }
            });
        }

        

        function initializeIdentifyPanel(featureCollection) {
            var featuresPerPage = 1;
            totalPages = featureCollection.features.length;
            currentPage = 1;
            currentFeatureCollection = featureCollection;

            // Show first page initially
            showPage(1);

            // Add event listeners for tab changes
            $(document).on('shown.bs.tab', '[data-tab-type]', function (e) {
                var featureId = $(this).data('feature-id');
                var tabType = $(this).data('tab-type');
                var targetId = $(e.target).attr('href');
                
                if (tabType === 'related') {
                    loadRelatedRecords(featureId, targetId);
                } else if (tabType === 'docs') {
                    loadDocuments(featureId, targetId);
                }
            });
        }
        function showPage(pageNum) {
            if (!currentFeatureCollection || pageNum < 1 || pageNum > totalPages) return;
            currentPage = pageNum;

            var feature = currentFeatureCollection.features[pageNum - 1];
            var html = '<div class="card m-0">';
            html += '<div class="card-header d-flex justify-content-between align-items-center p-2">';
            html += '<h6 class="mb-0">نتایج شناسایی</h5>';
            html += '<button type="button" class="btn btn-sm btn-outline-secondary" onclick="$(\'#panel-Identify\').hide();">';
            html += '<i class="fa fa-times"></i>';
            html += '</button>';
            html += '</div>';

            // Single Feature Card
            html += '<div class="card m-0">';
            html += '<div class="card-header  d-flex justify-content-between align-items-center p-1">';
            html += '<small>عارضه ' + pageNum + ' از ' + totalPages + '</small>';
            html += '<div>';
            html += '<button type="button" class="btn btn-sm btn-outline-primary" onclick="zoomToIdentifyFeature(' + (pageNum - 1) + ');">';
            html += '<i class="fa fa-search-plus"></i>';
            html += '</button>';
            html += '<button type="button" class="btn btn-sm btn-outline-primary" onclick="flashIdentifyFeature(' + (pageNum - 1) + ');">';
            html += '<i class="fa fa-bolt"></i>';
            html += '</button>';
            html += '</div>';
            html += '</div>';
            html += '<div class="card-body p-1">';

            // Tab Navigation
            html += '<ul class="nav nav-tabs" role="tablist">';
            html += '<li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#info' + (pageNum - 1) + '" role="tab">اطلاعات</a></li>';
            html += '<li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#related' + (pageNum - 1) + '" role="tab" data-feature-id="' + (pageNum - 1) + '" data-tab-type="related">رکوردهای مرتبط</a></li>';
            html += '<li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#docs' + (pageNum - 1) + '" role="tab" data-feature-id="' + (pageNum - 1) + '" data-tab-type="docs">مستندات</a></li>';
            html += '</ul>';

            // Tab Content
            html += '<div class="tab-content mt-1">';

            // Info Tab
            html += '<div class="tab-pane fade show active" id="info' + (pageNum - 1) + '" role="tabpanel">';
            if (feature.properties) {
                for (var prop in feature.properties) {
                    if (feature.properties[prop] !== null && feature.properties[prop] !== '') {
                        html += '<div class="row mb-1">';
                        html += '<div class="col-5"><small><strong>' + prop + ':</strong></small></div>';
                        html += '<div class="col-7"><small>' + feature.properties[prop] + '</small></div>';
                        html += '</div>';
                    }
                }
            }
            html += '</div>';

            // Related Records Tab
            html += '<div class="tab-pane fade" id="related' + (pageNum - 1) + '" role="tabpanel">';
            html += '<div class="text-center p-3"><i class="fa fa-spinner fa-spin"></i> در حال بارگذاری...</div>';
            html += '</div>';

            // Documents Tab
            html += '<div class="tab-pane fade" id="docs' + (pageNum - 1) + '" role="tabpanel">';
            html += '<div class="text-center p-3"><i class="fa fa-spinner fa-spin"></i> در حال بارگذاری...</div>';
            html += '</div>';

            html += '</div>'; // End tab-content
            html += '</div></div>'; // End card-body and card

            // Pagination Controls
            html += '<div class="d-flex justify-content-center mt-1">';
            html += '<nav aria-label="Page navigation">';
            html += '<ul class="pagination">';

            // Previous button
            html += '<li class="page-item ' + (currentPage === 1 ? 'disabled' : '') + '">';
            html += '<a class="page-link" href="#" onclick="changePage(' + (currentPage - 1) + '); return false;">قبلی</a>';
            html += '</li>';

            // Page numbers
            for (var i = 1; i <= totalPages; i++) {
                if(i == 1 || i == 2 || i == totalPages - 1 || i == totalPages || i == currentPage)
                {
                    html += '<li class="page-item ' + (i === currentPage ? 'active' : '') + '">';
                    html += '<a class="page-link" href="#" onclick="changePage(' + i + '); return false;">' + i + '</a>';
                    html += '</li>';
                }
            }

            // Next button
            html += '<li class="page-item ' + (currentPage === totalPages ? 'disabled' : '') + '">';
            html += '<a class="page-link" href="#" onclick="changePage(' + (currentPage + 1) + '); return false;">بعدی</a>';
            html += '</li>';

            html += '</ul>';
            html += '</nav>';
            html += '</div>';

            html += '</div>';
            $("#panel-Identify").html(html);
        }

        function changePage(pageNum) {
            if (pageNum < 1 || pageNum > totalPages) return;
            currentPage = pageNum;
            showPage(pageNum);
        }

        function  getVisibleLayers()
        {
            var visibleLayers = [];
            lyrCo[0].getLayers().forEach(function(layer) {
                if (layer.visible) {
                    visibleLayers.push('show:' + layer.id);
                }
            });
            return visibleLayers.join(',');
        }


        function zoomToIdentifyFeature(featureIndex) {
            var feature = currentFeatureCollection.features[featureIndex];
            if (feature && feature.geometry) {
                var bounds = L.geoJSON(feature).getBounds();
                map.fitBounds(bounds, {
                    padding: [50, 50],
                    maxZoom: 18
                });
            }
        }
        function flashIdentifyFeature(featureIndex)
        {
            var feature = currentFeatureCollection.features[featureIndex];
            if (feature && feature.geometry) {
                flashFeature(feature);
            }
        }
        
        function loadRelatedRecords(featureId, targetId) {
            var feature = currentFeatureCollection.features[featureId];
            if (!feature) return;

            $.ajax({
                url: baseUrl + '/GeoMap/GetRelatedRecords',
                type: 'POST',
                data: {
                    featureId: feature.properties.ID || featureId,
                    layerId: feature.layerId
                },
                success: function(response) {
                    var html = '<div class="table-responsive">';
                    html += '<table class="table table-sm">';
                    html += '<thead><tr>';
                    for (var col in response.columns) {
                        html += '<th>' + response.columns[col] + '</th>';
                    }
                    html += '</tr></thead><tbody>';

                    response.data.forEach(function(row) {
                        html += '<tr>';
                        for (var col in response.columns) {
                            html += '<td>' + row[col] + '</td>';
                        }
                        html += '</tr>';
                    });

                    html += '</tbody></table></div>';
                    $(targetId).html(html);
                },
                error: function() {
                    $(targetId).html('<div class="alert alert-danger">خطا در بارگذاری اطلاعات</div>');
                }
            });
        }

        function loadDocuments(featureId, targetId) {
            var feature = currentFeatureCollection.features[featureId];
            if (!feature) return;

            $.ajax({
                url: baseUrl + '/GeoMap/GetFeatureDocuments',
                type: 'POST',
                data: {
                    featureId: feature.properties.ID || featureId,
                    layerId: feature.layerId
                },
                success: function(response) {
                    var html = '<div class="list-group">';
                    response.forEach(function(doc) {
                        html += '<a href="' + doc.url + '" class="list-group-item list-group-item-action" target="_blank">';
                        html += '<div class="d-flex w-100 justify-content-between">';
                        html += '<h6 class="mb-1">' + doc.title + '</h6>';
                        html += '<small>' + doc.date + '</small>';
                        html += '</div>';
                        html += '<p class="mb-1">' + doc.description + '</p>';
                        html += '</a>';
                    });
                    html += '</div>';
                    $(targetId).html(html);
                },
                error: function() {
                    $(targetId).html('<div class="alert alert-danger">خطا در بارگذاری مستندات</div>');
                }
            });
        }

        // ========================================
        // توابع انتخاب بر اساس geometry
        // ========================================

        /**
         * انتخاب عارضه‌ها بر اساس geometry
         *  {Object} geometry - geometry برای انتخاب (WKT یا GeoJSON)
         *  {string|Array} layers - لایه‌ها برای انتخاب (رشته یا آرایه)
         *  {Object} options - تنظیمات اضافی
         * returns {Promise} نتیجه انتخاب
         */
        function selectFeaturesByGeometry(geometry, layers, options = {}) {
            return new Promise((resolve, reject) => {
                // تنظیمات پیش‌فرض
                const defaultOptions = {
                    spatialRel: 'esriSpatialRelIntersects',
                    returnGeometry: false,
                    returnIdsOnly: false,
                    maxRecordCount: 1000
                };

                const finalOptions = { ...defaultOptions, ...options };

                // تبدیل layers به رشته اگر آرایه باشد
                const layersParam = Array.isArray(layers) ? layers.join(',') : layers;

                // تبدیل geometry به فرمت مناسب
                let geometryParam = '';
                if (typeof geometry === 'string') {
                    // اگر WKT باشد
                    geometryParam = geometry;
                } else if (typeof geometry === 'object') {
                    // اگر GeoJSON یا Leaflet geometry باشد
                    if (geometry.type) {
                        // GeoJSON format
                        geometryParam = JSON.stringify(geometry);
                    } else if (geometry.getBounds) {
                        // Leaflet layer
                        const bounds = geometry.getBounds();
                        geometryParam = `${bounds.getWest()},${bounds.getSouth()},${bounds.getEast()},${bounds.getNorth()}`;
                    } else if (geometry.getLatLng) {
                        // Leaflet marker
                        const latlng = geometry.getLatLng();
                        geometryParam = `POINT(${latlng.lng} ${latlng.lat})`;
                    }
                }

                if (!geometryParam) {
                    reject(new Error('فرمت geometry نامعتبر است'));
                    return;
                }

                // ارسال درخواست
                $.ajax({
                    url: baseUrl + '/rest/services/map/mapserver/selectbygeometry',
                    type: 'POST',
                    data: {
                        geometry: geometryParam,
                        layers: layersParam,
                        spatialRel: finalOptions.spatialRel,
                        returnGeometry: finalOptions.returnGeometry,
                        returnIdsOnly: finalOptions.returnIdsOnly,
                        maxRecordCount: finalOptions.maxRecordCount
                    },
                    success: function(response) {
                        if (response.success) {
                            console.log(`انتخاب موفق: ${response.totalCount} عارضه یافت شد`);
                            resolve(response);
                        } else {
                            reject(new Error(response.message || 'خطا در انتخاب عارضه‌ها'));
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('خطا در انتخاب بر اساس geometry:', error);
                        reject(new Error(`خطا در ارتباط با سرور: ${error}`));
                    }
                });
            });
        }

        /**
         * انتخاب عارضه‌ها بر اساس مستطیل انتخاب
         *  {L.LatLngBounds} bounds - محدوده انتخاب
         *  {string|Array} layers - لایه‌ها برای انتخاب
         *  {Object} options - تنظیمات اضافی
         */
        function selectFeaturesByBounds(bounds, layers, options = {}) {
            const bbox = `${bounds.getWest()},${bounds.getSouth()},${bounds.getEast()},${bounds.getNorth()}`;
            return selectFeaturesByGeometry(bbox, layers, options);
        }

        /**
         * انتخاب عارضه‌ها بر اساس نقطه
         *  {L.LatLng} latlng - نقطه انتخاب
         *  {string|Array} layers - لایه‌ها برای انتخاب
         *  {Object} options - تنظیمات اضافی
         */
        function selectFeaturesByPoint(latlng, layers, options = {}) {
            const point = `POINT(${latlng.lng} ${latlng.lat})`;
            return selectFeaturesByGeometry(point, layers, options);
        }

        /**
         * انتخاب عارضه‌ها بر اساس چندضلعی
         *  {Array} coordinates - مختصات چندضلعی [[lng, lat], ...]
         *  {string|Array} layers - لایه‌ها برای انتخاب
         *  {Object} options - تنظیمات اضافی
         */
        function selectFeaturesByPolygon(coordinates, layers, options = {}) {
            // تبدیل مختصات به WKT
            const coordsStr = coordinates.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
            const polygon = `POLYGON((${coordsStr}))`;
            return selectFeaturesByGeometry(polygon, layers, options);
        }

        /**
         * نمایش نتایج انتخاب در پنل
         *  {Object} selectionResult - نتیجه انتخاب
         */
        function displaySelectionResults(selectionResult) {
            if (!selectionResult.success) {
                console.error('خطا در نتایج انتخاب:', selectionResult.message);
                return;
            }

            let html = '<div class="p-3">';
            html += '<div class="d-flex justify-content-between align-items-center mb-3">';
            html += '<h5 class="mb-0">نتایج انتخاب</h5>';
            html += '<button type="button" class="btn btn-sm btn-outline-secondary" onclick="$(\'#panel-Identify\').hide();">';
            html += '<i class="fa fa-times"></i>';
            html += '</button>';
            html += '</div>';

            html += `<div class="alert alert-info">تعداد کل عارضه‌های انتخاب شده: ${selectionResult.totalCount}</div>`;

            if (selectionResult.layers && selectionResult.layers.length > 0) {
                selectionResult.layers.forEach((layer, layerIndex) => {
                    html += '<div class="card mb-2">';
                    html += `<div class="card-header bg-primary text-white">`;
                    html += `<small>${layer.LayerName} (${layer.FeatureCount} عارضه)</small>`;
                    html += '</div>';

                    if (layer.Features && layer.Features.length > 0) {
                        html += '<div class="card-body p-2" style="max-height: 300px; overflow-y: auto;">';
                        layer.Features.forEach((feature, featureIndex) => {
                            html += `<div class="border-bottom mb-2 pb-2">`;
                            html += `<strong>عارضه ${featureIndex + 1}:</strong><br>`;

                            if (feature.attributes) {
                                for (const [key, value] of Object.entries(feature.attributes)) {
                                    if (value !== null && value !== '') {
                                        html += `<small><strong>${key}:</strong> ${value}</small><br>`;
                                    }
                                }
                            }
                            html += '</div>';
                        });
                        html += '</div>';
                    }
                    html += '</div>';
                });
            }

            html += '</div>';

            // نمایش در پنل شناسایی
            $("#panel-Identify").html(html).show();
        }

        // ========================================
        // مثال‌های استفاده از توابع انتخاب
        // ========================================

        /**
         * مثال: انتخاب عارضه‌ها در محدوده فعلی نقشه
         */
        function selectFeaturesInCurrentView() {
            const bounds = map.getBounds();
            const visibleLayers = getVisibleLayerIds(); // تابع برای دریافت لایه‌های قابل مشاهده

            if (visibleLayers.length === 0) {
                alert('هیچ لایه‌ای قابل مشاهده نیست');
                return;
            }

            selectFeaturesByBounds(bounds, visibleLayers, {
                returnGeometry: false,
                maxRecordCount: 500
            })
            .then(result => {
                displaySelectionResults(result);
            })
            .catch(error => {
                console.error('خطا در انتخاب:', error);
                alert('خطا در انتخاب عارضه‌ها: ' + error.message);
            });
        }

        /**
         * مثال: انتخاب عارضه‌ها بر اساس کلیک روی نقشه
         */
        function selectFeaturesAtPoint(e) {
            const visibleLayers = getVisibleLayerIds();

            if (visibleLayers.length === 0) {
                return;
            }

            selectFeaturesByPoint(e.latlng, visibleLayers, {
                returnGeometry: true,
                maxRecordCount: 10
            })
            .then(result => {
                if (result.totalCount > 0) {
                    displaySelectionResults(result);
                } else {
                    console.log('هیچ عارضه‌ای در این نقطه یافت نشد');
                }
            })
            .catch(error => {
                console.error('خطا در انتخاب نقطه‌ای:', error);
            });
        }

        /**
         * مثال: انتخاب عارضه‌ها بر اساس چندضلعی ترسیم شده
         */
        function selectFeaturesInDrawnPolygon(layer) {
            const visibleLayers = getVisibleLayerIds();

            if (visibleLayers.length === 0) {
                alert('هیچ لایه‌ای قابل مشاهده نیست');
                return;
            }

            // تبدیل لایه Leaflet به مختصات
            const coordinates = layer.getLatLngs()[0].map(latlng => [latlng.lng, latlng.lat]);
            // بستن چندضلعی
            coordinates.push(coordinates[0]);

            selectFeaturesByPolygon(coordinates, visibleLayers, {
                returnGeometry: false,
                maxRecordCount: 1000
            })
            .then(result => {
                displaySelectionResults(result);
                console.log(`انتخاب در چندضلعی: ${result.totalCount} عارضه یافت شد`);
            })
            .catch(error => {
                console.error('خطا در انتخاب چندضلعی:', error);
                alert('خطا در انتخاب عارضه‌ها: ' + error.message);
            });
        }

        /**
         * دریافت لیست لایه‌های قابل مشاهده
         */
        function getVisibleLayerIds() {
            const visibleLayers = [];

            // بررسی لایه‌های انتخاب شده در درخت
            const selectedNodes = $("#tree").fancytree('getTree').getSelectedNodes();
            selectedNodes.forEach(node => {
                if (node.key && node.key.startsWith('L') && node.key.length === 12) {
                    // بررسی اینکه لایه فعال است
                    const layerId = node.key.substring(1);
                    if ($("#C" + layerId).is(':checked')) {
                        visibleLayers.push(node.key);
                    }
                }
            });

            return visibleLayers;
        }

        /**
         * انتخاب عارضه‌ها بر اساس WKT geometry
         *  {string} wktGeometry - geometry به فرمت WKT
         *  {Array} layerIds - آرایه ID های لایه
         */
        function selectFeaturesByWKT(wktGeometry, layerIds) {
            return selectFeaturesByGeometry(wktGeometry, layerIds, {
                spatialRel: 'esriSpatialRelIntersects',
                returnGeometry: true,
                returnIdsOnly: false,
                maxRecordCount: 1000
            });
        }

        // تابع رفتن به موقعیت خاص
        function togglegotoxy() {
            resetTools();

            // ایجاد محتوای دیالوگ GoToXY
            var dialogContent = `
                <div class="gotoxy-dialog">
                    <h5 class="mb-3">رفتن به مختصات</h5>
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label">سیستم مختصات:</label>
                            <select class="form-select" id="coordinateSystem">
                                <option value="geographic">جغرافیایی (WGS84)</option>
                                <option value="mercator">مرکاتور (Web Mercator)</option>
                                <option value="utm38">UTM Zone 38N</option>
                                <option value="utm39">UTM Zone 39N</option>
                                <option value="utm40">UTM Zone 40N</option>
                                <option value="utm41">UTM Zone 41N</option>
                            </select>
                        </div>
                        <div class="col-6 mb-3">
                            <label class="form-label" id="xLabel">طول جغرافیایی (X):</label>
                            <input type="number" class="form-control" id="coordinateX" placeholder="51.3890" step="any">
                        </div>
                        <div class="col-6 mb-3">
                            <label class="form-label" id="yLabel">عرض جغرافیایی (Y):</label>
                            <input type="number" class="form-control" id="coordinateY" placeholder="35.6892" step="any">
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">سطح زوم:</label>
                            <select class="form-select" id="zoomLevel">
                                <option value="10">10 - شهر</option>
                                <option value="12">12 - منطقه</option>
                                <option value="15" selected>15 - محله</option>
                                <option value="17">17 - خیابان</option>
                                <option value="19">19 - ساختمان</option>
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="addMarker" checked>
                                <label class="form-check-label" for="addMarker">
                                    اضافه کردن نشانگر
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="btn-group w-100">
                                <button type="button" class="btn btn-primary" onclick="goToCoordinates()">
                                    <i class="fa fa-map-marker"></i> رفتن
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="closeGoToXYDialog()">
                                    <i class="fa fa-times"></i> بستن
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // ایجاد دیالوگ
            if (window.gotoxyDialog) {
                window.gotoxyDialog.close();
            }

            window.gotoxyDialog = L.control.dialog({
                size: [400, 350],
                minSize: [350, 300],
                maxSize: [450, 400],
                anchor: [100, 50],
                position: 'topleft',
                initOpen: true
            })
            .setContent(dialogContent)
            .addTo(map);

            // تنظیم event listener برای تغییر سیستم مختصات
            setTimeout(function() {
                $('#coordinateSystem').on('change', updateCoordinateLabels);
                updateCoordinateLabels();
            }, 100);
        }

        // ========================================
        // توابع GoToXY
        // ========================================

        var gotoxyMarker = null;

        function updateCoordinateLabels() {
            var system = $('#coordinateSystem').val();
            var xLabel = $('#xLabel');
            var yLabel = $('#yLabel');
            var xInput = $('#coordinateX');
            var yInput = $('#coordinateY');

            switch(system) {
                case 'geographic':
                    xLabel.text('طول جغرافیایی (Longitude):');
                    yLabel.text('عرض جغرافیایی (Latitude):');
                    xInput.attr('placeholder', '51.3890');
                    yInput.attr('placeholder', '35.6892');
                    break;
                case 'mercator':
                    xLabel.text('X (متر):');
                    yLabel.text('Y (متر):');
                    xInput.attr('placeholder', '5718499.45');
                    yInput.attr('placeholder', '4268239.12');
                    break;
                case 'utm38':
                    xLabel.text('Easting (متر):');
                    yLabel.text('Northing (متر):');
                    xInput.attr('placeholder', '718499');
                    yInput.attr('placeholder', '3968239');
                    break;
                case 'utm39':
                    xLabel.text('Easting (متر):');
                    yLabel.text('Northing (متر):');
                    xInput.attr('placeholder', '318499');
                    yInput.attr('placeholder', '3968239');
                    break;
                case 'utm40':
                    xLabel.text('Easting (متر):');
                    yLabel.text('Northing (متر):');
                    xInput.attr('placeholder', '918499');
                    yInput.attr('placeholder', '3968239');
                    break;
                case 'utm41':
                    xLabel.text('Easting (متر):');
                    yLabel.text('Northing (متر):');
                    xInput.attr('placeholder', '518499');
                    yInput.attr('placeholder', '3968239');
                    break;
            }
        }

        function goToCoordinates() {
            var x = parseFloat($('#coordinateX').val());
            var y = parseFloat($('#coordinateY').val());
            var system = $('#coordinateSystem').val();
            var zoom = parseInt($('#zoomLevel').val());
            var addMarker = $('#addMarker').is(':checked');

            if (isNaN(x) || isNaN(y)) {
                alert('لطفاً مختصات معتبر وارد کنید');
                return;
            }

            try {
                var targetCoords = transformCoordinates(x, y, system);

                if (!targetCoords) {
                    alert('خطا در تبدیل مختصات');
                    return;
                }

                // رفتن به موقعیت
                map.setView([targetCoords.lat, targetCoords.lng], zoom);

                // حذف نشانگر قبلی
                if (gotoxyMarker) {
                    map.removeLayer(gotoxyMarker);
                    gotoxyMarker = null;
                }

                // اضافه کردن نشانگر جدید
                if (addMarker) {
                    gotoxyMarker = L.marker([targetCoords.lat, targetCoords.lng], {
                        icon: L.divIcon({
                            className: 'gotoxy-marker',
                            html: '<i class="fa fa-map-marker" style="color: blue; font-size: 24px;"></i>',
                            iconSize: [24, 24],
                            iconAnchor: [12, 24]
                        })
                    }).addTo(map);

                    var popupContent = `
                        <div>
                            <strong>موقعیت مورد نظر</strong><br>
                            <small>سیستم: ${getSystemName(system)}</small><br>
                            <small>X: ${x}</small><br>
                            <small>Y: ${y}</small><br>
                            <small>WGS84: ${targetCoords.lng.toFixed(6)}, ${targetCoords.lat.toFixed(6)}</small>
                        </div>
                    `;

                    gotoxyMarker.bindPopup(popupContent).openPopup();
                }

                console.log(`رفتن به مختصات: ${targetCoords.lng}, ${targetCoords.lat} (${system})`);

            } catch (error) {
                console.error('خطا در رفتن به مختصات:', error);
                alert('خطا در پردازش مختصات: ' + error.message);
            }
        }

        function transformCoordinates(x, y, system) {
            try {
                switch(system) {
                    case 'geographic':
                        return { lng: x, lat: y };

                    case 'mercator':
                        // تبدیل از Web Mercator به WGS84
                        var lng = x / 20037508.34 * 180;
                        var lat = y / 20037508.34 * 180;
                        lat = 180 / Math.PI * (2 * Math.atan(Math.exp(lat * Math.PI / 180)) - Math.PI / 2);
                        return { lng: lng, lat: lat };

                    case 'utm38':
                        return transformUTMToWGS84(x, y, 38);

                    case 'utm39':
                        return transformUTMToWGS84(x, y, 39);

                    case 'utm40':
                        return transformUTMToWGS84(x, y, 40);

                    case 'utm41':
                        return transformUTMToWGS84(x, y, 41);

                    default:
                        throw new Error('سیستم مختصات نامعتبر');
                }
            } catch (error) {
                console.error('خطا در تبدیل مختصات:', error);
                return null;
            }
        }

        function transformUTMToWGS84(easting, northing, zone) {
            try {
                // استفاده از proj4 برای تبدیل UTM به WGS84
                var utmProj = `+proj=utm +zone=${zone} +datum=WGS84 +units=m +no_defs`;
                var wgs84Proj = '+proj=longlat +datum=WGS84 +no_defs';

                var result = proj4(utmProj, wgs84Proj, [easting, northing]);
                return { lng: result[0], lat: result[1] };
            } catch (error) {
                console.error('خطا در تبدیل UTM:', error);
                throw new Error('خطا در تبدیل مختصات UTM');
            }
        }

        function getSystemName(system) {
            switch(system) {
                case 'geographic': return 'جغرافیایی (WGS84)';
                case 'mercator': return 'مرکاتور (Web Mercator)';
                case 'utm38': return 'UTM Zone 38N';
                case 'utm39': return 'UTM Zone 39N';
                case 'utm40': return 'UTM Zone 40N';
                case 'utm41': return 'UTM Zone 41N';
                default: return 'نامشخص';
            }
        }

        function closeGoToXYDialog() {
            if (window.gotoxyDialog) {
                window.gotoxyDialog.close();
                window.gotoxyDialog = null;
            }
        }
 
        //-----------------------------------------
        var polygcreate;    
        var styleGraphic = { color: '#820091', weight: 3, fillColor: '#820091', dashArray: '5, 5', fillOpacity: 0.2 };
        var styleGraphic2 = { color: '#ed0000', weight: 3, fillColor: '#ed0000', dashArray: '5, 5', fillOpacity: 0.1 };

        function changeColorGraphicLayer(color) {
            styleGraphic.color = color;
            styleGraphic.fillColor = color;
            for (var i in map._layers)
                if (map._layers[i].tool && map._layers[i].tool == "selecttool")
                    map._layers[i].setStyle({ color: color, fillColor: color });
        }

        function activeSelectBox() {
            map.dragging.disable();
            new L.Draw.Rectangle(map, { shapeOptions: styleGraphic }).enable();
            selectBoxtool = true;
            document.getElementById('map').style.cursor = 'crosshair';
        }

        function activeSelectRectangle() {
            map.dragging.disable();
            new L.Draw.Rectangle(map, { shapeOptions: styleGraphic }).enable();
            selecttool = true;
            document.getElementById('map').style.cursor = 'crosshair';
        }
        function activeSelectCircle() {
            map.dragging.disable();
            new L.Draw.Circle(map, { shapeOptions: styleGraphic }).enable();
            selectBoxtool = true; // استفاده از همان متغیر برای consistency
            document.getElementById('map').style.cursor = 'crosshair';
            console.log('ابزار انتخاب دایره‌ای فعال شد');
        }

        function activeSelectPolygon() {
            map.dragging.disable();
            new L.Draw.Polygon(map, {
                shapeOptions: styleGraphic,
                allowIntersection: false,
                drawError: {
                    color: '#e1e100',
                    message: '<strong>خطا!</strong> نمی‌توانید خطوط را قطع کنید!'
                }
            }).enable();
            selectBoxtool = true;
            document.getElementById('map').style.cursor = 'crosshair';
            console.log('ابزار انتخاب چندضلعی فعال شد');
        }
        function activeSelectMarker() {
            map.dragging.disable();
            new L.Draw.Marker(map, drawControl.options.marker).enable();
            selecttool = true;
            document.getElementById('map').style.cursor = 'crosshair';
        }
        function activeSelectPolyline() {
            map.dragging.disable();
            new L.Draw.Polyline(map, { shapeOptions: styleGraphic }).enable();
            selecttool = true;
            document.getElementById('map').style.cursor = 'crosshair';
        }

       
        function activeSelectPolygon() {
            map.dragging.disable();
            polygcreate = new L.Draw.Polygon(map, { shapeOptions: styleGraphic });//.enable();
            polygcreate.enable();
            selecttool = true;

            document.getElementById('map').style.cursor = 'crosshair';
        }

        function deactiveSelectBox() {
            if (selectBoxtool) {
                // دریافت لایه فعال
                var activeLayerId = $("#activeLayer").val();
                if (activeLayerId && activeLayerId.length > 1) {
                    var layerId = activeLayerId.substring(1);

                    // انجام انتخاب بر اساس geometry ترسیم شده
                    performSelectionByGraphic("selectBoxtool", 'esriSpatialRelIntersects', layerId);
                }

                // حذف لایه‌های انتخاب از نقشه
                var eLayers = editableLayers.getLayers();
                for (var i = 0; i < eLayers.length; i++) {
                    if (eLayers[i].tool == "selectBoxtool") {
                        editableLayers.removeLayer(eLayers[i]);
                    }
                }
            }

            map.dragging.enable();
            document.getElementById('map').style.cursor = '';
            selectBoxtool = false;
            selecttool = false;
        }
        
      
        ///------------ Go To XY
        function togglegotoxy()
        {
            $("#gotoxyContent").toggle();
            $("#searchcontent").hide();//$("#editcontent").hide();
        }

        var gotoxyMarker;
        function gotoxy() {
            try {
                if (gotoxyMarker) {
                    map.removeLayer(gotoxyMarker);
                    gotoxyMarker = null;
                }
                var x = $("#gotoxyX").val();
                var y = $("#gotoxyY").val();
                var coordinateSystem = $("#coordinateSystem").val();

                if (x !== "" && y !== "") {
                    var index = 0;
                    switch (coordinateSystem) {
                        case '38':
                            index = 2;
                            break;
                        case '39':
                            index = 3;
                            break;
                        case '40':
                            index = 4;
                            break;
                        case '41':
                            index = 5;
                            break;
                        default:
                    }

                    var prjInv = [x, y];
                    if (index > 0) {
                        prjInv = proj4(projectSystems[index]).inverse([x, y]);
                    }
                    gotoxyMarker = L.marker([prjInv[1], prjInv[0]]).addTo(map);
                    gotoxyMarker = gotoxyMarker.bindPopup("<button class='btn btn-success' onclick='map.removeLayer(gotoxyMarker); gotoxyMarker = null;'>حذف</button>");
                    map.setView([prjInv[1], prjInv[0]], 14);
                }
            }
            catch (err) {
                _Response("View-GoToXY", "Failed:" + err, null);
            }
        }
        //--------------------------Measurement
        var measureControl;

        function measurmentToggle()
        {
            if (!measureControl) {
                measureControl = L.control.measure({ position: 'bottomleft', primaryLengthUnit: 'meters', secondaryLengthUnit: undefined, primaryAreaUnit: 'sqmeters', localization: 'fa' });
                measureControl.addTo(map);
            }
            else {
                map.removeControl(measureControl);
                measureControl = null;
            }
        }

                //----------------------buffer

        function bufferwindows() {
            var url = URLBASE + 'GeoMap/_Buffer';
            $.ajax({
                url: url,
                cache: false,
                success: function (data) {
                    $("#dialog-Property .modal-content").html(data);
                    var modalElement = document.getElementById('dialog-Property');
                    var modal = new bootstrap.Modal(modalElement);
                    modal.show();

                    // Add event listener for when modal is hidden
                    modalElement.addEventListener('hidden.bs.modal', function () {
                        // Remove backdrop manually if it's still present
                        const backdrop = document.querySelector('.modal-backdrop');
                        if (backdrop) {
                            backdrop.remove();
                        }
                        // Also remove modal-open class from body if needed
                        document.body.classList.remove('modal-open');
                        // And remove inline styles that Bootstrap might have added
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';
                    });

                },
                error: function (data) {
                    console.error("Error loading modal content:", data);
                }
            });
            return false;
        }

        function buffer() {

            var leafletGeometry = identifiedFeature.toGeoJSON();
            var distance = $("#bufferValue").val();
            var geojson = turf.buffer(leafletGeometry, distance / 111325, 'meters');
            var layer = L.geoJSON(geojson.features[0], { style: styleGraphic2 });//.addTo(map);

            editableLayers.addLayer(layer);
            layer.tool = "selecttool";
            layer.type = "polygon";
            layer.buffer = true;
            layer.bindPopup('<a href="javascript:void(0)" class="md-btn md-btn-danger md-btn-mini md-btn-icon" onclick="editableLayers.removeLayer(' + layer._leaflet_id + ');"><i class="uk-icon-trash-o no_margin"></i></a>')
        }


        function bufferAllGraphic(distance, deleteprev, fillColor) {

            var tempLyr = []
            for (var i in editableLayers._layers) {
                var lyr = editableLayers._layers[i];
                if (lyr.tool == "selecttool")
                    tempLyr.push(lyr);
            }
            if (deleteprev)
                for (var i in tempLyr)
                    editableLayers.removeLayer(tempLyr[i]._leaflet_id);
            styleGraphic2.color = fillColor;
            styleGraphic2.fillColor = fillColor;

            for (var i in tempLyr) {
                var leafletGeometry = tempLyr[i].toGeoJSON();
                var geojson = turf.buffer(leafletGeometry, distance / 111325, 'meters');
                var layer = L.geoJSON(geojson.features[0], { style: styleGraphic2 });//.addTo(map);

                editableLayers.addLayer(layer);
                layer.tool = "selecttool";
                layer.buffer = true;
                layer.type = "polygon";
                layer.bindPopup('<a href="javascript:void(0)" class="md-btn md-btn-danger md-btn-mini md-btn-icon" onclick="editableLayers.removeLayer(' + layer._leaflet_id + ');"><i class="uk-icon-trash-o no_margin"></i></a>')
            }

        }
        function unionAllGraphic() {

            var tempLyr = []
            for (var i in editableLayers._layers) {
                var lyr = editableLayers._layers[i];
                if (lyr.tool == "selecttool")
                    tempLyr.push(lyr);
            }
            for (var i in tempLyr)
                editableLayers.removeLayer(tempLyr[i]._leaflet_id);

            var glayer;
            var cnt = 0;
            for (var i in tempLyr) {

                if (cnt == 0) {
                    if (tempLyr[i].toGeoJSON().features)
                        glayer = tempLyr[i].toGeoJSON().features[0];
                    else
                        glayer = tempLyr[i].toGeoJSON();
                }
                else {
                    if (tempLyr[i].toGeoJSON().features)
                        glayer = turf.union(glayer, tempLyr[i].toGeoJSON().features[0]);
                    else
                        glayer = turf.union(glayer, tempLyr[i].toGeoJSON());

                }

                cnt++;
            }
            console.log(glayer);
            var layer = L.geoJSON(glayer, { style: styleGraphic2 });//.addTo(map);

            editableLayers.addLayer(layer);
            layer.tool = "selecttool";
            layer.buffer = true;
            layer.type = "polygon";
            layer.bindPopup('<a href="javascript:void(0)" class="md-btn md-btn-danger md-btn-mini md-btn-icon" onclick="editableLayers.removeLayer(' + layer._leaflet_id + ');"><i class="uk-icon-trash-o no_margin"></i></a>')

            console.log(layer);
        }

        //------------------------

        var isGraphicSketch = false;
        var isGraphicSketchMeasurment = false;

        function setGraphicSketchClicked(e) { 
            if (isGraphicSketch) {
                $("#loading").show();
                L.esri.identifyFeatures({
                    url:  baseUrl + '/rest/services/map/mapserver'
                }).on(map).at(e.latlng).tolerance(0)
                    .layers('show:' + lyrCo[0].getLayers().toString())
                    .layerDef(JSON.stringify(lyrCo[0].getLayerDefs()))
                    .run(function (error, featureCollection) {
                     $("#loading").hide();
                    if (featureCollection.features.length > 0) {
                        var feature = featureCollection.features[0];
                        if (isGraphicSketchMeasurment /*&& feature.geometryType == 'esriGeometryPolygon'*/) {
                            var feats = {}; feats.type = "FeatureCollection"; feats.features = [];
                            for (var j = 0; j < feature.geometry.coordinates.length; j++)
                                for (var i = 1; i < feature.geometry.coordinates[j].length; i++) {



                                    var ff = {}; ff.type = "Feature";
                                    ff.geometry = {}; ff.type = "LineString"; ff.coordinates = [];
                                    ff.coordinates.push(feature.geometry.coordinates[j][i - 1]);
                                    ff.coordinates.push(feature.geometry.coordinates[j][i]);

                                    ff.properties = {}; ff.properties.length = L.GeometryUtil.length(L.polyline(ff.coordinates));
                                    feats.features.push(ff);
                                }


                            var srt = feats.features.sort(function (obj1, obj2) {
                                // Ascending: first age less than the previous
                                return obj2.properties.length - obj1.properties.length;
                            });
                            var ggg = L.featureGroup();
                            for (var i = 0; i < srt.length; i++) {
                                if (i > 20)
                                    srt[i].properties.length = undefined;
                                else {
                                    var x1 = srt[i].coordinates[0][1];
                                    var y1 = srt[i].coordinates[0][0];
                                    var x2 = srt[i].coordinates[1][1];
                                    var y2 = srt[i].coordinates[1][0];

                                    var a = (Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI) - 90;
                                    var Top = 0; var Left = 0;
                                    if (a <= 0 && a >= -90) { Left = (a * 25 / 90); }
                                    else if (a <= -90 && a >= -180) { Top = -((a + 90) * 20 / 90); Left = -((a + 180) * 25 / 90); }
                                    else if (a <= -180 && a >= -270) { Top = ((a + 270) * 20 / 90); }

                                    var ll = 0;
                                    var l = srt[i].properties.length;
                                    if (l > 1000) ll = (l / 1000).toFixed(2) + ' کیلومتر';
                                    else ll = (l).toFixed(2) + ' متر';
                                    
                                    var divHtml = '<i class="graphiclabels" style="transform: rotate(' + a.toFixed(0) + 'deg) !important;' + ("top:" + Top.toFixed(0) + "px;" + "left:" + Left.toFixed(0) + "px;") + '">' + ll.replace(".", "/") + '</i>';
                                    var featuregm = L.marker([(x1 + x2) / 2, (y1 + y2) / 2], {
                                        icon: L.divIcon({
                                            html: divHtml,
                                            iconSize: [20, 20],
                                            iconAnchor: [10, 20],
                                            className: 'myDivIcon'
                                        })
                                    });
                                    featuregm.divHtml = divHtml;
                                    ggg.addLayer(featuregm);

                                }
                            }
                            var layer2 = L.geoJSON(feats, {
                                onEachFeature: function (fe, la) {
                                    // la.setText(fe.properties.name, { offset: -5 });
                                    //la.bindPopup(fe.properties.name, { direction: 'left' });
                                    // if (fe.properties.length)
                                    //   la.bindTooltip(MeasureSystemUnit(fe.properties.length, 'length'), { permanent: true, direction: "top", className: "graphiclabels", offset: L.point(0, 0) });
                                    // streetLabelsRenderer._layers.push(la);
                                    //la.bindLabel("salaaam");//fe.properties.name/*, { noHide: true }*/);
                                },
                                style: styleGraphic
                            });
                            ggg.HasdivIcon = true;
                            layer2.addLayer(ggg);
                            editableLayers.addLayer(layer2);

                            layer2.tool = "selecttool";
                            layer2.type = "polygon";
                            layer2.bindPopup('<a href="javascript:void(0)" class="md-btn md-btn-danger md-btn-mini md-btn-icon" onclick="editableLayers.removeLayer(' + layer2._leaflet_id + ');"><i class="uk-icon-trash-o no_margin"></i></a>')
                            isGraphicSketchMeasurment = false;

                        }
                        else {
                            var layer = L.geoJSON(feature, { style: styleGraphic });
                            editableLayers.addLayer(layer);
                            layer.tool = "selecttool";
                            layer.type = "polygon";
                            layer.bindPopup('<a href="javascript:void(0)" class="md-btn md-btn-danger md-btn-mini md-btn-icon" onclick="editableLayers.removeLayer(' + layer._leaflet_id + ');"><i class="fa fa-trash"></i></a>')
                        }
                        document.getElementById('map').style.cursor = '';
                        isGraphicSketch = false;
                    }
                });
            }
        }


        function deleteAllGraphic() {
            for (var item in editableLayers._layers) {
                var layer = editableLayers._layers[item];
                if (layer.tool == "selecttool")
                    editableLayers.removeLayer(item);
            }

            if (identifiedFeature)
                map.removeLayer(identifiedFeature);

            if (highlighFeature)
                map.removeLayer(highlighFeature);

            markerLayers.clearLayers();
        }
        
        function ZoomToExtentLayer(layerId, selected) {
            try {
                // Get layer definitions from the current map layers
                var layerdef = {};
                for (var i = 0; i < lyrCo.length; i++) {
                    var def = lyrCo[i].getLayerDefs();
                    if (def[layerId]) {
                        layerdef[layerId] = def[layerId];
                        break;
                    }
                }

                // Show loading indicator
                $("#loading").show();

                // Make AJAX call to backend
                $.ajax({
                    type: 'POST',
                    url: baseUrl + '/rest/services/map/mapserver/Zoom',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        layers: layerId,
                        selected: selected,
                        layerDefs: JSON.stringify(layerdef)
                    }),
                    dataType: 'json',
                    success: function (response) {
                          
                        if (response.success && response.data) {
                          
                            // Convert coordinates from Web Mercator to WGS84
                            var prj = 'PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0],AUTHORITY["ESRI","102100"]]';
                            var xymin = proj4(prj).inverse([response.data.xMin, response.data.yMin]);
                            var xymax = proj4(prj).inverse([response.data.xMax, response.data.yMax]);
                            var bounds = [[xymin[1], xymin[0]], [xymax[1], xymax[0]]];
                            
                            // Fly to the bounds with animation
                            map.flyToBounds(bounds, {
                                duration: 1, // Duration in seconds
                                easeLinearity: 0.25
                            });
                        } else {
                            showToast(response.message || "خطا در بزرگنمایی", 'danger', 4000);
                        }
                    },
                    error: function (xhr, status, error) {
                        var errorMessage = "خطا در ارتباط با سرور";
                        try {
                            var response = JSON.parse(xhr.responseText);
                            errorMessage = response.message || errorMessage;
                        } catch (e) {
                            console.error("Error parsing error response:", e);
                        }
                        showToast(response.message || "خطا در بزرگنمایی", 'danger', 4000);
                    },
                    complete: function () {
                        $("#loading").hide();
                    }
                });
            } catch (err) {
                
                $("#loading").hide();
                showToast("در بزرگنمایی خطایی رخ داده است", 'danger', 4000);
            }
        }

        function highlightFeature(feature)
        {
            if (highlighFeature) {
                map.removeLayer(highlighFeature);
            }
            highlighFeature = L.geoJSON(feature, { style: highlightStyle }).addTo(map);
            map.fitBounds(highlighFeature.getBounds());
        }

        function flashFeature(feature)
        {
            if (highlighFeature) {
                map.removeLayer(highlighFeature);
            }
            highlighFeature = L.featureGroup();

            var colorselected = '#ff00ff';

            var featuregm;
            if (feature.geometry.type == "Point") {
                var gm = feature.geometry.coordinates;
                featuregm = L.circleMarker([gm[1], gm[0]], { "color": colorselected, opacity: 1, fillOpacity: 1 }).on('click', function () {  });
                highlighFeature.addLayer(featuregm);
            }
            else {
                featuregm = L.geoJSON(feature, { "color": colorselected, opacity: 1, fillOpacity: 1 }).on('click', function () { });
                highlighFeature.addLayer(featuregm);
            }

            highlighFeature.addTo(map);
            setTimeout(function () {
                map.removeLayer(highlighFeature);
                setTimeout(function () {
                    highlighFeature.addTo(map);
                    setTimeout(function () {
                        map.removeLayer(highlighFeature);
                        setTimeout(function () {
                            highlighFeature.addTo(map);
                            setTimeout(function () {
                                map.removeLayer(highlighFeature);
                            }, 600);
                        }, 600);
                    }, 600);
                }, 600);
            }, 600);
        }

        // === مدیریت انتخاب‌های کاربر ===

        /**
         * دریافت انتخاب‌های کاربر برای یک لایه
         */
        async function getUserSelections(layerId) {
            try {
                const response = await fetch(`/REST/GetUserSelections?layerId=${layerId}`);
                const data = await response.json();

                if (data.success) {
                    return data.selections;
                } else {
                    console.error('خطا در دریافت انتخاب‌های کاربر:', data.message);
                    return [];
                }
            } catch (error) {
                console.error('خطا در دریافت انتخاب‌های کاربر:', error);
                return [];
            }
        }

        /**
         * پاک کردن انتخاب‌های کاربر برای یک لایه
         */
        async function clearUserSelections(layerId) {
            try {
                const response = await fetch('/REST/ClearUserSelections', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ layerId: layerId })
                });

                const data = await response.json();

                if (data.success) {
                    console.log('انتخاب‌های کاربر پاک شد');
                    showToast('انتخاب‌های کاربر پاک شد', 'success', 3000);
                    // به‌روزرسانی نقشه
                    refreshMapLayer(layerId);
                    return true;
                } else {
                    console.error('خطا در پاک کردن انتخاب‌ها:', data.message);
                    showToast('خطا در پاک کردن انتخاب‌ها', 'danger', 3000);
                    return false;
                }
            } catch (error) {
                console.error('خطا در پاک کردن انتخاب‌ها:', error);
                showToast('خطا در پاک کردن انتخاب‌ها', 'danger', 3000);
                return false;
            }
        }

        /**
         * تغییر وضعیت هایلایت انتخاب‌های کاربر
         */
        async function toggleUserSelectionsHighlight(layerId, highlight) {
            try {
                const response = await fetch('/REST/ToggleUserSelectionsHighlight', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ layerId: layerId, highlight: highlight })
                });

                const data = await response.json();

                if (data.success) {
                    console.log(`وضعیت هایلایت ${highlight ? 'فعال' : 'غیرفعال'} شد`);
                    showToast(`وضعیت هایلایت ${highlight ? 'فعال' : 'غیرفعال'} شد`, 'success', 3000);
                    // به‌روزرسانی نقشه
                    refreshMapLayer(layerId);
                    return true;
                } else {
                    console.error('خطا در تغییر وضعیت هایلایت:', data.message);
                    showToast('خطا در تغییر وضعیت هایلایت', 'danger', 3000);
                    return false;
                }
            } catch (error) {
                console.error('خطا در تغییر وضعیت هایلایت:', error);
                showToast('خطا در تغییر وضعیت هایلایت', 'danger', 3000);
                return false;
            }
        }

        /**
         * به‌روزرسانی لایه نقشه
         */
        function refreshMapLayer(layerId) {
            try {
                // به‌روزرسانی لایه‌های ArcGIS
                if (typeof lyrCo !== 'undefined' && lyrCo.length > 0) {
                    for (var i = 0; i < lyrCo.length; i++) {
                        if (lyrCo[i] && typeof lyrCo[i].refresh === 'function') {
                            lyrCo[i].refresh();
                        }
                    }
                }

                console.log(`لایه ${layerId} به‌روزرسانی شد`);
            } catch (error) {
                console.error('خطا در به‌روزرسانی لایه:', error);
            }
        }

        /**
         * نمایش اطلاعات انتخاب‌های کاربر در کنسول
         */
        async function showUserSelectionsInfo(layerId) {
            const selections = await getUserSelections(layerId);

            if (selections.length > 0) {
                console.log(`انتخاب‌های کاربر برای لایه ${layerId}:`, selections);

                selections.forEach((selection, index) => {
                    console.log(`${index + 1}. تاریخ: ${selection.Date}, تعداد: ${selection.FeatureCount}, نوع: ${selection.SelectionType}`);
                });

                showToast(`${selections.length} انتخاب برای لایه ${layerId} یافت شد`, 'info', 3000);
            } else {
                console.log(`هیچ انتخابی برای لایه ${layerId} یافت نشد`);
                showToast(`هیچ انتخابی برای لایه ${layerId} یافت نشد`, 'warning', 3000);
            }
        }

        /**
         * اضافه کردن دکمه‌های مدیریت انتخاب به منوی کنتکست لایه
         */
        function addSelectionManagementToLayerMenu(layerId) {
            // این تابع باید بر اساس ساختار منوی لایه شما پیاده‌سازی شود
            console.log(`اضافه کردن منوی مدیریت انتخاب برای لایه ${layerId}`);
        }

        // === مدیریت نوار ابزار ===

        /**
         * باز کردن صفحه ابزار
         */
        function openToolPage(toolType) {
            const toolPage = document.getElementById('toolPage');
            const toolPageTitle = document.getElementById('toolPageTitle');
            const toolPageContent = document.getElementById('toolPageContent');

            // تنظیم عنوان بر اساس نوع ابزار
            const titles = {
                'quick-search': 'جستجوی سریع',
                'spatial-search': 'جستجوی مکانی',
                'descriptive-search': 'جستجوی توصیفی',
                'statistical-report': 'گزارش آماری',
                'report-builder': 'گزارش ساز',
                'import-shp': 'ایمپورت فایل SHP',
                'print-tools': 'ابزار چاپ'
            };

            toolPageTitle.textContent = titles[toolType] || 'ابزار';

            // نمایش loading
            toolPageContent.innerHTML = `
                <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">در حال بارگذاری...</span>
                    </div>
                </div>
            `;

            // نمایش صفحه ابزار
            toolPage.style.display = 'flex';

            // بارگذاری محتوا با AJAX
            loadToolContent(toolType);
        }

        /**
         * بستن صفحه ابزار
         */
        function closeToolPage() {
            const toolPage = document.getElementById('toolPage');
            toolPage.style.display = 'none';
        }

        /**
         * بارگذاری محتوای ابزار با AJAX
         */
        function loadToolContent(toolType) {
            const toolPageContent = document.getElementById('toolPageContent');

            // URL های مربوط به هر ابزار
            const urls = {
                'quick-search': '/GeoMap/QuickSearch',
                'spatial-search': '/GeoMap/SpatialSearch',
                'descriptive-search': '/GeoMap/DescriptiveSearch',
                'statistical-report': '/GeoMap/StatisticalReport',
                'report-builder': '/GeoMap/ReportBuilder',
                'import-shp': '/GeoMap/ImportShp',
                'print-tools': '/GeoMap/PrintTools'
            };

            const url = urls[toolType];
            if (!url) {
                toolPageContent.innerHTML = `
                    <div class="alert alert-warning m-3">
                        <i class="fa fa-exclamation-triangle"></i>
                        ابزار مورد نظر هنوز پیاده‌سازی نشده است.
                    </div>
                `;
                return;
            }

            // بارگذاری محتوا
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(html => {
                    toolPageContent.innerHTML = html;

                    // اجرای اسکریپت‌های موجود در محتوای بارگذاری شده
                    const scripts = toolPageContent.querySelectorAll('script');
                    scripts.forEach(script => {
                        const newScript = document.createElement('script');
                        if (script.src) {
                            newScript.src = script.src;
                        } else {
                            newScript.textContent = script.textContent;
                        }
                        document.head.appendChild(newScript);
                    });
                })
                .catch(error => {
                    console.error('Error loading tool content:', error);
                    toolPageContent.innerHTML = `
                        <div class="alert alert-danger m-3">
                            <i class="fa fa-exclamation-circle"></i>
                            خطا در بارگذاری محتوا: ${error.message}
                        </div>
                    `;
                });
        }

        // بستن صفحه ابزار با کلید Escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const toolPage = document.getElementById('toolPage');
                if (toolPage.style.display === 'flex') {
                    closeToolPage();
                }
            }
        });


        //-----------------------------------------------
        // BaseMap functions are now handled by BaseMapManager
        // Legacy functions kept for backward compatibility
        function changeBaseMapIndex(index) {
            if (baseMapManager) {
                baseMapManager.changeBaseMap(index);
            }
        }

        function changeBaseMap() {
            if (baseMapManager) {
                const current = baseMapManager.getCurrentBaseMap();
                const nextIndex = (current.index + 1) % baseMapManager.baseMaps.length;
                baseMapManager.changeBaseMap(nextIndex);
            }
        }
        //------------------------------------------------------
    </script>

    <!-- ===== اسکریپت مقداردهی کامپوننت‌ها ===== -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // مقداردهی کامپوننت‌های صفحه
            initializeGeoMapComponents();

            // اتصال رویدادهای کامپوننت‌ها
            bindGeoMapEvents();

            console.log('GeoMap page initialized with modern components');
        });

        /**
         * مقداردهی کامپوننت‌های GeoMap
         */
        function initializeGeoMapComponents() {
            // مقداردهی کامپوننت درخت لایه‌ها
            const layerTreeContainer = document.getElementById('layer-tree-container');
            if (layerTreeContainer && window.ComponentFactory) {
                window.layerTreeComponent = window.ComponentFactory.create('layer-tree', layerTreeContainer, {
                    onLayerToggle: function(layerId, isVisible, layerData) {
                        console.log('Layer toggled:', layerId, isVisible);
                        // اتصال به GeoMapCore
                        if (window.geoMapCore) {
                            window.geoMapCore.setLayerVisibility(layerId, isVisible);
                        }
                    },
                    onLayerSelect: function(layerId, layerData) {
                        console.log('Layer selected:', layerId);
                        // بروزرسانی لایه فعال
                        updateActiveLayer(layerId, layerData);
                    },
                    onLayerProperties: function(layerId) {
                        console.log('Show layer properties:', layerId);
                        if (window.geoMapCore) {
                            window.geoMapCore.showLayerProperties(layerId);
                        }
                    }
                });
            }

            // مقداردهی کامپوننت ابزارهای جستجو (در صورت وجود)
            const searchToolsContainer = document.getElementById('search-tools-container');
            if (searchToolsContainer && window.ComponentFactory) {
                window.searchToolsComponent = window.ComponentFactory.create('search-tools', searchToolsContainer, {
                    onSearchComplete: function(searchType, results) {
                        console.log('Search completed:', searchType, results);
                        // نمایش نتایج روی نقشه
                        if (window.geoMapCore) {
                            window.geoMapCore.displaySearchResults(results, searchType);
                        }
                    },
                    onResultSelect: function(result, index) {
                        console.log('Search result selected:', result);
                        // زوم به نتیجه انتخاب شده
                        if (window.geoMapCore && result.geometry) {
                            window.geoMapCore.zoomToGeometry(result.geometry);
                        }
                    }
                });
            }
        }

        /**
         * اتصال رویدادهای کامپوننت‌ها
         */
        function bindGeoMapEvents() {
            // رویداد تغییر اندازه پنجره
            window.addEventListener('resize', function() {
                if (window.geoMapCore && window.geoMapCore.map) {
                    // بروزرسانی اندازه نقشه
                    setTimeout(() => {
                        window.geoMapCore.map.invalidateSize();
                    }, 100);
                }
            });

            // رویداد تغییر وضعیت sidebar
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                            // بروزرسانی اندازه نقشه هنگام تغییر sidebar
                            setTimeout(() => {
                                if (window.geoMapCore && window.geoMapCore.map) {
                                    window.geoMapCore.map.invalidateSize();
                                }
                            }, 350); // منتظر اتمام انیمیشن
                        }
                    });
                });

                observer.observe(sidebar, { attributes: true });
            }
        }

        /**
         * بروزرسانی لایه فعال
         */
        function updateActiveLayer(layerId, layerData) {
            const activeLayerInput = document.getElementById('activeLayerLable');
            const activeLayerHidden = document.getElementById('activeLayer');

            if (activeLayerInput && layerData) {
                activeLayerInput.value = layerData.aliasName || layerData.name;
            }

            if (activeLayerHidden) {
                activeLayerHidden.value = layerId;
            }
        }

        /**
         * نمایش ابزار جستجو در modal
         */
        function showSearchToolModal(searchType) {
            // ایجاد modal برای ابزارهای جستجو
            const modalHtml = `
                <div class="modal fade" id="searchToolModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">ابزارهای جستجو</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div id="search-tools-container" data-component="search-tools" data-default-search-type="${searchType}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // حذف modal قبلی در صورت وجود
            const existingModal = document.getElementById('searchToolModal');
            if (existingModal) {
                existingModal.remove();
            }

            // اضافه کردن modal جدید
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // مقداردهی کامپوننت جستجو
            const searchContainer = document.getElementById('search-tools-container');
            if (searchContainer && window.ComponentFactory) {
                const searchComponent = window.ComponentFactory.create('search-tools', searchContainer, {
                    defaultSearchType: searchType,
                    onSearchComplete: function(searchType, results) {
                        console.log('Search completed in modal:', searchType, results);
                        // بستن modal و نمایش نتایج
                        const modal = bootstrap.Modal.getInstance(document.getElementById('searchToolModal'));
                        if (modal) {
                            modal.hide();
                        }

                        if (window.geoMapCore) {
                            window.geoMapCore.displaySearchResults(results, searchType);
                        }
                    }
                });
            }

            // نمایش modal
            const modal = new bootstrap.Modal(document.getElementById('searchToolModal'));
            modal.show();
        }

        // تابع سراسری برای استفاده در onclick handlers
        window.showSearchTool = showSearchToolModal;

        // ========================================
        // مدیریت نقشه‌های ذخیره شده
        // ========================================

        /**
         * بارگذاری لیست نقشه‌های ذخیره شده
         */
        async function loadMapViews() {
            try {
                showToast('در حال بارگذاری نقشه‌ها...', 'info', 2000);

                const response = await fetch('/GeoMap/GetAllMapViews');
                const result = await response.json();

                if (result.success) {
                    displayMapViews(result.data);
                } else {
                    showToast(result.message || 'خطا در بارگذاری نقشه‌ها', 'danger', 3000);
                    displayMapViews([]);
                }
            } catch (error) {
                console.error('Error loading map views:', error);
                showToast('خطا در بارگذاری نقشه‌ها', 'danger', 3000);
                displayMapViews([]);
            }
        }

        /**
         * نمایش لیست نقشه‌های ذخیره شده
         */
        function displayMapViews(mapViews) {
            const container = document.getElementById('map-views-list');

            if (!mapViews || mapViews.length === 0) {
                container.innerHTML = `
                    <div class="no-views-message">
                        <i class="fa fa-map-o"></i><br>
                        هیچ نقشه ذخیره شده‌ای یافت نشد
                    </div>
                `;
                return;
            }

            // گروه‌بندی بر اساس GroupName
            const groupedViews = mapViews.reduce((groups, view) => {
                const group = view.GroupName || 'بدون گروه';
                if (!groups[group]) {
                    groups[group] = [];
                }
                groups[group].push(view);
                return groups;
            }, {});

            let html = '';

            Object.keys(groupedViews).sort().forEach(groupName => {
                html += `<div class="view-group">`;
                html += `<h6 class="view-group-title">${groupName}</h6>`;

                groupedViews[groupName].forEach(view => {
                    html += createViewItemHtml(view);
                });

                html += `</div>`;
            });

            container.innerHTML = html;
        }

        /**
         * ایجاد HTML برای یک آیتم نقشه
         */
        function createViewItemHtml(view) {
            const isPublic = view.Public ? '<i class="fa fa-globe text-success" title="عمومی"></i>' : '<i class="fa fa-lock text-muted" title="خصوصی"></i>';
            const hasZoom = view.Zoom ? '<i class="fa fa-search-plus text-info" title="دارای zoom"></i>' : '';

            return `
                <div class="view-item" data-view-id="${view.ID}">
                    <div class="view-item-header">
                        <h6 class="view-item-title">${view.Name}</h6>
                        <div class="view-item-actions">
                            <button type="button" class="btn btn-primary" onclick="loadMapView(${view.ID})" title="بارگذاری نقشه">
                                <i class="fa fa-eye"></i>
                            </button>
                            <button type="button" class="btn btn-warning" onclick="editMapView(${view.ID})" title="ویرایش">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteMapView(${view.ID})" title="حذف">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="view-item-meta">
                        ${isPublic} ${hasZoom} توسط: ${view.UserName} | تاریخ: ${view.Date}
                    </div>
                    ${view.Description ? `<div class="view-item-description">${view.Description}</div>` : ''}
                </div>
            `;
        }

        /**
         * نمایش اطلاعات انتخاب‌ها
         */
        async function showSelectionInfo() {
            try {
                var activeLayerId = $("#activeLayer").val();
                if (!activeLayerId || activeLayerId.length <= 1) {
                    showToast('لطفاً ابتدا یک لایه را انتخاب کنید', 'warning', 3000);
                    return;
                }

                var layerId = activeLayerId.substring(1);
                var selections = await getUserSelections(layerId);

                if (selections.length === 0) {
                    showToast('هیچ انتخابی برای این لایه یافت نشد', 'info', 3000);
                    return;
                }

                var selection = selections[0];
                var oidCount = selection.OIDs ? selection.OIDs.split(',').length : 0;

                var infoHtml = `
                    <div class="selection-info p-3">
                        <h5 class="mb-3">اطلاعات انتخاب‌ها</h5>
                        <div class="row">
                            <div class="col-12">
                                <p><strong>لایه:</strong> ${selection.Table}</p>
                                <p><strong>تعداد عارضه‌های انتخاب شده:</strong> <span class="badge badge-primary">${oidCount}</span></p>
                                <p><strong>تاریخ انتخاب:</strong> ${selection.Date}</p>
                                <p><strong>وضعیت هایلایت:</strong>
                                    <span class="badge ${selection.IsHighlight ? 'badge-success' : 'badge-secondary'}">
                                        ${selection.IsHighlight ? 'فعال' : 'غیرفعال'}
                                    </span>
                                </p>
                                <p><strong>نوع انتخاب:</strong> ${selection.SelectionType || 'هندسی'}</p>
                            </div>
                        </div>
                        <div class="mt-3 text-center">
                            <button class="btn btn-sm btn-primary me-2" onclick="toggleUserSelectionsHighlight('${layerId}', ${!selection.IsHighlight}); $('#dialog-Property').modal('hide');">
                                <i class="fa fa-eye"></i> ${selection.IsHighlight ? 'غیرفعال کردن' : 'فعال کردن'} هایلایت
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="clearUserSelections('${layerId}'); $('#dialog-Property').modal('hide');">
                                <i class="fa fa-trash"></i> حذف انتخاب‌ها
                            </button>
                        </div>
                    </div>
                `;

                // نمایش در modal
                $("#dialog-Property .modal-content").html(`
                    <div class="modal-header">
                        <h5 class="modal-title">اطلاعات انتخاب‌ها</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${infoHtml}
                    </div>
                `);

                var modalElement = document.getElementById('dialog-Property');
                var modal = new bootstrap.Modal(modalElement);
                modal.show();

            } catch (error) {
                console.error('خطا در نمایش اطلاعات انتخاب‌ها:', error);
                showToast('خطا در نمایش اطلاعات انتخاب‌ها', 'danger', 3000);
            }
        }

        // تابع سراسری برای استفاده در onclick handlers
        window.showSelectionInfo = showSelectionInfo;

        /**
         * بارگذاری یک نقشه خاص
         */
        async function loadMapView(viewId) {
            try {
                showToast('در حال بارگذاری نقشه...', 'info', 2000);

                const response = await fetch(`/GeoMap/GetMapView?id=${viewId}`);
                const result = await response.json();

                if (result.success) {
                    const mapView = result.data;

                    // بارگذاری لایه‌ها
                    if (mapView.Layers) {
                        try {
                            const layers = JSON.parse(mapView.Layers);
                            await applyMapViewLayers(layers);
                        } catch (e) {
                            console.warn('خطا در پارس کردن لایه‌ها:', e);
                        }
                    }

                    // بارگذاری گرافیک‌ها
                    if (mapView.Graphic) {
                        try {
                            const graphics = JSON.parse(mapView.Graphic);
                            await applyMapViewGraphics(graphics);
                        } catch (e) {
                            console.warn('خطا در پارس کردن گرافیک‌ها:', e);
                        }
                    }

                    // تنظیم extent
                    if (mapView.Zoom && mapView.BBX) {
                        try {
                            const bbox = JSON.parse(mapView.BBX);
                            map.fitBounds([[bbox.ymin, bbox.xmin], [bbox.ymax, bbox.xmax]]);
                        } catch (e) {
                            console.warn('خطا در تنظیم extent:', e);
                        }
                    }

                    // تغییر نقشه پایه
                    if (mapView.BaseMapID) {
                        await changeBaseMap(mapView.BaseMapID);
                    }

                    showToast(`نقشه "${mapView.Name}" بارگذاری شد`, 'success', 3000);

                    // انتخاب آیتم در لیست
                    document.querySelectorAll('.view-item').forEach(item => {
                        item.classList.remove('selected');
                    });
                    document.querySelector(`[data-view-id="${viewId}"]`)?.classList.add('selected');

                } else {
                    showToast(result.message || 'خطا در بارگذاری نقشه', 'danger', 3000);
                }
            } catch (error) {
                console.error('Error loading map view:', error);
                showToast('خطا در بارگذاری نقشه', 'danger', 3000);
            }
        }

        /**
         * اعمال لایه‌های نقشه
         */
        async function applyMapViewLayers(layers) {
            // پیاده‌سازی بر اساس ساختار لایه‌های موجود
            console.log('Applying layers:', layers);
            // TODO: پیاده‌سازی بارگذاری لایه‌ها
        }

        /**
         * اعمال گرافیک‌های نقشه
         */
        async function applyMapViewGraphics(graphics) {
            // پاک کردن گرافیک‌های قبلی
            clearAllGraphics();

            // اضافه کردن گرافیک‌های جدید
            console.log('Applying graphics:', graphics);
            // TODO: پیاده‌سازی بارگذاری گرافیک‌ها
        }

        /**
         * ویرایش نقشه
         */
        function editMapView(viewId) {
            showSaveMapViewDialog(viewId);
        }

        /**
         * حذف نقشه
         */
        async function deleteMapView(viewId) {
            if (!confirm('آیا از حذف این نقشه اطمینان دارید؟')) {
                return;
            }

            try {
                const formData = new FormData();
                formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

                const response = await fetch(`/GeoMap/DeleteMapView?id=${viewId}`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showToast(result.message, 'success', 3000);
                    await loadMapViews(); // بروزرسانی لیست
                } else {
                    showToast(result.message || 'خطا در حذف نقشه', 'danger', 3000);
                }
            } catch (error) {
                console.error('Error deleting map view:', error);
                showToast('خطا در حذف نقشه', 'danger', 3000);
            }
        }

        /**
         * بروزرسانی لیست نقشه‌ها
         */
        function refreshMapViews() {
            loadMapViews();
        }

        /**
         * نمایش دیالوگ ذخیره نقشه
         */
        function showSaveMapViewDialog(editId = null) {
            const isEdit = editId !== null;
            const title = isEdit ? 'ویرایش نقشه' : 'ذخیره نقشه جدید';

            const dialogHtml = `
                <div class="modal fade" id="saveMapViewModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="saveMapViewForm">
                                    <input type="hidden" name="Id" value="${editId || 0}">
                                    <input type="hidden" name="__RequestVerificationToken" value="${document.querySelector('input[name="__RequestVerificationToken"]').value}">

                                    <div class="mb-3">
                                        <label for="mapViewName" class="form-label">نام نقشه *</label>
                                        <input type="text" class="form-control" id="mapViewName" name="Name" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="mapViewDescription" class="form-label">توضیحات</label>
                                        <textarea class="form-control" id="mapViewDescription" name="Description" rows="3"></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label for="mapViewGroup" class="form-label">گروه</label>
                                        <input type="text" class="form-control" id="mapViewGroup" name="GroupName" placeholder="نام گروه (اختیاری)">
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="saveCurrentExtent" name="Zoom">
                                            <label class="form-check-label" for="saveCurrentExtent">
                                                ذخیره محدوده فعلی نقشه
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="makePublic" name="Public">
                                            <label class="form-check-label" for="makePublic">
                                                نقشه عمومی (قابل مشاهده برای همه کاربران)
                                            </label>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                                <button type="button" class="btn btn-primary" onclick="saveMapView()">
                                    <i class="fa fa-save"></i> ${isEdit ? 'بروزرسانی' : 'ذخیره'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // حذف modal قبلی اگر وجود دارد
            const existingModal = document.getElementById('saveMapViewModal');
            if (existingModal) {
                existingModal.remove();
            }

            // اضافه کردن modal جدید
            document.body.insertAdjacentHTML('beforeend', dialogHtml);

            // نمایش modal
            const modal = new bootstrap.Modal(document.getElementById('saveMapViewModal'));
            modal.show();

            // اگر در حالت ویرایش است، اطلاعات را بارگذاری کن
            if (isEdit) {
                loadMapViewForEdit(editId);
            }
        }

        /**
         * بارگذاری اطلاعات نقشه برای ویرایش
         */
        async function loadMapViewForEdit(viewId) {
            try {
                const response = await fetch(`/GeoMap/GetMapView?id=${viewId}`);
                const result = await response.json();

                if (result.success) {
                    const mapView = result.data;
                    document.getElementById('mapViewName').value = mapView.Name || '';
                    document.getElementById('mapViewDescription').value = mapView.Description || '';
                    document.getElementById('mapViewGroup').value = mapView.GroupName || '';
                    document.getElementById('saveCurrentExtent').checked = mapView.Zoom || false;
                    document.getElementById('makePublic').checked = mapView.Public || false;
                }
            } catch (error) {
                console.error('Error loading map view for edit:', error);
                showToast('خطا در بارگذاری اطلاعات نقشه', 'danger', 3000);
            }
        }

        /**
         * ذخیره نقشه
         */
        async function saveMapView() {
            try {
                const form = document.getElementById('saveMapViewForm');
                const formData = new FormData(form);

                // اضافه کردن اطلاعات لایه‌ها
                const layersData = getCurrentLayersState();
                formData.append('Layers', JSON.stringify(layersData));

                // اضافه کردن گرافیک‌ها
                const graphicsData = getCurrentGraphicsState();
                formData.append('Graphic', JSON.stringify(graphicsData));

                // اضافه کردن bounding box اگر انتخاب شده
                if (document.getElementById('saveCurrentExtent').checked) {
                    const bounds = map.getBounds();
                    const bbox = {
                        xmin: bounds.getWest(),
                        ymin: bounds.getSouth(),
                        xmax: bounds.getEast(),
                        ymax: bounds.getNorth()
                    };
                    formData.append('BBX', JSON.stringify(bbox));
                }

                // اضافه کردن BaseMapID
                if (typeof currentBaseMapId !== 'undefined') {
                    formData.append('BaseMapID', currentBaseMapId);
                }

                const response = await fetch('/GeoMap/SaveMapView', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showToast(result.message, 'success', 3000);

                    // بستن modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('saveMapViewModal'));
                    modal.hide();

                    // بروزرسانی لیست
                    await loadMapViews();
                } else {
                    showToast(result.message || 'خطا در ذخیره نقشه', 'danger', 3000);
                }
            } catch (error) {
                console.error('Error saving map view:', error);
                showToast('خطا در ذخیره نقشه', 'danger', 3000);
            }
        }

        /**
         * دریافت وضعیت فعلی لایه‌ها
         */
        function getCurrentLayersState() {
            // TODO: پیاده‌سازی بر اساس ساختار لایه‌های موجود
            return {};
        }

        /**
         * دریافت وضعیت فعلی گرافیک‌ها
         */
        function getCurrentGraphicsState() {
            const graphics = [];

            if (editableLayers) {
                editableLayers.eachLayer(function(layer) {
                    const geoJson = layer.toGeoJSON();
                    graphics.push({
                        type: geoJson.geometry.type,
                        coordinates: geoJson.geometry.coordinates,
                        properties: geoJson.properties || {}
                    });
                });
            }

            return graphics;
        }

        /**
         * مقداردهی اولیه نقشه‌های ذخیره شده
         */
        function initializeMapViews() {
            // بارگذاری نقشه‌ها هنگام تغییر به تب نقشه‌ها
            document.getElementById('views-tab').addEventListener('shown.bs.tab', function() {
                loadMapViews();
            });

            // تنظیم event listener برای تب‌ها
            const tabTriggerList = [].slice.call(document.querySelectorAll('#sidebarTabs button'));
            tabTriggerList.forEach(function (tabTriggerEl) {
                new bootstrap.Tab(tabTriggerEl);
            });

            console.log('Map views initialized');
        }

        // توابع سراسری برای استفاده در onclick handlers
        window.loadMapViews = loadMapViews;
        window.loadMapView = loadMapView;
        window.editMapView = editMapView;
        window.deleteMapView = deleteMapView;
        window.refreshMapViews = refreshMapViews;
        window.showSaveMapViewDialog = showSaveMapViewDialog;
        window.saveMapView = saveMapView;

        /**
         * مقداردهی اولیه ابزارهای اندازه‌گیری
         */
        function initializeMeasurementTools() {
            const container = document.getElementById('measurement-tools-container');
            if (container && window.ComponentFactory) {
                try {
                    const measurementTools = window.ComponentFactory.create('measurement-tools', container, {
                        units: ['meters', 'kilometers', 'feet', 'miles'],
                        defaultUnit: 'meters',
                        coordinateSystems: ['geographic', 'mercator', 'utm38', 'utm39', 'utm40', 'utm41'],
                        defaultCoordinateSystem: 'geographic',
                        precision: 2,
                        onMeasurementStart: function(tool) {
                            console.log('Measurement started:', tool);
                            resetTools(); // Reset other tools
                        },
                        onMeasurementEnd: function(tool, result) {
                            console.log('Measurement ended:', tool, result);
                        },
                        onCoordinateClick: function(coords, system) {
                            console.log('Coordinate clicked:', coords, system);
                        }
                    });

                    // Store reference globally
                    window.measurementTools = measurementTools;

                    console.log('Measurement tools initialized successfully');
                } catch (error) {
                    console.error('Error initializing measurement tools:', error);
                }
            } else {
                console.warn('Measurement tools container or ComponentFactory not found');
            }
        }

        // توابع سراسری برای استفاده در onclick handlers
        window.initializeMeasurementTools = initializeMeasurementTools;

        /**
         * مقداردهی اولیه ابزارهای تحلیل مکانی
         */
        function initializeSpatialAnalysis() {
            const container = document.getElementById('spatial-analysis-container');
            if (container && window.ComponentFactory) {
                try {
                    const spatialAnalysis = window.ComponentFactory.create('spatial-analysis', container, {
                        bufferUnits: ['meters', 'kilometers', 'feet', 'miles'],
                        defaultBufferUnit: 'meters',
                        defaultBufferDistance: 100,
                        onBufferCreated: function(layer, original) {
                            console.log('Buffer created:', layer, original);
                        },
                        onUnionCreated: function(layer, originals) {
                            console.log('Union created:', layer, originals);
                        },
                        onAnalysisStart: function(tool) {
                            console.log('Analysis started:', tool);
                            resetTools(); // Reset other tools
                        },
                        onAnalysisEnd: function(tool, result) {
                            console.log('Analysis ended:', tool, result);
                        }
                    });

                    // Store reference globally
                    window.spatialAnalysisTools = spatialAnalysis;

                    console.log('Spatial analysis tools initialized successfully');
                } catch (error) {
                    console.error('Error initializing spatial analysis tools:', error);
                }
            } else {
                console.warn('Spatial analysis container or ComponentFactory not found');
            }
        }

        /**
         * مقداردهی اولیه ابزار رفتن به مختصات
         */
        function initializeGoToXY() {
            const container = document.getElementById('goto-xy-container');
            if (container && window.ComponentFactory) {
                try {
                    const gotoXY = window.ComponentFactory.create('goto-xy', container, {
                        coordinateSystems: ['geographic', 'mercator', 'utm38', 'utm39', 'utm40', 'utm41'],
                        defaultCoordinateSystem: 'geographic',
                        zoomLevel: 15,
                        showMarker: true,
                        animateToLocation: true,
                        markerDuration: 5000,
                        onLocationFound: function(latlng, coordinates) {
                            console.log('Location found:', latlng, coordinates);
                        },
                        onLocationError: function(error) {
                            console.error('Location error:', error);
                        }
                    });

                    // Store reference globally
                    window.gotoXYComponent = gotoXY;

                    console.log('GoTo XY component initialized successfully');
                } catch (error) {
                    console.error('Error initializing GoTo XY component:', error);
                }
            } else {
                console.warn('GoTo XY container or ComponentFactory not found');
            }
        }

        /**
         * مقداردهی اولیه ابزارهای ترسیم
         */
        function initializeDrawingTools() {
            const container = document.getElementById('drawing-tools-container');
            if (container && window.ComponentFactory) {
                try {
                    const drawingTools = window.ComponentFactory.create('drawing-tools', container, {
                        drawingModes: ['point', 'line', 'polygon', 'circle', 'rectangle', 'freehand'],
                        defaultDrawingMode: 'point',
                        enableSnapping: true,
                        snapDistance: 10,
                        showTooltips: true,
                        allowEdit: true,
                        onDrawStart: function(type) {
                            console.log('Drawing started:', type);
                            resetTools(); // Reset other tools
                        },
                        onDrawEnd: function(layer, type) {
                            console.log('Drawing ended:', layer, type);
                        },
                        onDrawCancel: function() {
                            console.log('Drawing cancelled');
                        },
                        onFeatureEdit: function(layers) {
                            console.log('Features edited:', layers);
                        },
                        onFeatureDelete: function(layers) {
                            console.log('Features deleted:', layers);
                        }
                    });

                    // Store reference globally
                    window.drawingTools = drawingTools;

                    console.log('Drawing tools initialized successfully');
                } catch (error) {
                    console.error('Error initializing drawing tools:', error);
                }
            } else {
                console.warn('Drawing tools container or ComponentFactory not found');
            }
        }

        /**
         * مقداردهی اولیه ابزارهای خصوصیات
         */
        function initializePropertyTools() {
            const container = document.getElementById('property-tools-container');
            if (container && window.ComponentFactory) {
                try {
                    const propertyTools = window.ComponentFactory.create('property-tools', container, {
                        showGeometry: true,
                        showAttributes: true,
                        showCoordinates: true,
                        showArea: true,
                        showLength: true,
                        coordinateFormat: 'decimal',
                        areaUnit: 'square_meters',
                        lengthUnit: 'meters',
                        maxAttributeLength: 100,
                        onPropertyShow: function() {
                            console.log('Property panel shown');
                        },
                        onPropertyHide: function() {
                            console.log('Property panel hidden');
                        },
                        onPropertyUpdate: function(feature, changes) {
                            console.log('Property updated:', feature, changes);
                        },
                        onFeatureSelect: function(feature) {
                            console.log('Feature selected:', feature);
                            // Set identified feature for other tools
                            if (window.spatialAnalysisTools) {
                                window.spatialAnalysisTools.setIdentifiedFeature(feature);
                            }
                        },
                        onFeatureDeselect: function(feature) {
                            console.log('Feature deselected:', feature);
                        }
                    });

                    // Store reference globally
                    window.propertyTools = propertyTools;

                    console.log('Property tools initialized successfully');
                } catch (error) {
                    console.error('Error initializing property tools:', error);
                }
            } else {
                console.warn('Property tools container or ComponentFactory not found');
            }
        }

        // توابع سراسری برای استفاده در onclick handlers
        window.initializeSpatialAnalysis = initializeSpatialAnalysis;
        window.initializeGoToXY = initializeGoToXY;
        window.initializeDrawingTools = initializeDrawingTools;
        window.initializePropertyTools = initializePropertyTools;
    </script>
}