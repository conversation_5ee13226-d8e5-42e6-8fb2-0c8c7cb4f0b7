﻿@using System.Text.Json
@model IEnumerable<BaseGIS.Core.Entities.GroupInfo>
@{
    ViewData["Title"] = "نقشه";
    Layout = "~/Views/Shared/_LayoutMap.cshtml";

    string treeData = ViewBag.TreeData;
    var Theme = ViewBag.Theme;  
    Dictionary<string, string> Themes = new Dictionary<string, string>();  

    if (Theme != null && Theme != "")
    {
        var tms = Theme.Split(',');
        for (int i = 0; i < tms.Length; i++)
        {
            var tms1 = tms[i].Split(':');
            if (tms1.Length > 1)
            {
                // Store the HTML color string directly
                Themes.Add(tms1[0].ToLower(), tms1[1]);
            }
        }
    }
}

@section Styles {
    <link href="/lib/jqueryui/themes/base/jquery-ui.min.css" rel="stylesheet" />
    <link href="~/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css" rel="stylesheet" />
    <link href="~/lib/ion-rangeslider/css/ion.rangeslider.min.css" rel="stylesheet" />
    <link href="~/lib/leaflet-dialog/Leaflet.Dialog.css" rel="stylesheet" />
    <link href="~/lib/leaflet-measure/leaflet-measure.css" rel="stylesheet" />
    <style>

        .map-container {
            display: flex;
            height: calc(100vh - 62px);
            direction: rtl;
        }

        #sidebar {
            width: 250px;
            height: 100%;
            transition: width 0.35s ease;
            overflow: hidden;
            background: #fff;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--primary-color);
        }

            #sidebar.hidden {
                width: 0;
                display: block;
            }

        .sidebar-wrapper {
            width: 100%;
            height: 100%;
        }

        .active-layer-container {
            padding: 6px;
            border-bottom: 1px solid #ddd;
            background: #f8f9fa;
        }

        #activeLayerInput {
            background-color: #fff;
            cursor: default;
        }

        #tree {
            height: 100%;
            max-height: calc(100vh - 150px); /* تنظیم ارتفاع با توجه به Textbox */
            overflow-y: auto;
            padding: 6px;
            background-color: var(--background-color);
        }

        #map {
            width: calc(100% - 250px);
            height: 100%;
            position: relative;
            order: 1;
        }

            #map.full-width {
                width: 100%;
            }

        /* دکمه سایدبار حذف شد - حالا از header استفاده می‌شود */

        /* استایل‌های نوار ابزار */
        #standardToolbar {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

       /*  #standardToolbar .btn {
            width: 40px;
            height: 40px;
            border: 1px solid #ccc;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        #standardToolbar .btn:hover {
            background: rgba(0, 123, 255, 0.1);
            border-color: #007bff;
            color: #007bff;
        } */

        /* استایل dropdown ها */
        .dropdown-menu {
            display: none !important;
            position: absolute !important;
            left: 45px !important;
            top: 0 !important;
            background: rgba(255, 255, 255, 0.95) !important;
            border: 1px solid #ccc !important;
            border-radius: 4px !important;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
            padding: 4px !important;
            z-index: 1000 !important;
        }

        .dropdown-menu.show {
            display: flex !important;
            flex-direction: row !important;
            gap: 2px !important;
        }

        .dropdown-menu .btn {
            width: 36px !important;
            height: 36px !important;
            font-size: 14px !important;
            margin: 0 !important;
        }

        /* استایل‌های دیالوگ‌ها */
        .measurement-dialog, .gotoxy-dialog {
            font-family: 'Vazir', sans-serif;
            direction: rtl;
            text-align: right;
        }

        .measurement-dialog .btn-group .btn,
        .gotoxy-dialog .btn-group .btn {
            border-radius: 0;
        }

        .measurement-dialog .btn-group .btn:first-child,
        .gotoxy-dialog .btn-group .btn:first-child {
            border-top-right-radius: 0.375rem;
            border-bottom-right-radius: 0.375rem;
        }

        .measurement-dialog .btn-group .btn:last-child,
        .gotoxy-dialog .btn-group .btn:last-child {
            border-top-left-radius: 0.375rem;
            border-bottom-left-radius: 0.375rem;
        }

        .measurement-marker, .gotoxy-marker {
            background: transparent;
            border: none;
        }

        /* استایل نشانگرهای اندازه‌گیری */
        .leaflet-div-icon.measurement-marker,
        .leaflet-div-icon.gotoxy-marker {
            background: transparent !important;
            border: none !important;
        }

        /* استایل دیالوگ Leaflet */
        .leaflet-control-dialog {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
            overflow: hidden;
        }

        .leaflet-control-dialog .leaflet-control-dialog-contents {
            padding: 15px;
            background: white;
        }

        .leaflet-control-dialog .leaflet-control-dialog-grabber {
            background: #007bff;
            height: 30px;
            cursor: move;
        }

        .leaflet-control-dialog .leaflet-control-dialog-close {
            background: #dc3545;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            font-size: 16px;
            cursor: pointer;
        }

        .leaflet-control-dialog .leaflet-control-dialog-close:hover {
            background: #c82333;
        }


        span.fancytree-node.custom > span.fancytree-title {
            color: maroon;
            font-family: "SamanFont";
        }

        span.fancytree-node.plan > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/Gallery16.png");
        }

        span.fancytree-node.group > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/group.png");
        }

        span.fancytree-node.polyline > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/polyline.png");
        }

        span.fancytree-node.point > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/point.png");
        }

        span.fancytree-node.polygon > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/polygon.png");
        }
         
        .fancytree-container,
        span.fancytree-focused span.fancytree-title {
            outline: 0 !important;
        }
         
        @@media (max-width: 768px) {
            #sidebar {
                width: 200px;
            }

            #map {
                width: calc(100% - 200px);
            }

            #sidebar.hidden + #map {
                width: 100%;
            }
        }
    </style>
    <style>

        .btnView {
            text-decoration: none;
            width: 44px !important;
            height: 40px;
        @if (Themes.Keys.Contains("view_backcolor"))
        {
            var color = Themes["view_backcolor"];
            <text> background-color: rgba(255,255,255,.5);
                border: 1px solid @(color);
            </text>
        }
        else
        {
            <text> background-color: rgba(0,176,240,.5);
                border: 1px solid rgb(0,176,240);
            </text>
        }
        @if (Themes.Keys.Contains("view_color"))
        {
            var color = Themes["view_color"];
            <text> color: @(color);
            </text>
        }
        else
        {
            <text> color: white;
            </text>
        }
        }


            .btnView:hover {
                color: #ce2a21;
                border: 1px solid;
        @if (Themes.Keys.Contains("view_backcolor"))
        {
            var color = Themes["view_backcolor"];
            <text> 
            box-shadow: inset 0 0 20px @(color) 80, 0 0 20px @(color) 33;
            text-shadow: 1px 1px 2px @(color);
            </text>
        }
        else
        {
            <text> 
            box-shadow: inset 0 0 20px rgba(0,176,240, .5), 0 0 20px rgba(0,176,240, .2);
            text-shadow: 1px 1px 2px rgb(0,176,240);
            </text>
        } outline-color: rgb(0,176,240);
                outline-offset: 15px;
                z-index: 999;
                transform: scale(1.3,1.3);
                -webkit-transform: scale(1.3,1.3);
                -moz-transform: scale(1.3,1.3);
            }


        .btnEdit {
        @if (Themes.Keys.Contains("edit_backcolor"))
        {
            var color = Themes["edit_backcolor"];
            <text> background-color: @(color) 80;
            </text>
        }
        @if (Themes.Keys.Contains("edit_color"))
        {
            var color = Themes["edit_color"];
            <text> color: @(color);
            </text>
        }
        else
        {
            <text> color:red;
            </text>
        }
            /*background-color: rgba(240,176,0,.5); */
        }

            .btnEdit:hover {
                /* border: 1px solid;
                        box-shadow: inset 0 0 20px rgba(240,176,0, .5), 0 0 20px rgba(240,176,0, .2);
                        outline-color: rgba(240,176,0, 1);
                        outline-offset: 15px;
                        text-shadow: 1px 1px 2px rgb(240,176,0);
                           */
            }

        @@keyframes fadeblinking {
            from {
                opacity: 0.1;
            }
        }

        .blinking {
            animation: fadeblinking 0.3s infinite alternate;
        }
    </style>
}

<div class="map-container">
    <!-- سایدبار -->
    <div id="sidebar">
        <div class="sidebar-wrapper">
            <div class="active-layer-container">
                <input type="text" id="activeLayerLable" class="form-control"  value="" placeholder="لایه فعال">
                <input type="text" id="activeLayer" class="d-none">
            </div>
            <div id="tree">
                <ul id="treeData" style="display:none;">
                    @Html.Raw(treeData)
                </ul>
            </div>
        </div>
    </div>
    <!-- نقشه -->
    <div id="map" class="map">
        <div style="position:absolute; left:50%; bottom:0px;z-index: 1001;">
            <div style="text-align:center">
                <span class="" id="scaleText" style="color: white;  text-shadow: 1px 1px 2px black, 0 0 25px blue, 0 0 5px darkblue;"></span>
                <br /> <span class="" id="positionText" style="color: white;  text-shadow: 1px 1px 2px black, 0 0 25px blue, 0 0 5px darkblue;"></span>
                <br /> <span class="" id="positionUTMText" style="color: white;  text-shadow: 1px 1px 2px black, 0 0 25px blue, 0 0 5px darkblue;"></span>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="dialog-Property" tabindex="-1" role="dialog" aria-labelledby="remoteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        </div>
    </div>
</div>

<div id="panel-Identify" style="position:absolute; display:none; top:50px; bottom:10px; right: 0px; width:400px; height:calc(100vh - 50px); background-color:#fff;border:1px solid gray; z-index:1000;" class=" ">
</div>

@section Scripts {
    <script src="~/lib/jqueryui/jquery-ui.min.js"></script>
    <script src="~/lib/jquery.fancytree/jquery.fancytree-all.min.js"></script>
    <script src="~/lib/esri-leaflet/dist/esri-leaflet.js"></script>
    <script src="~/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js"></script>
    <script src="~/lib/ion-rangeslider/js/ion.rangeslider.min.js"></script>
    <script src="~/lib/proj4/dist/proj4.js"></script>
    <script src="~/lib/leaflet-dialog/Leaflet.Dialog.js"></script>
    <script src="~/lib/leaflet-measure/leaflet-measure.js"></script>
    
    <script>
        // ========================================
        // متغیرهای سراسری
        // ========================================
        var baseUrl = '@ViewBag.BaseUrl';
        var URLBASE = "@Url.Content("~")";
        var map;
        var mapIndex = "1";
        var lyrs = [mapIndex];
        var lyrCo = [];
        var layerdef = '';
        var latLngCenter = [32.702222, 51.979378];
        var polygcreate;
        var isIdentify = false;
        var identifiedFeature;
        var highlighFeature ;
        var highlightStyle = {
            "color": "#FF00FF"
        };
        var isGraphicSketch = false;
        var isSketch = false;
        var identifyTool;
        var identifyResults = [];
        var currentIdentifyIndex = 0;
        var totalPages = 0;
        var currentPage = 1;
        var currentFeatureCollection = null;
        

        $(document).ready(function() {
            // ========================================
            // مقداردهی اولیه نقشه
            // ========================================
            initializeMap();
            initializeMapLayers();
            initializeFancytree();
            setupHeaderButtons();
            setupMapControls();
            setupMapEvents();

            $('#ActiveLayerLable').autocomplete({
                source: function (request, response) {
                    $.ajax({
                        type: "Get",
                        url: "/GeoMap/SearchTable",
                        dataType: "json",
                        data: { search: request.term },
                        success: function (data) {
                            
                            response($.map(data, function (item) {
                                return { label: item.Lable, value: item.Lable, id: item.ID };
                            }));
                            $(".ui-autocomplete").css("width", "220px");
                            $(".ui-autocomplete").css("background-color", "lightgray");
                            $(".ui-autocomplete").attr("dir", "rtl");
                            $(".ui-autocomplete").css("padding", "10px;");
                        }
                    });
                },
                minLength: 2,
                select: function (event, ui) {
                    $('#ActiveLayerLable').val(ui.item.label); // Fixed: ui.item.lable should be ui.item.label
                    $('#ActiveLayer').val(ui.item.id);
                    console.log(ui.item.id);
                    var treeSl = $('#tree').fancytree('getTree');
                    var nodeSl = treeSl.getNodeByKey(ui.item.id);
                    if (nodeSl) {
                        nodeSl.parent.setExpanded(true);
                        nodeSl.setActive();
                    }
                }
            });
        });

        function initializeMap() {
            map = L.map('map', {
                zoom: 5,
                center: latLngCenter,
                zoomControl: false,
                attributionControl: false
            });

            // اضافه کردن لایه پایه OpenStreetMap
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: ''
            }).addTo(map);
        }

        function initializeMapLayers() {
            // تنظیم لایه‌های ESRI
            lyrCo[0] = L.esri.dynamicMapLayer({
                url: baseUrl + '/rest/services/map/mapserver',
                opacity: 1,
                layers: [],
                f: 'image',
                format: 'png32',
                transparent: true,
                crs: L.CRS.EPSG3857
            }).addTo(map);

            lyrCo[0].idMap = mapIndex.toString();

            // رویدادهای لایه
            lyrCo[0].on("loading", function (e) {
                // می‌توان loading indicator اضافه کرد
            });

            lyrCo[0].on("load", function (e) {
                // می‌توان loading indicator را حذف کرد
            });
        }

        function setupMapControls() {
            // ========================================
            // کنترل‌های نقشه
            // ========================================

            // کنترل تغییر نقشه پایه
            L.control.custom({
                position: 'bottomright',
                content: '<a href="javascript:changeBaseMap()" id="basemapbtn"><img src="../mapService/img/basemap/0.png" class="img-thumbnail" id="basemapimage" style="width:70px;"></a>',
                style: { margin: '5px', padding: '0px' }
            }).addTo(map);

            // نوار ابزار اصلی
            L.control.custom({
                position: 'topleft',
                content: createToolbarContent(),
                classes: '',
                style: {
                    'z-index': '100',
                    margin: '2px',
                    cursor: 'pointer'
                },
                events: {
                    click: function (data) {
                        handleToolbarClick(data);
                    }
                }
            }).addTo(map);

            // تنظیمات hover برای basemap
            $("#basemapimage").hover(function () {
                $("#panel-satImages").show();
            });
            $("#panel-satImages").mouseleave(function () {
                $("#panel-satImages").hide();
            });
        }

        function createToolbarContent() {
            return '<div id="standardToolbar" style="width:44px;min-height:450px; z-index:902;"> ' +
                    // دکمه جابجایی
                    '<button type="button" class="btn tool2 btnView" title="جابجایی" onclick="resetTools();"> ' +
                        '<i class="fa fa-hand"></i> ' +
                    '</button>' +
                    // دکمه شناسایی
                    '<button id="info" type="button" class="btn tool2 btnView" title="شناسایی" onclick="activateIdentifyTool();"> ' +
                        '<i class="fa fa-info"></i> ' +
                    '</button>' +
                    // دکمه رفتن به موقعیت
                    '<button type="button" class="btn tool2 btnView" title="رفتن به موقعیت خاص" onclick="togglegotoxy();"> ' +
                        '<i class="fa fa-street-view"></i> ' +
                    '</button>' +
                    // دکمه اندازه‌گیری
                    '<button type="button" class="btn tool2 btnView" title="اندازه گیری" onclick="showMeasurementDialog();"> ' +
                        '<i class="fa fa-arrows-h"></i> ' +
                    '</button>' +
                    // منوی ترسیم
                    '<div class="btn-group-container" style="position:relative;">' +
                        '<button onclick="toggleBtn(\'dpDraw\');" title="ترسیم گرافیک" type="button" class="btn tool2 btnView">' +
                            '<i class="fa fa-pencil"></i>' +
                        '</button>' +
                        '<div class="dropdown-menu" id="dpDraw">' +
                            '<button type="button" class="btn btnView" title="ترسیم نقطه" onclick="activateDrawTool(\'marker\');"><i class="fa fa-circle"></i></button>' +
                            '<button type="button" class="btn btnView" title="ترسیم خط" onclick="activateDrawTool(\'polyline\');"><i class="fa fa-minus"></i></button>' +
                            '<button type="button" class="btn btnView" title="ترسیم چندضلعی" onclick="activateDrawTool(\'polygon\');"><i class="fa fa-square-o"></i></button>' +
                            '<button type="button" class="btn btnView" title="ترسیم حریم" onclick="activateBufferTool();"><i class="fa fa-dot-circle-o"></i></button>' +
                            '<button type="button" class="btn btnView" title="حذف کلیه گرافیک ها" onclick="clearAllGraphics();"><i class="fa fa-trash"></i></button>' +
                        '</div>' +
                    '</div>' +
                    // منوی انتخاب
                    '<div class="btn-group-container" style="position:relative;">' +
                        '<button onclick="toggleBtn(\'dpSelect\');" title="انتخاب" type="button" class="btn tool2 btnView">' +
                            '<i class="fa fa-search"></i>' +
                        '</button>' +
                        '<div class="dropdown-menu" id="dpSelect">' +
                            '<button type="button" class="btn btnView" title="انتخاب مستطیلی" onclick="activateSelectTool(\'rectangle\');"><i class="fa fa-square-o"></i></button>' +
                            '<button type="button" class="btn btnView" title="انتخاب دایره‌ای" onclick="activateSelectTool(\'circle\');"><i class="fa fa-circle-o"></i></button>' +
                            '<button type="button" class="btn btnView" title="حذف انتخاب فعال" onclick="clearActiveSelection();"><i class="fa fa-eraser"></i></button>' +
                            '<button type="button" class="btn btnView" title="معکوس انتخاب" onclick="invertSelection();"><i class="fa fa-random"></i></button>' +
                            '<button type="button" class="btn btnView" title="حذف همه انتخاب‌ها" onclick="clearAllSelections();"><i class="fa fa-trash-o"></i></button>' +
                        '</div>' +
                    '</div>' +
                '</div>';
        }

        function handleToolbarClick(data) {
            var id = data.target.id;
            if (data.path != undefined)
                id = data.path[1].id;
            if (id == "locate")
                map.locate({ setView: true });
        }

        function setupMapEvents() {
            // ========================================
            // رویدادهای نقشه
            // ========================================

            // رویداد حرکت نقشه
            map.on("moveend", function (e) {
                // می‌توان عملیات مورد نیاز را اضافه کرد
            });

            // رویداد حرکت موس روی نقشه
            map.on("mousemove", function (e) {
                var arrlatlng = Project(e.latlng);
                var label = "   " + arrlatlng[0].toFixed(4) + ", " + arrlatlng[1].toFixed(4);
                $("#positionText").html(label);

                var labelUTM = getUTMLabel(arrlatlng);
                $("#positionUTMText").html(labelUTM);
            });

            // رویداد کلیک روی نقشه
            map.on('click', function (e) {
                if (isIdentify)
                    identifyLayers(e);
                setGraphicSketchClicked(e);
            });

            // رویداد دابل کلیک
            map.on('dblclick', function (e) {
                if (polygcreate && polygcreate._enabled)
                    polygcreate.completeShape();
            });

            // رویداد تغییر زوم - تنظیم مقیاس
            map.on("zoomend", function (e) {
                setScale();
            });
        }

        function setupHeaderButtons() {
            // دکمه تغییر حالت سایدبار
            $('#sidebar_toggle').on('click', function(e) {
                e.preventDefault();
                animateSidebar();
            });

            // دکمه page_toggle - رفتن به صفحه اصلی
            $('#page_toggle').on('click', function(e) {
                e.preventDefault();
                window.location.href = '@Url.Action("Index", "Home")';
            });
        }

        // ========================================
        // تنظیمات FancyTree (درخت لایه‌ها)
        // ========================================
        function initializeFancytree() {
            $("#tree").fancytree({
                checkbox: true,
                selectMode: 2,
                rtl: true,
                init: function (event, data) {
                    // Set key from first part of title (just for this demo output)
                    data.tree.visit(function (n) {
                        //n.key = n.title.split(" ")[0];
                        if (n.key[0] != "L") {
                            n.checkbox = false;
                        }
                    });
                },
                select: function (event, data) {
                    var node = data.node;

                    // اگر گره‌ای انتخاب شد و یک لایه (L) است، والد آن را انتخاب کن
                    if (node.isSelected() && node.key && node.key[0] == "L") {
                        if (node.getParent()) {
                            node.getParent().setSelected(true, { noEvents: true }); // بدون فعال‌سازی رویداد
                        }
                    }

                    // اگر گره‌ای از حالت انتخاب خارج شد و یک گره والد (گروه) است، فرزندان را uncheck کن
                    if (!node.isSelected() && node.key && node.key[0] == "G") {
                        node.visit(function (child) {
                            child.setSelected(false, { noEvents: true }); // بدون فعال‌سازی رویداد
                        });
                    }

                    // فراخوانی refreshTree فقط یک بار در انتها
                    refreshTree();
                },
                beforeExpand: function (event, data) {
                    var kkey = data.node.key;
                    if (kkey.length > 7 && data.node.key[0] == "L")
                    {
                        setSymbologyByID(kkey.substring(1, 12), -1);
                    }
                },

                // The following options are only required, if we have more than one tree on one page:
                cookieId: "fancytree-Cb2",
                idPrefix: "fancytree-Cb2-",
                click: function (event, data) {
                    if (data && data.node && data.node.isSelected() && data.node.key[0] == "L" && data.node.key.length == 12) {
                        $("#activeLayerLable").val(data.node.li.innerText.split('\n')[0]);
                        $("#activeLayer").val(data.node.key);
                    }
                },
                dblclick: function (event, data) {
                    if (data && data.node && data.node.key[0] == "L" && data.node.key.length == 12) {
                        SettingLayer(data.node.key.substring(1, 80));
                    }
                },
            });
        }

        // ========================================
        // توابع مدیریت لایه‌ها
        // ========================================
        function SettingLayer(layerid) {
            settinglayerid = layerid;
            var url = baseUrl + '/GeoMap/Property?Layerid=' + layerid;
            
            $.ajax({
                url: url,
                cache: false,
                success: function(data) {
                    $("#dialog-Property .modal-content").html(data);
                    var modalElement = document.getElementById('dialog-Property');
                    var modal = new bootstrap.Modal(modalElement);
                    modal.show();
                    setPropertyProps(layerid);
                    modalElement.addEventListener('hidden.bs.modal', function() {
                        const backdrop = document.querySelector('.modal-backdrop');
                        if (backdrop) {
                            backdrop.remove();
                        }
                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';
                    });
                },
                error: function(data) {
                    console.error("Error loading modal content:", data);
                }
            });
            return false;
        }

        function setPropertyProps(layerid) {
            $("#propertyLayerId").val(layerid);
            getFieldinLable("L" + layerid);

            var chk = $("#C" + layerid).is(':checked');
            $("#layerchecked").prop('checked', chk);
            var op = parseInt($("#O" + layerid).val());
            if (op > 0) {
                op = 100 - op;
            } else {
                op = 0;
            }
            $("#layeropacity").ionRangeSlider({
                min: 0,
                max: 100,
                from: op,
                step: 2,
                grid: true,
                prefix: "%"
            });
            var lbl = $("#La" + layerid).val().replace(/@@/g, ':').replace(/\\n/g, '\n');
            var arrLabels = lbl.split(':');
            var joinLabel = '';
            if (arrLabels.length > 1) {
                joinLabel = arrLabels[arrLabels.length - 1];
            }
            var baseLabel = '';
            if (joinLabel.split('___').length > 1) {
                baseLabel = lbl.replace(joinLabel, '');
                baseLabel = baseLabel.substring(0, baseLabel.length - 1);
            } else {
                baseLabel = lbl;
            }
            $("#layerlabledd").val(baseLabel);
            $("#layerlablejoindd").val(joinLabel);

            var sym = $("#Sym" + layerid).val();
            $("input[name='radio_Sym'][value='" + sym + "']").prop("checked");
            getLegend(sym);
        }

        function setlableopacity() {
            var chk = $("#layerchecked").is(':checked');
            $("#C" + settinglayerid).prop('checked', chk);

            var op = $("#layeropacity").val();
            $("#O" + settinglayerid).val(100 - op);

            var ll = $("#layerlabledd").val().replace(/\n/g, '\\n').replace(/\:/g, '@@');
            var llj = $("#layerlablejoindd").val().replace(/\n/g, '\\n').replace(/\:/g, '@@');
            if ($("#layerlablejoindd").val() && $("#layerlablejoindd").val().trim() != '') {
                if ($("#layerlabledd").val() && $("#layerlabledd").val().trim() != '') {
                    ll = ll + '@@' + llj;
                }
            }
            $("#La" + settinglayerid).val(ll);
            refreshTree();
        }

        function setSymbology() {
            var sym = $("input:radio[name='radio_Sym']:checked").val();
            $("#Sym" + settinglayerid).val(sym);
            setSymbologyByID(settinglayerid, sym);
            refreshTree();
        }

        function setSymbologyByID(layerid, sym) {
            
            sym = $("#Sym" + layerid).val();
            if (!sym) sym = -1;
            var childNode = $("#tree").fancytree("getTree").getNodeByKey("L" + layerid);
            if (childNode) {
                childNode.removeChildren();
            }
            
            $.ajax({
                type: 'POST',
                url: "/Rest/Legend",
                data: { "layerId": "L" + layerid, "symbologyId": sym },
                dataType: 'json',
                success: function(data) {
                    for (var i = 0; i < data.length; i++) {
                        var nodett = childNode.addChildren({
                            active: data[i].active,
                            checkbox: data[i].checkbox,
                            data: data[i].data,
                            expanded: data[i].expanded,
                            focus: data[i].focus,
                            folder: data[i].folder,
                            icon: data[i].icon,
                            key: data[i].key,
                            lazy: data[i].lazy,
                            selected: data[i].selected,
                            title: data[i].title,
                            tooltip: data[i].tooltip
                        });
                        if (data[i].iconHeight && data[i].iconWidth) {
                            $(nodett.span).children('img').height(data[i].iconHeight).width(data[i].iconWidth);
                        }
                    }
                    if (data.length >= 10) {
                        var nodeOther = childNode.addChildren({
                            checkbox: false,
                            icon: false,
                            folder: false,
                            key: "L" + layerid + "_otherLegend",
                            lazy: false,
                            title: "<a href='#' onclick='getLegendOther(1,\"" + layerid + "\");'>...</a>",
                            tooltip: "ادامه..."
                        });
                    }
                },
                error: function(data) {
                    console.error("Error in setSymbologyByID:", data);
                }
            });
        }

        function refreshTree() {
            var selNodes = $("#tree").fancytree('getTree').getSelectedNodes();
            for (var i = 0; i < selNodes.length; i++) {
                if (selNodes[i].getParent() && selNodes[i].getParent().getParent() != null) {
                    selNodes[i].getParent().setSelected(true);
                }
            }
            var r = [];
            for (var j = 0; j < lyrs.length; j++) {
                if (lyrs[j] != "") {
                    for (var i = 0; i < selNodes.length; i++) {
                        var id = selNodes[i].key;
                        if (id[0] == 'L') {
                            if (selNodes[i].getParent() && selNodes[i].getParent().isSelected()) {
                                if (lyrs[j] == parseInt(id.substring(2, 8)).toString()) {
                                    var ids = id.substr(1);
                                    if ($("#C" + ids).is(':checked')) {
                                        ids = ids + ";" + $("#La" + ids).val() + ";" + $("#O" + ids).val() + ";" + $("#Sym" + ids).val();
                                    } else {
                                        ids = ids + ";false;" + $("#O" + ids).val() + ";" + $("#Sym" + ids).val();
                                    }
                                    r.push(ids);
                                }
                            }
                        }
                    }
                    
                    //basemap.bringToBack();
                    lyrCo[j] = lyrCo[j].setLayers(r);
                }
            }
        }

        function animateSidebar() {
            var sidebar = document.getElementById('sidebar');
            var mapDiv = document.getElementById('map');

            sidebar.classList.toggle('hidden');
            mapDiv.classList.toggle('full-width');

            // به‌روزرسانی نقشه بعد از انیمیشن
            setTimeout(function() {
                map.invalidateSize();
            }, 350);
        }

        // ========================================
        // توابع کمکی مختصات و مقیاس
        // ========================================
        function getUTMLabel(arrlatlng) {
            var index = 0;
            var utmZone = '38';
            if (arrlatlng[0] > 42 && arrlatlng[0] < 48) {
                utmZone = '38';
            }
            else if (arrlatlng[0] > 48 && arrlatlng[0] < 54) {
                utmZone = '39';
            }
            else if (arrlatlng[0] > 54 && arrlatlng[0] < 60) {
                utmZone = '40';
            }
            else if (arrlatlng[0] > 60 && arrlatlng[0] < 66) {
                utmZone = '41';
            }

            switch (utmZone) {
                case '38':
                    index = 2;
                    break;
                case '39':
                    index = 3;
                    break;
                case '40':
                    index = 4;
                    break;
                case '41':
                    index = 5;
                    break;
                default:
            }

            var arrUTM = arrlatlng;
            if (index > 0) {
                arrUTM = proj4(projectSystems[index]).forward(arrlatlng);
            }
            var labelUTM = "UTM" + utmZone + ": " + arrUTM[0].toFixed(0) + ", " + arrUTM[1].toFixed(0);
            return labelUTM;
        }

        function setScale()
        {
            var prj = 'PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0],AUTHORITY["ESRI","102100"]]';
            var bnd = map.getBounds();
            var xy1 = proj4(prj).forward([bnd._northEast.lng, bnd._northEast.lat]);
            var xy2 = proj4(prj).forward([bnd._southWest.lng, bnd._southWest.lat]);
            var label = (Math.abs(xy1[0] - xy2[0]) / ((map.getSize().x * 0.0254) / 96)).toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ",");

            //var metres = scale._getRoundNum(map.containerPointToLatLng([0, map.getSize().y / 2]).distanceTo(map.containerPointToLatLng([scale.options.maxWidth, map.getSize().y / 2])))
            //var label = metres < 1000 ? metres + ' m' : (metres / 1000) + ' km';
            $("#scaleText").html("1:" + label);
        }


        // ========================================
        // سیستم‌های مختصات و تبدیل
        // ========================================
        var projectIndex = 0;
        var projectSystems = [
                '',
                'PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0],AUTHORITY["ESRI","102100"]]',
                'PROJCS["WGS 84 / UTM zone 38N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",45],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32638"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["WGS 84 / UTM zone 39N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",51],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32639"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["WGS 84 / UTM zone 40N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",57],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32640"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["WGS 84 / UTM zone 41N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",63],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32641"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["\Iran Lambert\",GEOGCS["\GCS_WGS_1984\",DATUM["\D_WGS_1984\",SPHEROID["\WGS_1984\",6378137.0,298.257223563]],PRIMEM["\Greenwich\",0.0],UNIT["\Degree\",0.0174532925199433]],PROJECTION["\Lambert_Conformal_Conic\"],PARAMETER["\False_Easting\",5000000.0],PARAMETER["\False_Northing\",30000000.0],PARAMETER["\Central_Meridian\",54.0],PARAMETER["\Standard_Parallel_1\",30.0],PARAMETER["\Standard_Parallel_2\",36.0],PARAMETER["\Central_Parallel\",24.0],UNIT["\Meter\",1.0]]'
        ];
        function setproject(index) {
            projectIndex = index;            
        }
        function Project(latlng) {
            if (projectIndex == 0)
                return [latlng.lng, latlng.lat];
            return proj4(projectSystems[projectIndex]).forward([latlng.lng, latlng.lat]);
        }
        function ProjectInverse(x, y) {
            if (projectIndex == 0)
                return [x, y];
            return proj4(projectSystems[projectIndex]).inverse([x, y]);
        }


        function enableTool() {
            map.dragging.enable();

            isIdentify = false;
            isGraphicSketch = false;
            isSketch = false;

            document.getElementById('map').style.cursor = '';

            if (identifiedFeature)
                map.removeLayer(identifiedFeature);
            if (highlighFeature)
                map.removeLayer(highlighFeature);

            if (toolname == "info") {
                document.getElementById('map').style.cursor = 'help';
                isIdentifySaman = true;
            }
             
            if (toolname == "sketchGraphic") {
                document.getElementById('map').style.cursor = 'copy';
                isGraphicSketch = true;
            }
        }

        function toggleBtn(id) {
            // بستن سایر dropdown ها
            $('.dropdown-menu').removeClass('show');

            // تغییر وضعیت dropdown فعلی
            var dropdown = $('#' + id);
            if (dropdown.hasClass('show')) {
                dropdown.removeClass('show');
            } else {
                dropdown.addClass('show');
            }
        }

        function toggleBtnShow(id) {
            $('#' + id).addClass('show');
        }

        function toggleBtnHide(id) {
            $('#' + id).removeClass('show');
        }

        // بستن dropdown ها هنگام کلیک روی نقشه
        $(document).on('click', function(e) {
            if (!$(e.target).closest('#standardToolbar').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });

        // ========================================
        // توابع نوار ابزار
        // ========================================

        function resetTools() {
            // ریست کردن تمام ابزارها
            toolname = '';
            enableTool();
            isIdentify = false;
            isGraphicSketch = false;
            isSketch = false;
            document.getElementById('map').style.cursor = 'grab';

            // بستن تمام dropdown ها
            $('.dropdown-menu').removeClass('show');
        }

        function activateIdentifyTool() {
            resetTools();
            toolname = 'info';
            isIdentify = true;
            document.getElementById('map').style.cursor = 'help';
        }

        function showMeasurementDialog() {
            resetTools();

            // ایجاد محتوای دیالوگ اندازه‌گیری
            var dialogContent = `
                <div class="measurement-dialog">
                    <h5 class="mb-3">ابزار اندازه‌گیری</h5>
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label">نوع اندازه‌گیری:</label>
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary" id="measurePoint" onclick="startMeasurement('point')">
                                    <i class="fa fa-circle"></i> نقطه
                                </button>
                                <button type="button" class="btn btn-outline-primary" id="measureLine" onclick="startMeasurement('line')">
                                    <i class="fa fa-minus"></i> خط
                                </button>
                                <button type="button" class="btn btn-outline-primary" id="measurePolygon" onclick="startMeasurement('polygon')">
                                    <i class="fa fa-square-o"></i> چندضلعی
                                </button>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">واحد اندازه‌گیری:</label>
                            <select class="form-select" id="measurementUnit">
                                <option value="meters">متر</option>
                                <option value="kilometers">کیلومتر</option>
                                <option value="feet">فوت</option>
                                <option value="miles">مایل</option>
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="alert alert-info" id="measurementResult" style="display: none;">
                                <strong>نتیجه:</strong> <span id="measurementValue"></span>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="btn-group w-100">
                                <button type="button" class="btn btn-success" onclick="clearMeasurements()">
                                    <i class="fa fa-trash"></i> پاک کردن
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="closeMeasurementDialog()">
                                    <i class="fa fa-times"></i> بستن
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // ایجاد دیالوگ
            if (window.measurementDialog) {
                window.measurementDialog.close();
            }

            window.measurementDialog = L.control.dialog({
                size: [350, 300],
                minSize: [300, 250],
                maxSize: [400, 350],
                anchor: [50, 50],
                position: 'topleft',
                initOpen: true
            })
            .setContent(dialogContent)
            .addTo(map);
        }

        // ========================================
        // توابع اندازه‌گیری
        // ========================================

        var currentMeasurement = null;
        var measurementLayers = [];

        function startMeasurement(type) {
            // پاک کردن انتخاب قبلی
            $('.measurement-dialog .btn').removeClass('btn-primary').addClass('btn-outline-primary');
            $('#measure' + type.charAt(0).toUpperCase() + type.slice(1)).removeClass('btn-outline-primary').addClass('btn-primary');

            // پاک کردن اندازه‌گیری قبلی
            clearCurrentMeasurement();

            var unit = $('#measurementUnit').val();

            switch(type) {
                case 'point':
                    startPointMeasurement();
                    break;
                case 'line':
                    startLineMeasurement(unit);
                    break;
                case 'polygon':
                    startPolygonMeasurement(unit);
                    break;
            }
        }

        function startPointMeasurement() {
            map.once('click', function(e) {
                var coords = Project(e.latlng);
                var result = `مختصات: ${coords[0].toFixed(4)}, ${coords[1].toFixed(4)}`;

                // نمایش نتیجه
                $('#measurementValue').text(result);
                $('#measurementResult').show();

                // اضافه کردن نشانگر
                var marker = L.marker(e.latlng, {
                    icon: L.divIcon({
                        className: 'measurement-marker',
                        html: '<i class="fa fa-map-marker" style="color: red; font-size: 20px;"></i>',
                        iconSize: [20, 20],
                        iconAnchor: [10, 20]
                    })
                }).addTo(map);

                measurementLayers.push(marker);
                currentMeasurement = marker;
            });

            map.getContainer().style.cursor = 'crosshair';
        }

        function startLineMeasurement(unit) {
            var polyline = L.polyline([], {
                color: 'red',
                weight: 3,
                opacity: 0.8
            }).addTo(map);

            measurementLayers.push(polyline);
            currentMeasurement = polyline;

            var points = [];
            var totalDistance = 0;

            function onMapClick(e) {
                points.push(e.latlng);
                polyline.addLatLng(e.latlng);

                if (points.length > 1) {
                    var lastDistance = points[points.length - 2].distanceTo(points[points.length - 1]);
                    totalDistance += lastDistance;

                    var result = formatDistance(totalDistance, unit);
                    $('#measurementValue').text(`طول کل: ${result}`);
                    $('#measurementResult').show();
                }

                // اضافه کردن نشانگر برای هر نقطه
                var marker = L.circleMarker(e.latlng, {
                    color: 'red',
                    fillColor: 'white',
                    fillOpacity: 1,
                    radius: 4,
                    weight: 2
                }).addTo(map);

                measurementLayers.push(marker);
            }

            function onMapDblClick(e) {
                map.off('click', onMapClick);
                map.off('dblclick', onMapDblClick);
                map.getContainer().style.cursor = '';
            }

            map.on('click', onMapClick);
            map.on('dblclick', onMapDblClick);
            map.getContainer().style.cursor = 'crosshair';
        }

        function startPolygonMeasurement(unit) {
            var polygon = L.polygon([], {
                color: 'red',
                weight: 3,
                opacity: 0.8,
                fillColor: 'red',
                fillOpacity: 0.2
            }).addTo(map);

            measurementLayers.push(polygon);
            currentMeasurement = polygon;

            var points = [];

            function onMapClick(e) {
                points.push(e.latlng);
                polygon.addLatLng(e.latlng);

                if (points.length >= 3) {
                    var area = L.GeometryUtil.geodesicArea(points);
                    var result = formatArea(area, unit);
                    $('#measurementValue').text(`مساحت: ${result}`);
                    $('#measurementResult').show();
                }

                // اضافه کردن نشانگر برای هر نقطه
                var marker = L.circleMarker(e.latlng, {
                    color: 'red',
                    fillColor: 'white',
                    fillOpacity: 1,
                    radius: 4,
                    weight: 2
                }).addTo(map);

                measurementLayers.push(marker);
            }

            function onMapDblClick(e) {
                map.off('click', onMapClick);
                map.off('dblclick', onMapDblClick);
                map.getContainer().style.cursor = '';

                // بستن چندضلعی
                if (points.length >= 3) {
                    polygon.addLatLng(points[0]);
                }
            }

            map.on('click', onMapClick);
            map.on('dblclick', onMapDblClick);
            map.getContainer().style.cursor = 'crosshair';
        }

        function clearCurrentMeasurement() {
            if (currentMeasurement) {
                map.removeLayer(currentMeasurement);
                currentMeasurement = null;
            }

            map.off('click');
            map.off('dblclick');
            map.getContainer().style.cursor = '';
        }

        function clearMeasurements() {
            // پاک کردن تمام لایه‌های اندازه‌گیری
            measurementLayers.forEach(layer => {
                map.removeLayer(layer);
            });
            measurementLayers = [];
            currentMeasurement = null;

            // پاک کردن نتیجه
            $('#measurementResult').hide();
            $('#measurementValue').text('');

            // ریست کردن دکمه‌ها
            $('.measurement-dialog .btn').removeClass('btn-primary').addClass('btn-outline-primary');

            map.off('click');
            map.off('dblclick');
            map.getContainer().style.cursor = '';
        }

        function closeMeasurementDialog() {
            clearMeasurements();
            if (window.measurementDialog) {
                window.measurementDialog.close();
                window.measurementDialog = null;
            }
        }

        function formatDistance(distance, unit) {
            switch(unit) {
                case 'kilometers':
                    return (distance / 1000).toFixed(2) + ' کیلومتر';
                case 'feet':
                    return (distance * 3.28084).toFixed(2) + ' فوت';
                case 'miles':
                    return (distance / 1609.34).toFixed(2) + ' مایل';
                default:
                    return distance.toFixed(2) + ' متر';
            }
        }

        function formatArea(area, unit) {
            switch(unit) {
                case 'kilometers':
                    return (area / 1000000).toFixed(2) + ' کیلومتر مربع';
                case 'feet':
                    return (area * 10.7639).toFixed(2) + ' فوت مربع';
                case 'miles':
                    return (area / 2589988.11).toFixed(2) + ' مایل مربع';
                default:
                    return area.toFixed(2) + ' متر مربع';
            }
        }

        // توابع ابزارهای ترسیم
        function activateDrawTool(type) {
            resetTools();

            switch(type) {
                case 'marker':
                    activeSelectMarker();
                    break;
                case 'polyline':
                    activeSelectPolyline();
                    break;
                case 'polygon':
                    activeSelectPolygon();
                    break;
                default:
                    console.warn('نوع ابزار ترسیم نامعتبر:', type);
            }
        }

        function activateBufferTool() {
            resetTools();
            if (typeof bufferwindows === 'function') {
                bufferwindows();
            } else {
                console.warn('bufferwindows function not found');
                alert('ابزار ترسیم حریم در حال توسعه است');
            }
        }

        function clearAllGraphics() {
            if (typeof deleteAllGraphic === 'function') {
                deleteAllGraphic();
            } else {
                console.warn('deleteAllGraphic function not found');
                // پاک کردن تمام لایه‌های گرافیکی
                for (var item in map._layers) {
                    if (map._layers[item].tool === "selecttool") {
                        map.removeLayer(map._layers[item]);
                    }
                }
            }
        }

        // توابع ابزارهای انتخاب
        function activateSelectTool(type) {
            resetTools();

            switch(type) {
                case 'rectangle':
                    activeSelectBox();
                    break;
                case 'circle':
                    // اگر تابع انتخاب دایره‌ای وجود دارد
                    if (typeof activeSelectCircle === 'function') {
                        activeSelectCircle();
                    } else {
                        console.warn('activeSelectCircle function not found');
                        activeSelectBox(); // استفاده از انتخاب مستطیلی به عنوان جایگزین
                    }
                    break;
                default:
                    console.warn('نوع ابزار انتخاب نامعتبر:', type);
            }
        }

        function clearActiveSelection() {
            if (typeof UnSelectBoxactiveLayer === 'function') {
                UnSelectBoxactiveLayer();
            } else {
                console.warn('UnSelectBoxactiveLayer function not found');
            }
        }

        function invertSelection() {
            if (typeof switchSelection === 'function') {
                switchSelection();
            } else {
                console.warn('switchSelection function not found');
            }
        }

        function clearAllSelections() {
            if (typeof UnSelectBox === 'function') {
                UnSelectBox();
            } else {
                console.warn('UnSelectBox function not found');
            }
        }

        // تابع شناسایی لایه‌ها
        function identifyLayers(e) {
            if (!isIdentify) return;

            // نمایش پنل شناسایی
            $("#panel-Identify").show().html('<div class="p-3"><i class="fa fa-spinner fa-spin"></i> در حال شناسایی...</div>');

            // فراخوانی سرویس شناسایی
            L.esri.identifyFeatures({
                url: baseUrl + '/rest/services/map/mapserver'
            })
           .on(map)
            .at(e.latlng)
            .tolerance(2)
            .layers(function() {
                var visibleLayers = [];
                if (lyrCo && lyrCo[0] && lyrCo[0].getLayers) {
                    var layers = lyrCo[0].getLayers();
                    layers.forEach(function(layer) {
                        var parts = layer.split(';');
                        if (parts.length >= 3) {
                            var layerId = parts[0];
                            var isVisible = parts[2] !== 'false';
                            if (isVisible) {
                                visibleLayers.push('show:' + layerId);
                            }
                        }
                    });
                }
                var result = visibleLayers.join(',');
                return result;
            }())
            .layerDef(function() {
                var layerDefs = {};
                if (lyrCo && lyrCo[0] && lyrCo[0].getLayers) {
                    layerDefs = lyrCo[0].getLayerDefs();
                }
                var jsonStr = JSON.stringify(layerDefs);
                return jsonStr;
            }())
            .run(function(error, featureCollection) {
                if (error) {
                    console.error('خطا در شناسایی:', error);
                    $("#panel-Identify").html('<div class="alert alert-danger m-3">خطا در شناسایی عارضه‌ها</div>');
                    return;
                }

                if (featureCollection.features && featureCollection.features.length > 0) {
                    initializeIdentifyPanel(featureCollection);
                } else {
                    $("#panel-Identify").html('<div class="alert alert-info m-3">هیچ عارضه‌ای در این موقعیت یافت نشد</div>');
                }
            });
        }

        

        function initializeIdentifyPanel(featureCollection) {
            var featuresPerPage = 1;
            totalPages = featureCollection.features.length;
            currentPage = 1;
            currentFeatureCollection = featureCollection;

            // Show first page initially
            showPage(1);

            // Add event listeners for tab changes
            $(document).on('shown.bs.tab', '[data-tab-type]', function (e) {
                var featureId = $(this).data('feature-id');
                var tabType = $(this).data('tab-type');
                var targetId = $(e.target).attr('href');
                
                if (tabType === 'related') {
                    loadRelatedRecords(featureId, targetId);
                } else if (tabType === 'docs') {
                    loadDocuments(featureId, targetId);
                }
            });
        }
        function showPage(pageNum) {
            if (!currentFeatureCollection || pageNum < 1 || pageNum > totalPages) return;
            currentPage = pageNum;

            var feature = currentFeatureCollection.features[pageNum - 1];
            var html = '<div class="card m-0">';
            html += '<div class="card-header d-flex justify-content-between align-items-center p-2">';
            html += '<h6 class="mb-0">نتایج شناسایی</h5>';
            html += '<button type="button" class="btn btn-sm btn-outline-secondary" onclick="$(\'#panel-Identify\').hide();">';
            html += '<i class="fa fa-times"></i>';
            html += '</button>';
            html += '</div>';

            // Single Feature Card
            html += '<div class="card m-0">';
            html += '<div class="card-header  d-flex justify-content-between align-items-center p-1">';
            html += '<small>عارضه ' + pageNum + ' از ' + totalPages + '</small>';
            html += '<div>';
            html += '<button type="button" class="btn btn-sm btn-outline-primary" onclick="zoomToIdentifyFeature(' + (pageNum - 1) + ');">';
            html += '<i class="fa fa-search-plus"></i>';
            html += '</button>';
            html += '<button type="button" class="btn btn-sm btn-outline-primary" onclick="flashIdentifyFeature(' + (pageNum - 1) + ');">';
            html += '<i class="fa fa-bolt"></i>';
            html += '</button>';
            html += '</div>';
            html += '</div>';
            html += '<div class="card-body p-1">';

            // Tab Navigation
            html += '<ul class="nav nav-tabs" role="tablist">';
            html += '<li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#info' + (pageNum - 1) + '" role="tab">اطلاعات</a></li>';
            html += '<li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#related' + (pageNum - 1) + '" role="tab" data-feature-id="' + (pageNum - 1) + '" data-tab-type="related">رکوردهای مرتبط</a></li>';
            html += '<li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#docs' + (pageNum - 1) + '" role="tab" data-feature-id="' + (pageNum - 1) + '" data-tab-type="docs">مستندات</a></li>';
            html += '</ul>';

            // Tab Content
            html += '<div class="tab-content mt-1">';

            // Info Tab
            html += '<div class="tab-pane fade show active" id="info' + (pageNum - 1) + '" role="tabpanel">';
            if (feature.properties) {
                for (var prop in feature.properties) {
                    if (feature.properties[prop] !== null && feature.properties[prop] !== '') {
                        html += '<div class="row mb-1">';
                        html += '<div class="col-5"><small><strong>' + prop + ':</strong></small></div>';
                        html += '<div class="col-7"><small>' + feature.properties[prop] + '</small></div>';
                        html += '</div>';
                    }
                }
            }
            html += '</div>';

            // Related Records Tab
            html += '<div class="tab-pane fade" id="related' + (pageNum - 1) + '" role="tabpanel">';
            html += '<div class="text-center p-3"><i class="fa fa-spinner fa-spin"></i> در حال بارگذاری...</div>';
            html += '</div>';

            // Documents Tab
            html += '<div class="tab-pane fade" id="docs' + (pageNum - 1) + '" role="tabpanel">';
            html += '<div class="text-center p-3"><i class="fa fa-spinner fa-spin"></i> در حال بارگذاری...</div>';
            html += '</div>';

            html += '</div>'; // End tab-content
            html += '</div></div>'; // End card-body and card

            // Pagination Controls
            html += '<div class="d-flex justify-content-center mt-1">';
            html += '<nav aria-label="Page navigation">';
            html += '<ul class="pagination">';

            // Previous button
            html += '<li class="page-item ' + (currentPage === 1 ? 'disabled' : '') + '">';
            html += '<a class="page-link" href="#" onclick="changePage(' + (currentPage - 1) + '); return false;">قبلی</a>';
            html += '</li>';

            // Page numbers
            for (var i = 1; i <= totalPages; i++) {
                if(i == 1 || i == 2 || i == totalPages - 1 || i == totalPages || i == currentPage)
                {
                    html += '<li class="page-item ' + (i === currentPage ? 'active' : '') + '">';
                    html += '<a class="page-link" href="#" onclick="changePage(' + i + '); return false;">' + i + '</a>';
                    html += '</li>';
                }
            }

            // Next button
            html += '<li class="page-item ' + (currentPage === totalPages ? 'disabled' : '') + '">';
            html += '<a class="page-link" href="#" onclick="changePage(' + (currentPage + 1) + '); return false;">بعدی</a>';
            html += '</li>';

            html += '</ul>';
            html += '</nav>';
            html += '</div>';

            html += '</div>';
            $("#panel-Identify").html(html);
        }

        function changePage(pageNum) {
            if (pageNum < 1 || pageNum > totalPages) return;
            currentPage = pageNum;
            showPage(pageNum);
        }

        function  getVisibleLayers()
        {
            var visibleLayers = [];
            lyrCo[0].getLayers().forEach(function(layer) {
                if (layer.visible) {
                    visibleLayers.push('show:' + layer.id);
                }
            });
            return visibleLayers.join(',');
        }


        function zoomToIdentifyFeature(featureIndex) {
            var feature = currentFeatureCollection.features[featureIndex];
            if (feature && feature.geometry) {
                var bounds = L.geoJSON(feature).getBounds();
                map.fitBounds(bounds, {
                    padding: [50, 50],
                    maxZoom: 18
                });
            }
        }
        function flashIdentifyFeature(featureIndex)
        {
            var feature = currentFeatureCollection.features[featureIndex];
            if (feature && feature.geometry) {
                flashFeature(feature);
            }
        }
        
        function loadRelatedRecords(featureId, targetId) {
            var feature = currentFeatureCollection.features[featureId];
            if (!feature) return;

            $.ajax({
                url: baseUrl + '/GeoMap/GetRelatedRecords',
                type: 'POST',
                data: {
                    featureId: feature.properties.ID || featureId,
                    layerId: feature.layerId
                },
                success: function(response) {
                    var html = '<div class="table-responsive">';
                    html += '<table class="table table-sm">';
                    html += '<thead><tr>';
                    for (var col in response.columns) {
                        html += '<th>' + response.columns[col] + '</th>';
                    }
                    html += '</tr></thead><tbody>';

                    response.data.forEach(function(row) {
                        html += '<tr>';
                        for (var col in response.columns) {
                            html += '<td>' + row[col] + '</td>';
                        }
                        html += '</tr>';
                    });

                    html += '</tbody></table></div>';
                    $(targetId).html(html);
                },
                error: function() {
                    $(targetId).html('<div class="alert alert-danger">خطا در بارگذاری اطلاعات</div>');
                }
            });
        }

        function loadDocuments(featureId, targetId) {
            var feature = currentFeatureCollection.features[featureId];
            if (!feature) return;

            $.ajax({
                url: baseUrl + '/GeoMap/GetFeatureDocuments',
                type: 'POST',
                data: {
                    featureId: feature.properties.ID || featureId,
                    layerId: feature.layerId
                },
                success: function(response) {
                    var html = '<div class="list-group">';
                    response.forEach(function(doc) {
                        html += '<a href="' + doc.url + '" class="list-group-item list-group-item-action" target="_blank">';
                        html += '<div class="d-flex w-100 justify-content-between">';
                        html += '<h6 class="mb-1">' + doc.title + '</h6>';
                        html += '<small>' + doc.date + '</small>';
                        html += '</div>';
                        html += '<p class="mb-1">' + doc.description + '</p>';
                        html += '</a>';
                    });
                    html += '</div>';
                    $(targetId).html(html);
                },
                error: function() {
                    $(targetId).html('<div class="alert alert-danger">خطا در بارگذاری مستندات</div>');
                }
            });
        }

        // ========================================
        // توابع انتخاب بر اساس geometry
        // ========================================

        /**
         * انتخاب عارضه‌ها بر اساس geometry
         *  {Object} geometry - geometry برای انتخاب (WKT یا GeoJSON)
         *  {string|Array} layers - لایه‌ها برای انتخاب (رشته یا آرایه)
         *  {Object} options - تنظیمات اضافی
         * returns {Promise} نتیجه انتخاب
         */
        function selectFeaturesByGeometry(geometry, layers, options = {}) {
            return new Promise((resolve, reject) => {
                // تنظیمات پیش‌فرض
                const defaultOptions = {
                    spatialRel: 'esriSpatialRelIntersects',
                    returnGeometry: false,
                    returnIdsOnly: false,
                    maxRecordCount: 1000
                };

                const finalOptions = { ...defaultOptions, ...options };

                // تبدیل layers به رشته اگر آرایه باشد
                const layersParam = Array.isArray(layers) ? layers.join(',') : layers;

                // تبدیل geometry به فرمت مناسب
                let geometryParam = '';
                if (typeof geometry === 'string') {
                    // اگر WKT باشد
                    geometryParam = geometry;
                } else if (typeof geometry === 'object') {
                    // اگر GeoJSON یا Leaflet geometry باشد
                    if (geometry.type) {
                        // GeoJSON format
                        geometryParam = JSON.stringify(geometry);
                    } else if (geometry.getBounds) {
                        // Leaflet layer
                        const bounds = geometry.getBounds();
                        geometryParam = `${bounds.getWest()},${bounds.getSouth()},${bounds.getEast()},${bounds.getNorth()}`;
                    } else if (geometry.getLatLng) {
                        // Leaflet marker
                        const latlng = geometry.getLatLng();
                        geometryParam = `POINT(${latlng.lng} ${latlng.lat})`;
                    }
                }

                if (!geometryParam) {
                    reject(new Error('فرمت geometry نامعتبر است'));
                    return;
                }

                // ارسال درخواست
                $.ajax({
                    url: baseUrl + '/rest/services/map/mapserver/selectbygeometry',
                    type: 'POST',
                    data: {
                        geometry: geometryParam,
                        layers: layersParam,
                        spatialRel: finalOptions.spatialRel,
                        returnGeometry: finalOptions.returnGeometry,
                        returnIdsOnly: finalOptions.returnIdsOnly,
                        maxRecordCount: finalOptions.maxRecordCount
                    },
                    success: function(response) {
                        if (response.success) {
                            console.log(`انتخاب موفق: ${response.totalCount} عارضه یافت شد`);
                            resolve(response);
                        } else {
                            reject(new Error(response.message || 'خطا در انتخاب عارضه‌ها'));
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('خطا در انتخاب بر اساس geometry:', error);
                        reject(new Error(`خطا در ارتباط با سرور: ${error}`));
                    }
                });
            });
        }

        /**
         * انتخاب عارضه‌ها بر اساس مستطیل انتخاب
         *  {L.LatLngBounds} bounds - محدوده انتخاب
         *  {string|Array} layers - لایه‌ها برای انتخاب
         *  {Object} options - تنظیمات اضافی
         */
        function selectFeaturesByBounds(bounds, layers, options = {}) {
            const bbox = `${bounds.getWest()},${bounds.getSouth()},${bounds.getEast()},${bounds.getNorth()}`;
            return selectFeaturesByGeometry(bbox, layers, options);
        }

        /**
         * انتخاب عارضه‌ها بر اساس نقطه
         *  {L.LatLng} latlng - نقطه انتخاب
         *  {string|Array} layers - لایه‌ها برای انتخاب
         *  {Object} options - تنظیمات اضافی
         */
        function selectFeaturesByPoint(latlng, layers, options = {}) {
            const point = `POINT(${latlng.lng} ${latlng.lat})`;
            return selectFeaturesByGeometry(point, layers, options);
        }

        /**
         * انتخاب عارضه‌ها بر اساس چندضلعی
         *  {Array} coordinates - مختصات چندضلعی [[lng, lat], ...]
         *  {string|Array} layers - لایه‌ها برای انتخاب
         *  {Object} options - تنظیمات اضافی
         */
        function selectFeaturesByPolygon(coordinates, layers, options = {}) {
            // تبدیل مختصات به WKT
            const coordsStr = coordinates.map(coord => `${coord[0]} ${coord[1]}`).join(', ');
            const polygon = `POLYGON((${coordsStr}))`;
            return selectFeaturesByGeometry(polygon, layers, options);
        }

        /**
         * نمایش نتایج انتخاب در پنل
         *  {Object} selectionResult - نتیجه انتخاب
         */
        function displaySelectionResults(selectionResult) {
            if (!selectionResult.success) {
                console.error('خطا در نتایج انتخاب:', selectionResult.message);
                return;
            }

            let html = '<div class="p-3">';
            html += '<div class="d-flex justify-content-between align-items-center mb-3">';
            html += '<h5 class="mb-0">نتایج انتخاب</h5>';
            html += '<button type="button" class="btn btn-sm btn-outline-secondary" onclick="$(\'#panel-Identify\').hide();">';
            html += '<i class="fa fa-times"></i>';
            html += '</button>';
            html += '</div>';

            html += `<div class="alert alert-info">تعداد کل عارضه‌های انتخاب شده: ${selectionResult.totalCount}</div>`;

            if (selectionResult.layers && selectionResult.layers.length > 0) {
                selectionResult.layers.forEach((layer, layerIndex) => {
                    html += '<div class="card mb-2">';
                    html += `<div class="card-header bg-primary text-white">`;
                    html += `<small>${layer.LayerName} (${layer.FeatureCount} عارضه)</small>`;
                    html += '</div>';

                    if (layer.Features && layer.Features.length > 0) {
                        html += '<div class="card-body p-2" style="max-height: 300px; overflow-y: auto;">';
                        layer.Features.forEach((feature, featureIndex) => {
                            html += `<div class="border-bottom mb-2 pb-2">`;
                            html += `<strong>عارضه ${featureIndex + 1}:</strong><br>`;

                            if (feature.attributes) {
                                for (const [key, value] of Object.entries(feature.attributes)) {
                                    if (value !== null && value !== '') {
                                        html += `<small><strong>${key}:</strong> ${value}</small><br>`;
                                    }
                                }
                            }
                            html += '</div>';
                        });
                        html += '</div>';
                    }
                    html += '</div>';
                });
            }

            html += '</div>';

            // نمایش در پنل شناسایی
            $("#panel-Identify").html(html).show();
        }

        // ========================================
        // مثال‌های استفاده از توابع انتخاب
        // ========================================

        /**
         * مثال: انتخاب عارضه‌ها در محدوده فعلی نقشه
         */
        function selectFeaturesInCurrentView() {
            const bounds = map.getBounds();
            const visibleLayers = getVisibleLayerIds(); // تابع برای دریافت لایه‌های قابل مشاهده

            if (visibleLayers.length === 0) {
                alert('هیچ لایه‌ای قابل مشاهده نیست');
                return;
            }

            selectFeaturesByBounds(bounds, visibleLayers, {
                returnGeometry: false,
                maxRecordCount: 500
            })
            .then(result => {
                displaySelectionResults(result);
            })
            .catch(error => {
                console.error('خطا در انتخاب:', error);
                alert('خطا در انتخاب عارضه‌ها: ' + error.message);
            });
        }

        /**
         * مثال: انتخاب عارضه‌ها بر اساس کلیک روی نقشه
         */
        function selectFeaturesAtPoint(e) {
            const visibleLayers = getVisibleLayerIds();

            if (visibleLayers.length === 0) {
                return;
            }

            selectFeaturesByPoint(e.latlng, visibleLayers, {
                returnGeometry: true,
                maxRecordCount: 10
            })
            .then(result => {
                if (result.totalCount > 0) {
                    displaySelectionResults(result);
                } else {
                    console.log('هیچ عارضه‌ای در این نقطه یافت نشد');
                }
            })
            .catch(error => {
                console.error('خطا در انتخاب نقطه‌ای:', error);
            });
        }

        /**
         * مثال: انتخاب عارضه‌ها بر اساس چندضلعی ترسیم شده
         */
        function selectFeaturesInDrawnPolygon(layer) {
            const visibleLayers = getVisibleLayerIds();

            if (visibleLayers.length === 0) {
                alert('هیچ لایه‌ای قابل مشاهده نیست');
                return;
            }

            // تبدیل لایه Leaflet به مختصات
            const coordinates = layer.getLatLngs()[0].map(latlng => [latlng.lng, latlng.lat]);
            // بستن چندضلعی
            coordinates.push(coordinates[0]);

            selectFeaturesByPolygon(coordinates, visibleLayers, {
                returnGeometry: false,
                maxRecordCount: 1000
            })
            .then(result => {
                displaySelectionResults(result);
                console.log(`انتخاب در چندضلعی: ${result.totalCount} عارضه یافت شد`);
            })
            .catch(error => {
                console.error('خطا در انتخاب چندضلعی:', error);
                alert('خطا در انتخاب عارضه‌ها: ' + error.message);
            });
        }

        /**
         * دریافت لیست لایه‌های قابل مشاهده
         */
        function getVisibleLayerIds() {
            const visibleLayers = [];

            // بررسی لایه‌های انتخاب شده در درخت
            const selectedNodes = $("#tree").fancytree('getTree').getSelectedNodes();
            selectedNodes.forEach(node => {
                if (node.key && node.key.startsWith('L') && node.key.length === 12) {
                    // بررسی اینکه لایه فعال است
                    const layerId = node.key.substring(1);
                    if ($("#C" + layerId).is(':checked')) {
                        visibleLayers.push(node.key);
                    }
                }
            });

            return visibleLayers;
        }

        /**
         * انتخاب عارضه‌ها بر اساس WKT geometry
         *  {string} wktGeometry - geometry به فرمت WKT
         *  {Array} layerIds - آرایه ID های لایه
         */
        function selectFeaturesByWKT(wktGeometry, layerIds) {
            return selectFeaturesByGeometry(wktGeometry, layerIds, {
                spatialRel: 'esriSpatialRelIntersects',
                returnGeometry: true,
                returnIdsOnly: false,
                maxRecordCount: 1000
            });
        }

        // تابع رفتن به موقعیت خاص
        function togglegotoxy() {
            resetTools();

            // ایجاد محتوای دیالوگ GoToXY
            var dialogContent = `
                <div class="gotoxy-dialog">
                    <h5 class="mb-3">رفتن به مختصات</h5>
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label">سیستم مختصات:</label>
                            <select class="form-select" id="coordinateSystem">
                                <option value="geographic">جغرافیایی (WGS84)</option>
                                <option value="mercator">مرکاتور (Web Mercator)</option>
                                <option value="utm38">UTM Zone 38N</option>
                                <option value="utm39">UTM Zone 39N</option>
                                <option value="utm40">UTM Zone 40N</option>
                                <option value="utm41">UTM Zone 41N</option>
                            </select>
                        </div>
                        <div class="col-6 mb-3">
                            <label class="form-label" id="xLabel">طول جغرافیایی (X):</label>
                            <input type="number" class="form-control" id="coordinateX" placeholder="51.3890" step="any">
                        </div>
                        <div class="col-6 mb-3">
                            <label class="form-label" id="yLabel">عرض جغرافیایی (Y):</label>
                            <input type="number" class="form-control" id="coordinateY" placeholder="35.6892" step="any">
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">سطح زوم:</label>
                            <select class="form-select" id="zoomLevel">
                                <option value="10">10 - شهر</option>
                                <option value="12">12 - منطقه</option>
                                <option value="15" selected>15 - محله</option>
                                <option value="17">17 - خیابان</option>
                                <option value="19">19 - ساختمان</option>
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="addMarker" checked>
                                <label class="form-check-label" for="addMarker">
                                    اضافه کردن نشانگر
                                </label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="btn-group w-100">
                                <button type="button" class="btn btn-primary" onclick="goToCoordinates()">
                                    <i class="fa fa-map-marker"></i> رفتن
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="closeGoToXYDialog()">
                                    <i class="fa fa-times"></i> بستن
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // ایجاد دیالوگ
            if (window.gotoxyDialog) {
                window.gotoxyDialog.close();
            }

            window.gotoxyDialog = L.control.dialog({
                size: [400, 350],
                minSize: [350, 300],
                maxSize: [450, 400],
                anchor: [100, 50],
                position: 'topleft',
                initOpen: true
            })
            .setContent(dialogContent)
            .addTo(map);

            // تنظیم event listener برای تغییر سیستم مختصات
            setTimeout(function() {
                $('#coordinateSystem').on('change', updateCoordinateLabels);
                updateCoordinateLabels();
            }, 100);
        }

        // ========================================
        // توابع GoToXY
        // ========================================

        var gotoxyMarker = null;

        function updateCoordinateLabels() {
            var system = $('#coordinateSystem').val();
            var xLabel = $('#xLabel');
            var yLabel = $('#yLabel');
            var xInput = $('#coordinateX');
            var yInput = $('#coordinateY');

            switch(system) {
                case 'geographic':
                    xLabel.text('طول جغرافیایی (Longitude):');
                    yLabel.text('عرض جغرافیایی (Latitude):');
                    xInput.attr('placeholder', '51.3890');
                    yInput.attr('placeholder', '35.6892');
                    break;
                case 'mercator':
                    xLabel.text('X (متر):');
                    yLabel.text('Y (متر):');
                    xInput.attr('placeholder', '5718499.45');
                    yInput.attr('placeholder', '4268239.12');
                    break;
                case 'utm38':
                    xLabel.text('Easting (متر):');
                    yLabel.text('Northing (متر):');
                    xInput.attr('placeholder', '718499');
                    yInput.attr('placeholder', '3968239');
                    break;
                case 'utm39':
                    xLabel.text('Easting (متر):');
                    yLabel.text('Northing (متر):');
                    xInput.attr('placeholder', '318499');
                    yInput.attr('placeholder', '3968239');
                    break;
                case 'utm40':
                    xLabel.text('Easting (متر):');
                    yLabel.text('Northing (متر):');
                    xInput.attr('placeholder', '918499');
                    yInput.attr('placeholder', '3968239');
                    break;
                case 'utm41':
                    xLabel.text('Easting (متر):');
                    yLabel.text('Northing (متر):');
                    xInput.attr('placeholder', '518499');
                    yInput.attr('placeholder', '3968239');
                    break;
            }
        }

        function goToCoordinates() {
            var x = parseFloat($('#coordinateX').val());
            var y = parseFloat($('#coordinateY').val());
            var system = $('#coordinateSystem').val();
            var zoom = parseInt($('#zoomLevel').val());
            var addMarker = $('#addMarker').is(':checked');

            if (isNaN(x) || isNaN(y)) {
                alert('لطفاً مختصات معتبر وارد کنید');
                return;
            }

            try {
                var targetCoords = transformCoordinates(x, y, system);

                if (!targetCoords) {
                    alert('خطا در تبدیل مختصات');
                    return;
                }

                // رفتن به موقعیت
                map.setView([targetCoords.lat, targetCoords.lng], zoom);

                // حذف نشانگر قبلی
                if (gotoxyMarker) {
                    map.removeLayer(gotoxyMarker);
                    gotoxyMarker = null;
                }

                // اضافه کردن نشانگر جدید
                if (addMarker) {
                    gotoxyMarker = L.marker([targetCoords.lat, targetCoords.lng], {
                        icon: L.divIcon({
                            className: 'gotoxy-marker',
                            html: '<i class="fa fa-map-marker" style="color: blue; font-size: 24px;"></i>',
                            iconSize: [24, 24],
                            iconAnchor: [12, 24]
                        })
                    }).addTo(map);

                    var popupContent = `
                        <div>
                            <strong>موقعیت مورد نظر</strong><br>
                            <small>سیستم: ${getSystemName(system)}</small><br>
                            <small>X: ${x}</small><br>
                            <small>Y: ${y}</small><br>
                            <small>WGS84: ${targetCoords.lng.toFixed(6)}, ${targetCoords.lat.toFixed(6)}</small>
                        </div>
                    `;

                    gotoxyMarker.bindPopup(popupContent).openPopup();
                }

                console.log(`رفتن به مختصات: ${targetCoords.lng}, ${targetCoords.lat} (${system})`);

            } catch (error) {
                console.error('خطا در رفتن به مختصات:', error);
                alert('خطا در پردازش مختصات: ' + error.message);
            }
        }

        function transformCoordinates(x, y, system) {
            try {
                switch(system) {
                    case 'geographic':
                        return { lng: x, lat: y };

                    case 'mercator':
                        // تبدیل از Web Mercator به WGS84
                        var lng = x / 20037508.34 * 180;
                        var lat = y / 20037508.34 * 180;
                        lat = 180 / Math.PI * (2 * Math.atan(Math.exp(lat * Math.PI / 180)) - Math.PI / 2);
                        return { lng: lng, lat: lat };

                    case 'utm38':
                        return transformUTMToWGS84(x, y, 38);

                    case 'utm39':
                        return transformUTMToWGS84(x, y, 39);

                    case 'utm40':
                        return transformUTMToWGS84(x, y, 40);

                    case 'utm41':
                        return transformUTMToWGS84(x, y, 41);

                    default:
                        throw new Error('سیستم مختصات نامعتبر');
                }
            } catch (error) {
                console.error('خطا در تبدیل مختصات:', error);
                return null;
            }
        }

        function transformUTMToWGS84(easting, northing, zone) {
            try {
                // استفاده از proj4 برای تبدیل UTM به WGS84
                var utmProj = `+proj=utm +zone=${zone} +datum=WGS84 +units=m +no_defs`;
                var wgs84Proj = '+proj=longlat +datum=WGS84 +no_defs';

                var result = proj4(utmProj, wgs84Proj, [easting, northing]);
                return { lng: result[0], lat: result[1] };
            } catch (error) {
                console.error('خطا در تبدیل UTM:', error);
                throw new Error('خطا در تبدیل مختصات UTM');
            }
        }

        function getSystemName(system) {
            switch(system) {
                case 'geographic': return 'جغرافیایی (WGS84)';
                case 'mercator': return 'مرکاتور (Web Mercator)';
                case 'utm38': return 'UTM Zone 38N';
                case 'utm39': return 'UTM Zone 39N';
                case 'utm40': return 'UTM Zone 40N';
                case 'utm41': return 'UTM Zone 41N';
                default: return 'نامشخص';
            }
        }

        function closeGoToXYDialog() {
            if (window.gotoxyDialog) {
                window.gotoxyDialog.close();
                window.gotoxyDialog = null;
            }
        }
 
        //-----------------------------------------
        var polygcreate;    
        var styleGraphic = { color: '#820091', weight: 3, fillColor: '#820091', dashArray: '5, 5', fillOpacity: 0.2 };
        var styleGraphic2 = { color: '#ed0000', weight: 3, fillColor: '#ed0000', dashArray: '5, 5', fillOpacity: 0.1 };

        function changeColorGraphicLayer(color) {
            styleGraphic.color = color;
            styleGraphic.fillColor = color;
            for (var i in map._layers)
                if (map._layers[i].tool && map._layers[i].tool == "selecttool")
                    map._layers[i].setStyle({ color: color, fillColor: color });
        }

        function activeSelectBox() {
            map.dragging.disable();
            new L.Draw.Rectangle(map, { shapeOptions: styleGraphic }).enable();
            selectBoxtool = true;
            document.getElementById('map').style.cursor = 'crosshair';
        }

        function activeSelectRectangle() {
            map.dragging.disable();
            new L.Draw.Rectangle(map, { shapeOptions: styleGraphic }).enable();
            selecttool = true;
            document.getElementById('map').style.cursor = 'crosshair';
        }
        function activeSelectCircle() {
            map.dragging.disable();
            new L.Draw.Circle(map, { shapeOptions: styleGraphic }).enable();
            selecttool = true;
            document.getElementById('map').style.cursor = 'crosshair';
        }
        function activeSelectMarker() {
            map.dragging.disable();
            new L.Draw.Marker(map, drawControl.options.marker).enable();
            selecttool = true;
            document.getElementById('map').style.cursor = 'crosshair';
        }
        function activeSelectPolyline() {
            map.dragging.disable();
            new L.Draw.Polyline(map, { shapeOptions: styleGraphic }).enable();
            selecttool = true;
            document.getElementById('map').style.cursor = 'crosshair';
        }

       
        function activeSelectPolygon() {
            map.dragging.disable();
            polygcreate = new L.Draw.Polygon(map, { shapeOptions: styleGraphic });//.enable();
            polygcreate.enable();
            selecttool = true;

            document.getElementById('map').style.cursor = 'crosshair';
        }

        function deactiveSelectBox() {
            if (selectBoxtool) {
                //$("#graphic_selected").prop("checked", false);
                var lyrid = $("#activeLayer").val();
                lyrid = lyrid.substring(1);
                selectByGraphic("selectBoxtool", 'intersects', lyrid, false);
                var eLayers = editableLayers.getLayers();
                for (var i = 0; i < eLayers.length; i++)
                    if (eLayers[i].tool == "selectBoxtool")
                        editableLayers.removeLayer(eLayers[i]);
                // editableLayers.removeLayer(layerEdit);
            }

            map.dragging.enable();
            //editableLayers.clearLayers();
            document.getElementById('map').style.cursor = '';
            selectBoxtool = false;
            selecttool = false;
        }
        
      
        ///------------ Go To XY
        function togglegotoxy()
        {
            $("#gotoxyContent").toggle();
            $("#searchcontent").hide();//$("#editcontent").hide();
        }

        var gotoxyMarker;
        function gotoxy() {
            try {
                if (gotoxyMarker) {
                    map.removeLayer(gotoxyMarker);
                    gotoxyMarker = null;
                }
                var x = $("#gotoxyX").val();
                var y = $("#gotoxyY").val();
                var coordinateSystem = $("#coordinateSystem").val();

                if (x !== "" && y !== "") {
                    var index = 0;
                    switch (coordinateSystem) {
                        case '38':
                            index = 2;
                            break;
                        case '39':
                            index = 3;
                            break;
                        case '40':
                            index = 4;
                            break;
                        case '41':
                            index = 5;
                            break;
                        default:
                    }

                    var prjInv = [x, y];
                    if (index > 0) {
                        prjInv = proj4(projectSystems[index]).inverse([x, y]);
                    }
                    gotoxyMarker = L.marker([prjInv[1], prjInv[0]]).addTo(map);
                    gotoxyMarker = gotoxyMarker.bindPopup("<button class='btn btn-success' onclick='map.removeLayer(gotoxyMarker); gotoxyMarker = null;'>حذف</button>");
                    map.setView([prjInv[1], prjInv[0]], 14);
                }
            }
            catch (err) {
                _Response("View-GoToXY", "Failed:" + err, null);
            }
        }
        //--------------------------Measurement
        var measureControl;

        function measurmentToggle()
        {
            if (!measureControl) {
                measureControl = L.control.measure({ position: 'bottomleft', primaryLengthUnit: 'meters', secondaryLengthUnit: undefined, primaryAreaUnit: 'sqmeters', localization: 'fa' });
                measureControl.addTo(map);
            }
            else {
                map.removeControl(measureControl);
                measureControl = null;
            }
        }

                //----------------------buffer

        function bufferwindows() {
            var url = URLBASE + 'GeoMap/_Buffer';
            $.ajax({
                url: url,
                cache: false,
                success: function (data) {
                    $("#dialog-Property .modal-content").html(data);
                    var modalElement = document.getElementById('dialog-Property');
                    var modal = new bootstrap.Modal(modalElement);
                    modal.show();

                    // Add event listener for when modal is hidden
                    modalElement.addEventListener('hidden.bs.modal', function () {
                        // Remove backdrop manually if it's still present
                        const backdrop = document.querySelector('.modal-backdrop');
                        if (backdrop) {
                            backdrop.remove();
                        }
                        // Also remove modal-open class from body if needed
                        document.body.classList.remove('modal-open');
                        // And remove inline styles that Bootstrap might have added
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';
                    });

                },
                error: function (data) {
                    console.error("Error loading modal content:", data);
                }
            });
            return false;
        }

        function buffer() {

            var leafletGeometry = identifiedFeature.toGeoJSON();
            var distance = $("#bufferValue").val();
            var geojson = turf.buffer(leafletGeometry, distance / 111325, 'meters');
            var layer = L.geoJSON(geojson.features[0], { style: styleGraphic2 });//.addTo(map);

            editableLayers.addLayer(layer);
            layer.tool = "selecttool";
            layer.type = "polygon";
            layer.buffer = true;
            layer.bindPopup('<a href="javascript:void(0)" class="md-btn md-btn-danger md-btn-mini md-btn-icon" onclick="editableLayers.removeLayer(' + layer._leaflet_id + ');"><i class="uk-icon-trash-o no_margin"></i></a>')
        }


        function bufferAllGraphic(distance, deleteprev, fillColor) {

            var tempLyr = []
            for (var i in editableLayers._layers) {
                var lyr = editableLayers._layers[i];
                if (lyr.tool == "selecttool")
                    tempLyr.push(lyr);
            }
            if (deleteprev)
                for (var i in tempLyr)
                    editableLayers.removeLayer(tempLyr[i]._leaflet_id);
            styleGraphic2.color = fillColor;
            styleGraphic2.fillColor = fillColor;

            for (var i in tempLyr) {
                var leafletGeometry = tempLyr[i].toGeoJSON();
                var geojson = turf.buffer(leafletGeometry, distance / 111325, 'meters');
                var layer = L.geoJSON(geojson.features[0], { style: styleGraphic2 });//.addTo(map);

                editableLayers.addLayer(layer);
                layer.tool = "selecttool";
                layer.buffer = true;
                layer.type = "polygon";
                layer.bindPopup('<a href="javascript:void(0)" class="md-btn md-btn-danger md-btn-mini md-btn-icon" onclick="editableLayers.removeLayer(' + layer._leaflet_id + ');"><i class="uk-icon-trash-o no_margin"></i></a>')
            }

        }
        function unionAllGraphic() {

            var tempLyr = []
            for (var i in editableLayers._layers) {
                var lyr = editableLayers._layers[i];
                if (lyr.tool == "selecttool")
                    tempLyr.push(lyr);
            }
            for (var i in tempLyr)
                editableLayers.removeLayer(tempLyr[i]._leaflet_id);

            var glayer;
            var cnt = 0;
            for (var i in tempLyr) {

                if (cnt == 0) {
                    if (tempLyr[i].toGeoJSON().features)
                        glayer = tempLyr[i].toGeoJSON().features[0];
                    else
                        glayer = tempLyr[i].toGeoJSON();
                }
                else {
                    if (tempLyr[i].toGeoJSON().features)
                        glayer = turf.union(glayer, tempLyr[i].toGeoJSON().features[0]);
                    else
                        glayer = turf.union(glayer, tempLyr[i].toGeoJSON());

                }

                cnt++;
            }
            console.log(glayer);
            var layer = L.geoJSON(glayer, { style: styleGraphic2 });//.addTo(map);

            editableLayers.addLayer(layer);
            layer.tool = "selecttool";
            layer.buffer = true;
            layer.type = "polygon";
            layer.bindPopup('<a href="javascript:void(0)" class="md-btn md-btn-danger md-btn-mini md-btn-icon" onclick="editableLayers.removeLayer(' + layer._leaflet_id + ');"><i class="uk-icon-trash-o no_margin"></i></a>')

            console.log(layer);
        }

        //------------------------

        var isGraphicSketch = false;
        var isGraphicSketchMeasurment = false;

        function setGraphicSketchClicked(e) { 
            if (isGraphicSketch) {
                $("#loading").show();
                L.esri.identifyFeatures({
                    url:  baseUrl + '/rest/services/map/mapserver'
                }).on(map).at(e.latlng).tolerance(0)
                    .layers('show:' + lyrCo[0].getLayers().toString())
                    .layerDef(JSON.stringify(lyrCo[0].getLayerDefs()))
                    .run(function (error, featureCollection) {
                     $("#loading").hide();
                    if (featureCollection.features.length > 0) {
                        var feature = featureCollection.features[0];
                        if (isGraphicSketchMeasurment /*&& feature.geometryType == 'esriGeometryPolygon'*/) {
                            var feats = {}; feats.type = "FeatureCollection"; feats.features = [];
                            for (var j = 0; j < feature.geometry.coordinates.length; j++)
                                for (var i = 1; i < feature.geometry.coordinates[j].length; i++) {



                                    var ff = {}; ff.type = "Feature";
                                    ff.geometry = {}; ff.type = "LineString"; ff.coordinates = [];
                                    ff.coordinates.push(feature.geometry.coordinates[j][i - 1]);
                                    ff.coordinates.push(feature.geometry.coordinates[j][i]);

                                    ff.properties = {}; ff.properties.length = L.GeometryUtil.length(L.polyline(ff.coordinates));
                                    feats.features.push(ff);
                                }


                            var srt = feats.features.sort(function (obj1, obj2) {
                                // Ascending: first age less than the previous
                                return obj2.properties.length - obj1.properties.length;
                            });
                            var ggg = L.featureGroup();
                            for (var i = 0; i < srt.length; i++) {
                                if (i > 20)
                                    srt[i].properties.length = undefined;
                                else {
                                    var x1 = srt[i].coordinates[0][1];
                                    var y1 = srt[i].coordinates[0][0];
                                    var x2 = srt[i].coordinates[1][1];
                                    var y2 = srt[i].coordinates[1][0];

                                    var a = (Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI) - 90;
                                    var Top = 0; var Left = 0;
                                    if (a <= 0 && a >= -90) { Left = (a * 25 / 90); }
                                    else if (a <= -90 && a >= -180) { Top = -((a + 90) * 20 / 90); Left = -((a + 180) * 25 / 90); }
                                    else if (a <= -180 && a >= -270) { Top = ((a + 270) * 20 / 90); }

                                    var ll = 0;
                                    var l = srt[i].properties.length;
                                    if (l > 1000) ll = (l / 1000).toFixed(2) + ' کیلومتر';
                                    else ll = (l).toFixed(2) + ' متر';
                                    
                                    var divHtml = '<i class="graphiclabels" style="transform: rotate(' + a.toFixed(0) + 'deg) !important;' + ("top:" + Top.toFixed(0) + "px;" + "left:" + Left.toFixed(0) + "px;") + '">' + ll.replace(".", "/") + '</i>';
                                    var featuregm = L.marker([(x1 + x2) / 2, (y1 + y2) / 2], {
                                        icon: L.divIcon({
                                            html: divHtml,
                                            iconSize: [20, 20],
                                            iconAnchor: [10, 20],
                                            className: 'myDivIcon'
                                        })
                                    });
                                    featuregm.divHtml = divHtml;
                                    ggg.addLayer(featuregm);

                                }
                            }
                            var layer2 = L.geoJSON(feats, {
                                onEachFeature: function (fe, la) {
                                    // la.setText(fe.properties.name, { offset: -5 });
                                    //la.bindPopup(fe.properties.name, { direction: 'left' });
                                    // if (fe.properties.length)
                                    //   la.bindTooltip(MeasureSystemUnit(fe.properties.length, 'length'), { permanent: true, direction: "top", className: "graphiclabels", offset: L.point(0, 0) });
                                    // streetLabelsRenderer._layers.push(la);
                                    //la.bindLabel("salaaam");//fe.properties.name/*, { noHide: true }*/);
                                },
                                style: styleGraphic
                            });
                            ggg.HasdivIcon = true;
                            layer2.addLayer(ggg);
                            editableLayers.addLayer(layer2);

                            layer2.tool = "selecttool";
                            layer2.type = "polygon";
                            layer2.bindPopup('<a href="javascript:void(0)" class="md-btn md-btn-danger md-btn-mini md-btn-icon" onclick="editableLayers.removeLayer(' + layer2._leaflet_id + ');"><i class="uk-icon-trash-o no_margin"></i></a>')
                            isGraphicSketchMeasurment = false;

                        }
                        else {
                            var layer = L.geoJSON(feature, { style: styleGraphic });
                            editableLayers.addLayer(layer);
                            layer.tool = "selecttool";
                            layer.type = "polygon";
                            layer.bindPopup('<a href="javascript:void(0)" class="md-btn md-btn-danger md-btn-mini md-btn-icon" onclick="editableLayers.removeLayer(' + layer._leaflet_id + ');"><i class="fa fa-trash"></i></a>')
                        }
                        document.getElementById('map').style.cursor = '';
                        isGraphicSketch = false;
                    }
                });
            }
        }


        function deleteAllGraphic() {
            for (var item in editableLayers._layers) {
                var layer = editableLayers._layers[item];
                if (layer.tool == "selecttool")
                    editableLayers.removeLayer(item);
            }

            if (identifiedFeature)
                map.removeLayer(identifiedFeature);

            if (highlighFeature)
                map.removeLayer(highlighFeature);

            markerLayers.clearLayers();
        }
        
        function ZoomToExtentLayer(layerId, selected) {
            try {
                // Get layer definitions from the current map layers
                var layerdef = {};
                for (var i = 0; i < lyrCo.length; i++) {
                    var def = lyrCo[i].getLayerDefs();
                    if (def[layerId]) {
                        layerdef[layerId] = def[layerId];
                        break;
                    }
                }

                // Show loading indicator
                $("#loading").show();

                // Make AJAX call to backend
                $.ajax({
                    type: 'POST',
                    url: baseUrl + '/rest/services/map/mapserver/Zoom',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        layers: layerId,
                        selected: selected,
                        layerDefs: JSON.stringify(layerdef)
                    }),
                    dataType: 'json',
                    success: function (response) {
                          
                        if (response.success && response.data) {
                          
                            // Convert coordinates from Web Mercator to WGS84
                            var prj = 'PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0],AUTHORITY["ESRI","102100"]]';
                            var xymin = proj4(prj).inverse([response.data.xMin, response.data.yMin]);
                            var xymax = proj4(prj).inverse([response.data.xMax, response.data.yMax]);
                            var bounds = [[xymin[1], xymin[0]], [xymax[1], xymax[0]]];
                            
                            // Fly to the bounds with animation
                            map.flyToBounds(bounds, {
                                duration: 1, // Duration in seconds
                                easeLinearity: 0.25
                            });
                        } else {
                            showToast(response.message || "خطا در بزرگنمایی", 'danger', 4000);
                        }
                    },
                    error: function (xhr, status, error) {
                        var errorMessage = "خطا در ارتباط با سرور";
                        try {
                            var response = JSON.parse(xhr.responseText);
                            errorMessage = response.message || errorMessage;
                        } catch (e) {
                            console.error("Error parsing error response:", e);
                        }
                        showToast(response.message || "خطا در بزرگنمایی", 'danger', 4000);
                    },
                    complete: function () {
                        $("#loading").hide();
                    }
                });
            } catch (err) {
                
                $("#loading").hide();
                showToast("در بزرگنمایی خطایی رخ داده است", 'danger', 4000);
            }
        }

        function highlightFeature(feature)
        {
            if (highlighFeature) {
                map.removeLayer(highlighFeature);
            }
            highlighFeature = L.geoJSON(feature, { style: highlightStyle }).addTo(map);
            map.fitBounds(highlighFeature.getBounds());
        }

        function flashFeature(feature)
        {
            if (highlighFeature) {
                map.removeLayer(highlighFeature);
            }
            highlighFeature = L.featureGroup();

            var colorselected = '#ff00ff';

            var featuregm;
            if (feature.geometry.type == "Point") {
                var gm = feature.geometry.coordinates;
                featuregm = L.circleMarker([gm[1], gm[0]], { "color": colorselected, opacity: 1, fillOpacity: 1 }).on('click', function () {  });
                highlighFeature.addLayer(featuregm);
            }
            else {
                featuregm = L.geoJSON(feature, { "color": colorselected, opacity: 1, fillOpacity: 1 }).on('click', function () { });
                highlighFeature.addLayer(featuregm);
            }

            highlighFeature.addTo(map);
            setTimeout(function () {
                map.removeLayer(highlighFeature);
                setTimeout(function () {
                    highlighFeature.addTo(map);
                    setTimeout(function () {
                        map.removeLayer(highlighFeature);
                        setTimeout(function () {
                            highlighFeature.addTo(map);
                            setTimeout(function () {
                                map.removeLayer(highlighFeature);
                            }, 600);
                        }, 600);
                    }, 600);
                }, 600);
            }, 600);
        }

    </script>
}