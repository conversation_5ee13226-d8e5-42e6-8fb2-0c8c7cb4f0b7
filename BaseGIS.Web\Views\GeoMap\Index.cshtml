﻿@using System.Text.Json
@model IEnumerable<BaseGIS.Core.Entities.GroupInfo>
@{
    ViewData["Title"] = "نقشه";
    Layout = "~/Views/Shared/_LayoutMap.cshtml";

    string treeData = ViewBag.TreeData;

}

@section Styles {
    <link rel="stylesheet" href="/lib/jqueryui/themes/base/jquery-ui.min.css" />
    <link href="~/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css" rel="stylesheet" />

    <link href="~/lib/ion-rangeslider/css/ion.rangeslider.min.css" rel="stylesheet" />
    <style>

        .map-container {
            display: flex;
            height: calc(100vh - 62px);
            direction: rtl;
        }

        #sidebar {
            width: 250px;
            height: 100%;
            transition: width 0.35s ease;
            overflow: hidden;
            background: #fff;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
        }

            #sidebar.hidden {
                width: 0;
                display: block;
            }

        .sidebar-wrapper {
            width: 100%;
            height: 100%;
        }

        .active-layer-container {
            padding: 10px;
            border-bottom: 1px solid #ddd;
            background: #f8f9fa;
        }

        #activeLayerInput {
            background-color: #fff;
            cursor: default;
        }

        #tree {
            height: 100%;
            max-height: calc(100vh - 150px); /* تنظیم ارتفاع با توجه به Textbox */
            overflow-y: auto;
            padding: 10px;
            background-color: var(--background-color);
        }

        #map {
            width: calc(100% - 250px);
            height: 100%;
            position: relative;
            order: 1;
        }

            #map.full-width {
                width: 100%;
            }

        .btn-Sidebar {
            position: absolute;
            z-index: 1000;
            top: 10px;
            border-radius: 5px 0 0 5px;
            height: 34px;
            width: 34px;
            background: white;
            padding: 4px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            transition: right 0.35s ease;
        }

        #sidebar.hidden + #map .btn-Sidebar {
            right: 0;
        }

        .btn-Sidebar a {
            color: var(--primary-color);
            text-decoration: none;
        }


        span.fancytree-node.custom > span.fancytree-title {
            color: maroon;
            font-family: "SamanFont";
        }

        span.fancytree-node.plan > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/Gallery16.png");
        }

        span.fancytree-node.group > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/group.png");
        }

        span.fancytree-node.polyline > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/polyline.png");
        }

        span.fancytree-node.point > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/point.png");
        }

        span.fancytree-node.polygon > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/polygon.png");
        }
         
        .fancytree-container,
        span.fancytree-focused span.fancytree-title {
            outline: 0 !important;
        }
         
        @@media (max-width: 768px) {
            #sidebar {
                width: 200px;
            }

            #map {
                width: calc(100% - 200px);
            }

            #sidebar.hidden + #map {
                width: 100%;
            }

            .btn-Sidebar {
                right: 200px;
            }
        }
    </style>
}

<div class="map-container">
    <!-- سایدبار -->
    <div id="sidebar">
        <div class="sidebar-wrapper">
            <div class="active-layer-container">
                <label for="activeLayerInput" class="form-label">لایه فعال</label>
                <input type="text" id="activeLayerLable" class="form-control" readonly value="هیچ لایه‌ای انتخاب نشده">
                <input type="text" id="activeLayer" class="d-none">

            </div>
            <div id="tree">
                <ul id="treeData" style="display:none;">
                    @Html.Raw(treeData)
                </ul>
            </div>
        </div>
    </div>
    <!-- نقشه -->
    <div id="map" class="map">
        <span class="btn-Sidebar">
            <a id="btnLeftSidebar" href="#" onclick="animateSidebar();" title="باز/بستن سایدبار"><i class="fa fa-angle-double-right fa-2x"></i></a>
        </span>
    </div>
</div>

<div class="modal" id="dialog-Property" tabindex="-1" role="dialog" aria-labelledby="remoteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/lib/jqueryui/jquery-ui.min.js"></script>
    <script src="~/lib/jquery.fancytree/jquery.fancytree-all.min.js"></script>
    <script src="~/lib/esri-leaflet/dist/esri-leaflet.js"></script>
    <script src="~/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js"></script>
    <script src="~/lib/ion-rangeslider/js/ion.rangeslider.min.js"></script>

    <script>
        var baseUrl = window.location.origin;
         var URLBASE = "@Url.Content("~")";
        var map;
      
        var mapIndex = "1";
        var lyrs = ['1'];
        var lyrCo = [];
       

        $(document).ready(function() {
            // مقداردهی اولیه نقشه
            map = L.map('map', {
                center: [32.6946, 53.4814],
                zoom: 5.6,
                zoomControl: false
            });

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: ''
            }).addTo(map);

            lyrCo[0] = L.esri.dynamicMapLayer({
                url: baseUrl + '/rest/services/map/mapserver',
                opacity: 1,
                layers: [],
                f: 'image',
                format: 'png32',
                transparent: true,
                crs: L.CRS.EPSG3857
            }).addTo(map);

            lyrCo[0].idMap = mapIndex.toString();
            lyrCo[0].on("loading", function (e) {

            });
            lyrCo[0].on("load", function (e) {

            });

            initializeFancytree();
        });

        function initializeFancytree() {
            $("#tree").fancytree({
                checkbox: true,
                selectMode: 2,
                rtl: true,
                init: function (event, data) {
                    // Set key from first part of title (just for this demo output)
                    data.tree.visit(function (n) {
                        //n.key = n.title.split(" ")[0];
                        if (n.key[0] != "L") {
                            n.checkbox = false;
                        }
                    });
                },
                select: function (event, data) {
                    var node = data.node;

                    // اگر گره‌ای انتخاب شد و یک لایه (L) است، والد آن را انتخاب کن
                    if (node.isSelected() && node.key && node.key[0] == "L") {
                        if (node.getParent()) {
                            node.getParent().setSelected(true, { noEvents: true }); // بدون فعال‌سازی رویداد
                        }
                    }

                    // اگر گره‌ای از حالت انتخاب خارج شد و یک گره والد (گروه) است، فرزندان را uncheck کن
                    if (!node.isSelected() && node.key && node.key[0] == "G") {
                        node.visit(function (child) {
                            child.setSelected(false, { noEvents: true }); // بدون فعال‌سازی رویداد
                        });
                    }

                    // فراخوانی refreshTree فقط یک بار در انتها
                    refreshTree();
                },
                beforeExpand: function (event, data) {
                    var kkey = data.node.key;
                    if (kkey.length > 7 && data.node.key[0] == "L")
                    {
                        setSymbologyByID(kkey.substring(1, 12), -1);
                    }
                },

                // The following options are only required, if we have more than one tree on one page:
                cookieId: "fancytree-Cb2",
                idPrefix: "fancytree-Cb2-",
                click: function (event, data) {
                    if (data && data.node && data.node.isSelected() && data.node.key[0] == "L" && data.node.key.length == 12) {
                        $("#activeLayerLable").val(data.node.li.innerText.split('\n')[0]);
                        $("#activeLayer").val(data.node.key);
                    }
                },
                dblclick: function (event, data) {
                    if (data && data.node && data.node.key[0] == "L" && data.node.key.length == 12) {
                        SettingLayer(data.node.key.substring(1, 80));
                    }
                },
            });
        }

        // سایر توابع مرتبط با تنظیمات لایه‌ها
        function SettingLayer(layerid) {
            settinglayerid = layerid;
            var url = baseUrl + '/GeoMap/Property?Layerid=' + layerid;
            
            $.ajax({
                url: url,
                cache: false,
                success: function(data) {
                    $("#dialog-Property .modal-content").html(data);
                    var modalElement = document.getElementById('dialog-Property');
                    var modal = new bootstrap.Modal(modalElement);
                    modal.show();
                    setPropertyProps(layerid);
                    modalElement.addEventListener('hidden.bs.modal', function() {
                        const backdrop = document.querySelector('.modal-backdrop');
                        if (backdrop) {
                            backdrop.remove();
                        }
                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';
                    });
                },
                error: function(data) {
                    console.error("Error loading modal content:", data);
                }
            });
            return false;
        }

        function setPropertyProps(layerid) {
            $("#propertyLayerId").val(layerid);
            getFieldinLable("L" + layerid);

            var chk = $("#C" + layerid).is(':checked');
            $("#layerchecked").prop('checked', chk);
            var op = parseInt($("#O" + layerid).val());
            if (op > 0) {
                op = 100 - op;
            } else {
                op = 0;
            }
            $("#layeropacity").ionRangeSlider({
                min: 0,
                max: 100,
                from: op,
                step: 2,
                grid: true,
                prefix: "%"
            });
            var lbl = $("#La" + layerid).val().replace(/@@/g, ':').replace(/\\n/g, '\n');
            var arrLabels = lbl.split(':');
            var joinLabel = '';
            if (arrLabels.length > 1) {
                joinLabel = arrLabels[arrLabels.length - 1];
            }
            var baseLabel = '';
            if (joinLabel.split('___').length > 1) {
                baseLabel = lbl.replace(joinLabel, '');
                baseLabel = baseLabel.substring(0, baseLabel.length - 1);
            } else {
                baseLabel = lbl;
            }
            $("#layerlabledd").val(baseLabel);
            $("#layerlablejoindd").val(joinLabel);

            var sym = $("#Sym" + layerid).val();
            $("input[name='radio_Sym'][value='" + sym + "']").prop("checked");
            getLegend(sym);
        }

        function setlableopacity() {
            var chk = $("#layerchecked").is(':checked');
            $("#C" + settinglayerid).prop('checked', chk);

            var op = $("#layeropacity").val();
            $("#O" + settinglayerid).val(100 - op);

            var ll = $("#layerlabledd").val().replace(/\n/g, '\\n').replace(/\:/g, '@@');
            var llj = $("#layerlablejoindd").val().replace(/\n/g, '\\n').replace(/\:/g, '@@');
            if ($("#layerlablejoindd").val() && $("#layerlablejoindd").val().trim() != '') {
                if ($("#layerlabledd").val() && $("#layerlabledd").val().trim() != '') {
                    ll = ll + '@@' + llj;
                }
            }
            $("#La" + settinglayerid).val(ll);
            refreshTree();
        }

        function setSymbology() {
            var sym = $("input:radio[name='radio_Sym']:checked").val();
            $("#Sym" + settinglayerid).val(sym);
            setSymbologyByID(settinglayerid, sym);
            refreshTree();
        }

        function setSymbologyByID(layerid, sym) {
            
            sym = $("#Sym" + layerid).val();
            if (!sym) sym = -1;
            var childNode = $("#tree").fancytree("getTree").getNodeByKey("L" + layerid);
            if (childNode) {
                childNode.removeChildren();
            }
            
            $.ajax({
                type: 'POST',
                url: "/Rest/Legend",
                data: { "layerId": "L" + layerid, "symbologyId": sym },
                dataType: 'json',
                success: function(data) {
                    for (var i = 0; i < data.length; i++) {
                        var nodett = childNode.addChildren({
                            active: data[i].active,
                            checkbox: data[i].checkbox,
                            data: data[i].data,
                            expanded: data[i].expanded,
                            focus: data[i].focus,
                            folder: data[i].folder,
                            icon: data[i].icon,
                            key: data[i].key,
                            lazy: data[i].lazy,
                            selected: data[i].selected,
                            title: data[i].title,
                            tooltip: data[i].tooltip
                        });
                        if (data[i].iconHeight && data[i].iconWidth) {
                            $(nodett.span).children('img').height(data[i].iconHeight).width(data[i].iconWidth);
                        }
                    }
                    if (data.length >= 10) {
                        var nodeOther = childNode.addChildren({
                            checkbox: false,
                            icon: false,
                            folder: false,
                            key: "L" + layerid + "_otherLegend",
                            lazy: false,
                            title: "<a href='#' onclick='getLegendOther(1,\"" + layerid + "\");'>...</a>",
                            tooltip: "ادامه..."
                        });
                    }
                },
                error: function(data) {
                    console.error("Error in setSymbologyByID:", data);
                }
            });
        }

        function refreshTree() {
            var selNodes = $("#tree").fancytree('getTree').getSelectedNodes();
            for (var i = 0; i < selNodes.length; i++) {
                if (selNodes[i].getParent() && selNodes[i].getParent().getParent() != null) {
                    selNodes[i].getParent().setSelected(true);
                }
            }
            var r = [];
            for (var j = 0; j < lyrs.length; j++) {
                if (lyrs[j] != "") {
                    for (var i = 0; i < selNodes.length; i++) {
                        var id = selNodes[i].key;
                        if (id[0] == 'L') {
                            if (selNodes[i].getParent() && selNodes[i].getParent().isSelected()) {
                                if (lyrs[j] == parseInt(id.substring(2, 8)).toString()) {
                                    var ids = id.substr(1);
                                    if ($("#C" + ids).is(':checked')) {
                                        ids = ids + ";" + $("#La" + ids).val() + ";" + $("#O" + ids).val() + ";" + $("#Sym" + ids).val();
                                    } else {
                                        ids = ids + ";false;" + $("#O" + ids).val() + ";" + $("#Sym" + ids).val();
                                    }
                                    r.push(ids);
                                }
                            }
                        }
                    }
                    
                    //basemap.bringToBack();
                    lyrCo[j] = lyrCo[j].setLayers(r);
                }
            }
        }

        function animateSidebar() {
            var sidebar = document.getElementById('sidebar');
            var mapDiv = document.getElementById('map');
            var btnLeftSidebar = document.getElementById('btnLeftSidebar');

            sidebar.classList.toggle('hidden');
            mapDiv.classList.toggle('full-width');

            if (sidebar.classList.contains('hidden')) {
                btnLeftSidebar.innerHTML = '<i class="fa fa-angle-double-left fa-2x"></i>';
                btnLeftSidebar.style.right = '0px';
            } else {
                btnLeftSidebar.innerHTML = '<i class="fa fa-angle-double-right fa-2x"></i>';
                btnLeftSidebar.style.right = '250px';
            }
            setTimeout(function() {
                map.invalidateSize();
            }, 350);
        }
    </script>
}