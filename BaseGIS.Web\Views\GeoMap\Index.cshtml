@using System.Text.Json
@using BaseGIS.Core.Entities
@using BaseGIS.Web.ViewModels
@model IEnumerable<BaseGIS.Core.Entities.GroupInfo>
@{
    ViewData["Title"] = "نقشه";
    Layout = "~/Views/Shared/_LayoutMap.cshtml";

    string treeData = ViewBag.TreeData;
    List<BaseMapViewModel> ListBaseMap = ViewBag.ListBaseMap ?? new List<BaseMapViewModel>();
    int BaseMapID = ViewBag.BaseMapID;
    var Theme = ViewBag.Theme;
    BaseMapConfigViewModel BaseMapConfig = ViewBag.BaseMapConfig ?? new BaseMapConfigViewModel();
    Dictionary<string, string> Themes = new Dictionary<string, string>();

    if (Theme != null && Theme != "")
    {
        var tms = Theme.Split(',');
        for (int i = 0; i < tms.Length; i++)
        {
            var tms1 = tms[i].Split(':');
            if (tms1.Length > 1)
            {
                // Store the HTML color string directly
                Themes.Add(tms1[0].ToLower(), tms1[1]);
            }
        }
    }
}

@section Styles {
    <!-- ===== کتابخانه‌های CSS از LibMan ===== -->
    <link href="~/lib/jqueryui/themes/base/jquery-ui.min.css" rel="stylesheet" />
    <link href="~/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css" rel="stylesheet" />
    <link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
    <link href="~/lib/sweetalert2/sweetalert2.min.css" rel="stylesheet" />
    <link href="~/lib/leaflet-dialog/Leaflet.Dialog.css" rel="stylesheet" />
    <link href="~/lib/leaflet-measure/leaflet-measure.css" rel="stylesheet" />
    <link href="~/lib/ion-rangeslider/css/ion.rangeslider.min.css" rel="stylesheet" />
    <link href="~/lib/leaflet-draw/dist/leaflet.draw.css" rel="stylesheet" />

    <!-- ===== استایل‌های کامپوننت‌ها ===== -->
    <link href="~/css/components.css" rel="stylesheet" />
    <link href="~/css/components/measurement-tools.css" rel="stylesheet" />
    <link href="~/css/components/spatial-analysis.css" rel="stylesheet" />
    <link href="~/css/components/goto-xy.css" rel="stylesheet" />
    <link href="~/css/components/drawing-tools.css" rel="stylesheet" />
    <link href="~/css/components/property-tools.css" rel="stylesheet" />
    <link href="~/css/components/property-panel.css" rel="stylesheet" />

    <style>
        /* استایل‌های اصلی صفحه */
        .map-container {
            display: flex;
            height: calc(100vh - 62px);
            direction: rtl;
        }

        #sidebar {
            width: 250px;
            height: 100%;
            transition: width 0.35s ease;
            overflow: hidden;
            background: #fff;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--primary-color);
        }

        #sidebar.hidden {
            width: 0;
            display: block;
        }

        #map {
            width: calc(100% - 250px);
            height: 100%;
            position: relative;
            order: 1;
        }

        #map.full-width {
            width: 100%;
        }

        /* Responsive */
        @@media (max-width: 768px) {
            #sidebar {
                width: 200px;
            }

            #map {
                width: calc(100% - 200px);
            }

            #sidebar.hidden + #map {
                width: 100%;
            }
        }
    
        /* استایل‌های اصلی صفحه */
        .map-container {
            display: flex;
            height: calc(100vh - 62px);
            direction: rtl;
        }

        #sidebar {
            width: 250px;
            height: 100%;
            transition: width 0.35s ease;
            overflow: hidden;
            background: #fff;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--primary-color);
        }

        #sidebar.hidden {
            width: 0;
            display: block;
        }

        #map {
            width: calc(100% - 250px);
            height: 100%;
            position: relative;
            order: 1;
        }

        #map.full-width {
            width: 100%;
        }

        /* Tab Container */
        .tab-container {
            height: calc(100vh - 120px);
            display: flex;
            flex-direction: column;
        }

        .tab-container .nav-tabs {
            border-bottom: 1px solid #dee2e6;
            background: #f8f9fa;
            margin: 0;
            padding: 0 6px;
            flex-shrink: 0;
        }

        .tab-container .nav-tabs .nav-link {
            border: none;
            border-radius: 0;
            padding: 8px 12px;
            font-size: 12px;
            color: #6c757d;
            background: transparent;
        }

        .tab-container .nav-tabs .nav-link:hover {
            border-color: transparent;
            background: rgba(0,123,255,0.1);
            color: #007bff;
        }

        .tab-container .nav-tabs .nav-link.active {
            color: #007bff;
            background: #fff;
            border-bottom: 2px solid #007bff;
            font-weight: 500;
        }

        .tab-container .tab-content {
            flex: 1;
            overflow: hidden;
            background: #fff;
        }

        .tab-container .tab-pane {
            height: 100%;
            overflow-y: auto;
        }

        /* Responsive */
        @@media (max-width: 768px) {
            #sidebar {
                width: 200px;
            }

            #map {
                width: calc(100% - 200px);
            }

            #sidebar.hidden + #map {
                width: 100%;
            }
        }
    </style>
}

<!-- ===== صفحه اصلی نقشه ===== -->
<div class="map-container">
    <!-- ===== سایدبار ===== -->
    <div id="sidebar">
        <div class="tab-container">
            <ul class="nav nav-tabs" id="sidebarTabs">
                <li class="nav-item">
                    <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#layers-panel">لایه‌ها</button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-bs-toggle="tab" data-bs-target="#views-panel">نقشه‌ها</button>
                </li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane fade show active" id="layers-panel">
                    <div id="layer-tree-container" data-component="layer-tree"></div>
                </div>
                <div class="tab-pane fade" id="views-panel">
                    <div id="map-views-container" data-component="map-views"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- ===== نقشه اصلی ===== -->
    <div id="map">
        <!-- کامپوننت‌های ابزار -->
        <div id="map-core-container" data-component="map-core"></div>
        <div id="toolbar-container" data-component="toolbar"></div>
        <div id="measurement-tools-container" data-component="measurement-tools"></div>
        <div id="spatial-analysis-container" data-component="spatial-analysis"></div>
        <div id="goto-xy-container" data-component="goto-xy"></div>
        <div id="drawing-tools-container" data-component="drawing-tools"></div>
        <div id="property-tools-container" data-component="property-tools"></div>
    </div>
</div>

<!-- ===== Modal ها و Panel های ضروری ===== -->
<div class="modal" id="dialog-Property" tabindex="-1" role="dialog" aria-labelledby="remoteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content"></div>
    </div>
</div>
@section Scripts {
    <!-- ===== کتابخانه‌های JavaScript از LibMan ===== -->
    <script src="~/lib/jqueryui/jquery-ui.min.js"></script>
    <script src="~/lib/jquery.fancytree/jquery.fancytree-all.min.js"></script>
    <script src="~/lib/select2/js/select2.min.js"></script>
    <script src="~/lib/sweetalert2/sweetalert2.min.js"></script>
    <script src="~/lib/esri-leaflet/dist/esri-leaflet.js"></script>
    <script src="~/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js"></script>
    <script src="~/lib/leaflet-draw/dist/leaflet.draw.js"></script>
    <script src="~/lib/leaflet-dialog/Leaflet.Dialog.js"></script>
    <script src="~/lib/leaflet-measure/leaflet-measure.js"></script>
    <script src="~/lib/proj4/dist/proj4.js"></script>
    <script src="~/lib/ion-rangeslider/js/ion.rangeslider.min.js"></script>

    <!-- ===== کامپوننت‌های پایه ===== -->
    <script src="~/js/components/base-component.js"></script>
    <script src="~/js/components/component-factory.js"></script>

    <!-- ===== کامپوننت‌های GeoMap ===== -->
    <script src="~/js/geomap/map-core.js"></script>
    <script src="~/js/geomap/toolbar.js"></script>
    <script src="~/js/geomap/layer-tree.js"></script>
    <script src="~/js/geomap/map-views.js"></script>
    <script src="~/js/geomap/baseMapManager.js"></script>
    <script src="~/js/geomap/measurement-tools.js"></script>
    <script src="~/js/geomap/spatial-analysis.js"></script>
    <script src="~/js/geomap/goto-xy.js"></script>
    <script src="~/js/geomap/drawing-tools.js"></script>
    <script src="~/js/geomap/property-tools.js"></script>
    
    <script>
        // ========================================
        // متغیرهای سراسری و تنظیمات
        // ========================================

        // Server data
        var baseUrl = '@ViewBag.BaseUrl';
        var URLBASE = "@Url.Content("~")";
        var treeData = @Html.Raw(treeData ?? "[]");

        // Component instances - مراجع کامپوننت‌ها
        var mapCore = null;
        var toolbar = null;
        var layerTree = null;
        var mapViews = null;
        var measurementTools = null;
        var spatialAnalysis = null;
        var gotoXY = null;
        var drawingTools = null;
        var propertyTools = null;

        // Global variables for backward compatibility - متغیرهای سازگاری
        var map, lyrCo = [], editableLayers, markerLayers, drawControl, baseMapManager;
        var isIdentify = false, isSketch = false, selecttool = false, selectBoxtool = false;

        // ========================================
        // مقداردهی اولیه صفحه
        // ========================================

        $(document).ready(function() {
            console.log('🚀 GeoMap page loading...');

            // Initialize core components
            initializeComponents();

            // Setup UI elements
            setupHeaderButtons();

            console.log('✅ GeoMap page loaded successfully');
        });

        // ========================================
        // مقداردهی کامپوننت‌ها
        // ========================================

        function initializeComponents() {
            console.log('🔧 Initializing components...');

            try {
                // 1. Map Core - هسته نقشه (اولویت اول)
                initializeMapCore();

            } catch (error) {
                console.error('❌ Error initializing components:', error);
            }
        }

        function initializeMapCore() {
            console.log('🗺️ Initializing Map Core...');

            mapCore = window.ComponentFactory.create('map-core', 'map-core-container', {
                center: [32.702222, 51.979378],
                zoom: 5,
                baseUrl: baseUrl,
                mapIndex: "1",
                onMapReady: function(mapInstance) {
                    console.log('✅ Map ready, initializing UI components...');
                    initializeUIComponents();
                }
            });

            if (mapCore) {
                // Set global references for backward compatibility
                window.mapCore = mapCore;
                console.log('✅ Map Core initialized');
            } else {
                console.error('❌ Failed to initialize Map Core');
            }
        }

        function initializeUIComponents() {
            console.log('🎨 Initializing UI components...');

            try {
                // Initialize sidebar components
                initializeSidebarComponents();

                // Initialize toolbar and tools
                initializeToolbarComponents();

                // Initialize tool components
                initializeToolComponents();

                // Set global references for backward compatibility
                setGlobalReferences();

                console.log('✅ All UI components initialized successfully');

            } catch (error) {
                console.error('❌ Error initializing UI components:', error);
            }
        }

        function initializeSidebarComponents() {
            console.log('📋 Initializing sidebar components...');

            // Layer Tree - درخت لایه‌ها
            layerTree = window.ComponentFactory.create('layer-tree', 'layer-tree-container', {
                treeData: treeData,
                baseUrl: baseUrl,
                onLayerToggle: function(layerId, visible) {
                    console.log('Layer toggled:', layerId, visible);
                }
            });

            // Map Views - نقشه‌های ذخیره شده
            mapViews = window.ComponentFactory.create('map-views', 'map-views-container', {
                onViewLoad: function(view) {
                    console.log('View loaded:', view);
                }
            });
        }

        function initializeToolbarComponents() {
            console.log('🔧 Initializing toolbar...');

            // Toolbar - نوار ابزار
            toolbar = window.ComponentFactory.create('toolbar', 'toolbar-container', {
                position: 'topleft',
                tools: ['pan', 'identify', 'measurement', 'spatial-analysis', 'goto-xy', 'drawing', 'property'],
                onToolActivate: function(tool) {
                    console.log('Tool activated:', tool);
                }
            });
        }

        function initializeToolComponents() {
            console.log('⚒️ Initializing tool components...');

            // Measurement Tools - ابزارهای اندازه‌گیری
            measurementTools = window.ComponentFactory.create('measurement-tools', 'measurement-tools-container', {
                showInToolbar: true
            });

            // Spatial Analysis - تحلیل مکانی
            spatialAnalysis = window.ComponentFactory.create('spatial-analysis', 'spatial-analysis-container', {
                showInToolbar: true
            });

            // GoTo XY - رفتن به مختصات
            gotoXY = window.ComponentFactory.create('goto-xy', 'goto-xy-container', {
                showInToolbar: true
            });

            // Drawing Tools - ابزارهای ترسیم
            drawingTools = window.ComponentFactory.create('drawing-tools', 'drawing-tools-container', {
                showInToolbar: true
            });

            // Property Tools - ابزارهای خصوصیات
            propertyTools = window.ComponentFactory.create('property-tools', 'property-tools-container', {
                showInToolbar: true
            });
        }

        function setGlobalReferences() {
            // Set global references for backward compatibility
            window.layerTree = layerTree;
            window.mapViews = mapViews;
            window.toolbar = toolbar;
            window.measurementTools = measurementTools;
            window.spatialAnalysis = spatialAnalysis;
            window.gotoXY = gotoXY;
            window.drawingTools = drawingTools;
            window.propertyTools = propertyTools;
        }

        // ========================================
        // تنظیمات رابط کاربری
        // ========================================

        function setupHeaderButtons() {
            console.log('🎛️ Setting up header buttons...');

            // دکمه تغییر حالت سایدبار
            $('#sidebar_toggle').on('click', function(e) {
                e.preventDefault();
                animateSidebar();
            });

            // دکمه page_toggle - رفتن به صفحه اصلی
            $('#page_toggle').on('click', function(e) {
                e.preventDefault();
                window.location.href = '@Url.Action("Index", "Home")';
            });

            console.log('✅ Header buttons configured');
        }

        function animateSidebar() {
            const sidebar = document.getElementById('sidebar');
            const map = document.getElementById('map');

            if (sidebar.classList.contains('hidden')) {
                sidebar.classList.remove('hidden');
                map.classList.remove('full-width');
            } else {
                sidebar.classList.add('hidden');
                map.classList.add('full-width');
            }

            // Trigger map resize after animation
            setTimeout(() => {
                if (window.map) {
                    window.map.invalidateSize();
                }
            }, 350);
        }

        // ========================================
        // توابع کمکی برای سازگاری با کدهای قدیمی
        // ========================================

        function resetTools() {
            if (mapCore) {
                mapCore.resetTools();
            }
        }

        function activateIdentifyTool() {
            if (toolbar) {
                toolbar.activateTool('identify');
            }
        }

        // ========================================
        // توابع کمکی برای سازگاری با کدهای قدیمی
        // ========================================

        // ========================================
        // توابع مدیریت لایه‌ها - Layer Management
        // ========================================

        function refreshTree() {
            if (layerTree && layerTree.refreshTree) {
                layerTree.refreshTree();
            }
        }

        function SettingLayer(layerid) {
            if (layerTree && layerTree.openLayerSettings) {
                layerTree.openLayerSettings(layerid);
            }
        }

        function setSymbologyByID(layerid, sym) {
            if (layerTree && layerTree.setSymbology) {
                layerTree.setSymbology(layerid, sym);
            }
        }

        // این توابع حالا توسط کامپوننت layer-tree مدیریت می‌شوند

        // ========================================
        // متغیرهای کمکی
        // ========================================

        var settinglayerid = null;

        // ========================================
        // توابع کمکی و ابزارها - Utility Functions
        // ========================================

        function getUTMLabel(arrlatlng) {
            if (measurementTools && measurementTools.getUTMLabel) {
                return measurementTools.getUTMLabel(arrlatlng);
            }
            return "UTM: " + arrlatlng[0].toFixed(0) + ", " + arrlatlng[1].toFixed(0);
        }

        function setScale() {
            if (window.map && $("#scaleText").length) {
                try {
                    var bounds = window.map.getBounds();
                    var distance = bounds.getNorthEast().distanceTo(bounds.getSouthWest());
                    var scale = Math.round(distance * 1000 / window.map.getSize().x);
                    $("#scaleText").html("1:" + scale.toLocaleString());
                } catch (error) {
                    console.error('Error calculating scale:', error);
                }
            }
        }

        function enableTool() {
            if (mapCore) {
                mapCore.resetTools();
            }
        }

        function hideAllDropdowns() {
            $('.dropdown-menu').removeClass('show');
        }

        // ========================================
        // سیستم‌های مختصات - Coordinate Systems
        // ========================================

        var projectIndex = 0;
        var projectSystems = [
            '',
            'PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0],AUTHORITY["ESRI","102100"]]'
        ];

        function setproject(index) {
            projectIndex = index;
        }

        function Project(latlng) {
            if (projectIndex == 0)
                return [latlng.lng, latlng.lat];
            return proj4(projectSystems[projectIndex]).forward([latlng.lng, latlng.lat]);
        }

        function ProjectInverse(x, y) {
            if (projectIndex == 0)
                return [x, y];
            return proj4(projectSystems[projectIndex]).inverse([x, y]);
        }

        // ========================================
        // متغیرهای سازگاری - Compatibility Variables
        // ========================================

        var toolname = '';
        var identifiedFeature = null;
        var highlighFeature = null;
        var isIdentifySaman = false;
        var polygcreate = null;
        var gotoxyMarker = null;
        var totalPages = 0;
        var currentPage = 1;
        var currentFeatureCollection = null;
        var styleGraphic = { color: '#820091', weight: 3, fillColor: '#820091', dashArray: '5, 5', fillOpacity: 0.2 };
        var styleGraphic2 = { color: '#ed0000', weight: 3, fillColor: '#ed0000', dashArray: '5, 5', fillOpacity: 0.1 };



        // ========================================
        // توابع انتخاب و تحلیل مکانی - Spatial Analysis Functions
        // ========================================

        function performSelectionByLayer(layerId) {
            if (spatialAnalysis && spatialAnalysis.performSelectionByLayer) {
                spatialAnalysis.performSelectionByLayer(layerId);
            }
        }

        function activateSelectTool(type) {
            if (spatialAnalysis && spatialAnalysis.activateSelectTool) {
                spatialAnalysis.activateSelectTool(type);
            }
        }

        function performSelectionByGraphic(toolType, spatialRel, layerId) {
            if (spatialAnalysis && spatialAnalysis.performSelectionByGraphic) {
                spatialAnalysis.performSelectionByGraphic(toolType, spatialRel, layerId);
            }
        }

        function clearActiveSelection() {
            if (spatialAnalysis && spatialAnalysis.clearActiveSelection) {
                spatialAnalysis.clearActiveSelection();
            }
        }

        function invertSelection() {
            if (spatialAnalysis && spatialAnalysis.invertSelection) {
                spatialAnalysis.invertSelection();
            }
        }

        function clearAllSelections() {
            if (spatialAnalysis && spatialAnalysis.clearAllSelections) {
                spatialAnalysis.clearAllSelections();
            }
        }

        function convertGeoJsonToWKT(geometry) {
            if (spatialAnalysis && spatialAnalysis.convertGeoJsonToWKT) {
                return spatialAnalysis.convertGeoJsonToWKT(geometry);
            }
            return null;
        }

        function selectFeaturesByGeometry(geometry, layers, options) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesByGeometry) {
                return spatialAnalysis.selectFeaturesByGeometry(geometry, layers, options);
            }
            return Promise.reject(new Error('Spatial analysis component not available'));
        }

        function selectFeaturesByBounds(bounds, layers, options) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesByBounds) {
                return spatialAnalysis.selectFeaturesByBounds(bounds, layers, options);
            }
            return Promise.reject(new Error('Spatial analysis component not available'));
        }

        function selectFeaturesByPoint(latlng, layers, options) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesByPoint) {
                return spatialAnalysis.selectFeaturesByPoint(latlng, layers, options);
            }
            return Promise.reject(new Error('Spatial analysis component not available'));
        }

        function selectFeaturesByPolygon(coordinates, layers, options) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesByPolygon) {
                return spatialAnalysis.selectFeaturesByPolygon(coordinates, layers, options);
            }
            return Promise.reject(new Error('Spatial analysis component not available'));
        }

        function selectFeaturesByWKT(wktGeometry, layerIds) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesByWKT) {
                return spatialAnalysis.selectFeaturesByWKT(wktGeometry, layerIds);
            }
            return Promise.reject(new Error('Spatial analysis component not available'));
        }

        function selectFeaturesInCurrentView() {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesInCurrentView) {
                spatialAnalysis.selectFeaturesInCurrentView();
            }
        }

        function selectFeaturesAtPoint(e) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesAtPoint) {
                spatialAnalysis.selectFeaturesAtPoint(e);
            }
        }

        function selectFeaturesInDrawnPolygon(layer) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesInDrawnPolygon) {
                spatialAnalysis.selectFeaturesInDrawnPolygon(layer);
            }
        }

        function displaySelectionResults(selectionResult) {
            if (spatialAnalysis && spatialAnalysis.displaySelectionResults) {
                spatialAnalysis.displaySelectionResults(selectionResult);
            }
        }

        // تابع شناسایی لایه‌ها - توسط کامپوننت property-tools مدیریت می‌شود
        function identifyLayers(e) {
            if (propertyTools && propertyTools.identifyLayers) {
                propertyTools.identifyLayers(e);
            }
        }

        // توابع شناسایی - توسط کامپوننت property-tools مدیریت می‌شوند
        var totalPages = 0;
        var currentPage = 1;
        var currentFeatureCollection = null;

        function initializeIdentifyPanel(featureCollection) {
            if (propertyTools && propertyTools.initializeIdentifyPanel) {
                propertyTools.initializeIdentifyPanel(featureCollection);
            }
        }
        // توابع شناسایی - توسط کامپوننت property-tools مدیریت می‌شوند
        function showPage(pageNum) {
            if (propertyTools && propertyTools.showPage) {
                propertyTools.showPage(pageNum);
            }
        }

        function changePage(pageNum) {
            if (propertyTools && propertyTools.changePage) {
                propertyTools.changePage(pageNum);
            }
        }

        function getVisibleLayers() {
            if (layerTree && layerTree.getVisibleLayers) {
                return layerTree.getVisibleLayers();
            }
            return '';
        }

        function zoomToIdentifyFeature(featureIndex) {
            if (propertyTools && propertyTools.zoomToFeature) {
                propertyTools.zoomToFeature(featureIndex);
            }
        }

        function flashIdentifyFeature(featureIndex) {
            if (propertyTools && propertyTools.flashFeature) {
                propertyTools.flashFeature(featureIndex);
            }
        }

        function loadRelatedRecords(featureId, targetId) {
            if (propertyTools && propertyTools.loadRelatedRecords) {
                propertyTools.loadRelatedRecords(featureId, targetId);
            }
        }

        function loadDocuments(featureId, targetId) {
            if (propertyTools && propertyTools.loadDocuments) {
                propertyTools.loadDocuments(featureId, targetId);
            }
        }

        // ========================================
        // توابع انتخاب - توسط کامپوننت spatial-analysis مدیریت می‌شوند
        // ========================================

        function selectFeaturesByGeometry(geometry, layers, options) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesByGeometry) {
                return spatialAnalysis.selectFeaturesByGeometry(geometry, layers, options);
            }
            return Promise.reject(new Error('Spatial analysis component not available'));
        }

        // توابع انتخاب - توسط کامپوننت spatial-analysis مدیریت می‌شوند
        function selectFeaturesByBounds(bounds, layers, options) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesByBounds) {
                return spatialAnalysis.selectFeaturesByBounds(bounds, layers, options);
            }
            return Promise.reject(new Error('Spatial analysis component not available'));
        }

        function selectFeaturesByPoint(latlng, layers, options) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesByPoint) {
                return spatialAnalysis.selectFeaturesByPoint(latlng, layers, options);
            }
            return Promise.reject(new Error('Spatial analysis component not available'));
        }

        function selectFeaturesByPolygon(coordinates, layers, options) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesByPolygon) {
                return spatialAnalysis.selectFeaturesByPolygon(coordinates, layers, options);
            }
            return Promise.reject(new Error('Spatial analysis component not available'));
        }

        // توابع نمایش نتایج - توسط کامپوننت spatial-analysis مدیریت می‌شوند
        function displaySelectionResults(selectionResult) {
            if (spatialAnalysis && spatialAnalysis.displaySelectionResults) {
                spatialAnalysis.displaySelectionResults(selectionResult);
            }
        }

        // توابع انتخاب - توسط کامپوننت spatial-analysis مدیریت می‌شوند
        function selectFeaturesInCurrentView() {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesInCurrentView) {
                spatialAnalysis.selectFeaturesInCurrentView();
            }
        }

        function selectFeaturesAtPoint(e) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesAtPoint) {
                spatialAnalysis.selectFeaturesAtPoint(e);
            }
        }

        function selectFeaturesInDrawnPolygon(layer) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesInDrawnPolygon) {
                spatialAnalysis.selectFeaturesInDrawnPolygon(layer);
            }
        }

        function selectFeaturesByWKT(wktGeometry, layerIds) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesByWKT) {
                return spatialAnalysis.selectFeaturesByWKT(wktGeometry, layerIds);
            }
            return Promise.reject(new Error('Spatial analysis component not available'));
        }

        // تابع رفتن به موقعیت خاص - توسط کامپوننت goto-xy مدیریت می‌شود
        function togglegotoxy() {
            if (gotoXY && gotoXY.toggle) {
                gotoXY.toggle();
            }
        }

        // ========================================
        // توابع GoToXY - توسط کامپوننت goto-xy مدیریت می‌شوند
        // ========================================

        var gotoxyMarker = null;

        function updateCoordinateLabels() {
            if (gotoXY && gotoXY.updateCoordinateLabels) {
                gotoXY.updateCoordinateLabels();
            }
        }

        function goToCoordinates() {
            if (gotoXY && gotoXY.goToCoordinates) {
                gotoXY.goToCoordinates();
            }
        }

        function transformCoordinates(x, y, system) {
            if (gotoXY && gotoXY.transformCoordinates) {
                return gotoXY.transformCoordinates(x, y, system);
            }
            return null;
        }

        function transformUTMToWGS84(easting, northing, zone) {
            if (gotoXY && gotoXY.transformUTMToWGS84) {
                return gotoXY.transformUTMToWGS84(easting, northing, zone);
            }
            return null;
        }

        function getSystemName(system) {
            if (gotoXY && gotoXY.getSystemName) {
                return gotoXY.getSystemName(system);
            }
            return 'نامشخص';
        }

        function closeGoToXYDialog() {
            if (gotoXY && gotoXY.closeDialog) {
                gotoXY.closeDialog();
            }
        }
 
        // ========================================
        // توابع انتخاب - توسط کامپوننت spatial-analysis مدیریت می‌شوند
        // ========================================

        var polygcreate;
        var styleGraphic = { color: '#820091', weight: 3, fillColor: '#820091', dashArray: '5, 5', fillOpacity: 0.2 };
        var styleGraphic2 = { color: '#ed0000', weight: 3, fillColor: '#ed0000', dashArray: '5, 5', fillOpacity: 0.1 };

        function changeColorGraphicLayer(color) {
            if (spatialAnalysis && spatialAnalysis.changeColorGraphicLayer) {
                spatialAnalysis.changeColorGraphicLayer(color);
            }
        }

        function activeSelectBox() {
            if (spatialAnalysis && spatialAnalysis.activeSelectBox) {
                spatialAnalysis.activeSelectBox();
            }
        }

        function activeSelectRectangle() {
            if (spatialAnalysis && spatialAnalysis.activeSelectRectangle) {
                spatialAnalysis.activeSelectRectangle();
            }
        }

        function activeSelectCircle() {
            if (spatialAnalysis && spatialAnalysis.activeSelectCircle) {
                spatialAnalysis.activeSelectCircle();
            }
        }

        function activeSelectPolygon() {
            if (spatialAnalysis && spatialAnalysis.activeSelectPolygon) {
                spatialAnalysis.activeSelectPolygon();
            }
        }

        function activeSelectMarker() {
            if (spatialAnalysis && spatialAnalysis.activeSelectMarker) {
                spatialAnalysis.activeSelectMarker();
            }
        }

        function activeSelectPolyline() {
            if (spatialAnalysis && spatialAnalysis.activeSelectPolyline) {
                spatialAnalysis.activeSelectPolyline();
            }
        }

        function deactiveSelectBox() {
            if (spatialAnalysis && spatialAnalysis.deactiveSelectBox) {
                spatialAnalysis.deactiveSelectBox();
            }
        }

        function deleteAllGraphic() {
            if (spatialAnalysis && spatialAnalysis.deleteAllGraphic) {
                spatialAnalysis.deleteAllGraphic();
            }
        }
        
        function ZoomToExtentLayer(layerId, selected) {
            if (layerTree && layerTree.zoomToExtentLayer) {
                layerTree.zoomToExtentLayer(layerId, selected);
            }
        }

        function highlightFeature(feature) {
            if (propertyTools && propertyTools.highlightFeature) {
                propertyTools.highlightFeature(feature);
            }
        }

        function flashFeature(feature) {
            if (propertyTools && propertyTools.flashFeature) {
                propertyTools.flashFeature(feature);
            }
        }

        // === مدیریت انتخاب‌های کاربر - توسط کامپوننت spatial-analysis مدیریت می‌شوند ===

        function getUserSelections(layerId) {
            if (spatialAnalysis && spatialAnalysis.getUserSelections) {
                return spatialAnalysis.getUserSelections(layerId);
            }
            return Promise.resolve([]);
        }

        function clearUserSelections(layerId) {
            if (spatialAnalysis && spatialAnalysis.clearUserSelections) {
                return spatialAnalysis.clearUserSelections(layerId);
            }
            return Promise.resolve(false);
        }

        function toggleUserSelectionsHighlight(layerId, highlight) {
            if (spatialAnalysis && spatialAnalysis.toggleUserSelectionsHighlight) {
                return spatialAnalysis.toggleUserSelectionsHighlight(layerId, highlight);
            }
            return Promise.resolve(false);
        }

        function refreshMapLayer(layerId) {
            if (layerTree && layerTree.refreshMapLayer) {
                layerTree.refreshMapLayer(layerId);
            }
        }

        function showUserSelectionsInfo(layerId) {
            if (spatialAnalysis && spatialAnalysis.showUserSelectionsInfo) {
                spatialAnalysis.showUserSelectionsInfo(layerId);
            }
        }

        function addSelectionManagementToLayerMenu(layerId) {
            if (spatialAnalysis && spatialAnalysis.addSelectionManagementToLayerMenu) {
                spatialAnalysis.addSelectionManagementToLayerMenu(layerId);
            }
        }

        // === مدیریت نوار ابزار - توسط کامپوننت toolbar مدیریت می‌شود ===

        function openToolPage(toolType) {
            if (toolbar && toolbar.openToolPage) {
                toolbar.openToolPage(toolType);
            }
        }

        function closeToolPage() {
            if (toolbar && toolbar.closeToolPage) {
                toolbar.closeToolPage();
            }
        }

        function loadToolContent(toolType) {
            if (toolbar && toolbar.loadToolContent) {
                toolbar.loadToolContent(toolType);
            }
        }


        // === توابع BaseMap - توسط کامپوننت base-map-manager مدیریت می‌شوند ===
        function changeBaseMapIndex(index) {
            if (baseMapManager && baseMapManager.changeBaseMap) {
                baseMapManager.changeBaseMap(index);
            }
        }

        function changeBaseMap() {
            if (baseMapManager && baseMapManager.changeBaseMap) {
                const current = baseMapManager.getCurrentBaseMap();
                const nextIndex = (current.index + 1) % baseMapManager.baseMaps.length;
                baseMapManager.changeBaseMap(nextIndex);
            }
        }
    
        document.addEventListener('DOMContentLoaded', function() {
            // مقداردهی کامپوننت‌های صفحه
            initializeGeoMapComponents();

            // اتصال رویدادهای کامپوننت‌ها
            bindGeoMapEvents();

            console.log('GeoMap page initialized with modern components');
        });

        function initializeGeoMapComponents() {
            // مقداردهی کامپوننت‌ها توسط ComponentFactory انجام می‌شود
            if (window.ComponentFactory) {
                window.ComponentFactory.initializeAll();
            }
        }

        function bindGeoMapEvents() {
            // اتصال رویدادها توسط کامپوننت‌ها انجام می‌شود
            if (window.mapCore) {
                window.mapCore.bindEvents();
            }
        }

        function updateActiveLayer(layerId, layerData) {
            const activeLayerInput = document.getElementById('activeLayerLable');
            const activeLayerHidden = document.getElementById('activeLayer');

            if (activeLayerInput && layerData) {
                activeLayerInput.value = layerData.aliasName || layerData.name;
            }

            if (activeLayerHidden) {
                activeLayerHidden.value = layerId;
            }
        }

        function showSearchToolModal(searchType) {
            if (searchTools && searchTools.showModal) {
                searchTools.showModal(searchType);
            }
        }

        // تابع سراسری برای استفاده در onclick handlers
        window.showSearchTool = showSearchToolModal;

         
    </script>
}