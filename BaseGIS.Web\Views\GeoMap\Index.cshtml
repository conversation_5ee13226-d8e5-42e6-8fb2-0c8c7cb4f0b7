@using System.Text.Json
@using BaseGIS.Core.Entities
@using BaseGIS.Web.ViewModels
@model IEnumerable<BaseGIS.Core.Entities.GroupInfo>
@{
    ViewData["Title"] = "نقشه";
    Layout = "~/Views/Shared/_LayoutMap.cshtml";

    string treeData = ViewBag.TreeData;
    List<BaseMapViewModel> ListBaseMap = ViewBag.ListBaseMap ?? new List<BaseMapViewModel>();
    int BaseMapID = ViewBag.BaseMapID;
    var Theme = ViewBag.Theme;
    BaseMapConfigViewModel BaseMapConfig = ViewBag.BaseMapConfig ?? new BaseMapConfigViewModel();
    Dictionary<string, string> Themes = new Dictionary<string, string>();

    if (Theme != null && Theme != "")
    {
        var tms = Theme.Split(',');
        for (int i = 0; i < tms.Length; i++)
        {
            var tms1 = tms[i].Split(':');
            if (tms1.Length > 1)
            {
                // Store the HTML color string directly
                Themes.Add(tms1[0].ToLower(), tms1[1]);
            }
        }
    }
}

@section Styles {
    <!-- ===== کتابخانه‌های CSS از LibMan ===== -->
    <link href="~/lib/jqueryui/themes/base/jquery-ui.min.css" rel="stylesheet" />
    <link href="~/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css" rel="stylesheet" />
    <link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
    <link href="~/lib/sweetalert2/sweetalert2.min.css" rel="stylesheet" />
    <link href="~/lib/leaflet-dialog/Leaflet.Dialog.css" rel="stylesheet" />
    <link href="~/lib/leaflet-measure/leaflet-measure.css" rel="stylesheet" />
    <link href="~/lib/ion-rangeslider/css/ion.rangeslider.min.css" rel="stylesheet" />
    <link href="~/lib/leaflet-draw/dist/leaflet.draw.css" rel="stylesheet" />

    <!-- ===== استایل‌های کامپوننت‌ها ===== -->
    <link href="~/css/components.css" rel="stylesheet" />
    <link href="~/css/components/measurement-tools.css" rel="stylesheet" />
    <link href="~/css/components/spatial-analysis.css" rel="stylesheet" />
    <link href="~/css/components/goto-xy.css" rel="stylesheet" />
    <link href="~/css/components/drawing-tools.css" rel="stylesheet" />
    <link href="~/css/components/property-tools.css" rel="stylesheet" />

    <style>
        /* استایل‌های اصلی صفحه */
        .map-container {
            display: flex;
            height: calc(100vh - 62px);
            direction: rtl;
        }

        #sidebar {
            width: 250px;
            height: 100%;
            transition: width 0.35s ease;
            overflow: hidden;
            background: #fff;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--primary-color);
        }

        #sidebar.hidden {
            width: 0;
            display: block;
        }

        #map {
            width: calc(100% - 250px);
            height: 100%;
            position: relative;
            order: 1;
        }

        #map.full-width {
            width: 100%;
        }

        /* Responsive */
        @@media (max-width: 768px) {
            #sidebar {
                width: 200px;
            }

            #map {
                width: calc(100% - 200px);
            }

            #sidebar.hidden + #map {
                width: 100%;
            }
        }
    
        /* استایل‌های اصلی صفحه */
        .map-container {
            display: flex;
            height: calc(100vh - 62px);
            direction: rtl;
        }

        #sidebar {
            width: 250px;
            height: 100%;
            transition: width 0.35s ease;
            overflow: hidden;
            background: #fff;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--primary-color);
        }

        #sidebar.hidden {
            width: 0;
            display: block;
        }

        #map {
            width: calc(100% - 250px);
            height: 100%;
            position: relative;
            order: 1;
        }

        #map.full-width {
            width: 100%;
        }

        /* Tab Container */
        .tab-container {
            height: calc(100vh - 120px);
            display: flex;
            flex-direction: column;
        }

        .tab-container .nav-tabs {
            border-bottom: 1px solid #dee2e6;
            background: #f8f9fa;
            margin: 0;
            padding: 0 6px;
            flex-shrink: 0;
        }

        .tab-container .nav-tabs .nav-link {
            border: none;
            border-radius: 0;
            padding: 8px 12px;
            font-size: 12px;
            color: #6c757d;
            background: transparent;
        }

        .tab-container .nav-tabs .nav-link:hover {
            border-color: transparent;
            background: rgba(0,123,255,0.1);
            color: #007bff;
        }

        .tab-container .nav-tabs .nav-link.active {
            color: #007bff;
            background: #fff;
            border-bottom: 2px solid #007bff;
            font-weight: 500;
        }

        .tab-container .tab-content {
            flex: 1;
            overflow: hidden;
            background: #fff;
        }

        .tab-container .tab-pane {
            height: 100%;
            overflow-y: auto;
        }

        /* Responsive */
        @@media (max-width: 768px) {
            #sidebar {
                width: 200px;
            }

            #map {
                width: calc(100% - 200px);
            }

            #sidebar.hidden + #map {
                width: 100%;
            }
        }
    </style>
}

<!-- ===== صفحه اصلی نقشه ===== -->
<div class="map-container">
    <!-- ===== سایدبار ===== -->
    <div id="sidebar">
        <div class="tab-container">
            <ul class="nav nav-tabs" id="sidebarTabs">
                <li class="nav-item">
                    <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#layers-panel">لایه‌ها</button>
                </li>
                <li class="nav-item">
                    <button class="nav-link" data-bs-toggle="tab" data-bs-target="#views-panel">نقشه‌ها</button>
                </li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane fade show active" id="layers-panel">
                    <div id="layer-tree-container" data-component="layer-tree"></div>
                </div>
                <div class="tab-pane fade" id="views-panel">
                    <div id="map-views-container" data-component="map-views"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- ===== نقشه اصلی ===== -->
    <div id="map">
        <!-- کامپوننت‌های ابزار -->
        <div id="map-core-container" data-component="map-core"></div>
        <div id="toolbar-container" data-component="toolbar"></div>
        <div id="measurement-tools-container" data-component="measurement-tools"></div>
        <div id="spatial-analysis-container" data-component="spatial-analysis"></div>
        <div id="goto-xy-container" data-component="goto-xy"></div>
        <div id="drawing-tools-container" data-component="drawing-tools"></div>
        <div id="property-tools-container" data-component="property-tools"></div>
    </div>
</div>

<!-- ===== Modal ها و Panel های ضروری ===== -->
<div class="modal" id="dialog-Property" tabindex="-1" role="dialog" aria-labelledby="remoteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content"></div>
    </div>
</div>

<div id="panel-Identify" style="position:absolute; display:none; top:50px; bottom:10px; right: 0px; width:400px; height:calc(100vh - 50px); background-color:#fff;border:1px solid gray; z-index:1000;"></div>
@section Scripts {
    <!-- ===== کتابخانه‌های JavaScript از LibMan ===== -->
    <script src="~/lib/jqueryui/jquery-ui.min.js"></script>
    <script src="~/lib/jquery.fancytree/jquery.fancytree-all.min.js"></script>
    <script src="~/lib/select2/js/select2.min.js"></script>
    <script src="~/lib/sweetalert2/sweetalert2.min.js"></script>
    <script src="~/lib/esri-leaflet/dist/esri-leaflet.js"></script>
    <script src="~/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js"></script>
    <script src="~/lib/leaflet-draw/dist/leaflet.draw.js"></script>
    <script src="~/lib/leaflet-dialog/Leaflet.Dialog.js"></script>
    <script src="~/lib/leaflet-measure/leaflet-measure.js"></script>
    <script src="~/lib/proj4/dist/proj4.js"></script>
    <script src="~/lib/ion-rangeslider/js/ion.rangeslider.min.js"></script>

    <!-- ===== کامپوننت‌های پایه ===== -->
    <script src="~/js/components/base-component.js"></script>
    <script src="~/js/components/component-factory.js"></script>

    <!-- ===== کامپوننت‌های GeoMap ===== -->
    <script src="~/js/geomap/map-core.js"></script>
    <script src="~/js/geomap/toolbar.js"></script>
    <script src="~/js/geomap/layer-tree.js"></script>
    <script src="~/js/geomap/map-views.js"></script>
    <script src="~/js/geomap/baseMapManager.js"></script>
    <script src="~/js/geomap/measurement-tools.js"></script>
    <script src="~/js/geomap/spatial-analysis.js"></script>
    <script src="~/js/geomap/goto-xy.js"></script>
    <script src="~/js/geomap/drawing-tools.js"></script>
    <script src="~/js/geomap/property-tools.js"></script>
    
    <script>
        // ========================================
        // متغیرهای سراسری
        // ========================================
        var baseUrl = '@ViewBag.BaseUrl';
        var URLBASE = "@Url.Content("~")";
        var treeData = @Html.Raw(treeData ?? "[]");

        // Component instances
        var mapCore = null;
        var toolbar = null;
        var layerTree = null;
        var mapViews = null;
        var measurementTools = null;
        var spatialAnalysis = null;
        var gotoXY = null;
        var drawingTools = null;
        var propertyTools = null;

        // Global variables for backward compatibility
        var map, lyrCo = [], editableLayers, markerLayers, drawControl, baseMapManager;
        var isIdentify = false, isSketch = false, selecttool = false, selectBoxtool = false;

        $(document).ready(function() {
            // ========================================
            // مقداردهی کامپوننت‌ها
            // ========================================
            initializeComponents();
            setupHeaderButtons();
        });

        function initializeComponents() {
            // ========================================
            // مقداردهی کامپوننت‌های اصلی
            // ========================================

            // 1. Map Core - هسته نقشه
            mapCore = window.ComponentFactory.create('map-core', 'map-core-container', {
                center: [32.702222, 51.979378],
                zoom: 5,
                baseUrl: baseUrl,
                mapIndex: "1",
                onMapReady: function(mapInstance) {
                    console.log('Map ready');
                    // Initialize other components after map is ready
                    initializeUIComponents();
                }
            });

            if (mapCore) {
                mapCore.initializeMap();
                // Set global references
                window.mapCore = mapCore;
            }
        }

        function initializeUIComponents() {
            // ========================================
            // مقداردهی کامپوننت‌های رابط کاربری
            // ========================================

            // 2. Layer Tree - درخت لایه‌ها
            layerTree = window.ComponentFactory.create('layer-tree', 'layer-tree-container', {
                treeData: treeData,
                baseUrl: baseUrl,
                onLayerToggle: function(layerId, visible) {
                    console.log('Layer toggled:', layerId, visible);
                }
            });

            // 3. Map Views - نقشه‌های ذخیره شده
            mapViews = window.ComponentFactory.create('map-views', 'map-views-container', {
                onViewLoad: function(view) {
                    console.log('View loaded:', view);
                }
            });

            // 4. Toolbar - نوار ابزار
            toolbar = window.ComponentFactory.create('toolbar', 'toolbar-container', {
                position: 'topleft',
                tools: ['pan', 'identify', 'measurement', 'spatial-analysis', 'goto-xy', 'drawing', 'property'],
                onToolActivate: function(tool) {
                    console.log('Tool activated:', tool);
                }
            });

            // 5. Measurement Tools - ابزارهای اندازه‌گیری
            measurementTools = window.ComponentFactory.create('measurement-tools', 'measurement-tools-container', {
                showInToolbar: true
            });

            // 6. Spatial Analysis - تحلیل مکانی
            spatialAnalysis = window.ComponentFactory.create('spatial-analysis', 'spatial-analysis-container', {
                showInToolbar: true
            });

            // 7. GoTo XY - رفتن به مختصات
            gotoXY = window.ComponentFactory.create('goto-xy', 'goto-xy-container', {
                showInToolbar: true
            });

            // 8. Drawing Tools - ابزارهای ترسیم
            drawingTools = window.ComponentFactory.create('drawing-tools', 'drawing-tools-container', {
                showInToolbar: true
            });

            // 9. Property Tools - ابزارهای خصوصیات
            propertyTools = window.ComponentFactory.create('property-tools', 'property-tools-container', {
                showInToolbar: true
            });

            // Set global references
            window.layerTree = layerTree;
            window.mapViews = mapViews;
            window.toolbar = toolbar;
            window.measurementTools = measurementTools;
            window.spatialAnalysis = spatialAnalysis;
            window.gotoXY = gotoXY;
            window.drawingTools = drawingTools;
            window.propertyTools = propertyTools;

            console.log('All components initialized successfully');
        }

        // ========================================
        // توابع کمکی برای سازگاری با کدهای قدیمی
        // ========================================

        function resetTools() {
            if (mapCore) {
                mapCore.resetTools();
            }
        }

        function activateIdentifyTool() {
            if (toolbar) {
                toolbar.activateTool('identify');
            }
        }

        function setupHeaderButtons() {
            // دکمه تغییر حالت سایدبار
            $('#sidebar_toggle').on('click', function(e) {
                e.preventDefault();
                animateSidebar();
            });

            // دکمه page_toggle - رفتن به صفحه اصلی
            $('#page_toggle').on('click', function(e) {
                e.preventDefault();
                window.location.href = '@Url.Action("Index", "Home")';
            });
        }

        // ========================================
        // توابع کمکی برای سازگاری با کدهای قدیمی
        // ========================================

        function animateSidebar() {
            const sidebar = document.getElementById('sidebar');
            const map = document.getElementById('map');

            if (sidebar.classList.contains('hidden')) {
                sidebar.classList.remove('hidden');
                map.classList.remove('full-width');
            } else {
                sidebar.classList.add('hidden');
                map.classList.add('full-width');
            }

            // Trigger map resize after animation
            setTimeout(() => {
                if (window.map) {
                    window.map.invalidateSize();
                }
            }, 350);
        }

        // ========================================
        // توابع ضروری برای سازگاری با کدهای قدیمی
        // ========================================

        // این توابع توسط کامپوننت layer-tree مدیریت می‌شوند
        function refreshTree() {
            if (layerTree && layerTree.refreshTree) {
                layerTree.refreshTree();
            }
        }

        function SettingLayer(layerid) {
            if (layerTree && layerTree.openLayerSettings) {
                layerTree.openLayerSettings(layerid);
            }
        }

        function setSymbologyByID(layerid, sym) {
            if (layerTree && layerTree.setSymbology) {
                layerTree.setSymbology(layerid, sym);
            }
        }

        // ========================================
        // توابع ضروری برای سازگاری
        // ========================================

        // این توابع حذف شده‌اند و توسط کامپوننت‌ها مدیریت می‌شوند
        var settinglayerid = null;

        // توابع ضروری که توسط کامپوننت‌ها مدیریت می‌شوند
        function setPropertyProps(layerid) {
            if (layerTree && layerTree.setPropertyProps) {
                layerTree.setPropertyProps(layerid);
            }
        }

        function setlableopacity() {
            if (layerTree && layerTree.setLabelOpacity) {
                layerTree.setLabelOpacity();
            }
        }

        function setSymbology() {
            if (layerTree && layerTree.setSymbology) {
                layerTree.setSymbology();
            }
        }

        // ========================================
        // توابع کمکی ضروری
        // ========================================

        // این توابع توسط کامپوننت‌ها مدیریت می‌شوند
        function getUTMLabel(arrlatlng) {
            if (measurementTools && measurementTools.getUTMLabel) {
                return measurementTools.getUTMLabel(arrlatlng);
            }
            return "UTM: " + arrlatlng[0].toFixed(0) + ", " + arrlatlng[1].toFixed(0);
        }

        function setScale() {
            if (window.map && $("#scaleText").length) {
                try {
                    var bounds = window.map.getBounds();
                    var distance = bounds.getNorthEast().distanceTo(bounds.getSouthWest());
                    var scale = Math.round(distance * 1000 / window.map.getSize().x);
                    $("#scaleText").html("1:" + scale.toLocaleString());
                } catch (error) {
                    console.error('Error calculating scale:', error);
                }
            }
        }

        // سیستم‌های مختصات
        var projectIndex = 0;
        var projectSystems = [
            '',
            'PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0],AUTHORITY["ESRI","102100"]]'
        ];

        function setproject(index) {
            projectIndex = index;
        }

        function Project(latlng) {
            if (projectIndex == 0)
                return [latlng.lng, latlng.lat];
            return proj4(projectSystems[projectIndex]).forward([latlng.lng, latlng.lat]);
        }

        function ProjectInverse(x, y) {
            if (projectIndex == 0)
                return [x, y];
            return proj4(projectSystems[projectIndex]).inverse([x, y]);
        }


        // ========================================
        // توابع ضروری برای سازگاری
        // ========================================

        // این توابع توسط کامپوننت‌ها مدیریت می‌شوند
        function enableTool() {
            if (mapCore) {
                mapCore.resetTools();
            }
        }

        // توابع سازگاری برای کدهای قدیمی
        var toolname = '';
        var identifiedFeature = null;
        var highlighFeature = null;
        var isIdentifySaman = false;
        var polygcreate = null;

        // ========================================
        // توابع ضروری برای سازگاری با کدهای قدیمی
        // ========================================

        // این توابع توسط کامپوننت‌ها مدیریت می‌شوند
        function performSelectionByLayer(layerId) {
            if (spatialAnalysis && spatialAnalysis.performSelectionByLayer) {
                spatialAnalysis.performSelectionByLayer(layerId);
            }
        }

        // تابع سراسری برای استفاده در onclick handlers
        window.performSelectionByLayer = performSelectionByLayer;

        // ========================================
        // توابع ضروری برای سازگاری
        // ========================================

        // این توابع توسط کامپوننت‌ها مدیریت می‌شوند
        function activateSelectTool(type) {
            if (spatialAnalysis && spatialAnalysis.activateSelectTool) {
                spatialAnalysis.activateSelectTool(type);
            }
        }

        function hideAllDropdowns() {
            $('.dropdown-menu').removeClass('show');
        }

        // توابع ضروری که توسط کامپوننت‌ها مدیریت می‌شوند
        function performSelectionByGraphic(toolType, spatialRel, layerId) {
            if (spatialAnalysis && spatialAnalysis.performSelectionByGraphic) {
                spatialAnalysis.performSelectionByGraphic(toolType, spatialRel, layerId);
            }
        }

        function clearActiveSelection() {
            if (spatialAnalysis && spatialAnalysis.clearActiveSelection) {
                spatialAnalysis.clearActiveSelection();
            }
        }

        function invertSelection() {
            if (spatialAnalysis && spatialAnalysis.invertSelection) {
                spatialAnalysis.invertSelection();
            }
        }

        function clearAllSelections() {
            if (spatialAnalysis && spatialAnalysis.clearAllSelections) {
                spatialAnalysis.clearAllSelections();
            }
        }

        function convertGeoJsonToWKT(geometry) {
            if (spatialAnalysis && spatialAnalysis.convertGeoJsonToWKT) {
                return spatialAnalysis.convertGeoJsonToWKT(geometry);
            }
            return null;
        }

        function getVisibleLayerIds() {
            if (layerTree && layerTree.getVisibleLayerIds) {
                return layerTree.getVisibleLayerIds();
            }
            return [];
        }

        // تابع شناسایی لایه‌ها - توسط کامپوننت property-tools مدیریت می‌شود
        function identifyLayers(e) {
            if (propertyTools && propertyTools.identifyLayers) {
                propertyTools.identifyLayers(e);
            }
        }

        // توابع شناسایی - توسط کامپوننت property-tools مدیریت می‌شوند
        var totalPages = 0;
        var currentPage = 1;
        var currentFeatureCollection = null;

        function initializeIdentifyPanel(featureCollection) {
            if (propertyTools && propertyTools.initializeIdentifyPanel) {
                propertyTools.initializeIdentifyPanel(featureCollection);
            }
        }
        // توابع شناسایی - توسط کامپوننت property-tools مدیریت می‌شوند
        function showPage(pageNum) {
            if (propertyTools && propertyTools.showPage) {
                propertyTools.showPage(pageNum);
            }
        }

        function changePage(pageNum) {
            if (propertyTools && propertyTools.changePage) {
                propertyTools.changePage(pageNum);
            }
        }

        function getVisibleLayers() {
            if (layerTree && layerTree.getVisibleLayers) {
                return layerTree.getVisibleLayers();
            }
            return '';
        }

        function zoomToIdentifyFeature(featureIndex) {
            if (propertyTools && propertyTools.zoomToFeature) {
                propertyTools.zoomToFeature(featureIndex);
            }
        }

        function flashIdentifyFeature(featureIndex) {
            if (propertyTools && propertyTools.flashFeature) {
                propertyTools.flashFeature(featureIndex);
            }
        }

        function loadRelatedRecords(featureId, targetId) {
            if (propertyTools && propertyTools.loadRelatedRecords) {
                propertyTools.loadRelatedRecords(featureId, targetId);
            }
        }

        function loadDocuments(featureId, targetId) {
            if (propertyTools && propertyTools.loadDocuments) {
                propertyTools.loadDocuments(featureId, targetId);
            }
        }

        // ========================================
        // توابع انتخاب - توسط کامپوننت spatial-analysis مدیریت می‌شوند
        // ========================================

        function selectFeaturesByGeometry(geometry, layers, options) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesByGeometry) {
                return spatialAnalysis.selectFeaturesByGeometry(geometry, layers, options);
            }
            return Promise.reject(new Error('Spatial analysis component not available'));
        }

        // توابع انتخاب - توسط کامپوننت spatial-analysis مدیریت می‌شوند
        function selectFeaturesByBounds(bounds, layers, options) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesByBounds) {
                return spatialAnalysis.selectFeaturesByBounds(bounds, layers, options);
            }
            return Promise.reject(new Error('Spatial analysis component not available'));
        }

        function selectFeaturesByPoint(latlng, layers, options) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesByPoint) {
                return spatialAnalysis.selectFeaturesByPoint(latlng, layers, options);
            }
            return Promise.reject(new Error('Spatial analysis component not available'));
        }

        function selectFeaturesByPolygon(coordinates, layers, options) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesByPolygon) {
                return spatialAnalysis.selectFeaturesByPolygon(coordinates, layers, options);
            }
            return Promise.reject(new Error('Spatial analysis component not available'));
        }

        // توابع نمایش نتایج - توسط کامپوننت spatial-analysis مدیریت می‌شوند
        function displaySelectionResults(selectionResult) {
            if (spatialAnalysis && spatialAnalysis.displaySelectionResults) {
                spatialAnalysis.displaySelectionResults(selectionResult);
            }
        }

        // توابع انتخاب - توسط کامپوننت spatial-analysis مدیریت می‌شوند
        function selectFeaturesInCurrentView() {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesInCurrentView) {
                spatialAnalysis.selectFeaturesInCurrentView();
            }
        }

        function selectFeaturesAtPoint(e) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesAtPoint) {
                spatialAnalysis.selectFeaturesAtPoint(e);
            }
        }

        function selectFeaturesInDrawnPolygon(layer) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesInDrawnPolygon) {
                spatialAnalysis.selectFeaturesInDrawnPolygon(layer);
            }
        }

        function selectFeaturesByWKT(wktGeometry, layerIds) {
            if (spatialAnalysis && spatialAnalysis.selectFeaturesByWKT) {
                return spatialAnalysis.selectFeaturesByWKT(wktGeometry, layerIds);
            }
            return Promise.reject(new Error('Spatial analysis component not available'));
        }

        // تابع رفتن به موقعیت خاص - توسط کامپوننت goto-xy مدیریت می‌شود
        function togglegotoxy() {
            if (gotoXY && gotoXY.toggle) {
                gotoXY.toggle();
            }
        }

        // ========================================
        // توابع GoToXY - توسط کامپوننت goto-xy مدیریت می‌شوند
        // ========================================

        var gotoxyMarker = null;

        function updateCoordinateLabels() {
            if (gotoXY && gotoXY.updateCoordinateLabels) {
                gotoXY.updateCoordinateLabels();
            }
        }

        function goToCoordinates() {
            if (gotoXY && gotoXY.goToCoordinates) {
                gotoXY.goToCoordinates();
            }
        }

        function transformCoordinates(x, y, system) {
            if (gotoXY && gotoXY.transformCoordinates) {
                return gotoXY.transformCoordinates(x, y, system);
            }
            return null;
        }

        function transformUTMToWGS84(easting, northing, zone) {
            if (gotoXY && gotoXY.transformUTMToWGS84) {
                return gotoXY.transformUTMToWGS84(easting, northing, zone);
            }
            return null;
        }

        function getSystemName(system) {
            if (gotoXY && gotoXY.getSystemName) {
                return gotoXY.getSystemName(system);
            }
            return 'نامشخص';
        }

        function closeGoToXYDialog() {
            if (gotoXY && gotoXY.closeDialog) {
                gotoXY.closeDialog();
            }
        }
 
        // ========================================
        // توابع انتخاب - توسط کامپوننت spatial-analysis مدیریت می‌شوند
        // ========================================

        var polygcreate;
        var styleGraphic = { color: '#820091', weight: 3, fillColor: '#820091', dashArray: '5, 5', fillOpacity: 0.2 };
        var styleGraphic2 = { color: '#ed0000', weight: 3, fillColor: '#ed0000', dashArray: '5, 5', fillOpacity: 0.1 };

        function changeColorGraphicLayer(color) {
            if (spatialAnalysis && spatialAnalysis.changeColorGraphicLayer) {
                spatialAnalysis.changeColorGraphicLayer(color);
            }
        }

        function activeSelectBox() {
            if (spatialAnalysis && spatialAnalysis.activeSelectBox) {
                spatialAnalysis.activeSelectBox();
            }
        }

        function activeSelectRectangle() {
            if (spatialAnalysis && spatialAnalysis.activeSelectRectangle) {
                spatialAnalysis.activeSelectRectangle();
            }
        }

        function activeSelectCircle() {
            if (spatialAnalysis && spatialAnalysis.activeSelectCircle) {
                spatialAnalysis.activeSelectCircle();
            }
        }

        function activeSelectPolygon() {
            if (spatialAnalysis && spatialAnalysis.activeSelectPolygon) {
                spatialAnalysis.activeSelectPolygon();
            }
        }

        function activeSelectMarker() {
            if (spatialAnalysis && spatialAnalysis.activeSelectMarker) {
                spatialAnalysis.activeSelectMarker();
            }
        }

        function activeSelectPolyline() {
            if (spatialAnalysis && spatialAnalysis.activeSelectPolyline) {
                spatialAnalysis.activeSelectPolyline();
            }
        }

        function deactiveSelectBox() {
            if (spatialAnalysis && spatialAnalysis.deactiveSelectBox) {
                spatialAnalysis.deactiveSelectBox();
            }
        }

        function deleteAllGraphic() {
            if (spatialAnalysis && spatialAnalysis.deleteAllGraphic) {
                spatialAnalysis.deleteAllGraphic();
            }
        }
        
        function ZoomToExtentLayer(layerId, selected) {
            if (layerTree && layerTree.zoomToExtentLayer) {
                layerTree.zoomToExtentLayer(layerId, selected);
            }
        }

        function highlightFeature(feature) {
            if (propertyTools && propertyTools.highlightFeature) {
                propertyTools.highlightFeature(feature);
            }
        }

        function flashFeature(feature) {
            if (propertyTools && propertyTools.flashFeature) {
                propertyTools.flashFeature(feature);
            }
        }

        // === مدیریت انتخاب‌های کاربر - توسط کامپوننت spatial-analysis مدیریت می‌شوند ===

        function getUserSelections(layerId) {
            if (spatialAnalysis && spatialAnalysis.getUserSelections) {
                return spatialAnalysis.getUserSelections(layerId);
            }
            return Promise.resolve([]);
        }

        function clearUserSelections(layerId) {
            if (spatialAnalysis && spatialAnalysis.clearUserSelections) {
                return spatialAnalysis.clearUserSelections(layerId);
            }
            return Promise.resolve(false);
        }

        function toggleUserSelectionsHighlight(layerId, highlight) {
            if (spatialAnalysis && spatialAnalysis.toggleUserSelectionsHighlight) {
                return spatialAnalysis.toggleUserSelectionsHighlight(layerId, highlight);
            }
            return Promise.resolve(false);
        }

        function refreshMapLayer(layerId) {
            if (layerTree && layerTree.refreshMapLayer) {
                layerTree.refreshMapLayer(layerId);
            }
        }

        }

        function showUserSelectionsInfo(layerId) {
            if (spatialAnalysis && spatialAnalysis.showUserSelectionsInfo) {
                spatialAnalysis.showUserSelectionsInfo(layerId);
            }
        }

        function addSelectionManagementToLayerMenu(layerId) {
            if (spatialAnalysis && spatialAnalysis.addSelectionManagementToLayerMenu) {
                spatialAnalysis.addSelectionManagementToLayerMenu(layerId);
            }
        }

        // === مدیریت نوار ابزار - توسط کامپوننت toolbar مدیریت می‌شود ===

        function openToolPage(toolType) {
            if (toolbar && toolbar.openToolPage) {
                toolbar.openToolPage(toolType);
            }
        }

        function closeToolPage() {
            if (toolbar && toolbar.closeToolPage) {
                toolbar.closeToolPage();
            }
        }

        function loadToolContent(toolType) {
            if (toolbar && toolbar.loadToolContent) {
                toolbar.loadToolContent(toolType);
            }
        }


        // === توابع BaseMap - توسط کامپوننت base-map-manager مدیریت می‌شوند ===
        function changeBaseMapIndex(index) {
            if (baseMapManager && baseMapManager.changeBaseMap) {
                baseMapManager.changeBaseMap(index);
            }
        }

        function changeBaseMap() {
            if (baseMapManager && baseMapManager.changeBaseMap) {
                const current = baseMapManager.getCurrentBaseMap();
                const nextIndex = (current.index + 1) % baseMapManager.baseMaps.length;
                baseMapManager.changeBaseMap(nextIndex);
            }
        }
    </script>

    <!-- ===== اسکریپت مقداردهی کامپوننت‌ها ===== -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // مقداردهی کامپوننت‌های صفحه
            initializeGeoMapComponents();

            // اتصال رویدادهای کامپوننت‌ها
            bindGeoMapEvents();

            console.log('GeoMap page initialized with modern components');
        });

        function initializeGeoMapComponents() {
            // مقداردهی کامپوننت‌ها توسط ComponentFactory انجام می‌شود
            if (window.ComponentFactory) {
                window.ComponentFactory.initializeAll();
            }
        }

        function bindGeoMapEvents() {
            // اتصال رویدادها توسط کامپوننت‌ها انجام می‌شود
            if (window.mapCore) {
                window.mapCore.bindEvents();
            }
        }

        function updateActiveLayer(layerId, layerData) {
            const activeLayerInput = document.getElementById('activeLayerLable');
            const activeLayerHidden = document.getElementById('activeLayer');

            if (activeLayerInput && layerData) {
                activeLayerInput.value = layerData.aliasName || layerData.name;
            }

            if (activeLayerHidden) {
                activeLayerHidden.value = layerId;
            }
        }

        function showSearchToolModal(searchType) {
            if (searchTools && searchTools.showModal) {
                searchTools.showModal(searchType);
            }
        }

        // تابع سراسری برای استفاده در onclick handlers
        window.showSearchTool = showSearchToolModal;

        // ========================================
        // مدیریت نقشه‌های ذخیره شده - توسط کامپوننت view-list مدیریت می‌شود
        // ========================================

        function loadMapViews() {
            if (viewList && viewList.loadMapViews) {
                viewList.loadMapViews();
            }
        }

        function displayMapViews(mapViews) {
            if (viewList && viewList.displayMapViews) {
                viewList.displayMapViews(mapViews);
            }
        }

        function createViewItemHtml(view) {
            if (viewList && viewList.createViewItemHtml) {
                return viewList.createViewItemHtml(view);
            }
            return '';
        }

        function showSelectionInfo() {
            if (spatialAnalysis && spatialAnalysis.showSelectionInfo) {
                spatialAnalysis.showSelectionInfo();
            }
        }
        // تابع سراسری برای استفاده در onclick handlers
        window.showSelectionInfo = showSelectionInfo;

        function loadMapView(viewId) {
            if (viewList && viewList.loadMapView) {
                viewList.loadMapView(viewId);
            }
        }

        function applyMapViewLayers(layers) {
            if (viewList && viewList.applyMapViewLayers) {
                viewList.applyMapViewLayers(layers);
            }
        }

        function applyMapViewGraphics(graphics) {
            if (viewList && viewList.applyMapViewGraphics) {
                viewList.applyMapViewGraphics(graphics);
            }
        }

        function editMapView(viewId) {
            if (viewList && viewList.editMapView) {
                viewList.editMapView(viewId);
            }
        }

        function deleteMapView(viewId) {
            if (viewList && viewList.deleteMapView) {
                viewList.deleteMapView(viewId);
            }
        }

        function refreshMapViews() {
            if (viewList && viewList.refreshMapViews) {
                viewList.refreshMapViews();
            }
        }

        /**
         * نمایش دیالوگ ذخیره نقشه
         */
        function showSaveMapViewDialog(editId = null) {
            const isEdit = editId !== null;
            const title = isEdit ? 'ویرایش نقشه' : 'ذخیره نقشه جدید';

            const dialogHtml = `
                <div class="modal fade" id="saveMapViewModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="saveMapViewForm">
                                    <input type="hidden" name="Id" value="${editId || 0}">
                                    <input type="hidden" name="__RequestVerificationToken" value="${document.querySelector('input[name="__RequestVerificationToken"]').value}">

                                    <div class="mb-3">
                                        <label for="mapViewName" class="form-label">نام نقشه *</label>
                                        <input type="text" class="form-control" id="mapViewName" name="Name" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="mapViewDescription" class="form-label">توضیحات</label>
                                        <textarea class="form-control" id="mapViewDescription" name="Description" rows="3"></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label for="mapViewGroup" class="form-label">گروه</label>
                                        <input type="text" class="form-control" id="mapViewGroup" name="GroupName" placeholder="نام گروه (اختیاری)">
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="saveCurrentExtent" name="Zoom">
                                            <label class="form-check-label" for="saveCurrentExtent">
                                                ذخیره محدوده فعلی نقشه
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="makePublic" name="Public">
                                            <label class="form-check-label" for="makePublic">
                                                نقشه عمومی (قابل مشاهده برای همه کاربران)
                                            </label>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                                <button type="button" class="btn btn-primary" onclick="saveMapView()">
                                    <i class="fa fa-save"></i> ${isEdit ? 'بروزرسانی' : 'ذخیره'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // حذف modal قبلی اگر وجود دارد
            const existingModal = document.getElementById('saveMapViewModal');
            if (existingModal) {
                existingModal.remove();
            }

            // اضافه کردن modal جدید
            document.body.insertAdjacentHTML('beforeend', dialogHtml);

            // نمایش modal
            const modal = new bootstrap.Modal(document.getElementById('saveMapViewModal'));
            modal.show();

            // اگر در حالت ویرایش است، اطلاعات را بارگذاری کن
            if (isEdit) {
                loadMapViewForEdit(editId);
            }
        }

        /**
         * بارگذاری اطلاعات نقشه برای ویرایش
         */
        async function loadMapViewForEdit(viewId) {
            try {
                const response = await fetch(`/GeoMap/GetMapView?id=${viewId}`);
                const result = await response.json();

                if (result.success) {
                    const mapView = result.data;
                    document.getElementById('mapViewName').value = mapView.Name || '';
                    document.getElementById('mapViewDescription').value = mapView.Description || '';
                    document.getElementById('mapViewGroup').value = mapView.GroupName || '';
                    document.getElementById('saveCurrentExtent').checked = mapView.Zoom || false;
                    document.getElementById('makePublic').checked = mapView.Public || false;
                }
            } catch (error) {
                console.error('Error loading map view for edit:', error);
                showToast('خطا در بارگذاری اطلاعات نقشه', 'danger', 3000);
            }
        }

        /**
         * ذخیره نقشه
         */
        async function saveMapView() {
            try {
                const form = document.getElementById('saveMapViewForm');
                const formData = new FormData(form);

                // اضافه کردن اطلاعات لایه‌ها
                const layersData = getCurrentLayersState();
                formData.append('Layers', JSON.stringify(layersData));

                // اضافه کردن گرافیک‌ها
                const graphicsData = getCurrentGraphicsState();
                formData.append('Graphic', JSON.stringify(graphicsData));

                // اضافه کردن bounding box اگر انتخاب شده
                if (document.getElementById('saveCurrentExtent').checked) {
                    const bounds = map.getBounds();
                    const bbox = {
                        xmin: bounds.getWest(),
                        ymin: bounds.getSouth(),
                        xmax: bounds.getEast(),
                        ymax: bounds.getNorth()
                    };
                    formData.append('BBX', JSON.stringify(bbox));
                }

                // اضافه کردن BaseMapID
                if (typeof currentBaseMapId !== 'undefined') {
                    formData.append('BaseMapID', currentBaseMapId);
                }

                const response = await fetch('/GeoMap/SaveMapView', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showToast(result.message, 'success', 3000);

                    // بستن modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('saveMapViewModal'));
                    modal.hide();

                    // بروزرسانی لیست
                    await loadMapViews();
                } else {
                    showToast(result.message || 'خطا در ذخیره نقشه', 'danger', 3000);
                }
            } catch (error) {
                console.error('Error saving map view:', error);
                showToast('خطا در ذخیره نقشه', 'danger', 3000);
            }
        }

        /**
         * دریافت وضعیت فعلی لایه‌ها
         */
        function getCurrentLayersState() {
            // TODO: پیاده‌سازی بر اساس ساختار لایه‌های موجود
            return {};
        }

        /**
         * دریافت وضعیت فعلی گرافیک‌ها
         */
        function getCurrentGraphicsState() {
            const graphics = [];

            if (editableLayers) {
                editableLayers.eachLayer(function(layer) {
                    const geoJson = layer.toGeoJSON();
                    graphics.push({
                        type: geoJson.geometry.type,
                        coordinates: geoJson.geometry.coordinates,
                        properties: geoJson.properties || {}
                    });
                });
            }

            return graphics;
        }

        /**
         * مقداردهی اولیه نقشه‌های ذخیره شده
         */
        function initializeMapViews() {
            // بارگذاری نقشه‌ها هنگام تغییر به تب نقشه‌ها
            document.getElementById('views-tab').addEventListener('shown.bs.tab', function() {
                loadMapViews();
            });

            // تنظیم event listener برای تب‌ها
            const tabTriggerList = [].slice.call(document.querySelectorAll('#sidebarTabs button'));
            tabTriggerList.forEach(function (tabTriggerEl) {
                new bootstrap.Tab(tabTriggerEl);
            });

            console.log('Map views initialized');
        }

        // توابع سراسری برای استفاده در onclick handlers
        window.loadMapViews = loadMapViews;
        window.loadMapView = loadMapView;
        window.editMapView = editMapView;
        window.deleteMapView = deleteMapView;
        window.refreshMapViews = refreshMapViews;
        window.showSaveMapViewDialog = showSaveMapViewDialog;
        window.saveMapView = saveMapView;

        /**
         * مقداردهی اولیه ابزارهای اندازه‌گیری
         */
        function initializeMeasurementTools() {
            const container = document.getElementById('measurement-tools-container');
            if (container && window.ComponentFactory) {
                try {
                    const measurementTools = window.ComponentFactory.create('measurement-tools', container, {
                        units: ['meters', 'kilometers', 'feet', 'miles'],
                        defaultUnit: 'meters',
                        coordinateSystems: ['geographic', 'mercator', 'utm38', 'utm39', 'utm40', 'utm41'],
                        defaultCoordinateSystem: 'geographic',
                        precision: 2,
                        onMeasurementStart: function(tool) {
                            console.log('Measurement started:', tool);
                            resetTools(); // Reset other tools
                        },
                        onMeasurementEnd: function(tool, result) {
                            console.log('Measurement ended:', tool, result);
                        },
                        onCoordinateClick: function(coords, system) {
                            console.log('Coordinate clicked:', coords, system);
                        }
                    });

                    // Store reference globally
                    window.measurementTools = measurementTools;

                    console.log('Measurement tools initialized successfully');
                } catch (error) {
                    console.error('Error initializing measurement tools:', error);
                }
            } else {
                console.warn('Measurement tools container or ComponentFactory not found');
            }
        }

        /**
         * مقداردهی اولیه ابزارهای تحلیل مکانی
         */
        function initializeSpatialAnalysis() {
            const container = document.getElementById('spatial-analysis-container');
            if (container && window.ComponentFactory) {
                try {
                    const spatialAnalysis = window.ComponentFactory.create('spatial-analysis', container, {
                        bufferUnits: ['meters', 'kilometers', 'feet', 'miles'],
                        defaultBufferUnit: 'meters',
                        defaultBufferDistance: 100,
                        onBufferCreated: function(layer, original) {
                            console.log('Buffer created:', layer, original);
                        },
                        onUnionCreated: function(layer, originals) {
                            console.log('Union created:', layer, originals);
                        },
                        onAnalysisStart: function(tool) {
                            console.log('Analysis started:', tool);
                            resetTools(); // Reset other tools
                        },
                        onAnalysisEnd: function(tool, result) {
                            console.log('Analysis ended:', tool, result);
                        }
                    });

                    // Store reference globally
                    window.spatialAnalysisTools = spatialAnalysis;

                    console.log('Spatial analysis tools initialized successfully');
                } catch (error) {
                    console.error('Error initializing spatial analysis tools:', error);
                }
            } else {
                console.warn('Spatial analysis container or ComponentFactory not found');
            }
        }

        /**
         * مقداردهی اولیه ابزار رفتن به مختصات
         */
        function initializeGoToXY() {
            const container = document.getElementById('goto-xy-container');
            if (container && window.ComponentFactory) {
                try {
                    const gotoXY = window.ComponentFactory.create('goto-xy', container, {
                        coordinateSystems: ['geographic', 'mercator', 'utm38', 'utm39', 'utm40', 'utm41'],
                        defaultCoordinateSystem: 'geographic',
                        zoomLevel: 15,
                        showMarker: true,
                        animateToLocation: true,
                        markerDuration: 5000,
                        onLocationFound: function(latlng, coordinates) {
                            console.log('Location found:', latlng, coordinates);
                        },
                        onLocationError: function(error) {
                            console.error('Location error:', error);
                        }
                    });

                    // Store reference globally
                    window.gotoXYComponent = gotoXY;

                    console.log('GoTo XY component initialized successfully');
                } catch (error) {
                    console.error('Error initializing GoTo XY component:', error);
                }
            } else {
                console.warn('GoTo XY container or ComponentFactory not found');
            }
        }

        /**
         * مقداردهی اولیه ابزارهای ترسیم
         */
        function initializeDrawingTools() {
            const container = document.getElementById('drawing-tools-container');
            if (container && window.ComponentFactory) {
                try {
                    const drawingTools = window.ComponentFactory.create('drawing-tools', container, {
                        drawingModes: ['point', 'line', 'polygon', 'circle', 'rectangle', 'freehand'],
                        defaultDrawingMode: 'point',
                        enableSnapping: true,
                        snapDistance: 10,
                        showTooltips: true,
                        allowEdit: true,
                        onDrawStart: function(type) {
                            console.log('Drawing started:', type);
                            resetTools(); // Reset other tools
                        },
                        onDrawEnd: function(layer, type) {
                            console.log('Drawing ended:', layer, type);
                        },
                        onDrawCancel: function() {
                            console.log('Drawing cancelled');
                        },
                        onFeatureEdit: function(layers) {
                            console.log('Features edited:', layers);
                        },
                        onFeatureDelete: function(layers) {
                            console.log('Features deleted:', layers);
                        }
                    });

                    // Store reference globally
                    window.drawingTools = drawingTools;

                    console.log('Drawing tools initialized successfully');
                } catch (error) {
                    console.error('Error initializing drawing tools:', error);
                }
            } else {
                console.warn('Drawing tools container or ComponentFactory not found');
            }
        }

        /**
         * مقداردهی اولیه ابزارهای خصوصیات
         */
        function initializePropertyTools() {
            const container = document.getElementById('property-tools-container');
            if (container && window.ComponentFactory) {
                try {
                    const propertyTools = window.ComponentFactory.create('property-tools', container, {
                        showGeometry: true,
                        showAttributes: true,
                        showCoordinates: true,
                        showArea: true,
                        showLength: true,
                        coordinateFormat: 'decimal',
                        areaUnit: 'square_meters',
                        lengthUnit: 'meters',
                        maxAttributeLength: 100,
                        onPropertyShow: function() {
                            console.log('Property panel shown');
                        },
                        onPropertyHide: function() {
                            console.log('Property panel hidden');
                        },
                        onPropertyUpdate: function(feature, changes) {
                            console.log('Property updated:', feature, changes);
                        },
                        onFeatureSelect: function(feature) {
                            console.log('Feature selected:', feature);
                            // Set identified feature for other tools
                            if (window.spatialAnalysisTools) {
                                window.spatialAnalysisTools.setIdentifiedFeature(feature);
                            }
                        },
                        onFeatureDeselect: function(feature) {
                            console.log('Feature deselected:', feature);
                        }
                    });

                    // Store reference globally
                    window.propertyTools = propertyTools;

                    console.log('Property tools initialized successfully');
                } catch (error) {
                    console.error('Error initializing property tools:', error);
                }
            } else {
                console.warn('Property tools container or ComponentFactory not found');
            }
        }

        // توابع سراسری برای استفاده در onclick handlers
        window.initializeMeasurementTools = initializeMeasurementTools;
        window.initializeSpatialAnalysis = initializeSpatialAnalysis;
        window.initializeGoToXY = initializeGoToXY;
        window.initializeDrawingTools = initializeDrawingTools;
        window.initializePropertyTools = initializePropertyTools;
    </script>
}