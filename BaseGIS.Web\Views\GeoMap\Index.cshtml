@using System.Text.Json
@model IEnumerable<BaseGIS.Core.Entities.GroupInfo>
@{
    ViewData["Title"] = "نقشه";
    Layout = "~/Views/Shared/_LayoutMap.cshtml";

    string treeData = ViewBag.TreeData;

}

@section Styles {
    <link rel="stylesheet" href="/lib/jqueryui/themes/base/jquery-ui.min.css" />
    <link href="~/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css" rel="stylesheet" />

    <link href="~/lib/ion-rangeslider/css/ion.rangeslider.min.css" rel="stylesheet" />
    <style>

        .map-container {
            display: flex;
            height: calc(100vh - 62px);
            direction: rtl;
        }

        #sidebar {
            width: 250px;
            height: 100%;
            transition: width 0.35s ease;
            overflow: hidden;
            background: #fff;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
        }

            #sidebar.hidden {
                width: 0;
                display: block;
            }

        .sidebar-wrapper {
            width: 100%;
            height: 100%;
        }

        .active-layer-container {
            padding: 10px;
            border-bottom: 1px solid #ddd;
            background: #f8f9fa;
        }

        #activeLayerInput {
            background-color: #fff;
            cursor: default;
        }

        #tree {
            height: 100%;
            max-height: calc(100vh - 150px); /* تنظیم ارتفاع با توجه به Textbox */
            overflow-y: auto;
            padding: 10px;
            background-color: var(--background-color);
        }

        #map {
            width: calc(100% - 250px);
            height: 100%;
            position: relative;
            order: 1;
        }

            #map.full-width {
                width: 100%;
            }

        .btn-Sidebar {
            position: absolute;
            z-index: 1000;
            top: 10px;
            border-radius: 5px 0 0 5px;
            height: 34px;
            width: 34px;
            background: white;
            padding: 4px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            transition: right 0.35s ease;
        }

        #sidebar.hidden + #map .btn-Sidebar {
            right: 0;
        }

        .btn-Sidebar a {
            color: var(--primary-color);
            text-decoration: none;
        }


        span.fancytree-node.custom > span.fancytree-title {
            color: maroon;
            font-family: "SamanFont";
        }

        span.fancytree-node.plan > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/Gallery16.png");
        }

        span.fancytree-node.group > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/group.png");
        }

        span.fancytree-node.polyline > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/polyline.png");
        }

        span.fancytree-node.point > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/point.png");
        }

        span.fancytree-node.polygon > span.fancytree-icon {
            background-position: 0 0;
            background-image: url("../mapService/img/polygon.png");
        }
         
        .fancytree-container,
        span.fancytree-focused span.fancytree-title {
            outline: 0 !important;
        }
         
        @@media (max-width: 768px) {
            #sidebar {
                width: 200px;
            }

            #map {
                width: calc(100% - 200px);
            }

            #sidebar.hidden + #map {
                width: 100%;
            }

            .btn-Sidebar {
                right: 200px;
            }
        }
    </style>
}

<div class="map-container">
    <!-- سایدبار -->
    <div id="sidebar">
        <div class="sidebar-wrapper">
            <div class="active-layer-container">
                <label for="activeLayerInput" class="form-label">لایه فعال</label>
                <input type="text" id="activeLayerLable" class="form-control" readonly value="هیچ لایه‌ای انتخاب نشده">
                <input type="text" id="activeLayer" class="d-none">

            </div>
            <div id="tree">
                <ul id="treeData" style="display:none;">
                    @Html.Raw(treeData)
                </ul>
            </div>
        </div>
    </div>
    <!-- نقشه -->
    <div id="map" class="map">
        <span class="btn-Sidebar">
            <a id="btnLeftSidebar" href="#" onclick="animateSidebar();" title="باز/بستن سایدبار"><i class="fa fa-angle-double-right fa-2x"></i></a>
        </span>
        <div style="text-align:center">
            <span class="" id="scaleText" style="color: white; text-shadow: 1px 1px 2px black, 0 0 25px blue, 0 0 5px darkblue;"></span>
            <br /> <span class="" id="positionText" style="color: white;  text-shadow: 1px 1px 2px black, 0 0 25px blue, 0 0 5px darkblue;"></span>
            <br /> <span class="" id="positionUTMText" style="color: white;  text-shadow: 1px 1px 2px black, 0 0 25px blue, 0 0 5px darkblue;"></span>
        </div>
    </div>
</div>


<div class="modal" id="dialog-Property" tabindex="-1" role="dialog" aria-labelledby="remoteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
        </div>
    </div>
</div>

<div id="panel-Identify" style="position:absolute; display:none; top:50px; bottom:10px; right: 0px; width:532px; height:calc(100vh - 50px); background-color:#fff;border:1px solid red; z-index:1000;" class=" ">
</div>

@section Scripts {
    <script src="~/lib/jqueryui/jquery-ui.min.js"></script>
    <script src="~/lib/jquery.fancytree/jquery.fancytree-all.min.js"></script>
    <script src="~/lib/esri-leaflet/dist/esri-leaflet.js"></script>
    <script src="~/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js"></script>
    <script src="~/lib/ion-rangeslider/js/ion.rangeslider.min.js"></script>

    <script>
        var baseUrl = window.location.origin;
         var URLBASE = "@Url.Content("~")";
        var map;
      
        var mapIndex = "1";
        var lyrs = ['1'];
        var lyrCo = [];

        var latLngCenter = [32.702222, 51.979378];

        var polygcreate;
        var isIdentify  = false;

        $(document).ready(function() {
            // مقداردهی اولیه نقشه
            map = L.map('map', {
                zoom: 5,
                center: latLngCenter,
                zoomControl: false,
                attributionControl: false
            });

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: ''
            }).addTo(map);

            lyrCo[0] = L.esri.dynamicMapLayer({
                url: baseUrl + '/rest/services/map/mapserver',
                opacity: 1,
                layers: [],
                f: 'image',
                format: 'png32',
                transparent: true,
                crs: L.CRS.EPSG3857
            }).addTo(map);

            lyrCo[0].idMap = mapIndex.toString();
            lyrCo[0].on("loading", function (e) {

            });
            lyrCo[0].on("load", function (e) {

            });

            initializeFancytree();
            
            L.control.custom({
                position: 'bottomright',
                content: '<a href="javascript:changeBaseMap()" id="basemapbtn"><img src="../Content/mapService/img/basemap/0.png" class="img-thumbnail" id="basemapimage" style="width:70px;"></a>',
                style: { margin: '5px', padding: '0px' }
            }).addTo(map);

        L.control.custom({
            position: 'topleft',
            content:
                    '<div  id="standardToolbar" class="" style="width:44px;min-height:450px; z-index:902;"> ' +
                    '<button type="button" class="btn tool2 btnView" title="جابجایی" style="font-size:22px" onclick="toolname=\'\';enableTool();"> <i class="fa fa-hand-paper-o  "  ></i> </button>' +
                    '<button id="globe" type="button" class="btn tool2 btnView" title="بزرگنمایی به طرح" style="font-size:22px" onclick="zoomToPlan()"> <i class="fa fa-globe  "   ></i> </button>' +
                    '<button id="info" type="button" class="btn tool2 btnView" title="شناسایی" style=" font-size:22px" onclick="toolname=\'infoSaman\';enableTool();"> <i class="fa fa-info "  ></i> </button>' +
                    '<button id="infoOther" type="button" class="btn tool2 btnView" title="شناسایی عمومی" style="font-size:22px" onclick="toolname=\'infoOther\';enableTool();"> <i class="fa fa-question  "  ></i> </button>' +
                    '<button type="button" class="btn tool2 btnView "   title="رفتن به موقعیت خاص" style="font-size:22px" onclick="togglegotoxy();">  <i class="fa fa-street-view  " ></i> </button>' +
                    '<button type="button" class="btn tool2 btnView" title="اندازه گیری" style="font-size:22px" onclick="enableTool();ShowDialogMeasurement();"> <i class="fa fa-arrows-h" ></i> </button>' +
                    '<div class="btn tool2 " role="group" style="padding:0px;border:0px;" > <button  onclick="toggleBtn(\'dpDraw\');" title="ترسیم گرافیک" type="button" class="btn btnView" style=" font-size:22px;" data-toggle="" aria-haspopup="true" aria-expanded="true" > <i class="fa fa-pencil" ></i> </button>' +
                        '<div class="dropdown-menu " id="dpDraw" style="padding:0px;background:rgba(0,0,0,0);border:rgba(0,0,0,0);margin-top:0px;margin-left:12px;position:relative;float:right;" dir="ltr">' +
                            '<button  type="button" class="btn  btnView " title="ترسیم نقطه" onclick="toolname=\'\';enableTool();activeSelectMarker();"><i class=" fa fa-circle "  ></i></button>' +
                            '<button   type="button" class="btn  btnView " title="ترسیم خط"  onclick="toolname=\'\';enableTool();activeSelectPolyline();"><i class="fa fa-vine"  ></i></button>' +
                            '<button type="button" class="btn  btnView " title="ترسیم چندضلعی" onclick="toolname=\'\';enableTool();activeSelectPolygon();"><i class="fa fa-object-group "  ></i></button>' +
                            '<button type="button" class="btn  btnView " title="ترسیم بر اساس عارضه" onclick="toolname=\'sketchGraphic\';enableTool(); "><i class="fa fa-mouse-pointer "  ></i></button>' +
                            '<button  type="button" class="btn  btnView " title="ترسیم حریم" onclick="bufferwindows();" ><i class="fa-dot-circle-o fa fa-x"  ></i></button>' +
                            '<button type="button" class="btn tool2 btnView" title="محاسبه اضلاع عارضه"   onclick="isGraphicSketchMeasurment=true;toolname = \'sketchGraphic\';enableTool(); "><i class="fa fa-expand  fa-lg"  ></i></button>' +
                            '<button type="button" class="btn  btnView " title="حذف کلیه گرافیک ها"   onclick="deleteAllGraphic();"><i class="fa fa-trash fa-lg"  ></i></button>' +
                         '</div>' +
                    '</div>' +
                    '<div class="btn tool2 " role="group" style="padding:0px;border:0px;" > <button  onclick="toggleBtn(\'dpSelect\');" title="انتخاب" type="button" class="btn  btnView" style=" font-size:22px" data-toggle="" aria-haspopup="true" aria-expanded="false" > <i class="fa fa-search " ></i> </button>' +
                         '<div class="dropdown-menu " id="dpSelect" style="padding:0px;background:rgba(0,0,0,0);border:rgba(0,0,0,0);margin-top:0px;margin-left:12px;position:relative;float:right;" dir="ltr">' +
                            '<button type="button" class="btn  btnView" title="انتخاب از لایه فعال" onclick="toolname=\'\';enableTool();activeSelectBox();  "><i class="fa fa-mouse-pointer" ></i></button>' +
                            '<button type="button" class="btn  btnView" title="حذف انتخاب لایه فعال" onclick="UnSelectBoxactiveLayer() "> <i class="fa fa-eraser  " ></i> </button>' +
                            '<button  type="button" class="btn  btnView" title="معکوس انتخاب لایه فعال" onclick="switchSelection();" style=" "> <i class="fa fa-random "  ></i> </button>' +
                            '<button type="button" class="btn  btnView" title="حذف همه انتخاب ها" onclick="UnSelectBox(); "> <i class="fa fa-trash-o" ></i> </button>' +
                         '</div>' +
                    '</div>' +
                    '</div>',
                    classes: '',
                    style:
                    {
                        //'box-shadow': 'rgb(41, 187, 255) 0px -3px 7px 0px inset',
                        'z-index': '100',
                        margin: '2px',
                        cursor: 'pointer'
                    },
                    events:
                    {
                        click: function (data) {
                          //  toolname = "";
                            id = data.target.id;
                            if (data.path != undefined)
                                id = data.path[1].id;
                            if (id == "locate")
                                map.locate({ setView: true });
                        }
                    }
                }).addTo(map);

            $("#basemapimage").hover(function () {
                $("#panel-satImages").show();
            });
            $("#panel-satImages").mouseleave(function () {
                $("#panel-satImages").hide();
            });

        var scale = L.control.scale();

        map.on("moveend", function (e) {
        });

        /* Clear feature highlight when map is clicked */
        map.on("mousemove ", function (e) {
            var arrlatlng = Project(e.latlng);
            var label = "   " + arrlatlng[0].toFixed(4) + ", " + arrlatlng[1].toFixed(4);
            $("#positionText").html(label);

            var labelUTM = getUTMLabel(arrlatlng);
            $("#positionUTMText").html(labelUTM);
        });
       
        map.on('click', function (e) {
            if (isIdentify)
                identifyLayers(e);           
            setGraphicSketchClicked(e);
        });

        map.on('dblclick', function (e) {
            if (polygcreate && polygcreate._enabled)
                polygcreate.completeShape();
        });
        //-------------------------------- Set Scale
        map.on("zoomend", function (e) {
            setScale();
        });

        
        });

        function initializeFancytree() {
            $("#tree").fancytree({
                checkbox: true,
                selectMode: 2,
                rtl: true,
                init: function (event, data) {
                    // Set key from first part of title (just for this demo output)
                    data.tree.visit(function (n) {
                        //n.key = n.title.split(" ")[0];
                        if (n.key[0] != "L") {
                            n.checkbox = false;
                        }
                    });
                },
                select: function (event, data) {
                    var node = data.node;

                    // اگر گره‌ای انتخاب شد و یک لایه (L) است، والد آن را انتخاب کن
                    if (node.isSelected() && node.key && node.key[0] == "L") {
                        if (node.getParent()) {
                            node.getParent().setSelected(true, { noEvents: true }); // بدون فعال‌سازی رویداد
                        }
                    }

                    // اگر گره‌ای از حالت انتخاب خارج شد و یک گره والد (گروه) است، فرزندان را uncheck کن
                    if (!node.isSelected() && node.key && node.key[0] == "G") {
                        node.visit(function (child) {
                            child.setSelected(false, { noEvents: true }); // بدون فعال‌سازی رویداد
                        });
                    }

                    // فراخوانی refreshTree فقط یک بار در انتها
                    refreshTree();
                },
                beforeExpand: function (event, data) {
                    var kkey = data.node.key;
                    if (kkey.length > 7 && data.node.key[0] == "L")
                    {
                        setSymbologyByID(kkey.substring(1, 12), -1);
                    }
                },

                // The following options are only required, if we have more than one tree on one page:
                cookieId: "fancytree-Cb2",
                idPrefix: "fancytree-Cb2-",
                click: function (event, data) {
                    if (data && data.node && data.node.isSelected() && data.node.key[0] == "L" && data.node.key.length == 12) {
                        $("#activeLayerLable").val(data.node.li.innerText.split('\n')[0]);
                        $("#activeLayer").val(data.node.key);
                    }
                },
                dblclick: function (event, data) {
                    if (data && data.node && data.node.key[0] == "L" && data.node.key.length == 12) {
                        SettingLayer(data.node.key.substring(1, 80));
                    }
                },
            });
        }

        // سایر توابع مرتبط با تنظیمات لایه‌ها
        function SettingLayer(layerid) {
            settinglayerid = layerid;
            var url = baseUrl + '/GeoMap/Property?Layerid=' + layerid;
            
            $.ajax({
                url: url,
                cache: false,
                success: function(data) {
                    $("#dialog-Property .modal-content").html(data);
                    var modalElement = document.getElementById('dialog-Property');
                    var modal = new bootstrap.Modal(modalElement);
                    modal.show();
                    setPropertyProps(layerid);
                    modalElement.addEventListener('hidden.bs.modal', function() {
                        const backdrop = document.querySelector('.modal-backdrop');
                        if (backdrop) {
                            backdrop.remove();
                        }
                        document.body.classList.remove('modal-open');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';
                    });
                },
                error: function(data) {
                    console.error("Error loading modal content:", data);
                }
            });
            return false;
        }

        function setPropertyProps(layerid) {
            $("#propertyLayerId").val(layerid);
            getFieldinLable("L" + layerid);

            var chk = $("#C" + layerid).is(':checked');
            $("#layerchecked").prop('checked', chk);
            var op = parseInt($("#O" + layerid).val());
            if (op > 0) {
                op = 100 - op;
            } else {
                op = 0;
            }
            $("#layeropacity").ionRangeSlider({
                min: 0,
                max: 100,
                from: op,
                step: 2,
                grid: true,
                prefix: "%"
            });
            var lbl = $("#La" + layerid).val().replace(/@@/g, ':').replace(/\\n/g, '\n');
            var arrLabels = lbl.split(':');
            var joinLabel = '';
            if (arrLabels.length > 1) {
                joinLabel = arrLabels[arrLabels.length - 1];
            }
            var baseLabel = '';
            if (joinLabel.split('___').length > 1) {
                baseLabel = lbl.replace(joinLabel, '');
                baseLabel = baseLabel.substring(0, baseLabel.length - 1);
            } else {
                baseLabel = lbl;
            }
            $("#layerlabledd").val(baseLabel);
            $("#layerlablejoindd").val(joinLabel);

            var sym = $("#Sym" + layerid).val();
            $("input[name='radio_Sym'][value='" + sym + "']").prop("checked");
            getLegend(sym);
        }

        function setlableopacity() {
            var chk = $("#layerchecked").is(':checked');
            $("#C" + settinglayerid).prop('checked', chk);

            var op = $("#layeropacity").val();
            $("#O" + settinglayerid).val(100 - op);

            var ll = $("#layerlabledd").val().replace(/\n/g, '\\n').replace(/\:/g, '@@');
            var llj = $("#layerlablejoindd").val().replace(/\n/g, '\\n').replace(/\:/g, '@@');
            if ($("#layerlablejoindd").val() && $("#layerlablejoindd").val().trim() != '') {
                if ($("#layerlabledd").val() && $("#layerlabledd").val().trim() != '') {
                    ll = ll + '@@' + llj;
                }
            }
            $("#La" + settinglayerid).val(ll);
            refreshTree();
        }

        function setSymbology() {
            var sym = $("input:radio[name='radio_Sym']:checked").val();
            $("#Sym" + settinglayerid).val(sym);
            setSymbologyByID(settinglayerid, sym);
            refreshTree();
        }

        function setSymbologyByID(layerid, sym) {
            
            sym = $("#Sym" + layerid).val();
            if (!sym) sym = -1;
            var childNode = $("#tree").fancytree("getTree").getNodeByKey("L" + layerid);
            if (childNode) {
                childNode.removeChildren();
            }
            
            $.ajax({
                type: 'POST',
                url: "/Rest/Legend",
                data: { "layerId": "L" + layerid, "symbologyId": sym },
                dataType: 'json',
                success: function(data) {
                    for (var i = 0; i < data.length; i++) {
                        var nodett = childNode.addChildren({
                            active: data[i].active,
                            checkbox: data[i].checkbox,
                            data: data[i].data,
                            expanded: data[i].expanded,
                            focus: data[i].focus,
                            folder: data[i].folder,
                            icon: data[i].icon,
                            key: data[i].key,
                            lazy: data[i].lazy,
                            selected: data[i].selected,
                            title: data[i].title,
                            tooltip: data[i].tooltip
                        });
                        if (data[i].iconHeight && data[i].iconWidth) {
                            $(nodett.span).children('img').height(data[i].iconHeight).width(data[i].iconWidth);
                        }
                    }
                    if (data.length >= 10) {
                        var nodeOther = childNode.addChildren({
                            checkbox: false,
                            icon: false,
                            folder: false,
                            key: "L" + layerid + "_otherLegend",
                            lazy: false,
                            title: "<a href='#' onclick='getLegendOther(1,\"" + layerid + "\");'>...</a>",
                            tooltip: "ادامه..."
                        });
                    }
                },
                error: function(data) {
                    console.error("Error in setSymbologyByID:", data);
                }
            });
        }

        function refreshTree() {
            var selNodes = $("#tree").fancytree('getTree').getSelectedNodes();
            for (var i = 0; i < selNodes.length; i++) {
                if (selNodes[i].getParent() && selNodes[i].getParent().getParent() != null) {
                    selNodes[i].getParent().setSelected(true);
                }
            }
            var r = [];
            for (var j = 0; j < lyrs.length; j++) {
                if (lyrs[j] != "") {
                    for (var i = 0; i < selNodes.length; i++) {
                        var id = selNodes[i].key;
                        if (id[0] == 'L') {
                            if (selNodes[i].getParent() && selNodes[i].getParent().isSelected()) {
                                if (lyrs[j] == parseInt(id.substring(2, 8)).toString()) {
                                    var ids = id.substr(1);
                                    if ($("#C" + ids).is(':checked')) {
                                        ids = ids + ";" + $("#La" + ids).val() + ";" + $("#O" + ids).val() + ";" + $("#Sym" + ids).val();
                                    } else {
                                        ids = ids + ";false;" + $("#O" + ids).val() + ";" + $("#Sym" + ids).val();
                                    }
                                    r.push(ids);
                                }
                            }
                        }
                    }
                    
                    //basemap.bringToBack();
                    lyrCo[j] = lyrCo[j].setLayers(r);
                }
            }
        }

        function animateSidebar() {
            var sidebar = document.getElementById('sidebar');
            var mapDiv = document.getElementById('map');
            var btnLeftSidebar = document.getElementById('btnLeftSidebar');

            sidebar.classList.toggle('hidden');
            mapDiv.classList.toggle('full-width');

            if (sidebar.classList.contains('hidden')) {
                btnLeftSidebar.innerHTML = '<i class="fa fa-angle-double-left fa-2x"></i>';
                btnLeftSidebar.style.right = '0px';
            } else {
                btnLeftSidebar.innerHTML = '<i class="fa fa-angle-double-right fa-2x"></i>';
                btnLeftSidebar.style.right = '250px';
            }
            setTimeout(function() {
                map.invalidateSize();
            }, 350);
        }

        function getUTMLabel(arrlatlng) {
            var index = 0;
            var utmZone = '38';
            if (arrlatlng[0] > 42 && arrlatlng[0] < 48) {
                utmZone = '38';
            }
            else if (arrlatlng[0] > 48 && arrlatlng[0] < 54) {
                utmZone = '39';
            }
            else if (arrlatlng[0] > 54 && arrlatlng[0] < 60) {
                utmZone = '40';
            }
            else if (arrlatlng[0] > 60 && arrlatlng[0] < 66) {
                utmZone = '41';
            }

            switch (utmZone) {
                case '38':
                    index = 2;
                    break;
                case '39':
                    index = 3;
                    break;
                case '40':
                    index = 4;
                    break;
                case '41':
                    index = 5;
                    break;
                default:
            }

            var arrUTM = arrlatlng;
            if (index > 0) {
                arrUTM = proj4(projectSystems[index]).forward(arrlatlng);
            }
            var labelUTM = "UTM" + utmZone + ": " + arrUTM[0].toFixed(0) + ", " + arrUTM[1].toFixed(0);
            return labelUTM;
        }

        function setScale()
        {
            var prj = 'PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0],AUTHORITY["ESRI","102100"]]';
            var bnd = map.getBounds();
            var xy1 = proj4(prj).forward([bnd._northEast.lng, bnd._northEast.lat]);
            var xy2 = proj4(prj).forward([bnd._southWest.lng, bnd._southWest.lat]);
            var label = (Math.abs(xy1[0] - xy2[0]) / ((map.getSize().x * 0.0254) / 96)).toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ",");

            //var metres = scale._getRoundNum(map.containerPointToLatLng([0, map.getSize().y / 2]).distanceTo(map.containerPointToLatLng([scale.options.maxWidth, map.getSize().y / 2])))
            //var label = metres < 1000 ? metres + ' m' : (metres / 1000) + ' km';
            $("#scaleText").html("1:" + label);
        }


        //------------------ change Coordinate
        var projectIndex = 0;
        var projectSystems = [
                '',
                'PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0],AUTHORITY["ESRI","102100"]]',
                'PROJCS["WGS 84 / UTM zone 38N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",45],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32638"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["WGS 84 / UTM zone 39N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",51],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32639"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["WGS 84 / UTM zone 40N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",57],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32640"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["WGS 84 / UTM zone 41N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",63],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32641"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["\Iran Lambert\",GEOGCS["\GCS_WGS_1984\",DATUM["\D_WGS_1984\",SPHEROID["\WGS_1984\",6378137.0,298.257223563]],PRIMEM["\Greenwich\",0.0],UNIT["\Degree\",0.0174532925199433]],PROJECTION["\Lambert_Conformal_Conic\"],PARAMETER["\False_Easting\",5000000.0],PARAMETER["\False_Northing\",30000000.0],PARAMETER["\Central_Meridian\",54.0],PARAMETER["\Standard_Parallel_1\",30.0],PARAMETER["\Standard_Parallel_2\",36.0],PARAMETER["\Central_Parallel\",24.0],UNIT["\Meter\",1.0]]'
        ];
        function setproject(index) {
            projectIndex = index;
            projectSystems = [
                  '',
                'PROJCS["WGS_1984_Web_Mercator_Auxiliary_Sphere",GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.257223563]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]],PROJECTION["Mercator_Auxiliary_Sphere"],PARAMETER["False_Easting",0.0],PARAMETER["False_Northing",0.0],PARAMETER["Central_Meridian",0.0],PARAMETER["Standard_Parallel_1",0.0],PARAMETER["Auxiliary_Sphere_Type",0.0],UNIT["Meter",1.0],AUTHORITY["ESRI","102100"]]',
                'PROJCS["WGS 84 / UTM zone 38N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",45],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32638"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["WGS 84 / UTM zone 39N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",51],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32639"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["WGS 84 / UTM zone 40N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",57],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32640"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["WGS 84 / UTM zone 41N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.01745329251994328,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]],UNIT["metre",1,AUTHORITY["EPSG","9001"]],PROJECTION["Transverse_Mercator"],PARAMETER["latitude_of_origin",0],PARAMETER["central_meridian",63],PARAMETER["scale_factor",0.9996],PARAMETER["false_easting",500000],PARAMETER["false_northing",0],AUTHORITY["EPSG","32641"],AXIS["Easting",EAST],AXIS["Northing",NORTH]]',
                'PROJCS["\Iran Lambert\",GEOGCS["\GCS_WGS_1984\",DATUM["\D_WGS_1984\",SPHEROID["\WGS_1984\",6378137.0,298.257223563]],PRIMEM["\Greenwich\",0.0],UNIT["\Degree\",0.0174532925199433]],PROJECTION["\Lambert_Conformal_Conic\"],PARAMETER["\False_Easting\",5000000.0],PARAMETER["\False_Northing\",30000000.0],PARAMETER["\Central_Meridian\",54.0],PARAMETER["\Standard_Parallel_1\",30.0],PARAMETER["\Standard_Parallel_2\",36.0],PARAMETER["\Central_Parallel\",24.0],UNIT["\Meter\",1.0]]'
            ];
        }
        function Project(latlng) {
            if (projectIndex == 0)
                return [latlng.lng, latlng.lat];
            return proj4(projectSystems[projectIndex]).forward([latlng.lng, latlng.lat]);
        }
        function ProjectInverse(x, y) {
            if (projectIndex == 0)
                return [x, y];
            return proj4(projectSystems[projectIndex]).inverse([x, y]);
        }

        //-----------------------------------------
    </script>
}