@{
    ViewData["Title"] = "دسترسی غیرمجاز";
}

<div class="container mt-5 pt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-sm border-danger">
                <div class="card-header bg-danger text-white text-center">
                    <h4><i class="fas fa-exclamation-triangle me-2"></i> دسترسی غیرمجاز</h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-lock fa-5x text-danger mb-3"></i>
                        <h4 class="mb-3">شما به این صفحه دسترسی ندارید</h4>
                        <p class="text-muted">برای دسترسی به این بخش، به مجوزهای بیشتری نیاز دارید.</p>
                    </div>
                    <hr class="my-4">
                    <div class="d-flex justify-content-center gap-3">
                        <a asp-controller="Home" asp-action="Index" class="btn btn-outline-primary">
                            <i class="fas fa-home me-2"></i> بازگشت به صفحه اصلی
                        </a>
                        <a href="javascript:history.back()" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i> بازگشت به صفحه قبل
                        </a>
                    </div>
                </div>
                <div class="card-footer text-center text-muted">
                    <small>در صورت بروز اشتباه با مدیر سیستم تماس بگیرید</small>
                </div>
            </div>
        </div>
    </div>
</div> 