/**
 * Map Core Component
 * کامپوننت هسته نقشه - مدیریت اصلی نقشه و لایه‌ها
 */

class MapCore extends BaseComponent {
    getDefaultOptions() {
        return {
            ...super.getDefaultOptions(),
            center: [32.702222, 51.979378],
            zoom: 5,
            baseUrl: '',
            mapIndex: "1",
            zoomControl: false,
            attributionControl: false,
            onMapReady: null,
            onLayersReady: null,
            onMapClick: null,
            onMapDoubleClick: null,
            onMapMove: null,
            onMapZoom: null
        };
    }

    beforeInit() {
        this.map = null;
        this.lyrCo = [];
        this.editableLayers = null;
        this.markerLayers = null;
        this.drawControl = null;
        this.baseMapManager = null;
        this.isInitialized = false;
        this.selectedLayers = new Map();
        this.searchResults = new Map();
        this.currentExtent = null;
        
        // Global variables for backward compatibility
        window.map = null;
        window.lyrCo = [];
        window.editableLayers = null;
        window.markerLayers = null;
        window.drawControl = null;
        window.baseMapManager = null;
    }

    render() {
        // Map container should already exist in HTML
        if (!document.getElementById('map')) {
            console.error('Map container not found');
            return;
        }
    }

    bindEvents() {
        super.bindEvents();
        // Events will be bound after map initialization
    }

    afterInit() {
        // Initialize map after component is ready
        this.initializeMap();
    }

    // ========================================
    // Map Initialization
    // ========================================

    initializeMap() {
        try {
            // Create Leaflet map
            this.map = L.map('map', {
                zoom: this.options.zoom,
                center: this.options.center,
                zoomControl: this.options.zoomControl,
                attributionControl: this.options.attributionControl
            });

            // Set global reference
            window.map = this.map;

            // Add default base layer
            this.addDefaultBaseLayer();

            // Initialize layers
            this.initializeMapLayers();

            // Setup controls
            this.setupMapControls();

            // Bind map events
            this.bindMapEvents();

            this.isInitialized = true;

            if (this.options.onMapReady) {
                this.options.onMapReady(this.map);
            }

            this.trigger('mapReady', { map: this.map });

            console.log('Map core initialized successfully');
        } catch (error) {
            console.error('Error initializing map:', error);
        }
    }

    addDefaultBaseLayer() {
        // Add OpenStreetMap as default base layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: ''
        }).addTo(this.map);
    }

    initializeMapLayers() {
        try {
            // Setup ESRI dynamic map layer
            this.lyrCo[0] = L.esri.dynamicMapLayer({
                url: this.options.baseUrl + '/rest/services/map/mapserver',
                opacity: 1,
                layers: [],
                f: 'image',
                format: 'png32',
                transparent: true,
                crs: L.CRS.EPSG3857
            }).addTo(this.map);

            this.lyrCo[0].idMap = this.options.mapIndex.toString();

            // Set global reference
            window.lyrCo = this.lyrCo;

            // Layer events
            this.lyrCo[0].on("loading", (e) => {
                this.trigger('layerLoading', { layer: this.lyrCo[0] });
            });

            this.lyrCo[0].on("load", (e) => {
                this.trigger('layerLoaded', { layer: this.lyrCo[0] });
            });

            if (this.options.onLayersReady) {
                this.options.onLayersReady(this.lyrCo);
            }

            this.trigger('layersReady', { layers: this.lyrCo });

        } catch (error) {
            console.error('Error initializing map layers:', error);
        }
    }

    setupMapControls() {
        try {
            // Initialize editable layers
            this.editableLayers = new L.FeatureGroup();
            this.map.addLayer(this.editableLayers);
            window.editableLayers = this.editableLayers;

            // Initialize marker layers
            this.markerLayers = new L.FeatureGroup();
            this.map.addLayer(this.markerLayers);
            window.markerLayers = this.markerLayers;

            // Setup draw control
            this.setupDrawControl();

            // Initialize BaseMap Manager
            this.initializeBaseMapManager();

            this.trigger('controlsReady');

        } catch (error) {
            console.error('Error setting up map controls:', error);
        }
    }

    setupDrawControl() {
        this.drawControl = new L.Control.Draw({
            position: 'topright',
            draw: {
                polygon: {
                    allowIntersection: false,
                    drawError: {
                        color: '#e1e100',
                        message: '<strong>خطا!</strong> نمی‌توانید خطوط را قطع کنید!'
                    },
                    shapeOptions: {
                        color: '#820091',
                        weight: 3,
                        fillColor: '#820091',
                        fillOpacity: 0.2
                    }
                },
                polyline: {
                    shapeOptions: {
                        color: '#820091',
                        weight: 3
                    }
                },
                rectangle: {
                    shapeOptions: {
                        color: '#820091',
                        weight: 3,
                        fillColor: '#820091',
                        fillOpacity: 0.2
                    }
                },
                circle: {
                    shapeOptions: {
                        color: '#820091',
                        weight: 3,
                        fillColor: '#820091',
                        fillOpacity: 0.2
                    }
                },
                marker: true
            },
            edit: {
                featureGroup: this.editableLayers,
                remove: true
            }
        });

        window.drawControl = this.drawControl;

        // Setup draw events
        this.setupDrawEvents();
    }

    setupDrawEvents() {
        this.map.on(L.Draw.Event.CREATED, (e) => {
            const layer = e.layer;
            this.editableLayers.addLayer(layer);
            
            // Add popup with delete button
            layer.bindPopup(`
                <button class="btn btn-sm btn-danger" onclick="window.mapCore.removeLayer(${layer._leaflet_id})">
                    <i class="fa fa-trash"></i> حذف
                </button>
            `);

            this.trigger('featureCreated', { layer, type: e.layerType });
        });

        this.map.on(L.Draw.Event.EDITED, (e) => {
            this.trigger('featuresEdited', { layers: e.layers });
        });

        this.map.on(L.Draw.Event.DELETED, (e) => {
            this.trigger('featuresDeleted', { layers: e.layers });
        });
    }

    initializeBaseMapManager() {
        if (window.BaseMapManager) {
            this.baseMapManager = new window.BaseMapManager(this.map, {
                position: 'bottomright',
                controlSize: 70,
                panelId: 'panel-satImages'
            });
            window.baseMapManager = this.baseMapManager;
        }
    }

    bindMapEvents() {
        this.map.on('click', (e) => {
            if (this.options.onMapClick) {
                this.options.onMapClick(e);
            }
            this.trigger('mapClick', e);
        });

        this.map.on('dblclick', (e) => {
            if (this.options.onMapDoubleClick) {
                this.options.onMapDoubleClick(e);
            }
            this.trigger('mapDoubleClick', e);
        });

        this.map.on('move', (e) => {
            if (this.options.onMapMove) {
                this.options.onMapMove(e);
            }
            this.trigger('mapMove', e);
        });

        this.map.on('zoom', (e) => {
            if (this.options.onMapZoom) {
                this.options.onMapZoom(e);
            }
            this.trigger('mapZoom', e);
        });
    }

    // ========================================
    // Public API
    // ========================================

    getMap() {
        return this.map;
    }

    getLayers() {
        return this.lyrCo;
    }

    getEditableLayers() {
        return this.editableLayers;
    }

    getMarkerLayers() {
        return this.markerLayers;
    }

    addLayer(layer) {
        if (this.editableLayers) {
            this.editableLayers.addLayer(layer);
        }
    }

    removeLayer(layerId) {
        if (this.editableLayers) {
            const layer = this.editableLayers.getLayer(layerId);
            if (layer) {
                this.editableLayers.removeLayer(layer);
            }
        }
    }

    clearAllLayers() {
        if (this.editableLayers) {
            this.editableLayers.clearLayers();
        }
        if (this.markerLayers) {
            this.markerLayers.clearLayers();
        }
    }

    setView(center, zoom) {
        if (this.map) {
            this.map.setView(center, zoom);
        }
    }

    fitBounds(bounds) {
        if (this.map) {
            this.map.fitBounds(bounds);
        }
    }

    // ========================================
    // Utility Methods
    // ========================================

    isReady() {
        return this.isInitialized && this.map !== null;
    }

    resetTools() {
        // Reset all active tools
        if (this.map) {
            this.map.getContainer().style.cursor = '';
        }

        // Reset global variables
        window.isIdentify = false;
        window.isSketch = false;
        window.selecttool = false;
        window.selectBoxtool = false;

        this.trigger('toolsReset');
    }

    // ========================================
    // Search and Identify Methods
    // ========================================

    /**
     * انجام جستجوی سریع
     */
    async performQuickSearch(searchTerm) {
        if (!searchTerm || searchTerm.trim() === '') {
            this.showWarning('لطفاً عبارت جستجو را وارد کنید');
            return;
        }

        try {
            this.showLoading('در حال جستجو...');

            const request = {
                searchTerm: searchTerm.trim(),
                maxResults: 50
            };

            const response = await fetch('/GeoMap/PerformQuickSearch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(request)
            });

            const result = await response.json();

            if (result.ok) {
                this.displaySearchResults(result.data, 'quick');
            } else {
                this.showError(result.msg);
            }
        } catch (error) {
            console.error('Error performing quick search:', error);
            this.showError('خطا در انجام جستجو');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * نمایش نتایج جستجو
     */
    displaySearchResults(results, searchType) {
        console.log(`Displaying ${searchType} search results:`, results);

        // Store results
        this.searchResults.set(searchType, results);

        // Clear previous results
        this.clearSearchResults();

        // Add results to map
        results.forEach(result => {
            this.addSearchResultToMap(result);
        });

        // Show results panel
        this.showSearchResultsPanel(results, searchType);
    }

    /**
     * پاک کردن نتایج جستجو
     */
    clearSearchResults() {
        // Remove search result layers from map
        console.log('Clearing search results');
        this.trigger('searchResultsCleared');
    }

    /**
     * اضافه کردن نتیجه جستجو به نقشه
     */
    addSearchResultToMap(result) {
        if (result.geometry) {
            // Add geometry to map
            console.log('Adding search result to map:', result);
            this.trigger('searchResultAdded', { result });
        }
    }

    /**
     * شناسایی فیچرها در نقطه کلیک
     */
    async identifyFeatures(coordinate) {
        console.log('Identifying features at:', coordinate);
        this.trigger('featuresIdentified', { coordinate });
    }

    // ========================================
    // Utility Methods
    // ========================================

    showLoading(message = 'در حال بارگذاری...') {
        console.log('Loading:', message);
        this.trigger('loadingShown', { message });
    }

    hideLoading() {
        console.log('Loading hidden');
        this.trigger('loadingHidden');
    }

    showError(message) {
        console.error('Error:', message);
        this.trigger('errorShown', { message });
        if (window.toastr) {
            toastr.error(message, 'خطا');
        }
    }

    showWarning(message) {
        console.warn('Warning:', message);
        this.trigger('warningShown', { message });
        if (window.toastr) {
            toastr.warning(message, 'هشدار');
        }
    }

    showSuccess(message) {
        console.log('Success:', message);
        this.trigger('successShown', { message });
        if (window.toastr) {
            toastr.success(message, 'موفقیت');
        }
    }

    // ========================================
    // Cleanup
    // ========================================

    destroy() {
        if (this.map) {
            this.map.remove();
            this.map = null;
        }

        // Clear global references
        window.map = null;
        window.lyrCo = [];
        window.editableLayers = null;
        window.markerLayers = null;
        window.drawControl = null;
        window.baseMapManager = null;

        super.destroy();
    }
}

// Register component
window.ComponentFactory.register('map-core', MapCore);
