/*!
 * OverlayScrollbars
 * Version: 2.11.0
 *
 * Copyright (c) <PERSON> | KingSora.
 * https://github.com/KingSora
 *
 * Released under the MIT license.
 */
var OverlayScrollbarsGlobal=function(n){"use strict";var r=function(n,r){var t,e=n.v,i=n.i,o=n.o,u=e,c=function(n,r){var e=u,c=n,a=r||(i?!i(e,c):e!==c);return(a||o)&&(u=c,t=e),[u,a,t]};return[r?function(n){return c(r(u,t),n)}:c,function(n){return[u,!!n,t]}]},t="undefined"!=typeof window&&"undefined"!=typeof HTMLElement&&window.document?window:{},e=Math.max,i=Math.min,o=Math.round,u=Math.abs,c=Math.sign,a=t.cancelAnimationFrame,f=t.requestAnimationFrame,l=t.setTimeout,s=t.clearTimeout,v=function(n){return void 0!==t[n]?t[n]:void 0},d=v("MutationObserver"),p=v("IntersectionObserver"),y=v("ResizeObserver"),h=v("ScrollTimeline"),x=function(n){return void 0===n},b=function(n){return null===n},g=function(n){return"number"==typeof n},w=function(n){return"string"==typeof n},m=function(n){return"boolean"==typeof n},M=function(n){return"function"==typeof n},O=function(n){return Array.isArray(n)},S=function(n){return"object"==typeof n&&!O(n)&&!b(n)},C=function(n){var r=!!n&&n.length,t=g(r)&&r>-1&&r%1==0;return!(!(O(n)||!M(n)&&t)||r>0&&S(n)&&!(r-1 in n))},D=function(n){return!!n&&n.constructor===Object},N=function(n){return n instanceof HTMLElement},L=function(n){return n instanceof Element},H=function(){return performance.now()},P=function(n,r,t,i,o){var u=0,c=H(),l=e(0,t),s=function(t){var a=H(),v=a-c>=l,d=t?1:1-(e(0,c+l-a)/l||0),p=(r-n)*(M(o)?o(d,d*l,0,1,l):d)+n,y=v||1===d;i&&i(p,d,y),u=y?0:f((function(){return s()}))};return s(),function(n){a(u),n&&s(n)}};function T(n,r){if(C(n))for(var t=0;t<n.length&&!1!==r(n[t],t,n);t++);else n&&T(Object.keys(n),(function(t){return r(n[t],t,n)}));return n}var k,E=function(n,r){return n.indexOf(r)>=0},j=function(n,r){return n.concat(r)},R=function(n,r,t){return!w(r)&&C(r)?Array.prototype.push.apply(n,r):n.push(r),n},A=function(n){return Array.from(n||[])},z=function(n){return O(n)?n:!w(n)&&C(n)?A(n):[n]},F=function(n){return!!n&&!n.length},V=function(n){return A(new Set(n))},_=function(n,r,t){T(n,(function(n){return!n||n.apply(void 0,r||[])})),!t&&(n.length=0)},Y="paddingTop",I="paddingRight",W="paddingLeft",B="paddingBottom",q="marginLeft",G="marginRight",X="marginBottom",U="overflowX",J="overflowY",K="width",Z="height",Q="visible",$="hidden",nn="scroll",rn=function(n,r,t,e){if(n&&r){var i=!0;return T(t,(function(t){n[t]!==r[t]&&(i=!1)})),i}return!1},tn=function(n,r){return rn(n,r,["w","h"])},en=function(n,r){return rn(n,r,["x","y"])},on=function(n,r){return rn(n,r,["t","r","b","l"])},un=function(){},cn=function(n){for(var r=arguments.length,t=new Array(r>1?r-1:0),e=1;e<r;e++)t[e-1]=arguments[e];return n.bind.apply(n,[0].concat(t))},an=function(n){var r,t=n?l:f,e=n?s:a;return[function(i){e(r),r=t((function(){return i()}),M(n)?n():n)},function(){return e(r)}]},fn=function(n,r){var t,e,i,o,u=r||{},c=u.u,v=u.p,d=u._,p=u.m,y=un,h=function(r){y(),s(t),o=t=e=void 0,y=un,n.apply(this,r)},x=function(n){return p&&e?p(e,n):n},b=function(){y!==un&&h(x(i)||i)},w=function(){var n=A(arguments),r=M(c)?c():c;if(g(r)&&r>=0){var u,p=M(v)?v():v,w=g(p)&&p>=0,m=r>0?l:f,O=r>0?s:a,S=x(n)||n,C=h.bind(0,S);y(),d&&!o?(C(),o=!0,u=m((function(){return o=void 0}),r)):(u=m(C,r),w&&!t&&(t=l(b,p))),y=function(){return O(u)},e=i=S}else h(n)};return w.S=b,w},ln=function(n,r){return Object.prototype.hasOwnProperty.call(n,r)},sn=function(n){return n?Object.keys(n):[]},vn=function(n,r,t,e,i,o,u){var c=[r,t,e,i,o,u];return"object"==typeof n&&!b(n)||M(n)||(n={}),T(c,(function(r){T(r,(function(t,e){var i=r[e];if(n===i)return!0;var o=O(i);if(i&&D(i)){var u=n[e],c=u;o&&!O(u)?c=[]:o||D(u)||(c={}),n[e]=vn(c,i)}else n[e]=o?i.slice():i}))})),n},dn=function(n,r){return T(vn({},n),(function(n,r,t){void 0===n?delete t[r]:n&&D(n)&&(t[r]=dn(n))}))},pn=function(n){return!sn(n).length},yn=function(n,r,t){return e(n,i(r,t))},hn=function(n){return V((O(n)?n:(n||"").split(" ")).filter((function(n){return n})))},xn=function(n,r){return n&&n.getAttribute(r)},bn=function(n,r){return n&&n.hasAttribute(r)},gn=function(n,r,t){T(hn(r),(function(r){n&&n.setAttribute(r,String(t||""))}))},wn=function(n,r){T(hn(r),(function(r){return n&&n.removeAttribute(r)}))},mn=function(n,r){var t=hn(xn(n,r)),e=cn(gn,n,r),i=function(n,r){var e=new Set(t);return T(hn(n),(function(n){e[r](n)})),A(e).join(" ")};return{O:function(n){return e(i(n,"delete"))},C:function(n){return e(i(n,"add"))},A:function(n){var r=hn(n);return r.reduce((function(n,r){return n&&t.includes(r)}),r.length>0)}}},Mn=function(n,r,t){return mn(n,r).O(t),cn(On,n,r,t)},On=function(n,r,t){return mn(n,r).C(t),cn(Mn,n,r,t)},Sn=function(n,r,t,e){return(e?On:Mn)(n,r,t)},Cn=function(n,r,t){return mn(n,r).A(t)},Dn=function(n){return mn(n,"class")},Nn=function(n,r){Dn(n).O(r)},Ln=function(n,r){return Dn(n).C(r),cn(Nn,n,r)},Hn=function(n,r){var t=r?L(r)&&r:document;return t?A(t.querySelectorAll(n)):[]},Pn=function(n,r){return L(n)&&n.matches(r)},Tn=function(n){return Pn(n,"body")},kn=function(n){return n?A(n.childNodes):[]},En=function(n){return n&&n.parentElement},jn=function(n,r){return L(n)&&n.closest(r)},Rn=function(n){return document.activeElement},An=function(n,r,t){var e=jn(n,r),i=n&&function(n,r){var t=r?L(r)&&r:document;return t&&t.querySelector(n)}(t,e),o=jn(i,r)===e;return!(!e||!i)&&(e===n||i===n||o&&jn(jn(n,t),r)!==e)},zn=function(n){T(z(n),(function(n){var r=En(n);n&&r&&r.removeChild(n)}))},Fn=function(n,r){return cn(zn,n&&r&&T(z(r),(function(r){r&&n.appendChild(r)})))},Vn=function(n){var r=document.createElement("div");return gn(r,"class",n),r},_n=function(n){var r=Vn(),t=k,e=n.trim();return r.innerHTML=t?t.createHTML(e):e,T(kn(r),(function(n){return zn(n)}))},Yn=function(n,r){return n.getPropertyValue(r)||n[r]||""},In=function(n){var r=n||0;return isFinite(r)?r:0},Wn=function(n){return In(parseFloat(n||""))},Bn=function(n){return Math.round(1e4*n)/1e4},qn=function(n){return Bn(In(n))+"px"};function Gn(n,r){n&&r&&T(r,(function(r,t){try{var e=n.style,o=b(r)||m(r)?"":g(r)?qn(r):r;0===t.indexOf("--")?e.setProperty(t,o):e[t]=o}catch(i){}}))}function Xn(n,r,e){var i=w(r),o=i?"":{};if(n){var u=t.getComputedStyle(n,e)||n.style;o=i?Yn(u,r):A(r).reduce((function(n,r){return n[r]=Yn(u,r),n}),o)}return o}var Un=function(n,r,t){var e=r?r+"-":"",i=t?"-"+t:"",o=e+"top"+i,u=e+"right"+i,c=e+"bottom"+i,a=e+"left"+i,f=Xn(n,[o,u,c,a]);return{t:Wn(f[o]),r:Wn(f[u]),b:Wn(f[c]),l:Wn(f[a])}},Jn=function(n,r){return"translate"+(S(n)?"("+n.x+","+n.y+")":(r?"X":"Y")+"("+n+")")},Kn={w:0,h:0},Zn=function(n,r){return r?{w:r[n+"Width"],h:r[n+"Height"]}:Kn},Qn=function(n){return Zn("inner",n||t)},$n=cn(Zn,"offset"),nr=cn(Zn,"client"),rr=cn(Zn,"scroll"),tr=function(n){var r=parseFloat(Xn(n,K))||0,t=parseFloat(Xn(n,Z))||0;return{w:r-o(r),h:t-o(t)}},er=function(n){return n.getBoundingClientRect()},ir=function(n){return!(!n||!n[Z]&&!n[K])},or=function(n,r){var t=ir(n);return!ir(r)&&t},ur=function(n,r,t,e){T(hn(r),(function(r){n&&n.removeEventListener(r,t,e)}))},cr=function(n,r,t,e){var i,o=null==(i=e&&e.T)||i,u=e&&e.H||!1,c=e&&e.P||!1,a={passive:o,capture:u};return cn(_,hn(r).map((function(r){var e=c?function(i){ur(n,r,e,u),t&&t(i)}:t;return n&&n.addEventListener(r,e,a),cn(ur,n,r,e,u)})))},ar=function(n){return n.stopPropagation()},fr=function(n){return n.preventDefault()},lr=function(n){return ar(n)||fr(n)},sr=function(n,r){var t=g(r)?{x:r,y:r}:r||{},e=t.x,i=t.y;g(e)&&(n.scrollLeft=e),g(i)&&(n.scrollTop=i)},vr=function(n){return{x:n.scrollLeft,y:n.scrollTop}},dr=function(n,r){var t=n.D,e=n.M,i=r.w,o=r.h,a=function(n,r,t){var e=c(n)*t,i=c(r)*t;if(e===i){var o=u(n),a=u(r);i=o>a?0:i,e=o<a?0:e}return[(e=e===i?0:e)+0,i+0]},f=a(t.x,e.x,i),l=f[0],s=f[1],v=a(t.y,e.y,o);return{D:{x:l,y:v[0]},M:{x:s,y:v[1]}}},pr=function(n){var r=n.D,t=n.M,e=function(n,r){return 0===n&&n<=r};return{x:e(r.x,t.x),y:e(r.y,t.y)}},yr=function(n,r){var t=n.D,e=n.M,i=function(n,r,t){return yn(0,1,(n-t)/(n-r)||0)};return{x:i(t.x,e.x,r.x),y:i(t.y,e.y,r.y)}},hr=function(n){n&&n.focus&&n.focus({preventScroll:!0})},xr=function(n,r){T(z(r),n)},br=function(n){var r=new Map,t=function(n,t){if(n){var e=r.get(n);xr((function(n){e&&e[n?"delete":"clear"](n)}),t)}else r.forEach((function(n){n.clear()})),r.clear()},e=function(n,i){if(w(n)){var o=r.get(n)||new Set;return r.set(n,o),xr((function(n){M(n)&&o.add(n)}),i),cn(t,n,i)}m(i)&&i&&t();var u=sn(n),c=[];return T(u,(function(r){var t=n[r];t&&R(c,e(r,t))})),cn(_,c)};return e(n||{}),[e,t,function(n,t){T(A(r.get(n)),(function(n){t&&!F(t)?n.apply(0,t):n()}))}]},gr={},wr={},mr=function(n,r,t){return sn(n).map((function(e){var i=n[e],o=i.static,u=i.instance,c=t||[],a=c[0],f=c[1],l=c[2],s=t?u:o;if(s){var v=t?s(a,f,r):s(r);return(l||wr)[e]=v}}))},Mr=function(n){return wr[n]};!function(n){function r(){return n.exports=r=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var e in t)({}).hasOwnProperty.call(t,e)&&(n[e]=t[e])}return n},n.exports.I=!0,n.exports["default"]=n.exports,r.apply(null,arguments)}n.exports=r,n.exports.I=!0,n.exports["default"]=n.exports}({exports:{}});var Or,Sr,Cr="__osOptionsValidationPlugin",Dr="data-overlayscrollbars",Nr="os-environment",Lr=Nr+"-scrollbar-hidden",Hr=Dr+"-initialize",Pr="noClipping",Tr=Dr+"-body",kr=Dr,Er="host",jr=Dr+"-viewport",Rr=U,Ar=J,zr="arrange",Fr="measuring",Vr="scrolling",_r="scrollbarHidden",Yr="noContent",Ir=Dr+"-padding",Wr=Dr+"-content",Br="os-size-observer",qr=Br+"-appear",Gr=Br+"-listener",Xr=Gr+"-scroll",Ur=Gr+"-item",Jr=Ur+"-final",Kr="os-trinsic-observer",Zr="os-theme-none",Qr="os-scrollbar",$r=Qr+"-rtl",nt=Qr+"-horizontal",rt=Qr+"-vertical",tt=Qr+"-track",et=Qr+"-handle",it=Qr+"-visible",ot=Qr+"-cornerless",ut=Qr+"-interaction",ct=Qr+"-unusable",at=Qr+"-auto-hide",ft=at+"-hidden",lt=Qr+"-wheel",st=tt+"-interactive",vt=et+"-interactive",dt="__osSizeObserverPlugin",pt=function(n){return(n={})[dt]={static:function(){return function(n,r,t){var e,i,o=3333333,u="scroll",c=_n('<div class="'+Ur+'" dir="ltr"><div class="'+Ur+'"><div class="'+Jr+'"></div></div><div class="'+Ur+'"><div class="'+Jr+'" style="width: 200%; height: 200%"></div></div></div>'),l=c[0],s=l.lastChild,v=l.firstChild,d=null==v?void 0:v.firstChild,p=$n(l),y=p,h=!1,x=function(){sr(v,o),sr(s,o)},b=function(n){i=0,h&&(p=y,r(!0===n))},g=function(n){y=$n(l),h=!n||!tn(y,p),n?(ar(n),h&&!i&&(a(i),i=f(b))):b(!1===n),x()},w=[Fn(n,c),cr(v,u,g),cr(s,u,g)];return Ln(n,Xr),Gn(d,((e={})[K]=o,e[Z]=o,e)),f(x),[t?cn(g,!1):x,w]}}},n}(),yt=function(n,r){var t=r.L,e=n("showNativeOverlaidScrollbars"),i=e[0],o=e[1];return[i&&t.x&&t.y,o]},ht=function(n){return 0===n.indexOf(Q)},xt=function(n,r){var t=function(n,r,t,e){var i=n===Q?$:n.replace(Q+"-",""),o=ht(n),u=ht(t);return r||e?o&&u?Q:o?r&&e?i:r?Q:$:r?i:u&&e?Q:$:$},e={x:t(r.x,n.x,r.y,n.y),y:t(r.y,n.y,r.x,n.x)};return{V:e,k:{x:e.x===nn,y:e.y===nn}}},bt="__osScrollbarsHidingPlugin",gt=function(n){return(n={})[bt]={static:function(){return{R:function(n,r,t,e,i){var o=n.F,u=n.N,c=e.j,a=e.L,f=e.q,l=!o&&!c&&(a.x||a.y),s=yt(i,e)[0],v=function(n){var r=n.k,t=c||s?0:42,e=function(n,r,e){return[r&&!c?n?t:e:0,n&&!!t]},i=e(a.x,r.x,f.x),o=i[0],u=i[1],l=e(a.y,r.y,f.y);return{U:{x:o,y:l[0]},B:{x:u,y:l[1]}}},d=function(n,t,e){var i=t.Y;if(!o){var u,c=vn({},((u={})[G]=0,u[X]=0,u[q]=0,u)),a=v(n),f=a.U,l=a.B,s=l.x,d=l.y,p=f.x,y=f.y,h=r.W,x=i?q:G,b=i?W:I,g=h[x],w=h[X],m=h[b],M=h[B];return c[K]="calc(100% + "+(y+-1*g)+"px)",c[x]=-y+g,c[X]=-p+w,e&&(c[b]=m+(d?y:0),c[B]=M+(s?p:0)),c}};return{X:v,Z:function(n,e,i){if(l){var o=r.W,c=v(n),a=c.U,f=c.B,s=f.x,d=f.y,p=a.x,y=a.y,h=o[t.Y?I:W],x=o.paddingTop,b=e.w+i.w,g=e.h+i.h,w={w:y&&d?y+b-h+"px":"",h:p&&s?p+g-x+"px":""};Gn(u,{"--os-vaw":w.w,"--os-vah":w.h})}return l},G:function(n){if(l){var e=n||function(){var n=function(n){var r=Xn(u,n);return[r,r===nn]},r=n(U),t=r[0],e=r[1],i=n(J);return{V:{x:t,y:i[0]},k:{x:e,y:i[1]}}}(),i=r.W,o=v(e).B,c=o.x,a=o.y,f={},s=function(n){return T(n,(function(n){f[n]=i[n]}))};c&&s([X,Y,B]),a&&s([q,G,W,I]);var p=Xn(u,sn(f)),y=Mn(u,jr,zr);return Gn(u,f),[function(){Gn(u,vn({},p,d(e,t,l))),y()},e]}return[un]},$:d}}}}},n}(),wt="__osClickScrollPlugin",mt=function(n){return(n={})[wt]={static:function(){return function(n,r,t,e){var i=!1,o=un,u=133,c=222,a=an(u),f=a[0],l=a[1],s=Math.sign(r),v=t*s,d=v/2,p=function(n){return 1-(1-n)*(1-n)},y=function(r,t){return P(r,t,c,n,p)},h=function(t,e){return P(t,r-v,u*e,(function(t,e,i){n(t),i&&(o=y(t,r))}))},x=P(0,v,c,(function(u,c,a){if(n(u),a&&(e(i),!i)){var l=r-u;Math.sign(l-d)===s&&f((function(){var n=l-v,e=Math.sign(n)===s;o=e?h(u,Math.abs(n)/t):y(u,r)}))}}),p);return function(n){i=!0,n&&x(),l(),o()}}}},n}(),Mt=function(n){return JSON.stringify(n,(function(n,r){if(M(r))throw 0;return r}))},Ot=function(n,r){return n?(""+r).split(".").reduce((function(n,r){return n&&ln(n,r)?n[r]:void 0}),n):void 0},St={paddingAbsolute:!1,showNativeOverlaidScrollbars:!1,update:{elementEvents:[["img","load"]],debounce:[0,33],attributes:null,ignoreMutation:null},overflow:{x:"scroll",y:"scroll"},scrollbars:{theme:"os-theme-dark",visibility:"auto",autoHide:"never",autoHideDelay:1300,autoHideSuspend:!1,dragScroll:!0,clickScroll:!1,pointers:["mouse","touch","pen"]}},Ct=function(n,r){var t={};return T(j(sn(r),sn(n)),(function(e){var i=n[e],o=r[e];if(S(i)&&S(o))vn(t[e]={},Ct(i,o)),pn(t[e])&&delete t[e];else if(ln(r,e)&&o!==i){var c=!0;if(O(i)||O(o))try{Mt(i)===Mt(o)&&(c=!1)}catch(u){}c&&(t[e]=o)}})),t},Dt=function(n,r,t){return function(e){return[Ot(n,e),t||void 0!==Ot(r,e)]}},Nt=function(){return Sr||(Sr=function(){var n=function(n,r,t){Fn(document.body,n),Fn(document.body,n);var e=nr(n),i=$n(n),o=tr(r);return t&&zn(n),{x:i.h-e.h+o.h,y:i.w-e.w+o.w}},e=_n('<div class="'+Nr+'"><div></div><style>'+"."+Nr+"{scroll-behavior:auto!important;position:fixed;opacity:0;visibility:hidden;overflow:scroll;height:200px;width:200px;z-index:-1}."+Nr+" div{width:200%;height:200%;margin:10px 0}."+Lr+"{scrollbar-width:none!important}."+Lr+"::-webkit-scrollbar,."+Lr+"::-webkit-scrollbar-corner{appearance:none!important;display:none!important;width:0!important;height:0!important}"+"</style></div>")[0],i=e.firstChild,o=e.lastChild,u=Or;u&&(o.nonce=u);var c=br(),a=c[0],f=c[2],l=r({v:n(e,i),i:en},cn(n,e,i,!0)),s=l[0],v=(0,l[1])()[0],d=function(n){var r=!1,e=Ln(n,Lr);try{r="none"===Xn(n,"scrollbar-width")||"none"===Xn(n,"display","::-webkit-scrollbar")}catch(t){}return e(),r}(e),p={x:0===v.x,y:0===v.y},y={elements:{host:null,padding:!d,viewport:function(n){return d&&Tn(n)&&n},content:!1},scrollbars:{slot:!0},cancel:{nativeScrollbarsOverlaid:!1,body:null}},x=vn({},St),b=cn(vn,{},x),g=cn(vn,{},y),w={q:v,L:p,j:d,J:!!h,K:cn(a,"r"),rr:g,ar:function(n){return vn(y,n)&&g()},er:b,tr:function(n){return vn(x,n)&&b()},nr:vn({},y),vr:vn({},x)};if(wn(e,"style"),zn(e),cr(t,"resize",(function(){f("r",[])})),M(t.matchMedia)&&!d&&(!p.x||!p.y)){var m=function(n){var r=t.matchMedia("(resolution: "+t.devicePixelRatio+"dppx)");cr(r,"change",(function(){n(),m(n)}),{P:!0})};m((function(){var n=s(),r=n[0],t=n[1];vn(w.q,r),f("r",[t])}))}return w}()),Sr},Lt=function(n,r,t,e){var i=!1,o=e||{},u=o.ir,c=o.ur,a=o.cr,f=o.lr,l=o.sr,s=o.dr,v=fn((function(){return i&&t(!0)}),{u:33,p:99}),p=function(n,r,t){var e=!1,i=!!t&&new WeakMap,o=function(o){if(i&&t){var u=t.map((function(r){var t=r||[],e=t[0],i=t[1];return[i&&e?(o||Hn)(e,n):[],i]}));T(u,(function(t){return T(t[0],(function(o){var u=t[1],c=i.get(o)||[];if(n.contains(o)&&u){var a=cr(o,u,(function(n){e?(a(),i.delete(o)):r(n)}));i.set(o,R(c,a))}else _(c),i.delete(o)}))}))}};return o(),[function(){e=!0},o]}(n,v,a),y=p[0],h=p[1],x=c||[],b=j(u||[],x),g=function(i,o){if(!F(o)){var u=l||un,c=s||un,a=[],v=[],d=!1,p=!1;if(T(o,(function(t){var i=t.attributeName,o=t.target,l=t.type,s=t.oldValue,y=t.addedNodes,h=t.removedNodes,b="attributes"===l,g="childList"===l,m=n===o,M=b&&i,O=M&&xn(o,i||""),S=w(O)?O:null,C=M&&s!==S,D=E(x,i)&&C;if(r&&(g||!m)){var N=b&&C,L=N&&f&&Pn(o,f),H=(L?!u(o,i,s,S):!b||N)&&!c(t,!!L,n,e);T(y,(function(n){return R(a,n)})),T(h,(function(n){return R(a,n)})),p=p||H}!r&&m&&C&&!u(o,i,s,S)&&(R(v,i),d=d||D)})),h((function(n){return V(a).reduce((function(r,t){return R(r,Hn(n,t)),Pn(t,n)?R(r,t):r}),[])})),r)return!i&&p&&t(!1),[!1];if(!F(v)||d){var y=[V(v),d];return!i&&t.apply(0,y),y}}},m=new d(cn(g,!1));return[function(){return m.observe(n,{attributes:!0,attributeOldValue:!0,attributeFilter:b,subtree:r,childList:r,characterData:r}),i=!0,function(){i&&(y(),m.disconnect(),i=!1)}},function(){if(i)return v.S(),g(!0,m.takeRecords())}]},Ht=function(n,t,e){var i=(e||{}).pr,o=Mr(dt),u=r({v:!1,o:!0})[0];return function(){var r=[],e=_n('<div class="'+Br+'"><div class="'+Gr+'"></div></div>')[0],c=e.firstChild,a=function(n){var r=!1,e=!1;if(n instanceof ResizeObserverEntry){var i=u(n.contentRect),o=i[0],c=i[2],a=ir(o);r=!(e=or(o,c))&&!a}else e=!0===n;r||t({_r:!0,pr:e})};if(y){var f=new y((function(n){return a(n.pop())}));f.observe(c),R(r,(function(){f.disconnect()}))}else{if(!o)return un;var l=o(c,a,i),s=l[0],v=l[1];R(r,j([Ln(e,qr),cr(e,"animationstart",s)],v))}return cn(_,R(r,Fn(n,e)))}},Pt=function(n,t){var e,i=Vn(Kr),o=r({v:!1})[0],u=function(n,r){if(n){var e=o(function(n){return 0===n.h||n.isIntersecting||n.intersectionRatio>0}(n));return e[1]&&!r&&t(e)&&[e]}},c=function(n,r){return u(r.pop(),n)};return[function(){var r=[];if(p)(e=new p(cn(c,!1),{root:n})).observe(i),R(r,(function(){e.disconnect()}));else{var t=function(){var n=$n(i);u(n)};R(r,Ht(i,t)()),t()}return cn(_,R(r,Fn(n,i)))},function(){return e&&c(!0,e.takeRecords())}]},Tt=function(n,t,e,i){var o,u,c,a,f,l,s="["+kr+"]",v="["+jr+"]",d=["id","class","style","open","wrap","cols","rows"],p=n.gr,h=n.hr,x=n.N,b=n.br,w=n.mr,m=n.F,S=n.yr,C=n.Sr,D=n.wr,N=n.Or,L=function(n){return"rtl"===Xn(n,"direction")},H={Cr:!1,Y:L(p)},P=Nt(),T=Mr(bt),k=r({i:tn,v:{w:0,h:0}},(function(){var r=T&&T.R(n,t,H,P,e).G,i=!(S&&m)&&Cn(h,kr,Pr),o=!m&&C(zr),u=o&&vr(b),c=u&&N(),a=D(Fr,i),f=o&&r&&r()[0],l=rr(x),s=tr(x);return f&&f(),sr(b,u),c&&c(),i&&a(),{w:l.w+s.w,h:l.h+s.h}})),E=k[0],R=fn(i,{u:function(){return o},p:function(){return u},m:function(n,r){var t=n[0],e=r[0];return[j(sn(t),sn(e)).reduce((function(n,r){return n[r]=t[r]||e[r],n}),{})]}}),A=function(n){var r=L(p);vn(n,{Er:l!==r}),vn(H,{Y:r}),l=r},z=function(n,r){var t=n[0],e={Ar:n[1]};return vn(H,{Cr:t}),!r&&i(e),e},F=function(n){var r=n._r,t=n.pr,e=r&&!t||!P.j?i:R,o={_r:r||t,pr:t};A(o),e(o)},V=function(n,r){var t=E()[1],e={Tr:t};return A(e),t&&!r&&(n?i:R)(e),e},_=function(n,r,t){var e={Hr:r};return A(e),r&&!t&&R(e),e},Y=w?Pt(h,z):[],I=Y[0],W=Y[1],B=!m&&Ht(h,F,{pr:!0}),q=Lt(h,!1,_,{ur:d,ir:d}),G=q[0],X=q[1],U=m&&y&&new y((function(n){var r=n[n.length-1].contentRect;F({_r:!0,pr:or(r,f)}),f=r})),J=fn((function(){var n=E()[1];i({Tr:n})}),{u:222,_:!0});return[function(){U&&U.observe(h);var n=B&&B(),r=I&&I(),t=G(),e=P.K((function(n){n?R({Pr:n}):J()}));return function(){U&&U.disconnect(),n&&n(),r&&r(),a&&a(),t(),e()}},function(n){var r=n.Dr,t=n.zr,e=n.Mr,i={},f=r("update.ignoreMutation")[0],l=r("update.attributes"),p=l[0],y=l[1],h=r("update.elementEvents"),b=h[0],S=h[1],C=r("update.debounce"),D=C[0],N=C[1],L=t||e;if(S||y){c&&c(),a&&a();var H=Lt(w||x,!0,V,{ir:j(d,p||[]),cr:b,lr:s,dr:function(n,r){var t=n.target,e=n.attributeName;return!(r||!e||m)&&An(t,s,v)||!!jn(t,"."+Qr)||!!function(n){return M(f)&&f(n)}(n)}}),P=H[0],T=H[1];a=P(),c=T}if(N)if(R.S(),O(D)){var k=D[0],E=D[1];o=g(k)&&k,u=g(E)&&E}else g(D)?(o=D,u=!1):(o=!1,u=!1);if(L){var F=X(),Y=W&&W(),I=c&&c();F&&vn(i,_(F[0],F[1],L)),Y&&vn(i,z(Y[0],L)),I&&vn(i,V(I[0],L))}return A(i),i},H]},kt=function(n,r){return M(r)?r.apply(0,n):r},Et=function(n,r,t,e){var i=x(e)?t:e;return kt(n,i)||r.apply(0,n)},jt=function(n,r,t,e){var i=x(e)?t:e,o=kt(n,i);return!!o&&(N(o)?o:r.apply(0,n))},Rt=function(n,r){var t=r||{},e=t.nativeScrollbarsOverlaid,i=t.body,o=Nt(),u=o.L,c=o.j,a=(0,o.rr)().cancel,f=a.nativeScrollbarsOverlaid,l=a.body,s=null!=e?e:f,v=x(i)?l:i,d=(u.x||u.y)&&s,p=n&&(b(v)?!c:v);return!!d||!!p},At=function(n,r,t,e){var i="--os-viewport-percent",o="--os-scroll-percent",u="--os-scroll-direction",c=(0,Nt().rr)().scrollbars.slot,a=r.gr,f=r.hr,l=r.N,s=r.Ir,v=r.br,d=r.yr,p=r.F,y=((s?{}:n).scrollbars||{}).slot,x=[],b=[],g=[],w=jt([a,f,l],(function(){return p&&d?a:f}),c,y),M=function(n){if(h){var r=null,e=[],i=new h({source:v,axis:n}),o=function(){r&&r.cancel(),r=null};return{kr:function(u){var c=t.Lr,a=pr(c)[n],f="x"===n,l=[Jn(0,f),Jn("calc(100cq"+(f?"w":"h")+" + -100%)",f)],s=a?l:l.reverse();return e[0]===s[0]&&e[1]===s[1]||(o(),e=s,r=u.Vr.animate({clear:["left"],transform:s},{timeline:i})),o}}}},O={x:M("x"),y:M("y")},S=function(n,r,t){var e=t?Ln:Nn;T(n,(function(n){e(n.Nr,r)}))},C=function(n,r){T(n,(function(n){var t=r(n);Gn(t[0],t[1])}))},D=function(n,r,t){var e=m(t),i=!e||!t;(!e||t)&&S(b,n,r),i&&S(g,n,r)},N=function(n){var r=n?"x":"y",t=Vn(Qr+" "+(n?nt:rt)),i=Vn(tt),o=Vn(et),u={Nr:t,jr:i,Vr:o},c=O[r];return R(n?b:g,u),R(x,[Fn(t,i),Fn(i,o),cn(zn,t),c&&c.kr(u),e(u,D,n)]),u},L=cn(N,!0),H=cn(N,!1);return L(),H(),[{qr:function(){var n=function(){var n=t.Rr,r=t.Fr,e=function(n,r){return yn(0,1,n/(n+r)||0)};return{x:e(r.x,n.x),y:e(r.y,n.y)}}(),r=function(n){return function(r){var t;return[r.Nr,(t={},t[i]=Bn(n)+"",t)]}};C(b,r(n.x)),C(g,r(n.y))},Ur:function(){if(!h){var n=t.Lr,r=yr(n,vr(v)),e=function(n){return function(r){var t;return[r.Nr,(t={},t[o]=Bn(n)+"",t)]}};C(b,e(r.x)),C(g,e(r.y))}},Br:function(){var n=t.Lr,r=pr(n),e=function(n){return function(r){var t;return[r.Nr,(t={},t[u]=n?"0":"1",t)]}};C(b,e(r.x)),C(g,e(r.y)),h&&(b.forEach(O.x.kr),g.forEach(O.y.kr))},Yr:function(){if(p&&!d){var n=t.Rr,r=t.Lr,e=pr(r),i=yr(r,vr(v)),o=function(r){var t=r.Nr,o=En(t)===l&&t,u=function(n,r,t){var e=r*n;return qn(t?e:-e)};return[o,o&&{transform:Jn({x:u(i.x,n.x,e.x),y:u(i.y,n.y,e.y)})}]};C(b,o),C(g,o)}},Wr:D,Xr:{Zr:b,Gr:L,$r:cn(C,b)},Jr:{Zr:g,Gr:H,$r:cn(C,g)}},function(){return Fn(w,b[0].Nr),Fn(w,g[0].Nr),cn(_,x)}]},zt=function(n,r,t,e){return function(i,c,a){var f=r.hr,s=r.N,v=r.F,d=r.br,p=r.Kr,y=r.Or,h=i.Nr,x=i.jr,b=i.Vr,g=an(333),w=g[0],m=g[1],O=an(444),S=O[0],C=O[1],D=function(n){M(d.scrollBy)&&d.scrollBy({behavior:"smooth",left:n.x,top:n.y})},N=!0;return cn(_,[cr(b,"pointermove pointerleave",e),cr(h,"pointerenter",(function(){c(ut,!0)})),cr(h,"pointerleave pointercancel",(function(){c(ut,!1)})),!v&&cr(h,"mousedown",(function(){var n=Rn();(bn(n,jr)||bn(n,kr)||n===document.body)&&l(cn(hr,s),25)})),cr(h,"wheel",(function(n){var r=n.deltaX,t=n.deltaY,e=n.deltaMode;N&&0===e&&En(h)===f&&D({x:r,y:t}),N=!1,c(lt,!0),w((function(){N=!0,c(lt)})),fr(n)}),{T:!1,H:!0}),cr(h,"pointerdown",cn(cr,p,"click",lr,{P:!0,H:!0,T:!1}),{H:!0}),function(){var r="pointerup pointercancel lostpointercapture",e="client"+(a?"X":"Y"),i=a?K:Z,c=a?"left":"top",f=a?"w":"h",l=a?"x":"y",s=function(n,r){return function(e){var i,o=t.Rr,u=$n(x)[f]-$n(b)[f],c=r*e/u*o[l];sr(d,((i={})[l]=n+c,i))}},v=[];return cr(x,"pointerdown",(function(t){var a=jn(t.target,"."+et)===b,h=a?b:x,g=n.scrollbars,w=g[a?"dragScroll":"clickScroll"],m=t.button,M=t.isPrimary,O=t.pointerType,N=g.pointers;if(0===m&&M&&w&&(N||[]).includes(O)){_(v),C();var L=!a&&(t.shiftKey||"instant"===w),H=cn(er,b),P=cn(er,x),T=o(er(d)[i])/$n(d)[f]||1,k=s(vr(d)[l],1/T),E=t[e],j=H(),A=P(),z=j[i],F=function(n,r){return(n||H())[c]-(r||P())[c]}(j,A)+z/2,V=E-A[c],Y=a?0:V-F,I=function(n){_(q),h.releasePointerCapture(n.pointerId)},W=a||L,B=y(),q=[cr(p,r,I),cr(p,"selectstart",(function(n){return fr(n)}),{T:!1}),cr(x,r,I),W&&cr(x,"pointermove",(function(n){return k(Y+(n[e]-E))})),W&&function(){var n=vr(d);B();var r=vr(d),t={x:r.x-n.x,y:r.y-n.y};(u(t.x)>3||u(t.y)>3)&&(y(),sr(d,n),D(t),S(B))}];if(h.setPointerCapture(t.pointerId),L)k(Y);else if(!a){var G=Mr(wt);if(G){var X=G(k,Y,z,(function(n){n?B():R(q,B)}));R(q,X),R(v,cn(X,!0))}}}}))}(),m,C])}},Ft=function(n){var r=Nt(),e=r.rr,i=r.j,o=e().elements,u=o.padding,c=o.viewport,a=o.content,f=N(n),l=f?{}:n,s=l.elements||{},v=s.padding,d=s.viewport,p=s.content,y=f?n:l.target,h=Tn(y),x=y.ownerDocument,b=x.documentElement,g=function(){return x.defaultView||t},w=cn(Et,[y]),m=cn(jt,[y]),M=cn(Vn,""),O=cn(w,M,c),S=cn(m,M,a),C=O(d),D=C===y,L=D&&h,H=!D&&S(p),P=!D&&C===H,T=L?b:C,k=L?T:y,j=!D&&m(M,u,v),A=!P&&H,z=[A,T,j,k].map((function(n){return N(n)&&!En(n)&&n})),F=function(n){return n&&E(z,n)},V=!F(T)&&function(n){var r=$n(n),t=rr(n),e=Xn(n,U),i=Xn(n,J);return t.w-r.w>0&&!ht(e)||t.h-r.h>0&&!ht(i)}(T)?T:y,Y=L?b:T,I={gr:y,hr:k,N:T,oa:j,mr:A,br:Y,Qr:L?x:T,ua:h?b:V,Kr:x,yr:h,Ir:f,F:D,ca:g,Sr:function(n){return Cn(T,jr,n)},wr:function(n,r){return Sn(T,jr,n,r)},Or:function(){return Sn(Y,jr,Vr,!0)}},W=I.gr,B=I.hr,q=I.oa,G=I.N,X=I.mr,K=[function(){wn(B,[kr,Hr]),wn(W,Hr),h&&wn(b,[Hr,kr])}],Z=kn([X,G,q,B,W].find((function(n){return n&&!F(n)}))),Q=L?W:X||G,$=cn(_,K);return[I,function(){var n=g(),r=Rn(),t=function(n){Fn(En(n),kn(n)),zn(n)},e=function(n){return cr(n,"focusin focusout focus blur",lr,{H:!0,T:!1})},o="tabindex",u=xn(G,o),c=e(r);return gn(B,kr,D?"":Er),gn(q,Ir,""),gn(G,jr,""),gn(X,Wr,""),D||(gn(G,o,u||"-1"),h&&gn(b,Tr,"")),Fn(Q,Z),Fn(B,q),Fn(q||B,!D&&G),Fn(G,X),R(K,[c,function(){var n=Rn(),r=F(G),i=r&&n===G?W:n,c=e(i);wn(q,Ir),wn(X,Wr),wn(G,jr),h&&wn(b,Tr),u?gn(G,o,u):wn(G,o),F(X)&&t(X),r&&t(G),F(q)&&t(q),hr(i),c()}]),i&&!D&&(On(G,jr,_r),R(K,cn(wn,G,jr))),hr(!D&&h&&r===W&&n.top===n?G:r),c(),Z=0,$},$]},Vt=function(n){var r=n.mr;return function(n){var t,e=n.ra,i=n.la,o=n.Mr,u=(e||{}).Ar,c=i.Cr;r&&(u||o)&&Gn(r,((t={})[Z]=c&&"100%",t))}},_t=function(n,t){var e=n.hr,i=n.oa,o=n.N,u=n.F,c=r({i:on,v:Un()},cn(Un,e,"padding","")),a=c[0],f=c[1];return function(n){var r=n.Dr,e=n.ra,c=n.la,l=n.Mr,s=f(l),v=s[0],d=s[1],p=Nt().j,y=e||{},h=y._r,x=y.Tr,b=y.Er,g=c.Y,w=r("paddingAbsolute"),m=w[0],M=w[1];if(h||d||l||x){var O=a(l);v=O[0],d=O[1]}var S=!u&&(M||b||d);if(S){var C,D,N=!m||!i&&!p,L=v.r+v.l,H=v.t+v.b,P=((C={})[G]=N&&!g?-L:0,C[X]=N?-H:0,C[q]=N&&g?-L:0,C.top=N?-v.t:0,C.right=N?g?-v.r:"auto":0,C.left=N?g?"auto":-v.l:0,C[K]=N&&"calc(100% + "+L+"px)",C),T=((D={})[Y]=N?v.t:0,D[I]=N?v.r:0,D[B]=N?v.b:0,D[W]=N?v.l:0,D);Gn(i||o,P),Gn(o,T),vn(t,{oa:v,fa:!N,W:i?T:vn({},P,T)})}return{sa:S}}},Yt=function(n,i){var o=Nt(),u=n.hr,c=n.oa,a=n.N,l=n.F,s=n.Qr,v=n.br,d=n.yr,p=n.wr,y=n.ca,h=o.j,x=d&&l,b=cn(e,0),g={display:function(){return!1},direction:function(n){return"ltr"!==n},flexDirection:function(n){return n.endsWith("-reverse")},writingMode:function(n){return"horizontal-tb"!==n}},w=sn(g),m={i:tn,v:{w:0,h:0}},M={i:en,v:{}},O=function(n){p(Fr,!x&&n)},S=function(n){var r=w.some((function(r){var t=n[r];return t&&g[r](t)}));if(!r)return{D:{x:0,y:0},M:{x:1,y:1}};O(!0);var t=vr(v),e=p(Yr,!0),i=cr(s,nn,(function(n){var r=vr(v);n.isTrusted&&r.x===t.x&&r.y===t.y&&ar(n)}),{H:!0,P:!0});sr(v,{x:0,y:0}),e();var o=vr(v),u=rr(v);sr(v,{x:u.w,y:u.h});var c=vr(v);sr(v,{x:c.x-o.x<1&&-u.w,y:c.y-o.y<1&&-u.h});var a=vr(v);return sr(v,t),f((function(){return i()})),{D:o,M:a}},C=function(n,r){var e=t.devicePixelRatio%1!=0?1:0,i={w:b(n.w-r.w),h:b(n.h-r.h)};return{w:i.w>e?i.w:0,h:i.h>e?i.h:0}},D=r(m,cn(tr,a)),N=D[0],L=D[1],H=r(m,cn(rr,a)),P=H[0],T=H[1],k=r(m),E=k[0],j=k[1],R=r(M)[0],A=r(m),z=A[0],F=A[1],V=r(M)[0],_=r({i:function(n,r){return rn(n,r,w)},v:{}},(function(){return function(n){return!!n&&function(n){return!!(n.offsetWidth||n.offsetHeight||n.getClientRects().length)}(n)}(a)?Xn(a,w):{}})),Y=_[0],I=r({i:function(n,r){return en(n.D,r.D)&&en(n.M,r.M)},v:{D:{x:0,y:0},M:{x:0,y:0}}}),W=I[0],B=I[1],q=Mr(bt),G=function(n,r){return""+(r?Rr:Ar)+function(n){var r=String(n||"");return r?r[0].toUpperCase()+r.slice(1):""}(n)},X=function(n){var r=function(n){return[Q,$,nn].map((function(r){return G(r,n)}))},t=r(!0).concat(r()).join(" ");p(t),p(sn(n).map((function(r){return G(n[r],"x"===r)})).join(" "),!0)};return function(r,t){var e=r.Dr,f=r.ra,l=r.la,s=r.Mr,v=t.sa,d=f||{},g=d.Er,w=d.pr,m=d.Pr,M=q&&q.R(n,i,l,o,e)||{},D=M.Z,H=M.G,k=M.$,A=yt(e,o),_=A[0],I=A[1],G=e("overflow"),U=G[0],J=G[1],K=ht(U.x),Z=ht(U.y),Q=L(s),$=T(s),nn=j(s),rn=F(s);I&&h&&p(_r,!_),Cn(u,kr,Pr)&&O(!0);var tn=(H?H():[])[0],en=(Q=N(s))[0],on=($=P(s))[0],un=nr(a),cn=x&&Qn(y()),an={w:b(on.w+en.w),h:b(on.h+en.h)},fn={w:b((cn?cn.w:un.w+b(un.w-on.w))+en.w),h:b((cn?cn.h:un.h+b(un.h-on.h))+en.h)};tn&&tn(),rn=z(fn),nn=E(C(an,fn),s);var ln=rn,sn=ln[0],dn=ln[1],pn=nn,yn=pn[0],hn=pn[1],xn=$,bn=xn[0],gn=xn[1],wn=Q,mn=wn[0],Mn=wn[1],On=R({x:yn.w>0,y:yn.h>0}),Dn=On[0],Nn=On[1],Ln=K&&Z&&(Dn.x||Dn.y)||K&&Dn.x&&!Dn.y||Z&&Dn.y&&!Dn.x,Hn=v||g||m||Mn||gn||dn||hn||J||I||!0,Pn=xt(Dn,U),Tn=V(Pn.V),kn=Tn[0],En=Tn[1],jn=Y(s),Rn=jn[0],An=jn[1],zn=g||w||An||Nn||s,Fn=zn?W(S(Rn),s):B(),Vn=Fn[0],_n=Fn[1];return Hn&&(En&&X(Pn.V),k&&D&&Gn(a,k(Pn,l,D(Pn,bn,mn)))),O(!1),Sn(u,kr,Pr,Ln),Sn(c,Ir,Pr,Ln),vn(i,{V:kn,Fr:{x:sn.w,y:sn.h},Rr:{x:yn.w,y:yn.h},ia:Dn,Lr:dr(Vn,yn)}),{na:En,ea:dn,ta:hn,va:_n||hn,da:zn}}},It=new WeakMap,Wt=function(n){return It.get(n)},Bt=function(n,r,t){var e=Nt().er,i=N(n),o=i?n:n.target,u=Wt(o);if(r&&!u){var c=!1,a=[],f={},l=function(n){var r=dn(n),t=Mr(Cr);return t?t(r,!0):r},s=vn({},e(),l(r)),v=br(),d=v[0],p=v[1],y=v[2],h=br(t),x=h[0],b=h[1],g=h[2],w=function(n,r){g(n,r),y(n,r)},m=function(n,r,t,e,i){var o=!1,u=Dt(r,{}),c=function(n){var r,t=Ft(n),e=t[0],i=t[1],o=t[2],u={oa:{t:0,r:0,b:0,l:0},fa:!1,W:(r={},r[G]=0,r[X]=0,r[q]=0,r[Y]=0,r[I]=0,r[B]=0,r[W]=0,r),Fr:{x:0,y:0},Rr:{x:0,y:0},V:{x:$,y:$},ia:{x:!1,y:!1},Lr:{D:{x:0,y:0},M:{x:0,y:0}}},c=e.gr,a=e.br,f=e.F,l=e.Or,s=Nt(),v=s.j,d=s.L,p=!v&&(d.x||d.y),y=[Vt(e),_t(e,u),Yt(e,u)];return[i,function(n){var r={},t=p&&vr(a),e=t&&l();return T(y,(function(t){vn(r,t(n,r)||{})})),sr(a,t),e&&e(),!f&&sr(c,0),r},u,e,o]}(n),a=c[0],f=c[1],l=c[2],s=c[3],v=c[4],d=Tt(s,l,u,(function(n){M({},n)})),p=d[0],y=d[1],h=d[2],x=function(n,r,t,e,i,o){var u,c,a,f,l,s=un,v=0,d=["mouse","pen"],p=function(n){return d.includes(n.pointerType)},y=an(),h=y[0],x=y[1],b=an(100),g=b[0],w=b[1],m=an(100),M=m[0],O=m[1],S=an((function(){return v})),C=S[0],D=S[1],N=At(n,i,e,zt(r,i,e,(function(n){return p(n)&&Y()}))),L=N[0],H=N[1],P=i.hr,T=i.Qr,k=i.yr,E=L.Wr,j=L.qr,A=L.Ur,z=L.Br,F=L.Yr,V=function(n,r){if(D(),n)E(ft);else{var t=cn(E,ft,!0);v>0&&!r?C(t):t()}},Y=function(){(a?u:f)||(V(!0),g((function(){V(!1)})))},I=function(n){E(at,n,!0),E(at,n,!1)},W=function(n){p(n)&&(u=a,a&&V(!0))},B=[D,w,O,x,function(){return s()},cr(P,"pointerover",W,{P:!0}),cr(P,"pointerenter",W),cr(P,"pointerleave",(function(n){p(n)&&(u=!1,a&&V(!1))})),cr(P,"pointermove",(function(n){p(n)&&c&&Y()})),cr(T,"scroll",(function(n){h((function(){A(),Y()})),o(n),F()}))];return[function(){return cn(_,R(B,H()))},function(n){var r=n.Dr,i=n.Mr,o=n.ra,u=n.aa||{},d=u.ea,p=u.ta,y=u.na,h=u.va,x=o||{},b=x.Er,g=x.pr,w=t.Y,m=Nt().L,O=e.V,S=e.ia,C=r("showNativeOverlaidScrollbars"),D=C[0],N=C[1],L=r("scrollbars.theme"),H=L[0],P=L[1],R=r("scrollbars.visibility"),_=R[0],Y=R[1],W=r("scrollbars.autoHide"),B=W[0],q=W[1],G=r("scrollbars.autoHideSuspend"),X=G[0],U=G[1],J=r("scrollbars.autoHideDelay")[0],K=r("scrollbars.dragScroll"),Z=K[0],$=K[1],rn=r("scrollbars.clickScroll"),tn=rn[0],en=rn[1],on=r("overflow"),un=on[0],an=on[1],fn=g&&!i,ln=S.x||S.y,sn=d||p||h||b||i,vn=y||Y||an,dn=D&&m.x&&m.y,pn=function(n,r,t){var e=n.includes(nn)&&(_===Q||"auto"===_&&r===nn);return E(it,e,t),e};if(v=J,fn&&(X&&ln?(I(!1),s(),M((function(){s=cr(T,"scroll",cn(I,!0),{P:!0})}))):I(!0)),N&&E(Zr,dn),P&&(E(l),E(H,!0),l=H),U&&!X&&I(!0),q&&(c="move"===B,a="leave"===B,V(f="never"===B,!0)),$&&E(vt,Z),en&&E(st,!!tn),vn){var yn=pn(un.x,O.x,!0),hn=pn(un.y,O.y,!1);E(ot,!(yn&&hn))}sn&&(A(),j(),F(),h&&z(),E(ct,!S.x,!0),E(ct,!S.y,!1),E($r,w&&!k))},{},L]}(n,r,h,l,s,i),b=x[0],g=x[1],w=x[3],m=function(n){return sn(n).some((function(r){return!!n[r]}))},M=function(n,i){if(t())return!1;var u=n.pa,c=n.Mr,a=n.zr,l=n._a,s=u||{},v=!!c||!o,d={Dr:Dt(r,s,v),pa:s,Mr:v};if(l)return g(d),!1;var p=i||y(vn({},d,{zr:a})),x=f(vn({},d,{la:h,ra:p}));g(vn({},d,{ra:p,aa:x}));var b=m(p),w=m(x),M=b||w||!pn(s)||v;return o=!0,M&&e(n,{ra:p,aa:x}),M};return[function(){var n=s.ua,r=s.br,t=s.Or,e=vr(n),i=[p(),a(),b()],o=t();return sr(r,e),o(),cn(_,i)},M,function(){return{ga:h,ha:l}},{ba:s,ma:w},v]}(n,s,(function(){return c}),(function(n,r){var t=n.pa,e=n.Mr,i=r.ra,o=r.aa,u=i._r,c=i.Er,a=i.Ar,f=i.Tr,l=i.Hr,s=i.pr,v=o.ea,d=o.ta,p=o.na,y=o.va;w("updated",[H,{updateHints:{sizeChanged:!!u,directionChanged:!!c,heightIntrinsicChanged:!!a,overflowEdgeChanged:!!v,overflowAmountChanged:!!d,overflowStyleChanged:!!p,scrollCoordinatesChanged:!!y,contentMutation:!!f,hostMutation:!!l,appear:!!s},changedOptions:t||{},force:!!e}])}),(function(n){return w("scroll",[H,n])})),M=m[0],O=m[1],S=m[2],C=m[3],D=m[4],L=function(n){!function(n){It.delete(n)}(o),_(a),c=!0,w("destroyed",[H,n]),p(),b()},H={options:function(n,r){if(n){var t=r?e():{},i=Ct(s,vn(t,l(n)));pn(i)||(vn(s,i),O({pa:i}))}return vn({},s)},on:x,off:function(n,r){n&&r&&b(n,r)},state:function(){var n=S(),r=n.ga,t=n.ha,e=r.Y,i=t.Fr,o=t.Rr,u=t.V,a=t.ia,f=t.oa,l=t.fa,s=t.Lr;return vn({},{overflowEdge:i,overflowAmount:o,overflowStyle:u,hasOverflow:a,scrollCoordinates:{start:s.D,end:s.M},padding:f,paddingAbsolute:l,directionRTL:e,destroyed:c})},elements:function(){var n=C.ba,r=n.gr,t=n.hr,e=n.oa,i=n.N,o=n.mr,u=n.br,c=n.Qr,a=C.ma,f=a.Xr,l=a.Jr,s=function(n){var r=n.Vr,t=n.jr;return{scrollbar:n.Nr,track:t,handle:r}},v=function(n){var r=n.Zr,t=n.Gr,e=s(r[0]);return vn({},e,{clone:function(){var n=s(t());return O({_a:!0}),n}})};return vn({},{target:r,host:t,padding:e||i,viewport:i,content:o||i,scrollOffsetElement:u,scrollEventElement:c,scrollbarHorizontal:v(f),scrollbarVertical:v(l)})},update:function(n){return O({Mr:n,zr:!0})},destroy:cn(L,!1),plugin:function(n){return f[sn(n)[0]]}};return R(a,[D]),function(n,r){It.set(n,r)}(o,H),mr(gr,Bt,[H,d,f]),Rt(C.ba.yr,!i&&n.cancel)?(L(!0),H):(R(a,M()),w("initialized",[H]),H.update(),H)}return u};return Bt.plugin=function(n){var r=O(n),t=r?n:[n],e=t.map((function(n){return mr(n,Bt)[0]}));return function(n){T(n,(function(n){return T(n,(function(r,t){gr[t]=n[t]}))}))}(t),r?e:e[0]},Bt.valid=function(n){var r=n&&n.elements,t=M(r)&&r();return D(t)&&!!Wt(t.target)},Bt.env=function(){var n=Nt(),r=n.q,t=n.L,e=n.j,i=n.J,o=n.nr,u=n.vr,c=n.rr,a=n.ar,f=n.er,l=n.tr;return vn({},{scrollbarsSize:r,scrollbarsOverlaid:t,scrollbarsHiding:e,scrollTimeline:i,staticDefaultInitialization:o,staticDefaultOptions:u,getDefaultInitialization:c,setDefaultInitialization:a,getDefaultOptions:f,setDefaultOptions:l})},Bt.nonce=function(n){Or=n},Bt.trustedTypePolicy=function(n){k=n},n.ClickScrollPlugin=mt,n.OverlayScrollbars=Bt,n.ScrollbarsHidingPlugin=gt,n.SizeObserverPlugin=pt,n}({});