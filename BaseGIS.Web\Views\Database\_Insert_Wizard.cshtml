﻿@using BaseGIS.Core.Entities
@using System.Web
@using BaseGIS.Web.ViewModels
@model InsertWizardViewModel

@{
    int counter = 1;
}

<link href="~/lib/dropzone/min/dropzone.min.css" rel="stylesheet" />
<link href="~/lib/smartwizard/dist/css/smart_wizard_all.min.css" rel="stylesheet" type="text/css" />
<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />

@Html.AntiForgeryToken()
<div class="card border-danger p-1 samanFont" id="wid-id-43">
    <div class="card-header bg-danger text-white">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title samanFont mb-0">اعتبارسنجی @(Model.Id != null ? "" : " لایه " + Model.Id)</h5>
        </div>
    </div>
    <div class="card-body p-0">
        <div id="wizard">
            <ul class="nav">
                <li class="nav-item"><a class="nav-link" href="#step1" data-toggle="tab">آپلود فایل</a></li>
                <li class="nav-item"><a class="nav-link" href="#step2" data-toggle="tab">تناظر فیلدها</a></li>
                <li class="nav-item"><a class="nav-link" href="#step3" data-toggle="tab">پایان</a></li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane fade @(Model.PathFile == null ? "show active" : "")" id="step1">
                    <h3 class="samanFont">آپلود فایل</h3>
                    <form dir="ltr" action="@Url.Action("_FileUpload", "Database")" class="dropzone" id="mydropzone">
                        @Html.AntiForgeryToken()
                    </form>
                </div>
                <div class="tab-pane fade @(Model.PathFile != null ? "show active" : "")" id="step2">
                    <h3 class="samanFont">تحلیل فایل</h3>
                    <input type="hidden" name="filePath" id="filePath" value="@Model.PathFile" />
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th><i class="fa fa-building"></i> فایل اصلی</th>
                                    <th><i class="fa fa-calendar"></i> معادل در لایه</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.FieldInfos != null)
                                {
                                    foreach (var item in Model.FieldInfos)
                                    {
                                        if (!string.Equals(item.Name, "gcode", StringComparison.OrdinalIgnoreCase) &&
                                            !string.Equals(item.Name, "area", StringComparison.OrdinalIgnoreCase) &&
                                            !string.Equals(item.Name, "length", StringComparison.OrdinalIgnoreCase) &&
                                            !string.Equals(item.Name, "objectid", StringComparison.OrdinalIgnoreCase) &&
                                            !string.Equals(item.Name, "user", StringComparison.OrdinalIgnoreCase) &&
                                            !string.Equals(item.Name, "time", StringComparison.OrdinalIgnoreCase) &&
                                            !string.Equals(item.Name, "usergroup", StringComparison.OrdinalIgnoreCase) &&
                                            !string.Equals(item.Name, "org", StringComparison.OrdinalIgnoreCase) &&
                                            !string.Equals(item.Name, "createtime", StringComparison.OrdinalIgnoreCase) &&
                                            !string.Equals(item.Name, "creatoruser", StringComparison.OrdinalIgnoreCase) &&
                                            !string.Equals(item.Name, "lasttime", StringComparison.OrdinalIgnoreCase) &&
                                            !string.Equals(item.Name, "lastuser", StringComparison.OrdinalIgnoreCase))
                                        {
                                            <tr>
                                                <td>@(counter++)</td>
                                                <td>@Html.Raw(HttpUtility.HtmlEncode(item.AliasName))</td>
                                                <td>
                                                    <select class="form-select select2" id="@item.Name" name="<EMAIL>">
                                                        <option value=""></option>
                                                        @if (Model.ShapeFileColumns != null)
                                                        {
                                                            foreach (string col in Model.ShapeFileColumns)
                                                            {
                                                                if (string.Equals(item.Name, col, StringComparison.OrdinalIgnoreCase))
                                                                {
                                                                    <option value="@col" selected>@col</option>
                                                                }
                                                                else
                                                                {
                                                                    <option value="@col" >@col</option>
                                                                }
                                                            }
                                                        }
                                                    </select>
                                                </td>
                                            </tr>
                                        }
                                    }
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="tab-pane fade" id="step3">
                    <h3 class="saman">برای اتمام دکمه ذخیره را فشار دهید...</h3>
                    <div id="finished"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="~/lib/dropzone/min/dropzone.min.js"></script>
<script src="~/lib/smartwizard/dist/js/jquery.smartWizard.min.js" type="text/javascript"></script>
<script src="~/lib/select2/js/select2.min.js"></script>
<script src="~/lib/select2/js/i18n/fa.js"></script>

<script type="text/javascript">
    Dropzone.autoDiscover = false;

        $(document).ready(function () {            
            initializeWizard();
            initializeDropzone();
            initializeSelect2();
            @if (Model.CountRecords > 0)
            {
                <text>
                showToast('این لایه دارای @Model.CountRecords رکورد اطلاعات می‌باشد', 'danger', 8000, 'top-center');
                </text>
            }
        });

        function initializeSelect2() {
            $('.select2').select2({
                theme: 'bootstrap-5',
                language: 'fa',
                dir: 'rtl',
                placeholder: 'انتخاب کنید',
                allowClear: true,
                width: '100%'
            });
        }

        function initializeWizard() {
            $('#wizard').smartWizard({
                theme: 'arrows',
                lang: {
                    next: 'بعدی',
                    previous: 'قبلی'
                },
                justified: true,
                enableUrlHash: false,
                toolbar: {
                    showNextButton: true,
                    showPreviousButton: true,
                    extraHtml: '<button class="btn btn-success" onclick="publishData()">ذخیره</button>'
                },
                transition: {
                    animation: 'fade',
                    speed: '400'
                },
                anchor: {
                    enableNavigation: true,
                    enableNavigationAlways: false
                }
            });

            @if (!string.IsNullOrEmpty(Model.PathFile))
            {
                <text>
                $('#wizard').smartWizard('goToStep', 1);
                </text>
            }

            $('#wizard').on('leaveStep', function (e, anchorObject, currentStepIndex, nextStepIndex, stepDirection) {
                if (stepDirection === 'forward' && currentStepIndex === 0) {
                    if (!$('#filePath').val()) {
                        showToast('لطفاً ابتدا فایل را آپلود کنید.', 'danger', 4000);
                        return false;
                    }
                }
                return true;
            });
        }



        // تابع مقداردهی اولیه ویزارد بعد از آپلود فایل
        function initializeWizardAfterUpload() {
            // مقداردهی اولیه ویزارد
            $('#wizard').smartWizard({
                theme: 'arrows',
                lang: {
                    next: 'بعدی',
                    previous: 'قبلی'
                },
                justified: true,
                enableUrlHash: false,
                toolbar: {
                    showNextButton: true,
                    showPreviousButton: true,
                    extraHtml: '<button class="btn btn-success" onclick="publishData()">ذخیره</button>'
                },
                transition: {
                    animation: 'fade',
                    speed: '400'
                },
                anchor: {
                    enableNavigation: true,
                    enableNavigationAlways: false
                }
            });

            // مقداردهی اولیه Select2
            $('.select2').select2({
                theme: 'bootstrap-5',
                language: 'fa',
                dir: 'rtl',
                placeholder: 'انتخاب کنید',
                allowClear: true,
                width: '100%'
            });

            // اضافه کردن event handler برای leaveStep
            $('#wizard').on('leaveStep', function (e, anchorObject, currentStepIndex, nextStepIndex, stepDirection) {
                if (stepDirection === 'forward' && currentStepIndex === 0) {
                    if (!$('#filePath').val()) {
                        showToast('لطفاً ابتدا فایل را آپلود کنید.', 'danger', 4000);
                        return false;
                    }
                }
                return true;
            });

            // رفتن به مرحله دوم
            $('#wizard').smartWizard('goToStep', 1);

            // نمایش پیام موفقیت
            showToast('فایل با موفقیت آپلود شد. لطفاً تناظر فیلدها را بررسی کنید.', 'success', 4000);
        }

        function initializeDropzone() {
            new Dropzone('#mydropzone', {
                url: '@Url.Action("_FileUpload", "Database")',
                maxFiles: 1,
                maxFilesize: 100,
                acceptedFiles: '.zip',
                dictDefaultMessage: '<span class="text-center"><span class="font-lg"><i class="fa fa-caret-right text-danger"></i><span class="font-xs"> فایل فشرده جهت آپلود را در اینجا بیندازید</span></span><span><h4 class="display-inline"> (یا کلیک کنید)</h4></span>',
                dictResponseError: 'خطا در آپلود فایل!',
                init: function () {
                    this.on('success', function (file, data) {
                        if (data.success) {
                            // تنظیم مسیر فایل
                            $('#filePath').val(data.responseText);

                            // پس از آپلود موفق، ویزارد را با اطلاعات جدید بارگذاری کن
                            $.ajax({
                                url: '@Url.Action("_Insert_Wizard", "Database")',
                                type: 'GET',
                                data: { id: '@Model.Id', path: data.responseText },
                                success: function (html) {
                                    // جایگزینی کل ویزارد با نسخه جدید
                                    $('#wid-id-43').replaceWith(html);

                                    // مقداردهی اولیه مجدد ویزارد جدید
                                    setTimeout(function() {
                                        initializeWizardAfterUpload();
                                    }, 100);
                                },
                                error: function () {
                                    showToast('خطا در بارگذاری اطلاعات فایل.', 'danger', 4000);
                                }
                            });
                        } else {
                            showToast(data.responseText, 'danger', 4000);
                        }
                    });
                    this.on('sending', function (file, xhr, formData) {
                        formData.append('id', '@Model.Id');
                        formData.append('__RequestVerificationToken', $('input[name="__RequestVerificationToken"]').val());
                    });
                }
            });
        }

        function publishData() {
            var fields1 = [];
            var fields2 = [];
            @if (!string.IsNullOrEmpty(Model.Id) && !string.IsNullOrEmpty(Model.PathFile))
            {
                foreach (var item in Model.FieldInfos)
                {
                    if (!string.Equals(item.Name, "gcode", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "area", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "length", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "objectid", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "user", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "time", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "usergroup", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "org", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "createtime", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "creatoruser", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "lasttime", StringComparison.OrdinalIgnoreCase) &&
                        !string.Equals(item.Name, "lastuser", StringComparison.OrdinalIgnoreCase))
                    {
                        <text>
                        fields1.push('@item.Name');
                        fields2.push($('#@item.Name').val() || '');
                        </text>
                    }
                }
            }
             
            if  (!(fields1.length === 0 && fields2.length === 0) )
            {
                if ( fields1.length === 0 || fields2.length === 0 ) {
                    showToast('لطفاً فیلدها را انتخاب کنید.', 'danger', 4000);
                    return;
                }
            }

            var data = {
                id: '@Model.Id',
                path: '@Model.PathFile',
                FieldSource: fields1.join(','),
                FieldDest: fields2.join(','),
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            };

            $('#finished').html('<h3 class="text-center text-default samanFont"><i class="fa fa-refresh fa-spin"></i></h3>');
            $.ajax({
                url: '@Url.Action("PublishData", "Database")',
                type: 'POST',
                data: data,
                success: function (response) {
                    if (response.success) {
                        showToast(response.responseText, 'danger', 4000);
                        $('#finished').html('<h3 class="text-center text-success samanFont"><i class="fa fa-check"></i> تبریک!<br><small>'+response.responseText+'</small></h3>');
                    } else {
                        showToast(response.responseText, 'danger', 4000);
                        $('#finished').html('<h3 class="text-center text-danger samanFont"><i class="fa fa-check"></i> خطا!<br><small>'+response.responseText+'</small></h3>');
                    }
                },
                error: function (xhr, status, error) {
                    showToast('خطا در ذخیره داده‌ها: ' + error, 'danger', 4000);
                    $('#finished').html('<h3 class="text-center text-danger samanFont"><i class="fa fa-check"></i> خطا!<br><small>' + error + '</small></h3>');
                }
            });
        }
    </script>
