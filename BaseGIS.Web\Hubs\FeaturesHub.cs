﻿using Microsoft.AspNetCore.SignalR;

namespace BaseGIS.Web.Hubs
{
    public class FeaturesHub : Hub
    {
        public async Task NotifyFeatureAdded(int tableId)
        {
            await Clients.All.SendAsync("FeatureAdded", tableId);
        }

        public async Task NotifyFeatureUpdated(int tableId, int featureId)
        {
            await Clients.All.SendAsync("FeatureUpdated", tableId, featureId);
        }

        public async Task NotifyFeatureDeleted(int tableId, int featureId)
        {
            await Clients.All.SendAsync("FeatureDeleted", tableId, featureId);
        }
    }
}