﻿/* تعریف فونت‌ها */

.appFont {
    font-family: 'AppFont' !important;
    font-weight: normal !important;
}

.appFontBold {
    font-family: 'AppFontBold' !important;
    font-weight: normal !important;
}

@font-face {
    font-family: 'AppFont';
    src: url('../fonts/Shabnam-FD.eot');
    src: url('../fonts/Shabnam-FD.woff') format('woff'), url('../fonts/Shabnam-FD.ttf') format('truetype'), url('../fonts/Shabnam-FD.svg#Shabnam-FD') format('svg'), url('../fonts/Shabnam-FD.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'AppFontBold';
    src: url('../fonts/Shabnam-Bold-FD-FD.eot');
    src: url('../fonts/Shabnam-Bold-FD.woff') format('woff'), url('../fonts/Shabnam-Bold-FD.ttf') format('truetype'), url('../fonts/Shabnam-Bold-FD.svg#Shabnam-Bold-FD') format('svg'), url('../fonts/Shabnam-Bold-FD.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
}


@font-face {
    font-family: 'WYekan';
    src: url('../fonts/YekanWeb-Regular.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'FontAwesome';
    src: url("../fonts/fontawesome-webfont.woff2") format('woff2'), url("../fonts/fontawesome-webfont.woff") format("woff"), url("fontawesome-webfont.ttf") format("truetype");
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-Thin.woff2') format('woff2');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-ExtraLight.woff2') format('woff2');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts//Vazirmatn-Light.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-SemiBold.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-ExtraBold.woff2') format('woff2');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-Black.woff2') format('woff2');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "BYekan";
    src: url("../fonts/BYekan.woff") format("woff");
    font-weight: normal;
    font-style: normal;
}

/* متغیرهای پیش‌فرض */
:root {
    --primary-color: #3b5998;            /* آبی عمیق و حرفه‌ای، مناسب برای دکمه‌ها و عناصر اصلی */
    --primary-hover: #2a4373;
    --secondary-color: #ff6f61;          /* مرجانی گرم، برای ایجاد کنتراست و جلب توجه */
    --secondary-hover: #e65a4e;
    --success-color: #38a169;            /* سبز زمردی ملایم برای پیام‌های موفقیت */
    --success-hover: #2e8555;
    --warning-color: #ed8936;            /* نارنجی ملایم و گرم برای هشدارها */
    --warning-hover: #de7f30;
    --danger-color: #e53e3e;             /* قرمز پررنگ اما متعادل برای خطاها */
    --danger-hover: #c82333;
    --shadow-color: rgba(0, 0, 0, 0.15); /* سایه کمی پررنگ‌تر برای عمق بیشتر */
    --accent-color: #ffffff;             /* سفید خالص برای پس‌زمینه‌های کارت و عناصر برجسته */
    --background-color: #f8fafc;         /* خاکستری بسیار روشن با ته‌رنگ سرد برای پس‌زمینه */
    --text-color: #333355;               /* خاکستری تیره مایل به مشکی برای خوانایی بالا */
    --border-color: #e2e8f0;             /* خاکستری سرد و روشن برای حاشیه‌ها */
    --muted-color: #718096;              /* خاکستری متوسط برای متن‌های کم اهمیت یا غیرفعال */
    --highlight-color: #90cdf4;          /* آبی آسمانی برای حالت‌های هاور یا انتخاب */
    --light-color: 248, 249, 250;
}

/* پالت آبی */
.palette-blue {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --accent-color: #ffffff;
    --background-color: #f0f8ff;
    --border-color: #ced4da;
    --text-color: #212529;
    --muted-color: #6c757d;
    --highlight-color: #17a2b8;
}

/* پالت سبز */
.palette-green {
    --primary-color: #28a745;
    --secondary-color: #17a2b8;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --accent-color: #ffffff;
    --background-color: #f1f8e9;
    --border-color: #ced4da;
    --text-color: #212529;
    --muted-color: #6c757d;
    --highlight-color: #17a2b8;
}

/* پالت قرمز */
.palette-red {
    --primary-color: #dc3545;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --accent-color: #ffffff;
    --background-color: #f8d7da;
    --border-color: #ced4da;
    --text-color: #212529;
    --muted-color: #6c757d;
    --highlight-color: #17a2b8;
}

/* پالت بنفش */
.palette-purple {
    --primary-color: #6f42c1;
    --secondary-color: #17a2b8;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --accent-color: #ffffff;
    --background-color: #f3e5f5;
    --border-color: #ced4da;
    --text-color: #212529;
    --muted-color: #6c757d;
    --highlight-color: #17a2b8;
}

/* پالت تیره (حالت شب) */
.palette-dark {
    --primary-color: #343a40;
    --secondary-color: #495057;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --shadow-color: rgba(0, 0, 0, 0.5);
    --accent-color: #343a40;
    --background-color: #212529;
    --border-color: #495057;
    --text-color: #f8f9fa;
    --muted-color: #adb5bd;
    --highlight-color: #6c757d;
}

/* سایز فونت */
.font-small {
    font-size: 0.875rem;
}

.font-medium {
    font-size: 1rem;
}

.font-large {
    font-size: 1.125rem;
}

/* رنگ فونت */
.font-color-dark {
    --text-color: #444466;
}

.font-color-light {
    --text-color: #ffffff;
}


html, body {
    font-family: 'AppFont';
    font-size: 14px;
}
/* استایل کلی بدنه */
body {
    background-color: var(--background-color);
    color: var(--text-color) !important;
    /*line-height: 1.6;*/
    
}
