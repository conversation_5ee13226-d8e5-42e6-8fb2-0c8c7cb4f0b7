using BaseGIS.Core.Entities;
using BaseGIS.Infrastructure.Persistence;
using BaseGIS.Web.Helper;
using BaseGIS.Web.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;

[Authorize]
public class DashboardController : Controller
{
    private readonly ApplicationDbContext _dbContext;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IConfiguration _configuration;
    private readonly IDashboardService _dashboardService;
    private readonly ILogger<DashboardController> _logger;

    public DashboardController(
        ApplicationDbContext dbContext,
        UserManager<ApplicationUser> userManager,
        IConfiguration configuration,
        IDashboardService dashboardService,
        ILogger<DashboardController> logger)
    {
        _dbContext = dbContext;
        _userManager = userManager;
        _configuration = configuration;
        _dashboardService = dashboardService;
        _logger = logger;
    }

    public string GetUserGroup(string userName)
    {
        // Simplified user group logic - you may need to implement this based on your requirements
        var user = _dbContext.Users.FirstOrDefault(a => a.UserName == userName);
        if (user != null)
        {
            // Get user roles and return the first role name or a default
            var roles = _userManager.GetRolesAsync(user).Result;
            return roles.FirstOrDefault() ?? "default";
        }
        return "default";
    }

    public async Task<ActionResult> Index()
    {
        try
        {
            var id = Request.Query["id"].ToString();
            var currentUser = _dbContext.Users.FirstOrDefault(a => a.UserName == User.Identity.Name);

            if (currentUser == null)
            {
                return RedirectToAction("Login", "Account");
            }

            ViewBag.CurrentUser = currentUser;

            var isAdmin = User.IsInRole("Admin") || User.IsInRole("SuperAdmin");

            // Get dashboards using service
            var dashboards = await _dashboardService.GetDashboardsAsync(currentUser.Id, isAdmin);
            var dashboardsList = dashboards.ToList();

            if (string.IsNullOrWhiteSpace(id) && dashboardsList.Any())
                id = dashboardsList.First().ID.ToString();

            int.TryParse(id, out int idInt);

            var dashboard = dashboardsList.FirstOrDefault(x => x.ID == idInt) ??
                           dashboardsList.FirstOrDefault() ??
                           new Dashboard();

            // panels و groupedPanels همیشه لیست باشند
            var panels = _dbContext.DashboardPanels
                .Where(x => x.Dashboard.ID == dashboard.ID)
                .Select(x => new { x.ID, x.Name, x.FilterByField })
                .ToList();

            ViewBag.ID = dashboard.ID.ToString();
            ViewBag.SQL = _dbContext.DashboardSQLs.ToList();
            ViewBag.Panels = panels.ToDictionary(x => x.ID, x => x.Name);
            ViewBag.colors = _dbContext.DashboardColors.ToList();
            ViewBag.isAdmin = isAdmin;
            ViewBag.Dashboards = dashboardsList;
            ViewBag.Dashboard = dashboard;

            var FilterByFields = new List<string>();
            var FilterByFields_Alias = new List<string>();
            var FilterByFields_Suggest = new List<string>();

            for (int i = 0; i < panels.Count; i++)
            {
                if (!string.IsNullOrWhiteSpace(panels[i].FilterByField))
                {
                    var splt = panels[i].FilterByField.Split(',');
                    for (int j = 0; j < splt.Length; j++)
                        if (!string.IsNullOrWhiteSpace(splt[j]))
                        {
                            var ss = splt[j].Split(':');
                            FilterByFields.Add(ss[0].ToLower());
                            FilterByFields_Alias.Add(ss.Length > 1 ? ss[1] : "");
                            FilterByFields_Suggest.Add(ss.Length > 2 ? ss[2] : "");
                        }
                }
            }

            var ff = _dbContext.FieldInfos
                .Include(x => x.TableInfo)
                .Include(x => x.DomainInfos)
                .Where(x => FilterByFields.Contains((x.TableInfo.Name + "." + x.Name).ToLower()))
                .Select(x => new FilterField
                {
                    DisableRule = x.DisableRule,
                    AliasName = x.AliasName,
                    DomainInfos = x.DomainInfos,
                    Editable = x.Editable,
                    FieldLength = x.FieldLength,
                    FieldType = x.FieldType,
                    Id = x.Id,
                    IsDisplay = x.IsDisplay,
                    IsRequired = x.IsRequired,
                    IsUnique = x.IsUnique,
                    Name = x.Name,
                    UnitName = x.UnitName,
                    Updated = x.Updated,
                    ValidationRule = x.ValidationRule,
                    WebService_Period = x.WebService_Period,
                    WebService_URL = x.WebService_URL
                })
                .ToList();

            for (int i = 0; i < ff.Count; i++)
            {
                var f = ff[i];
                for (int j = 0; j < FilterByFields.Count; j++)
                {
                    if (FilterByFields[j] == (f.TableInfo.Name + "." + f.Name).ToLower())
                    {
                        f.Alias = (!string.IsNullOrWhiteSpace(FilterByFields_Alias[j]) ? FilterByFields_Alias[j] : f.AliasName + " (" + f.TableInfo.AliasName + ")");
                        f.Suggestion = FilterByFields_Suggest[j];
                    }
                }
            }
            ViewBag.FilterByFields = ff;

            ViewBag.UsedIDs = new List<int>();

            currentUser.AccessFailedCount = 0;
            _dbContext.SaveChanges();

            string message = string.Empty;
            bool afterLogin = false;
            if (TempData.ContainsKey("AfterLogin"))
                bool.TryParse(TempData["AfterLogin"]?.ToString(), out afterLogin);

            if (afterLogin)
            {
                message = string.Format("<div class='BYekan'>زمان آخرین تلاش: {0}", DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"));
                message += " <br /> ";
                message += "موفق";
                message += " <br /> ";
                message += string.Format("تعداد تلاش ناموفق قبلی: {0}</div>", currentUser.AccessFailedCount);
            }
            ViewBag.Message = message;

            // panels گروه‌بندی شده
            var groupedPanels = _dbContext.DashboardPanels
                .Where(x => x.Dashboard.ID == dashboard.ID)
                .OrderBy(x => x.Group)
                .ThenBy(x => x.Order)
                .GroupBy(x => x.Group)
                .ToList();

            // اگر هیچ پنلی نبود، لیست خالی ارسال شود تا View خطا ندهد
            return View(groupedPanels ?? new List<IGrouping<int?, DashboardPanel>>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading dashboard index for user {UserId}", User.Identity?.Name);
            return View("Error");
        }
    }

    public async Task<ActionResult> GetDashboard(int id)
    {
        try
        {
            var currentUser = _dbContext.Users.FirstOrDefault(a => a.UserName == User.Identity.Name);
            if (currentUser == null)
                return Json(new { ok = false, msg = "کاربر یافت نشد" });

            var isAdmin = User.IsInRole("Admin") || User.IsInRole("SuperAdmin");
            var result = await _dashboardService.GetDashboardAsync(id, currentUser.Id, isAdmin);

            if (result == null)
                return Json(new { ok = false, msg = "داشبورد یافت نشد" });

            return Json(new { ok = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dashboard {DashboardId}", id);
            return Json(new { ok = false, msg = "خطا در بارگذاری داشبورد" });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public ActionResult EditDashboard(IFormCollection frm)
    {
        var ER = "";

        var ID = frm["Dashboard_ID"];
        var Name = frm["Dashboard_Name"];
        var IsPublic = frm["Dashboard_IsPublic"];
        var cssClass = frm["Dashboard_CssClass"];





        if (!string.IsNullOrWhiteSpace(Name))
        {
            var p = _dbContext.Dashboards.Where(x => x.ID.ToString() == ID).FirstOrDefault();
            if (p == null)
                p = new Dashboard();

            p.Name = Name;
            p.IsPublic = IsPublic == "on";
            p.CssClass = cssClass;

            var username = User.Identity.Name;
            p.User = _dbContext.Users.Where(x => x.UserName == username).FirstOrDefault();
            p.Date = DateTime.Now;

            if (p.ID == 0)
                _dbContext.Dashboards.Add(p);
            _dbContext.SaveChanges();

            return Json(new { msg = "با موفقیت ذخیره شد", ok = true, id = p.ID });

        }
        else
            ER = "لطفا تمام موارد را پر نمایید";

        return Json(new { msg = ER, ok = false });

    }



    public ActionResult GetDashboardPanels(int DashboardID, string? PanelID = null, string? input = null, string? input1 = null, string? input2 = null, string? input3 = null, string? input4 = null, string? input5 = null, string? input6 = null, string? input7 = null)
    {
        ApplicationUser currentUser = _dbContext.Users.Where(a => a.UserName == User.Identity.Name).FirstOrDefault();
        var isAdmin = User.IsInRole("Admin") || User.IsInRole("SuperAdmin");

        try
        {
            if (DashboardID == 0)
            {
                // Simplified access control - get first available dashboard for non-admin users
                if (!isAdmin)
                {
                    var firstDashboard = _dbContext.Dashboards.Where(x => x.IsPublic == true).FirstOrDefault();
                    if (firstDashboard != null)
                    {
                        DashboardID = firstDashboard.ID;
                    }
                }
            }
        }
        catch { }


        var Dashboard = _dbContext.Dashboards.Include(x => x.User).Where(x => x.ID == DashboardID).FirstOrDefault();

        if (Dashboard == null)
            return Json(new { msg = "پاسخ نامعتبر می باشد", ok = false });

        if (isAdmin || (Dashboard != null && Dashboard.User != null && Dashboard.User.UserName == User.Identity.Name))
        {
            var inpt = input == null ? "" : input;
            var inpt1 = input1 == null ? "" : input1;
            var inpt2 = input2 == null ? "" : input2;
            var inpt3 = input3 == null ? "" : input3;
            var inpt4 = input4 == null ? "" : input4;
            var inpt5 = input5 == null ? "" : input5;
            var inpt6 = input6 == null ? "" : input6;
            var inpt7 = input7 == null ? "" : input7;

            if (string.IsNullOrWhiteSpace(PanelID))
            {
                var ug = GetUserGroup(User.Identity.Name);
                var Panels = _dbContext.DashboardPanels.Include(x => x.SQL).Where(x => x.Dashboard.ID == DashboardID && x.Panels == null).OrderByDescending(x => x.Group).OrderBy(x => x.Order).ToList().GroupBy(x => x.Group).ToList();
                var p = Panels.Select(x => new { x.Key, Items = x.ToList().Select(y => new PanelView() { InputMapping = y.InputMapping, FilterByField = y.FilterByField, Group = y.Group, HTML = ConvertHtml(User.Identity.Name, y, inpt, inpt1, inpt2, inpt3, inpt4, inpt5, inpt6, inpt7), ID = y.ID, Name = (y.Name != null ? y.Name.Replace("@ID", inpt).Replace("@TITLE", inpt1).Replace("@VALUE1", inpt3).Replace("@VALUE2", inpt4).Replace("@VALUE3", inpt5).Replace("@VALUE4", inpt6).Replace("@VALUE5", inpt7).Replace("@VALUE", inpt2).Replace("@UG", ug) : null), Name2 = (y.Name2 != null ? y.Name2.Replace("@ID", inpt).Replace("@TITLE", inpt1).Replace("@VALUE1", inpt3).Replace("@VALUE2", inpt4).Replace("@VALUE3", inpt5).Replace("@VALUE4", inpt6).Replace("@VALUE5", inpt7).Replace("@VALUE", inpt2).Replace("@UG", ug) : null), Order = y.Order, Padding = y.Padding, Panels = y.Panels, Parameters = y.Parameters, Size = y.Size, SizeH = y.SizeH, SQLID = y.SQL != null ? y.SQL.ID : 0, Type1 = y.Type1, Type2 = y.Type2, BackgroundColor = y.BackgroundColor, HideHeader = y.HideHeader }).ToList() }).OrderBy(x => x.Key).ToList();

                //Set User Parameter
                if (!User.IsInRole("Admin") && !User.IsInRole("SuperAdmin"))
                {
                    var userpanels = _dbContext.DashboardUserParameters.Where(x => x.DashboardID == DashboardID && x.UserId == currentUser.Id).ToList();
                    if (userpanels.Count > 0)
                    {
                        for (int i = 0; i < p.Count; i++)
                        {
                            var cnt = p[i].Items.Count();
                            for (int j = 0; j < p[i].Items.Count; j++)
                            {
                                var pp = userpanels.Where(x => x.PanelID == p[i].Items[j].ID).Select(x => x.Parameters).FirstOrDefault();
                                if (pp != null)
                                    p[i].Items[j].Parameters = pp;
                            }
                        }
                    }
                }
                return Json(new { data = p, ok = true });
            }
            else
            {
                var ug = GetUserGroup(User.Identity.Name);
                var Panels = _dbContext.DashboardPanels.Include(x => x.SQL).Where(x => x.Dashboard.ID == DashboardID && ("," + x.Panels + ",").Contains("," + PanelID + ",")).OrderByDescending(x => x.Group).OrderBy(x => x.Order).ToList().GroupBy(x => x.Group).ToList();
                var p = Panels.Select(x => new { x.Key, Items = x.ToList().Select(y => new PanelView() { InputMapping = y.InputMapping, FilterByField = y.FilterByField, Group = y.Group, HTML = ConvertHtml(User.Identity.Name, y, inpt, inpt1, inpt2, inpt3, inpt4, inpt5, inpt6, inpt7), ID = y.ID, Name = (y.Name != null ? y.Name.Replace("@ID", inpt).Replace("@TITLE", inpt1).Replace("@VALUE1", inpt3).Replace("@VALUE2", inpt4).Replace("@VALUE3", inpt5).Replace("@VALUE4", inpt6).Replace("@VALUE5", inpt7).Replace("@VALUE", inpt2).Replace("@UG", ug) : null), Name2 = (y.Name2 != null ? y.Name2.Replace("@ID", inpt).Replace("@TITLE", inpt1).Replace("@VALUE1", inpt3).Replace("@VALUE2", inpt4).Replace("@VALUE3", inpt5).Replace("@VALUE4", inpt6).Replace("@VALUE5", inpt7).Replace("@VALUE", inpt2).Replace("@UG", ug) : null), Order = y.Order, Padding = y.Padding, Panels = y.Panels, Parameters = y.Parameters, Size = y.Size, SizeH = y.SizeH, SQLID = y.SQL != null ? y.SQL.ID : 0, Type1 = y.Type1, Type2 = y.Type2, BackgroundColor = y.BackgroundColor, HideHeader = y.HideHeader }).ToList() }).OrderBy(x => x.Key).ToList();
                var url = _dbContext.DashboardPanels.Where(x => x.ID.ToString() == PanelID).Select(x => x.URL).FirstOrDefault();
                var msg = _dbContext.DashboardPanels.Where(x => x.ID.ToString() == PanelID).Select(x => new { x.MessagePanel, x.MessageColor }).FirstOrDefault();
                //Set User Parameter
                if (!User.IsInRole("Admin") && !User.IsInRole("SuperAdmin"))
                {
                    var userpanels = _dbContext.DashboardUserParameters.Where(x => x.DashboardID == DashboardID && x.UserId == currentUser.Id).ToList();
                    if (userpanels.Count > 0)
                    {
                        for (int i = 0; i < p.Count; i++)
                        {
                            var cnt = p[i].Items.Count();
                            for (int j = 0; j < p[i].Items.Count; j++)
                            {
                                var pp = userpanels.Where(x => x.PanelID == p[i].Items[j].ID).Select(x => x.Parameters).FirstOrDefault();
                                if (pp != null)
                                    p[i].Items[j].Parameters = pp;
                            }
                        }
                    }
                }

                return Json(new { data = p, url = url, ok = true, msg = msg });
            }

        }
        else
            return Json(new { msg = "پاسخ نامعتبر می باشد", ok = false });

    }


    public ActionResult GetPanel(int id)
    {
        var p = _dbContext.DashboardPanels.Include(x => x.SQL).Where(x => x.ID == id).FirstOrDefault();
        return Json(new { ok = true, data = p });
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public ActionResult EditPanel(IFormCollection frm)
    {
        // Simplified admin check - you may need to implement proper configuration
        var isAdmin = User.IsInRole("Admin") || User.IsInRole("SuperAdmin");
        if (!isAdmin)
        {
            return Json(new { msg = "دسترسی غیرمجاز", ok = false });
        }

        var ER = "";
        var ID = frm["ID"];
        var DashboardID = frm["DashboardID"];
        var Name = frm["Name"];
        var Name2 = frm["Name2"];
        var Type1 = frm["Type1"];
        var Type2 = frm["Type2"];
        var SQL = frm["SQL"];
        var Html = frm["HTML"];
        var Size = frm["Size"];
        var SizeH = frm["Sizeh"];
        var Order = frm["Order"];
        var Group = frm["Group"];
        var Padding = frm["Padding"];
        var Panels = frm["Panels"];
        var URL = frm["URL"];
        var MessagePanel = frm["MessagePanel"];
        var MessageColor = frm["MessageColor"];
        var FilterByField = frm["FilterByField"];
        var InputMapping = frm["InputMapping"];
        var HideHeader = frm["HideHeader"];
        var BackgroundColor = frm["BackgroundColor"];

        // , BackgroundColor = y.BackgroundColor, HideHeader = y.HideHeader
        if (!string.IsNullOrWhiteSpace(DashboardID))
        {
            if (Type1 == "3") { Type2 = "0"; SQL = "0"; }
            if (
                !string.IsNullOrWhiteSpace(Name) &&
                !string.IsNullOrWhiteSpace(Type1) &&
                !string.IsNullOrWhiteSpace(Type2) &&
                !string.IsNullOrWhiteSpace(SQL))
            {
                var p = _dbContext.DashboardPanels.Where(x => x.ID.ToString() == ID).FirstOrDefault();
                if (p == null)
                    p = new DashboardPanel();

                p.Name = Name;
                p.Name2 = Name2;
                p.Type1 = (DashboardType1)int.Parse(Type1);
                p.Type2 = (DashboardType2)int.Parse(Type2);
                p.Dashboard = _dbContext.Dashboards.Where(x => x.ID.ToString() == DashboardID).FirstOrDefault();
                p.Date = DateTime.Now;
                p.HTML = Html;
                p.URL = URL;
                p.MessageColor = MessageColor;
                p.MessagePanel = MessagePanel;
                p.FilterByField = FilterByField;
                p.InputMapping = InputMapping;
                p.HideHeader = HideHeader == "on";
                p.BackgroundColor = BackgroundColor;
                int order = 1;
                int.TryParse(Order, out order);
                p.Order = order;
                int size = 12;
                int.TryParse(Size, out size);
                p.Size = size;
                double sizeh = 1;
                double.TryParse(SizeH, out sizeh);
                p.SizeH = sizeh;
                p.Group = null;
                var group = 0;
                if (int.TryParse(Group, out group))
                    p.Group = group;
                var padding = 0;
                if (int.TryParse(Padding, out padding))
                    p.Padding = padding;


                p.SQL = _dbContext.DashboardSQLs.Where(x => x.ID.ToString() == SQL).FirstOrDefault();
                var username = User.Identity.Name;
                p.User = _dbContext.Users.Where(x => x.UserName == username).FirstOrDefault();
                p.Panels = Panels;
                if (p.ID == 0)
                    _dbContext.DashboardPanels.Add(p);

                _dbContext.DashboardUserParameters.RemoveRange(_dbContext.DashboardUserParameters.Where(x => x.PanelID.ToString() == ID));
                _dbContext.SaveChanges();

                return Json(new { msg = "با موفقیت ذخیره شد", ok = true, id = p.ID });

            }
            else
                ER = "لطفا تمام موارد را پر نمایید";

        }
        else
            ER = "هیچ داشبوردی انتخاب نشده است";


        return Json(new { msg = ER, ok = false });

    }

    public ActionResult DeletePanel(int id)
    {
        var p = _dbContext.DashboardPanels.Where(x => x.ID == id).FirstOrDefault();
        _dbContext.DashboardPanels.Remove(p);
        _dbContext.DashboardUserParameters.RemoveRange(_dbContext.DashboardUserParameters.Where(x => x.PanelID == id));
        _dbContext.SaveChanges();

        return Json(new { ok = true, data = "با موفقیت حذف گردید" });
    }

    public ActionResult ResetPanel(int DashboardID, int PanelID)
    {
        ApplicationUser currentUser = _dbContext.Users.Where(a => a.UserName == User.Identity.Name).FirstOrDefault();
        if (!User.IsInRole("Admin") && !User.IsInRole("SuperAdmin"))
        {
            if (PanelID == 0)
            {
                _dbContext.DashboardUserParameters.RemoveRange(_dbContext.DashboardUserParameters.Where(x => x.DashboardID == DashboardID && x.UserId == currentUser.Id));
                _dbContext.SaveChanges();
            }
            else
            {
                _dbContext.DashboardUserParameters.RemoveRange(_dbContext.DashboardUserParameters.Where(x => x.DashboardID == DashboardID && x.PanelID == PanelID && x.UserId == currentUser.Id));
                _dbContext.SaveChanges();
            }
        }
        return Json(new { ok = true, data = "با موفقیت ریست گردید" });
    }


    public ActionResult GetDataChart(int id, string? type, string? typeTwoBar, int? color, string? colorTitle, bool? showhelp, bool label = false, string grid = "", int? angle = null, int? angleX = null, string? input = null, string? input1 = null, string? input2 = null, string? input3 = null, string? input4 = null, string? input5 = null, string? input6 = null, string? input7 = null, int? sizelabel = null, bool? showdecimal = null, int? TargetColor = null, int? TargetBaseColor = null, bool? TargetShow = null, bool? TargetLabel = null, int? TargetAngle = null, int? TargetSize = null, int? TargetWidth = null, bool? TargetShowDecimal = null, double? TargetHeight = null, string? Where = null)
    {
        ApplicationUser currentUser = _dbContext.Users.Where(a => a.UserName == User.Identity.Name).FirstOrDefault();

        bool validateSQL = SQLExtensions.ValidateSQLWhere(Where ?? string.Empty);
        if (!validateSQL) return null;

        var p = _dbContext.DashboardPanels.Include(x => x.Dashboard).Include(x => x.SQL).Where(x => x.ID == id).FirstOrDefault();
        var Parameters = "";
        if (!string.IsNullOrWhiteSpace(type))
        {

            ChartParameter c = new ChartParameter() { Type = type ?? "line", Check = label, Color = color, ColorTitle = colorTitle ?? "", ShowHelp = showhelp, Grid = grid, Angle = angle, AngleX = angleX, ShowDecimal = showdecimal, SizeLabel = sizelabel, TargetColor = TargetColor, TargetBaseColor = TargetBaseColor, TargetShow = TargetShow, TargetLabel = TargetLabel, TargetAngle = TargetAngle, TargetSize = TargetSize, TargetWidth = TargetWidth, TargetHeight = TargetHeight, TargetShowDecimal = TargetShowDecimal, TypeTwoBar = typeTwoBar ?? "t1" };
            Parameters = Newtonsoft.Json.JsonConvert.SerializeObject(c);
            if (User.IsInRole("Admin") || User.IsInRole("SuperAdmin"))
            {
                p.Parameters = Parameters;
                _dbContext.SaveChanges();

            }
            else
            {
                var up = _dbContext.DashboardUserParameters.Where(x => x.DashboardID == p.Dashboard.ID && x.PanelID == p.ID && x.UserId == currentUser.Id).FirstOrDefault();
                if (up != null)
                    up.Parameters = Parameters;
                else
                    _dbContext.DashboardUserParameters.Add(new DashboardUserParameter { DashboardID = p.Dashboard.ID, PanelID = p.ID, Parameters = Parameters, UserId = currentUser.Id });
                _dbContext.SaveChanges();

            }
        }
        else
        {
            Parameters = p.Parameters;
            if (!User.IsInRole("Admin") && !User.IsInRole("SuperAdmin"))
            {
                var up = _dbContext.DashboardUserParameters.Where(x => x.DashboardID == p.Dashboard.ID && x.PanelID == p.ID && x.UserId == currentUser.Id).FirstOrDefault();
                if (up != null)
                    Parameters = up.Parameters;
            }
        }


        var sql = ConvertSQL(p.SQL.SQL, GetUserGroup(User.Identity.Name), input, input1, input2, input3, input4, input5, input6, input7, Where);

        DBManagement dbm = new DBManagement(_configuration);
        var dt = dbm.SelectTableSQL(sql);
        var items = ConvertDataTabletoString(dt);

        if (p.Type2 != DashboardType2.Attribute)
        {
            type = "line";
            typeTwoBar = "t1";
            label = false;

            if (!string.IsNullOrWhiteSpace(Parameters))
            {
                var c = Newtonsoft.Json.JsonConvert.DeserializeObject<ChartParameter>(Parameters);

                if (!User.IsInRole("Admin") && !User.IsInRole("SuperAdmin"))
                {

                    var up = _dbContext.DashboardUserParameters.Where(x => x.DashboardID == p.Dashboard.ID && x.PanelID == p.ID && x.UserId == currentUser.Id).FirstOrDefault();
                    if (up != null)
                        c = Newtonsoft.Json.JsonConvert.DeserializeObject<ChartParameter>(up.Parameters);
                }
                type = c.Type;
                typeTwoBar = c.TypeTwoBar;
                label = c.Check;
            }


            var isList = p.Type1 == DashboardType1.List;
            var SizeH = p.SizeH == null || p.SizeH < 0.5 ? 1 : p.SizeH;

            return Json(new { ok = true, items = items, type = type, label = label, isList = isList, SizeH = SizeH, typeTwoBar = typeTwoBar });
        }
        return null;
    }

    public ActionResult GetDataGrid(int id, string? input = null, string? input1 = null, string? input2 = null, string? input3 = null, string? input4 = null, string? input5 = null, string? input6 = null, string? input7 = null, string? Where = null, int page = 0)
    {
        bool validateSQL = SQLExtensions.ValidateSQLWhere(Where ?? string.Empty);
        if (!validateSQL) return null;

        var p = _dbContext.DashboardPanels.Include(x => x.SQL).Where(x => x.ID == id).FirstOrDefault();
        var sql = ConvertSQL(p.SQL.SQL, GetUserGroup(User.Identity.Name), input, input1, input2, input3, input4, input5, input6, input7, Where);
        if (sql.Contains("@PageNumber"))
            sql = sql.Replace("@PageNumber", page.ToString()).Replace("@PageRowCount", "50");


        DBManagement dbm = new DBManagement(_configuration);
        var dt = dbm.SelectTableSQL(sql);
        var data = GetTableRows(dt);

        return Json(new { ok = true, data = data });
    }

    public ActionResult GetDataHtml(int id, string? input = null, string? input1 = null, string? input2 = null, string? input3 = null, string? input4 = null, string? input5 = null, string? input6 = null, string? input7 = null, string? Where = null)
    {

        var inpt = input == null ? "" : input;
        var inpt1 = input1 == null ? "" : input1;
        var inpt2 = input2 == null ? "" : input2;
        var inpt3 = input3 == null ? "" : input3;
        var inpt4 = input4 == null ? "" : input4;
        var inpt5 = input5 == null ? "" : input5;
        var inpt6 = input6 == null ? "" : input6;
        var inpt7 = input7 == null ? "" : input7;
        var Panel = _dbContext.DashboardPanels.Include(x => x.SQL).Where(x => x.ID == id).FirstOrDefault();
        if (Panel == null)
            return Json(new { msg = "هیچ پنلی وجود ندارد", ok = false });

        bool validateSQL = SQLExtensions.ValidateSQLWhere(Where ?? string.Empty);
        if (!validateSQL) return null;

        var HTML = ConvertHtml(User.Identity?.Name ?? "Anonymous", Panel, inpt, inpt1, inpt2, inpt3, inpt4, inpt5, inpt6, inpt7, Where);
        return Json(new { data = HTML, ok = true });
    }


    public string ConvertDataTabletoString(DataTable dt)
    {
        List<Dictionary<string, object>> rows = new List<Dictionary<string, object>>();
        Dictionary<string, object> row;
        foreach (DataRow dr in dt.Rows)
        {
            row = new Dictionary<string, object>();
            foreach (DataColumn col in dt.Columns)
            {
                row.Add(col.ColumnName, dr[col]);
            }
            rows.Add(row);
        }
        return System.Text.Json.JsonSerializer.Serialize(rows);
    }
    public static List<Dictionary<string, object>> GetTableRows(System.Data.DataTable dtData)
    {
        List<Dictionary<string, object>>
        lstRows = new List<Dictionary<string, object>>();
        Dictionary<string, object> dictRow = null;

        foreach (System.Data.DataRow dr in dtData.Rows)
        {
            dictRow = new Dictionary<string, object>();
            foreach (System.Data.DataColumn col in dtData.Columns)
            {
                if (col.DataType == typeof(double))
                    dictRow.Add(col.ColumnName, (dr[col] != null && dr[col] != DBNull.Value ? ((double)dr[col]).ToString("0.##") : dr[col]));
                else
                    dictRow.Add(col.ColumnName, dr[col]);
            }
            lstRows.Add(dictRow);
        }
        return lstRows;
    }


    public string ConvertHtml(string username, DashboardPanel panel, string? input = null, string? input1 = null, string? input2 = null, string? input3 = null, string? input4 = null, string? input5 = null, string? input6 = null, string? input7 = null, string? Where = null)
    {
        ApplicationUser currentUser = _dbContext.Users.Where(a => a.UserName == User.Identity.Name).FirstOrDefault();

        var result = "";
        if (!string.IsNullOrWhiteSpace(panel.HTML))
        {
            var ug = GetUserGroup(username);
            if (panel.Type1 == DashboardType1.Map)
            {

                if (!string.IsNullOrWhiteSpace(input))
                    result = panel.HTML.Replace("@ID", input);
                else
                    result = panel.HTML.Replace("@ID", "");

                if (result.Contains("@UG"))
                    result = result.Replace("@UG", ug);

                if (!string.IsNullOrWhiteSpace(input1))
                    result = result.Replace("@TITLE", input1);
                else
                    result = result.Replace("@TITLE", "");


                try { if (!string.IsNullOrEmpty(input2)) { double d = double.Parse(input2); if (d > 0 && d < 1) { input2 = d.ToString("0.##"); } } } catch { }

                result = result.Replace("@VALUE1", !string.IsNullOrWhiteSpace(input3) ? input3 : "");
                result = result.Replace("@VALUE2", !string.IsNullOrWhiteSpace(input4) ? input4 : "");
                result = result.Replace("@VALUE3", !string.IsNullOrWhiteSpace(input5) ? input5 : "");
                result = result.Replace("@VALUE4", !string.IsNullOrWhiteSpace(input6) ? input6 : "");
                result = result.Replace("@VALUE5", !string.IsNullOrWhiteSpace(input7) ? input7 : "");
                result = result.Replace("@VALUE", !string.IsNullOrWhiteSpace(input2) ? input2 : "");




                result = result.Replace("@WHERE", !string.IsNullOrWhiteSpace(Where) ? Where : "");

                if (panel.Type1 == DashboardType1.Map && result.Contains("@TO"))
                {
                    result = result.Replace("@TO", "admin_12345");
                    result = GenerateHash(_dbContext, result, currentUser, HttpContext.Connection.RemoteIpAddress?.ToString());
                }
            }
            else
            {



                var sql = ConvertSQL(panel.SQL.SQL, ug, input, input1, input2, input3, input4, input5, input6, input7, Where);

                DBManagement dbm = new DBManagement(_configuration);
                var dt = dbm.SelectTableSQL(sql);

                var htm = panel.HTML;
                if (htm.Contains("@UG"))
                    htm = htm.Replace("@UG", ug);


                var splt1 = htm.Split(new string[] { "@for", "@endfor" }, StringSplitOptions.None);
                for (int i = 0; i < splt1.Length; i++)
                {
                    for (int j = 0; j < dt.Rows.Count; j++)
                    {
                        var output = splt1[i];
                        for (int k = 0; k < dt.Columns.Count; k++)
                            output = output.Replace("@data" + (k + 1), ConvertToDouble(dt.Rows[j][k]));

                        var iidd = dt.Columns.Contains("id") ? dt.Rows[j]["id"].ToString() : "";
                        var iTitle = dt.Columns.Contains("title") ? dt.Rows[j]["title"].ToString() : "";

                        var v = dt.Columns.Contains("value") ? dt.Rows[j]["value"].ToString() : "";
                        try { double d = double.Parse(v); if (d > 0 && d < 1) { v = d.ToString("0.##"); } } catch { }
                        var iValue = v;// dt.Columns.Contains("value") ? dt.Rows[j]["value"].ToString() : "";
                        var iValue1 = dt.Columns.Contains("value1") ? dt.Rows[j]["value1"].ToString() : "";
                        var iValue2 = dt.Columns.Contains("value2") ? dt.Rows[j]["value2"].ToString() : "";
                        var iValue3 = dt.Columns.Contains("value3") ? dt.Rows[j]["value3"].ToString() : "";
                        var iValue4 = dt.Columns.Contains("value4") ? dt.Rows[j]["value4"].ToString() : "";
                        var iValue5 = dt.Columns.Contains("value5") ? dt.Rows[j]["value5"].ToString() : "";




                        output = output.Replace("@PanelClick", "onclick=\"LoadPanel('" + panel.ID + "', '" + iidd + "', '" + iTitle + "', '" + iValue + "', '" + iValue1 + "', '" + iValue2 + "', '" + iValue3 + "', '" + iValue4 + "', '" + iValue5 + "');setPanelClicked('" + panel.ID + "');\"  style='cursor:pointer;'");
                        result += output;
                        if (i % 2 == 0)
                            break;
                    }
                }


                result = result.Replace(Environment.NewLine, " ");
            }

        }

        return result;
    }
    private static string ConvertToDouble(object val)
    {
        double d = 0;
        int i = 0;
        if (int.TryParse(val.ToString(), out i))
        {
            if (i == 0)
                return "0";
            return i.ToString("###,###,###");
        }
        else if (double.TryParse(val.ToString(), out d))
            return d.ToString("###,###,##0.##");
        else
            return val.ToString();
    }

    private static string GenerateHash(ApplicationDbContext _dbContext, string paras, ApplicationUser usr, string clientIP)
    {
        if (!string.IsNullOrWhiteSpace(paras))
        {
            var MapParameters = paras.Split('&');
            List<string> parameters = new List<string>();
            for (int i = 0; i < MapParameters.Length; i++)
                if (MapParameters[i].Split('=')[0].ToLower() != "token")
                {
                    parameters.Add(MapParameters[i]);
                }


            var token = _dbContext.Tokens.Where(a => a.IP == clientIP && a.IsDynamic && a.User.UserName == usr.UserName).FirstOrDefault();
            if (token == null)
            {

                var expire = DateTime.Now.AddYears(5);
                var tkString = AdvancedToken.GenerateToken(new AdvancedTokenParameter() { UserName = usr.UserName, ExpireDate = expire, IP = clientIP, IsHash = true });
                var tk = new Token() { Expire = expire, User = _dbContext.Users.Where(x => x.Id == usr.Id).FirstOrDefault(), IP = clientIP, IsHash = true, IsDynamic = true, IsRequiredMapRequestID = false };
                tk.TokenString = tkString;
                _dbContext.Tokens.Add(tk);
                _dbContext.SaveChanges();

                token = tk;

            }
            var url = "hash=" + AdvancedHash.GenerateHash(string.Join("&", parameters), token.TokenString, usr.UserName, clientIP);
            return url;
        }
        return null;
    }

    private static string ConvertSQL(string sql, string usergroup, string? input = null, string? input1 = null, string? input2 = null, string? input3 = null, string? input4 = null, string? input5 = null, string? input6 = null, string? input7 = null, string? Where = null)
    {
        if (Where == "undefined")
            Where = null;
        var ug = usergroup; ;
        if (sql.Contains("@UG"))
            sql = sql.Replace("@UG", ug);
        if (!string.IsNullOrWhiteSpace(input))
            sql = sql.Replace("@ID", input.Replace("'", "''"));
        if (!string.IsNullOrWhiteSpace(input1))
            sql = sql.Replace("@TITLE", input1.Replace("'", "''"));
        if (!string.IsNullOrWhiteSpace(input3))
            sql = sql.Replace("@VALUE1", input3.Replace("'", "''"));
        if (!string.IsNullOrWhiteSpace(input4))
            sql = sql.Replace("@VALUE2", input4.Replace("'", "''"));

        if (!string.IsNullOrWhiteSpace(input5))
            sql = sql.Replace("@VALUE3", input5.Replace("'", "''"));
        if (!string.IsNullOrWhiteSpace(input6))
            sql = sql.Replace("@VALUE4", input6.Replace("'", "''"));
        if (!string.IsNullOrWhiteSpace(input7))
            sql = sql.Replace("@VALUE5", input7.Replace("'", "''"));

        if (!string.IsNullOrWhiteSpace(input2))
            sql = sql.Replace("@VALUE", input2.Replace("'", "''"));

        sql = sql.Replace("@WHERE", !string.IsNullOrWhiteSpace(Where) ? Where : "");

        return sql;
    }

    public ActionResult SearchDataField(string term, string field)
    {
        var s = field.Split('.');
        var Table = s[0];
        var Field = s[1];
        var Text = term;
        DBManagement dbm = new DBManagement(_configuration);

        var query = "select distinct top 50  " + Field + " as text," + Field + " as id from " + Table;
        if (!string.IsNullOrWhiteSpace(Text))
            query = "select * from (" + query + " Where " + Field + " like N'%" + Text + "%' ORDER BY  " + Field + " " + ")a ORDER BY CASE WHEN a.text = @Text THEN 0 ELSE 1 END ";
        else
            query += " Where " + Field + " is not NULL and " + Field + "<> ''  ORDER BY  " + Field + " ";

        var table = dbm.SelectTableParametricSQL(query, new Dictionary<string, string>() { { "@Text", "'" + Text + "'" } }).Table;

        var data = Utilities.GetTableRows(table);
        return Json(new { results = data, ok = true });
    }
}


public class ChartParameter
{
    public string Type { get; set; }
    public bool Check { get; set; }
    public int? Color { get; set; }
    public string ColorTitle { get; set; }
    public bool? ShowHelp { get; set; }

    public string Grid { get; set; }
    public int? Angle { get; set; }
    public int? AngleX { get; set; }
    public bool? ShowDecimal { get; set; }
    public int? SizeLabel { get; set; }
    public int? TargetColor { get; set; }
    public int? TargetBaseColor { get; set; }
    public bool? TargetShow { get; set; }
    public bool? TargetLabel { get; set; }
    public int? TargetAngle { get; set; }
    public int? TargetSize { get; set; }
    public int? TargetWidth { get; set; }
    public bool? TargetShowDecimal { get; set; }
    public double? TargetHeight { get; set; }


    public string TypeTwoBar { get; set; }

}

public class PanelView
{
    public int? Group { get; set; }
    public string HTML { get; set; }
    public int ID { get; set; }
    public string Name { get; set; }
    public string Name2 { get; set; }
    public int Order { get; set; }
    public int? Padding { get; set; }
    public string Panels { get; set; }
    public string Parameters { get; set; }
    public int Size { get; set; }
    public double? SizeH { get; set; }
    public int SQLID { get; set; }
    public string FilterByField { get; set; }
    public string InputMapping { get; set; }
    public DashboardType1 Type1 { get; set; }
    public DashboardType2 Type2 { get; set; }

    public bool? HideHeader { get; set; }
    public string BackgroundColor { get; set; }
}

public class FilterField : FieldInfo
{
    public string Suggestion { get; set; }
    public string Alias { get; set; }
    public List<string> SuggestionList
    {
        get
        {
            var lst = new List<string>();
            if (!string.IsNullOrWhiteSpace(Suggestion))
                lst = Suggestion.Split(';').ToList();
            return lst;
        }
    }
}