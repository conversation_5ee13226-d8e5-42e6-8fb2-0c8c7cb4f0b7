!function(o,e){"function"==typeof define&&define.amd?define(["exports","echarts"],e):"object"==typeof exports&&"string"!=typeof exports.nodeName?e(0,require("echarts/lib/echarts")):e(0,o.echarts)}(this,function(o,e){e?e.registerTheme("red",{color:["#d8361b","#f16b4c","#f7b4a9","#d26666","#99311c","#c42703","#d07e75"],title:{textStyle:{fontWeight:"normal",color:"#d8361b"}},visualMap:{color:["#d8361b","#ffd2d2"]},dataRange:{color:["#bd0707","#ffd2d2"]},toolbox:{color:["#d8361b","#d8361b","#d8361b","#d8361b"]},tooltip:{backgroundColor:"rgba(0,0,0,0.5)",axisPointer:{type:"line",lineStyle:{color:"#d8361b",type:"dashed"},crossStyle:{color:"#d8361b"},shadowStyle:{color:"rgba(200,200,200,0.3)"}}},dataZoom:{dataBackgroundColor:"#eee",fillerColor:"rgba(216,54,27,0.2)",handleColor:"#d8361b"},grid:{borderWidth:0},categoryAxis:{axisLine:{lineStyle:{color:"#d8361b"}},splitLine:{lineStyle:{color:["#eee"]}}},valueAxis:{axisLine:{lineStyle:{color:"#d8361b"}},splitArea:{show:!0,areaStyle:{color:["rgba(250,250,250,0.1)","rgba(200,200,200,0.1)"]}},splitLine:{lineStyle:{color:["#eee"]}}},timeline:{lineStyle:{color:"#d8361b"},controlStyle:{color:"#d8361b",borderColor:"#d8361b"}},candlestick:{itemStyle:{color:"#f16b4c",color0:"#f7b4a9"},lineStyle:{width:1,color:"#d8361b",color0:"#d26666"},areaStyle:{color:"#d8361b",color0:"#d07e75"}},graph:{itemStyle:{color:"#d07e75"},linkStyle:{color:"#d8361b"}},chord:{padding:4,itemStyle:{color:"#d07e75",borderWidth:1,borderColor:"rgba(128, 128, 128, 0.5)"},lineStyle:{color:"rgba(128, 128, 128, 0.5)"},areaStyle:{color:"#d8361b"}},map:{itemStyle:{color:"#d8361b"},areaStyle:{color:"#d07e75"},label:{color:"#c12e34"}},gauge:{axisLine:{lineStyle:{color:[[.2,"#f16b4c"],[.8,"#d8361b"],[1,"#99311c"]],width:8}}}}):"undefined"!=typeof console&&console&&console.error&&console.error("ECharts is not Loaded")});