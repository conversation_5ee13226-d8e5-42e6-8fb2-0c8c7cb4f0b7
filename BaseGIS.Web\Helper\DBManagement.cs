﻿using BaseGIS.Core.Entities;
using Microsoft.Data.SqlClient;
using System.Collections;
using System.Data;
using System.Xml;

namespace BaseGIS.Web.Helper
{
    public class DBManagement : IDisposable
    {
        private readonly IConfiguration _configuration;
        private bool _disposed = false;

        public string ConnectionString { get; }

        public DBManagement(IConfiguration configuration)
        {
            _configuration = configuration;
            ConnectionString = configuration.GetConnectionString("DefaultConnection");
        }

        public DBManagement(string _conString)
        {
            ConnectionString = _conString;
        }

        #region Method

        public ArrayList GetFeatureInfo(string table, out string aliasTable)
        {
            aliasTable = "";
            ArrayList aryF = new ArrayList();


            DataTable dt = SelectTable("GDB_Items", "lower(Name) Like '%" + table.ToLower() + "'");
            if (dt.Rows.Count > 0)
            {



                for (int jj = 0; jj < dt.Rows.Count; jj++)
                {
                    string[] nams = dt.Rows[jj]["Name"].ToString().Split('.');
                    if (nams[nams.Length - 1].ToLower().Trim() == table.ToLower())
                    {

                        string xml = dt.Rows[jj]["Definition"].ToString();

                        XmlDocument xmlDoc = new XmlDocument();
                        xmlDoc.LoadXml(xml);

                        if (xmlDoc.ChildNodes[0].Name.ToLower() == "DEFeatureClassInfo".ToLower() || xmlDoc.ChildNodes[0].Name.ToLower() == "DETableInfo".ToLower())//GPCodedValueDomain2")
                        {

                            aliasTable = xmlDoc.ChildNodes[0]["AliasName"].InnerText;

                            if (aliasTable.Trim() == "")
                                aliasTable = table;
                            XmlNodeList gpExs = xmlDoc.GetElementsByTagName("GPFieldInfoExs");
                            if (gpExs.Count > 0)
                            {
                                XmlNodeList gpExs1 = xmlDoc.GetElementsByTagName("GPFieldInfoExs")[0].ChildNodes;
                                //XmlNodeList fields = xmlDoc.GetElementsByTagName("GPFieldInfoExs")[0].ChildNodes;
                                for (int i = 0; i < gpExs1.Count; i++)
                                {
                                    XmlNodeList fields = gpExs1[i].ChildNodes;
                                    Dictionary<string, string> flds = new Dictionary<string, string>();
                                    for (int ii = 0; ii < fields.Count; ii++)
                                        flds.Add(fields[ii].Name, fields[ii].InnerText);
                                    aryF.Add(flds);
                                }
                            }
                            break;
                        }
                        else
                        {
                        }
                    }
                }
            }
            return aryF;
        }
        public Dictionary<string, string> GetValueInfo(string FieldValue, ArrayList aryFields)
        {
            for (int i = 0; i < aryFields.Count; i++)
            {
                if (((Dictionary<string, string>)aryFields[i])["Name"] == FieldValue)
                    return (Dictionary<string, string>)aryFields[i];
            }
            return null;
        }
        public Dictionary<string, string[]> GetDomains(ArrayList aryFieldInfo)
        {
            // Dictionary<string, string> FDomainName = new Dictionary<string, string>();
            ArrayList FDomainK = new ArrayList();
            ArrayList FDomainN = new ArrayList();

            Dictionary<string, string[]> Domains = new Dictionary<string, string[]>();
            string where = "";
            for (int i = 0; i < aryFieldInfo.Count; i++)
                if (((Dictionary<string, string>)aryFieldInfo[i]).ContainsKey("DomainName"))
                {

                    // if (!FDomainName.Keys.Contains(((Dictionary<string, string>)aryFieldInfo[i])["DomainName"]))
                    {
                        where += "lower(Name) = '" + ((Dictionary<string, string>)aryFieldInfo[i])["DomainName"].ToLower() + "' OR ";
                        //FDomainName.Add(((Dictionary<string, string>)aryFieldInfo[i])["DomainName"], ((Dictionary<string, string>)aryFieldInfo[i])["Name"]);
                        FDomainK.Add(((Dictionary<string, string>)aryFieldInfo[i])["DomainName"]);
                        FDomainN.Add(((Dictionary<string, string>)aryFieldInfo[i])["Name"]);

                    }
                }

            if (where.Length > 0)
            {
                where = where.Substring(0, where.Length - 3);
                DataTable dt = SelectTable("GDB_Items", where);
                for (int j = 0; j < dt.Rows.Count; j++)
                {
                    string xml = dt.Rows[j]["Definition"].ToString();
                    XmlDocument xmlDoc = new XmlDocument();
                    xmlDoc.LoadXml(xml);

                    XmlNodeList gpExs = xmlDoc.GetElementsByTagName("CodedValues");
                    if (gpExs.Count > 0)
                    {
                        XmlNodeList gpExs1 = xmlDoc.GetElementsByTagName("CodedValues")[0].ChildNodes;
                        //XmlNodeList fields = xmlDoc.GetElementsByTagName("GPFieldInfoExs")[0].ChildNodes;
                        string[] dmns = new string[gpExs1.Count];
                        for (int i = 0; i < gpExs1.Count; i++)
                        {
                            XmlNodeList fields = gpExs1[i].ChildNodes;
                            dmns[i] = fields[0].InnerText;
                        }
                        for (int i = 0; i < FDomainK.Count; i++)
                        {
                            if (dt.Rows[j]["Name"].ToString() == FDomainK[i].ToString())
                            {
                                // Domains.Add(FDomainName[dt.Rows[j]["Name"].ToString()].ToString(), dmns);
                                Domains.Add(FDomainN[i].ToString(), dmns);
                            }
                        }

                    }

                }
            }

            return Domains;

        }

        public DataTable SelectTableSQL(string SQLSatatment)
        {
            string query = SQLSatatment;
            DataTable dt = new DataTable();

            using (SqlConnection con = new SqlConnection(ConnectionString))
            using (SqlCommand command = new SqlCommand(query, con))
            using (SqlDataAdapter adapter = new SqlDataAdapter(command))
            {
                try
                {
                    con.Open();
                    adapter.Fill(dt);
                }
                catch (Exception ex)
                {
                    // Log error if needed
                    throw;
                }
            }
            return dt;
        }
        public DataTable SelectTableSQL(string SQLSatatment, int startRecord, int maxRecords)
        {
            string query = SQLSatatment;
            DataTable dt = new DataTable();

            using (SqlConnection con = new SqlConnection(ConnectionString))
            using (SqlCommand command = new SqlCommand(query, con))
            using (SqlDataAdapter adapter = new SqlDataAdapter(command))
            {
                try
                {
                    con.Open();
                    adapter.Fill(startRecord, maxRecords, dt);
                }
                catch (Exception ex)
                {
                    // Log error if needed
                    throw;
                }
            }
            return dt;
        }
        public DataTable SelectTableSQL(string SQLSatatment, string time, out string msgTxt)
        {
            if (string.IsNullOrEmpty(time))
                time = "300";

            var intTime = int.Parse(time);
            if (intTime > 10000)
                intTime = 10000;

            msgTxt = "Successfull";
            string query = SQLSatatment;
            DataTable dt = new DataTable();

            using (SqlConnection con = new SqlConnection(ConnectionString))
            using (SqlCommand command = new SqlCommand(query, con))
            using (SqlDataAdapter adapter = new SqlDataAdapter(command))
            {
                try
                {
                    command.CommandTimeout = intTime;
                    con.Open();
                    adapter.Fill(dt);
                }
                catch (Exception msg)
                {
                    if (msg.Message.StartsWith("Timeout expired."))
                        msgTxt = "زمان مورد نیاز برای جستجوی مدنظر بیش از حد مجاز می باشد و قابل اجرا نمی باشد";
                    else
                        msgTxt = msg.Message;
                }
            }
            return dt;
        }

        public DataTable SelectTableSQL(string query, SqlParameter[] parameters)
        {
            DataTable dt = new DataTable();
            using (SqlConnection con = new SqlConnection(ConnectionString))
            using (SqlCommand command = new SqlCommand(query, con))
            using (SqlDataAdapter adapter = new SqlDataAdapter(command))
            {
                try
                {
                    command.Parameters.AddRange(parameters);
                    con.Open();
                    adapter.Fill(dt);
                }
                catch (Exception ex)
                {
                    throw;
                }
            }
            return dt;
        }

        public DataTable SelectTable(string table)
        {
            string msgTxt = "ok,Read";
            string query = "select * From " + table;


            SqlConnection con = new SqlConnection(ConnectionString);
            SqlCommand command = new SqlCommand(query, con);
            SqlDataAdapter adapter = new SqlDataAdapter(command);
            DataTable dt = new DataTable();
            try
            {
                con.Open();
                adapter.Fill(dt);

            }
            catch (Exception msg)
            {
                msgTxt = msg.Message;
            }
            finally
            {
                con.Close();
            }
            return dt;
        }
        public DataTable SelectTable(string table, string whereCluses)
        {
            string msgTxt = "ok,Read";
            string query = "select * From " + table;
            if (whereCluses.Trim() != "")
                query += " Where " + whereCluses;

            SqlConnection con = new SqlConnection(ConnectionString);
            SqlCommand command = new SqlCommand(query, con);
            SqlDataAdapter adapter = new SqlDataAdapter(command);
            DataTable dt = new DataTable();
            try
            {
                con.Open();
                adapter.Fill(dt);
            }
            catch (Exception msg)
            {
                msgTxt = msg.Message;
            }
            finally
            {
                con.Close();
            }
            return dt;
        }

        public DataTable SelectTableUniqueColumn(string table, string Column)
        {
            string msgTxt = "ok,Read";
            //string query = "select * From " + table;
            string query = " SELECT DISTINCT " + Column + " FROM " + table;

            SqlConnection con = new SqlConnection(ConnectionString);
            SqlCommand command = new SqlCommand(query, con);
            SqlDataAdapter adapter = new SqlDataAdapter(command);
            DataTable dt = new DataTable();
            try
            {
                con.Open();
                adapter.Fill(dt);


            }
            catch (Exception msg)
            {
                msgTxt = msg.Message;
            }
            finally
            {
                con.Close();
            }
            return dt;
        }
        private int GetRowCount(string tableName, string orderByColumn, string searchExpression)
        {

            string sql = "SELECT        COUNT(*) AS A FROM            {0} {1} ";
            string whereClause = String.IsNullOrEmpty(searchExpression) ? String.Empty : " WHERE " + searchExpression;

            sql = String.Format(sql, tableName, whereClause);

            DataTable dtResult = SelectTableSQL(sql);

            int count = 0;

            if (dtResult.Rows.Count > 0)
                if (dtResult.Columns.Count > 0)
                    count = System.Convert.ToInt32(dtResult.Rows[0][0].ToString());


            return count;
        }
        public int SelectMax(string table, string filed)
        {
            string msgTxt = "";
            int expr = -1;
            string query = "select Max(" + filed + ") From " + table;


            SqlConnection con = new SqlConnection(ConnectionString);
            SqlCommand command = new SqlCommand(query, con);
            SqlDataAdapter adapter = new SqlDataAdapter(command);
            DataTable dt = new DataTable();
            try
            {
                con.Open();
                adapter.Fill(dt);


            }
            catch (Exception msg)
            {
                msgTxt = msg.Message;
            }
            finally
            {
                con.Close();
            }

            if (dt.Rows.Count > 0)
                if (dt.Rows[0][0].ToString() == "")
                    expr = 0;
                else
                    expr = System.Convert.ToInt32(dt.Rows[0][0].ToString());

            return expr;
        }
        public bool ExcuteSQL(string cmdText, out string msgTxt)
        {
            bool ok = false;
            msgTxt = "با موفقیت انجام شد";
            SqlConnection con = new SqlConnection(ConnectionString);
            SqlCommand commandAdd = new SqlCommand(cmdText, con);
            try
            {
                con.Open();
                commandAdd.ExecuteNonQuery();
                ok = true;
            }
            catch (Exception msg)
            {
                msgTxt = msg.Message;
            }
            finally
            {
                con.Close();
            }
            return ok;
        }
        public object ExecuteScalar(string cmdText, out string msgText)
        {
            object value = null;
            msgText = "با موفقیت انجام شد";
            SqlConnection con = new SqlConnection(ConnectionString);
            SqlCommand commandAdd = new SqlCommand(cmdText, con);
            try
            {
                con.Open();
                value = commandAdd.ExecuteScalar();
            }
            catch (Exception msg)
            {
                msgText = msg.Message;
            }
            finally
            {
                con.Close();
            }
            return value;
        }
        public object ExecuteScalar(string SQLSatatment, CommandType commandType)
        {
            object value = null;

            string query = SQLSatatment;

            SqlConnection con = new SqlConnection(ConnectionString);
            SqlCommand command = new SqlCommand(query, con);
            command.CommandType = commandType;

            try
            {
                con.Open();
                value = command.ExecuteScalar();
            }
            catch (Exception ex)
            {
            }
            finally
            {
                con.Close();
            }
            return value;
        }

        public bool ExistRecord(string table, string whereCluses)
        {
            bool exist = false;
            string query = "select 1 From " + table;
            if (whereCluses.Trim() != "")
                query += " Where " + whereCluses;

            SqlConnection con = new SqlConnection(ConnectionString);
            SqlCommand command = new SqlCommand(query, con);
            SqlDataAdapter adapter = new SqlDataAdapter(command);
            DataTable dt = new DataTable();
            try
            {
                con.Open();
                adapter.Fill(dt);

                if (dt.Rows.Count > 0)
                    exist = true;

            }
            catch (Exception msg)
            {
                return exist;
            }
            finally
            {
                con.Close();
            }
            return exist;
        }


        public string InsertData(string table, string oid, ArrayList Fields, ArrayList Values)
        {
            string msgTxt = "با موفقیت ذخیره شد";
            string queryAdd = "insert into  " + table + " ( " + oid + ",";
            for (int i = 0; i < Fields.Count - 1; i++)
                queryAdd += Fields[i].ToString() + ",";
            queryAdd += Fields[Fields.Count - 1].ToString() + ") values(  (SELECT ISNULL(MAX(" + oid + ")+1,0) FROM " + table + "), ";
            for (int i = 0; i < Values.Count - 1; i++)
                if (Values[i].ToString().Split(':')[0] == "geometry")
                    queryAdd += Values[i].ToString() + " ,";
                else
                    queryAdd += "N'" + Values[i].ToString() + "' ,";

            if (Values[Values.Count - 1].ToString().Split(':')[0] == "geometry")
                queryAdd += Values[Values.Count - 1].ToString() + ")";
            else
                queryAdd += "N'" + Values[Values.Count - 1].ToString() + "')";


            //queryAdd += "N'" + Values[Values.Count - 1].ToString() + "')";



            //ArrayList arry = new ArrayList();
            SqlConnection con = new SqlConnection(ConnectionString);
            // SqlCommand commandRead = new SqlCommand(queryRead, con);
            SqlCommand commandAdd = new SqlCommand(queryAdd, con);
            //SqlDataAdapter adapter = new SqlDataAdapter(command);
            try
            {
                con.Open();
                commandAdd.ExecuteNonQuery();

            }
            catch (Exception msg)
            {
                msgTxt = msg.Message;// "ثبت نگردید    ";//+ msg;//msg.Message;//
            }
            finally
            {
                con.Close();
            }
            return msgTxt;


        }
        public string InsertDataImage(string table, string oid, ArrayList Fields, ArrayList Values)
        {
            string msgTxt = "Inserted Image";

            string queryAdd = "insert into  " + table + " ( " + oid + ",";
            for (int i = 0; i < Fields.Count - 1; i++)
                queryAdd += Fields[i].ToString() + ",";
            queryAdd += Fields[Fields.Count - 1].ToString() + ") values(  (SELECT ISNULL(MAX(" + oid + ")+1,0) FROM " + table + "), ";


            for (int i = 0; i < Values.Count - 1; i++)
                queryAdd += "@" + Fields[i].ToString() + " ,";
            queryAdd += "@" + Fields[Fields.Count - 1].ToString() + " )";



            SqlConnection con = new SqlConnection(ConnectionString);

            try
            {
                using (SqlCommand cmd = new SqlCommand(queryAdd, con))
                {
                    for (int i = 0; i < Values.Count; i++)
                    {
                        if (Values[i].ToString().Split(':')[0] == "geometry")
                            cmd.Parameters.Add("@" + Fields[i].ToString(), SqlDbType.VarBinary).Value = Values[i].ToString();
                        if (Values[i].GetType() == typeof(byte[]))
                            cmd.Parameters.Add("@" + Fields[i].ToString(), SqlDbType.VarBinary).Value = (byte[])Values[i];
                        else
                            cmd.Parameters.Add("@" + Fields[i].ToString(), SqlDbType.NVarChar).Value = Values[i].ToString();


                    }

                    con.Open();
                    cmd.ExecuteNonQuery();
                }


            }
            catch (Exception msg)
            {
                msgTxt = msg.Message;// "ثبت نگردید    ";//+ msg;//msg.Message;//
            }
            finally
            {
                con.Close();
            }
            return msgTxt;





        }

        public bool UpdateData(string table, ArrayList Fields, ArrayList Values, string where, out string msgTxt)
        {
            bool ok = false;
            msgTxt = "با موفقیت ویرایش شد";
            string queryAdd = "UPDATE  " + table + " SET ";
            for (int i = 0; i < Fields.Count; i++)
            {


                queryAdd += Fields[i].ToString() + " = N'" + Values[i].ToString() + "' , ";
            }
            queryAdd = queryAdd.Substring(0, queryAdd.Length - 2);
            queryAdd += "WHERE " + where;


            //ArrayList arry = new ArrayList();
            SqlConnection con = new SqlConnection(ConnectionString);
            // SqlCommand commandRead = new SqlCommand(queryRead, con);
            SqlCommand commandAdd = new SqlCommand(queryAdd, con);
            //SqlDataAdapter adapter = new SqlDataAdapter(command);
            try
            {
                con.Open();
                commandAdd.ExecuteNonQuery();
                ok = true;
            }
            catch (Exception msg)
            {
                msgTxt = msg.Message;
            }
            finally
            {
                con.Close();
            }
            return ok;


        }
        public string UpdateData(string table, DataRow row, string where)
        {
            string msgTxt = "ویرایش با موفقیت صورت پذیرفت";
            string queryAdd = "UPDATE  " + table + " SET ";
            for (int i = 1; i < row.Table.Columns.Count; i++)
            {
                if (row.Table.Columns[i].ToString().Split(':')[0] == "geometry")
                    queryAdd += row.Table.Columns[i].ToString() + " =  " + row[i].ToString() + " , ";
                else

                    queryAdd += row.Table.Columns[i].ToString() + " = N'" + row[i].ToString() + "' , ";


            }
            queryAdd = queryAdd.Substring(0, queryAdd.Length - 2);
            queryAdd += "WHERE " + where;


            //ArrayList arry = new ArrayList();
            SqlConnection con = new SqlConnection(ConnectionString);
            // SqlCommand commandRead = new SqlCommand(queryRead, con);
            SqlCommand commandAdd = new SqlCommand(queryAdd, con);
            //SqlDataAdapter adapter = new SqlDataAdapter(command);
            try
            {
                con.Open();
                commandAdd.ExecuteNonQuery();

            }
            catch (Exception msg)
            {
                msgTxt = msg.Message;
            }
            finally
            {
                con.Close();
            }
            return msgTxt;


        }

        public bool DeleteData(string table, string where, out string msg)
        {
            bool ok = true;
            msg = "با موفقیت حذف صورت پذیرفت";

            string querydel = "DELETE FROM " + table + " WHERE " + where;

            //ArrayList arry = new ArrayList();
            SqlConnection con = new SqlConnection(ConnectionString);
            // SqlCommand commandRead = new SqlCommand(queryRead, con);
            SqlCommand commandAdd = new SqlCommand(querydel, con);
            //SqlDataAdapter adapter = new SqlDataAdapter(command);
            try
            {
                con.Open();
                commandAdd.ExecuteNonQuery();
            }
            catch (Exception msg1)
            {
                msg = msg1.Message;
                ok = false;
            }
            finally
            {
                con.Close();
            }

            return ok;
        }
        public string InsertOrUpdateData(string table, string oid, ArrayList Fields, ArrayList Values, string where)
        {
            string msgTxt = "با موفقیت ذخیره شد";
            string queryAdd = "insert into  " + table + " ( " + oid + ",";
            for (int i = 0; i < Fields.Count - 1; i++)
                queryAdd += Fields[i].ToString() + ",";
            queryAdd += Fields[Fields.Count - 1].ToString() + ") values(  (SELECT ISNULL(MAX(" + oid + ")+1,0) FROM " + table + "), ";
            for (int i = 0; i < Values.Count - 1; i++)
                if (Values[i].ToString().Split(':')[0] == "geometry")
                    queryAdd += Values[i].ToString() + " ,";
                else
                    queryAdd += "N'" + Values[i].ToString() + "' ,";

            if (Values[Values.Count - 1].ToString().Split(':')[0] == "geometry")
                queryAdd += Values[Values.Count - 1].ToString() + ")";
            else
                queryAdd += "N'" + Values[Values.Count - 1].ToString() + "')";


            //queryAdd += "N'" + Values[Values.Count - 1].ToString() + "')";

            queryAdd += " ON DUPLICATE KEY UPDATE " + where;

            //ArrayList arry = new ArrayList();
            SqlConnection con = new SqlConnection(ConnectionString);
            // SqlCommand commandRead = new SqlCommand(queryRead, con);
            SqlCommand commandAdd = new SqlCommand(queryAdd, con);
            //SqlDataAdapter adapter = new SqlDataAdapter(command);
            try
            {
                con.Open();
                commandAdd.ExecuteNonQuery();

            }
            catch (Exception msg)
            {
                msgTxt = msg.Message;// "ثبت نگردید    ";//+ msg;//msg.Message;//
            }
            finally
            {
                con.Close();
            }
            return msgTxt;


        }

        public int InsertDocumentContent(string contentType, byte[] content, string fCode_OwnPlan)
        {
            int result = 0;

            string cmdText = "insert into DocumentContent (ContentType,DocContent,FCode_OwnPlan) values(@ContentType,@DocContent, @FCode_OwnPlan); SET @Id = SCOPE_IDENTITY(); ";

            SqlConnection con = new SqlConnection(ConnectionString);
            SqlCommand commandAdd = new SqlCommand(cmdText, con);

            commandAdd.Parameters.Add("@ContentType", SqlDbType.NVarChar).Value = contentType;
            commandAdd.Parameters.Add("@DocContent", SqlDbType.VarBinary).Value = (byte[])content;
            commandAdd.Parameters.Add("@FCode_OwnPlan", SqlDbType.NVarChar).Value = fCode_OwnPlan;

            commandAdd.Parameters.Add("@Id", SqlDbType.Int, 0, "Id").Direction = ParameterDirection.Output;
            commandAdd.UpdatedRowSource = UpdateRowSource.OutputParameters;

            try
            {
                con.Open();
                object val = commandAdd.ExecuteNonQuery();
                result = Convert.ToInt32(commandAdd.Parameters["@Id"].Value);
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                con.Close();
            }
            return result;
        }

        public DataRow GetEntity(string tableName, int ObjectID)
        {
            try
            {
                string query = string.Format("SELECT Top(1) * FROM {0} Where ObjectID = {1}", tableName, ObjectID);

                DataTable dt = SelectTableSQL(query);
                return dt.Rows[0];
            }
            catch (Exception)
            {
                throw;
            }
        }
        public DataRow GetEntityByCode(string tableName, string gCode)
        {
            try
            {
                string query = string.Format("SELECT Top(1) * FROM {0} Where GCode = '{1}'", tableName, gCode);

                DataTable dt = SelectTableSQL(query);
                return dt.Rows[0];
            }
            catch (Exception)
            {
                throw;
            }
        }
        public void InsertEntity(ApplicationUser user, string tableName, List<FieldInfo> listFields, Dictionary<string, object> dictRow, string org, string strFeature, ref string outGCode)
        {
            string time = Convertion.ConvertDateTimeToPersianLong(DateTime.Now);
            TableInfo tableInfo = listFields[0].TableInfo as TableInfo;
            List<string> exceptedList = new List<string>()
            { "OBJECTID", "GCODE", "LASTTIME", "LASTUSER", "CREATETIME", "CREATORUSER", "ORG" };

            List<string> fields = new List<string>();

            List<string> insertParams = new List<string>();
            foreach (KeyValuePair<string, object> pair in dictRow)
            {
                if (!exceptedList.Contains(pair.Key.ToUpper()))
                {
                    fields.Add(pair.Key);
                    insertParams.Add("@" + pair.Key);
                }
            }

            insertParams.Add("@GCode");
            insertParams.Add("@CreateTime");
            insertParams.Add("@CreatorUser");
            insertParams.Add("@LastTime");
            insertParams.Add("@LastUser");
            insertParams.Add("@Org");

            using (System.Data.SqlClient.SqlConnection con = new System.Data.SqlClient.SqlConnection(ConnectionString))
            {
                con.Open();
                var tran = con.BeginTransaction();
                try
                {
                    using (System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand("", con, tran))
                    {
                        string strFields = string.Join(",", fields) + ", GCode, CreateTime, CreatorUser, LastTime, LastUser, Org";
                        if (!string.IsNullOrEmpty(strFeature))
                        {
                            strFields += ", Shape";
                        }

                        string strFieldValues = string.Join(",", insertParams);
                        if (!string.IsNullOrEmpty(strFeature))
                        {
                            strFieldValues += string.Format(", geometry::STGeomFromText('{0}', 0)", strFeature);
                        }
                        cmd.CommandText = string.Format("INSERT INTO {0} ({1}) Values ({2})", tableName, strFields, strFieldValues);

                        for (int i = 0; i < fields.Count; i++)
                        {
                            cmd.Parameters.AddWithValue("@" + fields[i], dictRow[fields[i]]);
                        }
                        if (string.IsNullOrEmpty(outGCode))
                        {
                            outGCode = GenerateGCode(tableInfo);
                        }

                        cmd.Parameters.AddWithValue("@GCode", outGCode);
                        cmd.Parameters.AddWithValue("@CreateTime", time);
                        cmd.Parameters.AddWithValue("@CreatorUser", user.FullName);

                        cmd.Parameters.AddWithValue("@LastTime", time);
                        cmd.Parameters.AddWithValue("@LastUser", user.FullName);

                        cmd.Parameters.AddWithValue("@Org", org);

                        cmd.ExecuteNonQuery();
                    }
                    tran.Commit();
                }
                catch (Exception e)
                {
                    tran.Rollback();
                    outGCode = string.Empty;
                    throw e;
                }
            }
        }

        public void UpdateEntity(ApplicationUser user, string tableName, List<FieldInfo> listFields, Dictionary<string, object> dictRow, string org, string strFeature = "")
        {
            string time = Convertion.ConvertDateTimeToPersianLong(DateTime.Now);

            List<string> exceptedList = new List<string>()
            { "OBJECTID", "GCODE",  "CREATETIME", "CREATORUSER" };

            List<string> fields = new List<string>();

            List<string> updateParams = new List<string>();
            foreach (KeyValuePair<string, object> pair in dictRow)
            {
                if (!exceptedList.Contains(pair.Key.ToUpper()))
                {
                    fields.Add(pair.Key);
                    updateParams.Add(pair.Key + "=@" + pair.Key);
                }
            }

            updateParams.Add("LastTime=@LastTime");
            updateParams.Add("LastUser=@LastUser");
            updateParams.Add("Org=@Org");

            using (System.Data.SqlClient.SqlConnection con = new System.Data.SqlClient.SqlConnection(ConnectionString))
            {
                con.Open();
                var tran = con.BeginTransaction();
                try
                {
                    using (System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand("", con, tran))
                    {
                        if (!string.IsNullOrEmpty(strFeature))
                        {
                            updateParams.Add(string.Format("[Shape] = geometry::STGeomFromText('{0}', 0)", strFeature));
                        }
                        cmd.CommandText = string.Format("Update {0} Set {1} Where GCode = '{2}' ", tableName, string.Join(",", updateParams), dictRow["GCode"]);

                        for (int i = 0; i < fields.Count; i++)
                        {
                            var paramValue = dictRow[fields[i]] ?? (object)DBNull.Value;
                            cmd.Parameters.AddWithValue("@" + fields[i], paramValue);
                        }

                        cmd.Parameters.AddWithValue("@LastTime", time);
                        cmd.Parameters.AddWithValue("@LastUser", user.FullName);
                        //cmd.Parameters.AddWithValue("@UserGroup", (user != null && user.UserGroup != null) ? user.UserGroup.Name.ToString() : "");
                        cmd.Parameters.AddWithValue("@Org", org);

                        cmd.ExecuteNonQuery();
                    }
                    tran.Commit();

                }
                catch (Exception e)
                {
                    tran.Rollback();
                    throw e;
                }
            }
        }

        public string GetAppSetting(string key)
        {
            string query = string.Format("Select Config From dbo.AppSettings Where Name = '{0}'", key);
            string msgTxt = string.Empty;
            string appValue = ExecuteScalar(query, out msgTxt).ToString();
            return appValue;
        }

        public string GenerateGCode(TableInfo tableInfo)
        {
            string code = "";
            int result = GetLastIdForCode(tableInfo.Name);

            string coCode = GetAppSetting("CoCode");
            string companyCode = string.IsNullOrEmpty(coCode) ? "GW" : coCode;
            string abbCode = "Zz";

            abbCode = tableInfo.ShortName;

            code = string.Format("{0}{1}{2}", companyCode, abbCode, result.ToString("0000000"));

            return code;
        }

        public int GetLastIdForCode(string tableName)
        {
            int result = 1;
            try
            {
                //string query = string.Format("SELECT Max(Code) FROM {0} ", tableName);
                string query = string.Format("SELECT Top(1) GCode FROM {0} Order by right(GCode, 7) Desc", tableName);

                DataTable dt = SelectTableSQL(query);
                if (dt.Rows.Count != 0)
                {
                    string lastCode = dt.Rows[0][0].ToString();
                    Int32.TryParse(lastCode.Remove(0, 5), out result);
                    result++;
                }
            }
            catch (Exception ex)
            {
                //ExceptionUtils.LogException(ex, "GetUniqueCode");
            }

            return result;
        }

        public string GenerateDependentCode(string ownPlanCode, string tableName, string fieldName, string prefix)
        {
            string code = "";

            Int32 result = 1;
            try
            {
                //string query = string.Format("SELECT IDENT_CURRENT ('dbo.{0}') AS Current_Identity;", tableName);
                string query = string.Format("SELECT Top(1) {0} FROM {1} Where FCode_OwnPlan = '{2}' AND left({0}, 4) = '{3}' Order by Cast(SUBSTRING({0}, 5, LEN({0}) - 4) AS int) Desc",
                    fieldName, tableName, ownPlanCode, prefix);
                DataTable dt = SelectTableSQL(query);
                if (dt.Rows.Count != 0)
                {
                    string lastCode = dt.Rows[0][0].ToString();
                    Int32.TryParse(lastCode.Remove(0, 4), out result);
                    result++;

                    if (result == 1)
                    {
                        throw new Exception("خطا در تولید کد!");
                    }
                }
            }
            catch (Exception ex)
            {
                //ExceptionUtils.LogException(ex, "GetUniqueCode");
            }

            code = string.Format("{0}{1}", prefix, result);
            return code;
        }

        public string UpdateSpatial(string table, string geometry, string where)
        {
            string msgTxt = "با موفقیت ویرایش شد";

            string queryAdd = "UPDATE   " + table + "    SET [Shape] = geometry::STGeomFromText('" + geometry + "', 0) WHERE " + where;
            SqlConnection con = new SqlConnection(ConnectionString);
            SqlCommand commandAdd = new SqlCommand(queryAdd, con);
            try
            {
                con.Open();
                commandAdd.ExecuteNonQuery();
                MakeShapeValid(table);
            }
            catch (Exception msg)
            {
                msgTxt = msg.Message;
            }
            finally
            {
                con.Close();
            }
            return msgTxt;
        }

        public string MakeShapeValid(string table)
        {
            string msgTxt = "با موفقیت انجام شد";

            string query = string.Format("Update {0} SET  [Shape] =  [Shape].MakeValid()", table);
            SqlConnection con = new SqlConnection(ConnectionString);
            SqlCommand commandAdd = new SqlCommand(query, con);
            try
            {
                con.Open();
                commandAdd.ExecuteNonQuery();
            }
            catch (Exception msg)
            {
                msgTxt = msg.Message;
            }
            finally
            {
                con.Close();
            }
            return msgTxt;
        }
        #endregion

        #region SQL Excute
        //public static string GetConnectioString(DownSystem downSystem,bool remote=false)
        //{
        //    if (remote)
        //        return "Data Source=" + downSystem.DBReomte_ServerIP + ";Initial Catalog=" + downSystem.DBReomte_DBName + ";Persist Security Info=True;User ID=" + downSystem.DBReomte_UserName + ";Password=" + downSystem.DBReomte_Password + ";";
        //    else
        //        return "Data Source=" + downSystem.DB_ServerIP + ";Initial Catalog=" + downSystem.DB_DBName + ";Persist Security Info=True;User ID=" + downSystem.DB_UserName + ";Password=" + downSystem.DB_Password + ";";

        //}
        public enum FeatureType
        {
            Polygon,
            Polyline,
            Point,
            Table,
            Null
        }
        public static FeatureType GetFeatureType(string datasetType)
        {
            if (datasetType.ToLower() == "table")
                return FeatureType.Table;
            else if (datasetType.ToLower() == "polyline")
                return FeatureType.Polyline;
            else if (datasetType.ToLower() == "point")
                return FeatureType.Point;
            else if (datasetType.ToLower() == "polygon")
                return FeatureType.Polygon;
            return FeatureType.Null;
        }

        public bool CreateTable(string tableName, FeatureType featureType, out string message)
        {
            string shape = "";
            if (featureType != FeatureType.Table)
                shape = "[Shape] [geometry] NULL,";


            bool excute = false;
            message = "OK";
            string query = "";
            if (featureType == FeatureType.Polyline)
                query = "CREATE TABLE [" + tableName + "]([OBJECTID] [int] IDENTITY(1,1) NOT NULL ,	[GCode] [nvarchar](254) NOT NULL PRIMARY KEY," + shape + "[CreateTime] [nvarchar](50) NULL,[CreatorUser] [nvarchar](50) NULL,[LastTime] [nvarchar](50) NULL,[LastUser] [nvarchar](50) NULL,[Org] [nvarchar](254) NULL,[Length] numeric(18, 2) NULL)";
            else if (featureType == FeatureType.Polygon)
                query = "CREATE TABLE [" + tableName + "]([OBJECTID] [int] IDENTITY(1,1) NOT NULL ,	[GCode] [nvarchar](254) NOT NULL PRIMARY KEY," + shape + "[CreateTime] [nvarchar](50) NULL,[CreatorUser] [nvarchar](50) NULL,[LastTime] [nvarchar](50) NULL,[LastUser] [nvarchar](50) NULL,[Org] [nvarchar](254) NULL,[Length] numeric(18, 2) NULL,[Area] numeric(18, 2) NULL)";
            else
                query = "CREATE TABLE [" + tableName + "]([OBJECTID] [int] IDENTITY(1,1) NOT NULL ,	[GCode] [nvarchar](254) NOT NULL PRIMARY KEY," + shape + "[CreateTime] [nvarchar](50) NULL,[CreatorUser] [nvarchar](50) NULL,[LastTime] [nvarchar](50) NULL,[LastUser] [nvarchar](50) NULL,[Org] [nvarchar](254) NULL)";

            SqlConnection con = new SqlConnection(ConnectionString);
            SqlCommand command = new SqlCommand(query, con);
            SqlDataAdapter adapter = new SqlDataAdapter(command);
            DataTable dt = new DataTable();
            try
            {
                con.Open();
                adapter.Fill(dt);
                excute = true;

            }
            catch (Exception msg)
            {
                message = msg.Message;
            }
            finally
            {
                con.Close();
            }
            return excute;
        }
        public bool DropTable(string tableName, out string message)
        {


            bool excute = false;
            message = "OK";
            string query = "if exists (select * from INFORMATION_SCHEMA.TABLES where TABLE_NAME = '" + tableName + "' AND TABLE_SCHEMA = 'dbo')    drop table " + tableName + ";";

            SqlConnection con = new SqlConnection(ConnectionString);
            SqlCommand command = new SqlCommand(query, con);
            SqlDataAdapter adapter = new SqlDataAdapter(command);
            DataTable dt = new DataTable();
            try
            {
                con.Open();
                adapter.Fill(dt);
                excute = true;

            }
            catch (Exception msg)
            {
                message = msg.Message;
            }
            finally
            {
                con.Close();
            }
            return excute;
        }

        public bool CreateField(FieldInfo fieldInfo, out string message)
        {
            //ALTER TABLE table_name ADD column_name datatype;
            if (fieldInfo.FieldType == "domain" || fieldInfo.FieldType == "multidomain")
                fieldInfo.FieldType = "nvarchar(Max)";
            else if (fieldInfo.FieldType.ToLower() == "datetime")
                fieldInfo.FieldType = "nvarchar(50)";
            else if (fieldInfo.FieldType == "nvarchar")
                if (fieldInfo.FieldLength > 0)
                    fieldInfo.FieldType = "nvarchar(" + fieldInfo.FieldLength.ToString() + ")";
                else
                    fieldInfo.FieldType = "nvarchar(Max)";
            else if (fieldInfo.FieldType == "bool")
                fieldInfo.FieldType = "Bit";


            string FRequierd = "";
            if (fieldInfo.IsRequired)
                FRequierd = " NOT NULL";
            bool excute = false;
            message = "OK";
            string query = "ALTER TABLE " + fieldInfo.TableInfo.Name + " ADD " + fieldInfo.Name + " " + fieldInfo.FieldType + " " + FRequierd;

            SqlConnection con = new SqlConnection(ConnectionString);
            SqlCommand command = new SqlCommand(query, con);
            SqlDataAdapter adapter = new SqlDataAdapter(command);
            DataTable dt = new DataTable();
            try
            {
                con.Open();
                adapter.Fill(dt);

                if (fieldInfo.IsUnique)
                {
                    query = " ALTER TABLE " + fieldInfo.TableInfo.Name + " ADD CONSTRAINT uq_" + fieldInfo.Name + " UNIQUE (" + fieldInfo.Name + ")";
                    command = new SqlCommand(query, con);
                    adapter = new SqlDataAdapter(command);
                    adapter.Fill(dt);
                }
                excute = true;

            }
            catch (Exception msg)
            {
                message = msg.Message;
            }
            finally
            {
                con.Close();
            }
            return excute;
        }
        public bool DropField(FieldInfo fieldInfo, out string message)
        {
            //ALTER TABLE table_name DROP COLUMN column_name;


            bool excute = false;
            message = "OK";
            SqlConnection con = new SqlConnection(ConnectionString);
            DataTable dt = new DataTable();
            try
            {
                string query = "ALTER TABLE " + fieldInfo.TableInfo.Name + " DROP COLUMN " + fieldInfo.Name + "";


                SqlCommand command = new SqlCommand(query, con);
                SqlDataAdapter adapter = new SqlDataAdapter(command);


                con.Open();
                if (fieldInfo.IsUnique)
                {
                    string query1 = "ALTER TABLE " + fieldInfo.TableInfo.Name + " DROP CONSTRAINT uq_" + fieldInfo.Name;
                    SqlCommand command1 = new SqlCommand(query1, con);
                    SqlDataAdapter adapter1 = new SqlDataAdapter(command1);
                    adapter1.Fill(dt);
                }


                adapter.Fill(dt);
                excute = true;

            }
            catch (Exception msg)
            {
                message = msg.Message;
            }
            finally
            {
                con.Close();
            }
            return excute;
        }

        public bool CreateForignTable(RelationInfo relationInfo, out string message)
        {

            bool excute = false;
            message = "OK";
            SqlConnection con = new SqlConnection(ConnectionString);
            DataTable dt = new DataTable();
            try
            {
                // string query = "ALTER TABLE " + relationInfo.RelatedTable.Name + " ADD CONSTRAINT FK_" + relationInfo.MainTable.Name + "_" + relationInfo.RelatedTable.Name + " FOREIGN KEY (FCode_" + relationInfo.MainTable.Name + ")  REFERENCES " + relationInfo.MainTable.Name + " (GCode)         ON DELETE CASCADE        ON UPDATE CASCADE  ";
                if (relationInfo.RelationType == "1")
                {
                    string query = "ALTER TABLE " + relationInfo.RelatedTable.Name + " ADD FCode_" + relationInfo.MainTable.Name.Replace("smn", "") + " [nvarchar](254) CONSTRAINT FK_" + relationInfo.MainTable.Name + "_" + relationInfo.RelatedTable.Name + " FOREIGN KEY REFERENCES " + relationInfo.MainTable.Name + " (GCode)         ON DELETE CASCADE        ON UPDATE CASCADE  ";
                    SqlCommand command = new SqlCommand(query, con);
                    con.Open();
                    int ok = command.ExecuteNonQuery();
                    excute = true;
                }
                else
                {
                    string query = "CREATE TABLE " + relationInfo.MainTable.Name + "_" + relationInfo.RelatedTable.Name + "([OBJECTID] [int] NOT NULL PRIMARY KEY,FCode_" + relationInfo.MainTable.Name.Replace("smn", "") + " [nvarchar](254) NOT NULL,FCode_" + relationInfo.RelatedTable.Name + " [nvarchar](254) NOT NULL,	 FOREIGN KEY (FCode_" + relationInfo.MainTable.Name.Replace("smn", "") + ") REFERENCES " + relationInfo.MainTable.Name + " (GCode) ON DELETE CASCADE        ON UPDATE CASCADE, FOREIGN KEY (FCode_" + relationInfo.RelatedTable.Name.Replace("smn", "") + ") REFERENCES " + relationInfo.RelatedTable.Name + " (GCode)  ON DELETE CASCADE        ON UPDATE CASCADE)";
                    SqlCommand command = new SqlCommand(query, con);
                    con.Open();
                    int ok = command.ExecuteNonQuery();
                    excute = true;

                }
            }
            catch (Exception msg)
            {
                message = msg.Message;
            }
            finally
            {
                con.Close();
            }
            return excute;
        }
        public bool DropForignTable(RelationInfo relationInfo, out string message)
        {

            bool excute = false;
            message = "OK";
            SqlConnection con = new SqlConnection(ConnectionString);
            DataTable dt = new DataTable();
            try
            {

                // string query = "ALTER TABLE " + relationInfo.RelatedTable.Name + " ADD CONSTRAINT FK_" + relationInfo.MainTable.Name + "_" + relationInfo.RelatedTable.Name + " FOREIGN KEY (FCode_" + relationInfo.MainTable.Name + ")  REFERENCES " + relationInfo.MainTable.Name + " (GCode)         ON DELETE CASCADE        ON UPDATE CASCADE  ";
                if (relationInfo.RelationType == "1")
                {
                    string query = "ALTER TABLE " + relationInfo.RelatedTable.Name + " DROP CONSTRAINT FK_" + relationInfo.MainTable.Name + "_" + relationInfo.RelatedTable.Name + ";";
                    query += "ALTER TABLE " + relationInfo.RelatedTable.Name + " DROP COLUMN FCode_" + relationInfo.MainTable.Name + ";";
                    //string query = "ALTER TABLE " + relationInfo.RelatedTable.Name + " ADD FCode_" + relationInfo.MainTable.Name + " [nvarchar](254) CONSTRAINT FK_" + relationInfo.MainTable.Name + "_" + relationInfo.RelatedTable.Name + " FOREIGN KEY REFERENCES " + relationInfo.MainTable.Name + " (GCode)         ON DELETE CASCADE        ON UPDATE CASCADE  ";
                    SqlCommand command = new SqlCommand(query, con);
                    command.CommandType = CommandType.Text;
                    con.Open();
                    int ok = command.ExecuteNonQuery();
                    excute = true;
                }
                else
                {
                    string query = "DROP TABLE " + relationInfo.MainTable.Name + "_" + relationInfo.RelatedTable.Name;
                    // string query = "CREATE TABLE " + relationInfo.MainTable.Name + "_" + relationInfo.RelatedTable.Name + "([OBJECTID] [int] NOT NULL PRIMARY KEY,FCode_" + relationInfo.MainTable.Name + " [nvarchar](254) NOT NULL,FCode_" + relationInfo.RelatedTable.Name + " [nvarchar](254) NOT NULL,	 FOREIGN KEY (FCode_" + relationInfo.MainTable.Name + ") REFERENCES " + relationInfo.MainTable.Name + " (GCode) ON DELETE CASCADE        ON UPDATE CASCADE, FOREIGN KEY (FCode_" + relationInfo.RelatedTable.Name + ") REFERENCES " + relationInfo.RelatedTable.Name + " (GCode)  ON DELETE CASCADE        ON UPDATE CASCADE)";
                    SqlCommand command = new SqlCommand(query, con);
                    con.Open();
                    int ok = command.ExecuteNonQuery();
                    excute = true;

                }

            }
            catch (Exception msg)
            {
                message = msg.Message;
            }
            finally
            {
                con.Close();
            }
            return excute;
        }


        #endregion

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                }

                _disposed = true;
            }
        }

        ~DBManagement()
        {
            Dispose(false);
        }
    }
}
