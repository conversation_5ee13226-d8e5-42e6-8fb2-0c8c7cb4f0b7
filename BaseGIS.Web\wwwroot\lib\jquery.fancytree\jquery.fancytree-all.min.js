/*! jQuery Fancytree Plugin - 2.38.0 - 2021-02-09T20:03:49Z
  * https://github.com/mar10/fancytree
  * Copyright (c) 2021 <PERSON>; Licensed MIT
 */
(function( factory ) {
	if ( typeof define === "function" && define.amd ) {
		// AMD. Register as an anonymous module.
		define( [
			"jquery",
			"jquery-ui/ui/widgets/mouse",
			"jquery-ui/ui/widgets/draggable",
			"jquery-ui/ui/widgets/droppable",
			"jquery-ui/ui/effects/effect-blind",
			"jquery-ui/ui/data",
			"jquery-ui/ui/effect",
			"jquery-ui/ui/focusable",
			"jquery-ui/ui/keycode",
			"jquery-ui/ui/position",
			"jquery-ui/ui/scroll-parent",
			"jquery-ui/ui/tabbable",
			"jquery-ui/ui/unique-id",
			"jquery-ui/ui/widget"
		], factory );
	} else if ( typeof module === "object" && module.exports ) {
		// Node/CommonJS
		module.exports = factory(require("jquery"));
	} else {
		// Browser globals
		factory( jQuery );
	}
}(function( $ ) {

!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree.ui-deps"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree.ui-deps"),module.exports=e(require("jquery"))):e(jQuery)}(function(k){"use strict";if(!k.ui||!k.ui.fancytree){for(var e,h=null,c=new RegExp(/\.|\//),t=/[&<>"'/]/g,n=/[<>"'/]/g,f="$recursive_request",p="$request_target_invalid",i={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"},r={16:!0,17:!0,18:!0},u={8:"backspace",9:"tab",10:"return",13:"return",19:"pause",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"insert",46:"del",59:";",61:"=",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9",106:"*",107:"+",109:"-",110:".",111:"/",112:"f1",113:"f2",114:"f3",115:"f4",116:"f5",117:"f6",118:"f7",119:"f8",120:"f9",121:"f10",122:"f11",123:"f12",144:"numlock",145:"scroll",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},g={16:"shift",17:"ctrl",18:"alt",91:"meta",93:"meta"},o={0:"",1:"left",2:"middle",3:"right"},v="active expanded focus folder lazy radiogroup selected unselectable unselectableIgnore".split(" "),y={},m="columns types".split(" "),b="checkbox expanded extraClasses folder icon iconTooltip key lazy partsel radiogroup refKey selected statusNodeType title tooltip type unselectable unselectableIgnore unselectableStatus".split(" "),s={},x={},a={active:!0,children:!0,data:!0,focus:!0},l=0;l<v.length;l++)y[v[l]]=!0;for(l=0;l<b.length;l++)e=b[l],s[e]=!0,e!==e.toLowerCase()&&(x[e.toLowerCase()]=e);return N(k.ui,"Fancytree requires jQuery UI (http://jqueryui.com)"),Date.now||(Date.now=function(){return(new Date).getTime()}),A.prototype={_findDirectChild:function(e){var t,n,i=this.children;if(i)if("string"==typeof e){for(t=0,n=i.length;t<n;t++)if(i[t].key===e)return i[t]}else{if("number"==typeof e)return this.children[e];if(e.parent===this)return e}return null},_setChildren:function(e){N(e&&(!this.children||0===this.children.length),"only init supported"),this.children=[];for(var t=0,n=e.length;t<n;t++)this.children.push(new A(this,e[t]));this.tree._callHook("treeStructureChanged",this.tree,"setChildren")},addChildren:function(e,t){var n,i,r,o,s=this.getFirstChild(),a=this.getLastChild(),l=[];for(k.isPlainObject(e)&&(e=[e]),this.children||(this.children=[]),n=0,i=e.length;n<i;n++)l.push(new A(this,e[n]));if(o=l[0],null==t?this.children=this.children.concat(l):(t=this._findDirectChild(t),N(0<=(r=k.inArray(t,this.children)),"insertBefore must be an existing child"),this.children.splice.apply(this.children,[r,0].concat(l))),s&&!t){for(n=0,i=l.length;n<i;n++)l[n].render();s!==this.getFirstChild()&&s.renderStatus(),a!==this.getLastChild()&&a.renderStatus()}else this.parent&&!this.parent.ul&&!this.tr||this.render();return 3===this.tree.options.selectMode&&this.fixSelection3FromEndNodes(),this.triggerModifyChild("add",1===l.length?l[0]:null),o},addClass:function(e){return this.toggleClass(e,!0)},addNode:function(e,t){switch(t=void 0===t||"over"===t?"child":t){case"after":return this.getParent().addChildren(e,this.getNextSibling());case"before":return this.getParent().addChildren(e,this);case"firstChild":var n=this.children?this.children[0]:null;return this.addChildren(e,n);case"child":case"over":return this.addChildren(e)}N(!1,"Invalid mode: "+t)},addPagingNode:function(e,t){var n,i;if(t=t||"child",!1!==e)return e=k.extend({title:this.tree.options.strings.moreData,statusNodeType:"paging",icon:!1},e),this.partload=!0,this.addNode(e,t);for(n=this.children.length-1;0<=n;n--)"paging"===(i=this.children[n]).statusNodeType&&this.removeChild(i);this.partload=!1},appendSibling:function(e){return this.addNode(e,"after")},applyCommand:function(e,t){return this.tree.applyCommand(e,this,t)},applyPatch:function(e){if(null===e)return this.remove(),C(this);var t,n,i={children:!0,expanded:!0,parent:!0};for(t in e)e.hasOwnProperty(t)&&(n=e[t],i[t]||k.isFunction(n)||(s[t]?this[t]=n:this.data[t]=n));return e.hasOwnProperty("children")&&(this.removeChildren(),e.children&&this._setChildren(e.children)),this.isVisible()&&(this.renderTitle(),this.renderStatus()),e.hasOwnProperty("expanded")?this.setExpanded(e.expanded):C(this)},collapseSiblings:function(){return this.tree._callHook("nodeCollapseSiblings",this)},copyTo:function(e,t,n){return e.addNode(this.toDict(!0,n),t)},countChildren:function(e){var t,n,i,r=this.children;if(!r)return 0;if(i=r.length,!1!==e)for(t=0,n=i;t<n;t++)i+=r[t].countChildren();return i},debug:function(e){4<=this.tree.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("log",arguments))},discard:function(){return this.warn("FancytreeNode.discard() is deprecated since 2014-02-16. Use .resetLazy() instead."),this.resetLazy()},discardMarkup:function(e){e=e?"nodeRemoveMarkup":"nodeRemoveChildMarkup";this.tree._callHook(e,this)},error:function(e){1<=this.tree.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("error",arguments))},findAll:function(t){t=k.isFunction(t)?t:L(t);var n=[];return this.visit(function(e){t(e)&&n.push(e)}),n},findFirst:function(t){t=k.isFunction(t)?t:L(t);var n=null;return this.visit(function(e){if(t(e))return n=e,!1}),n},findRelatedNode:function(e,t){return this.tree.findRelatedNode(this,e,t)},_changeSelectStatusAttrs:function(e){var t=!1,n=this.tree.options,i=h.evalOption("unselectable",this,this,n,!1),n=h.evalOption("unselectableStatus",this,this,n,void 0);switch(e=i&&null!=n?n:e){case!1:t=this.selected||this.partsel,this.selected=!1,this.partsel=!1;break;case!0:t=!this.selected||!this.partsel,this.selected=!0,this.partsel=!0;break;case void 0:t=this.selected||!this.partsel,this.selected=!1,this.partsel=!0;break;default:N(!1,"invalid state: "+e)}return t&&this.renderStatus(),t},fixSelection3AfterClick:function(e){var t=this.isSelected();this.visit(function(e){if(e._changeSelectStatusAttrs(t),e.radiogroup)return"skip"}),this.fixSelection3FromEndNodes(e)},fixSelection3FromEndNodes:function(e){var u=this.tree.options;N(3===u.selectMode,"expected selectMode 3"),function e(t){var n,i,r,o,s,a,l,d,c=t.children;if(c&&c.length){for(l=!(a=!0),n=0,i=c.length;n<i;n++)o=e(r=c[n]),h.evalOption("unselectableIgnore",r,r,u,!1)||(!1!==o&&(l=!0),!0!==o&&(a=!1));s=!!a||!!l&&void 0}else s=null==(d=h.evalOption("unselectableStatus",t,t,u,void 0))?!!t.selected:!!d;return t.partsel&&!t.selected&&t.lazy&&null==t.children&&(s=void 0),t._changeSelectStatusAttrs(s),s}(this),this.visitParents(function(e){for(var t,n,i,r=e.children,o=!0,s=!1,a=0,l=r.length;a<l;a++)t=r[a],h.evalOption("unselectableIgnore",t,t,u,!1)||(((n=null==(i=h.evalOption("unselectableStatus",t,t,u,void 0))?!!t.selected:!!i)||t.partsel)&&(s=!0),n||(o=!1));n=!!o||!!s&&void 0,e._changeSelectStatusAttrs(n)})},fromDict:function(e){for(var t in e)s[t]?this[t]=e[t]:"data"===t?k.extend(this.data,e.data):k.isFunction(e[t])||a[t]||(this.data[t]=e[t]);e.children&&(this.removeChildren(),this.addChildren(e.children)),this.renderTitle()},getChildren:function(){if(void 0!==this.hasChildren())return this.children},getFirstChild:function(){return this.children?this.children[0]:null},getIndex:function(){return k.inArray(this,this.parent.children)},getIndexHier:function(e,n){e=e||".";var i,r=[];return k.each(this.getParentList(!1,!0),function(e,t){i=""+(t.getIndex()+1),n&&(i=("0000000"+i).substr(-n)),r.push(i)}),r.join(e)},getKeyPath:function(e){var t=this.tree.options.keyPathSeparator;return t+this.getPath(!e,"key",t)},getLastChild:function(){return this.children?this.children[this.children.length-1]:null},getLevel:function(){for(var e=0,t=this.parent;t;)e++,t=t.parent;return e},getNextSibling:function(){if(this.parent)for(var e=this.parent.children,t=0,n=e.length-1;t<n;t++)if(e[t]===this)return e[t+1];return null},getParent:function(){return this.parent},getParentList:function(e,t){for(var n=[],i=t?this:this.parent;i;)(e||i.parent)&&n.unshift(i),i=i.parent;return n},getPath:function(e,t,n){e=!1!==e,t=t||"title",n=n||"/";var i,r=[],o=k.isFunction(t);return this.visitParents(function(e){e.parent&&(i=o?t(e):e[t],r.unshift(i))},e),r.join(n)},getPrevSibling:function(){if(this.parent)for(var e=this.parent.children,t=1,n=e.length;t<n;t++)if(e[t]===this)return e[t-1];return null},getSelectedNodes:function(t){var n=[];return this.visit(function(e){if(e.selected&&(n.push(e),!0===t))return"skip"}),n},hasChildren:function(){return this.lazy?null==this.children?void 0:0!==this.children.length&&(1!==this.children.length||!this.children[0].isStatusNode()||void 0):!(!this.children||!this.children.length)},hasClass:function(e){return 0<=(" "+(this.extraClasses||"")+" ").indexOf(" "+e+" ")},hasFocus:function(){return this.tree.hasFocus()&&this.tree.focusNode===this},info:function(e){3<=this.tree.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("info",arguments))},isActive:function(){return this.tree.activeNode===this},isBelowOf:function(e){return this.getIndexHier(".",5)>e.getIndexHier(".",5)},isChildOf:function(e){return this.parent&&this.parent===e},isDescendantOf:function(e){if(!e||e.tree!==this.tree)return!1;for(var t=this.parent;t;){if(t===e)return!0;t===t.parent&&k.error("Recursive parent link: "+t),t=t.parent}return!1},isExpanded:function(){return!!this.expanded},isFirstSibling:function(){var e=this.parent;return!e||e.children[0]===this},isFolder:function(){return!!this.folder},isLastSibling:function(){var e=this.parent;return!e||e.children[e.children.length-1]===this},isLazy:function(){return!!this.lazy},isLoaded:function(){return!this.lazy||void 0!==this.hasChildren()},isLoading:function(){return!!this._isLoading},isRoot:function(){return this.isRootNode()},isPartsel:function(){return!this.selected&&!!this.partsel},isPartload:function(){return!!this.partload},isRootNode:function(){return this.tree.rootNode===this},isSelected:function(){return!!this.selected},isStatusNode:function(){return!!this.statusNodeType},isPagingNode:function(){return"paging"===this.statusNodeType},isTopLevel:function(){return this.tree.rootNode===this.parent},isUndefined:function(){return void 0===this.hasChildren()},isVisible:function(){var e,t,n=this.tree.enableFilter,i=this.getParentList(!1,!1);if(n&&!this.match&&!this.subMatchCount)return!1;for(e=0,t=i.length;e<t;e++)if(!i[e].expanded)return!1;return!0},lazyLoad:function(e){k.error("FancytreeNode.lazyLoad() is deprecated since 2014-02-16. Use .load() instead.")},load:function(e){var t=this,n=this.isExpanded();return N(this.isLazy(),"load() requires a lazy node"),e||this.isUndefined()?(this.isLoaded()&&this.resetLazy(),!1===(e=this.tree._triggerNodeEvent("lazyLoad",this))?C(this):(N("boolean"!=typeof e,"lazyLoad event must return source in data.result"),e=this.tree._callHook("nodeLoadChildren",this,e),n?(this.expanded=!0,e.always(function(){t.render()})):e.always(function(){t.renderStatus()}),e)):C(this)},makeVisible:function(e){for(var t=this,n=[],i=new k.Deferred,r=this.getParentList(!1,!1),o=r.length,s=!(e&&!0===e.noAnimation),a=!(e&&!1===e.scrollIntoView),l=o-1;0<=l;l--)n.push(r[l].setExpanded(!0,e));return k.when.apply(k,n).done(function(){a?t.scrollIntoView(s).done(function(){i.resolve()}):i.resolve()}),i.promise()},moveTo:function(t,e,n){void 0===e||"over"===e?e="child":"firstChild"===e&&(t.children&&t.children.length?(e="before",t=t.children[0]):e="child");var i,r=this.tree,o=this.parent,s="child"===e?t:t.parent;if(this!==t){if(this.parent?s.isDescendantOf(this)&&k.error("Cannot move a node to its own descendant"):k.error("Cannot move system root"),s!==o&&o.triggerModifyChild("remove",this),1===this.parent.children.length){if(this.parent===s)return;this.parent.children=this.parent.lazy?[]:null,this.parent.expanded=!1}else N(0<=(i=k.inArray(this,this.parent.children)),"invalid source parent"),this.parent.children.splice(i,1);if((this.parent=s).hasChildren())switch(e){case"child":s.children.push(this);break;case"before":N(0<=(i=k.inArray(t,s.children)),"invalid target parent"),s.children.splice(i,0,this);break;case"after":N(0<=(i=k.inArray(t,s.children)),"invalid target parent"),s.children.splice(i+1,0,this);break;default:k.error("Invalid mode "+e)}else s.children=[this];n&&t.visit(n,!0),s===o?s.triggerModifyChild("move",this):s.triggerModifyChild("add",this),r!==t.tree&&(this.warn("Cross-tree moveTo is experimental!"),this.visit(function(e){e.tree=t.tree},!0)),r._callHook("treeStructureChanged",r,"moveTo"),o.isDescendantOf(s)||o.render(),s.isDescendantOf(o)||s===o||s.render()}},navigate:function(e,t){var n,i=k.ui.keyCode;switch(e){case"left":case i.LEFT:if(this.expanded)return this.setExpanded(!1);break;case"right":case i.RIGHT:if(!this.expanded&&(this.children||this.lazy))return this.setExpanded()}if(n=this.findRelatedNode(e)){try{n.makeVisible({scrollIntoView:!1})}catch(e){}return!1===t?(n.setFocus(),C()):n.setActive()}return this.warn("Could not find related node '"+e+"'."),C()},remove:function(){return this.parent.removeChild(this)},removeChild:function(e){return this.tree._callHook("nodeRemoveChild",this,e)},removeChildren:function(){return this.tree._callHook("nodeRemoveChildren",this)},removeClass:function(e){return this.toggleClass(e,!1)},render:function(e,t){return this.tree._callHook("nodeRender",this,e,t)},renderTitle:function(){return this.tree._callHook("nodeRenderTitle",this)},renderStatus:function(){return this.tree._callHook("nodeRenderStatus",this)},replaceWith:function(e){var n=this.parent,i=k.inArray(this,n.children),r=this;return N(this.isPagingNode(),"replaceWith() currently requires a paging status node"),(e=this.tree._callHook("nodeLoadChildren",this,e)).done(function(e){var t=r.children;for(l=0;l<t.length;l++)t[l].parent=n;n.children.splice.apply(n.children,[i+1,0].concat(t)),r.children=null,r.remove(),n.render()}).fail(function(){r.setExpanded()}),e},resetLazy:function(){this.removeChildren(),this.expanded=!1,this.lazy=!0,this.children=void 0,this.renderStatus()},scheduleAction:function(e,t){this.tree.timer&&(clearTimeout(this.tree.timer),this.tree.debug("clearTimeout(%o)",this.tree.timer)),this.tree.timer=null;var n=this;switch(e){case"cancel":break;case"expand":this.tree.timer=setTimeout(function(){n.tree.debug("setTimeout: trigger expand"),n.setExpanded(!0)},t);break;case"activate":this.tree.timer=setTimeout(function(){n.tree.debug("setTimeout: trigger activate"),n.setActive(!0)},t);break;default:k.error("Invalid mode "+e)}},scrollIntoView:function(e,t){if(void 0!==t&&((p=t).tree&&void 0!==p.statusNodeType))throw Error("scrollIntoView() with 'topNode' option is deprecated since 2014-05-08. Use 'options.topNode' instead.");var n=k.extend({effects:!0===e?{duration:200,queue:!1}:e,scrollOfs:this.tree.options.scrollOfs,scrollParent:this.tree.options.scrollParent,topNode:null},t),i=n.scrollParent,r=this.tree.$container,o=r.css("overflow-y");i?i.jquery||(i=k(i)):i=!this.tree.tbody&&("scroll"===o||"auto"===o)?r:r.scrollParent(),i[0]!==document&&i[0]!==document.body||(this.debug("scrollIntoView(): normalizing scrollParent to 'window':",i[0]),i=k(window));var s,a,l=new k.Deferred,d=this,c=k(this.span).height(),u=n.scrollOfs.top||0,h=n.scrollOfs.bottom||0,f=i.height(),p=i.scrollTop(),e=i,t=i[0]===window,o=n.topNode||null,r=null;return this.isRootNode()||!this.isVisible()?(this.info("scrollIntoView(): node is invisible."),C()):(t?(a=k(this.span).offset().top,s=o&&o.span?k(o.span).offset().top:0,e=k("html,body")):(N(i[0]!==document&&i[0]!==document.body,"scrollParent should be a simple element or `window`, not document or body."),t=i.offset().top,a=k(this.span).offset().top-t+p,s=o?k(o.span).offset().top-t+p:0,f-=Math.max(0,i.innerHeight()-i[0].clientHeight)),a<p+u?r=a-u:p+f-h<a+c&&(r=a+c-f+h,o&&(N(o.isRootNode()||o.isVisible(),"topNode must be visible"),s<r&&(r=s-u))),null===r?l.resolveWith(this):n.effects?(n.effects.complete=function(){l.resolveWith(d)},e.stop(!0).animate({scrollTop:r},n.effects)):(e[0].scrollTop=r,l.resolveWith(this)),l.promise())},setActive:function(e,t){return this.tree._callHook("nodeSetActive",this,e,t)},setExpanded:function(e,t){return this.tree._callHook("nodeSetExpanded",this,e,t)},setFocus:function(e){return this.tree._callHook("nodeSetFocus",this,e)},setSelected:function(e,t){return this.tree._callHook("nodeSetSelected",this,e,t)},setStatus:function(e,t,n){return this.tree._callHook("nodeSetStatus",this,e,t,n)},setTitle:function(e){this.title=e,this.renderTitle(),this.triggerModify("rename")},sortChildren:function(e,t){var n,i,r=this.children;if(r){if(e=e||function(e,t){e=e.title.toLowerCase(),t=t.title.toLowerCase();return e===t?0:t<e?1:-1},r.sort(e),t)for(n=0,i=r.length;n<i;n++)r[n].children&&r[n].sortChildren(e,"$norender$");"$norender$"!==t&&this.render(),this.triggerModifyChild("sort")}},toDict:function(e,t){var n,i,r,o,s={},a=this;if(k.each(b,function(e,t){!a[t]&&!1!==a[t]||(s[t]=a[t])}),k.isEmptyObject(this.data)||(s.data=k.extend({},this.data),k.isEmptyObject(s.data)&&delete s.data),t){if(!1===(o=t(s,a)))return!1;"skip"===o&&(e=!1)}if(e&&k.isArray(this.children))for(s.children=[],n=0,i=this.children.length;n<i;n++)(r=this.children[n]).isStatusNode()||!1!==(o=r.toDict(!0,t))&&s.children.push(o);return s},toggleClass:function(e,t){var n,i,r=e.match(/\S+/g)||[],o=0,s=!1,a=this[this.tree.statusClassPropName],l=" "+(this.extraClasses||"")+" ";for(a&&k(a).toggleClass(e,t);n=r[o++];)if(i=0<=l.indexOf(" "+n+" "),t=void 0===t?!i:!!t)i||(l+=n+" ",s=!0);else for(;-1<l.indexOf(" "+n+" ");)l=l.replace(" "+n+" "," ");return this.extraClasses=k.trim(l),s},toggleExpanded:function(){return this.tree._callHook("nodeToggleExpanded",this)},toggleSelected:function(){return this.tree._callHook("nodeToggleSelected",this)},toString:function(){return"FancytreeNode@"+this.key+"[title='"+this.title+"']"},triggerModifyChild:function(e,t,n){var i=this.tree.options.modifyChild;i&&(t&&t.parent!==this&&k.error("childNode "+t+" is not a child of "+this),t={node:this,tree:this.tree,operation:e,childNode:t||null},n&&k.extend(t,n),i({type:"modifyChild"},t))},triggerModify:function(e,t){this.parent.triggerModifyChild(e,this,t)},visit:function(e,t){var n,i,r=!0,o=this.children;if(!0===t&&(!1===(r=e(this))||"skip"===r))return r;if(o)for(n=0,i=o.length;n<i&&!1!==(r=o[n].visit(e,!0));n++);return r},visitAndLoad:function(n,e,t){var i,r,o,s=this;return!n||!0!==e||!1!==(r=n(s))&&"skip"!==r?s.children||s.lazy?(i=new k.Deferred,o=[],s.load().done(function(){for(var e=0,t=s.children.length;e<t;e++){if(!1===(r=s.children[e].visitAndLoad(n,!0,!0))){i.reject();break}"skip"!==r&&o.push(r)}k.when.apply(this,o).then(function(){i.resolve()})}),i.promise()):C():t?r:C()},visitParents:function(e,t){if(t&&!1===e(this))return!1;for(var n=this.parent;n;){if(!1===e(n))return!1;n=n.parent}return!0},visitSiblings:function(e,t){for(var n,i=this.parent.children,r=0,o=i.length;r<o;r++)if(n=i[r],(t||n!==this)&&!1===e(n))return!1;return!0},warn:function(e){2<=this.tree.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("warn",arguments))}},P.prototype={_makeHookContext:function(e,t,n){var i,r;return void 0!==e.node?(t&&e.originalEvent!==t&&k.error("invalid args"),i=e):e.tree?i={node:e,tree:r=e.tree,widget:r.widget,options:r.widget.options,originalEvent:t,typeInfo:r.types[e.type]||{}}:e.widget?i={node:null,tree:e,widget:e.widget,options:e.widget.options,originalEvent:t}:k.error("invalid args"),n&&k.extend(i,n),i},_callHook:function(e,t,n){var i=this._makeHookContext(t),r=this[e],t=Array.prototype.slice.call(arguments,2);return k.isFunction(r)||k.error("_callHook('"+e+"') is not a function"),t.unshift(i),r.apply(this,t)},_setExpiringValue:function(e,t,n){this._tempCache[e]={value:t,expire:Date.now()+(+n||50)}},_getExpiringValue:function(e){var t=this._tempCache[e];return t&&t.expire>Date.now()?t.value:(delete this._tempCache[e],null)},_usesExtension:function(e){return 0<=k.inArray(e,this.options.extensions)},_requireExtension:function(e,t,n,i){null!=n&&(n=!!n);var r=this._local.name,o=this.options.extensions,s=k.inArray(e,o)<k.inArray(r,o),o=t&&null==this.ext[e],s=!o&&null!=n&&n!==s;return N(r&&r!==e,"invalid or same name '"+r+"' (require yourself?)"),!o&&!s||(i||(o||t?(i="'"+r+"' extension requires '"+e+"'",s&&(i+=" to be registered "+(n?"before":"after")+" itself")):i="If used together, `"+e+"` must be registered "+(n?"before":"after")+" `"+r+"`"),k.error(i),!1)},activateKey:function(e,t){e=this.getNodeByKey(e);return e?e.setActive(!0,t):this.activeNode&&this.activeNode.setActive(!1,t),e},addPagingNode:function(e,t){return this.rootNode.addPagingNode(e,t)},applyCommand:function(e,t,n){var i;switch(t=t||this.getActiveNode(),e){case"moveUp":(i=t.getPrevSibling())&&(t.moveTo(i,"before"),t.setActive());break;case"moveDown":(i=t.getNextSibling())&&(t.moveTo(i,"after"),t.setActive());break;case"indent":(i=t.getPrevSibling())&&(t.moveTo(i,"child"),i.setExpanded(),t.setActive());break;case"outdent":t.isTopLevel()||(t.moveTo(t.getParent(),"after"),t.setActive());break;case"remove":i=t.getPrevSibling()||t.getParent(),t.remove(),i&&i.setActive();break;case"addChild":t.editCreateNode("child","");break;case"addSibling":t.editCreateNode("after","");break;case"rename":t.editStart();break;case"down":case"first":case"last":case"left":case"parent":case"right":case"up":return t.navigate(e);default:k.error("Unhandled command: '"+e+"'")}},applyPatch:function(e){for(var t,n,i,r,o=e.length,s=[],a=0;a<o;a++)N(2===(t=e[a]).length,"patchList must be an array of length-2-arrays"),n=t[0],i=t[1],(r=null===n?this.rootNode:this.getNodeByKey(n))?(t=new k.Deferred,s.push(t),r.applyPatch(i).always(S(t,r))):this.warn("could not find node with key '"+n+"'");return k.when.apply(k,s).promise()},clear:function(e){this._callHook("treeClear",this)},count:function(){return this.rootNode.countChildren()},debug:function(e){4<=this.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("log",arguments))},destroy:function(){this.widget.destroy()},enable:function(e){!1===e?this.widget.disable():this.widget.enable()},enableUpdate:function(e){return e=!1!==e,!!this._enableUpdate==!!e?e:((this._enableUpdate=e)?(this.debug("enableUpdate(true): redraw "),this._callHook("treeStructureChanged",this,"enableUpdate"),this.render()):this.debug("enableUpdate(false)..."),!e)},error:function(e){1<=this.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("error",arguments))},expandAll:function(t,n){var e=this.enableUpdate(!1);t=!1!==t,this.visit(function(e){!1!==e.hasChildren()&&e.isExpanded()!==t&&e.setExpanded(t,n)}),this.enableUpdate(e)},findAll:function(e){return this.rootNode.findAll(e)},findFirst:function(e){return this.rootNode.findFirst(e)},findNextNode:function(t,n){var i,r=null,e=this.getFirstChild();function o(e){if((r=t(e)?e:r)||e===n)return!1}return t="string"==typeof t?(i=new RegExp("^"+t,"i"),function(e){return i.test(e.title)}):t,n=n||e,this.visitRows(o,{start:n,includeSelf:!1}),r||n===e||this.visitRows(o,{start:e,includeSelf:!0}),r},findRelatedNode:function(e,t,n){var i=null,r=k.ui.keyCode;switch(t){case"parent":case r.BACKSPACE:e.parent&&e.parent.parent&&(i=e.parent);break;case"first":case r.HOME:this.visit(function(e){if(e.isVisible())return i=e,!1});break;case"last":case r.END:this.visit(function(e){e.isVisible()&&(i=e)});break;case"left":case r.LEFT:e.expanded?e.setExpanded(!1):e.parent&&e.parent.parent&&(i=e.parent);break;case"right":case r.RIGHT:e.expanded||!e.children&&!e.lazy?e.children&&e.children.length&&(i=e.children[0]):(e.setExpanded(),i=e);break;case"up":case r.UP:this.visitRows(function(e){return i=e,!1},{start:e,reverse:!0,includeSelf:!1});break;case"down":case r.DOWN:this.visitRows(function(e){return i=e,!1},{start:e,includeSelf:!1});break;default:this.tree.warn("Unknown relation '"+t+"'.")}return i},generateFormElements:function(e,t,n){n=n||{};var i="string"==typeof e?e:"ft_"+this._id+"[]",r="string"==typeof t?t:"ft_"+this._id+"_active",o="fancytree_result_"+this._id,s=k("#"+o),a=3===this.options.selectMode&&!1!==n.stopOnParents;function l(e){s.append(k("<input>",{type:"checkbox",name:i,value:e.key,checked:!0}))}s.length?s.empty():s=k("<div>",{id:o}).hide().insertAfter(this.$container),!1!==t&&this.activeNode&&s.append(k("<input>",{type:"radio",name:r,value:this.activeNode.key,checked:!0})),n.filter?this.visit(function(e){var t=n.filter(e);if("skip"===t)return t;!1!==t&&l(e)}):!1!==e&&(a=this.getSelectedNodes(a),k.each(a,function(e,t){l(t)}))},getActiveNode:function(){return this.activeNode},getFirstChild:function(){return this.rootNode.getFirstChild()},getFocusNode:function(){return this.focusNode},getOption:function(e){return this.widget.option(e)},getNodeByKey:function(t,e){var n,i;return!e&&(n=document.getElementById(this.options.idPrefix+t))?n.ftnode||null:(e=e||this.rootNode,i=null,t=""+t,e.visit(function(e){if(e.key===t)return i=e,!1},!0),i)},getRootNode:function(){return this.rootNode},getSelectedNodes:function(e){return this.rootNode.getSelectedNodes(e)},hasFocus:function(){return!!this._hasFocus},info:function(e){3<=this.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("info",arguments))},isLoading:function(){var t=!1;return this.rootNode.visit(function(e){if(e._isLoading||e._requestId)return!(t=!0)},!0),t},loadKeyPath:function(e,t){var i,n,r,o=this,s=new k.Deferred,a=this.getRootNode(),l=this.options.keyPathSeparator,d=[],c=k.extend({},t);for("function"==typeof t?i=t:t&&t.callback&&(i=t.callback),c.callback=function(e,t,n){i&&i.call(e,t,n),s.notifyWith(e,[{node:t,status:n}])},null==c.matchKey&&(c.matchKey=function(e,t){return e.key===t}),k.isArray(e)||(e=[e]),n=0;n<e.length;n++)(r=e[n]).charAt(0)===l&&(r=r.substr(1)),d.push(r.split(l));return setTimeout(function(){o._loadKeyPathImpl(s,c,a,d).done(function(){s.resolve()})},0),s.promise()},_loadKeyPathImpl:function(e,o,t,n){var i,r,s,a,l,d,c,u,h,f,p=this;for(c={},r=0;r<n.length;r++)for(h=n[r],u=t;h.length;){if(s=h.shift(),!(a=function(e,t){var n,i,r=e.children;if(r)for(n=0,i=r.length;n<i;n++)if(o.matchKey(r[n],t))return r[n];return null}(u,s))){this.warn("loadKeyPath: key not found: "+s+" (parent: "+u+")"),o.callback(this,s,"error");break}if(0===h.length){o.callback(this,a,"ok");break}if(a.lazy&&void 0===a.hasChildren()){o.callback(this,a,"loaded"),c[s=a.key]?c[s].pathSegList.push(h):c[s]={parent:a,pathSegList:[h]};break}o.callback(this,a,"loaded"),u=a}for(l in i=[],c)c.hasOwnProperty(l)&&(d=c[l],f=new k.Deferred,i.push(f),function(t,n,e){o.callback(p,n,"loading"),n.load().done(function(){p._loadKeyPathImpl.call(p,t,o,n,e).always(S(t,p))}).fail(function(e){p.warn("loadKeyPath: error loading lazy "+n),o.callback(p,a,"error"),t.rejectWith(p)})}(f,d.parent,d.pathSegList));return k.when.apply(k,i).promise()},reactivate:function(e){var t,n=this.activeNode;return n?(this.activeNode=null,t=n.setActive(!0,{noFocus:!0}),e&&n.setFocus(),t):C()},reload:function(e){return this._callHook("treeClear",this),this._callHook("treeLoad",this,e)},render:function(e,t){return this.rootNode.render(e,t)},selectAll:function(t){this.visit(function(e){e.setSelected(t)})},setFocus:function(e){return this._callHook("treeSetFocus",this,e)},setOption:function(e,t){return this.widget.option(e,t)},debugTime:function(e){4<=this.options.debugLevel&&window.console.time(this+" - "+e)},debugTimeEnd:function(e){4<=this.options.debugLevel&&window.console.timeEnd(this+" - "+e)},toDict:function(e,t){t=this.rootNode.toDict(!0,t);return e?t:t.children},toString:function(){return"Fancytree@"+this._id},_triggerNodeEvent:function(e,t,n,i){i=this._makeHookContext(t,n,i),n=this.widget._trigger(e,n,i);return!1!==n&&void 0!==i.result?i.result:n},_triggerTreeEvent:function(e,t,n){n=this._makeHookContext(this,t,n),t=this.widget._trigger(e,t,n);return!1!==t&&void 0!==n.result?n.result:t},visit:function(e){return this.rootNode.visit(e,!1)},visitRows:function(t,e){if(!this.rootNode.hasChildren())return!1;if(e&&e.reverse)return delete e.reverse,this._visitRowsUp(t,e);for(var n,i,r,o=0,s=!1===(e=e||{}).includeSelf,a=!!e.includeHidden,l=!a&&this.enableFilter,d=e.start||this.rootNode.children[0],c=d.parent;c;){for(N(0<=(i=(r=c.children).indexOf(d)+o),"Could not find "+d+" in parent's children: "+c),n=i;n<r.length;n++)if(d=r[n],!l||d.match||d.subMatchCount){if(!s&&!1===t(d))return!1;if(s=!1,d.children&&d.children.length&&(a||d.expanded)&&!1===d.visit(function(e){return!l||e.match||e.subMatchCount?!1!==t(e)&&(a||!e.children||e.expanded?void 0:"skip"):"skip"},!1))return!1}c=(d=c).parent,o=1}return!0},_visitRowsUp:function(e,t){for(var n,i,r,o=!!t.includeHidden,s=t.start||this.rootNode.children[0];;){if((n=(r=s.parent).children)[0]===s){if(!(s=r).parent)break;n=r.children}else for(i=n.indexOf(s),s=n[i-1];(o||s.expanded)&&s.children&&s.children.length;)s=(n=(r=s).children)[n.length-1];if((o||s.isVisible())&&!1===e(s))return!1}},warn:function(e){2<=this.options.debugLevel&&(Array.prototype.unshift.call(arguments,this.toString()),d("warn",arguments))}},k.extend(P.prototype,{nodeClick:function(e){var t,n,i=e.targetType,r=e.node;if("expander"===i)r.isLoading()?r.debug("Got 2nd click while loading: ignored"):this._callHook("nodeToggleExpanded",e);else if("checkbox"===i)this._callHook("nodeToggleSelected",e),e.options.focusOnSelect&&this._callHook("nodeSetFocus",e,!0);else{if(t=!(n=!1),r.folder)switch(e.options.clickFolderMode){case 2:t=!(n=!0);break;case 3:n=t=!0}t&&(this.nodeSetFocus(e),this._callHook("nodeSetActive",e,!0)),n&&this._callHook("nodeToggleExpanded",e)}},nodeCollapseSiblings:function(e,t){var n,i,r,o=e.node;if(o.parent)for(i=0,r=(n=o.parent.children).length;i<r;i++)n[i]!==o&&n[i].expanded&&this._callHook("nodeSetExpanded",n[i],!1,t)},nodeDblclick:function(e){"title"===e.targetType&&4===e.options.clickFolderMode&&this._callHook("nodeToggleExpanded",e),"title"===e.targetType&&e.originalEvent.preventDefault()},nodeKeydown:function(e){var t=e.originalEvent,n=e.node,i=e.tree,r=e.options,o=t.which,s=t.key||String.fromCharCode(o),a=!!(t.altKey||t.ctrlKey||t.metaKey),l=!g[o]&&!u[o]&&!a,o=k(t.target),d=!0,c=!(t.ctrlKey||!r.autoActivate);if(n||(a=this.getActiveNode()||this.getFirstChild())&&(a.setFocus(),(n=e.node=this.focusNode).debug("Keydown force focus on active node")),r.quicksearch&&l&&!o.is(":input:enabled"))return 500<(o=Date.now())-i.lastQuicksearchTime&&(i.lastQuicksearchTerm=""),i.lastQuicksearchTime=o,i.lastQuicksearchTerm+=s,(s=i.findNextNode(i.lastQuicksearchTerm,i.getActiveNode()))&&s.setActive(),void t.preventDefault();switch(h.eventToString(t)){case"+":case"=":i.nodeSetExpanded(e,!0);break;case"-":i.nodeSetExpanded(e,!1);break;case"space":n.isPagingNode()?i._triggerNodeEvent("clickPaging",e,t):h.evalOption("checkbox",n,n,r,!1)?i.nodeToggleSelected(e):i.nodeSetActive(e,!0);break;case"return":i.nodeSetActive(e,!0);break;case"home":case"end":case"backspace":case"left":case"right":case"up":case"down":n.navigate(t.which,c);break;default:d=!1}d&&t.preventDefault()},nodeLoadChildren:function(o,s){var t,n,a,e=null,i=!0,l=o.tree,d=o.node,c=d.parent,r="nodeLoadChildren",u=Date.now();return k.isFunction(s)&&(s=s.call(l,{type:"source"},o),N(!k.isFunction(s),"source callback must not return another function")),k.isFunction(s.then)?e=s:s.url?e=(t=k.extend({},o.options.ajax,s)).debugDelay?(n=t.debugDelay,delete t.debugDelay,k.isArray(n)&&(n=n[0]+Math.random()*(n[1]-n[0])),d.warn("nodeLoadChildren waiting debugDelay "+Math.round(n)+" ms ..."),k.Deferred(function(e){setTimeout(function(){k.ajax(t).done(function(){e.resolveWith(this,arguments)}).fail(function(){e.rejectWith(this,arguments)})},n)})):k.ajax(t):k.isPlainObject(s)||k.isArray(s)?i=!(e={then:function(e,t){e(s,null,null)}}):k.error("Invalid source type: "+s),d._requestId&&(d.warn("Recursive load request #"+u+" while #"+d._requestId+" is pending."),d._requestId=u),i&&(l.debugTime(r),l.nodeSetStatus(o,"loading")),a=new k.Deferred,e.then(function(e,t,n){var i,r;if("json"!==s.dataType&&"jsonp"!==s.dataType||"string"!=typeof e||k.error("Ajax request returned a string (did you get the JSON dataType wrong?)."),d._requestId&&d._requestId>u)a.rejectWith(this,[f]);else if(null!==d.parent||null===c){if(o.options.postProcess){try{(r=l._triggerNodeEvent("postProcess",o,o.originalEvent,{response:e,error:null,dataType:s.dataType})).error&&l.warn("postProcess returned error:",r)}catch(e){r={error:e,message:""+e,details:"postProcess failed"}}if(r.error)return i=k.isPlainObject(r.error)?r.error:{message:r.error},i=l._makeHookContext(d,null,i),void a.rejectWith(this,[i]);(k.isArray(r)||k.isPlainObject(r)&&k.isArray(r.children))&&(e=r)}else e&&e.hasOwnProperty("d")&&o.options.enableAspx&&(42===o.options.enableAspx&&l.warn("The default for enableAspx will change to `false` in the fututure. Pass `enableAspx: true` or implement postProcess to silence this warning."),e="string"==typeof e.d?k.parseJSON(e.d):e.d);a.resolveWith(this,[e])}else a.rejectWith(this,[p])},function(e,t,n){n=l._makeHookContext(d,null,{error:e,args:Array.prototype.slice.call(arguments),message:n,details:e.status+": "+n});a.rejectWith(this,[n])}),a.done(function(e){var t,n,i;l.nodeSetStatus(o,"ok"),k.isPlainObject(e)?(N(d.isRootNode(),"source may only be an object for root nodes (expecting an array of child objects otherwise)"),N(k.isArray(e.children),"if an object is passed as source, it must contain a 'children' array (all other properties are added to 'tree.data')"),t=(n=e).children,delete n.children,k.each(m,function(e,t){void 0!==n[t]&&(l[t]=n[t],delete n[t])}),k.extend(l.data,n)):t=e,N(k.isArray(t),"expected array of children"),d._setChildren(t),l.options.nodata&&0===t.length&&(k.isFunction(l.options.nodata)?i=l.options.nodata.call(l,{type:"nodata"},o):!0===l.options.nodata&&d.isRootNode()?i=l.options.strings.noData:"string"==typeof l.options.nodata&&d.isRootNode()&&(i=l.options.nodata),i&&d.setStatus("nodata",i)),l._triggerNodeEvent("loadChildren",d)}).fail(function(e){var t;e!==f?e!==p?(e.node&&e.error&&e.message?t=e:"[object Object]"===(t=l._makeHookContext(d,null,{error:e,args:Array.prototype.slice.call(arguments),message:e?e.message||e.toString():""})).message&&(t.message=""),d.warn("Load children failed ("+t.message+")",t),!1!==l._triggerNodeEvent("loadError",t,null)&&l.nodeSetStatus(o,"error",t.message,t.details)):d.warn("Lazy parent node was removed while loading: discarding response."):d.warn("Ignored response for obsolete load request #"+u+" (expected #"+d._requestId+")")}).always(function(){d._requestId=null,i&&l.debugTimeEnd(r)}),a.promise()},nodeLoadKeyPath:function(e,t){},nodeRemoveChild:function(e,t){var n=e.node,i=k.extend({},e,{node:t}),r=n.children;if(1===r.length)return N(t===r[0],"invalid single child"),this.nodeRemoveChildren(e);this.activeNode&&(t===this.activeNode||this.activeNode.isDescendantOf(t))&&this.activeNode.setActive(!1),this.focusNode&&(t===this.focusNode||this.focusNode.isDescendantOf(t))&&(this.focusNode=null),this.nodeRemoveMarkup(i),this.nodeRemoveChildren(i),N(0<=(i=k.inArray(t,r)),"invalid child"),n.triggerModifyChild("remove",t),t.visit(function(e){e.parent=null},!0),this._callHook("treeRegisterNode",this,!1,t),r.splice(i,1)},nodeRemoveChildMarkup:function(e){e=e.node;e.ul&&(e.isRootNode()?k(e.ul).empty():(k(e.ul).remove(),e.ul=null),e.visit(function(e){e.li=e.ul=null}))},nodeRemoveChildren:function(e){var t=e.tree,n=e.node;n.children&&(this.activeNode&&this.activeNode.isDescendantOf(n)&&this.activeNode.setActive(!1),this.focusNode&&this.focusNode.isDescendantOf(n)&&(this.focusNode=null),this.nodeRemoveChildMarkup(e),n.triggerModifyChild("remove",null),n.visit(function(e){e.parent=null,t._callHook("treeRegisterNode",t,!1,e)}),n.lazy?n.children=[]:n.children=null,n.isRootNode()||(n.expanded=!1),this.nodeRenderStatus(e))},nodeRemoveMarkup:function(e){var t=e.node;t.li&&(k(t.li).remove(),t.li=null),this.nodeRemoveChildMarkup(e)},nodeRender:function(e,t,n,i,r){var o,s,a,l,d,c,u,h=e.node,f=e.tree,p=e.options,g=p.aria,v=!1,y=h.parent,m=!y,b=h.children,x=null;if(!1!==f._enableUpdate&&(m||y.ul)){if(N(m||y.ul,"parent UL must exist"),m||(h.li&&(t||h.li.parentNode!==h.parent.ul)&&(h.li.parentNode===h.parent.ul?x=h.li.nextSibling:this.debug("Unlinking "+h+" (must be child of "+h.parent+")"),this.nodeRemoveMarkup(e)),h.li?this.nodeRenderStatus(e):(v=!0,h.li=document.createElement("li"),(h.li.ftnode=h).key&&p.generateIds&&(h.li.id=p.idPrefix+h.key),h.span=document.createElement("span"),h.span.className="fancytree-node",g&&!h.tr&&k(h.li).attr("role","treeitem"),h.li.appendChild(h.span),this.nodeRenderTitle(e),p.createNode&&p.createNode.call(f,{type:"createNode"},e)),p.renderNode&&p.renderNode.call(f,{type:"renderNode"},e)),b){if(m||h.expanded||!0===n){for(h.ul||(h.ul=document.createElement("ul"),(!0!==i||r)&&h.expanded||(h.ul.style.display="none"),g&&k(h.ul).attr("role","group"),h.li?h.li.appendChild(h.ul):h.tree.$div.append(h.ul)),l=0,d=b.length;l<d;l++)u=k.extend({},e,{node:b[l]}),this.nodeRender(u,t,n,!1,!0);for(o=h.ul.firstChild;o;)o=(a=o.ftnode)&&a.parent!==h?(h.debug("_fixParent: remove missing "+a,o),c=o.nextSibling,o.parentNode.removeChild(o),c):o.nextSibling;for(o=h.ul.firstChild,l=0,d=b.length-1;l<d;l++)(s=b[l])===(a=o.ftnode)?o=o.nextSibling:h.ul.insertBefore(s.li,a.li)}}else h.ul&&(this.warn("remove child markup for "+h),this.nodeRemoveChildMarkup(e));m||v&&y.ul.insertBefore(h.li,x)}},nodeRenderTitle:function(e,t){var n,i,r=e.node,o=e.tree,s=e.options,a=s.aria,l=r.getLevel(),d=[];void 0!==t&&(r.title=t),r.span&&!1!==o._enableUpdate&&(t=a&&!1!==r.hasChildren()?" role='button'":"",l<s.minExpandLevel?(r.lazy||(r.expanded=!0),1<l&&d.push("<span "+t+" class='fancytree-expander fancytree-expander-fixed'></span>")):d.push("<span "+t+" class='fancytree-expander'></span>"),(l=h.evalOption("checkbox",r,r,s,!1))&&!r.isStatusNode()&&(t=a?" role='checkbox'":"",n="fancytree-checkbox",("radio"===l||r.parent&&r.parent.radiogroup)&&(n+=" fancytree-radio"),d.push("<span "+t+" class='"+n+"'></span>")),void 0!==r.data.iconClass&&(r.icon?k.error("'iconClass' node option is deprecated since v2.14.0: use 'icon' only instead"):(r.warn("'iconClass' node option is deprecated since v2.14.0: use 'icon' instead"),r.icon=r.data.iconClass)),!1!==(n=h.evalOption("icon",r,r,s,!0))&&(t=a?" role='presentation'":"",i=(i=h.evalOption("iconTooltip",r,r,s,null))?" title='"+T(i)+"'":"","string"==typeof n?c.test(n)?(n="/"===n.charAt(0)?n:(s.imagePath||"")+n,d.push("<img src='"+n+"' class='fancytree-icon'"+i+" alt='' />")):d.push("<span "+t+" class='fancytree-custom-icon "+n+"'"+i+"></span>"):n.text?d.push("<span "+t+" class='fancytree-custom-icon "+(n.addClass||"")+"'"+i+">"+h.escapeHtml(n.text)+"</span>"):n.html?d.push("<span "+t+" class='fancytree-custom-icon "+(n.addClass||"")+"'"+i+">"+n.html+"</span>"):d.push("<span "+t+" class='fancytree-icon'"+i+"></span>")),t="",t=(t=s.renderTitle?s.renderTitle.call(o,{type:"renderTitle"},e)||"":t)||"<span class='fancytree-title'"+(i=(i=!0===(i=h.evalOption("tooltip",r,r,s,null))?r.title:i)?" title='"+T(i)+"'":"")+(s.titlesTabbable?" tabindex='0'":"")+">"+(s.escapeTitles?h.escapeHtml(r.title):r.title)+"</span>",d.push(t),r.span.innerHTML=d.join(""),this.nodeRenderStatus(e),s.enhanceTitle&&(e.$title=k(">span.fancytree-title",r.span),t=s.enhanceTitle.call(o,{type:"enhanceTitle"},e)||""))},nodeRenderStatus:function(e){var t,n=e.node,i=e.tree,r=e.options,o=n.hasChildren(),s=n.isLastSibling(),a=r.aria,l=r._classNames,d=[],e=n[i.statusClassPropName];e&&!1!==i._enableUpdate&&(a&&(t=k(n.tr||n.li)),d.push(l.node),i.activeNode===n&&d.push(l.active),i.focusNode===n&&d.push(l.focused),n.expanded&&d.push(l.expanded),a&&(!1===o?t.removeAttr("aria-expanded"):t.attr("aria-expanded",Boolean(n.expanded))),n.folder&&d.push(l.folder),!1!==o&&d.push(l.hasChildren),s&&d.push(l.lastsib),n.lazy&&null==n.children&&d.push(l.lazy),n.partload&&d.push(l.partload),n.partsel&&d.push(l.partsel),h.evalOption("unselectable",n,n,r,!1)&&d.push(l.unselectable),n._isLoading&&d.push(l.loading),n._error&&d.push(l.error),n.statusNodeType&&d.push(l.statusNodePrefix+n.statusNodeType),n.selected?(d.push(l.selected),a&&t.attr("aria-selected",!0)):a&&t.attr("aria-selected",!1),n.extraClasses&&d.push(n.extraClasses),!1===o?d.push(l.combinedExpanderPrefix+"n"+(s?"l":"")):d.push(l.combinedExpanderPrefix+(n.expanded?"e":"c")+(n.lazy&&null==n.children?"d":"")+(s?"l":"")),d.push(l.combinedIconPrefix+(n.expanded?"e":"c")+(n.folder?"f":"")),e.className=d.join(" "),n.li&&k(n.li).toggleClass(l.lastsib,s))},nodeSetActive:function(e,t,n){n=n||{};var i=e.node,r=e.tree,o=e.options,s=!0===n.noEvents,a=!0===n.noFocus,n=!1!==n.scrollIntoView;return i===r.activeNode===(t=!1!==t)?C(i):(n&&e.originalEvent&&k(e.originalEvent.target).is("a,:checkbox")&&(i.info("Not scrolling while clicking an embedded link."),n=!1),t&&!s&&!1===this._triggerNodeEvent("beforeActivate",i,e.originalEvent)?w(i,["rejected"]):(t?(r.activeNode&&(N(r.activeNode!==i,"node was active (inconsistency)"),t=k.extend({},e,{node:r.activeNode}),r.nodeSetActive(t,!1),N(null===r.activeNode,"deactivate was out of sync?")),o.activeVisible&&i.makeVisible({scrollIntoView:n}),r.activeNode=i,r.nodeRenderStatus(e),a||r.nodeSetFocus(e),s||r._triggerNodeEvent("activate",i,e.originalEvent)):(N(r.activeNode===i,"node was not active (inconsistency)"),r.activeNode=null,this.nodeRenderStatus(e),s||e.tree._triggerNodeEvent("deactivate",i,e.originalEvent)),C(i)))},nodeSetExpanded:function(i,r,e){e=e||{};var t,n,o,s,a,l,d=i.node,c=i.tree,u=i.options,h=!0===e.noAnimation,f=!0===e.noEvents;if(r=!1!==r,k(d.li).hasClass(u._classNames.animating))return d.warn("setExpanded("+r+") while animating: ignored."),w(d,["recursion"]);if(d.expanded&&r||!d.expanded&&!r)return C(d);if(r&&!d.lazy&&!d.hasChildren())return C(d);if(!r&&d.getLevel()<u.minExpandLevel)return w(d,["locked"]);if(!f&&!1===this._triggerNodeEvent("beforeExpand",d,i.originalEvent))return w(d,["rejected"]);if(h||d.isVisible()||(h=e.noAnimation=!0),n=new k.Deferred,r&&!d.expanded&&u.autoCollapse){a=d.getParentList(!1,!0),l=u.autoCollapse;try{for(u.autoCollapse=!1,o=0,s=a.length;o<s;o++)this._callHook("nodeCollapseSiblings",a[o],e)}finally{u.autoCollapse=l}}return n.done(function(){var e=d.getLastChild();r&&u.autoScroll&&!h&&e&&c._enableUpdate?e.scrollIntoView(!0,{topNode:d}).always(function(){f||i.tree._triggerNodeEvent(r?"expand":"collapse",i)}):f||i.tree._triggerNodeEvent(r?"expand":"collapse",i)}),t=function(e){var t=u._classNames,n=u.toggleEffect;if(d.expanded=r,c._callHook("treeStructureChanged",i,r?"expand":"collapse"),c._callHook("nodeRender",i,!1,!1,!0),d.ul)if("none"!==d.ul.style.display==!!d.expanded)d.warn("nodeSetExpanded: UL.style.display already set");else{if(n&&!h)return k(d.li).addClass(t.animating),void(k.isFunction(k(d.ul)[n.effect])?k(d.ul)[n.effect]({duration:n.duration,always:function(){k(this).removeClass(t.animating),k(d.li).removeClass(t.animating),e()}}):(k(d.ul).stop(!0,!0),k(d.ul).parent().find(".ui-effects-placeholder").remove(),k(d.ul).toggle(n.effect,n.options,n.duration,function(){k(this).removeClass(t.animating),k(d.li).removeClass(t.animating),e()})));d.ul.style.display=d.expanded||!parent?"":"none"}e()},r&&d.lazy&&void 0===d.hasChildren()?d.load().done(function(){n.notifyWith&&n.notifyWith(d,["loaded"]),t(function(){n.resolveWith(d)})}).fail(function(e){t(function(){n.rejectWith(d,["load failed ("+e+")"])})}):t(function(){n.resolveWith(d)}),n.promise()},nodeSetFocus:function(e,t){var n,i=e.tree,r=e.node,o=i.options,s=!!e.originalEvent&&k(e.originalEvent.target).is(":input");if(t=!1!==t,i.focusNode){if(i.focusNode===r&&t)return;n=k.extend({},e,{node:i.focusNode}),i.focusNode=null,this._triggerNodeEvent("blur",n),this._callHook("nodeRenderStatus",n)}t&&(this.hasFocus()||(r.debug("nodeSetFocus: forcing container focus"),this._callHook("treeSetFocus",e,!0,{calledByNode:!0})),r.makeVisible({scrollIntoView:!1}),i.focusNode=r,o.titlesTabbable&&(s||k(r.span).find(".fancytree-title").focus()),o.aria&&k(i.$container).attr("aria-activedescendant",k(r.tr||r.li).uniqueId().attr("id")),this._triggerNodeEvent("focus",e),document.activeElement===i.$container.get(0)||1<=k(document.activeElement,i.$container).length||k(i.$container).focus(),o.autoScroll&&r.scrollIntoView(),this._callHook("nodeRenderStatus",e))},nodeSetSelected:function(e,t,n){n=n||{};var i=e.node,r=e.tree,o=e.options,s=!0===n.noEvents,a=i.parent;if(t=!1!==t,!h.evalOption("unselectable",i,i,o,!1))return i._lastSelectIntent=t,!!i.selected!==t||3===o.selectMode&&i.partsel&&!t?s||!1!==this._triggerNodeEvent("beforeSelect",i,e.originalEvent)?(t&&1===o.selectMode?(r.lastSelectedNode&&r.lastSelectedNode.setSelected(!1),i.selected=t):3!==o.selectMode||!a||a.radiogroup||i.radiogroup?a&&a.radiogroup?i.visitSiblings(function(e){e._changeSelectStatusAttrs(t&&e===i)},!0):i.selected=t:(i.selected=t,i.fixSelection3AfterClick(n)),this.nodeRenderStatus(e),r.lastSelectedNode=t?i:null,void(s||r._triggerNodeEvent("select",e))):!!i.selected:t},nodeSetStatus:function(i,e,t,n){var r=i.node,o=i.tree;function s(e,t){var n=r.children?r.children[0]:null;return n&&n.isStatusNode()?(k.extend(n,e),n.statusNodeType=t,o._callHook("nodeRenderTitle",n)):(r._setChildren([e]),o._callHook("treeStructureChanged",i,"setStatusNode"),r.children[0].statusNodeType=t,o.render()),r.children[0]}switch(e){case"ok":!function(){var e=r.children?r.children[0]:null;if(e&&e.isStatusNode()){try{r.ul&&(r.ul.removeChild(e.li),e.li=null)}catch(e){}1===r.children.length?r.children=[]:r.children.shift(),o._callHook("treeStructureChanged",i,"clearStatusNode")}}(),r._isLoading=!1,r._error=null,r.renderStatus();break;case"loading":r.parent||s({title:o.options.strings.loading+(t?" ("+t+")":""),checkbox:!1,tooltip:n},e),r._isLoading=!0,r._error=null,r.renderStatus();break;case"error":s({title:o.options.strings.loadError+(t?" ("+t+")":""),checkbox:!1,tooltip:n},e),r._isLoading=!1,r._error={message:t,details:n},r.renderStatus();break;case"nodata":s({title:t||o.options.strings.noData,checkbox:!1,tooltip:n},e),r._isLoading=!1,r._error=null,r.renderStatus();break;default:k.error("invalid node status "+e)}},nodeToggleExpanded:function(e){return this.nodeSetExpanded(e,!e.node.expanded)},nodeToggleSelected:function(e){var t=e.node,n=!t.selected;return t.partsel&&!t.selected&&!0===t._lastSelectIntent&&(n=!1,t.selected=!0),t._lastSelectIntent=n,this.nodeSetSelected(e,n)},treeClear:function(e){var t=e.tree;t.activeNode=null,t.focusNode=null,t.$div.find(">ul.fancytree-container").empty(),t.rootNode.children=null,t._callHook("treeStructureChanged",e,"clear")},treeCreate:function(e){},treeDestroy:function(e){this.$div.find(">ul.fancytree-container").remove(),this.$source&&this.$source.removeClass("fancytree-helper-hidden")},treeInit:function(e){var n=e.tree,i=n.options;n.$container.attr("tabindex",i.tabindex),k.each(m,function(e,t){void 0!==i[t]&&(n.info("Move option "+t+" to tree"),n[t]=i[t],delete i[t])}),i.checkboxAutoHide&&n.$container.addClass("fancytree-checkbox-auto-hide"),i.rtl?n.$container.attr("DIR","RTL").addClass("fancytree-rtl"):n.$container.removeAttr("DIR").removeClass("fancytree-rtl"),i.aria&&(n.$container.attr("role","tree"),1!==i.selectMode&&n.$container.attr("aria-multiselectable",!0)),this.treeLoad(e)},treeLoad:function(e,t){var n,i,r,o=e.tree,s=e.widget.element,a=k.extend({},e,{node:this.rootNode});if(o.rootNode.children&&this.treeClear(e),t=t||this.options.source)"string"==typeof t&&k.error("Not implemented");else switch(i=s.data("type")||"html"){case"html":(r=s.find(">ul").not(".fancytree-container").first()).length?(r.addClass("ui-fancytree-source fancytree-helper-hidden"),t=k.ui.fancytree.parseHtml(r),this.data=k.extend(this.data,E(r))):(h.warn("No `source` option was passed and container does not contain `<ul>`: assuming `source: []`."),t=[]);break;case"json":t=k.parseJSON(s.text()),s.contents().filter(function(){return 3===this.nodeType}).remove(),k.isPlainObject(t)&&(N(k.isArray(t.children),"if an object is passed as source, it must contain a 'children' array (all other properties are added to 'tree.data')"),t=(n=t).children,delete n.children,k.each(m,function(e,t){void 0!==n[t]&&(o[t]=n[t],delete n[t])}),k.extend(o.data,n));break;default:k.error("Invalid data-type: "+i)}return o._triggerTreeEvent("preInit",null),this.nodeLoadChildren(a,t).done(function(){o._callHook("treeStructureChanged",e,"loadChildren"),o.render(),3===e.options.selectMode&&o.rootNode.fixSelection3FromEndNodes(),o.activeNode&&o.options.activeVisible&&o.activeNode.makeVisible(),o._triggerTreeEvent("init",null,{status:!0})}).fail(function(){o.render(),o._triggerTreeEvent("init",null,{status:!1})})},treeRegisterNode:function(e,t,n){e.tree._callHook("treeStructureChanged",e,t?"addNode":"removeNode")},treeSetFocus:function(e,t,n){var i;(t=!1!==t)!==this.hasFocus()&&(!(this._hasFocus=t)&&this.focusNode?this.focusNode.setFocus(!1):!t||n&&n.calledByNode||k(this.$container).focus(),this.$container.toggleClass("fancytree-treefocus",t),this._triggerTreeEvent(t?"focusTree":"blurTree"),t&&!this.activeNode&&(i=this._lastMousedownNode||this.getFirstChild())&&i.setFocus())},treeSetOption:function(e,t,n){var i=e.tree,r=!0,o=!1,s=!1;switch(t){case"aria":case"checkbox":case"icon":case"minExpandLevel":case"tabindex":s=o=!0;break;case"checkboxAutoHide":i.$container.toggleClass("fancytree-checkbox-auto-hide",!!n);break;case"escapeTitles":case"tooltip":s=!0;break;case"rtl":!1===n?i.$container.removeAttr("DIR").removeClass("fancytree-rtl"):i.$container.attr("DIR","RTL").addClass("fancytree-rtl"),s=!0;break;case"source":r=!1,i._callHook("treeLoad",i,n),s=!0}i.debug("set option "+t+"="+n+" <"+typeof n+">"),r&&(this.widget._super||k.Widget.prototype._setOption).call(this.widget,t,n),o&&i._callHook("treeCreate",i),s&&i.render(!0,!1)},treeStructureChanged:function(e,t){}}),k.widget("ui.fancytree",{options:{activeVisible:!0,ajax:{type:"GET",cache:!1,dataType:"json"},aria:!0,autoActivate:!0,autoCollapse:!1,autoScroll:!1,checkbox:!1,clickFolderMode:4,copyFunctionsToData:!1,debugLevel:null,disabled:!1,enableAspx:42,escapeTitles:!1,extensions:[],focusOnSelect:!1,generateIds:!1,icon:!0,idPrefix:"ft_",keyboard:!0,keyPathSeparator:"/",minExpandLevel:1,nodata:!0,quicksearch:!1,rtl:!1,scrollOfs:{top:0,bottom:0},scrollParent:null,selectMode:2,strings:{loading:"Loading...",loadError:"Load error!",moreData:"More...",noData:"No data."},tabindex:"0",titlesTabbable:!1,toggleEffect:{effect:"slideToggle",duration:200},tooltip:!1,treeId:null,_classNames:{active:"fancytree-active",animating:"fancytree-animating",combinedExpanderPrefix:"fancytree-exp-",combinedIconPrefix:"fancytree-ico-",error:"fancytree-error",expanded:"fancytree-expanded",focused:"fancytree-focused",folder:"fancytree-folder",hasChildren:"fancytree-has-children",lastsib:"fancytree-lastsib",lazy:"fancytree-lazy",loading:"fancytree-loading",node:"fancytree-node",partload:"fancytree-partload",partsel:"fancytree-partsel",radio:"fancytree-radio",selected:"fancytree-selected",statusNodePrefix:"fancytree-statusnode-",unselectable:"fancytree-unselectable"},lazyLoad:null,postProcess:null},_deprecationWarning:function(e){var t=this.tree;t&&3<=t.options.debugLevel&&t.warn("$().fancytree('"+e+"') is deprecated (see https://wwwendt.de/tech/fancytree/doc/jsdoc/Fancytree_Widget.html")},_create:function(){this.tree=new P(this),this.$source=this.source||"json"===this.element.data("type")?this.element:this.element.find(">ul").first();for(var e,t,n=this.options,i=n.extensions,r=(this.tree,0);r<i.length;r++)t=i[r],(e=k.ui.fancytree._extensions[t])||k.error("Could not apply extension '"+t+"' (it is not registered, did you forget to include it?)"),this.tree.options[t]=function e(t){var n,i,r,o,s=t||{},a=1,l=arguments.length;if("object"==typeof s||k.isFunction(s)||(s={}),a===l)throw Error("need at least two args");for(;a<l;a++)if(null!=(n=arguments[a]))for(i in n)n.hasOwnProperty(i)&&(o=s[i],s!==(r=n[i])&&(r&&k.isPlainObject(r)?(o=o&&k.isPlainObject(o)?o:{},s[i]=e(o,r)):void 0!==r&&(s[i]=r)));return s}({},e.options,this.tree.options[t]),N(void 0===this.tree.ext[t],"Extension name must not exist as Fancytree.ext attribute: '"+t+"'"),this.tree.ext[t]={},function(e,t,n){for(var i in t)"function"==typeof t[i]?"function"==typeof e[i]?e[i]=_(i,e,0,t,n):"_"===i.charAt(0)?e.ext[n][i]=_(i,e,0,t,n):k.error("Could not override tree."+i+". Use prefix '_' to create tree."+n+"._"+i):"options"!==i&&(e.ext[n][i]=t[i])}(this.tree,e,t),0;void 0!==n.icons&&(!0===n.icon?(this.tree.warn("'icons' tree option is deprecated since v2.14.0: use 'icon' instead"),n.icon=n.icons):k.error("'icons' tree option is deprecated since v2.14.0: use 'icon' only instead")),void 0!==n.iconClass&&(n.icon?k.error("'iconClass' tree option is deprecated since v2.14.0: use 'icon' only instead"):(this.tree.warn("'iconClass' tree option is deprecated since v2.14.0: use 'icon' instead"),n.icon=n.iconClass)),void 0!==n.tabbable&&(n.tabindex=n.tabbable?"0":"-1",this.tree.warn("'tabbable' tree option is deprecated since v2.17.0: use 'tabindex='"+n.tabindex+"' instead")),this.tree._callHook("treeCreate",this.tree)},_init:function(){this.tree._callHook("treeInit",this.tree),this._bind()},_setOption:function(e,t){return this.tree._callHook("treeSetOption",this.tree,e,t)},_destroy:function(){this._unbind(),this.tree._callHook("treeDestroy",this.tree)},_unbind:function(){var e=this.tree._ns;this.element.off(e),this.tree.$container.off(e),k(document).off(e)},_bind:function(){var s=this,a=this.options,o=this.tree,e=o._ns;this._unbind(),o.$container.on("focusin"+e+" focusout"+e,function(e){var t=h.getNode(e),n="focusin"===e.type;if(!n&&t&&k(e.target).is("a"))t.debug("Ignored focusout on embedded <a> element.");else{if(n){if(o._getExpiringValue("focusin"))return void o.debug("Ignored double focusin.");o._setExpiringValue("focusin",!0,50),t||(t=o._getExpiringValue("mouseDownNode"))&&o.debug("Reconstruct mouse target for focusin from recent event.")}t?o._callHook("nodeSetFocus",o._makeHookContext(t,e),n):o.tbody&&k(e.target).parents("table.fancytree-container > thead").length?o.debug("Ignore focus event outside table body.",e):o._callHook("treeSetFocus",o,n)}}).on("selectstart"+e,"span.fancytree-title",function(e){e.preventDefault()}).on("keydown"+e,function(e){if(a.disabled||!1===a.keyboard)return!0;var t,n=o.focusNode,i=o._makeHookContext(n||o,e),r=o.phase;try{return o.phase="userEvent","preventNav"===(t=n?o._triggerNodeEvent("keydown",n,e):o._triggerTreeEvent("keydown",e))?t=!0:!1!==t&&(t=o._callHook("nodeKeydown",i)),t}finally{o.phase=r}}).on("mousedown"+e,function(e){e=h.getEventTarget(e);o._lastMousedownNode=e?e.node:null,o._setExpiringValue("mouseDownNode",o._lastMousedownNode)}).on("click"+e+" dblclick"+e,function(e){if(a.disabled)return!0;var t,n=h.getEventTarget(e),i=n.node,r=s.tree,o=r.phase;if(!i)return!0;t=r._makeHookContext(i,e);try{switch(r.phase="userEvent",e.type){case"click":return t.targetType=n.type,i.isPagingNode()?!0===r._triggerNodeEvent("clickPaging",t,e):!1!==r._triggerNodeEvent("click",t,e)&&r._callHook("nodeClick",t);case"dblclick":return t.targetType=n.type,!1!==r._triggerNodeEvent("dblclick",t,e)&&r._callHook("nodeDblclick",t)}}finally{r.phase=o}})},getActiveNode:function(){return this._deprecationWarning("getActiveNode"),this.tree.activeNode},getNodeByKey:function(e){return this._deprecationWarning("getNodeByKey"),this.tree.getNodeByKey(e)},getRootNode:function(){return this._deprecationWarning("getRootNode"),this.tree.rootNode},getTree:function(){return this._deprecationWarning("getTree"),this.tree}}),h=k.ui.fancytree,k.extend(k.ui.fancytree,{version:"2.38.0",buildType: "production",debugLevel: 3,_nextId:1,_nextNodeKey:1,_extensions:{},_FancytreeClass:P,_FancytreeNodeClass:A,jquerySupports:{positionMyOfs:function(e){for(var t,n,i=k.map(k.trim(e).split("."),function(e){return parseInt(e,10)}),r=k.map(Array.prototype.slice.call(arguments,1),function(e){return parseInt(e,10)}),o=0;o<r.length;o++)if((t=i[o]||0)!==(n=r[o]||0))return n<t;return!0}(k.ui.version,1,9)},assert:N,createTree:function(e,t){t=k(e).fancytree(t);return h.getTree(t)},debounce:function(t,n,i,r){var o;return 3===arguments.length&&"boolean"!=typeof i&&(r=i,i=!1),function(){var e=arguments;r=r||this,i&&!o&&n.apply(r,e),clearTimeout(o),o=setTimeout(function(){i||n.apply(r,e),o=null},t)}},debug:function(e){4<=k.ui.fancytree.debugLevel&&d("log",arguments)},error:function(e){1<=k.ui.fancytree.debugLevel&&d("error",arguments)},escapeHtml:function(e){return(""+e).replace(t,function(e){return i[e]})},fixPositionOptions:function(e){var t,n,i,r;return(e.offset||0<=(""+e.my+e.at).indexOf("%"))&&k.error("expected new position syntax (but '%' is not supported)"),k.ui.fancytree.jquerySupports.positionMyOfs||(t=/(\w+)([+-]?\d+)?\s+(\w+)([+-]?\d+)?/.exec(e.my),n=/(\w+)([+-]?\d+)?\s+(\w+)([+-]?\d+)?/.exec(e.at),i=(t[2]?+t[2]:0)+(n[2]?+n[2]:0),r=(t[4]?+t[4]:0)+(n[4]?+n[4]:0),e=k.extend({},e,{my:t[1]+" "+t[3],at:n[1]+" "+n[3]}),(i||r)&&(e.offset=i+" "+r)),e},getEventTarget:function(e){var t=e&&e.target?e.target.className:"",n={node:this.getNode(e.target),type:void 0};return/\bfancytree-title\b/.test(t)?n.type="title":/\bfancytree-expander\b/.test(t)?n.type=!1===n.node.hasChildren()?"prefix":"expander":/\bfancytree-checkbox\b/.test(t)?n.type="checkbox":/\bfancytree(-custom)?-icon\b/.test(t)?n.type="icon":/\bfancytree-node\b/.test(t)?n.type="title":e&&e.target&&((e=k(e.target)).is("ul[role=group]")?((n.node&&n.node.tree||h).debug("Ignoring click on outer UL."),n.node=null):e.closest(".fancytree-title").length?n.type="title":e.closest(".fancytree-checkbox").length?n.type="checkbox":e.closest(".fancytree-expander").length&&(n.type="expander")),n},getEventTargetType:function(e){return this.getEventTarget(e).type},getNode:function(e){if(e instanceof A)return e;for(e instanceof k?e=e[0]:void 0!==e.originalEvent&&(e=e.target);e;){if(e.ftnode)return e.ftnode;e=e.parentNode}return null},getTree:function(e){var t=e;return e instanceof P?e:("number"==typeof(e=void 0===e?0:e)?e=k(".fancytree-container").eq(e):"string"==typeof e?(e=k("#ft-id-"+t).eq(0)).length||(e=k(t).eq(0)):e instanceof Element||e instanceof HTMLDocument?e=k(e):e instanceof k?e=e.eq(0):void 0!==e.originalEvent&&(e=k(e.target)),(e=(e=e.closest(":ui-fancytree")).data("ui-fancytree")||e.data("fancytree"))?e.tree:null)},evalOption:function(e,t,n,i,r){var o,s=t.tree,i=i[e],n=n[e];return k.isFunction(i)?(o={node:t,tree:s,widget:s.widget,options:s.widget.options,typeInfo:s.types[t.type]||{}},null==(o=i.call(s,{type:e},o))&&(o=n)):o=null==n?i:n,o=null==o?r:o},setSpanIcon:function(e,t,n){var i=k(e);"string"==typeof n?i.attr("class",t+" "+n):(n.text?i.text(""+n.text):n.html&&(e.innerHTML=n.html),i.attr("class",t+" "+(n.addClass||"")))},eventToString:function(e){var t=e.which,n=e.type,i=[];return e.altKey&&i.push("alt"),e.ctrlKey&&i.push("ctrl"),e.metaKey&&i.push("meta"),e.shiftKey&&i.push("shift"),"click"===n||"dblclick"===n?i.push(o[e.button]+n):"wheel"===n?i.push(n):r[t]||i.push(u[t]||String.fromCharCode(t).toLowerCase()),i.join("+")},info:function(e){3<=k.ui.fancytree.debugLevel&&d("info",arguments)},keyEventToString:function(e){return this.warn("keyEventToString() is deprecated: use eventToString()"),this.eventToString(e)},overrideMethod:function(e,t,n,i){var r,o=e[t]||k.noop;e[t]=function(){var e=i||this;try{return r=e._super,e._super=o,n.apply(e,arguments)}finally{e._super=r}}},parseHtml:function(s){var a,l,d,c,u,h,f,p,e=s.find(">li"),g=[];return e.each(function(){var e,t,n=k(this),i=n.find(">span",this).first(),r=i.length?null:n.find(">a").first(),o={tooltip:null,data:{}};for(i.length?o.title=i.html():r&&r.length?(o.title=r.html(),o.data.href=r.attr("href"),o.data.target=r.attr("target"),o.tooltip=r.attr("title")):(o.title=n.html(),0<=(u=o.title.search(/<ul/i))&&(o.title=o.title.substring(0,u))),o.title=k.trim(o.title),c=0,h=v.length;c<h;c++)o[v[c]]=void 0;for(a=this.className.split(" "),d=[],c=0,h=a.length;c<h;c++)l=a[c],y[l]?o[l]=!0:d.push(l);if(o.extraClasses=d.join(" "),(f=n.attr("title"))&&(o.tooltip=f),(f=n.attr("id"))&&(o.key=f),n.attr("hideCheckbox")&&(o.checkbox=!1),(e=E(n))&&!k.isEmptyObject(e)){for(t in x)e.hasOwnProperty(t)&&(e[x[t]]=e[t],delete e[t]);for(c=0,h=b.length;c<h;c++)f=b[c],null!=(p=e[f])&&(delete e[f],o[f]=p);k.extend(o.data,e)}(s=n.find(">ul").first()).length?o.children=k.ui.fancytree.parseHtml(s):o.children=o.lazy?void 0:null,g.push(o)}),g},registerExtension:function(e){N(null!=e.name,"extensions must have a `name` property."),N(null!=e.version,"extensions must have a `version` property."),k.ui.fancytree._extensions[e.name]=e},unescapeHtml:function(e){var t=document.createElement("div");return t.innerHTML=e,0===t.childNodes.length?"":t.childNodes[0].nodeValue},warn:function(e){2<=k.ui.fancytree.debugLevel&&d("warn",arguments)}}),k.ui.fancytree}function N(e,t){e||(t="Fancytree assertion failed"+(t=t?": "+t:""),k.ui.fancytree.error(t),k.error(t))}function d(e,t){var n,i,r=window.console?window.console[e]:null;if(r)try{r.apply(window.console,t)}catch(e){for(i="",n=0;n<t.length;n++)i+=t[n];r(i)}}function _(e,i,t,n,r){var o,s,a;function l(){return o.apply(i,arguments)}function d(e){return o.apply(i,e)}return o=i[e],s=n[e],a=i.ext[r],function(){var e=i._local,t=i._super,n=i._superApply;try{return i._local=a,i._super=l,i._superApply=d,s.apply(i,arguments)}finally{i._local=e,i._super=t,i._superApply=n}}}function C(e,t){return(void 0===e?k.Deferred(function(){this.resolve()}):k.Deferred(function(){this.resolveWith(e,t)})).promise()}function w(e,t){return(void 0===e?k.Deferred(function(){this.reject()}):k.Deferred(function(){this.rejectWith(e,t)})).promise()}function S(e,t){return function(){e.resolveWith(t)}}function E(e){var t=k.extend({},e.data()),e=t.json;return delete t.fancytree,delete t.uiFancytree,e&&(delete t.json,t=k.extend(t,e)),t}function T(e){return(""+e).replace(n,function(e){return i[e]})}function L(t){return t=t.toLowerCase(),function(e){return 0<=e.title.toLowerCase().indexOf(t)}}function A(e,t){var n,i,r;for(this.parent=e,this.tree=e.tree,this.ul=null,this.li=null,this.statusNodeType=null,this._isLoading=!1,this._error=null,this.data={},n=0,i=b.length;n<i;n++)this[r=b[n]]=t[r];for(r in null==this.unselectableIgnore&&null==this.unselectableStatus||(this.unselectable=!0),t.hideCheckbox&&k.error("'hideCheckbox' node option was removed in v2.23.0: use 'checkbox: false'"),t.data&&k.extend(this.data,t.data),t)s[r]||!this.tree.options.copyFunctionsToData&&k.isFunction(t[r])||a[r]||(this.data[r]=t[r]);null==this.key?this.tree.options.defaultKey?(this.key=""+this.tree.options.defaultKey(this),N(this.key,"defaultKey() must return a unique key")):this.key="_"+h._nextNodeKey++:this.key=""+this.key,t.active&&(N(null===this.tree.activeNode,"only one active node allowed"),this.tree.activeNode=this),t.selected&&(this.tree.lastSelectedNode=this),(e=t.children)?e.length?this._setChildren(e):this.children=this.lazy?[]:null:this.children=null,this.tree._callHook("treeRegisterNode",this.tree,!0,this)}function P(e){this.widget=e,this.$div=e.element,this.options=e.options,this.options&&(void 0!==this.options.lazyload&&k.error("The 'lazyload' event is deprecated since 2014-02-25. Use 'lazyLoad' (with uppercase L) instead."),void 0!==this.options.loaderror&&k.error("The 'loaderror' event was renamed since 2014-07-03. Use 'loadError' (with uppercase E) instead."),void 0!==this.options.fx&&k.error("The 'fx' option was replaced by 'toggleEffect' since 2014-11-30."),void 0!==this.options.removeNode&&k.error("The 'removeNode' event was replaced by 'modifyChild' since 2.20 (2016-09-10).")),this.ext={},this.types={},this.columns={},this.data=E(this.$div),this._id=""+(this.options.treeId||k.ui.fancytree._nextId++),this._ns=".fancytree-"+this._id,this.activeNode=null,this.focusNode=null,this._hasFocus=null,this._tempCache={},this._lastMousedownNode=null,this._enableUpdate=!0,this.lastSelectedNode=null,this.systemFocusElement=null,this.lastQuicksearchTerm="",this.lastQuicksearchTime=0,this.viewport=null,this.statusClassPropName="span",this.ariaPropName="li",this.nodeContainerAttrName="li",this.$div.find(">ul.fancytree-container").remove();e={tree:this};this.rootNode=new A(e,{title:"root",key:"root_"+this._id,children:null,expanded:!0}),this.rootNode.parent=null,e=k("<ul>",{id:"ft-id-"+this._id,class:"ui-fancytree fancytree-container fancytree-plain"}).appendTo(this.$div),this.$container=e,this.rootNode.ul=e[0],null==this.options.debugLevel&&(this.options.debugLevel=h.debugLevel)}k.ui.fancytree.warn("Fancytree: ignored duplicate include")});

/*! Extension 'jquery.fancytree.childcounter.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(i){"use strict";return i.ui.fancytree._FancytreeClass.prototype.countSelected=function(e){this.options;return this.getSelectedNodes(e).length},i.ui.fancytree._FancytreeNodeClass.prototype.updateCounters=function(){var e=this,n=i("span.fancytree-childcounter",e.span),t=e.tree.options.childcounter,o=e.countChildren(t.deep);!(e.data.childCounter=o)&&t.hideZeros||e.isExpanded()&&t.hideExpanded?n.remove():(n=!n.length?i("<span class='fancytree-childcounter'/>").appendTo(i("span.fancytree-icon,span.fancytree-custom-icon",e.span)):n).text(o),!t.deep||e.isTopLevel()||e.isRootNode()||e.parent.updateCounters()},i.ui.fancytree.prototype.widgetMethod1=function(e){this.tree;return e},i.ui.fancytree.registerExtension({name:"childcounter",version:"2.38.0",options:{deep:!0,hideZeros:!0,hideExpanded:!1},foo:42,_appendCounter:function(e){},treeInit:function(e){e.options,e.options.childcounter;this._superApply(arguments),this.$container.addClass("fancytree-ext-childcounter")},treeDestroy:function(e){this._superApply(arguments)},nodeRenderTitle:function(e,n){var t=e.node,o=e.options.childcounter,r=null==t.data.childCounter?t.countChildren(o.deep):+t.data.childCounter;this._super(e,n),!r&&o.hideZeros||t.isExpanded()&&o.hideExpanded||i("span.fancytree-icon,span.fancytree-custom-icon",t.span).append(i("<span class='fancytree-childcounter'/>").text(r))},nodeSetExpanded:function(e,n,t){var o=e.tree;e.node;return this._superApply(arguments).always(function(){o.nodeRenderTitle(e)})}}),i.ui.fancytree});

/*! Extension 'jquery.fancytree.clones.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(c){"use strict";var f=c.ui.fancytree.assert;function n(e,t,n){for(var r,s,i=3&e.length,o=e.length-i,l=n,a=3432918353,u=461845907,c=0;c<o;)s=255&e.charCodeAt(c)|(255&e.charCodeAt(++c))<<8|(255&e.charCodeAt(++c))<<16|(255&e.charCodeAt(++c))<<24,++c,l=27492+(65535&(r=5*(65535&(l=(l^=s=(65535&(s=(s=(65535&s)*a+(((s>>>16)*a&65535)<<16)&4294967295)<<15|s>>>17))*u+(((s>>>16)*u&65535)<<16)&4294967295)<<13|l>>>19))+((5*(l>>>16)&65535)<<16)&4294967295))+((58964+(r>>>16)&65535)<<16);switch(s=0,i){case 3:s^=(255&e.charCodeAt(c+2))<<16;case 2:s^=(255&e.charCodeAt(c+1))<<8;case 1:l^=s=(65535&(s=(s=(65535&(s^=255&e.charCodeAt(c)))*a+(((s>>>16)*a&65535)<<16)&4294967295)<<15|s>>>17))*u+(((s>>>16)*u&65535)<<16)&4294967295}return l^=e.length,l=2246822507*(65535&(l^=l>>>16))+((2246822507*(l>>>16)&65535)<<16)&4294967295,l=3266489909*(65535&(l^=l>>>13))+((3266489909*(l>>>16)&65535)<<16)&4294967295,l^=l>>>16,t?("0000000"+(l>>>0).toString(16)).substr(-8):l>>>0}return c.ui.fancytree._FancytreeNodeClass.prototype.getCloneList=function(e){var t,n=this.tree,r=n.refMap[this.refKey]||null,s=n.keyMap;return r&&(t=this.key,e?r=c.map(r,function(e){return s[e]}):(r=c.map(r,function(e){return e===t?null:s[e]})).length<1&&(r=null)),r},c.ui.fancytree._FancytreeNodeClass.prototype.isClone=function(){var e=this.refKey||null,e=e&&this.tree.refMap[e]||null;return!!(e&&1<e.length)},c.ui.fancytree._FancytreeNodeClass.prototype.reRegister=function(t,e){t=null==t?null:""+t,e=null==e?null:""+e;var n=this.tree,r=this.key,s=this.refKey,i=n.keyMap,o=n.refMap,l=o[s]||null,n=!1;return null!=t&&t!==this.key&&(i[t]&&c.error("[ext-clones] reRegister("+t+"): already exists: "+this),delete i[r],i[t]=this,l&&(o[s]=c.map(l,function(e){return e===r?t:e})),this.key=t,n=!0),null!=e&&e!==this.refKey&&(l&&(1===l.length?delete o[s]:o[s]=c.map(l,function(e){return e===r?null:e})),o[e]?o[e].append(t):o[e]=[this.key],this.refKey=e,n=!0),n},c.ui.fancytree._FancytreeNodeClass.prototype.setRefKey=function(e){return this.reRegister(null,e)},c.ui.fancytree._FancytreeClass.prototype.getNodesByRef=function(e,t){var n=this.keyMap,e=this.refMap[e]||null;return e=e&&(e=t?c.map(e,function(e){e=n[e];return e.isDescendantOf(t)?e:null}):c.map(e,function(e){return n[e]})).length<1?null:e},c.ui.fancytree._FancytreeClass.prototype.changeRefKey=function(e,t){var n,r=this.keyMap,s=this.refMap[e]||null;if(s){for(n=0;n<s.length;n++)r[s[n]].refKey=t;delete this.refMap[e],this.refMap[t]=s}},c.ui.fancytree.registerExtension({name:"clones",version:"2.38.0",options:{highlightActiveClones:!0,highlightClones:!1},treeCreate:function(e){this._superApply(arguments),e.tree.refMap={},e.tree.keyMap={}},treeInit:function(e){this.$container.addClass("fancytree-ext-clones"),f(null==e.options.defaultKey),e.options.defaultKey=function(e){return t=e,"id_"+(t=n(e=(e=c.map(t.getParentList(!1,!0),function(e){return e.refKey||e.key})).join("/"),!0))+n(t+e,!0);var t},this._superApply(arguments)},treeClear:function(e){return e.tree.refMap={},e.tree.keyMap={},this._superApply(arguments)},treeRegisterNode:function(e,t,n){var r,s,i=e.tree,o=i.keyMap,l=i.refMap,a=n.key,u=n&&null!=n.refKey?""+n.refKey:null;return n.isStatusNode()||(t?(null!=o[n.key]&&(s=o[n.key],s="clones.treeRegisterNode: duplicate key '"+n.key+"': /"+n.getPath(!0)+" => "+s.getPath(!0),i.error(s),c.error(s)),o[a]=n,u&&((r=l[u])?(r.push(a),2===r.length&&e.options.clones.highlightClones&&o[r[0]].renderStatus()):l[u]=[a])):(null==o[a]&&c.error("clones.treeRegisterNode: node.key not registered: "+n.key),delete o[a],u&&(r=l[u])&&((s=r.length)<=1?(f(1===s),f(r[0]===a),delete l[u]):(function(e,t){for(var n=e.length-1;0<=n;n--)if(e[n]===t)return e.splice(n,1)}(r,a),2===s&&e.options.clones.highlightClones&&o[r[0]].renderStatus())))),this._super(e,t,n)},nodeRenderStatus:function(e){var t,n=e.node,r=this._super(e);return e.options.clones.highlightClones&&(t=c(n[e.tree.statusClassPropName])).length&&n.isClone()&&t.addClass("fancytree-clone"),r},nodeSetActive:function(e,n,t){var r=e.tree.statusClassPropName,s=e.node,i=this._superApply(arguments);return e.options.clones.highlightActiveClones&&s.isClone()&&c.each(s.getCloneList(!0),function(e,t){c(t[r]).toggleClass("fancytree-active-clone",!1!==n)}),i}}),c.ui.fancytree});

/*! Extension 'jquery.fancytree.dnd.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","jquery-ui/ui/widgets/draggable","jquery-ui/ui/widgets/droppable","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(v){"use strict";var t=!1,g="fancytree-drop-accept",u="fancytree-drop-after",c="fancytree-drop-before",f="fancytree-drop-reject";function h(e){return 0===e?"":0<e?"+"+e:""+e}function r(e){var r=e.options.dnd||null,n=e.options.glyph||null;r&&(t||(v.ui.plugin.add("draggable","connectToFancytree",{start:function(e,r){var t=v(this).data("ui-draggable")||v(this).data("draggable"),a=r.helper.data("ftSourceNode")||null;if(a)return t.offset.click.top=-2,t.offset.click.left=16,a.tree.ext.dnd._onDragEvent("start",a,null,e,r,t)},drag:function(e,r){var t,a=v(this).data("ui-draggable")||v(this).data("draggable"),n=r.helper.data("ftSourceNode")||null,o=r.helper.data("ftTargetNode")||null,d=v.ui.fancytree.getNode(e.target),l=n&&n.tree.options.dnd;e.target&&!d&&0<v(e.target).closest("div.fancytree-drag-helper,#fancytree-drop-marker").length?(n||o||v.ui.fancytree).debug("Drag event over helper: ignored."):(r.helper.data("ftTargetNode",d),l&&l.updateHelper&&(t=n.tree._makeHookContext(n,e,{otherNode:d,ui:r,draggable:a,dropMarker:v("#fancytree-drop-marker")}),l.updateHelper.call(n.tree,n,t)),o&&o!==d&&o.tree.ext.dnd._onDragEvent("leave",o,n,e,r,a),d&&d.tree.options.dnd.dragDrop&&(d===o||d.tree.ext.dnd._onDragEvent("enter",d,n,e,r,a),d.tree.ext.dnd._onDragEvent("over",d,n,e,r,a)))},stop:function(e,r){var t=v(this).data("ui-draggable")||v(this).data("draggable"),a=r.helper.data("ftSourceNode")||null,n=r.helper.data("ftTargetNode")||null,o="mouseup"===e.type&&1===e.which;o||(a||n||v.ui.fancytree).debug("Drag was cancelled"),n&&(o&&n.tree.ext.dnd._onDragEvent("drop",n,a,e,r,t),n.tree.ext.dnd._onDragEvent("leave",n,a,e,r,t)),a&&a.tree.ext.dnd._onDragEvent("stop",a,null,e,r,t)}}),t=!0)),r&&r.dragStart&&e.widget.element.draggable(v.extend({addClasses:!1,appendTo:e.$container,containment:!1,delay:0,distance:4,revert:!1,scroll:!0,scrollSpeed:7,scrollSensitivity:10,connectToFancytree:!0,helper:function(e){var r,t,a=v.ui.fancytree.getNode(e.target);return a?(t=a.tree.options.dnd,r=v(a.span),(r=v("<div class='fancytree-drag-helper'><span class='fancytree-drag-helper-img' /></div>").css({zIndex:3,position:"relative"}).append(r.find("span.fancytree-title").clone())).data("ftSourceNode",a),n&&r.find(".fancytree-drag-helper-img").addClass(n.map._addClass+" "+n.map.dragHelper),t.initHelper&&t.initHelper.call(a.tree,a,{node:a,tree:a.tree,originalEvent:e,ui:{helper:r}}),r):"<div>ERROR?: helper requested but sourceNode not found</div>"},start:function(e,r){return!!r.helper.data("ftSourceNode")}},e.options.dnd.draggable)),r&&r.dragDrop&&e.widget.element.droppable(v.extend({addClasses:!1,tolerance:"intersect",greedy:!1},e.options.dnd.droppable))}return v.ui.fancytree.registerExtension({name:"dnd",version:"2.38.0",options:{autoExpandMS:1e3,draggable:null,droppable:null,focusOnClick:!1,preventVoidMoves:!0,preventRecursiveMoves:!0,smartRevert:!0,dropMarkerOffsetX:-24,dropMarkerInsertOffsetX:-16,dragStart:null,dragStop:null,initHelper:null,updateHelper:null,dragEnter:null,dragOver:null,dragExpand:null,dragDrop:null,dragLeave:null},treeInit:function(t){var e=t.tree;this._superApply(arguments),e.options.dnd.dragStart&&e.$container.on("mousedown",function(e){var r;t.options.dnd.focusOnClick&&((r=v.ui.fancytree.getNode(e))&&r.debug("Re-enable focus that was prevented by jQuery UI draggable."),setTimeout(function(){v(e.target).closest(":tabbable").focus()},10))}),r(e)},_setDndStatus:function(e,r,t,a,n){var o,d="center",l=this._local,s=this.options.dnd,i=this.options.glyph,p=e?v(e.span):null,e=v(r.span),r=e.find("span.fancytree-title");if(l.$dropMarker||(l.$dropMarker=v("<div id='fancytree-drop-marker'></div>").hide().css({"z-index":1e3}).prependTo(v(this.$div).parent()),i&&l.$dropMarker.addClass(i.map._addClass+" "+i.map.dropMarker)),"after"===a||"before"===a||"over"===a){switch(o=s.dropMarkerOffsetX||0,a){case"before":d="top",o+=s.dropMarkerInsertOffsetX||0;break;case"after":d="bottom",o+=s.dropMarkerInsertOffsetX||0}r={my:"left"+h(o)+" center",at:"left "+d,of:r},this.options.rtl&&(r.my="right"+h(-o)+" center",r.at="right "+d),l.$dropMarker.toggleClass(u,"after"===a).toggleClass("fancytree-drop-over","over"===a).toggleClass(c,"before"===a).toggleClass("fancytree-rtl",!!this.options.rtl).show().position(v.ui.fancytree.fixPositionOptions(r))}else l.$dropMarker.hide();p&&p.toggleClass(g,!0===n).toggleClass(f,!1===n),e.toggleClass("fancytree-drop-target","after"===a||"before"===a||"over"===a).toggleClass(u,"after"===a).toggleClass(c,"before"===a).toggleClass(g,!0===n).toggleClass(f,!1===n),t.toggleClass(g,!0===n).toggleClass(f,!1===n)},_onDragEvent:function(e,r,t,a,n,o){var d,l,s,i,p=this.options.dnd,g=this._makeHookContext(r,a,{otherNode:t,ui:n,draggable:o}),u=null,c=this,f=v(r.span);switch(p.smartRevert&&(o.options.revert="invalid"),e){case"start":r.isStatusNode()?u=!1:p.dragStart&&(u=p.dragStart(r,g)),!1===u?(this.debug("tree.dragStart() cancelled"),n.helper.trigger("mouseup").hide()):(p.smartRevert&&(d=r[g.tree.nodeContainerAttrName].getBoundingClientRect(),l=v(o.options.appendTo)[0].getBoundingClientRect(),o.originalPosition.left=Math.max(0,d.left-l.left),o.originalPosition.top=Math.max(0,d.top-l.top)),f.addClass("fancytree-drag-source"),v(document).on("keydown.fancytree-dnd,mousedown.fancytree-dnd",function(e){("keydown"===e.type&&e.which===v.ui.keyCode.ESCAPE||"mousedown"===e.type)&&c.ext.dnd._cancelDrag()}));break;case"enter":u=!!(i=(!p.preventRecursiveMoves||!r.isDescendantOf(t))&&(p.dragEnter?p.dragEnter(r,g):null))&&(v.isArray(i)?{over:0<=v.inArray("over",i),before:0<=v.inArray("before",i),after:0<=v.inArray("after",i)}:{over:!0===i||"over"===i,before:!0===i||"before"===i,after:!0===i||"after"===i}),n.helper.data("enterResponse",u);break;case"over":s=null,!1===(l=n.helper.data("enterResponse"))||("string"==typeof l?s=l:(i=f.offset(),i={x:(i={x:a.pageX-i.left,y:a.pageY-i.top}).x/f.width(),y:i.y/f.height()},l.after&&.75<i.y||!l.over&&l.after&&.5<i.y?s="after":l.before&&i.y<=.25||!l.over&&l.before&&i.y<=.5?s="before":l.over&&(s="over"),p.preventVoidMoves&&(r===t?(this.debug("    drop over source node prevented"),s=null):"before"===s&&t&&r===t.getNextSibling()?(this.debug("    drop after source node prevented"),s=null):"after"===s&&t&&r===t.getPrevSibling()?(this.debug("    drop before source node prevented"),s=null):"over"===s&&t&&t.parent===r&&t.isLastSibling()&&(this.debug("    drop last child over own parent prevented"),s=null)),n.helper.data("hitMode",s))),"before"===s||"after"===s||!p.autoExpandMS||!1===r.hasChildren()||r.expanded||p.dragExpand&&!1===p.dragExpand(r,g)||r.scheduleAction("expand",p.autoExpandMS),s&&p.dragOver&&(g.hitMode=s,u=p.dragOver(r,g)),l=!1!==u&&null!==s,p.smartRevert&&(o.options.revert=!l),this._local._setDndStatus(t,r,n.helper,s,l);break;case"drop":(s=n.helper.data("hitMode"))&&p.dragDrop&&(g.hitMode=s,p.dragDrop(r,g));break;case"leave":r.scheduleAction("cancel"),n.helper.data("enterResponse",null),n.helper.data("hitMode",null),this._local._setDndStatus(t,r,n.helper,"out",void 0),p.dragLeave&&p.dragLeave(r,g);break;case"stop":f.removeClass("fancytree-drag-source"),v(document).off(".fancytree-dnd"),p.dragStop&&p.dragStop(r,g);break;default:v.error("Unsupported drag event: "+e)}return u},_cancelDrag:function(){var e=v.ui.ddmanager.current;e&&e.cancel()}}),v.ui.fancytree});

/*! Extension 'jquery.fancytree.dnd5.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(c){"use strict";var l,i,u=c.ui.fancytree,n=/Mac/.test(navigator.platform),f="fancytree-drag-source",p="fancytree-drag-remove",v="fancytree-drop-accept",y="fancytree-drop-after",b="fancytree-drop-before",h="fancytree-drop-over",m="fancytree-drop-reject",E="fancytree-drop-target",g="application/x-fancytree-node",D=null,w=null,N=null,x=null,S=null,d=null,s=null,k=null,C=null,M=null;function A(){N=w=d=k=s=M=S=null,x&&x.removeClass(f+" "+p),x=null,D&&D.hide(),i&&(i.remove(),i=null)}function T(e){return 0===e?"":0<e?"+"+e:""+e}function I(e,r){var t,o=r.tree,a=r.dataTransfer;"dragstart"===e.type?(r.effectAllowed=o.options.dnd5.effectAllowed,r.dropEffect=o.options.dnd5.dropEffectDefault):(r.effectAllowed=k,r.dropEffect=s),r.dropEffectSuggested=(t=e,o=(e=o).options.dnd5.dropEffectDefault,n?t.metaKey&&t.altKey||t.ctrlKey?o="link":t.metaKey?o="move":t.altKey&&(o="copy"):t.ctrlKey?o="copy":t.shiftKey?o="move":t.altKey&&(o="link"),o!==d&&e.info("evalEffectModifiers: "+t.type+" - evalEffectModifiers(): "+d+" -> "+o),d=o),r.isMove="move"===r.dropEffect,r.files=a.files||[]}function O(e,r,t){var o=r.tree,a=r.dataTransfer;return"dragstart"!==e.type&&k!==r.effectAllowed&&o.warn("effectAllowed should only be changed in dragstart event: "+e.type+": data.effectAllowed changed from "+k+" -> "+r.effectAllowed),!1===t&&(o.info("applyDropEffectCallback: allowDrop === false"),r.effectAllowed="none",r.dropEffect="none"),r.isMove="move"===r.dropEffect,"dragstart"===e.type&&(k=r.effectAllowed,s=r.dropEffect),a.effectAllowed=k,a.dropEffect=s}function P(e,r){if(r.options.dnd5.scroll&&(g=r.tree,d=e,a=g.options.dnd5,n=g.$scrollParent[0],l=a.scrollSensitivity,p=a.scrollSpeed,o=0,n!==document&&"HTML"!==n.tagName?(a=g.$scrollParent.offset(),i=n.scrollTop,a.top+n.offsetHeight-d.pageY<l?0<n.scrollHeight-g.$scrollParent.innerHeight()-i&&(n.scrollTop=o=i+p):0<i&&d.pageY-a.top<l&&(n.scrollTop=o=i-p)):0<(i=c(document).scrollTop())&&d.pageY-i<l?(o=i-p,c(document).scrollTop(o)):c(window).height()-(d.pageY-i)<l&&(o=i+p,c(document).scrollTop(o)),o&&g.debug("autoScroll: "+o+"px")),!r.node)return r.tree.warn("Ignored dragover for non-node"),C;var t,o,a=null,n=r.tree,d=n.options,s=d.dnd5,l=r.node,i=r.otherNode,f="center",p=c(l.span),g=p.find("span.fancytree-title");if(!1===S)return n.debug("Ignored dragover, since dragenter returned false."),!1;if("string"==typeof S&&c.error("assert failed: dragenter returned string"),o=p.offset(),p=(e.pageY-o.top)/p.height(),void 0===e.pageY&&n.warn("event.pageY is undefined: see issue #1013."),S.after&&.75<p||!S.over&&S.after&&.5<p?a="after":S.before&&p<=.25||!S.over&&S.before&&p<=.5?a="before":S.over&&(a="over"),s.preventVoidMoves&&"move"===r.dropEffect&&(l===i?(l.debug("Drop over source node prevented."),a=null):"before"===a&&i&&l===i.getNextSibling()?(l.debug("Drop after source node prevented."),a=null):"after"===a&&i&&l===i.getPrevSibling()?(l.debug("Drop before source node prevented."),a=null):"over"===a&&i&&i.parent===l&&i.isLastSibling()&&(l.debug("Drop last child over own parent prevented."),a=null)),(r.hitMode=a)&&s.dragOver&&(I(e,r),s.dragOver(l,r),O(e,r,!!a),a=r.hitMode),"after"===(C=a)||"before"===a||"over"===a){switch(t=s.dropMarkerOffsetX||0,a){case"before":f="top",t+=s.dropMarkerInsertOffsetX||0;break;case"after":f="bottom",t+=s.dropMarkerInsertOffsetX||0}g={my:"left"+T(t)+" center",at:"left "+f,of:g},d.rtl&&(g.my="right"+T(-t)+" center",g.at="right "+f),D.toggleClass(y,"after"===a).toggleClass(h,"over"===a).toggleClass(b,"before"===a).show().position(u.fixPositionOptions(g))}else D.hide();return c(l.span).toggleClass(E,"after"===a||"before"===a||"over"===a).toggleClass(y,"after"===a).toggleClass(b,"before"===a).toggleClass(v,"over"===a).toggleClass(m,!1===a),a}function j(e){var r,t,o,a=this,n=a.options.dnd5,d=null,s=u.getNode(e),l=e.dataTransfer||e.originalEvent.dataTransfer,i={tree:a,node:s,options:a.options,originalEvent:e.originalEvent,widget:a.widget,hitMode:S,dataTransfer:l,otherNode:w||null,otherNodeList:N||null,otherNodeData:null,useDefaultImage:!0,dropEffect:void 0,dropEffectSuggested:void 0,effectAllowed:void 0,files:null,isCancelled:void 0,isMove:void 0};switch(e.type){case"dragenter":if(M=null,!s){a.debug("Ignore non-node "+e.type+": "+e.target.tagName+"."+e.target.className),S=!1;break}if(c(s.span).addClass(h).removeClass(v+" "+m),o=0<=c.inArray(g,l.types),n.preventNonNodes&&!o){s.debug("Reject dropping a non-node."),S=!1;break}if(n.preventForeignNodes&&(!w||w.tree!==s.tree)){s.debug("Reject dropping a foreign node."),S=!1;break}if(n.preventSameParent&&i.otherNode&&i.otherNode.tree===s.tree&&s.parent===i.otherNode.parent){s.debug("Reject dropping as sibling (same parent)."),S=!1;break}if(n.preventRecursion&&i.otherNode&&i.otherNode.tree===s.tree&&s.isDescendantOf(i.otherNode)){s.debug("Reject dropping below own ancestor."),S=!1;break}if(n.preventLazyParents&&!s.isLoaded()){s.warn("Drop over unloaded target node prevented."),S=!1;break}D.show(),I(e,i),p=n.dragEnter(s,i),p=!!(f=p)&&(f=c.isPlainObject(f)?{over:!!f.over,before:!!f.before,after:!!f.after}:c.isArray(f)?{over:0<=c.inArray("over",f),before:0<=c.inArray("before",f),after:0<=c.inArray("after",f)}:{over:!0===f||"over"===f,before:!0===f||"before"===f,after:!0===f||"after"===f},0!==Object.keys(f).length&&f),O(e,i,d=(S=p)&&(p.over||p.before||p.after));break;case"dragover":if(!s){a.debug("Ignore non-node "+e.type+": "+e.target.tagName+"."+e.target.className);break}I(e,i),d=!!(C=P(e,i)),("over"===C||!1===C)&&!s.expanded&&!1!==s.hasChildren()?M?!(n.autoExpandMS&&Date.now()-M>n.autoExpandMS)||s.isLoading()||n.dragExpand&&!1===n.dragExpand(s,i)||s.setExpanded():M=Date.now():M=null;break;case"dragleave":if(!s){a.debug("Ignore non-node "+e.type+": "+e.target.tagName+"."+e.target.className);break}if(!c(s.span).hasClass(h)){s.debug("Ignore dragleave (multi).");break}c(s.span).removeClass(h+" "+v+" "+m),s.scheduleAction("cancel"),n.dragLeave(s,i),D.hide();break;case"drop":if(0<=c.inArray(g,l.types)&&(t=l.getData(g),a.info(e.type+": getData('application/x-fancytree-node'): '"+t+"'")),t||(t=l.getData("text"),a.info(e.type+": getData('text'): '"+t+"'")),t)try{void 0!==(r=JSON.parse(t)).title&&(i.otherNodeData=r)}catch(e){}a.debug(e.type+": nodeData: '"+t+"', otherNodeData: ",i.otherNodeData),c(s.span).removeClass(h+" "+v+" "+m),i.hitMode=C,I(e,i),i.isCancelled=!C;var f=w&&w.span,p=w&&w.tree;n.dragDrop(s,i),e.preventDefault(),f&&!document.body.contains(f)&&(p===a?(a.debug("Drop handler removed source element: generating dragEnd."),n.dragEnd(w,i)):a.warn("Drop handler removed source element: dragend event may be lost.")),A()}if(d)return e.preventDefault(),!1}return c.ui.fancytree.getDragNodeList=function(){return N||[]},c.ui.fancytree.getDragNode=function(){return w},c.ui.fancytree.registerExtension({name:"dnd5",version:"2.38.0",options:{autoExpandMS:1500,dropMarkerInsertOffsetX:-16,dropMarkerOffsetX:-24,dropMarkerParent:"body",multiSource:!1,effectAllowed:"all",dropEffectDefault:"move",preventForeignNodes:!1,preventLazyParents:!0,preventNonNodes:!1,preventRecursion:!0,preventSameParent:!1,preventVoidMoves:!0,scroll:!0,scrollSensitivity:20,scrollSpeed:5,setTextTypeJson:!1,sourceCopyHook:null,dragStart:null,dragDrag:c.noop,dragEnd:c.noop,dragEnter:null,dragOver:c.noop,dragExpand:c.noop,dragDrop:c.noop,dragLeave:c.noop},treeInit:function(e){var r=e.tree,t=e.options,o=t.glyph||null,a=t.dnd5;0<=c.inArray("dnd",t.extensions)&&c.error("Extensions 'dnd' and 'dnd5' are mutually exclusive."),a.dragStop&&c.error("dragStop is not used by ext-dnd5. Use dragEnd instead."),null!=a.preventRecursiveMoves&&c.error("preventRecursiveMoves was renamed to preventRecursion."),a.dragStart&&u.overrideMethod(e.options,"createNode",function(e,r){this._super.apply(this,arguments),r.node.span?r.node.span.draggable=!0:r.node.warn("Cannot add `draggable`: no span tag")}),this._superApply(arguments),this.$container.addClass("fancytree-ext-dnd5"),e=c("<span>").appendTo(this.$container),this.$scrollParent=e.scrollParent(),e.remove(),(D=c("#fancytree-drop-marker")).length||(D=c("<div id='fancytree-drop-marker'></div>").hide().css({"z-index":1e3,"pointer-events":"none"}).prependTo(a.dropMarkerParent),o&&u.setSpanIcon(D[0],o.map._addClass,o.map.dropMarker)),D.toggleClass("fancytree-rtl",!!t.rtl),a.dragStart&&r.$container.on("dragstart drag dragend",function(e){var r,t=this,o=t.options.dnd5,a=u.getNode(e),n=e.dataTransfer||e.originalEvent.dataTransfer,d={tree:t,node:a,options:t.options,originalEvent:e.originalEvent,widget:t.widget,dataTransfer:n,useDefaultImage:!0,dropEffect:void 0,dropEffectSuggested:void 0,effectAllowed:void 0,files:void 0,isCancelled:void 0,isMove:void 0};switch(e.type){case"dragstart":if(!a)return t.info("Ignored dragstart on a non-node."),!1;w=a,N=!1===o.multiSource?[a]:!0===o.multiSource?a.isSelected()?t.getSelectedNodes():[a]:o.multiSource(a,d),(x=c(c.map(N,function(e){return e.span}))).addClass(f);var s=a.toDict(!0,o.sourceCopyHook);s.treeId=a.tree._id,r=JSON.stringify(s);try{n.setData(g,r),n.setData("text/html",c(a.span).html()),n.setData("text/plain",a.title)}catch(e){t.warn("Could not set data (IE only accepts 'text') - "+e)}return(o.setTextTypeJson?n.setData("text",r):n.setData("text",a.title),I(e,d),!1===o.dragStart(a,d))?(A(),!1):(O(e,d),i=null,d.useDefaultImage&&(l=c(a.span).find(".fancytree-title"),N&&1<N.length&&(i=c("<span class='fancytree-childcounter'/>").text("+"+(N.length-1)).appendTo(l)),n.setDragImage&&n.setDragImage(l[0],-10,-10)),!0);case"drag":I(e,d),o.dragDrag(a,d),O(e,d),x.toggleClass(p,d.isMove);break;case"dragend":I(e,d),A(),d.isCancelled=!C,o.dragEnd(a,d,!C)}}.bind(r)),a.dragEnter&&r.$container.on("dragenter dragover dragleave drop",j.bind(r))}}),c.ui.fancytree});

/*! Extension 'jquery.fancytree.edit.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(l){"use strict";var t=/Mac/.test(navigator.platform),c=l.ui.fancytree.escapeHtml,o=l.ui.fancytree.unescapeHtml;return l.ui.fancytree._FancytreeNodeClass.prototype.editStart=function(){var t,i=this,e=this.tree,n=e.ext.edit,r=e.options.edit,a=l(".fancytree-title",i.span),s={node:i,tree:e,options:e.options,isNew:l(i[e.statusClassPropName]).hasClass("fancytree-edit-new"),orgTitle:i.title,input:null,dirty:!1};if(!1===r.beforeEdit.call(i,{type:"beforeEdit"},s))return!1;l.ui.fancytree.assert(!n.currentNode,"recursive edit"),n.currentNode=this,n.eventData=s,e.widget._unbind(),n.lastDraggableAttrValue=i.span.draggable,n.lastDraggableAttrValue&&(i.span.draggable=!1),l(document).on("mousedown.fancytree-edit",function(e){l(e.target).hasClass("fancytree-edit-input")||i.editEnd(!0,e)}),t=l("<input />",{class:"fancytree-edit-input",type:"text",value:e.options.escapeTitles?s.orgTitle:o(s.orgTitle)}),n.eventData.input=t,null!=r.adjustWidthOfs&&t.width(a.width()+r.adjustWidthOfs),null!=r.inputCss&&t.css(r.inputCss),a.html(t),t.focus().change(function(e){t.addClass("fancytree-edit-dirty")}).on("keydown",function(e){switch(e.which){case l.ui.keyCode.ESCAPE:i.editEnd(!1,e);break;case l.ui.keyCode.ENTER:return i.editEnd(!0,e),!1}e.stopPropagation()}).blur(function(e){return i.editEnd(!0,e)}),r.edit.call(i,{type:"edit"},s)},l.ui.fancytree._FancytreeNodeClass.prototype.editEnd=function(e,t){var i,n=this,r=this.tree,a=r.ext.edit,s=a.eventData,o=r.options.edit,d=l(".fancytree-title",n.span).find("input.fancytree-edit-input");return o.trim&&d.val(l.trim(d.val())),i=d.val(),s.dirty=i!==n.title,s.originalEvent=t,!1===e?s.save=!1:s.isNew?s.save=""!==i:s.save=s.dirty&&""!==i,!1!==o.beforeClose.call(n,{type:"beforeClose"},s)&&((!s.save||!1!==o.save.call(n,{type:"save"},s))&&(d.removeClass("fancytree-edit-dirty").off(),l(document).off(".fancytree-edit"),s.save?(n.setTitle(r.options.escapeTitles?i:c(i)),n.setFocus()):s.isNew?(n.remove(),n=s.node=null,a.relatedNode.setFocus()):(n.renderTitle(),n.setFocus()),a.eventData=null,a.currentNode=null,a.relatedNode=null,r.widget._bind(),n&&a.lastDraggableAttrValue&&(n.span.draggable=!0),r.$container.get(0).focus({preventScroll:!0}),s.input=null,o.close.call(n,{type:"close"},s),!0))},l.ui.fancytree._FancytreeNodeClass.prototype.editCreateNode=function(e,t){var i,n=this.tree,r=this;e=e||"child",null==t?t={title:""}:"string"==typeof t?t={title:t}:l.ui.fancytree.assert(l.isPlainObject(t)),"child"!==e||this.isExpanded()||!1===this.hasChildren()?((i=this.addNode(t,e)).match=!0,l(i[n.statusClassPropName]).removeClass("fancytree-hide").addClass("fancytree-match"),i.makeVisible().done(function(){l(i[n.statusClassPropName]).addClass("fancytree-edit-new"),r.tree.ext.edit.relatedNode=r,i.editStart()})):this.setExpanded().done(function(){r.editCreateNode(e,t)})},l.ui.fancytree._FancytreeClass.prototype.isEditing=function(){return this.ext.edit?this.ext.edit.currentNode:null},l.ui.fancytree._FancytreeNodeClass.prototype.isEditing=function(){return!!this.tree.ext.edit&&this.tree.ext.edit.currentNode===this},l.ui.fancytree.registerExtension({name:"edit",version:"2.38.0",options:{adjustWidthOfs:4,allowEmpty:!1,inputCss:{minWidth:"3em"},triggerStart:["f2","mac+enter","shift+click"],trim:!0,beforeClose:l.noop,beforeEdit:l.noop,close:l.noop,edit:l.noop,save:l.noop},currentNode:null,treeInit:function(e){var n=e.tree;this._superApply(arguments),this.$container.addClass("fancytree-ext-edit").on("fancytreebeforeupdateviewport",function(e,t){var i=n.isEditing();i&&(i.info("Cancel edit due to scroll event."),i.editEnd(!1,e))})},nodeClick:function(e){var t=l.ui.fancytree.eventToString(e.originalEvent),i=e.options.edit.triggerStart;return"shift+click"===t&&0<=l.inArray("shift+click",i)&&e.originalEvent.shiftKey||"click"===t&&0<=l.inArray("clickActive",i)&&e.node.isActive()&&!e.node.isEditing()&&l(e.originalEvent.target).hasClass("fancytree-title")?(e.node.editStart(),!1):this._superApply(arguments)},nodeDblclick:function(e){return 0<=l.inArray("dblclick",e.options.edit.triggerStart)?(e.node.editStart(),!1):this._superApply(arguments)},nodeKeydown:function(e){switch(e.originalEvent.which){case 113:if(0<=l.inArray("f2",e.options.edit.triggerStart))return e.node.editStart(),!1;break;case l.ui.keyCode.ENTER:if(0<=l.inArray("mac+enter",e.options.edit.triggerStart)&&t)return e.node.editStart(),!1}return this._superApply(arguments)}}),l.ui.fancytree});

/*! Extension 'jquery.fancytree.filter.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(g){"use strict";var m="__not_found__",x=g.ui.fancytree.escapeHtml;function v(e){return(e+"").replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1")}function C(e,t,i){for(var n=[],a=1;a<t.length;a++){var r=t[a].length+(1===a?0:1)+(n[n.length-1]||0);n.push(r)}var s=e.split("");return i?n.forEach(function(e){s[e]="\ufff7"+s[e]+"\ufff8"}):n.forEach(function(e){s[e]="<mark>"+s[e]+"</mark>"}),s.join("")}return g.ui.fancytree._FancytreeClass.prototype._applyFilterImpl=function(n,a,e){var t,r,s,l,o,h,d=0,i=this.options,c=i.escapeTitles,u=i.autoCollapse,p=g.extend({},i.filter,e),f="hide"===p.mode,y=!!p.leavesOnly&&!a;if("string"==typeof n){if(""===n)return this.warn("Fancytree passing an empty string as a filter is handled as clearFilter()."),void this.clearFilter();t=p.fuzzy?n.split("").map(v).reduce(function(e,t){return e+"([^"+t+"]*)"+t},""):v(n),r=new RegExp(t,"i"),s=new RegExp(v(n),"gi"),c&&(l=new RegExp(v("\ufff7"),"g"),o=new RegExp(v("\ufff8"),"g")),n=function(e){if(!e.title)return!1;var t,i=c?e.title:0<=(t=e.title).indexOf(">")?g("<div/>").html(t).text():t,t=i.match(r);return t&&p.highlight&&(c?(h=p.fuzzy?C(i,t,c):i.replace(s,function(e){return"\ufff7"+e+"\ufff8"}),e.titleWithHighlight=x(h).replace(l,"<mark>").replace(o,"</mark>")):p.fuzzy?e.titleWithHighlight=C(i,t):e.titleWithHighlight=i.replace(s,function(e){return"<mark>"+e+"</mark>"})),!!t}}return this.enableFilter=!0,this.lastFilterArgs=arguments,e=this.enableUpdate(!1),this.$div.addClass("fancytree-ext-filter"),f?this.$div.addClass("fancytree-ext-filter-hide"):this.$div.addClass("fancytree-ext-filter-dimm"),this.$div.toggleClass("fancytree-ext-filter-hide-expanders",!!p.hideExpanders),this.rootNode.subMatchCount=0,this.visit(function(e){delete e.match,delete e.titleWithHighlight,e.subMatchCount=0}),(t=this.getRootNode()._findDirectChild(m))&&t.remove(),i.autoCollapse=!1,this.visit(function(t){if(!y||null==t.children){var e=n(t),i=!1;if("skip"===e)return t.visit(function(e){e.match=!1},!0),"skip";e||!a&&"branch"!==e||!t.parent.match||(i=e=!0),e&&(d++,t.match=!0,t.visitParents(function(e){e!==t&&(e.subMatchCount+=1),!p.autoExpand||i||e.expanded||(e.setExpanded(!0,{noAnimation:!0,noEvents:!0,scrollIntoView:!1}),e._filterAutoExpanded=!0)},!0))}}),i.autoCollapse=u,0===d&&p.nodata&&f&&(t=p.nodata,!0===(t=g.isFunction(t)?t():t)?t={}:"string"==typeof t&&(t={title:t}),t=g.extend({statusNodeType:"nodata",key:m,title:this.options.strings.noData},t),this.getRootNode().addNode(t).match=!0),this._callHook("treeStructureChanged",this,"applyFilter"),this.enableUpdate(e),d},g.ui.fancytree._FancytreeClass.prototype.filterNodes=function(e,t){return"boolean"==typeof t&&(t={leavesOnly:t},this.warn("Fancytree.filterNodes() leavesOnly option is deprecated since 2.9.0 / 2015-04-19. Use opts.leavesOnly instead.")),this._applyFilterImpl(e,!1,t)},g.ui.fancytree._FancytreeClass.prototype.filterBranches=function(e,t){return this._applyFilterImpl(e,!0,t)},g.ui.fancytree._FancytreeClass.prototype.updateFilter=function(){this.enableFilter&&this.lastFilterArgs&&this.options.filter.autoApply?this._applyFilterImpl.apply(this,this.lastFilterArgs):this.warn("updateFilter(): no filter active.")},g.ui.fancytree._FancytreeClass.prototype.clearFilter=function(){var t,e=this.getRootNode()._findDirectChild(m),i=this.options.escapeTitles,n=this.options.enhanceTitle,a=this.enableUpdate(!1);e&&e.remove(),delete this.rootNode.match,delete this.rootNode.subMatchCount,this.visit(function(e){e.match&&e.span&&(t=g(e.span).find(">span.fancytree-title"),i?t.text(e.title):t.html(e.title),n&&n({type:"enhanceTitle"},{node:e,$title:t})),delete e.match,delete e.subMatchCount,delete e.titleWithHighlight,e.$subMatchBadge&&(e.$subMatchBadge.remove(),delete e.$subMatchBadge),e._filterAutoExpanded&&e.expanded&&e.setExpanded(!1,{noAnimation:!0,noEvents:!0,scrollIntoView:!1}),delete e._filterAutoExpanded}),this.enableFilter=!1,this.lastFilterArgs=null,this.$div.removeClass("fancytree-ext-filter fancytree-ext-filter-dimm fancytree-ext-filter-hide"),this._callHook("treeStructureChanged",this,"clearFilter"),this.enableUpdate(a)},g.ui.fancytree._FancytreeClass.prototype.isFilterActive=function(){return!!this.enableFilter},g.ui.fancytree._FancytreeNodeClass.prototype.isMatched=function(){return!(this.tree.enableFilter&&!this.match)},g.ui.fancytree.registerExtension({name:"filter",version:"2.38.0",options:{autoApply:!0,autoExpand:!1,counter:!0,fuzzy:!1,hideExpandedCounter:!0,hideExpanders:!1,highlight:!0,leavesOnly:!1,nodata:!0,mode:"dimm"},nodeLoadChildren:function(e,t){var i=e.tree;return this._superApply(arguments).done(function(){i.enableFilter&&i.lastFilterArgs&&e.options.filter.autoApply&&i._applyFilterImpl.apply(i,i.lastFilterArgs)})},nodeSetExpanded:function(e,t,i){var n=e.node;return delete n._filterAutoExpanded,!t&&e.options.filter.hideExpandedCounter&&n.$subMatchBadge&&n.$subMatchBadge.show(),this._superApply(arguments)},nodeRenderStatus:function(e){var t=e.node,i=e.tree,n=e.options.filter,a=g(t.span).find("span.fancytree-title"),r=g(t[i.statusClassPropName]),s=e.options.enhanceTitle,l=e.options.escapeTitles,e=this._super(e);return r.length&&i.enableFilter&&(r.toggleClass("fancytree-match",!!t.match).toggleClass("fancytree-submatch",!!t.subMatchCount).toggleClass("fancytree-hide",!(t.match||t.subMatchCount)),!n.counter||!t.subMatchCount||t.isExpanded()&&n.hideExpandedCounter?t.$subMatchBadge&&t.$subMatchBadge.hide():(t.$subMatchBadge||(t.$subMatchBadge=g("<span class='fancytree-childcounter'/>"),g("span.fancytree-icon, span.fancytree-custom-icon",t.span).append(t.$subMatchBadge)),t.$subMatchBadge.show().text(t.subMatchCount)),!t.span||t.isEditing&&t.isEditing.call(t)||(t.titleWithHighlight?a.html(t.titleWithHighlight):l?a.text(t.title):a.html(t.title),s&&s({type:"enhanceTitle"},{node:t,$title:a}))),e}}),g.ui.fancytree});

/*! Extension 'jquery.fancytree.glyph.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(i){"use strict";var d=i.ui.fancytree,n={awesome3:{_addClass:"",checkbox:"icon-check-empty",checkboxSelected:"icon-check",checkboxUnknown:"icon-check icon-muted",dragHelper:"icon-caret-right",dropMarker:"icon-caret-right",error:"icon-exclamation-sign",expanderClosed:"icon-caret-right",expanderLazy:"icon-angle-right",expanderOpen:"icon-caret-down",loading:"icon-refresh icon-spin",nodata:"icon-meh",noExpander:"",radio:"icon-circle-blank",radioSelected:"icon-circle",doc:"icon-file-alt",docOpen:"icon-file-alt",folder:"icon-folder-close-alt",folderOpen:"icon-folder-open-alt"},awesome4:{_addClass:"fa",checkbox:"fa-square-o",checkboxSelected:"fa-check-square-o",checkboxUnknown:"fa-square fancytree-helper-indeterminate-cb",dragHelper:"fa-arrow-right",dropMarker:"fa-long-arrow-right",error:"fa-warning",expanderClosed:"fa-caret-right",expanderLazy:"fa-angle-right",expanderOpen:"fa-caret-down",loading:{html:"<span class='fa fa-spinner fa-pulse' />"},nodata:"fa-meh-o",noExpander:"",radio:"fa-circle-thin",radioSelected:"fa-circle",doc:"fa-file-o",docOpen:"fa-file-o",folder:"fa-folder-o",folderOpen:"fa-folder-open-o"},awesome5:{_addClass:"",checkbox:"far fa-square",checkboxSelected:"far fa-check-square",checkboxUnknown:"fas fa-square fancytree-helper-indeterminate-cb",radio:"far fa-circle",radioSelected:"fas fa-circle",radioUnknown:"far fa-dot-circle",dragHelper:"fas fa-arrow-right",dropMarker:"fas fa-long-arrow-alt-right",error:"fas fa-exclamation-triangle",expanderClosed:"fas fa-caret-right",expanderLazy:"fas fa-angle-right",expanderOpen:"fas fa-caret-down",loading:"fas fa-spinner fa-pulse",nodata:"far fa-meh",noExpander:"",doc:"far fa-file",docOpen:"far fa-file",folder:"far fa-folder",folderOpen:"far fa-folder-open"},bootstrap3:{_addClass:"glyphicon",checkbox:"glyphicon-unchecked",checkboxSelected:"glyphicon-check",checkboxUnknown:"glyphicon-expand fancytree-helper-indeterminate-cb",dragHelper:"glyphicon-play",dropMarker:"glyphicon-arrow-right",error:"glyphicon-warning-sign",expanderClosed:"glyphicon-menu-right",expanderLazy:"glyphicon-menu-right",expanderOpen:"glyphicon-menu-down",loading:"glyphicon-refresh fancytree-helper-spin",nodata:"glyphicon-info-sign",noExpander:"",radio:"glyphicon-remove-circle",radioSelected:"glyphicon-ok-circle",doc:"glyphicon-file",docOpen:"glyphicon-file",folder:"glyphicon-folder-close",folderOpen:"glyphicon-folder-open"},material:{_addClass:"material-icons",checkbox:{text:"check_box_outline_blank"},checkboxSelected:{text:"check_box"},checkboxUnknown:{text:"indeterminate_check_box"},dragHelper:{text:"play_arrow"},dropMarker:{text:"arrow-forward"},error:{text:"warning"},expanderClosed:{text:"chevron_right"},expanderLazy:{text:"last_page"},expanderOpen:{text:"expand_more"},loading:{text:"autorenew",addClass:"fancytree-helper-spin"},nodata:{text:"info"},noExpander:{text:""},radio:{text:"radio_button_unchecked"},radioSelected:{text:"radio_button_checked"},doc:{text:"insert_drive_file"},docOpen:{text:"insert_drive_file"},folder:{text:"folder"},folderOpen:{text:"folder_open"}}};function l(e,r,n,a,o){var t=a.map,c=t[o],d=i(r),a=d.find(".fancytree-childcounter"),t=n+" "+(t._addClass||"");"string"==typeof(c=i.isFunction(c)?c.call(this,e,r,o):c)?(r.innerHTML="",d.attr("class",t+" "+c).append(a)):c&&(c.text?r.textContent=""+c.text:c.html?r.innerHTML=c.html:r.innerHTML="",d.attr("class",t+" "+(c.addClass||"")).append(a))}return i.ui.fancytree.registerExtension({name:"glyph",version:"2.38.0",options:{preset:null,map:{}},treeInit:function(e){var r=e.tree,e=e.options.glyph;e.preset?(d.assert(!!n[e.preset],"Invalid value for `options.glyph.preset`: "+e.preset),e.map=i.extend({},n[e.preset],e.map)):r.warn("ext-glyph: missing `preset` option."),this._superApply(arguments),r.$container.addClass("fancytree-ext-glyph")},nodeRenderStatus:function(e){var r,n,a=e.node,o=i(a.span),t=e.options.glyph,c=this._super(e);return a.isRootNode()||((n=o.children(".fancytree-expander").get(0))&&(r=a.expanded&&a.hasChildren()?"expanderOpen":a.isUndefined()?"expanderLazy":a.hasChildren()?"expanderClosed":"noExpander",l(a,n,"fancytree-expander",t,r)),(n=(a.tr?i("td",a.tr).find(".fancytree-checkbox"):o.children(".fancytree-checkbox")).get(0))&&(e=d.evalOption("checkbox",a,a,t,!1),a.parent&&a.parent.radiogroup||"radio"===e?l(a,n,"fancytree-checkbox fancytree-radio",t,r=a.selected?"radioSelected":"radio"):l(a,n,"fancytree-checkbox",t,r=a.selected?"checkboxSelected":a.partsel?"checkboxUnknown":"checkbox")),(n=o.children(".fancytree-icon").get(0))&&(r=a.statusNodeType||(a.folder?a.expanded&&a.hasChildren()?"folderOpen":"folder":a.expanded?"docOpen":"doc"),l(a,n,"fancytree-icon",t,r))),c},nodeSetStatus:function(e,r,n,a){var o,t=e.options.glyph,c=e.node,e=this._superApply(arguments);return"error"!==r&&"loading"!==r&&"nodata"!==r||(c.parent?(o=i(".fancytree-expander",c.span).get(0))&&l(c,o,"fancytree-expander",t,r):(o=i(".fancytree-statusnode-"+r,c[this.nodeContainerAttrName]).find(".fancytree-icon").get(0))&&l(c,o,"fancytree-icon",t,r)),e}}),i.ui.fancytree});

/*! Extension 'jquery.fancytree.gridnav.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree","./jquery.fancytree.table"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree.table"),module.exports=e(require("jquery"))):e(jQuery)}(function(l){"use strict";var p=l.ui.keyCode,o={text:[p.UP,p.DOWN],checkbox:[p.UP,p.DOWN,p.LEFT,p.RIGHT],link:[p.UP,p.DOWN,p.LEFT,p.RIGHT],radiobutton:[p.UP,p.DOWN,p.LEFT,p.RIGHT],"select-one":[p.LEFT,p.RIGHT],"select-multiple":[p.LEFT,p.RIGHT]};function a(e,t){var n,i,r,o,a,s,u=e.closest("td"),c=null;switch(t){case p.LEFT:c=u.prev();break;case p.RIGHT:c=u.next();break;case p.UP:case p.DOWN:for(n=u.parent(),r=n,a=u.get(0),s=0,r.children().each(function(){return this!==a&&(o=l(this).prop("colspan"),void(s+=o||1))}),i=s;(n=t===p.UP?n.prev():n.next()).length&&(n.is(":hidden")||!(c=function(e,t){var n,i=null,r=0;return e.children().each(function(){return t<=r?(i=l(this),!1):(n=l(this).prop("colspan"),void(r+=n||1))}),i}(n,i))||!c.find(":input,a").length););}return c}return l.ui.fancytree.registerExtension({name:"gridnav",version:"2.38.0",options:{autofocusInput:!1,handleCursorKeys:!0},treeInit:function(n){this._requireExtension("table",!0,!0),this._superApply(arguments),this.$container.addClass("fancytree-ext-gridnav"),this.$container.on("focusin",function(e){var t=l.ui.fancytree.getNode(e.target);t&&!t.isActive()&&(e=n.tree._makeHookContext(t,e),n.tree._callHook("nodeSetActive",e,!0))})},nodeSetActive:function(e,t,n){var i=e.options.gridnav,r=e.node,o=e.originalEvent||{},o=l(o.target).is(":input");t=!1!==t,this._superApply(arguments),t&&(e.options.titlesTabbable?(o||(l(r.span).find("span.fancytree-title").focus(),r.setFocus()),e.tree.$container.attr("tabindex","-1")):i.autofocusInput&&!o&&l(r.tr||r.span).find(":input:enabled").first().focus())},nodeKeydown:function(e){var t,n,i=e.options.gridnav,r=e.originalEvent,e=l(r.target);return e.is(":input:enabled")?t=e.prop("type"):e.is("a")&&(t="link"),t&&i.handleCursorKeys?!((t=o[t])&&0<=l.inArray(r.which,t)&&(n=a(e,r.which))&&n.length)||(n.find(":input:enabled,a").focus(),!1):this._superApply(arguments)}}),l.ui.fancytree});

/*! Extension 'jquery.fancytree.multi.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(o){"use strict";return o.ui.fancytree.registerExtension({name:"multi",version:"2.38.0",options:{allowNoSelect:!1,mode:"sameParent"},treeInit:function(e){this._superApply(arguments),this.$container.addClass("fancytree-ext-multi"),1===e.options.selectMode&&o.error("Fancytree ext-multi: selectMode: 1 (single) is not compatible.")},nodeClick:function(e){var t=e.tree,i=e.node,r=t.getActiveNode()||t.getFirstChild(),n="checkbox"===e.targetType,c="expander"===e.targetType;switch(o.ui.fancytree.eventToString(e.originalEvent)){case"click":if(c)break;n||(t.selectAll(!1),i.setSelected());break;case"shift+click":t.visitRows(function(e){if(e.setSelected(),e===i)return!1},{start:r,reverse:r.isBelowOf(i)});break;case"ctrl+click":case"meta+click":return void i.toggleSelected()}return this._superApply(arguments)},nodeKeydown:function(e){var t=e.tree,i=e.node,r=e.originalEvent;switch(o.ui.fancytree.eventToString(r)){case"up":case"down":t.selectAll(!1),i.navigate(r.which,!0),t.getActiveNode().setSelected();break;case"shift+up":case"shift+down":i.navigate(r.which,!0),t.getActiveNode().setSelected()}return this._superApply(arguments)}}),o.ui.fancytree});

/*! Extension 'jquery.fancytree.persist.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(y){"use strict";var t=null,o=null,i=null,s=y.ui.fancytree.assert,u="active",v="expanded",p="focus",f="selected";try{s(window.localStorage&&window.localStorage.getItem),o={get:function(e){return window.localStorage.getItem(e)},set:function(e,t){window.localStorage.setItem(e,t)},remove:function(e){window.localStorage.removeItem(e)}}}catch(e){y.ui.fancytree.warn("Could not access window.localStorage",e)}try{s(window.sessionStorage&&window.sessionStorage.getItem),i={get:function(e){return window.sessionStorage.getItem(e)},set:function(e,t){window.sessionStorage.setItem(e,t)},remove:function(e){window.sessionStorage.removeItem(e)}}}catch(e){y.ui.fancytree.warn("Could not access window.sessionStorage",e)}return"function"==typeof Cookies?t={get:Cookies.get,set:function(e,t){Cookies.set(e,t,this.options.persist.cookie)},remove:Cookies.remove}:y&&"function"==typeof y.cookie&&(t={get:y.cookie,set:function(e,t){y.cookie.set(e,t,this.options.persist.cookie)},remove:y.removeCookie}),y.ui.fancytree._FancytreeClass.prototype.clearPersistData=function(e){var t=this.ext.persist,o=t.cookiePrefix;0<=(e=e||"active expanded focus selected").indexOf(u)&&t._data(o+u,null),0<=e.indexOf(v)&&t._data(o+v,null),0<=e.indexOf(p)&&t._data(o+p,null),0<=e.indexOf(f)&&t._data(o+f,null)},y.ui.fancytree._FancytreeClass.prototype.clearCookies=function(e){return this.warn("'tree.clearCookies()' is deprecated since v2.27.0: use 'clearPersistData()' instead."),this.clearPersistData(e)},y.ui.fancytree._FancytreeClass.prototype.getPersistData=function(){var e=this.ext.persist,t=e.cookiePrefix,o=e.cookieDelimiter,i={};return i[u]=e._data(t+u),i[v]=(e._data(t+v)||"").split(o),i[f]=(e._data(t+f)||"").split(o),i[p]=e._data(t+p),i},y.ui.fancytree.registerExtension({name:"persist",version:"2.38.0",options:{cookieDelimiter:"~",cookiePrefix:void 0,cookie:{raw:!1,expires:"",path:"",domain:"",secure:!1},expandLazy:!1,expandOpts:void 0,fireActivate:!0,overrideSource:!0,store:"auto",types:"active expanded focus selected"},_data:function(e,t){var o=this._local.store;if(void 0===t)return o.get.call(this,e);null===t?o.remove.call(this,e):o.set.call(this,e,t)},_appendKey:function(e,t,o){t=""+t;var i=this._local,s=this.options.persist.cookieDelimiter,r=i.cookiePrefix+e,n=i._data(r),e=n?n.split(s):[],n=y.inArray(t,e);0<=n&&e.splice(n,1),o&&e.push(t),i._data(r,e.join(s))},treeInit:function(e){var a=e.tree,c=e.options,d=this._local,l=this.options.persist;return d.cookiePrefix=l.cookiePrefix||"fancytree-"+a._id+"-",d.storeActive=0<=l.types.indexOf(u),d.storeExpanded=0<=l.types.indexOf(v),d.storeSelected=0<=l.types.indexOf(f),d.storeFocus=0<=l.types.indexOf(p),d.store=null,"auto"===l.store&&(l.store=o?"local":"cookie"),y.isPlainObject(l.store)?d.store=l.store:"cookie"===l.store?d.store=t:"local"!==l.store&&"session"!==l.store||(d.store="local"===l.store?o:i),s(d.store,"Need a valid store."),a.$div.on("fancytreeinit",function(e){var t,o,i,s,r,n;!1!==a._triggerTreeEvent("beforeRestore",null,{})&&(i=d._data(d.cookiePrefix+p),s=!1===l.fireActivate,r=d._data(d.cookiePrefix+v),n=r&&r.split(l.cookieDelimiter),(d.storeExpanded?function e(t,o,i,s,r){var n,a,c,d,l=!1,u=t.options.persist.expandOpts,p=[],f=[];for(i=i||[],r=r||y.Deferred(),n=0,c=i.length;n<c;n++)a=i[n],(d=t.getNodeByKey(a))?s&&d.isUndefined()?(l=!0,t.debug("_loadLazyNodes: "+d+" is lazy: loading..."),"expand"===s?p.push(d.setExpanded(!0,u)):p.push(d.load())):(t.debug("_loadLazyNodes: "+d+" already loaded."),d.setExpanded(!0,u)):(f.push(a),t.debug("_loadLazyNodes: "+d+" was not yet found."));return y.when.apply(y,p).always(function(){if(l&&0<f.length)e(t,o,f,s,r);else{if(f.length)for(t.warn("_loadLazyNodes: could not load those keys: ",f),n=0,c=f.length;n<c;n++)a=i[n],o._appendKey(v,i[n],!1);r.resolve()}}),r}(a,d,n,!!l.expandLazy&&"expand",null):(new y.Deferred).resolve()).done(function(){if(d.storeSelected){if(r=d._data(d.cookiePrefix+f))for(n=r.split(l.cookieDelimiter),t=0;t<n.length;t++)(o=a.getNodeByKey(n[t]))?(void 0===o.selected||l.overrideSource&&!1===o.selected)&&(o.selected=!0,o.renderStatus()):d._appendKey(f,n[t],!1);3===a.options.selectMode&&a.visit(function(e){if(e.selected)return e.fixSelection3AfterClick(),"skip"})}d.storeActive&&(!(r=d._data(d.cookiePrefix+u))||!c.persist.overrideSource&&a.activeNode||(o=a.getNodeByKey(r))&&(o.debug("persist: set active",r),o.setActive(!0,{noFocus:!0,noEvents:s}))),d.storeFocus&&i&&(o=a.getNodeByKey(i))&&(a.options.titlesTabbable?y(o.span).find(".fancytree-title"):y(a.$container)).focus(),a._triggerTreeEvent("restore",null,{})}))}),this._superApply(arguments)},nodeSetActive:function(e,t,o){var i=this._local;return t=!1!==t,t=this._superApply(arguments),i.storeActive&&i._data(i.cookiePrefix+u,this.activeNode?this.activeNode.key:null),t},nodeSetExpanded:function(e,t,o){var i=e.node,s=this._local;return t=!1!==t,e=this._superApply(arguments),s.storeExpanded&&s._appendKey(v,i.key,t),e},nodeSetFocus:function(e,t){var o=this._local;return t=!1!==t,t=this._superApply(arguments),o.storeFocus&&o._data(o.cookiePrefix+p,this.focusNode?this.focusNode.key:null),t},nodeSetSelected:function(e,t,o){var i=e.tree,s=e.node,r=this._local;return t=!1!==t,t=this._superApply(arguments),r.storeSelected&&(3===i.options.selectMode?(i=(i=y.map(i.getSelectedNodes(!0),function(e){return e.key})).join(e.options.persist.cookieDelimiter),r._data(r.cookiePrefix+f,i)):r._appendKey(f,s.key,s.selected)),t}}),y.ui.fancytree});

/*! Extension 'jquery.fancytree.table.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(g){"use strict";var x=g.ui.fancytree.assert;function b(e,n){e.visit(function(e){var t=e.tr;if(t&&(t.style.display=e.hide||!n?"none":""),!e.expanded)return"skip"})}return g.ui.fancytree.registerExtension({name:"table",version:"2.38.0",options:{checkboxColumnIdx:null,indentation:16,mergeStatusColumns:!0,nodeColumnIdx:0},treeInit:function(e){var t,n,r,o=e.tree,d=e.options,s=d.table,a=o.widget.element;if(null!=s.customStatus&&(null==d.renderStatusColumns?(o.warn("The 'customStatus' option is deprecated since v2.15.0. Use 'renderStatusColumns' instead."),d.renderStatusColumns=s.customStatus):g.error("The 'customStatus' option is deprecated since v2.15.0. Use 'renderStatusColumns' only instead.")),d.renderStatusColumns&&!0===d.renderStatusColumns&&(d.renderStatusColumns=d.renderColumns),a.addClass("fancytree-container fancytree-ext-table"),(r=a.find(">tbody")).length||(a.find(">tr").length&&g.error("Expected table > tbody > tr. If you see this please open an issue."),r=g("<tbody>").appendTo(a)),o.tbody=r[0],o.columnCount=g("thead >tr",a).last().find(">th",a).length,(n=r.children("tr").first()).length)e=n.children("td").length,o.columnCount&&e!==o.columnCount&&(o.warn("Column count mismatch between thead ("+o.columnCount+") and tbody ("+e+"): using tbody."),o.columnCount=e),n=n.clone();else for(x(1<=o.columnCount,"Need either <thead> or <tbody> with <td> elements to determine column count."),n=g("<tr />"),t=0;t<o.columnCount;t++)n.append("<td />");n.find(">td").eq(s.nodeColumnIdx).html("<span class='fancytree-node' />"),d.aria&&(n.attr("role","row"),n.find("td").attr("role","gridcell")),o.rowFragment=document.createDocumentFragment(),o.rowFragment.appendChild(n.get(0)),r.empty(),o.statusClassPropName="tr",o.ariaPropName="tr",this.nodeContainerAttrName="tr",o.$container=a,this._superApply(arguments),g(o.rootNode.ul).remove(),o.rootNode.ul=null,this.$container.attr("tabindex",d.tabindex),d.aria&&o.$container.attr("role","treegrid").attr("aria-readonly",!0)},nodeRemoveChildMarkup:function(e){e.node.visit(function(e){e.tr&&(g(e.tr).remove(),e.tr=null)})},nodeRemoveMarkup:function(e){var t=e.node;t.tr&&(g(t.tr).remove(),t.tr=null),this.nodeRemoveChildMarkup(e)},nodeRender:function(e,t,n,r,o){var d,s,a,i,l,u,c,p,h,m,f=e.tree,y=e.node,C=e.options,v=!y.parent;if(!1!==f._enableUpdate){if(o||(e.hasCollapsedParents=y.parent&&!y.parent.expanded),!v)if(y.tr&&t&&this.nodeRemoveMarkup(e),y.tr)t?this.nodeRenderTitle(e):this.nodeRenderStatus(e);else{if(e.hasCollapsedParents&&!n)return;l=f.rowFragment.firstChild.cloneNode(!0),p=function(e){var t,n,r=e.parent,o=r?r.children:null;if(o&&1<o.length&&o[0]!==e)for(n=o[g.inArray(e,o)-1],x(n.tr);n.children&&n.children.length&&(t=n.children[n.children.length-1]).tr;)n=t;else n=r;return n}(y),x(p),(!0===r&&o||n&&e.hasCollapsedParents)&&(l.style.display="none"),p.tr?(h=p.tr,m=l,h.parentNode.insertBefore(m,h.nextSibling)):(x(!p.parent,"prev. row must have a tr, or be system root"),h=f.tbody,p=l,h.insertBefore(p,h.firstChild)),y.tr=l,y.key&&C.generateIds&&(y.tr.id=C.idPrefix+y.key),(y.tr.ftnode=y).span=g("span.fancytree-node",y.tr).get(0),this.nodeRenderTitle(e),C.createNode&&C.createNode.call(f,{type:"createNode"},e)}if(C.renderNode&&C.renderNode.call(f,{type:"renderNode"},e),(d=y.children)&&(v||n||y.expanded))for(a=0,i=d.length;a<i;a++)(c=g.extend({},e,{node:d[a]})).hasCollapsedParents=c.hasCollapsedParents||!y.expanded,this.nodeRender(c,t,n,r,!0);d&&!o&&(u=y.tr||null,s=f.tbody.firstChild,y.visit(function(e){var t;e.tr&&(e.parent.expanded||"none"===e.tr.style.display||(e.tr.style.display="none",b(e,!1)),e.tr.previousSibling!==u&&(y.debug("_fixOrder: mismatch at node: "+e),t=u?u.nextSibling:s,f.tbody.insertBefore(e.tr,t)),u=e.tr)}))}},nodeRenderTitle:function(e,t){var n=e.tree,r=e.node,o=e.options,d=r.isStatusNode(),s=this._super(e,t);return r.isRootNode()||(o.checkbox&&!d&&null!=o.table.checkboxColumnIdx&&(t=g("span.fancytree-checkbox",r.span),g(r.tr).find("td").eq(+o.table.checkboxColumnIdx).html(t)),this.nodeRenderStatus(e),d?o.renderStatusColumns?o.renderStatusColumns.call(n,{type:"renderStatusColumns"},e):o.table.mergeStatusColumns&&r.isTopLevel()&&g(r.tr).find(">td").eq(0).prop("colspan",n.columnCount).text(r.title).addClass("fancytree-status-merged").nextAll().remove():o.renderColumns&&o.renderColumns.call(n,{type:"renderColumns"},e)),s},nodeRenderStatus:function(e){var t=e.node,n=e.options;this._super(e),g(t.tr).removeClass("fancytree-node"),e=(t.getLevel()-1)*n.table.indentation,n.rtl?g(t.span).css({paddingRight:e+"px"}):g(t.span).css({paddingLeft:e+"px"})},nodeSetExpanded:function(t,n,r){if(n=!1!==n,t.node.expanded&&n||!t.node.expanded&&!n)return this._superApply(arguments);var o=new g.Deferred,e=g.extend({},r,{noEvents:!0,noAnimation:!0});function d(e){b(t.node,n),e?n&&t.options.autoScroll&&!r.noAnimation&&t.node.hasChildren()?t.node.getLastChild().scrollIntoView(!0,{topNode:t.node}).always(function(){r.noEvents||t.tree._triggerNodeEvent(n?"expand":"collapse",t),o.resolveWith(t.node)}):(r.noEvents||t.tree._triggerNodeEvent(n?"expand":"collapse",t),o.resolveWith(t.node)):(r.noEvents||t.tree._triggerNodeEvent(n?"expand":"collapse",t),o.rejectWith(t.node))}return r=r||{},this._super(t,n,e).done(function(){d(!0)}).fail(function(){d(!1)}),o.promise()},nodeSetStatus:function(e,t,n,r){return"ok"!==t||(e=(e=e.node).children?e.children[0]:null)&&e.isStatusNode()&&g(e.tr).remove(),this._superApply(arguments)},treeClear:function(e){return this.nodeRemoveChildMarkup(this._makeHookContext(this.rootNode)),this._superApply(arguments)},treeDestroy:function(e){return this.$container.find("tbody").empty(),this.$source&&this.$source.removeClass("fancytree-helper-hidden"),this._superApply(arguments)}}),g.ui.fancytree});

/*! Extension 'jquery.fancytree.themeroller.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(l){"use strict";return l.ui.fancytree.registerExtension({name:"themeroller",version:"2.38.0",options:{activeClass:"ui-state-active",addClass:"ui-corner-all",focusClass:"ui-state-focus",hoverClass:"ui-state-hover",selectedClass:"ui-state-highlight"},treeInit:function(e){var s=e.widget.element,t=e.options.themeroller;this._superApply(arguments),"TABLE"===s[0].nodeName?(s.addClass("ui-widget ui-corner-all"),s.find(">thead tr").addClass("ui-widget-header"),s.find(">tbody").addClass("ui-widget-conent")):s.addClass("ui-widget ui-widget-content ui-corner-all"),s.on("mouseenter mouseleave",".fancytree-node",function(e){var s=l.ui.fancytree.getNode(e.target),e="mouseenter"===e.type;l(s.tr||s.span).toggleClass(t.hoverClass+" "+t.addClass,e)})},treeDestroy:function(e){this._superApply(arguments),e.widget.element.removeClass("ui-widget ui-widget-content ui-corner-all")},nodeRenderStatus:function(e){var s={},t=e.node,a=l(t.tr||t.span),i=e.options.themeroller;this._super(e),s[i.activeClass]=!1,s[i.focusClass]=!1,s[i.selectedClass]=!1,t.isActive()&&(s[i.activeClass]=!0),t.hasFocus()&&(s[i.focusClass]=!0),t.isSelected()&&!t.isActive()&&(s[i.selectedClass]=!0),a.toggleClass(i.activeClass,s[i.activeClass]),a.toggleClass(i.focusClass,s[i.focusClass]),a.toggleClass(i.selectedClass,s[i.selectedClass]),a.addClass(i.addClass)}}),l.ui.fancytree});

/*! Extension 'jquery.fancytree.wide.min.js' */!function(e){"function"==typeof define&&define.amd?define(["jquery","./jquery.fancytree"],e):"object"==typeof module&&module.exports?(require("./jquery.fancytree"),module.exports=e(require("jquery"))):e(jQuery)}(function(o){"use strict";var p=/^([+-]?(?:\d+|\d*\.\d+))([a-z]*|%)$/;function f(e,t){var a=o("#"+(e="fancytree-style-"+e));if(t){a.length||(a=o("<style />").attr("id",e).addClass("fancytree-style").prop("type","text/css").appendTo("head"));try{a.html(t)}catch(e){a[0].styleSheet.cssText=t}return a}a.remove()}function u(e,t,a,n,l,i){for(var s="#"+e+" span.fancytree-level-",c=[],r=0;r<t;r++)c.push(s+(r+1)+" span.fancytree-title { padding-left: "+(r*a+n)+i+"; }");return c.push("#"+e+" div.ui-effects-wrapper ul li span.fancytree-title, #"+e+" li.fancytree-animating span.fancytree-title { padding-left: "+l+i+"; position: static; width: auto; }"),c.join("\n")}return o.ui.fancytree.registerExtension({name:"wide",version:"2.38.0",options:{iconWidth:null,iconSpacing:null,labelSpacing:null,levelOfs:null},treeCreate:function(e){this._superApply(arguments),this.$container.addClass("fancytree-ext-wide");var t=e.options.wide,a=o("<li id='fancytreeTemp'><span class='fancytree-node'><span class='fancytree-icon' /><span class='fancytree-title' /></span><ul />").appendTo(e.tree.$container),n=a.find(".fancytree-icon"),l=a.find("ul"),i=t.iconSpacing||n.css("margin-left"),s=t.iconWidth||n.css("width"),c=t.labelSpacing||"3px",r=t.levelOfs||l.css("padding-left");a.remove(),n=i.match(p)[2],i=parseFloat(i,10),t=c.match(p)[2],c=parseFloat(c,10),l=s.match(p)[2],s=parseFloat(s,10),a=r.match(p)[2],n===l&&a===l&&t===l||o.error("iconWidth, iconSpacing, and levelOfs must have the same css measure unit"),this._local.measureUnit=l,this._local.levelOfs=parseFloat(r),this._local.lineOfs=(1+(e.options.checkbox?1:0)+(!1===e.options.icon?0:1))*(s+i)+i,this._local.labelOfs=c,this._local.maxDepth=10,f(c=this.$container.uniqueId().attr("id"),u(c,this._local.maxDepth,this._local.levelOfs,this._local.lineOfs,this._local.labelOfs,this._local.measureUnit))},treeDestroy:function(e){return f(this.$container.attr("id"),null),this._superApply(arguments)},nodeRenderStatus:function(e){var t=e.node,a=t.getLevel(),n=this._super(e);return a>this._local.maxDepth&&(e=this.$container.attr("id"),this._local.maxDepth*=2,t.debug("Define global ext-wide css up to level "+this._local.maxDepth),f(e,u(e,this._local.maxDepth,this._local.levelOfs,this._local.lineOfs,this._local.labelSpacing,this._local.measureUnit))),o(t.span).addClass("fancytree-level-"+a),n}}),o.ui.fancytree});
// Value returned by `require('jquery.fancytree')`
return $.ui.fancytree;
}));  // End of closure
