using BaseGIS.Core.Entities;
using BaseGIS.Core.Interfaces;
using BaseGIS.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BaseGIS.Infrastructure.Repositories
{
    public class TableInfoRepository : ITableInfoRepository
    {
        private readonly ApplicationDbContext _context;

        public TableInfoRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<TableInfo> GetByIdAsync(int id)
        {
            return await _context.TableInfos
                .Include(t => t.FieldInfos)
                .Include(t => t.GroupInfo)
                .Include(t => t.Symbologies)
                .FirstOrDefaultAsync(t => t.Id == id);
        }

        public async Task<List<TableInfo>> GetAllAsync()
        {
            return await _context.TableInfos
                .Include(t => t.FieldInfos)
                .Include(t => t.GroupInfo)
                .Include(t => t.Symbologies)
                .ToListAsync();
        }

        public async Task AddAsync(TableInfo tableInfo)
        {
            await _context.TableInfos.AddAsync(tableInfo);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(TableInfo tableInfo)
        {
            _context.TableInfos.Update(tableInfo);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var tableInfo = await _context.TableInfos.FindAsync(id);
            if (tableInfo != null)
            {
                _context.TableInfos.Remove(tableInfo);
                await _context.SaveChangesAsync();
            }
        }
    }
}