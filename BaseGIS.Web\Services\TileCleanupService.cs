﻿namespace BaseGIS.Web.Services
{
    public class TileCleanupService : BackgroundService
    {
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                // حذف تایل‌های قدیمی‌تر از 7 روز
                var tilesDir = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "tiles");
                foreach (var file in Directory.GetFiles(tilesDir, "*.mvt", SearchOption.AllDirectories))
                {
                    if (File.GetLastWriteTime(file) < DateTime.UtcNow.AddDays(-7))
                    {
                        File.Delete(file);
                    }
                }
                await Task.Delay(TimeSpan.FromHours(24), stoppingToken);
            }
        }
    }
}
