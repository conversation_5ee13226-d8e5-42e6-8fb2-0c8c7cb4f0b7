﻿
.appFont {
    font-family: 'AppFont' !important;
    font-weight: normal !important;
}

.appFontBold {
    font-family: 'AppFontBold' !important;
    font-weight: normal !important;
}

@font-face {
    font-family: 'AppFont';
    src: url('../fonts/Shabnam-FD.eot');
    src: url('../fonts/Shabnam-FD.woff') format('woff'), url('../fonts/Shabnam-FD.ttf') format('truetype'), url('../fonts/Shabnam-FD.svg#Shabnam-FD') format('svg'), url('../fonts/Shabnam-FD.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'AppFontBold';
    src: url('../fonts/Shabnam-Bold-FD-FD.eot');
    src: url('../fonts/Shabnam-Bold-FD.woff') format('woff'), url('../fonts/Shabnam-Bold-FD.ttf') format('truetype'), url('../fonts/Shabnam-Bold-FD.svg#Shabnam-Bold-FD') format('svg'), url('../fonts/Shabnam-Bold-FD.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
}

body {
    background: linear-gradient(135deg, #4b3b7b 0%, #2a1e47 100%);
    color: #fff;
    font-family: 'AppFont', Tahoma, Geneva, Verdana, sans-serif;
}

.login-container {
    min-height: 100vh;
}

.login-card {
    max-width: 450px;
    width: 100%;
    border-radius: 15px;
    border: none;
}

.card-body {
    padding: 3rem !important;
}

.btn-primary {
    background-color: #1a73e8;
    border-color: #1a73e8;
    font-weight: 500;
}

.btn-pink {
    background-color: #ff4081;
    border-color: #ff4081;
    color: #fff;
    font-weight: 500;
}

    .btn-pink:hover {
        background-color: #e03572;
        border-color: #e03572;
    }

.form-control {
    border-radius: 8px;
    padding: 0.6rem 0.8rem;
    font-size: 0.9rem;
}

.form-control-lg {
    font-size: 1.1rem;
}

.form-check-label {
    font-size: 0.9rem;
    color: #555;
}

.social-icons i {
    font-size: 1.2rem;
}

.text-white-50 {
    opacity: 0.7;
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.3;
}

.lead {
    font-size: 1.1rem;
    opacity: 0.9;
}

.text-danger {
    font-size: 0.9rem;
}

/* RTL Adjustments */
html[lang="fa"] {
    direction: rtl;
    text-align: right;
}

    html[lang="fa"] .text-end {
        text-align: left !important;
    }

    html[lang="fa"] .social-icons a {
        margin-left: 1rem;
        margin-right: 0;
    }

        html[lang="fa"] .social-icons a:last-child {
            margin-left: 0;
        }

@media (max-width: 767.98px) {
    .login-container .row > div:first-child {
        padding: 3rem 1.5rem;
        text-align: center;
    }

    .login-card {
        margin: 0 1.5rem;
    }

    h1 {
        font-size: 2rem;
    }
}
