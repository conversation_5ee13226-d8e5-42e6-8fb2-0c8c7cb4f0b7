import { BaseComponent } from '../components/base-component.js';

class LayerPropertyPanelComponent extends BaseComponent {
    getDefaultOptions() {
        return {
            ...super.getDefaultOptions(),
            layerId: null,
            layerData: null,
            onPropertyChange: null,
            onSymbologyChange: null,
            onClose: null,
            onAfterSave: null,
            showToast: window.showToast || null
        };
    }

    init() {
        this.render();
    }

    render() {
        this.container.innerHTML = this.getPanelHtml();
        this.bindEvents();
        if (this.options.layerId) {
            this.loadLegend(this.options.layerData?.symbology || 'default');
        }
    }

    getPanelHtml() {
        const d = this.options.layerData || {};
        return `
            <div class="layer-property-panel">
                <div class="modal-header">
                    <h5 class="modal-title">ویژگی‌های لایه</h5>
                    <button type="button" class="btn-close" data-action="close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">نام لایه</label>
                        <input type="text" class="form-control" id="layerName" value="${d.name || ''}" readonly />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">برچسب</label>
                        <input type="text" class="form-control" id="layerLabel" value="${d.label || ''}" />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">شفافیت</label>
                        <input type="range" class="form-range" id="layerOpacity" min="0" max="100" value="${d.opacity ?? 100}" />
                        <span id="opacityValue">${d.opacity ?? 100}%</span>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">نمادگذاری</label>
                        <select class="form-select" id="layerSymbology">
                            <option value="default" ${d.symbology === 'default' ? 'selected' : ''}>پیش‌فرض</option>
                            <option value="custom" ${d.symbology === 'custom' ? 'selected' : ''}>سفارشی</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">وضعیت</label>
                        <select class="form-select" id="layerActive">
                            <option value="1" ${d.active ? 'selected' : ''}>فعال</option>
                            <option value="0" ${!d.active ? 'selected' : ''}>غیرفعال</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">قفل بودن</label>
                        <input type="checkbox" id="layerLocked" ${d.locked ? 'checked' : ''} />
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ترتیب نمایش</label>
                        <input type="number" class="form-control" id="layerOrder" value="${d.order ?? ''}" min="0" />
                    </div>
                    <div class="mb-3" id="legendContainer">
                        <div class="text-center text-muted">در حال بارگذاری legend...</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-action="delete">حذف لایه</button>
                    <button type="button" class="btn btn-primary" data-action="save">ذخیره</button>
                    <button type="button" class="btn btn-secondary" data-action="close">بستن</button>
                </div>
            </div>
        `;
    }

    bindEvents() {
        this.container.querySelector('[data-action="close"]').addEventListener('click', () => {
            if (this.options.onClose) this.options.onClose();
            this.hide();
        });
        this.container.querySelector('[data-action="save"]').addEventListener('click', () => this.save());
        this.container.querySelector('[data-action="delete"]').addEventListener('click', () => this.deleteLayer());
        this.container.querySelector('#layerSymbology').addEventListener('change', (e) => {
            if (this.options.onSymbologyChange) {
                this.options.onSymbologyChange(e.target.value);
            }
            this.loadLegend(e.target.value);
        });
        this.container.querySelector('#layerOpacity').addEventListener('input', (e) => {
            this.container.querySelector('#opacityValue').innerText = e.target.value + '%';
        });
    }

    show(layerId, layerData) {
        this.options.layerId = layerId;
        this.options.layerData = layerData;
        this.render();
        this.container.style.display = 'block';
        this.loadLegend(layerData?.symbology || 'default');
    }

    hide() {
        this.container.style.display = 'none';
    }

    async loadLegend(symbology) {
        const legendContainer = this.container.querySelector('#legendContainer');
        legendContainer.innerHTML = '<div class="text-center text-muted">در حال بارگذاری legend...</div>';
        try {
            // فرض: endpoint سرور برای legend: /Rest/Legend
            const res = await fetch(`/Rest/Legend?layerId=${this.options.layerId}&symbologyId=${symbology}`);
            if (!res.ok) throw new Error('خطا در دریافت legend');
            const data = await res.json();
            if (Array.isArray(data) && data.length > 0) {
                legendContainer.innerHTML = data.map(l => `<div><img src="${l.icon}" style="height:16px;width:16px;"> ${l.title}</div>`).join('');
            } else {
                legendContainer.innerHTML = '<div class="alert alert-info">legend یافت نشد</div>';
            }
        } catch (e) {
            legendContainer.innerHTML = `<div class="alert alert-danger">${e.message}</div>`;
        }
    }

    save() {
        // اعتبارسنجی
        const label = this.container.querySelector('#layerLabel').value.trim();
        if (!label) {
            this.showToast('برچسب لایه نمی‌تواند خالی باشد', 'warning');
            return;
        }
        const opacity = parseInt(this.container.querySelector('#layerOpacity').value);
        const symbology = this.container.querySelector('#layerSymbology').value;
        const active = this.container.querySelector('#layerActive').value === '1';
        const locked = this.container.querySelector('#layerLocked').checked;
        const order = parseInt(this.container.querySelector('#layerOrder').value);
        const data = {
            layerId: this.options.layerId,
            label,
            opacity,
            symbology,
            active,
            locked,
            order
        };
        if (this.options.onPropertyChange) {
            this.options.onPropertyChange(data);
        }
        if (this.options.onAfterSave) {
            this.options.onAfterSave(data);
        }
        this.showToast('تغییرات با موفقیت ذخیره شد', 'success');
        this.hide();
    }

    deleteLayer() {
        if (!confirm('آیا از حذف این لایه اطمینان دارید؟')) return;
        // ارسال event حذف
        if (this.options.onDelete) {
            this.options.onDelete(this.options.layerId);
        }
        this.showToast('لایه حذف شد', 'info');
        this.hide();
    }

    showToast(msg, type = 'info', duration = 2500) {
        if (this.options.showToast) {
            this.options.showToast(msg, type, duration);
        } else {
            alert(msg);
        }
    }
}

window.ComponentFactory.register('layer-property-panel', LayerPropertyPanelComponent); 