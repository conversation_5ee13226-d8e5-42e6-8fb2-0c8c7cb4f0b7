!function(o,e){"function"==typeof define&&define.amd?define(["exports","echarts"],e):"object"==typeof exports&&"string"!=typeof exports.nodeName?e(0,require("echarts/lib/echarts")):e(0,o.echarts)}(this,function(o,e){e?e.registerTheme("green",{color:["#408829","#68a54a","#a9cba2","#86b379","#397b29","#8abb6f","#759c6a","#bfd3b7"],title:{textStyle:{fontWeight:"normal",color:"#408829"}},visualMap:{color:["408829","#a9cba2"]},toolbox:{color:["#408829","#408829","#408829","#408829"]},tooltip:{backgroundColor:"rgba(0,0,0,0.5)",axisPointer:{type:"line",lineStyle:{color:"#408829",type:"dashed"},crossStyle:{color:"#408829"},shadowStyle:{color:"rgba(200,200,200,0.3)"}}},dataZoom:{dataBackgroundColor:"#eee",fillerColor:"rgba(64,136,41,0.2)",handleColor:"#408829"},grid:{borderWidth:0},categoryAxis:{axisLine:{lineStyle:{color:"#408829"}},splitLine:{lineStyle:{color:["#eee"]}}},valueAxis:{axisLine:{lineStyle:{color:"#408829"}},splitArea:{show:!0,areaStyle:{color:["rgba(250,250,250,0.1)","rgba(200,200,200,0.1)"]}},splitLine:{lineStyle:{color:["#eee"]}}},timeline:{lineStyle:{color:"#408829"},controlStyle:{color:"#408829",borderColor:"#408829"}},candlestick:{itemStyle:{color:"#68a54a",color0:"#a9cba2"},lineStyle:{width:1,color:"#408829",color0:"#86b379"},areaStyle:{color:"#408829",color0:"#bfd3b7"}},graph:{itemStyle:{color:"#bfd3b7"},linkStyle:{color:"#408829"}},chord:{padding:4,itemStyle:{color:"#bfd3b7",borderWidth:1,borderColor:"rgba(128, 128, 128, 0.5)"},lineStyle:{color:"rgba(128, 128, 128, 0.5)"},areaStyle:{color:"#408829"}},map:{itemStyle:{color:"#ddd"},areaStyle:{color:"#408829"},label:{color:"#000"}},gauge:{axisLine:{lineStyle:{color:[[.2,"#86b379"],[.8,"#68a54a"],[1,"#408829"]],width:8}}}}):"undefined"!=typeof console&&console&&console.error&&console.error("ECharts is not Loaded")});