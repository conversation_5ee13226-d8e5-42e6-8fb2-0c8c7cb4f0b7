namespace BaseGIS.Core.Entities
{
    public class SymbologyInfo
    {
        public int Id { get; set; }
        public SymbologyType Type { get; set; }
        public int TableInfoId { get; set; }
        public TableInfo TableInfo { get; set; }
        public string Name { get; set; }
        public bool IsDefault { get; set; }
        public string Json { get; set; }
        public string FieldAlias { get; set; }
        public string FieldName { get; set; }
    }

    public enum SymbologyType
    {
        Simple,
        Unique,
        Quantity
    }
}