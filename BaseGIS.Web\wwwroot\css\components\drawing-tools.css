/**
 * Drawing Tools Component Styles
 * استایل‌های کامپوننت ابزارهای ترسیم
 */

/* ========================================
   Drawing Tools Toolbar
======================================== */
.drawing-tools-toolbar {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    min-width: 280px;
}

.drawing-modes .btn-group {
    display: flex;
    gap: 3px;
    flex-wrap: wrap;
}

.drawing-actions .btn-group {
    display: flex;
    gap: 3px;
    justify-content: center;
}

.drawing-tools-toolbar .btn {
    padding: 8px 12px;
    font-size: 13px;
    border-radius: 5px;
    transition: all 0.2s ease;
    border: 1px solid #dee2e6;
    background: #fff;
    color: #495057;
    min-width: 40px;
}

.drawing-tools-toolbar .btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.drawing-tools-toolbar .btn.active {
    background: #007bff;
    border-color: #007bff;
    color: #fff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.drawing-status {
    border-top: 1px solid #dee2e6;
    padding-top: 8px;
    margin-top: 5px;
}

.status-text {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
    display: block;
    margin-bottom: 8px;
}

.drawing-options {
    display: flex;
    gap: 15px;
    align-items: center;
}

.drawing-options .form-check {
    margin: 0;
}

.drawing-options .form-check-input {
    margin-right: 5px;
}

.drawing-options .form-check-label {
    font-size: 11px;
    color: #6c757d;
    margin: 0;
}

/* ========================================
   Drawing Tools Dialog
======================================== */
.drawing-tools-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1050;
    display: none;
    width: 550px;
    max-width: 90vw;
    max-height: 80vh;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid #dee2e6;
    animation: fadeInScale 0.3s ease-out;
}

.drawing-tools-dialog .dialog-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.drawing-tools-dialog .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.drawing-tools-dialog .dialog-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.drawing-tools-dialog .dialog-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
}

.drawing-tools-dialog .dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* ========================================
   Style Controls
======================================== */
.style-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.style-controls .row {
    margin: 0;
}

.style-controls .col-md-6 {
    padding: 0 5px;
}

.style-controls .col-md-6:first-child {
    padding-left: 0;
}

.style-controls .col-md-6:last-child {
    padding-right: 0;
}

.style-preview {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
}

.style-preview h6 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

.preview-container {
    min-height: 80px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ========================================
   Layer Management
======================================== */
.layer-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.layer-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 10px;
    background: #f8f9fa;
}

.layer-item {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 10px;
    background: #fff;
    transition: all 0.2s ease;
}

.layer-item:last-child {
    margin-bottom: 0;
}

.layer-item:hover {
    border-color: #007bff;
    background: #f0f8ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.layer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.layer-type {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.layer-type i {
    color: #007bff;
    width: 16px;
    text-align: center;
}

.layer-actions {
    display: flex;
    gap: 5px;
}

.layer-actions .btn {
    padding: 4px 8px;
    font-size: 11px;
    line-height: 1;
}

.layer-details {
    font-size: 11px;
    color: #6c757d;
    line-height: 1.4;
}

.no-layers {
    padding: 40px 20px;
    text-align: center;
}

.no-layers i {
    color: #dee2e6;
}

.no-layers p {
    margin: 0;
    font-size: 13px;
}

/* ========================================
   Layer Statistics
======================================== */
.layer-statistics {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
}

.layer-statistics h6 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
}

.stat-value {
    font-size: 14px;
    font-weight: 600;
    color: #007bff;
}

/* ========================================
   Feature Popup
======================================== */
.feature-popup {
    min-width: 150px;
}

.feature-popup strong {
    display: block;
    margin-bottom: 8px;
    color: #333;
}

.popup-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.popup-actions .btn {
    padding: 4px 8px;
    font-size: 11px;
    line-height: 1;
}

/* ========================================
   Custom Drawing Marker
======================================== */
.custom-drawing-marker {
    border: none !important;
    background: transparent !important;
}

.custom-drawing-marker div {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* ========================================
   Form Controls
======================================== */
.drawing-tools-dialog .form-label {
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 5px;
}

.drawing-tools-dialog .form-control,
.drawing-tools-dialog .form-select {
    font-size: 13px;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.drawing-tools-dialog .form-control:focus,
.drawing-tools-dialog .form-select:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.drawing-tools-dialog .form-control-color {
    width: 50px;
    height: 38px;
    padding: 2px;
}

.drawing-tools-dialog .form-range {
    width: 100%;
    height: 6px;
    background: #dee2e6;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.drawing-tools-dialog .form-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.drawing-tools-dialog .form-range::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* ========================================
   Responsive Design
======================================== */
@media (max-width: 768px) {
    .drawing-tools-toolbar {
        min-width: auto;
        width: 100%;
    }
    
    .drawing-modes .btn-group,
    .drawing-actions .btn-group {
        justify-content: center;
    }
    
    .drawing-tools-toolbar .btn {
        padding: 6px 10px;
        font-size: 12px;
        min-width: 35px;
    }
    
    .drawing-tools-dialog {
        width: 95vw;
        max-height: 90vh;
    }
    
    .style-controls .row {
        flex-direction: column;
    }
    
    .style-controls .col-md-6 {
        padding: 0;
        margin-bottom: 10px;
    }
    
    .style-controls .col-md-6:last-child {
        margin-bottom: 0;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .layer-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .layer-actions {
        align-self: flex-end;
    }
    
    .drawing-options {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
}

/* ========================================
   Animation Effects
======================================== */
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.drawing-tools-toolbar {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.layer-item {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ========================================
   Dark Theme Support
======================================== */
@media (prefers-color-scheme: dark) {
    .drawing-tools-toolbar {
        background: rgba(33, 37, 41, 0.95);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .drawing-tools-dialog {
        background: #212529;
        border-color: #495057;
        color: #fff;
    }
    
    .drawing-tools-dialog .dialog-header,
    .drawing-tools-dialog .dialog-footer {
        background: #343a40;
        border-color: #495057;
    }
    
    .drawing-tools-dialog .dialog-header h5 {
        color: #fff;
    }
    
    .drawing-tools-dialog .form-control,
    .drawing-tools-dialog .form-select {
        background: #495057;
        border-color: #6c757d;
        color: #fff;
    }
    
    .drawing-tools-dialog .form-control:focus,
    .drawing-tools-dialog .form-select:focus {
        background: #495057;
        border-color: #007bff;
        color: #fff;
    }
    
    .layer-item {
        background: #343a40;
        border-color: #495057;
        color: #fff;
    }
    
    .layer-item:hover {
        background: #495057;
        border-color: #007bff;
    }
    
    .layer-type {
        color: #fff;
    }
    
    .layer-list {
        background: #343a40;
        border-color: #495057;
    }
    
    .stat-item {
        background: #343a40;
        border-color: #495057;
    }
    
    .preview-container {
        background: #343a40;
        border-color: #495057;
    }
    
    .drawing-tools-dialog .form-range {
        background: #495057;
    }
    
    .status-text {
        color: #adb5bd;
    }
    
    .drawing-options .form-check-label {
        color: #adb5bd;
    }
}
