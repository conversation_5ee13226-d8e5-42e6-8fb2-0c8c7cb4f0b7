﻿@using Microsoft.AspNetCore.Identity
@using BaseGIS.Core.Entities
@inject UserManager<ApplicationUser> UserManager
@{
    var userFullName = "";
    var userRoleName = "";
    var userImage = "../../img/avatars/male.png";
    var companyName = "BaseGIS";

    if (User?.Identity?.IsAuthenticated == true)
    {
        var user = await UserManager.GetUserAsync(User);
        if (user != null)
        {
            userFullName = user.FullName ?? user.UserName;
            var roles = await UserManager.GetRolesAsync(user);
            userRoleName = roles.FirstOrDefault() ?? "";
            userImage = user.Image ?? userImage;
        }
    }
}

<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - BaseGIS</title>
    <link rel="stylesheet" href="~/lib/bootstrap/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="~/lib/font-awesome/css/all.css" />
    <link rel="stylesheet" href="~/lib/leaflet/leaflet.css" />
    <link rel="stylesheet" href="~/lib/leaflet-draw/dist/leaflet.draw.css" />
    <link rel="stylesheet" href="~/lib/sweetalert2/sweetalert2.min.css" />
    <link rel="stylesheet" href="~/css/site_map.css" asp-append-version="true" />
    @await RenderSectionAsync("Styles", required: false)
</head>
<body class="d-flex flex-column min-vh-100 ">
    <!-- Toast -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>

    <!-- هدر -->
    <header id="header_main" class="border-bottom fixed-top header-app">
        <div class="container-fluid d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center gap-3">
                <img src="~/img/logo.png" alt="Logo" style="height: 40px;" />
                <div class="header-brand">@companyName</div>
            </div>
            <div class="d-flex align-items-center gap-3">
                <div>
                    <a asp-controller="Account" asp-action="Profile" class="header-item">@userFullName</a>
                    <p class="d-block font-small header-item mb-0">@userRoleName</p>
                </div>
                <div class="dropdown">
                    <img src="@userImage" alt="Avatar" class="rounded-circle" style="width: 40px; height: 40px; cursor: pointer;" data-bs-toggle="dropdown" />
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" asp-controller="Account" asp-action="Profile"><i class="fas fa-user me-2"></i>@userFullName (@userRoleName)</a></li>
                        <li>
                            <form asp-controller="Account" asp-action="Logout" method="post">
                                <button type="submit" class="dropdown-item"><i class="fas fa-sign-out-alt me-2"></i>خروج</button>
                            </form>
                        </li>
                    </ul>
                </div>
                <button id="page_toggle" class="btn btn-link" title="صفحه اصلی" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-cog fa-lg"></i>
                </button>
                <button id="sidebar_toggle" class="btn btn-link" title="تغییر حالت سایدبار">
                    <i class="fas fa-bars fa-lg"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- محتوای اصلی -->
    <div id="page_content">
        @RenderBody()
    </div>

    <!-- اسکریپت‌ها -->
    <script src="~/lib/jquery/jquery.min.js"></script>
    <script src="~/lib/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js"></script>

    <script src="~/lib/leaflet/leaflet.js"></script>
    <script src="~/lib/leaflet-draw/dist/leaflet.draw.js"></script>
    <script src="~/lib/sweetalert2/sweetalert2.min.js"></script>

    <script src="~/js/leaflet.control.custom.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)

    <script>
        $(document).ready(function () {
            const $body = $('body');
            const $sidebarToggle = $('#sidebar_toggle');
            const $settingsBtn = $('#settings_btn');
            const $colorPaletteSelect = $('#color_palette');
            const $fontSizeSelect = $('#font_size');
            const $fontColorSelect = $('#font_color');
            const $darkModeCheckbox = $('#dark_mode');

            // مقداردهی اولیه از localStorage
            let savedPalette = localStorage.getItem('colorPalette') || 'default';
            let savedFontSize = localStorage.getItem('fontSize') || 'medium';
            let savedFontColor = localStorage.getItem('fontColor') || 'dark';
            let isDarkMode = localStorage.getItem('darkMode') === 'true';

            $colorPaletteSelect.val(savedPalette);
            $fontSizeSelect.val(savedFontSize);
            $fontColorSelect.val(savedFontColor);
            $darkModeCheckbox.prop('checked', isDarkMode);

            applySettings(savedPalette, savedFontSize, isDarkMode, savedFontColor);


            $settingsBtn.on('click', function () {
                $body.toggleClass('settings_open');
            });

            $colorPaletteSelect.on('change', function () {
                savedPalette = $(this).val();
                localStorage.setItem('colorPalette', savedPalette);
                applySettings(savedPalette, savedFontSize, isDarkMode, savedFontColor);
            });

            $fontSizeSelect.on('change', function () {
                savedFontSize = $(this).val();
                localStorage.setItem('fontSize', savedFontSize);
                applySettings(savedPalette, savedFontSize, isDarkMode, savedFontColor);
            });

            $fontColorSelect.on('change', function () {
                savedFontColor = $(this).val();
                localStorage.setItem('fontColor', savedFontColor);
                applySettings(savedPalette, savedFontSize, isDarkMode, savedFontColor);
            });

            $darkModeCheckbox.on('change', function () {
                isDarkMode = $(this).is(':checked');
                localStorage.setItem('darkMode', isDarkMode);
                applySettings(savedPalette, savedFontSize, isDarkMode, savedFontColor);
            });

            function applySettings(palette, fontSize, isDarkMode, fontColor) {
                $body.removeClass('palette-blue palette-green palette-red palette-purple palette-dark font-small font-medium font-large font-color-dark font-color-light');
                if (isDarkMode) {
                    $body.addClass('palette-dark');
                } else if (palette !== 'default') {
                    $body.addClass(`palette-${palette}`);
                }
                $body.addClass(`font-${fontSize} font-color-${fontColor}`);
            }

          // نمایش پیام‌های Toast
        @if (TempData["SuccessMessage"] != null)
        {
            <text>showToast("@TempData["SuccessMessage"]", "success");</text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>showToast("@TempData["ErrorMessage"]", "error");</text>
        }
        });

    </script>
</body>
</html>

@functions {
    private string IsActive(string controller, string action)
    {
        var currentController = ViewContext.RouteData.Values["controller"]?.ToString();
        var currentAction = ViewContext.RouteData.Values["action"]?.ToString();
        return (currentController == controller && (action == null || currentAction == action)) ? "active" : "";
    }
}