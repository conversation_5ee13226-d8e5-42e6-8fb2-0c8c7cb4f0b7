﻿@using BaseGIS.Core.Entities

@{
    var tableInfos = Model as List<TableInfo>;
    var GroupList = tableInfos?.Select(x => x.GroupInfo).Distinct().ToList();
    var id = Context.Request.Query["id"];
    var role = ViewBag.Role as ApplicationRole;
    TableAccess roleTableAccess = role?.ConvertTable(role.TableAccess);
}

<div class="col-12" id="UpdateList">
    <div class="card border-danger mb-3 samanFont">
        <div class="card-header bg-danger text-white">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h5 class="card-title samanFont mb-0">فهرست لایه‌ها</h5>
                </div>
            </div>
        </div>
        <div class="collapse show" id="layerListContent">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-lg-10 col-md-12">
                        <select class="form-select select2" id="GList" data-placeholder="یک جدول انتخاب کنید">
                            <option value="0"></option>
                            @foreach (var group in GroupList)
                            {
                                var list = tableInfos.Where(a => a.GroupInfo == group).ToList();
                                if (list.Any())
                                {
                                    var lblG = group != null ? group.AliasName : "بدون گروه‌بندی";
                                    <optgroup label="@lblG">
                                        @foreach (var item in list)
                                        {
                                            bool access = true;
                                            if (roleTableAccess != null)
                                            {
                                                access = roleTableAccess.Tables.Any(t => t.Name == item.Name && t.IsEdit);
                                            }
                                            if (access)
                                            {
                                                string symbol = "fa-table";
                                                if (item.DatasetType.ToLower() == "point") { symbol = "fa-map-marker"; }
                                                else if (item.DatasetType.ToLower() == "polyline") { symbol = "fa-flash"; }
                                                else if (item.DatasetType.ToLower() == "polygon") { symbol = "fa-square-o"; }

                                                if (id == item.Id.ToString())
                                                {
                                                    <option value="@item.Id" selected data-icon="@symbol">@item.AliasName</option>
                                                }
                                                else
                                                {
                                                    <option value="@item.Id" data-icon="@symbol">@item.AliasName</option>
                                                }
                                            }
                                        }
                                    </optgroup>
                                }
                            }
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-12">
                        <button type="button" onclick="selectLayer()" class="btn btn-primary w-100">
                            <i class="fa fa-check me-1"></i>انتخاب
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <script src="~/lib/select2/js/select2.min.js"></script>
    <script src="~/lib/select2/js/i18n/fa.js"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            $('.select2').select2({
                theme: 'bootstrap-5',
                language: 'fa',
                dir: 'rtl',
                placeholder: 'یک جدول انتخاب کنید',
                allowClear: true,
                width: '100%'
            });
        });

        function selectLayer() {
            var selectedId = $('#GList').val();
            if (selectedId && selectedId !== '0') {
                window.onItemSelected(selectedId); // فراخوانی تابع از ویوی اصلی
            } else {
                $("#_Update_Wizard").hide();
            }
        }
    </script>