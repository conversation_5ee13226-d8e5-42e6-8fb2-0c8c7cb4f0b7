﻿@using BaseGIS.Core.Entities

@{
    var tableInfos = Model as List<TableInfo>;
    var groupList = tableInfos?.Select(x => x.GroupInfo).Distinct().ToList();
    var id = Context.Request.Query["id"];
    var role = ViewBag.Role as ApplicationRole;
    TableAccess roleTableAccess = role?.ConvertTable(role.TableAccess);
}

<div id="P_PersonelList">
    <div class="card border-danger mb-4">
        <div class="card-header bg-danger text-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">فهرست لایه‌ها</h5>
            <div class="card-actions">
                <button type="button" class="btn btn-sm btn-light" data-bs-toggle="collapse" data-bs-target="#layerListContent" aria-expanded="true">
                    <i class="fa fa-minus"></i>
                </button>
                <button type="button" class="btn btn-sm btn-light" id="fullscreenBtn">
                    <i class="fa fa-expand"></i>
                </button>
            </div>
        </div>
        <div class="collapse show" id="layerListContent">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12 col-lg-10">
                        <select class="form-select" id="GList" data-placeholder="یک جدول انتخاب کنید">
                            @if (groupList != null)
                            {
                                foreach (var group in groupList)
                                {
                                    var list = tableInfos.Where(a => a.GroupInfo == group).ToList();
                                    if (list.Any())
                                    {
                                        var lblG = group?.AliasName ?? "بدون گروه‌بندی";
                                        <option value="0"></option>
                                        <optgroup label="@lblG">
                                            @foreach (var item in list)
                                            {
                                                bool access = true;
                                                if (roleTableAccess != null)
                                                {
                                                    access = roleTableAccess.Tables.Any(t => t.Name == item.Name && t.IsEdit);
                                                }
                                                if (access)
                                                {
                                                    if (id == item.Id.ToString())
                                                    {
                                                        <option value="@item.Id" selected>@item.AliasName</option>
                                                    }
                                                    else
                                                    {
                                                        <option value="@item.Id" >@item.AliasName</option>
                                                    }
                                                }
                                            }
                                        </optgroup>
                                    }
                                }
                            }
                        </select>
                    </div>
                    <div class="col-12 col-lg-2">
                        <button type="button" id="confirmBtn" class="btn btn-primary w-100">تایید</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function () {
        // Initialize Select2
        $('#GList').select2({
            width: '100%',
            dir: 'rtl',
            placeholder: 'یک جدول انتخاب کنید',
            allowClear: true,
            language: {
                noResults: function () {
                    return "یافت نشد";
                }
            }
        });

        // Fullscreen functionality
        $('#fullscreenBtn').on('click', function () {
            $('#P_PersonelList').toggleClass('fullscreen');
            $(this).find('i').toggleClass('fa-expand fa-compress');
        });

        // Confirm button action
        $('#confirmBtn').on('click', function () {
            var sel = $('#GList').val();
            if (sel && sel !== '0') {
                window.location.href = '/Database/Update?id=' + sel;
            }
        });
    });
</script>

<style>
    .fullscreen {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9999;
        background: white;
        padding: 20px;
        overflow: auto;
    }
</style>