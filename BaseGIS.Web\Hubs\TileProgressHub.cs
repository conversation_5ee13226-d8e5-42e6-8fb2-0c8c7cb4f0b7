﻿using Microsoft.AspNetCore.SignalR;

namespace BaseGIS.Web.Hubs
{
    public class TileProgressHub : Hub
    {
        public async Task JoinOperationGroup(string operationId)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, operationId);
        }

        public async Task LeaveOperationGroup(string operationId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, operationId);
        }

        // These methods are called by the controller to send messages to a group
        public async Task SendProgress(string operationId, int progress, string message)
        {
            await Clients.Group(operationId).SendAsync("ReceiveProgress", progress, message);
        }

        public async Task SendError(string operationId, string errorMessage)
        {
            await Clients.Group(operationId).SendAsync("ReceiveError", errorMessage);
        }
    }
}
