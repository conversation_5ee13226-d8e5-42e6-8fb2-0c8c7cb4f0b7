/**
 * Dashboard Core Module
 * مدیریت اصلی داشبورد
 */

class DashboardCore {
    constructor() {
        this.activePanelID = 0;
        this.colorDefaults = [];
        this.initCharts = [];
        this.initOptions = [];
        this.isLoadingPanel = false;
        
        this.init();
    }

    /**
     * مقداردهی اولیه
     */
    init() {
        this.initializeComponents();
        this.bindEvents();
        this.loadColorDefaults();
    }

    /**
     * مقداردهی کامپوننت‌ها
     */
    initializeComponents() {
        // Select2 initialization
        $('.select2').select2({
            language: 'fa',
            dir: 'rtl'
        });

        // Tooltip initialization
        $('[data-toggle="tooltip"]').tooltip();
        
        // Popover initialization
        $('[data-toggle="popover"]').popover();
    }

    /**
     * اتصال رویدادها
     */
    bindEvents() {
        // Form submissions
        this.bindFormEvents();
        
        // Filter events
        this.bindFilterEvents();
        
        // Panel events
        this.bindPanelEvents();
    }

    /**
     * اتصال رویدادهای فرم
     */
    bindFormEvents() {
        // Dashboard form
        $("#DashboardForm").off('submit').on('submit', (e) => {
            e.preventDefault();
            this.submitDashboardForm(e.target);
        });

        // Panel form
        $("#PanelForm").off('submit').on('submit', (e) => {
            e.preventDefault();
            this.submitPanelForm(e.target);
        });
    }

    /**
     * اتصال رویدادهای فیلتر
     */
    bindFilterEvents() {
        // Filter button
        $('.filter-btn').off('click').on('click', () => {
            this.applyFilters();
        });

        // Clear filter button
        $('.clear-filter-btn').off('click').on('click', () => {
            this.clearFilters();
        });

        // Toggle filter button
        $('.toggle-filter-btn').off('click').on('click', () => {
            this.toggleFilter();
        });
    }

    /**
     * اتصال رویدادهای پنل
     */
    bindPanelEvents() {
        // Panel click events will be bound dynamically
        $(document).off('click', '.panel-clickable').on('click', '.panel-clickable', (e) => {
            this.handlePanelClick(e);
        });
    }

    /**
     * بارگذاری رنگ‌های پیش‌فرض
     */
    loadColorDefaults() {
        // This will be populated from server-side
        if (window.dashboardColors) {
            this.colorDefaults = window.dashboardColors;
        }
    }

    /**
     * ارسال فرم داشبورد
     */
    async submitDashboardForm(form) {
        const formData = new FormData(form);
        
        try {
            this.showLoading();
            
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.ok) {
                this.showSuccess("داشبورد با موفقیت ذخیره شد");
                setTimeout(() => {
                    window.location.href = `/dashboard?id=${result.id}`;
                }, 1000);
            } else {
                this.showError(result.msg);
            }
        } catch (error) {
            this.showError("خطا در ذخیره اطلاعات");
        } finally {
            this.hideLoading();
        }
    }

    /**
     * ارسال فرم پنل
     */
    async submitPanelForm(form) {
        const formData = new FormData(form);
        
        try {
            this.showLoading();
            
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.ok) {
                this.showSuccess("پنل با موفقیت ذخیره شد");
                $("#PanelModal").modal("hide");
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                this.showError(result.msg);
            }
        } catch (error) {
            this.showError("خطا در ذخیره اطلاعات پنل");
        } finally {
            this.hideLoading();
        }
    }

    /**
     * اعمال فیلترها
     */
    applyFilters() {
        const filterData = {};
        
        $(".filter").each(function() {
            const name = $(this).attr('name');
            const value = $(this).val();
            if (value && value.length > 0) {
                filterData[name] = value;
            }
        });
        
        this.loadPanels(filterData);
    }

    /**
     * پاک کردن فیلترها
     */
    clearFilters() {
        $(".filter").val('').trigger('change');
        this.loadPanels({});
    }

    /**
     * تغییر وضعیت نمایش فیلتر
     */
    toggleFilter() {
        $("#DivPanelFilter").toggle();
        const icon = $("#iFilter");
        if ($("#DivPanelFilter").is(":visible")) {
            icon.removeClass("fa-filter").addClass("fa-filter-slash");
        } else {
            icon.removeClass("fa-filter-slash").addClass("fa-filter");
        }
    }

    /**
     * بارگذاری پنل‌ها
     */
    async loadPanels(filterData = {}) {
        try {
            this.showLoading();
            
            const params = new URLSearchParams({
                DashboardID: window.dashboardId || 0,
                ...filterData
            });
            
            const response = await fetch(`/Dashboard/GetDashboardPanels?${params}`);
            const result = await response.text();
            
            $("#PanelsDiv").html(result);
            
        } catch (error) {
            this.showError("خطا در بارگذاری پنل‌ها");
        } finally {
            this.hideLoading();
        }
    }

    /**
     * مدیریت کلیک روی پنل
     */
    handlePanelClick(event) {
        const target = $(event.target);
        const panelData = target.data();
        
        if (panelData.panelId) {
            this.loadChildPanels(panelData);
        }
    }

    /**
     * بارگذاری پنل‌های فرزند
     */
    async loadChildPanels(panelData) {
        if (this.isLoadingPanel) return;
        
        try {
            this.isLoadingPanel = true;
            this.showLoading();
            
            const params = new URLSearchParams({
                DashboardID: window.dashboardId,
                PanelID: panelData.panelId,
                input: panelData.input || '',
                input1: panelData.input1 || '',
                input2: panelData.input2 || '',
                input3: panelData.input3 || '',
                input4: panelData.input4 || '',
                input5: panelData.input5 || '',
                input6: panelData.input6 || '',
                input7: panelData.input7 || ''
            });
            
            const response = await fetch(`/Dashboard/GetDashboardPanels?${params}`);
            const result = await response.json();
            
            if (result.ok) {
                this.renderChildPanels(result.data, panelData.level + 1);
            } else {
                this.showError(result.msg);
            }
            
        } catch (error) {
            this.showError("خطا در بارگذاری پنل‌های فرزند");
        } finally {
            this.isLoadingPanel = false;
            this.hideLoading();
        }
    }

    /**
     * رندر پنل‌های فرزند
     */
    renderChildPanels(panels, level) {
        // Implementation for rendering child panels
        // This will be moved from the current DrawPanel function
    }

    /**
     * نمایش loading
     */
    showLoading() {
        $("#loadingImg").show();
    }

    /**
     * مخفی کردن loading
     */
    hideLoading() {
        $("#loadingImg").hide();
    }

    /**
     * نمایش پیام موفقیت
     */
    showSuccess(message) {
        toastr.success(message, "موفقیت");
    }

    /**
     * نمایش پیام خطا
     */
    showError(message) {
        toastr.error(message, "خطا");
    }

    /**
     * نمایش پیام هشدار
     */
    showWarning(message) {
        toastr.warning(message, "هشدار");
    }
}

// Global instance
window.dashboardCore = new DashboardCore();
