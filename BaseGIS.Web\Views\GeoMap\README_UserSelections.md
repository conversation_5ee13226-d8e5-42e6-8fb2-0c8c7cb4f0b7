# سیستم مدیریت انتخاب‌های کاربر

این سند قابلیت جدید مدیریت انتخاب‌های کاربر را شرح می‌دهد که امکان ذخیره، بازیابی و هایلایت کردن انتخاب‌های کاربران را فراهم می‌کند.

## 🎯 اهداف

1. **ذخیره انتخاب‌ها** - ثبت شناسه‌های رکوردهای انتخاب شده توسط کاربر
2. **هایلایت خودکار** - نمایش انتخاب‌های قبلی در نقشه‌های جدید
3. **مدیریت انتخاب‌ها** - امکان مشاهده، پاک کردن و تغییر وضعیت انتخاب‌ها
4. **تجربه کاربری بهتر** - حفظ انتخاب‌های کاربر بین جلسات مختلف

## 📋 ساختار جدول UserSelect

### **فیلدهای جدول:**

| فیلد | نوع | توضیح |
|------|-----|-------|
| **ID** | `int` | شناسه یکتا |
| **UserID** | `nvarchar(450)` | شناسه کاربر |
| **PageID** | `nvarchar(50)` | شناسه صفحه (اختیاری) |
| **Table** | `nvarchar(100)` | نام جدول/لایه |
| **OIDs** | `nvarchar(max)` | شناسه‌های رکوردها (جدا شده با کاما) |
| **Date** | `nvarchar(max)` | تاریخ انتخاب |
| **IsHighlight** | `bit` | آیا باید هایلایت شود |
| **Where** | `nvarchar(max)` | شرط WHERE (اختیاری) |
| **LayerId** | `int` | شناسه لایه (TableInfo.Id) |
| **SelectionType** | `nvarchar(50)` | نوع انتخاب |
| **SelectionMetadata** | `nvarchar(2000)` | اطلاعات اضافی (JSON) |
| **IsActive** | `bit` | آیا انتخاب فعال است |

### **انواع انتخاب (SelectionType):**
- `geometry` - انتخاب بر اساس هندسه
- `manual` - انتخاب دستی
- `query` - انتخاب بر اساس کوئری

## 🔧 API Endpoints

### **1. دریافت انتخاب‌های کاربر**
```http
GET /REST/GetUserSelections?layerId={layerId}
```

**پاسخ:**
```json
{
  "success": true,
  "selections": [
    {
      "ID": 1,
      "LayerId": 5,
      "Table": "Buildings",
      "OIDs": "123,456,789",
      "Date": "2025-06-21 14:30:00",
      "IsHighlight": true,
      "SelectionType": "geometry",
      "SelectionMetadata": "{...}",
      "FeatureCount": 3
    }
  ]
}
```

### **2. پاک کردن انتخاب‌های کاربر**
```http
POST /REST/ClearUserSelections
Content-Type: application/json

{
  "layerId": 5
}
```

**پاسخ:**
```json
{
  "success": true,
  "message": "انتخاب‌های کاربر پاک شد"
}
```

### **3. تغییر وضعیت هایلایت**
```http
POST /REST/ToggleUserSelectionsHighlight
Content-Type: application/json

{
  "layerId": 5,
  "highlight": true
}
```

**پاسخ:**
```json
{
  "success": true,
  "message": "وضعیت هایلایت فعال شد"
}
```

## 🎨 قابلیت‌های سمت کلاینت

### **توابع JavaScript:**

#### **1. دریافت انتخاب‌ها:**
```javascript
const selections = await getUserSelections(layerId);
console.log('انتخاب‌های کاربر:', selections);
```

#### **2. پاک کردن انتخاب‌ها:**
```javascript
const success = await clearUserSelections(layerId);
if (success) {
    console.log('انتخاب‌ها پاک شد');
}
```

#### **3. تغییر وضعیت هایلایت:**
```javascript
// فعال کردن هایلایت
await toggleUserSelectionsHighlight(layerId, true);

// غیرفعال کردن هایلایت
await toggleUserSelectionsHighlight(layerId, false);
```

#### **4. نمایش اطلاعات انتخاب‌ها:**
```javascript
await showUserSelectionsInfo(layerId);
```

#### **5. به‌روزرسانی لایه:**
```javascript
refreshMapLayer(layerId);
```

## 🔄 فرآیند کار

### **1. انتخاب توسط کاربر:**
```mermaid
graph TD
    A[کاربر انتخاب می‌کند] --> B[SelectByGeometry فراخوانی می‌شود]
    B --> C[عارضه‌ها پیدا می‌شوند]
    C --> D[SaveUserSelections فراخوانی می‌شود]
    D --> E[انتخاب‌های قبلی غیرفعال می‌شوند]
    E --> F[انتخاب جدید ذخیره می‌شود]
    F --> G[نتیجه به کاربر برگردانده می‌شود]
```

### **2. نمایش نقشه:**
```mermaid
graph TD
    A[ExportMapImage فراخوانی می‌شود] --> B[لایه‌ها بارگذاری می‌شوند]
    B --> C[GetUserSelectionsForLayer فراخوانی می‌شود]
    C --> D{انتخاب‌هایی وجود دارد?}
    D -->|بله| E[عارضه‌های انتخاب شده پیدا می‌شوند]
    D -->|خیر| F[نقشه عادی نمایش داده می‌شود]
    E --> G[استایل هایلایت اعمال می‌شود]
    G --> H[لایه هایلایت اضافه می‌شود]
    H --> I[نقشه با هایلایت نمایش داده می‌شود]
```

## 🎨 استایل‌های هایلایت

### **استایل‌های مختلف بر اساس نوع هندسه:**

#### **نقطه (Point):**
```csharp
new SymbolStyle
{
    SymbolScale = 1.5,
    Fill = new Brush(Color.FromArgb(180, 255, 255, 0)), // زرد شفاف
    Outline = new Pen(Color.Red, 3),
    SymbolType = Mapsui.Styles.SymbolType.Ellipse
}
```

#### **خط (Polyline):**
```csharp
new VectorStyle
{
    Line = new Pen(Color.Red, 4),
    Outline = new Pen(Color.Yellow, 6)
}
```

#### **چندضلعی (Polygon):**
```csharp
new VectorStyle
{
    Fill = new Brush(Color.FromArgb(100, 255, 255, 0)), // زرد شفاف
    Outline = new Pen(Color.Red, 3),
    Line = new Pen(Color.Red, 2)
}
```

## 📊 مثال‌های کاربردی

### **1. انتخاب ساختمان‌ها:**
```javascript
// کاربر ساختمان‌هایی را انتخاب می‌کند
// سیستم خودکار شناسه‌ها را ذخیره می‌کند: "123,456,789"

// در بار بعدی نقشه، ساختمان‌های انتخاب شده هایلایت می‌شوند
const selections = await getUserSelections(5); // layerId = 5
console.log('ساختمان‌های انتخاب شده:', selections[0].OIDs);
// خروجی: "123,456,789"
```

### **2. مدیریت انتخاب‌ها:**
```javascript
// مشاهده انتخاب‌های فعلی
await showUserSelectionsInfo(5);

// پاک کردن انتخاب‌ها
await clearUserSelections(5);

// غیرفعال کردن هایلایت بدون حذف انتخاب‌ها
await toggleUserSelectionsHighlight(5, false);
```

### **3. انتخاب چندگانه:**
```javascript
// انتخاب در لایه‌های مختلف
await getUserSelections(5);  // ساختمان‌ها
await getUserSelections(8);  // جاده‌ها
await getUserSelections(12); // پارک‌ها

// هر لایه انتخاب‌های جداگانه‌ای دارد
```

## 🔧 پیکربندی

### **تنظیمات پیش‌فرض:**
```csharp
var userSelect = new UserSelect
{
    UserID = userId,
    LayerId = layerId,
    Table = layerName,
    OIDs = string.Join(",", featureIds),
    Date = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
    IsHighlight = true,        // پیش‌فرض: فعال
    IsActive = true,           // پیش‌فرض: فعال
    SelectionType = "geometry" // پیش‌فرض: انتخاب هندسی
};
```

### **شناسایی کاربر:**
```csharp
var userId = HttpContext.Session.GetString("UserId") ?? 
             User.Identity?.Name ?? 
             "anonymous";
```

## 📝 نکات مهم

### **1. عملکرد:**
- ✅ انتخاب‌های قبلی خودکار غیرفعال می‌شوند
- ✅ فقط جدیدترین انتخاب برای هر لایه فعال است
- ✅ استفاده از ایندکس بر روی LayerId و UserID

### **2. امنیت:**
- ✅ هر کاربر فقط انتخاب‌های خود را می‌بیند
- ✅ شناسایی کاربر بر اساس Session یا Identity
- ✅ اعتبارسنجی ورودی‌ها

### **3. مقیاس‌پذیری:**
- ✅ پشتیبانی از انتخاب‌های چندگانه
- ✅ ذخیره metadata برای اطلاعات اضافی
- ✅ قابلیت توسعه برای انواع انتخاب جدید

### **4. تجربه کاربری:**
- ✅ Toast notifications برای اطلاع‌رسانی
- ✅ به‌روزرسانی خودکار نقشه
- ✅ حفظ انتخاب‌ها بین جلسات

## 🎉 مزایای سیستم

| مزیت | توضیح |
|------|-------|
| **حافظه انتخاب** | انتخاب‌ها بین جلسات حفظ می‌شوند |
| **هایلایت خودکار** | عارضه‌های انتخاب شده خودکار هایلایت می‌شوند |
| **مدیریت آسان** | امکان مشاهده، پاک کردن و تغییر وضعیت |
| **چند کاربره** | هر کاربر انتخاب‌های جداگانه‌ای دارد |
| **انعطاف‌پذیر** | پشتیبانی از انواع مختلف انتخاب |
| **قابل توسعه** | ساختار قابل توسعه برای نیازهای آینده |

## 🚀 استفاده

### **برای کاربران:**
1. عارضه‌هایی را در نقشه انتخاب کنید
2. انتخاب‌ها خودکار ذخیره می‌شوند
3. در بار بعدی نقشه، انتخاب‌ها هایلایت می‌شوند
4. از منوی مدیریت برای کنترل انتخاب‌ها استفاده کنید

### **برای توسعه‌دهندگان:**
1. از API endpoints برای مدیریت انتخاب‌ها استفاده کنید
2. توابع JavaScript را برای تعامل با سیستم فراخوانی کنید
3. استایل‌های هایلایت را سفارشی کنید
4. metadata اضافی را در SelectionMetadata ذخیره کنید

این سیستم تجربه کاربری را به طور قابل توجهی بهبود می‌بخشد و امکان کار مؤثرتر با نقشه را فراهم می‌کند! 🗺️✨
