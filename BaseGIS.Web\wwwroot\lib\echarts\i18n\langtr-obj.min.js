!function(e){"function"==typeof define&&define.amd?define(["exports"],e):"object"==typeof exports&&"string"!=typeof exports.nodeName?e(exports):e({})}(function(e){var a,i={time:{month:["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","Ha<PERSON>ran","Temmuz","Ağustos","Eyl<PERSON>l","Ekim","Kasım","Aralık"],monthAbbr:["<PERSON>ca","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","May","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>"],dayOfWeek:["Paz<PERSON>","<PERSON><PERSON>si","<PERSON><PERSON>","Çarşamba","Perşembe","Cuma","Cumartesi"],dayOfWeekAbbr:["<PERSON>","<PERSON>zt","<PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","Cts"]},legend:{selector:{all:"<PERSON>ü<PERSON><PERSON><PERSON><PERSON>",inverse:"Seçimi Ters Çevir"}},toolbox:{brush:{title:{rect:"Dikdörtgen Seçimi",polygon:"Kement Seçimi",lineX:"Yatay Seçim",lineY:"Dikey Seçim",keep:"Seçimi Koru",clear:"Seçimi Sil"}},dataView:{title:"Veri Görünümü",lang:["Veri Görünümü","Kapat","Yenile"]},dataZoom:{title:{zoom:"Yakınlaştır/Uzaklaştır",back:"Yakınlaştırmayı Sıfırla"}},magicType:{title:{line:"Çizgisel Grafiğe Çevir",bar:"Çubuk Grafiğe Çevir",stack:"Yığın",tiled:"Blok"}},restore:{title:"Eski Haline Getir"},saveAsImage:{title:"Resim Olarak Kaydet",lang:["Resim Olarak Kaydetmek için Sağ Tıklayın"]}},series:{typeNames:{pie:"Pasta Grafiği",bar:"Çubuk Grafik",line:"Çizgi Grafiği",scatter:"Dağılım Grafiği",effectScatter:"Dalga Efekt Dağılım Grafiği",radar:"Radar Grafiği",tree:"Ağaç Grafiği",treemap:"Ağaç Haritası",boxplot:"Kutu Grafiği",candlestick:"Şamdan Grafik",k:"K Çizgi Grafiği",heatmap:"Sıcaklık Haritası",map:"Harita",parallel:"Paralel Koordinat Haritası",lines:"Çizgisel Grafik",graph:"İlişkisel Grafik",sankey:"Sankey Diagramı",funnel:"Huni Grafik",gauge:"Gösterge",pictorialBar:"Resimli Çubuk Grafiği",themeRiver:"Akış Haritası",sunburst:"Güeş Patlaması Tablosu"}},aria:{general:{withTitle:'Bu grafik "{title}" içindir.',withoutTitle:"Bu Bir Grafiktir."},series:{single:{prefix:"",withName:" Grafik Türü {seriesType} ve {seriesName} gösteriyor.",withoutName:" {seriesType} tipinde grafik."},multiple:{prefix:". {seriesCount} kadar grafik sayısından oluşur.",withName:" {seriesId}.serisi {seriesName} adını temsil eden bir {seriesType} temsil eder.",withoutName:" {seriesId}. serisi bir {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"Veriler Aşağıdaki Gibidir: ",partialData:"İlk {displayCnt} öğesi: ",withName:" {value} için {name}",withoutName:"{value}",separator:{middle:", ",end:". "}}}};for(a in i)i.hasOwnProperty(a)&&(e[a]=i[a])});