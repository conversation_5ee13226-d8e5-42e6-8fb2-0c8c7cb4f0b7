!function(e,a){"function"==typeof define&&define.amd?define(["exports","echarts"],a):"object"==typeof exports&&"string"!=typeof exports.nodeName?a(0,require("echarts/lib/echarts")):a(0,e.echarts)}(this,function(e,a){a.registerLocale("PT-br",{time:{month:["Janeiro","Fevereiro","Março","Abril","Maio","Junho","Julho","Agosto","Setembro","Outubro","Novembro","Dezembro"],monthAbbr:["<PERSON>","<PERSON>v","<PERSON>","Abr","<PERSON>","<PERSON>","Jul","A<PERSON>","Set","Out","Nov","Dez"],dayOfWeek:["Domingo","Segunda-feira","Terça-feira","Quarta-feira","Quinta-feira","Sexta-feira","Sábado"],dayOfWeekAbbr:["<PERSON>","Se<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>áb"]},legend:{selector:{all:"Todas",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Seleção retangular",polygon:"Seleção em laço",lineX:"Selecionar horizontalmente",lineY:"Selecionar verticalmente",keep:"Manter seleções",clear:"Limpar seleções"}},dataView:{title:"Exibição de dados",lang:["Exibição de dados","Fechar","Atualizar"]},dataZoom:{title:{zoom:"Zoom",back:"Restaurar Zoom"}},magicType:{title:{line:"Trocar para gráfico de linhas",bar:"Trocar para gráfico de barras",stack:"Empilhar",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Salvar como imagem",lang:["Clique com o botão direito para salvar imagem"]}},series:{typeNames:{pie:"Gráfico de pizza",bar:"Gráfico de barras",line:"Gráfico de linhas",scatter:"Gráfico de dispersão",effectScatter:"Gráfico de dispersão ondulado",radar:"Gráfico radar",tree:"Árvore",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"Gráfico K line",heatmap:"Mapa de calor",map:"Mapa",parallel:"Coordenadas paralelas",lines:"Gráfico de linhas",graph:"Relationship graph",sankey:"Gráfico Sankey",funnel:"Gráfico de funil",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst"}},aria:{general:{withTitle:'Este é um gráfico entitulado "{title}"',withoutTitle:"Este é um gráfico"},series:{single:{prefix:"",withName:" do tipo {seriesType} nomeada/nomeado como {seriesName}.",withoutName:" do tipo {seriesType}."},multiple:{prefix:". Consiste de {seriesCount} séries.",withName:" A {seriesId} série é um/uma {seriesType} representando {seriesName}.",withoutName:" A {seriesId} series é um/uma {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"Os dados são: ",partialData:"As primeiros {displayCnt} itens são: ",withName:"os dados para {name} são {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}})});