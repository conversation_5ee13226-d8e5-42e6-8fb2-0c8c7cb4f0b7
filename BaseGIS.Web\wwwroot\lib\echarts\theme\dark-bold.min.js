!function(e,o){"function"==typeof define&&define.amd?define(["exports","echarts"],o):"object"==typeof exports&&"string"!=typeof exports.nodeName?o(0,require("echarts/lib/echarts")):o(0,e.echarts)}(this,function(e,o){var l,r,t;o?((t={color:r=["#458c6b","#f2da87","#d9a86c","#d94436","#a62424","#76bc9b","#cce6da","#eeeeee"],backgroundColor:"#333",tooltip:{axisPointer:{lineStyle:{color:l="#eee"},crossStyle:{color:l}}},legend:{textStyle:{color:l}},title:{textStyle:{color:l}},toolbox:{iconStyle:{borderColor:l}},dataZoom:{dataBackgroundColor:"#eee",fillerColor:"rgba(200,200,200,0.2)",handleColor:"#458c6b"},timeline:{itemStyle:{color:r[1]},lineStyle:{color:l},controlStyle:{color:l,borderColor:l},label:{color:l}},timeAxis:(t=function(){return{axisLine:{lineStyle:{color:l}},axisTick:{lineStyle:{color:l}},axisLabel:{color:l},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:l}}}})(),logAxis:t(),valueAxis:t(),categoryAxis:t(),line:{symbol:"circle"},graph:{color:r},gauge:{axisLine:{lineStyle:{color:[[.2,"#f2da87"],[.8,"#458c6b"],[1,"#a62424"]],width:8}}}}).categoryAxis.splitLine.show=!1,o.registerTheme("dark-bold",t)):"undefined"!=typeof console&&console&&console.error&&console.error("ECharts is not Loaded")});