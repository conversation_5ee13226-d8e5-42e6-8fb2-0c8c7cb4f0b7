﻿@using BaseGIS.Core.Entities
@using Microsoft.AspNetCore.Identity
@inject UserManager<ApplicationUser> UserManager

@{
    var user = await UserManager.GetUserAsync(User);
    var isAdmin = user != null && (await UserManager.IsInRoleAsync(user, "Admin") || await UserManager.IsInRoleAsync(user, "RegionalManager"));
    var currentController = ViewContext.RouteData.Values["controller"]?.ToString();
    var isAdminPage = currentController == "Admin";
}

@if (isAdmin)
{
    <li class="nav-item">
        <a title="مدیریت" class="nav-link @IsActive("Admin")" data-bs-toggle="collapse" href="#adminMenu" role="button" aria-expanded="@(isAdminPage ? "true" : "false")" aria-controls="adminMenu">
            <i class="fas fa-cogs me-1"></i>
            <span class="menu-text">مدیریت</span>
        </a>
        <div class="collapse @(isAdminPage ? "show" : "")" id="adminMenu">
            <ul class="nav flex-column submenu">
                @if (User.IsInRole("Admin"))
                {
                    <li class="nav-item">
                        <a title="مدیریت کاربران" class="nav-link @IsActive("Admin", "Index")" asp-controller="Admin" asp-action="Index">
                            <i class="fas fa-users me-1"></i>
                            <span class="menu-text">مدیریت کاربران</span>
                        </a>
                    </li>              
                    
                    <li class="nav-item">
                        <a title="تنظیمات سیستم" class="nav-link @IsActive("Admin", "Configuration")" asp-controller="Admin" asp-action="Configuration">
                            <i class="fas fa-cog me-1"></i>
                            <span class="menu-text">تنظیمات سیستم</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a title="لاگ سرویس‌ها" class="nav-link @IsActive("Admin", "ServiceLogs")" asp-controller="Admin" asp-action="ServiceLogs">
                            <i class="fas fa-history me-1"></i>
                            <span class="menu-text">لاگ سرویس‌ها</span>
                        </a>
                    </li>
                }
            </ul>
        </div>
    </li>
}

@functions {
    private string IsActive(string controller, string action = null)
    {
        var currentController = ViewContext.RouteData.Values["controller"]?.ToString();
        var currentAction = ViewContext.RouteData.Values["action"]?.ToString();
        if (action == null)
            return currentController == controller ? "active" : "";
        return (currentController == controller && currentAction == action) ? "active" : "";
    }
}
