{"format": 1, "restore": {"D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Web\\BaseGIS.Web.csproj": {}}, "projects": {"D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Application\\BaseGIS.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Application\\BaseGIS.Application.csproj", "projectName": "BaseGIS.Application", "projectPath": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Application\\BaseGIS.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Core\\BaseGIS.Core.csproj": {"projectPath": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Core\\BaseGIS.Core.csproj"}, "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Infrastructure\\BaseGIS.Infrastructure.csproj": {"projectPath": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Infrastructure\\BaseGIS.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Core\\BaseGIS.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Core\\BaseGIS.Core.csproj", "projectName": "BaseGIS.Core", "projectPath": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Core\\BaseGIS.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Infrastructure\\BaseGIS.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Infrastructure\\BaseGIS.Infrastructure.csproj", "projectName": "BaseGIS.Infrastructure", "projectPath": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Infrastructure\\BaseGIS.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Core\\BaseGIS.Core.csproj": {"projectPath": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Core\\BaseGIS.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"GeoJSON.Net": {"target": "Package", "version": "[1.2.19, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite": {"target": "Package", "version": "[9.0.3, )"}, "NetTopologySuite.IO.GeoJSON": {"target": "Package", "version": "[4.0.0, )"}, "NetTopologySuite.IO.Shapefile": {"target": "Package", "version": "[2.1.0, )"}, "ProjNet": {"target": "Package", "version": "[2.0.0, )"}, "System.Linq.Dynamic.Core": {"target": "Package", "version": "[1.6.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Web\\BaseGIS.Web.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Web\\BaseGIS.Web.csproj", "projectName": "BaseGIS.Web", "projectPath": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Web\\BaseGIS.Web.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Web\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Application\\BaseGIS.Application.csproj": {"projectPath": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Application\\BaseGIS.Application.csproj"}, "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Core\\BaseGIS.Core.csproj": {"projectPath": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Core\\BaseGIS.Core.csproj"}, "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Infrastructure\\BaseGIS.Infrastructure.csproj": {"projectPath": "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Infrastructure\\BaseGIS.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"GeoAPI.CoordinateSystems": {"target": "Package", "version": "[1.7.5, )"}, "GeoJSON.Net": {"target": "Package", "version": "[1.4.1, )"}, "Mapsui": {"target": "Package", "version": "[4.1.9, )"}, "Mapsui.Nts": {"target": "Package", "version": "[4.1.9, )"}, "Mapsui.Rendering.Skia": {"target": "Package", "version": "[4.1.9, )"}, "Mapsui.Tiling": {"target": "Package", "version": "[4.1.9, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.SqlServer.Types": {"target": "Package", "version": "[160.1000.6, )"}, "NetTopologySuite.IO.GeoJSON": {"target": "Package", "version": "[4.0.0, )"}, "NetTopologySuite.IO.Shapefile": {"target": "Package", "version": "[2.1.0, )"}, "NetTopologySuite.IO.SqlServerBytes": {"target": "Package", "version": "[2.1.0, )"}, "NetTopologySuite.IO.VectorTiles": {"target": "Package", "version": "[1.1.0, )"}, "NetTopologySuite.IO.VectorTiles.Mapbox": {"target": "Package", "version": "[1.1.0, )"}, "ProjNet": {"target": "Package", "version": "[2.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "SharpZipLib": {"target": "Package", "version": "[1.4.2, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.9.0, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.5, )"}, "System.Linq.Dynamic.Core": {"target": "Package", "version": "[1.6.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}