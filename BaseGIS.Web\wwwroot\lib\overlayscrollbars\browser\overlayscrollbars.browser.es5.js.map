{"version": 3, "file": "overlayscrollbars.browser.es5.js", "sources": ["../../src/support/cache.ts", "../../src/support/compatibility/isBrowser.ts", "../../src/support/utils/alias.ts", "../../src/support/compatibility/apis.ts", "../../src/support/utils/types.ts", "../../src/support/utils/animation.ts", "../../src/support/utils/array.ts", "../../src/support/utils/strings.ts", "../../src/support/utils/equal.ts", "../../src/support/utils/noop.ts", "../../src/support/utils/function.ts", "../../src/support/utils/object.ts", "../../src/support/utils/math.ts", "../../src/support/dom/attribute.ts", "../../src/support/dom/class.ts", "../../src/support/dom/traversal.ts", "../../src/support/dom/manipulation.ts", "../../src/trustedTypePolicy.ts", "../../src/support/dom/create.ts", "../../src/support/dom/style.ts", "../../src/support/dom/dimensions.ts", "../../src/support/dom/events.ts", "../../src/support/dom/scroll.ts", "../../src/support/dom/focus.ts", "../../src/support/eventListeners.ts", "../../src/plugins/plugins.ts", "../../../../node_modules/@babel/runtime/helpers/extends.js", "../../src/plugins/optionsValidationPlugin/validation.ts", "../../src/plugins/optionsValidationPlugin/optionsValidationPlugin.ts", "../../src/classnames.ts", "../../src/plugins/sizeObserverPlugin/sizeObserverPlugin.ts", "../../src/setups/structureSetup/structureSetup.utils.ts", "../../src/plugins/scrollbarsHidingPlugin/scrollbarsHidingPlugin.ts", "../../src/plugins/clickScrollPlugin/clickScrollPlugin.ts", "../../src/options.ts", "../../src/nonce.ts", "../../src/environment.ts", "../../src/observers/domObserver.ts", "../../src/observers/sizeObserver.ts", "../../src/observers/trinsicObserver.ts", "../../src/setups/observersSetup/observersSetup.ts", "../../src/initialization.ts", "../../src/setups/scrollbarsSetup/scrollbarsSetup.elements.ts", "../../src/setups/scrollbarsSetup/scrollbarsSetup.events.ts", "../../src/setups/scrollbarsSetup/scrollbarsSetup.ts", "../../src/setups/structureSetup/structureSetup.elements.ts", "../../src/setups/structureSetup/updateSegments/trinsicUpdateSegment.ts", "../../src/setups/structureSetup/updateSegments/paddingUpdateSegment.ts", "../../src/setups/structureSetup/updateSegments/overflowUpdateSegment.ts", "../../src/setups/structureSetup/structureSetup.ts", "../../src/setups/setups.ts", "../../src/instances.ts", "../../src/overlayscrollbars.ts"], "sourcesContent": ["export interface CacheOptions<Value> {\r\n  // initial value of _value.\r\n  _initialValue: Value;\r\n  // Custom comparison function if shallow compare isn't enough. Returns true if nothing changed.\r\n  _equal?: EqualCachePropFunction<Value>;\r\n  // If true always updates _value and _previous, otherwise they update only when they changed.\r\n  _alwaysUpdateValues?: boolean;\r\n}\r\n\r\nexport type CacheValues<T> = [value: T, changed: boolean, previous?: T];\r\n\r\nexport type EqualCachePropFunction<Value> = (currentVal: Value, newVal: Value) => boolean;\r\n\r\nexport type CacheUpdater<Value> = (current: Value, previous?: Value) => Value;\r\n\r\nexport type UpdateCacheContextual<Value> = (newValue: Value, force?: boolean) => CacheValues<Value>;\r\n\r\nexport type UpdateCache<Value> = (force?: boolean) => CacheValues<Value>;\r\n\r\nexport type GetCurrentCache<Value> = (force?: boolean) => CacheValues<Value>;\r\n\r\nexport type Cache<Value> = [UpdateCache<Value>, GetCurrentCache<Value>];\r\n\r\nexport type CacheContextual<Value> = [UpdateCacheContextual<Value>, GetCurrentCache<Value>];\r\n\r\ntype CreateCache = {\r\n  <Value>(options: CacheOptions<Value>): CacheContextual<Value>;\r\n  <Value>(options: CacheOptions<Value>, update: CacheUpdater<Value>): Cache<Value>;\r\n  <Value>(options: CacheOptions<Value>, update?: CacheUpdater<Value>):\r\n    | CacheContextual<Value>\r\n    | Cache<Value>;\r\n};\r\n\r\nexport const createCache: CreateCache = <Value>(\r\n  options: CacheOptions<Value>,\r\n  update?: CacheUpdater<Value>\r\n): CacheContextual<Value> | Cache<Value> => {\r\n  const { _initialValue, _equal, _alwaysUpdateValues } = options;\r\n  let _value: Value = _initialValue;\r\n  let _previous: Value | undefined;\r\n\r\n  const cacheUpdateContextual: UpdateCacheContextual<Value> = (newValue, force?) => {\r\n    const curr = _value;\r\n\r\n    const newVal = newValue;\r\n    const changed = force || (_equal ? !_equal(curr, newVal) : curr !== newVal);\r\n\r\n    if (changed || _alwaysUpdateValues) {\r\n      _value = newVal;\r\n      _previous = curr;\r\n    }\r\n\r\n    return [_value, changed, _previous];\r\n  };\r\n  const cacheUpdateIsolated: UpdateCache<Value> = (force?) =>\r\n    cacheUpdateContextual(update!(_value, _previous), force);\r\n\r\n  const getCurrentCache: GetCurrentCache<Value> = (force?: boolean) => [\r\n    _value,\r\n    !!force, // changed\r\n    _previous,\r\n  ];\r\n\r\n  return [update ? cacheUpdateIsolated : cacheUpdateContextual, getCurrentCache] as\r\n    | CacheContextual<Value>\r\n    | Cache<Value>;\r\n};\r\n", "export const isBrowser =\r\n  // deno has the global `window` defined\r\n  typeof window !== 'undefined' &&\r\n  // make sure HTML element is available\r\n  typeof HTMLElement !== 'undefined' &&\r\n  // make sure document is defined\r\n  !!window.document;\r\n", "import { isBrowser } from '../compatibility/isBrowser';\r\n\r\nexport const wnd = (isBrowser ? window : {}) as typeof window;\r\nexport const mathMax = Math.max;\r\nexport const mathMin = Math.min;\r\nexport const mathRound = Math.round;\r\nexport const mathFloor = Math.floor;\r\nexport const mathCeil = Math.ceil;\r\nexport const mathAbs = Math.abs;\r\nexport const mathSign = Math.sign;\r\nexport const cAF = wnd.cancelAnimationFrame;\r\nexport const rAF = wnd.requestAnimationFrame;\r\nexport const setT = wnd.setTimeout;\r\nexport const clearT = wnd.clearTimeout;\r\n", "import { wnd } from '../utils/alias';\r\n\r\nconst getApi = <T>(name: string) =>\r\n  (typeof wnd[name as keyof typeof wnd] !== 'undefined'\r\n    ? wnd[name as keyof typeof wnd]\r\n    : undefined) as T;\r\n\r\nexport const MutationObserverConstructor = getApi<typeof MutationObserver>('MutationObserver');\r\nexport const IntersectionObserverConstructor =\r\n  getApi<typeof IntersectionObserver>('IntersectionObserver');\r\nexport const ResizeObserverConstructor = getApi<typeof ResizeObserver>('ResizeObserver');\r\nexport const scrollT = getApi<new (constructor: unknown) => AnimationTimeline>('ScrollTimeline');\r\n", "import type { PlainObject } from '../../typings';\r\n\r\nexport const isUndefined = (obj: any): obj is undefined => obj === undefined;\r\n\r\nexport const isNull = (obj: any): obj is null => obj === null;\r\n\r\nexport const type = (obj: any): string =>\r\n  isUndefined(obj) || isNull(obj)\r\n    ? `${obj}`\r\n    : Object.prototype.toString\r\n        .call(obj)\r\n        .replace(/^\\[object (.+)\\]$/, '$1')\r\n        .toLowerCase();\r\n\r\nexport const isNumber = (obj: any): obj is number => typeof obj === 'number';\r\n\r\nexport const isString = (obj: any): obj is string => typeof obj === 'string';\r\n\r\nexport const isBoolean = (obj: any): obj is boolean => typeof obj === 'boolean';\r\n\r\nexport const isFunction = (obj: any): obj is (...args: any[]) => any => typeof obj === 'function';\r\n\r\nexport const isArray = <T = any>(obj: any): obj is Array<T> => Array.isArray(obj);\r\n\r\nexport const isObject = (obj: any): obj is object =>\r\n  typeof obj === 'object' && !isArray(obj) && !isNull(obj);\r\n\r\n/**\r\n * Returns true if the given object is array like, false otherwise.\r\n * @param obj The Object\r\n */\r\nexport const isArrayLike = <T extends PlainObject = any>(obj: any): obj is ArrayLike<T> => {\r\n  const length = !!obj && obj.length;\r\n  const lengthCorrectFormat = isNumber(length) && length > -1 && length % 1 == 0; // eslint-disable-line eqeqeq\r\n\r\n  return isArray(obj) || (!isFunction(obj) && lengthCorrectFormat)\r\n    ? length > 0 && isObject(obj)\r\n      ? length - 1 in obj\r\n      : true\r\n    : false;\r\n};\r\n\r\n/**\r\n * Returns true if the given object is a \"plain\" (e.g. { key: value }) object, false otherwise.\r\n * @param obj The Object.\r\n */\r\nexport const isPlainObject = <T = any>(obj: any): obj is PlainObject<T> =>\r\n  !!obj && obj.constructor === Object;\r\n\r\n/**\r\n * Checks whether the given object is a HTMLElement.\r\n * @param obj The object which shall be checked.\r\n */\r\nexport const isHTMLElement = (obj: any): obj is HTMLElement => obj instanceof HTMLElement;\r\n\r\n/**\r\n * Checks whether the given object is a Element.\r\n * @param obj The object which shall be checked.\r\n */\r\nexport const isElement = (obj: any): obj is Element => obj instanceof Element;\r\n", "import { mathMax, rAF, cAF } from './alias';\r\nimport { isFunction } from './types';\r\n\r\n/**\r\n * percent: current percent (0 - 1),\r\n * time: current time (duration * percent),\r\n * min: start value\r\n * max: end value\r\n * duration: duration in ms\r\n */\r\nexport type EasingFn = (\r\n  percent: number,\r\n  time: number,\r\n  min: number,\r\n  max: number,\r\n  duration: number\r\n) => number;\r\n\r\nconst animationCurrentTime = () => performance.now();\r\n\r\nexport const animateNumber = (\r\n  from: number,\r\n  to: number,\r\n  duration: number,\r\n  onFrame: (progress: number, percent: number, completed: boolean) => any,\r\n  easing?: EasingFn | false\r\n): ((complete?: boolean) => void) => {\r\n  let animationFrameId = 0;\r\n  const timeStart = animationCurrentTime();\r\n  const finalDuration = mathMax(0, duration);\r\n  const frame = (complete?: boolean) => {\r\n    const timeNow = animationCurrentTime();\r\n    const timeElapsed = timeNow - timeStart;\r\n    const stopAnimation = timeElapsed >= finalDuration;\r\n    const percent = complete\r\n      ? 1\r\n      : 1 - (mathMax(0, timeStart + finalDuration - timeNow) / finalDuration || 0);\r\n    const progress =\r\n      (to - from) *\r\n        (isFunction(easing)\r\n          ? easing(percent, percent * finalDuration, 0, 1, finalDuration)\r\n          : percent) +\r\n      from;\r\n    const animationCompleted = stopAnimation || percent === 1;\r\n\r\n    onFrame && onFrame(progress, percent, animationCompleted);\r\n\r\n    animationFrameId = animationCompleted ? 0 : rAF!(() => frame());\r\n  };\r\n  frame();\r\n  return (complete) => {\r\n    cAF!(animationFrameId);\r\n    complete && frame(complete);\r\n  };\r\n};\r\n", "import type { PlainObject } from '../../typings';\r\nimport { isArray, isArrayLike, isString } from './types';\r\n\r\ntype RunEachItem = ((...args: any) => any | any[]) | false | null | undefined;\r\n\r\nexport function each<T extends Array<unknown> | ReadonlyArray<unknown>>(\r\n  array: T,\r\n  callback: (\r\n    value: T extends Array<infer V> | ReadonlyArray<infer V> ? V : never,\r\n    index: number,\r\n    source: T\r\n  ) => boolean | unknown\r\n): T;\r\nexport function each<T extends ArrayLike<unknown>>(\r\n  arrayLikeObject: T,\r\n  callback: (\r\n    value: T extends ArrayLike<infer V> ? V : never,\r\n    index: number,\r\n    source: T\r\n  ) => boolean | unknown\r\n): T;\r\nexport function each<T extends PlainObject>(\r\n  obj: T,\r\n  callback: (value: any, key: string, source: T) => boolean | unknown\r\n): T;\r\nexport function each(\r\n  source: Array<unknown> | ArrayLike<unknown> | ReadonlyArray<unknown> | PlainObject,\r\n  callback: (value: any, indexOrKey: any, source: any) => boolean | unknown\r\n): Array<unknown> | ArrayLike<unknown> | ReadonlyArray<unknown> | Set<unknown> | PlainObject {\r\n  if (isArrayLike(source)) {\r\n    for (let i = 0; i < source.length; i++) {\r\n      if (callback(source[i], i, source) === false) {\r\n        break;\r\n      }\r\n    }\r\n  } else if (source) {\r\n    // cant use support func keys here due to circular dep\r\n    each(Object.keys(source), (key) => callback(source[key], key, source));\r\n  }\r\n  return source;\r\n}\r\n\r\n/**\r\n * Returns true when the passed item is in the passed array and false otherwise.\r\n * @param arr The array.\r\n * @param item The item.\r\n * @returns Whether the item is in the array.\r\n */\r\nexport const inArray = <T = any>(arr: T[] | readonly T[], item: T): boolean =>\r\n  arr.indexOf(item) >= 0;\r\n\r\n/**\r\n * Concats two arrays and returns an new array without modifying any of the passed arrays.\r\n * @param a Array A.\r\n * @param b Array B.\r\n * @returns A new array which has the entries of both arrays.\r\n */\r\nexport const concat = <T>(a: T[] | ReadonlyArray<T>, b: T[] | ReadonlyArray<T>): T[] => a.concat(b);\r\n\r\n/**\r\n * Pushesh all given items into the given array and returns it.\r\n * @param array The array the items shall be pushed into.\r\n * @param items The items which shall be pushed into the array.\r\n */\r\nexport const push = <T>(array: T[], items: T | ArrayLike<T>, arrayIsSingleItem?: boolean): T[] => {\r\n  !arrayIsSingleItem && !isString(items) && isArrayLike(items)\r\n    ? Array.prototype.push.apply(array, items as T[])\r\n    : array.push(items as T);\r\n  return array;\r\n};\r\n\r\n/**\r\n * Creates a shallow-copied Array instance from an array-like or iterable object.\r\n * @param arr The object from which the array instance shall be created.\r\n */\r\nexport const from = <T = any>(arr?: ArrayLike<T> | Set<T>) => Array.from(arr || []);\r\n\r\n/**\r\n * Creates an array if the passed value is not an array, or returns the value if it is.\r\n * If the passed value is an array like structure and not a string it will be converted into an array.\r\n * @param value The value.\r\n * @returns An array which represents the passed value(s).\r\n */\r\nexport const createOrKeepArray = <T>(value: T | T[] | ArrayLike<T>): T[] => {\r\n  if (isArray(value)) {\r\n    return value;\r\n  }\r\n  return !isString(value) && isArrayLike(value) ? from(value) : [value];\r\n};\r\n\r\n/**\r\n * Check whether the passed array is empty.\r\n * @param array The array which shall be checked.\r\n */\r\nexport const isEmptyArray = (array: any[] | null | undefined): boolean => !!array && !array.length;\r\n\r\n/**\r\n * Deduplicates all items of the array.\r\n * @param array The array to be deduplicated.\r\n * @returns The deduplicated array.\r\n */\r\nexport const deduplicateArray = <T extends any[]>(array: T): T => from(new Set(array)) as T;\r\n\r\n/**\r\n * Calls all functions in the passed array/set of functions.\r\n * @param arr The array filled with function which shall be called.\r\n * @param args The args with which each function is called.\r\n * @param keep True when the Set / array should not be cleared afterwards, false otherwise.\r\n */\r\nexport const runEachAndClear = (arr: RunEachItem[], args?: any[], keep?: boolean): void => {\r\n  // eslint-disable-next-line prefer-spread\r\n  const runFn = (fn: RunEachItem) => (fn ? fn.apply(undefined, args || []) : true); // return true when fn is falsy to not break the loop\r\n  each(arr, runFn);\r\n  !keep && ((arr as any[]).length = 0);\r\n};\r\n", "export const strPaddingTop = 'paddingTop';\r\nexport const strPaddingRight = 'paddingRight';\r\nexport const strPaddingLeft = 'paddingLeft';\r\nexport const strPaddingBottom = 'paddingBottom';\r\nexport const strMarginLeft = 'marginLeft';\r\nexport const strMarginRight = 'marginRight';\r\nexport const strMarginBottom = 'marginBottom';\r\nexport const strOverflowX = 'overflowX';\r\nexport const strOverflowY = 'overflowY';\r\nexport const strWidth = 'width';\r\nexport const strHeight = 'height';\r\nexport const strVisible = 'visible';\r\nexport const strHidden = 'hidden';\r\nexport const strScroll = 'scroll';\r\n\r\nexport const capitalizeFirstLetter = (str: string | number | false | null | undefined): string => {\r\n  const finalStr = String(str || '');\r\n  return finalStr ? finalStr[0].toUpperCase() + finalStr.slice(1) : '';\r\n};\r\n", "import type { WH } from '../dom/dimensions';\r\nimport type { XY } from '../dom/offset';\r\nimport type { TRBL } from '../dom/style';\r\nimport type { PlainObject } from '../../typings';\r\nimport { each } from './array';\r\nimport { mathRound } from './alias';\r\nimport { strHeight, strWidth } from './strings';\r\n\r\n/**\r\n * Compares two objects and returns true if all values of the passed prop names are identical, false otherwise or if one of the two object is falsy.\r\n * @param a Object a.\r\n * @param b Object b.\r\n * @param props The props which shall be compared.\r\n */\r\nexport const equal = <T extends PlainObject>(\r\n  a: T | undefined,\r\n  b: T | undefined,\r\n  props: Array<keyof T> | ReadonlyArray<keyof T>,\r\n  propMutation?: ((value: any) => any) | null | false\r\n): boolean => {\r\n  if (a && b) {\r\n    let result = true;\r\n    each(props, (prop) => {\r\n      const compareA = propMutation ? propMutation(a[prop]) : a[prop];\r\n      const compareB = propMutation ? propMutation(b[prop]) : b[prop];\r\n      if (compareA !== compareB) {\r\n        result = false;\r\n      }\r\n    });\r\n    return result;\r\n  }\r\n  return false;\r\n};\r\n\r\n/**\r\n * Compares object a with object b and returns true if both have the same property values, false otherwise.\r\n * Also returns false if one of the objects is undefined or null.\r\n * @param a Object a.\r\n * @param b Object b.\r\n */\r\nexport const equalWH = <T>(a?: Partial<WH<T>>, b?: Partial<WH<T>>) =>\r\n  equal<Partial<WH<T>>>(a, b, ['w', 'h']);\r\n\r\n/**\r\n * Compares object a with object b and returns true if both have the same property values, false otherwise.\r\n * Also returns false if one of the objects is undefined or null.\r\n * @param a Object a.\r\n * @param b Object b.\r\n */\r\nexport const equalXY = <T>(a?: Partial<XY<T>>, b?: Partial<XY<T>>) =>\r\n  equal<Partial<XY<T>>>(a, b, ['x', 'y']);\r\n\r\n/**\r\n * Compares object a with object b and returns true if both have the same property values, false otherwise.\r\n * Also returns false if one of the objects is undefined or null.\r\n * @param a Object a.\r\n * @param b Object b.\r\n */\r\nexport const equalTRBL = (a?: TRBL, b?: TRBL) => equal<TRBL>(a, b, ['t', 'r', 'b', 'l']);\r\n\r\n/**\r\n * Compares two DOM Rects for their equality of their width and height properties\r\n * Also returns false if one of the DOM Rects is undefined or null.\r\n * @param a DOM Rect a.\r\n * @param b DOM Rect b.\r\n * @param round Whether the values should be rounded.\r\n */\r\nexport const equalBCRWH = (a?: DOMRect, b?: DOMRect, round?: boolean) =>\r\n  equal<DOMRect>(a, b, [strWidth, strHeight], round && ((value) => mathRound(value)));\r\n", "export const noop = () => {}; // eslint-disable-line\r\n", "import { isNumber, isFunction } from './types';\r\nimport { from } from './array';\r\nimport { rAF, cAF, setT, clearT } from './alias';\r\nimport { noop } from './noop';\r\n\r\ntype DebounceTiming = number | false | null | undefined;\r\n\r\nexport interface DebounceOptions<FunctionToDebounce extends (...args: any) => any> {\r\n  /**\r\n   * The timeout for debouncing. If null, no debounce is applied.\r\n   */\r\n  _timeout?: DebounceTiming | (() => DebounceTiming);\r\n  /**\r\n   * A maximum amount of ms. before the function will be called even with debounce.\r\n   */\r\n  _maxDelay?: DebounceTiming | (() => DebounceTiming);\r\n  /**\r\n   * Defines the calling on the leading edge of the timeout.\r\n   */\r\n  _leading?: boolean;\r\n  /**\r\n   * Function which merges parameters for each canceled debounce.\r\n   * If parameters can't be merged the function will return null, otherwise it returns the merged parameters.\r\n   */\r\n  _mergeParams?: (\r\n    prev: Parameters<FunctionToDebounce>,\r\n    curr: Parameters<FunctionToDebounce>\r\n  ) => Parameters<FunctionToDebounce> | false | null | undefined;\r\n}\r\n\r\nexport interface Debounced<FunctionToDebounce extends (...args: any) => any> {\r\n  (...args: Parameters<FunctionToDebounce>): ReturnType<FunctionToDebounce>;\r\n  _flush(): void;\r\n}\r\n\r\nexport const bind = <A extends any[], B extends any[], R>(\r\n  fn: (...args: [...A, ...B]) => R,\r\n  ...args: A\r\n): ((...args: B) => R) => fn.bind(0, ...args);\r\n\r\n/**\r\n * Creates a timeout and cleartimeout tuple. The timeout function always clears the previously created timeout before it runs.\r\n * @param timeout The timeout in ms. If no timeout (or 0) is passed requestAnimationFrame is used instead of setTimeout.\r\n * @returns A tuple with the timeout function as the first value and the clearTimeout function as the second value.\r\n */\r\nexport const selfClearTimeout = (timeout?: number | (() => number)) => {\r\n  let id: number;\r\n  const setTFn = timeout ? setT : rAF!;\r\n  const clearTFn = timeout ? clearT : cAF!;\r\n  return [\r\n    (callback: () => any) => {\r\n      clearTFn(id);\r\n      // @ts-ignore\r\n      id = setTFn(() => callback(), isFunction(timeout) ? timeout() : timeout);\r\n    },\r\n    () => clearTFn(id),\r\n  ] as [timeout: (callback: () => any) => void, clear: () => void];\r\n};\r\n\r\n/**\r\n * Debounces the given function either with a timeout or a animation frame.\r\n * @param functionToDebounce The function which shall be debounced.\r\n * @param options Options for debouncing.\r\n */\r\nexport const debounce = <FunctionToDebounce extends (...args: any) => any>(\r\n  functionToDebounce: FunctionToDebounce,\r\n  options?: DebounceOptions<FunctionToDebounce>\r\n): Debounced<FunctionToDebounce> => {\r\n  const { _timeout, _maxDelay, _leading, _mergeParams } = options || {};\r\n  let maxTimeoutId: number | undefined;\r\n  let prevArguments: Parameters<FunctionToDebounce> | null | undefined;\r\n  let latestArguments: Parameters<FunctionToDebounce> | null | undefined;\r\n  let leadingInvoked: boolean | undefined;\r\n  let clear = noop;\r\n\r\n  const invokeFunctionToDebounce = function (args: Parameters<FunctionToDebounce>) {\r\n    clear();\r\n    clearT(maxTimeoutId);\r\n    leadingInvoked = maxTimeoutId = prevArguments = undefined;\r\n    clear = noop;\r\n    // eslint-disable-next-line\r\n    // @ts-ignore\r\n    functionToDebounce.apply(this, args);\r\n  };\r\n\r\n  const mergeParms = (\r\n    curr: Parameters<FunctionToDebounce>\r\n  ): Parameters<FunctionToDebounce> | false | null | undefined =>\r\n    _mergeParams && prevArguments ? _mergeParams(prevArguments, curr) : curr;\r\n\r\n  const flush = () => {\r\n    /* istanbul ignore next */\r\n    if (clear !== noop) {\r\n      invokeFunctionToDebounce(mergeParms(latestArguments!) || latestArguments!);\r\n    }\r\n  };\r\n\r\n  const debouncedFn = function () {\r\n    // eslint-disable-next-line prefer-rest-params\r\n    const args: Parameters<FunctionToDebounce> = from(arguments) as Parameters<FunctionToDebounce>;\r\n    const finalTimeout = isFunction(_timeout) ? _timeout() : _timeout;\r\n    const hasTimeout = isNumber(finalTimeout) && finalTimeout >= 0;\r\n\r\n    if (hasTimeout) {\r\n      const finalMaxWait = isFunction(_maxDelay) ? _maxDelay() : _maxDelay;\r\n      const hasMaxWait = isNumber(finalMaxWait) && finalMaxWait >= 0;\r\n      const setTimeoutFn = finalTimeout > 0 ? setT : rAF!;\r\n      const clearTimeoutFn = finalTimeout > 0 ? clearT : cAF!;\r\n      const mergeParamsResult = mergeParms(args);\r\n      const invokedArgs = mergeParamsResult || args;\r\n      const boundInvoke = invokeFunctionToDebounce.bind(0, invokedArgs);\r\n      let timeoutId: number | undefined;\r\n\r\n      // if (!mergeParamsResult) {\r\n      //   invokeFunctionToDebounce(prevArguments || args);\r\n      // }\r\n\r\n      clear();\r\n      if (_leading && !leadingInvoked) {\r\n        boundInvoke();\r\n        leadingInvoked = true;\r\n        // @ts-ignore\r\n        timeoutId = setTimeoutFn(() => (leadingInvoked = undefined), finalTimeout);\r\n      } else {\r\n        // @ts-ignore\r\n        timeoutId = setTimeoutFn(boundInvoke, finalTimeout);\r\n\r\n        if (hasMaxWait && !maxTimeoutId) {\r\n          maxTimeoutId = setT(flush, finalMaxWait as number);\r\n        }\r\n      }\r\n\r\n      clear = () => clearTimeoutFn(timeoutId as number);\r\n\r\n      prevArguments = latestArguments = invokedArgs;\r\n    } else {\r\n      invokeFunctionToDebounce(args);\r\n    }\r\n  };\r\n  debouncedFn._flush = flush;\r\n\r\n  return debouncedFn as Debounced<FunctionToDebounce>;\r\n};\r\n", "import type { PlainObject } from '../../typings';\r\nimport { isArray, isFunction, isPlainObject, isNull } from './types';\r\nimport { each } from './array';\r\n\r\n/**\r\n * Determines whether the passed object has a property with the passed name.\r\n * @param obj The object.\r\n * @param prop The name of the property.\r\n */\r\nexport const hasOwnProperty = (obj: any, prop: string | number | symbol): boolean =>\r\n  Object.prototype.hasOwnProperty.call(obj, prop);\r\n\r\n/**\r\n * Returns the names of the enumerable string properties and methods of an object.\r\n * @param obj The object of which the properties shall be returned.\r\n */\r\nexport const keys = (obj: any): Array<string> => (obj ? Object.keys(obj) : []);\r\n\r\ntype AssignDeep = {\r\n  <T, U>(target: T, object1: U): T & U;\r\n  <T, U, V>(target: T, object1: U, object2: V): T & U & V;\r\n  <T, U, V, W>(target: T, object1: U, object2: V, object3: W): T & U & V & W;\r\n  <T, U, V, W, X>(target: T, object1: U, object2: V, object3: W, object4: X): T & U & V & W & X;\r\n  <T, U, V, W, X, Y>(\r\n    target: T,\r\n    object1: U,\r\n    object2: V,\r\n    object3: W,\r\n    object4: X,\r\n    object5: Y\r\n  ): T & U & V & W & X & Y;\r\n  <T, U, V, W, X, Y, Z>(\r\n    target: T,\r\n    object1?: U,\r\n    object2?: V,\r\n    object3?: W,\r\n    object4?: X,\r\n    object5?: Y,\r\n    object6?: Z\r\n  ): T & U & V & W & X & Y & Z;\r\n};\r\n\r\n// https://github.com/jquery/jquery/blob/master/src/core.js#L116\r\nexport const assignDeep: AssignDeep = <T, U, V, W, X, Y, Z>(\r\n  target: T,\r\n  object1?: U,\r\n  object2?: V,\r\n  object3?: W,\r\n  object4?: X,\r\n  object5?: Y,\r\n  object6?: Z\r\n): T & U & V & W & X & Y & Z => {\r\n  const sources: Array<any> = [object1, object2, object3, object4, object5, object6];\r\n\r\n  // Handle case when target is a string or something (possible in deep copy)\r\n  if ((typeof target !== 'object' || isNull(target)) && !isFunction(target)) {\r\n    target = {} as T;\r\n  }\r\n\r\n  each(sources, (source) => {\r\n    // Extend the base object\r\n    each(source, (_, key) => {\r\n      const copy: any = source[key];\r\n\r\n      // Prevent Object.prototype pollution\r\n      // Prevent never-ending loop\r\n      if (target === copy) {\r\n        return true;\r\n      }\r\n\r\n      const copyIsArray = isArray(copy);\r\n\r\n      // Recurse if we're merging plain objects or arrays\r\n      if (copy && isPlainObject(copy)) {\r\n        const src = target[key as keyof T];\r\n        let clone: any = src;\r\n\r\n        // Ensure proper type for the source value\r\n        if (copyIsArray && !isArray(src)) {\r\n          clone = [];\r\n        } else if (!copyIsArray && !isPlainObject(src)) {\r\n          clone = {};\r\n        }\r\n\r\n        // Never move original objects, clone them\r\n        target[key as keyof T] = assignDeep(clone, copy) as any;\r\n      } else {\r\n        target[key as keyof T] = copyIsArray ? copy.slice() : copy;\r\n      }\r\n    });\r\n  });\r\n\r\n  // Return the modified object\r\n  return target as any;\r\n};\r\n\r\nexport const removeUndefinedProperties = <T extends PlainObject>(target: T, deep?: boolean): T =>\r\n  each(assignDeep({}, target), (value, key, copy) => {\r\n    if (value === undefined) {\r\n      delete copy[key];\r\n    } else if (deep && value && isPlainObject(value)) {\r\n      copy[key as keyof typeof copy] = removeUndefinedProperties(value, deep) as any;\r\n    }\r\n  });\r\n\r\n/**\r\n * Returns true if the given object is empty, false otherwise.\r\n * @param obj The Object.\r\n */\r\nexport const isEmptyObject = (obj: any): boolean => !keys(obj).length;\r\n", "import { mathMax, mathMin } from './alias';\r\n\r\n/**\r\n * Caps the passed number between the `min` and `max` bounds.\r\n * @param min The min bound.\r\n * @param max The max bound.\r\n * @param number The number to be capped.\r\n * @returns The capped number between min and max.\r\n */\r\nexport const capNumber = (min: number, max: number, number: number) =>\r\n  mathMax(min, mathMin(max, number));\r\n", "import type { HTMLElementTarget } from './types';\r\nimport { bind, deduplicateArray, each, from, isArray } from '../utils';\r\n\r\nexport type AttributeElementTarget = HTMLElementTarget | Element;\r\n\r\nexport type DomTokens = string | string[] | false | null | undefined | void;\r\n\r\nexport const getDomTokensArray = (tokens: DomTokens) =>\r\n  deduplicateArray((isArray(tokens) ? tokens : (tokens || '').split(' ')).filter((token) => token));\r\n\r\n/**\r\n * Gets a attribute with the given attribute of the given element.\r\n * @param elm The element of which the attribute shall be get.\r\n * @param attrName The attribute name which shall be get.\r\n * @returns The attribute value or `null` when the attribute is not set or `false` if the element is undefined.\r\n */\r\nexport const getAttr = (elm: AttributeElementTarget, attrName: string) =>\r\n  elm && elm.getAttribute(attrName);\r\n\r\n/**\r\n * Returns whether the given attribute exists on the given element.\r\n * @param elm The element.\r\n * @param attrName The attribute.\r\n * @returns A Truthy value indicates a present attrubte.\r\n */\r\nexport const hasAttr = (elm: AttributeElementTarget, attrName: string) =>\r\n  elm && elm.hasAttribute(attrName);\r\n\r\n/**\r\n * Sets the given attributes to the given element.\r\n * @param elm The element of which the attributes shall be removed.\r\n * @param attrName The attribute names separated by a space.\r\n */\r\nexport const setAttrs = (\r\n  elm: AttributeElementTarget,\r\n  attrNames: string | string[],\r\n  value: string | number | false | null | undefined\r\n) => {\r\n  each(getDomTokensArray(attrNames), (attrName) => {\r\n    elm && elm.setAttribute(attrName, String(value || ''));\r\n  });\r\n};\r\n\r\n/**\r\n * Removes the given attributes from the given element.\r\n * @param elm The element of which the attribute shall be removed.\r\n * @param attrName The attribute names separated by a space.\r\n */\r\nexport const removeAttrs = (elm: AttributeElementTarget, attrNames: string | string[]): void => {\r\n  each(getDomTokensArray(attrNames), (attrName) => elm && elm.removeAttribute(attrName));\r\n};\r\n\r\nexport const domTokenListAttr = (elm: AttributeElementTarget, attrName: string) => {\r\n  const initialArr = getDomTokensArray(getAttr(elm, attrName));\r\n  const setElmAttr = bind(setAttrs, elm, attrName);\r\n  const domTokenListOperation = (operationTokens: DomTokens, operation: 'add' | 'delete') => {\r\n    const initialArrSet = new Set(initialArr);\r\n    each(getDomTokensArray(operationTokens), (token) => {\r\n      initialArrSet[operation](token);\r\n    });\r\n    return from(initialArrSet).join(' ');\r\n  };\r\n\r\n  return {\r\n    _remove: (removeTokens: DomTokens) => setElmAttr(domTokenListOperation(removeTokens, 'delete')),\r\n    _add: (addTokens: DomTokens) => setElmAttr(domTokenListOperation(addTokens, 'add')),\r\n    _has: (hasTokens: DomTokens) => {\r\n      const tokenSet = getDomTokensArray(hasTokens);\r\n      return tokenSet.reduce(\r\n        (boolean, token) => boolean && initialArr.includes(token),\r\n        tokenSet.length > 0\r\n      );\r\n    },\r\n  };\r\n};\r\n\r\n/**\r\n * Treats the given attribute like the \"class\" attribute and removes the given value from it.\r\n * @param elm The element.\r\n * @param attrName The attributeName to which the value shall be removed.\r\n * @param value The value which shall be removed.\r\n */\r\nexport const removeAttrClass = (\r\n  elm: AttributeElementTarget,\r\n  attrName: string,\r\n  value: DomTokens\r\n): (() => void) => {\r\n  domTokenListAttr(elm, attrName)._remove(value);\r\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\r\n  return bind(addAttrClass, elm, attrName, value);\r\n};\r\n\r\n/**\r\n * Treats the given attribute like the \"class\" attribute and adds value to it.\r\n * @param elm The element.\r\n * @param attrName The attributeName to which the value shall be added.\r\n * @param value The value which shall be added.\r\n */\r\nexport const addAttrClass = (\r\n  elm: AttributeElementTarget,\r\n  attrName: string,\r\n  value: DomTokens\r\n): (() => void) => {\r\n  domTokenListAttr(elm, attrName)._add(value);\r\n  return bind(removeAttrClass, elm, attrName, value);\r\n};\r\n\r\nexport const addRemoveAttrClass = (\r\n  elm: AttributeElementTarget,\r\n  attrName: string,\r\n  value: DomTokens,\r\n  add?: boolean\r\n) => (add ? addAttrClass : removeAttrClass)(elm, attrName, value);\r\n\r\n/**\r\n * Treats the given attribute like the \"class\" attribute and checks if the given value is in it.\r\n * @param elm The element.\r\n * @param attrName The attributeName from which the content shall be checked.\r\n * @param value The value.\r\n * @returns True if the given attribute has the value in it, false otherwise.\r\n */\r\nexport const hasAttrClass = (\r\n  elm: AttributeElementTarget,\r\n  attrName: string,\r\n  value: DomTokens\r\n): boolean => domTokenListAttr(elm, attrName)._has(value);\r\n", "import type { AttributeElementTarget, DomTokens } from './attribute';\r\nimport { each, from } from '../utils/array';\r\nimport { bind } from '../utils/function';\r\nimport { domTokenListAttr } from './attribute';\r\n\r\nconst createDomTokenListClass = (elm: AttributeElementTarget) => domTokenListAttr(elm, 'class');\r\n\r\n/**\r\n * Check whether the given element has the given class name(s).\r\n * @param elm The element.\r\n * @param className The class name(s).\r\n */\r\nexport const hasClass = (elm: AttributeElementTarget, className: DomTokens): boolean =>\r\n  createDomTokenListClass(elm)._has(className);\r\n\r\n/**\r\n * Removes the given class name(s) from the given element.\r\n * @param elm The element.\r\n * @param className The class name(s) which shall be removed. (separated by spaces)\r\n */\r\nexport const removeClass = (elm: AttributeElementTarget, className: DomTokens): void => {\r\n  createDomTokenListClass(elm)._remove(className);\r\n};\r\n\r\n/**\r\n * Adds the given class name(s) to the given element.\r\n * @param elm The element.\r\n * @param className The class name(s) which shall be added. (separated by spaces)\r\n * @returns A function which removes the added class name(s).\r\n */\r\nexport const addClass = (elm: AttributeElementTarget, className: DomTokens): (() => void) => {\r\n  createDomTokenListClass(elm)._add(className);\r\n  return bind(removeClass, elm, className);\r\n};\r\n\r\n/**\r\n * Takes two className strings, compares them and returns the difference as array.\r\n * @param classNameA ClassName A.\r\n * @param classNameB ClassName B.\r\n */\r\nexport const diffClass = (\r\n  classNameA: string | false | null | undefined,\r\n  classNameB: string | false | null | undefined\r\n) => {\r\n  const set = new Set<string>(classNameA ? classNameA.split(' ') : []);\r\n\r\n  each(classNameB ? classNameB.split(' ') : [], (className) => {\r\n    set.has(className) ? set.delete(className) : set.add(className);\r\n  });\r\n\r\n  return from(set);\r\n};\r\n", "import type { NodeElementTarget } from './types';\r\nimport { isElement } from '../utils/types';\r\nimport { push, from } from '../utils/array';\r\n\r\n/**\r\n * Find all elements with the passed selector, outgoing (and including) the passed element or the document if no element was provided.\r\n * @param selector The selector which has to be searched by.\r\n * @param elm The element from which the search shall be outgoing.\r\n */\r\nexport const find = (selector: string, elm?: NodeElementTarget): Element[] => {\r\n  const rootElm = elm ? isElement(elm) && elm : document;\r\n  return rootElm ? from(rootElm.querySelectorAll(selector)) : [];\r\n};\r\n\r\n/**\r\n * Find the first element with the passed selector, outgoing (and including) the passed element or the document if no element was provided.\r\n * @param selector The selector which has to be searched by.\r\n * @param elm The element from which the search shall be outgoing.\r\n */\r\nexport const findFirst = (selector: string, elm?: NodeElementTarget): NodeElementTarget => {\r\n  const rootElm = elm ? isElement(elm) && elm : document;\r\n  return rootElm && rootElm.querySelector(selector);\r\n};\r\n\r\n/**\r\n * Determines whether the passed element is matching with the passed selector.\r\n * @param elm The element which has to be compared with the passed selector.\r\n * @param selector The selector which has to be compared with the passed element. Additional selectors: ':visible' and ':hidden'.\r\n */\r\nexport const is = (elm: NodeElementTarget, selector: string): boolean =>\r\n  isElement(elm) && elm.matches(selector);\r\n\r\nexport const isBodyElement = (elm: NodeElementTarget) => is(elm, 'body'); // don't do targetElement === ownerDocument.body in case initialization happens in memory\r\n\r\n/**\r\n * Returns the children (no text-nodes or comments) of the passed element which are matching the passed selector. An empty array is returned if the passed element is null.\r\n * @param elm The element of which the children shall be returned.\r\n * @param selector The selector which must match with the children elements.\r\n */\r\nexport const children = (elm: NodeElementTarget, selector?: string): ReadonlyArray<Element> => {\r\n  const childs: Array<Element> = [];\r\n\r\n  return isElement(elm)\r\n    ? push(\r\n        childs,\r\n        from(elm.children).filter((child) => (selector ? is(child, selector) : child))\r\n      )\r\n    : childs;\r\n};\r\n\r\n/**\r\n * Returns the childNodes (incl. text-nodes or comments etc.) of the passed element. An empty array is returned if the passed element is null.\r\n * @param elm The element of which the childNodes shall be returned.\r\n */\r\nexport const contents = (elm: NodeElementTarget): ReadonlyArray<ChildNode> =>\r\n  elm ? from(elm.childNodes) : [];\r\n\r\n/**\r\n * Returns the parent element of the passed element, or null if the passed element is null.\r\n * @param elm The element of which the parent element shall be returned.\r\n */\r\nexport const parent = (elm: NodeElementTarget): NodeElementTarget => elm && elm.parentElement;\r\n\r\n/**\r\n * Returns the closest element to the passed element which matches the given selector.\r\n * @param elm The element.\r\n * @param selector The selector.\r\n * @returns The closest element to the passed element which matches the given selector.\r\n */\r\nexport const closest = (elm: NodeElementTarget, selector: string): NodeElementTarget =>\r\n  isElement(elm) && elm.closest(selector);\r\n\r\n/**\r\n * Gets the focused element of the passed or default document.\r\n * @returns The focused element of the passed document.\r\n */\r\nexport const getFocusedElement = (doc?: Document) => (doc || document).activeElement;\r\n\r\n/**\r\n * Determines whether the given element lies between two selectors in the DOM.\r\n * @param elm The element.\r\n * @param highBoundarySelector The high boundary selector.\r\n * @param deepBoundarySelector The deep boundary selector.\r\n */\r\nexport const liesBetween = (\r\n  elm: NodeElementTarget,\r\n  highBoundarySelector: string,\r\n  deepBoundarySelector: string\r\n): boolean => {\r\n  const closestHighBoundaryElm = closest(elm, highBoundarySelector);\r\n  const closestDeepBoundaryElm = elm && findFirst(deepBoundarySelector, closestHighBoundaryElm);\r\n  const deepBoundaryIsValid =\r\n    closest(closestDeepBoundaryElm, highBoundarySelector) === closestHighBoundaryElm;\r\n\r\n  return closestHighBoundaryElm && closestDeepBoundaryElm\r\n    ? closestHighBoundaryElm === elm ||\r\n        closestDeepBoundaryElm === elm ||\r\n        (deepBoundaryIsValid &&\r\n          closest(closest(elm, deepBoundarySelector), highBoundarySelector) !==\r\n            closestHighBoundaryElm)\r\n    : false;\r\n};\r\n", "import type { NodeElementTarget, NodeElementTargetCollection } from './types';\r\nimport { createOrKeepArray, each } from '../utils/array';\r\nimport { parent } from './traversal';\r\nimport { bind } from '../utils';\r\n\r\n/**\r\n * Removes the given Nodes from their parent.\r\n * @param nodes The Nodes which shall be removed.\r\n */\r\nexport const removeElements = (nodes: NodeElementTargetCollection): void => {\r\n  each(createOrKeepArray(nodes), (node) => {\r\n    const parentElm = parent(node);\r\n    node && parentElm && parentElm.removeChild(node);\r\n  });\r\n};\r\n\r\n/**\r\n * Appends the given children at the end of the given Node.\r\n * @param node The Node to which the children shall be appended.\r\n * @param children The Nodes which shall be appended.\r\n * @returns A function which removes the inserted nodes.\r\n */\r\nexport const appendChildren = (node: NodeElementTarget, children: NodeElementTargetCollection) =>\r\n  bind(\r\n    removeElements,\r\n    node &&\r\n      children &&\r\n      each(createOrKeepArray(children), (child) => {\r\n        child && node.appendChild(child);\r\n      })\r\n  );\r\n", "// at the time of implementation TypeScript doesn't offer any TrustedTypes typescript definitions\r\n// https://github.com/microsoft/TypeScript/issues/30024\r\nlet trustedTypePolicy: unknown | undefined;\r\n\r\nexport const getTrustedTypePolicy = () => trustedTypePolicy;\r\nexport const setTrustedTypePolicy = (newTrustedTypePolicy: unknown | undefined) => {\r\n  trustedTypePolicy = newTrustedTypePolicy;\r\n};\r\n", "import { each } from '../utils/array';\r\nimport { setAttrs } from './attribute';\r\nimport { contents } from './traversal';\r\nimport { removeElements } from './manipulation';\r\nimport { getTrustedTypePolicy } from '../../trustedTypePolicy';\r\n\r\n/**\r\n * Creates a div DOM node.\r\n */\r\nexport const createDiv = (classNames?: string): HTMLDivElement => {\r\n  const div = document.createElement('div');\r\n  setAttrs(div, 'class', classNames);\r\n  return div;\r\n};\r\n\r\n/**\r\n * Creates DOM nodes modeled after the passed html string and returns the root dom nodes as a array.\r\n * @param html The html string after which the DOM nodes shall be created.\r\n */\r\nexport const createDOM = (html: string): ReadonlyArray<Node> => {\r\n  const createdDiv = createDiv();\r\n  const trustedTypesPolicy = getTrustedTypePolicy();\r\n  const trimmedHtml = html.trim();\r\n  createdDiv.innerHTML = trustedTypesPolicy\r\n    ? (trustedTypesPolicy as any).createHTML(trimmedHtml)\r\n    : trimmedHtml;\r\n\r\n  return each(contents(createdDiv), (elm) => removeElements(elm));\r\n};\r\n", "import type { PlainObject, StyleObject, StyleObjectKey, StyleObjectValue } from '../../typings';\r\nimport type { XY } from './offset';\r\nimport type { HTMLElementTarget } from './types';\r\nimport { wnd } from '../utils/alias';\r\nimport { each, from } from '../utils/array';\r\nimport { isString, isNumber, isObject, isNull, isBoolean } from '../utils/types';\r\n\r\nexport interface TRBL {\r\n  t: number;\r\n  r: number;\r\n  b: number;\r\n  l: number;\r\n}\r\n\r\nconst getCSSVal = (computedStyle: CSSStyleDeclaration, prop: StyleObjectKey): string =>\r\n  computedStyle.getPropertyValue(prop) || computedStyle[prop as any] || '';\r\n\r\nconst validFiniteNumber = (number: number) => {\r\n  const notNaN = number || 0;\r\n  return isFinite(notNaN) ? notNaN : 0;\r\n};\r\n\r\nconst parseToZeroOrNumber = (value?: string): number => validFiniteNumber(parseFloat(value || ''));\r\n\r\nexport const roundCssNumber = (value: number) => Math.round(value * 10000) / 10000;\r\n\r\nexport const ratioToCssPercent = (ratio: number) =>\r\n  `${roundCssNumber(validFiniteNumber(ratio) * 100)}%`;\r\n\r\nexport const numberToCssPx = (number: number) => `${roundCssNumber(validFiniteNumber(number))}px`;\r\n\r\nexport function setStyles(\r\n  elm: HTMLElementTarget,\r\n  styles: StyleObject | false | null | undefined\r\n): void {\r\n  elm &&\r\n    styles &&\r\n    each(styles, (rawValue: StyleObjectValue, name) => {\r\n      try {\r\n        const elmStyle = elm.style;\r\n        const value =\r\n          isNull(rawValue) || isBoolean(rawValue)\r\n            ? ''\r\n            : isNumber(rawValue)\r\n              ? numberToCssPx(rawValue)\r\n              : rawValue;\r\n\r\n        if (name.indexOf('--') === 0) {\r\n          elmStyle.setProperty(name, value);\r\n        } else {\r\n          elmStyle[name as any] = value;\r\n        }\r\n      } catch {}\r\n    });\r\n}\r\n\r\nexport function getStyles(\r\n  elm: HTMLElementTarget,\r\n  styles: Array<StyleObjectKey> | ReadonlyArray<StyleObjectKey>,\r\n  pseudoElm?: string | null | undefined\r\n): Partial<Record<StyleObjectKey, string>>;\r\nexport function getStyles(\r\n  elm: HTMLElementTarget,\r\n  styles: StyleObjectKey,\r\n  pseudoElm?: string | null | undefined\r\n): string;\r\nexport function getStyles(\r\n  elm: HTMLElementTarget,\r\n  styles: Array<StyleObjectKey> | ReadonlyArray<StyleObjectKey> | StyleObjectKey,\r\n  pseudoElm?: string | null | undefined\r\n): Partial<Record<StyleObjectKey, string>> | string {\r\n  const getSingleStyle = isString(styles);\r\n  let getStylesResult: string | PlainObject = getSingleStyle ? '' : {};\r\n\r\n  if (elm) {\r\n    const computedStyle = wnd.getComputedStyle(elm, pseudoElm) || elm.style;\r\n    getStylesResult = getSingleStyle\r\n      ? getCSSVal(computedStyle, styles)\r\n      : from(styles).reduce((result, key) => {\r\n          result[key] = getCSSVal(computedStyle, key);\r\n          return result;\r\n        }, getStylesResult as PlainObject);\r\n  }\r\n  return getStylesResult;\r\n}\r\n\r\n/**\r\n * Returns the top right bottom left values of the passed css property.\r\n * @param elm The element of which the values shall be returned.\r\n * @param propertyPrefix The css property prefix. (e.g. \"border\")\r\n * @param propertySuffix The css property suffix. (e.g. \"width\")\r\n */\r\nexport const topRightBottomLeft = (\r\n  elm?: HTMLElementTarget,\r\n  propertyPrefix?: string,\r\n  propertySuffix?: string\r\n): TRBL => {\r\n  const finalPrefix = propertyPrefix ? `${propertyPrefix}-` : '';\r\n  const finalSuffix = propertySuffix ? `-${propertySuffix}` : '';\r\n  const top = `${finalPrefix}top${finalSuffix}` as StyleObjectKey;\r\n  const right = `${finalPrefix}right${finalSuffix}` as StyleObjectKey;\r\n  const bottom = `${finalPrefix}bottom${finalSuffix}` as StyleObjectKey;\r\n  const left = `${finalPrefix}left${finalSuffix}` as StyleObjectKey;\r\n  const result = getStyles(elm, [top, right, bottom, left]);\r\n  return {\r\n    t: parseToZeroOrNumber(result[top]),\r\n    r: parseToZeroOrNumber(result[right]),\r\n    b: parseToZeroOrNumber(result[bottom]),\r\n    l: parseToZeroOrNumber(result[left]),\r\n  };\r\n};\r\n\r\nexport const getTrasformTranslateValue = (\r\n  value: string | number | XY<string | number>,\r\n  isHorizontal?: boolean\r\n) =>\r\n  `translate${\r\n    isObject(value) ? `(${value.x},${value.y})` : `${isHorizontal ? 'X' : 'Y'}(${value})`\r\n  }`;\r\n", "import type { HTMLElementTarget } from './types';\r\nimport { getStyles } from './style';\r\nimport { mathRound, wnd } from '../utils/alias';\r\nimport { bind } from '../utils/function';\r\nimport { strHeight, strWidth } from '../utils/strings';\r\n\r\nexport interface WH<T = number> {\r\n  w: T;\r\n  h: T;\r\n}\r\n\r\nconst elementHasDimensions = (elm: HTMLElement): boolean =>\r\n  !!(elm.offsetWidth || elm.offsetHeight || elm.getClientRects().length);\r\nconst zeroObj: WH = {\r\n  w: 0,\r\n  h: 0,\r\n};\r\n\r\nconst getElmWidthHeightProperty = <E extends HTMLElement | Window>(\r\n  property: E extends HTMLElement ? 'client' | 'offset' | 'scroll' : 'inner',\r\n  elm: E | false | null | undefined\r\n): Readonly<WH> =>\r\n  elm\r\n    ? {\r\n        w: (elm as any)[`${property}Width`],\r\n        h: (elm as any)[`${property}Height`],\r\n      }\r\n    : zeroObj;\r\n\r\n/**\r\n * Returns the window inner- width and height.\r\n */\r\nexport const getWindowSize = (customWnd?: Window): Readonly<WH> =>\r\n  getElmWidthHeightProperty('inner', customWnd || wnd);\r\n\r\n/**\r\n * Returns the scroll- width and height of the passed element. If the element is null the width and height values are 0.\r\n * @param elm The element of which the scroll- width and height shall be returned.\r\n */\r\nexport const getOffsetSize = bind(getElmWidthHeightProperty<HTMLElement>, 'offset') satisfies (\r\n  elm: HTMLElementTarget\r\n) => Readonly<WH>;\r\n\r\n/**\r\n * Returns the client- width and height of the passed element. If the element is null the width and height values are 0.\r\n * @param elm The element of which the client- width and height shall be returned.\r\n */\r\nexport const getClientSize = bind(getElmWidthHeightProperty<HTMLElement>, 'client') satisfies (\r\n  elm: HTMLElementTarget\r\n) => Readonly<WH>;\r\n\r\n/**\r\n * Returns the client- width and height of the passed element. If the element is null the width and height values are 0.\r\n * @param elm The element of which the client- width and height shall be returned.\r\n */\r\nexport const getScrollSize = bind(getElmWidthHeightProperty<HTMLElement>, 'scroll') satisfies (\r\n  elm: HTMLElementTarget\r\n) => Readonly<WH>;\r\n\r\n/**\r\n * Returns the fractional- width and height of the passed element. If the element is null the width and height values are 0.\r\n * @param elm The element of which the fractional- width and height shall be returned.\r\n */\r\nexport const getFractionalSize = (elm: HTMLElementTarget): Readonly<WH> => {\r\n  const cssWidth = parseFloat(getStyles(elm, strWidth)) || 0;\r\n  const cssHeight = parseFloat(getStyles(elm, strHeight)) || 0;\r\n  return {\r\n    w: cssWidth - mathRound(cssWidth),\r\n    h: cssHeight - mathRound(cssHeight),\r\n  };\r\n};\r\n\r\n/**\r\n * Returns the BoundingClientRect of the passed element.\r\n * @param elm The element of which the BoundingClientRect shall be returned.\r\n */\r\nexport const getBoundingClientRect = (elm: HTMLElement): DOMRect => elm.getBoundingClientRect();\r\n\r\n/**\r\n * Determines whether the passed element has any dimensions.\r\n * @param elm The element.\r\n */\r\nexport const hasDimensions = (elm: HTMLElementTarget): boolean =>\r\n  !!elm && elementHasDimensions(elm);\r\n\r\n/**\r\n * Determines whether the passed DOM Rect has any dimensions.\r\n */\r\nexport const domRectHasDimensions = (rect?: DOMRectReadOnly | false | null) =>\r\n  !!(rect && (rect[strHeight] || rect[strWidth]));\r\n\r\n/**\r\n * Determines whether current DOM Rect has appeared according the the previous dom rect..\r\n * @param currContentRect The current DOM Rect.\r\n * @param prevContentRect The previous DOM Rect.\r\n * @returns Whether the dom rect appeared.\r\n */\r\nexport const domRectAppeared = (\r\n  currContentRect: DOMRectReadOnly | false | null | undefined,\r\n  prevContentRect: DOMRectReadOnly | false | null | undefined\r\n) => {\r\n  const rectHasDimensions = domRectHasDimensions(currContentRect);\r\n  const rectHadDimensions = domRectHasDimensions(prevContentRect);\r\n  return !rectHadDimensions && rectHasDimensions;\r\n};\r\n", "import type { DomTokens } from './attribute';\r\nimport { each, runEachAndClear } from '../utils/array';\r\nimport { bind } from '../utils/function';\r\nimport { keys } from '../utils';\r\nimport { getDomTokensArray } from './attribute';\r\n\r\nexport interface EventListenerOptions {\r\n  _capture?: boolean;\r\n  _passive?: boolean;\r\n  _once?: boolean;\r\n}\r\n\r\nexport type EventListenerTarget = EventTarget | false | null | undefined;\r\n\r\nexport type EventListenerMap = {\r\n  [eventNames: string]: ((event: any) => any) | false | null | undefined;\r\n};\r\n\r\n/**\r\n * Removes the passed event listener for the passed event names with the passed options.\r\n * @param target The element from which the listener shall be removed.\r\n * @param eventNames The eventsnames for which the listener shall be removed.\r\n * @param listener The listener which shall be removed.\r\n * @param capture The options of the removed listener.\r\n */\r\nexport const removeEventListener = <T extends Event = Event>(\r\n  target: EventListenerTarget,\r\n  eventNames: DomTokens,\r\n  listener: (event: T) => any,\r\n  capture?: boolean\r\n): void => {\r\n  each(getDomTokensArray(eventNames), (eventName) => {\r\n    target && target.removeEventListener(eventName, listener as EventListener, capture);\r\n  });\r\n};\r\n\r\n/**\r\n * Adds the passed event listener for the passed event names with the passed options.\r\n * @param target The element to which the listener shall be added.\r\n * @param eventNames The eventsnames for which the listener shall be called.\r\n * @param listener The listener which is called on the eventnames.\r\n * @param options The options of the added listener.\r\n */\r\nexport const addEventListener = <T extends Event = Event>(\r\n  target: EventListenerTarget,\r\n  eventNames: DomTokens,\r\n  listener: ((event: T) => any) | false | null | undefined,\r\n  options?: EventListenerOptions\r\n): (() => void) => {\r\n  const passive = (options && options._passive) ?? true;\r\n  const capture = (options && options._capture) || false;\r\n  const once = (options && options._once) || false;\r\n  const nativeOptions: AddEventListenerOptions = {\r\n    passive,\r\n    capture,\r\n  };\r\n\r\n  return bind(\r\n    runEachAndClear,\r\n    getDomTokensArray(eventNames).map((eventName) => {\r\n      const finalListener = (\r\n        once\r\n          ? (evt: T) => {\r\n              removeEventListener(target, eventName, finalListener, capture);\r\n              listener && listener(evt);\r\n            }\r\n          : listener\r\n      ) as EventListener;\r\n\r\n      target && target.addEventListener(eventName, finalListener, nativeOptions);\r\n      return bind(removeEventListener, target, eventName, finalListener, capture);\r\n    })\r\n  );\r\n};\r\n\r\n/**\r\n * Adds the passed event listeners for the passed event names with the passed options.\r\n * @param target The element to which the listener shall be added.\r\n * @param eventListenerMap A map which descirbes the event names and event listeners to be added.\r\n * @param options The options of the added listeners.\r\n */\r\nexport const addEventListeners = (\r\n  target: EventListenerTarget,\r\n  eventListenerMap: EventListenerMap,\r\n  options?: EventListenerOptions\r\n): (() => void) =>\r\n  bind(\r\n    runEachAndClear,\r\n    keys(eventListenerMap).map((eventNames) =>\r\n      addEventListener(target, eventNames, eventListenerMap[eventNames], options)\r\n    )\r\n  );\r\n\r\n/**\r\n * Shorthand for the stopPropagation event Method.\r\n * @param evt The event of which the stopPropagation method shall be called.\r\n */\r\nexport const stopPropagation = (evt: Event): void => evt.stopPropagation();\r\n\r\n/**\r\n * Shorthand for the preventDefault event Method.\r\n * @param evt The event of which the preventDefault method shall be called.\r\n */\r\nexport const preventDefault = (evt: Event): void => evt.preventDefault();\r\n\r\n/**\r\n * Shorthand for the stopPropagation and preventDefault event Method.\r\n * @param evt The event of which the stopPropagation and preventDefault methods shall be called.\r\n */\r\nexport const stopAndPrevent = (evt: Event): void =>\r\n  (stopPropagation(evt) as undefined) || (preventDefault(evt) as undefined);\r\n", "import type { XY } from './offset';\r\nimport type { WH } from './dimensions';\r\nimport { capNumber, isNumber, mathAbs, mathSign } from '../utils';\r\n\r\nexport interface ScrollCoordinates {\r\n  /** The start (origin) scroll coordinates for each axis. */\r\n  _start: XY<number>;\r\n  /** The end scroll coordinates for each axis. */\r\n  _end: XY<number>;\r\n}\r\n\r\n/**\r\n * Scroll the passed element to the passed position.\r\n * @param elm The element to be scrolled.\r\n * @param position The scroll position.\r\n */\r\nexport const scrollElementTo = (\r\n  elm: HTMLElement,\r\n  position: Partial<XY<number | false | null | undefined>> | number | false | null | undefined\r\n): void => {\r\n  const { x, y } = isNumber(position) ? { x: position, y: position } : position || {};\r\n  isNumber(x) && (elm.scrollLeft = x);\r\n  isNumber(y) && (elm.scrollTop = y);\r\n};\r\n\r\n/**\r\n * Scroll the passed element to the passed position.\r\n * @param elm The element to be scrolled.\r\n * @param position The scroll position.\r\n */\r\nexport const getElementScroll = (elm: HTMLElement): Readonly<XY> => ({\r\n  x: elm.scrollLeft,\r\n  y: elm.scrollTop,\r\n});\r\n\r\n/**\r\n * Scroll Coordinates which are 0.\r\n */\r\nexport const getZeroScrollCoordinates = (): ScrollCoordinates => ({\r\n  _start: { x: 0, y: 0 },\r\n  _end: { x: 0, y: 0 },\r\n});\r\n\r\n/**\r\n * Sanatizes raw scroll coordinates.\r\n * The passed `overflowAmount` is used as the \"max\" value for each axis if the sign of the raw max value is not `0`.\r\n * Makes sure that each axis has `0` either in the start or end coordinates.\r\n * @param rawScrollCoordinates The raw scroll coordinates.\r\n * @param overflowAmount The overflow amount.\r\n * @returns\r\n */\r\nexport const sanitizeScrollCoordinates = (\r\n  rawScrollCoordinates: ScrollCoordinates,\r\n  overflowAmount: WH<number>\r\n) => {\r\n  const { _start, _end } = rawScrollCoordinates;\r\n  const { w, h } = overflowAmount;\r\n  const sanitizeAxis = (start: number, end: number, amount: number) => {\r\n    let newStart = mathSign(start) * amount;\r\n    let newEnd = mathSign(end) * amount;\r\n\r\n    if (newStart === newEnd) {\r\n      const startAbs = mathAbs(start);\r\n      const endAbs = mathAbs(end);\r\n\r\n      newEnd = startAbs > endAbs ? 0 : newEnd;\r\n      newStart = startAbs < endAbs ? 0 : newStart;\r\n    }\r\n\r\n    // in doubt set start to 0\r\n    newStart = newStart === newEnd ? 0 : newStart;\r\n\r\n    return [newStart + 0, newEnd + 0] as const; // \"+ 0\" prevents \"-0\" to be in the result\r\n  };\r\n\r\n  const [startX, endX] = sanitizeAxis(_start.x, _end.x, w);\r\n  const [startY, endY] = sanitizeAxis(_start.y, _end.y, h);\r\n\r\n  return {\r\n    _start: {\r\n      x: startX,\r\n      y: startY,\r\n    },\r\n    _end: {\r\n      x: endX,\r\n      y: endY,\r\n    },\r\n  };\r\n};\r\n\r\n/**\r\n * Returns whether the passed scroll coordinates represent the browsers default scroll direction.\r\n * For the default scroll direction the following must be true:\r\n * 1. Start value is `0`.\r\n * 2. End value <= Start value.\r\n * @param scrollCoordinates The scroll coordinates.\r\n */\r\nexport const isDefaultDirectionScrollCoordinates = ({\r\n  _start,\r\n  _end,\r\n}: ScrollCoordinates): XY<boolean> => {\r\n  const getAxis = (start: number, end: number) => start === 0 && start <= end;\r\n\r\n  return {\r\n    x: getAxis(_start.x, _end.x),\r\n    y: getAxis(_start.y, _end.y),\r\n  };\r\n};\r\n\r\n/**\r\n * Gets the current scroll percent between 0..1 for each axis.\r\n * @param scrollCoordinates The scroll coordinates.\r\n * @param currentScroll The current scroll position of the element.\r\n */\r\nexport const getScrollCoordinatesPercent = (\r\n  { _start, _end }: ScrollCoordinates,\r\n  currentScroll: XY<number>\r\n) => {\r\n  const getAxis = (start: number, end: number, current: number) =>\r\n    capNumber(0, 1, (start - current) / (start - end) || 0);\r\n\r\n  return {\r\n    x: getAxis(_start.x, _end.x, currentScroll.x),\r\n    y: getAxis(_start.y, _end.y, currentScroll.y),\r\n  };\r\n};\r\n\r\n/**\r\n * Gets the scroll position of the given percent.\r\n * @param scrollCoordinates The scroll coordinates.\r\n * @param percent The percentage of the scroll.\r\n */\r\nexport const getScrollCoordinatesPosition = (\r\n  { _start, _end }: ScrollCoordinates,\r\n  percent: XY<number>\r\n) => {\r\n  const getAxis = (start: number, end: number, p: number) => start + (end - start) * p;\r\n\r\n  return {\r\n    x: getAxis(_start.x, _end.x, percent.x),\r\n    y: getAxis(_start.y, _end.y, percent.y),\r\n  };\r\n};\r\n", "import type { NodeElementTarget } from './types';\r\n\r\nexport const focusElement = (element: NodeElementTarget) => {\r\n  if (element && (element as HTMLElement).focus) {\r\n    (element as HTMLElement).focus({ preventScroll: true });\r\n  }\r\n};\r\n", "import { isBoolean, isFunction, isString } from './utils/types';\r\nimport { keys } from './utils/object';\r\nimport { each, push, from, isEmptyArray, runEachAndClear, createOrKeepArray } from './utils/array';\r\nimport { bind } from './utils/function';\r\n\r\nexport type EventListener<EventArgs extends Record<string, any[]>, N extends keyof EventArgs> = (\r\n  ...args: EventArgs[N]\r\n) => void;\r\n\r\nexport type EventListeners<EventArgs extends Record<string, any[]>> = {\r\n  [K in keyof EventArgs]?: EventListener<EventArgs, K> | EventListener<EventArgs, K>[] | null;\r\n};\r\n\r\nexport type RemoveEvent<EventArgs extends Record<string, any[]>> = {\r\n  <N extends keyof EventArgs>(name?: N, listener?: EventListener<EventArgs, N>): void;\r\n  <N extends keyof EventArgs>(name?: N, listener?: EventListener<EventArgs, N>[]): void;\r\n  <N extends keyof EventArgs>(\r\n    name?: N,\r\n    listener?: EventListener<EventArgs, N> | EventListener<EventArgs, N>[]\r\n  ): void;\r\n};\r\n\r\nexport type AddEvent<EventArgs extends Record<string, any[]>> = {\r\n  (eventListeners: EventListeners<EventArgs>, pure?: boolean): () => void;\r\n  <N extends keyof EventArgs>(name: N, listener: EventListener<EventArgs, N>): () => void;\r\n  <N extends keyof EventArgs>(name: N, listener: EventListener<EventArgs, N>[]): () => void;\r\n  <N extends keyof EventArgs>(\r\n    nameOrEventListeners: N | EventListeners<EventArgs>,\r\n    listener?: EventListener<EventArgs, N> | EventListener<EventArgs, N>[] | boolean\r\n  ): () => void;\r\n};\r\n\r\nexport type TriggerEvent<EventArgs extends Record<string, any[]>> = {\r\n  <N extends keyof EventArgs>(name: N, args: EventArgs[N]): void;\r\n};\r\n\r\nexport type EventListenerHub<EventArgs extends Record<string, any[]>> = [\r\n  AddEvent<EventArgs>,\r\n  RemoveEvent<EventArgs>,\r\n  TriggerEvent<EventArgs>\r\n];\r\n\r\nconst manageListener = <EventArgs extends Record<string, any[]>, N extends keyof EventArgs>(\r\n  callback: (listener?: EventListener<EventArgs, N>) => void,\r\n  listener?: EventListener<EventArgs, N> | EventListener<EventArgs, N>[]\r\n) => {\r\n  each(createOrKeepArray(listener), callback);\r\n};\r\n\r\nexport const createEventListenerHub = <EventArgs extends Record<string, any[]>>(\r\n  initialEventListeners?: EventListeners<EventArgs>\r\n): EventListenerHub<EventArgs> => {\r\n  const events = new Map<keyof EventArgs, Set<EventListener<EventArgs, keyof EventArgs>>>();\r\n\r\n  const removeEvent: RemoveEvent<EventArgs> = (name, listener) => {\r\n    if (name) {\r\n      const eventSet = events.get(name);\r\n      manageListener((currListener) => {\r\n        if (eventSet) {\r\n          eventSet[currListener ? 'delete' : 'clear'](currListener! as any);\r\n        }\r\n      }, listener);\r\n    } else {\r\n      events.forEach((eventSet) => {\r\n        eventSet.clear();\r\n      });\r\n      events.clear();\r\n    }\r\n  };\r\n\r\n  const addEvent: AddEvent<EventArgs> = (\r\n    nameOrEventListeners: keyof EventArgs | EventListeners<EventArgs>,\r\n    listenerOrPure?:\r\n      | EventListener<EventArgs, keyof EventArgs>\r\n      | EventListener<EventArgs, keyof EventArgs>[]\r\n      | boolean\r\n  ) => {\r\n    if (isString(nameOrEventListeners)) {\r\n      const eventSet = events.get(nameOrEventListeners) || new Set();\r\n      events.set(nameOrEventListeners, eventSet);\r\n\r\n      manageListener((currListener) => {\r\n        isFunction(currListener) && eventSet.add(currListener);\r\n      }, listenerOrPure as Exclude<typeof listenerOrPure, boolean>);\r\n\r\n      return bind(\r\n        removeEvent,\r\n        nameOrEventListeners,\r\n        listenerOrPure as Exclude<typeof listenerOrPure, boolean>\r\n      );\r\n    }\r\n    if (isBoolean(listenerOrPure) && listenerOrPure) {\r\n      removeEvent();\r\n    }\r\n\r\n    const eventListenerKeys = keys(nameOrEventListeners) as (keyof EventListeners<EventArgs>)[];\r\n    const offFns: (() => void)[] = [];\r\n    each(eventListenerKeys, (key) => {\r\n      const eventListener = (nameOrEventListeners as EventListeners<EventArgs>)[key];\r\n      eventListener && push(offFns, addEvent(key, eventListener));\r\n    });\r\n\r\n    return bind(runEachAndClear, offFns);\r\n  };\r\n\r\n  const triggerEvent: TriggerEvent<EventArgs> = (name, args) => {\r\n    each(from(events.get(name)), (event) => {\r\n      if (args && !isEmptyArray(args)) {\r\n        (event as (...eventArgs: EventArgs[keyof EventArgs]) => void).apply(0, args);\r\n      } else {\r\n        (event as () => void)();\r\n      }\r\n    });\r\n  };\r\n\r\n  addEvent(initialEventListeners || {});\r\n\r\n  return [addEvent, removeEvent, triggerEvent];\r\n};\r\n", "import type { OverlayScrollbars, OverlayScrollbarsStatic } from '../overlayscrollbars';\r\nimport type { EventListener, EventListenerArgs, EventListeners } from '../eventListeners';\r\nimport { each, keys } from '../support';\r\n\r\nexport type PluginModuleInstance = Record<string | number | symbol, any>;\r\n\r\nexport type InstancePluginEvent = {\r\n  /**\r\n   * Adds event listeners to the instance.\r\n   * @param eventListeners An object which contains the added listeners.\r\n   * @returns Returns a function which removes the added listeners.\r\n   */\r\n  (eventListeners: EventListeners): () => void;\r\n  /**\r\n   * Adds a single event listener to the instance.\r\n   * @param name The name of the event.\r\n   * @param listener The listener which is invoked on that event.\r\n   * @returns Returns a function which removes the added listeners.\r\n   */\r\n  <N extends keyof EventListenerArgs>(name: N, listener: EventListener<N>): () => void;\r\n  /**\r\n   * Adds multiple event listeners to the instance.\r\n   * @param name The name of the event.\r\n   * @param listener The listeners which are invoked on that event.\r\n   * @returns Returns a function which removes the added listeners.\r\n   */\r\n  <N extends keyof EventListenerArgs>(name: N, listener: EventListener<N>[]): () => void;\r\n};\r\n\r\n/**\r\n * Describes a OverlayScrollbars plugin module.\r\n * Plugin modules must be side-effect free and deterministic. (same input produces same output)\r\n */\r\nexport type PluginModule<\r\n  S extends PluginModuleInstance | void = PluginModuleInstance | void,\r\n  I extends PluginModuleInstance | void = PluginModuleInstance | void,\r\n> = (S extends PluginModuleInstance\r\n  ? {\r\n      /**\r\n       * Creates a plugin which is bound to the static object.\r\n       * The function will be called once with the static object as soon as the plugin is registered.\r\n       * The plugin can add new methods or fields to the passed static object.\r\n       * @param osStatic The static object the plugin is bound to.\r\n       * @returns The plugins instance object or a falsy value if the plugin doesn't need any instance object.\r\n       */\r\n      static: (osStatic: OverlayScrollbarsStatic) => S | void;\r\n    }\r\n  : object) &\r\n  (I extends PluginModuleInstance\r\n    ? {\r\n        /**\r\n         * Creates a A plugin which is bound to an instance.\r\n         * The function will be called each time a new instance is created.\r\n         * The plugin can add new methods or fields to the passed instance object.\r\n         * @param osInstance The instance object the plugin is bound to.\r\n         * @param event A function which adds events to the instance which can't be removed from outside the plugin. (instance events added with the `on` function can be removed with the optional `pure` parameter)\r\n         * @param osStatic The static object the plugin is bound to.\r\n         * @returns The plugins instance object or a falsy value if the plugin doesn't need any instance object.\r\n         */\r\n        instance: (\r\n          osInstance: OverlayScrollbars,\r\n          event: InstancePluginEvent,\r\n          osStatic: OverlayScrollbarsStatic\r\n        ) => I | void;\r\n      }\r\n    : object);\r\n\r\n/**\r\n * Describes a OverlayScrollbar plugin.\r\n */\r\nexport type Plugin<\r\n  Name extends string = string,\r\n  S extends PluginModuleInstance | void = PluginModuleInstance | void,\r\n  I extends PluginModuleInstance | void = PluginModuleInstance | void,\r\n> = {\r\n  /** The field is the plugins name. Plugin names must be globally unique, please choose wisely. */\r\n  [pluginName in Name]: PluginModule<S, I>;\r\n};\r\n\r\n/**\r\n * Describes a OverlayScrollbar plugin which has only a static module.\r\n */\r\nexport type StaticPlugin<\r\n  Name extends string = string,\r\n  T extends PluginModuleInstance = PluginModuleInstance,\r\n> = Plugin<Name, T, void>;\r\n\r\n/**\r\n * Describes a OverlayScrollbar plugin which has only a instance module.\r\n */\r\nexport type InstancePlugin<\r\n  Name extends string = string,\r\n  T extends PluginModuleInstance = PluginModuleInstance,\r\n> = Plugin<Name, void, T>;\r\n\r\n/**\r\n * Infers the type of the static modules instance of the passed plugin.\r\n */\r\nexport type InferStaticPluginModuleInstance<T extends StaticPlugin> =\r\n  T extends StaticPlugin<infer Name>\r\n    ? T[Name]['static'] extends (...args: any[]) => any\r\n      ? ReturnType<T[Name]['static']>\r\n      : void\r\n    : void;\r\n\r\n/**\r\n * Infers the type of the instance modules instance of the passed plugin.\r\n */\r\nexport type InferInstancePluginModuleInstance<T extends InstancePlugin> =\r\n  T extends InstancePlugin<infer Name>\r\n    ? T[Name]['instance'] extends (...args: any[]) => any\r\n      ? ReturnType<T[Name]['instance']>\r\n      : void\r\n    : void;\r\n\r\n/** All registered plugin modules. */\r\nexport const pluginModules: Record<string, PluginModule> = {};\r\n\r\n/** All static plugin module instances. */\r\nexport const staticPluginModuleInstances: Record<string, PluginModuleInstance | void> = {};\r\n\r\n/**\r\n * Adds plugins.\r\n * @param addedPlugin The plugin(s) to add.\r\n * @returns The added plugin modules of the registered plugins.\r\n */\r\nexport const addPlugins = (addedPlugin: Plugin[]) => {\r\n  each(addedPlugin, (plugin) =>\r\n    each(plugin, (_, key) => {\r\n      pluginModules[key] = plugin[key];\r\n    })\r\n  );\r\n};\r\n\r\nexport const registerPluginModuleInstances = (\r\n  plugin: Plugin,\r\n  staticObj: OverlayScrollbarsStatic,\r\n  instanceInfo?: [\r\n    instanceObj: OverlayScrollbars,\r\n    event: InstancePluginEvent,\r\n    instancePluginMap?: Record<string, PluginModuleInstance>,\r\n  ]\r\n): Array<PluginModuleInstance | void> =>\r\n  keys(plugin).map((name) => {\r\n    const { static: osStatic, instance: osInstance } = (\r\n      plugin as Plugin<string, PluginModuleInstance, PluginModuleInstance>\r\n    )[name];\r\n    const [instanceObj, event, instancePluginMap] = instanceInfo || [];\r\n    const ctor = instanceInfo ? osInstance : osStatic;\r\n    if (ctor) {\r\n      const instance = instanceInfo\r\n        ? (\r\n            ctor as Extract<\r\n              typeof ctor,\r\n              (\r\n                osInstance: OverlayScrollbars,\r\n                event: InstancePluginEvent,\r\n                osStatic: OverlayScrollbarsStatic\r\n              ) => PluginModuleInstance | void\r\n            >\r\n          )(instanceObj!, event!, staticObj)\r\n        : (\r\n            ctor as Extract<\r\n              typeof ctor,\r\n              (osStatic: OverlayScrollbarsStatic) => PluginModuleInstance | void\r\n            >\r\n          )(staticObj);\r\n      return ((instancePluginMap || staticPluginModuleInstances)[name] = instance);\r\n    }\r\n  });\r\n\r\nexport const getStaticPluginModuleInstance = <T extends StaticPlugin>(\r\n  pluginModuleName: T extends StaticPlugin<infer N> ? N : never\r\n): InferStaticPluginModuleInstance<T> | undefined =>\r\n  staticPluginModuleInstances[pluginModuleName] as InferStaticPluginModuleInstance<T> | undefined;\r\n", "function _extends() {\n  return (module.exports = _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports), _extends.apply(null, arguments);\n}\nmodule.exports = _extends, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "import type { PlainObject, DeepPartial } from '../../typings';\r\nimport type { OptionsObject } from '../../options';\r\nimport {\r\n  each,\r\n  hasOwnProperty,\r\n  keys,\r\n  push,\r\n  isEmptyObject,\r\n  type,\r\n  isUndefined,\r\n  isPlainObject,\r\n  isString,\r\n  createOrKeepArray,\r\n} from '../../support';\r\n\r\nexport type OptionsFunctionType = (this: any, ...args: any[]) => any;\r\nexport type OptionsTemplateType<T extends OptionsTemplateNativeTypes> = ExtractPropsKey<\r\n  OptionsTemplateTypeMap,\r\n  T\r\n>;\r\nexport type OptionsTemplateTypes = keyof OptionsTemplateTypeMap;\r\nexport type OptionsTemplateNativeTypes = OptionsTemplateTypeMap[keyof OptionsTemplateTypeMap];\r\n\r\nexport type OptionsTemplateValue<T extends OptionsTemplateNativeTypes = string> = T extends string\r\n  ? string extends T\r\n    ? OptionsTemplateValueNonEnum<T>\r\n    : string\r\n  : OptionsTemplateValueNonEnum<T>;\r\n\r\nexport type OptionsTemplate<T> = {\r\n  [P in keyof T]: T[P] extends OptionsObject\r\n    ? OptionsTemplate<T[P]>\r\n    : T[P] extends OptionsTemplateNativeTypes\r\n      ? OptionsTemplateValue<T[P]>\r\n      : never;\r\n};\r\n\r\nexport type OptionsValidationResult<T> = [\r\n  validated: DeepPartial<T>,\r\n  foreign: Record<string, unknown>,\r\n];\r\n\r\ntype OptionsTemplateTypeMap = {\r\n  __TPL_boolean_TYPE__: boolean;\r\n  __TPL_number_TYPE__: number;\r\n  __TPL_string_TYPE__: string;\r\n  __TPL_array_TYPE__: Array<any> | ReadonlyArray<any>;\r\n  __TPL_function_TYPE__: OptionsFunctionType;\r\n  __TPL_null_TYPE__: null;\r\n  __TPL_object_TYPE__: OptionsObject;\r\n};\r\n\r\ntype OptionsTemplateValueNonEnum<T extends OptionsTemplateNativeTypes> =\r\n  | OptionsTemplateType<T>\r\n  | [OptionsTemplateType<T>, ...Array<OptionsTemplateTypes>];\r\n\r\ntype ExtractPropsKey<T, TProps extends T[keyof T]> = {\r\n  [P in keyof T]: TProps extends T[P] ? P : never;\r\n}[keyof T];\r\n\r\ntype OptionsTemplateTypesDictionary = {\r\n  readonly boolean: OptionsTemplateType<boolean>;\r\n  readonly number: OptionsTemplateType<number>;\r\n  readonly string: OptionsTemplateType<string>;\r\n  readonly array: OptionsTemplateType<Array<any>>;\r\n  readonly object: OptionsTemplateType<OptionsObject>;\r\n  readonly function: OptionsTemplateType<OptionsFunctionType>;\r\n  readonly null: OptionsTemplateType<null>;\r\n};\r\n\r\nconst optionsTemplateTypes: OptionsTemplateTypesDictionary = {\r\n  boolean: '__TPL_boolean_TYPE__',\r\n  number: '__TPL_number_TYPE__',\r\n  string: '__TPL_string_TYPE__',\r\n  array: '__TPL_array_TYPE__',\r\n  object: '__TPL_object_TYPE__',\r\n  function: '__TPL_function_TYPE__',\r\n  null: '__TPL_null_TYPE__',\r\n};\r\n\r\n/**\r\n * Validates the given options object according to the given template object and returns a object which looks like:\r\n * {\r\n *  foreign   : a object which consists of properties which aren't defined inside the template. (foreign properties)\r\n *  validated : a object which consists only of valid properties. (property name is inside the template and value has a correct type)\r\n * }\r\n * @param template The template according to which the options object shall be validated.\r\n * @param options The options object which shall be validated.\r\n * @param optionsDiff When provided the returned validated object will only have properties which are different to this objects properties.\r\n * Example (assume all properties are valid to the template):\r\n * Options object            : { a: 'a', b: 'b', c: 'c' }\r\n * optionsDiff object        : { a: 'a', b: 'b', c: undefined }\r\n * Returned validated object : { c: 'c' }\r\n * Because the value of the properties a and b didn't change, they aren't included in the returned object.\r\n * Without the optionsDiff object the returned validated object would be: { a: 'a', b: 'b', c: 'c' }\r\n * @param doWriteErrors True if errors shall be logged into the console, false otherwise.\r\n * @param propPath The propertyPath which lead to this object. (used for error logging)\r\n */\r\nconst validateRecursive = <T extends PlainObject>(\r\n  template: OptionsTemplate<T>,\r\n  options: DeepPartial<T>,\r\n  doWriteErrors?: boolean,\r\n  propPath?: string\r\n): OptionsValidationResult<T> => {\r\n  const validatedOptions: DeepPartial<T> = {};\r\n  const optionsCopy: DeepPartial<T> = { ...options };\r\n  const props = keys(template).filter((prop) => hasOwnProperty(options, prop));\r\n\r\n  each(props, (prop: Extract<keyof T, string>) => {\r\n    const optionsValue: any = options[prop];\r\n    const templateValue: PlainObject | string | OptionsTemplateTypes | Array<OptionsTemplateTypes> =\r\n      template[prop];\r\n    const templateIsComplex = isPlainObject(templateValue);\r\n    const propPrefix = propPath ? `${propPath}.` : '';\r\n\r\n    // if the template has a object as value, it means that the options are complex (nested)\r\n    if (templateIsComplex && isPlainObject(optionsValue)) {\r\n      const [validated, foreign] = validateRecursive(\r\n        templateValue as T,\r\n        optionsValue,\r\n        doWriteErrors,\r\n        propPrefix + prop\r\n      );\r\n      validatedOptions[prop] = validated as any;\r\n      optionsCopy[prop] = foreign as any;\r\n\r\n      each([optionsCopy, validatedOptions], (value) => {\r\n        if (isEmptyObject(value[prop])) {\r\n          delete value[prop];\r\n        }\r\n      });\r\n    } else if (!templateIsComplex) {\r\n      let isValid = false;\r\n      const errorEnumStrings: Array<string> = [];\r\n      const errorPossibleTypes: Array<string> = [];\r\n      const optionsValueType = type(optionsValue) as keyof typeof optionsTemplateTypes;\r\n      const templateValueArr: Array<string | OptionsTemplateTypes> =\r\n        createOrKeepArray(templateValue);\r\n\r\n      each(templateValueArr, (currTemplateType) => {\r\n        // if currType value isn't inside possibleTemplateTypes we assume its a enum string value\r\n        let typeString: string | undefined;\r\n        each(optionsTemplateTypes, (value: string, key: string) => {\r\n          if (value === currTemplateType) {\r\n            typeString = key;\r\n          }\r\n        });\r\n        const isEnumString = isUndefined(typeString);\r\n        if (isEnumString && isString(optionsValue)) {\r\n          // split it into a array which contains all possible values for example: [\"yes\", \"no\", \"maybe\"]\r\n          const enumStringSplit = currTemplateType.split(' ');\r\n          isValid = !!enumStringSplit.find((possibility) => possibility === optionsValue);\r\n\r\n          // build error message\r\n          push(errorEnumStrings, enumStringSplit);\r\n        } else {\r\n          isValid = optionsTemplateTypes[optionsValueType] === currTemplateType;\r\n        }\r\n\r\n        // build error message\r\n        push(errorPossibleTypes, isEnumString ? optionsTemplateTypes.string : typeString!);\r\n\r\n        // continue if invalid, break if valid\r\n        return !isValid;\r\n      });\r\n\r\n      if (isValid) {\r\n        validatedOptions[prop] = optionsValue;\r\n      } else if (doWriteErrors) {\r\n        console.warn(\r\n          `${\r\n            `The option \"${propPrefix}${prop}\" wasn't set, because it doesn't accept the type [ ${optionsValueType.toUpperCase()} ] with the value of \"${optionsValue}\".\\r\\n` +\r\n            `Accepted types are: [ ${errorPossibleTypes.join(', ').toUpperCase()} ].\\r\\n`\r\n          }${\r\n            errorEnumStrings.length > 0\r\n              ? `\\r\\nValid strings are: [ ${errorEnumStrings.join(', ')} ].`\r\n              : ''\r\n          }`\r\n        );\r\n      }\r\n\r\n      delete optionsCopy[prop];\r\n    }\r\n  });\r\n\r\n  return [validatedOptions, optionsCopy]; // optionsCopy equals now to foreign options\r\n};\r\n\r\n/**\r\n * Validates the given options object according to the given template object and returns a tuple which looks like:\r\n * [\r\n *  validated : a object which consists only of valid properties. (property name is inside the template and value has a correct type)\r\n *  foreign   : a object which consists of properties which aren't defined inside the template. (foreign properties)\r\n * ]\r\n * @param template The template according to which the options object shall be validated.\r\n * @param options The options object which shall be validated.\r\n * @param doWriteErrors True if errors shall be logged into the console, false otherwise.\r\n */\r\nconst validateOptions = <T extends PlainObject>(\r\n  template: OptionsTemplate<T>,\r\n  options: DeepPartial<T>,\r\n  doWriteErrors?: boolean\r\n): OptionsValidationResult<T> => validateRecursive<T>(template, options, doWriteErrors);\r\n\r\nexport { validateOptions, optionsTemplateTypes };\r\n", "import type {\r\n  Options,\r\n  PartialOptions,\r\n  OverflowBehavior,\r\n  ScrollbarsVisibilityBehavior,\r\n  ScrollbarsAutoHideBehavior,\r\n  ScrollbarsClickScrollBehavior,\r\n} from '../../options';\r\nimport type { OptionsTemplate, OptionsTemplateValue } from './validation';\r\nimport type { StaticPlugin } from '../plugins';\r\nimport { validateOptions, optionsTemplateTypes as oTypes } from './validation';\r\n\r\nexport const optionsValidationPluginModuleName = '__osOptionsValidationPlugin';\r\n\r\nexport const OptionsValidationPlugin = /* @__PURE__ */ (() => ({\r\n  [optionsValidationPluginModuleName]: {\r\n    static: () => {\r\n      const numberAllowedValues: OptionsTemplateValue<number> = oTypes.number;\r\n      const booleanAllowedValues: OptionsTemplateValue<boolean> = oTypes.boolean;\r\n      const arrayNullValues: OptionsTemplateValue<Array<unknown> | null> = [\r\n        oTypes.array,\r\n        oTypes.null,\r\n      ];\r\n      const overflowAllowedValues: OptionsTemplateValue<OverflowBehavior> =\r\n        'hidden scroll visible visible-hidden';\r\n      const scrollbarsVisibilityAllowedValues: OptionsTemplateValue<ScrollbarsVisibilityBehavior> =\r\n        'visible hidden auto';\r\n      const scrollbarsAutoHideAllowedValues: OptionsTemplateValue<ScrollbarsAutoHideBehavior> =\r\n        'never scroll leavemove';\r\n      const scrollbarsClickScrollAllowedValues: OptionsTemplateValue<ScrollbarsClickScrollBehavior> =\r\n        [booleanAllowedValues, oTypes.string];\r\n\r\n      const optionsTemplate: OptionsTemplate<Options> = {\r\n        paddingAbsolute: booleanAllowedValues, // true || false\r\n        showNativeOverlaidScrollbars: booleanAllowedValues, // true || false\r\n        update: {\r\n          elementEvents: arrayNullValues, // array of tuples || null\r\n          attributes: arrayNullValues,\r\n          debounce: [oTypes.number, oTypes.array, oTypes.null], // number || number array || null\r\n          ignoreMutation: [oTypes.function, oTypes.null], // function || null\r\n        },\r\n        overflow: {\r\n          x: overflowAllowedValues, // visible-hidden  || visible-scroll || hidden || scroll\r\n          y: overflowAllowedValues, // visible-hidden  || visible-scroll || hidden || scroll\r\n        },\r\n        scrollbars: {\r\n          theme: [oTypes.string, oTypes.null], // string || null\r\n          visibility: scrollbarsVisibilityAllowedValues, // visible || hidden || auto\r\n          autoHide: scrollbarsAutoHideAllowedValues, // never || scroll || leave || move ||\r\n          autoHideDelay: numberAllowedValues, // number\r\n          autoHideSuspend: booleanAllowedValues, // true || false\r\n          dragScroll: booleanAllowedValues, // true || false\r\n          clickScroll: scrollbarsClickScrollAllowedValues, // true || false || instant\r\n          pointers: [oTypes.array, oTypes.null], // string array\r\n        },\r\n        /*\r\n        textarea: {\r\n          dynWidth: booleanAllowedValues, // true || false\r\n          dynHeight: booleanAllowedValues, // true || false\r\n          inheritedAttrs: stringArrayNullAllowedValues, // string || array || nul\r\n        },\r\n        */\r\n      };\r\n      return (options: PartialOptions, doWriteErrors?: boolean): PartialOptions => {\r\n        const [validated, foreign] = validateOptions(optionsTemplate, options, doWriteErrors);\r\n        return { ...foreign, ...validated };\r\n      };\r\n    },\r\n  },\r\n}))() satisfies StaticPlugin<typeof optionsValidationPluginModuleName>;\r\n", "import { strOverflowX, strOverflowY } from './support';\r\n\r\nconst dataAttributePrefix = `data-overlayscrollbars`;\r\n\r\n// environment\r\nexport const classNameEnvironment = 'os-environment';\r\nexport const classNameEnvironmentScrollbarHidden = `${classNameEnvironment}-scrollbar-hidden`;\r\n\r\n// initialize\r\nexport const dataAttributeInitialize = `${dataAttributePrefix}-initialize`;\r\n\r\n// shared\r\nexport const dataValueNoClipping = 'noClipping';\r\n\r\n// body\r\nexport const dataAttributeHtmlBody = `${dataAttributePrefix}-body`;\r\n\r\n// host\r\nexport const dataAttributeHost = dataAttributePrefix;\r\nexport const dataValueHostIsHost = 'host';\r\n\r\n// viewport\r\nexport const dataAttributeViewport = `${dataAttributePrefix}-viewport`;\r\nexport const dataValueViewportOverflowXPrefix = strOverflowX;\r\nexport const dataValueViewportOverflowYPrefix = strOverflowY;\r\nexport const dataValueViewportArrange = 'arrange';\r\nexport const dataValueViewportMeasuring = 'measuring';\r\nexport const dataValueViewportScrolling = 'scrolling';\r\nexport const dataValueViewportScrollbarHidden = 'scrollbarHidden';\r\nexport const dataValueViewportNoContent = 'noContent';\r\n\r\n// padding\r\nexport const dataAttributePadding = `${dataAttributePrefix}-padding`;\r\n\r\n// content\r\nexport const dataAttributeContent = `${dataAttributePrefix}-content`;\r\n\r\n// size observer\r\nexport const classNameSizeObserver = 'os-size-observer';\r\nexport const classNameSizeObserverAppear = `${classNameSizeObserver}-appear`;\r\nexport const classNameSizeObserverListener = `${classNameSizeObserver}-listener`;\r\nexport const classNameSizeObserverListenerScroll = `${classNameSizeObserverListener}-scroll`;\r\nexport const classNameSizeObserverListenerItem = `${classNameSizeObserverListener}-item`;\r\nexport const classNameSizeObserverListenerItemFinal = `${classNameSizeObserverListenerItem}-final`;\r\n\r\n// trinsic observer\r\nexport const classNameTrinsicObserver = 'os-trinsic-observer';\r\n\r\n// scrollbars\r\nexport const classNameScrollbarThemeNone = 'os-theme-none';\r\nexport const classNameScrollbar = 'os-scrollbar';\r\nexport const classNameScrollbarRtl = `${classNameScrollbar}-rtl`;\r\nexport const classNameScrollbarHorizontal = `${classNameScrollbar}-horizontal`;\r\nexport const classNameScrollbarVertical = `${classNameScrollbar}-vertical`;\r\nexport const classNameScrollbarTrack = `${classNameScrollbar}-track`;\r\nexport const classNameScrollbarHandle = `${classNameScrollbar}-handle`;\r\nexport const classNameScrollbarVisible = `${classNameScrollbar}-visible`;\r\nexport const classNameScrollbarCornerless = `${classNameScrollbar}-cornerless`;\r\nexport const classNameScrollbarTransitionless = `${classNameScrollbar}-transitionless`;\r\nexport const classNameScrollbarInteraction = `${classNameScrollbar}-interaction`;\r\nexport const classNameScrollbarUnusable = `${classNameScrollbar}-unusable`;\r\nexport const classNameScrollbarAutoHide = `${classNameScrollbar}-auto-hide`;\r\nexport const classNameScrollbarAutoHideHidden = `${classNameScrollbarAutoHide}-hidden`;\r\nexport const classNameScrollbarWheel = `${classNameScrollbar}-wheel`;\r\nexport const classNameScrollbarTrackInteractive = `${classNameScrollbarTrack}-interactive`;\r\nexport const classNameScrollbarHandleInteractive = `${classNameScrollbarHandle}-interactive`;\r\n", "import type { StaticPlugin } from '../plugins';\r\nimport {\r\n  createDOM,\r\n  appendChildren,\r\n  getOffsetSize,\r\n  addEventListener,\r\n  addClass,\r\n  equalWH,\r\n  cAF,\r\n  rAF,\r\n  stopPropagation,\r\n  bind,\r\n  scrollElementTo,\r\n  strWidth,\r\n  strHeight,\r\n  setStyles,\r\n} from '../../support';\r\nimport {\r\n  classNameSizeObserverListenerScroll,\r\n  classNameSizeObserverListenerItem,\r\n  classNameSizeObserverListenerItemFinal,\r\n} from '../../classnames';\r\n\r\nexport const sizeObserverPluginName = '__osSizeObserverPlugin';\r\n\r\nexport const SizeObserverPlugin = /* @__PURE__ */ (() => ({\r\n  [sizeObserverPluginName]: {\r\n    static:\r\n      () =>\r\n      (\r\n        listenerElement: HTMLElement,\r\n        onSizeChangedCallback: (appear: boolean) => any,\r\n        observeAppearChange: boolean | null | undefined\r\n      ): [appearCallback: () => void, offFns: (() => any)[]] => {\r\n        const scrollAmount = 3333333;\r\n        const scrollEventName = 'scroll';\r\n        const observerElementChildren = createDOM(\r\n          `<div class=\"${classNameSizeObserverListenerItem}\" dir=\"ltr\"><div class=\"${classNameSizeObserverListenerItem}\"><div class=\"${classNameSizeObserverListenerItemFinal}\"></div></div><div class=\"${classNameSizeObserverListenerItem}\"><div class=\"${classNameSizeObserverListenerItemFinal}\" style=\"width: 200%; height: 200%\"></div></div></div>`\r\n        );\r\n        const observerElementChildrenRoot = observerElementChildren[0] as HTMLElement;\r\n        const shrinkElement = observerElementChildrenRoot.lastChild as HTMLElement;\r\n        const expandElement = observerElementChildrenRoot.firstChild as HTMLElement;\r\n        const expandElementChild = expandElement?.firstChild as HTMLElement;\r\n\r\n        let cacheSize = getOffsetSize(observerElementChildrenRoot);\r\n        let currSize = cacheSize;\r\n        let isDirty = false;\r\n        let rAFId: number;\r\n\r\n        const reset = () => {\r\n          scrollElementTo(expandElement, scrollAmount);\r\n          scrollElementTo(shrinkElement, scrollAmount);\r\n        };\r\n        const onResized = (appear?: unknown) => {\r\n          rAFId = 0;\r\n          if (isDirty) {\r\n            cacheSize = currSize;\r\n            onSizeChangedCallback(appear === true);\r\n          }\r\n        };\r\n        const onScroll = (scrollEvent?: Event | false) => {\r\n          currSize = getOffsetSize(observerElementChildrenRoot);\r\n          isDirty = !scrollEvent || !equalWH(currSize, cacheSize);\r\n\r\n          if (scrollEvent) {\r\n            stopPropagation(scrollEvent);\r\n\r\n            if (isDirty && !rAFId) {\r\n              cAF!(rAFId);\r\n              rAFId = rAF!(onResized);\r\n            }\r\n          } else {\r\n            onResized(scrollEvent === false);\r\n          }\r\n\r\n          reset();\r\n        };\r\n        const destroyFns = [\r\n          appendChildren(listenerElement, observerElementChildren),\r\n          addEventListener(expandElement, scrollEventName, onScroll),\r\n          addEventListener(shrinkElement, scrollEventName, onScroll),\r\n        ];\r\n\r\n        addClass(listenerElement, classNameSizeObserverListenerScroll);\r\n\r\n        // lets assume that the divs will never be that large and a constant value is enough\r\n        setStyles(expandElementChild, {\r\n          [strWidth]: scrollAmount,\r\n          [strHeight]: scrollAmount,\r\n        });\r\n\r\n        rAF!(reset);\r\n\r\n        return [observeAppearChange ? bind(onScroll, false) : reset, destroyFns];\r\n      },\r\n  },\r\n}))() satisfies StaticPlugin<typeof sizeObserverPluginName>;\r\n", "import type { Env } from '../../environment';\r\nimport type { XY } from '../../support';\r\nimport type { Options, OptionsCheckFn, OverflowBehavior } from '../../options';\r\nimport type { OverflowStyle } from '../../typings';\r\nimport { strHidden, strScroll, strVisible } from '../../support';\r\n\r\nexport interface ViewportOverflowState {\r\n  _overflowScroll: XY<boolean>;\r\n  _overflowStyle: XY<OverflowStyle>;\r\n}\r\n\r\nexport const getShowNativeOverlaidScrollbars = (checkOption: OptionsCheckFn<Options>, env: Env) => {\r\n  const { _nativeScrollbarsOverlaid } = env;\r\n  const [showNativeOverlaidScrollbarsOption, showNativeOverlaidScrollbarsChanged] = checkOption(\r\n    'showNativeOverlaidScrollbars'\r\n  );\r\n\r\n  return [\r\n    showNativeOverlaidScrollbarsOption &&\r\n      _nativeScrollbarsOverlaid.x &&\r\n      _nativeScrollbarsOverlaid.y,\r\n    showNativeOverlaidScrollbarsChanged,\r\n  ] as const;\r\n};\r\n\r\nexport const overflowIsVisible = (overflowBehavior: string) =>\r\n  overflowBehavior.indexOf(strVisible) === 0;\r\n\r\n/**\r\n * Creates a viewport overflow state object.\r\n * @param hasOverflow The information whether an axis has overflow.\r\n * @param overflowBehavior The overflow behavior according to the options.\r\n * @returns A object which represents the newly set overflow state.\r\n */\r\nexport const createViewportOverflowState = (\r\n  hasOverflow: Partial<XY<boolean>>,\r\n  overflowBehavior: XY<OverflowBehavior>\r\n): ViewportOverflowState => {\r\n  const getAxisOverflowStyle = (\r\n    axisBehavior: OverflowBehavior,\r\n    axisHasOverflow: boolean | undefined,\r\n    perpendicularBehavior: OverflowBehavior,\r\n    perpendicularOverflow: boolean | undefined\r\n  ): OverflowStyle => {\r\n    // convert behavior to style:\r\n    // 'visible'        -> 'hidden'\r\n    // 'hidden'         -> 'hidden'\r\n    // 'scroll'         -> 'scroll'\r\n    // 'visible-hidden' -> 'hidden'\r\n    // 'visible-scroll' -> 'scroll'\r\n    const behaviorStyle =\r\n      axisBehavior === strVisible\r\n        ? strHidden\r\n        : (axisBehavior.replace(`${strVisible}-`, '') as OverflowStyle);\r\n    const axisOverflowVisible = overflowIsVisible(axisBehavior);\r\n    const perpendicularOverflowVisible = overflowIsVisible(perpendicularBehavior);\r\n\r\n    // if no axis has overflow set 'hidden'\r\n    if (!axisHasOverflow && !perpendicularOverflow) {\r\n      return strHidden;\r\n    }\r\n\r\n    // if both axis have a visible behavior ('visible', 'visible-hidden', 'visible-scroll') set 'visible'\r\n    if (axisOverflowVisible && perpendicularOverflowVisible) {\r\n      return strVisible;\r\n    }\r\n\r\n    // this this axis has a visible behavior\r\n    if (axisOverflowVisible) {\r\n      const nonPerpendicularOverflow = axisHasOverflow ? strVisible : strHidden;\r\n      return axisHasOverflow && perpendicularOverflow\r\n        ? behaviorStyle // if both axis have an overflow set ('hidden' or 'scroll')\r\n        : nonPerpendicularOverflow; // if only this axis has an overflow set 'visible', if no axis has an overflow set 'hidden'\r\n    }\r\n\r\n    const nonOverflow =\r\n      perpendicularOverflowVisible && perpendicularOverflow ? strVisible : strHidden;\r\n    return axisHasOverflow\r\n      ? behaviorStyle // if this axis has an overflow\r\n      : nonOverflow; // if the perp. axis has a visible behavior and has an overflow set 'visible', otherwise set 'hidden'\r\n  };\r\n\r\n  const _overflowStyle = {\r\n    x: getAxisOverflowStyle(overflowBehavior.x, hasOverflow.x, overflowBehavior.y, hasOverflow.y),\r\n    y: getAxisOverflowStyle(overflowBehavior.y, hasOverflow.y, overflowBehavior.x, hasOverflow.x),\r\n  };\r\n\r\n  return {\r\n    _overflowStyle,\r\n    _overflowScroll: {\r\n      x: _overflowStyle.x === strScroll,\r\n      y: _overflowStyle.y === strScroll,\r\n    },\r\n  };\r\n};\r\n", "import type { ObserversSetupState } from '../../setups';\r\nimport type { Options, OptionsCheckFn } from '../../options';\r\nimport type { StructureSetupElementsObj } from '../../setups/structureSetup/structureSetup.elements';\r\nimport type { ViewportOverflowState } from '../../setups/structureSetup/structureSetup.utils';\r\nimport type { Env } from '../../environment';\r\nimport type { WH } from '../../support';\r\nimport type { OverflowStyle, StyleObject, StyleObjectKey } from '../../typings';\r\nimport type { StructureSetupState } from '../../setups/structureSetup';\r\nimport type { StaticPlugin } from '../plugins';\r\nimport { getShowNativeOverlaidScrollbars } from '../../setups/structureSetup/structureSetup.utils';\r\nimport { dataValueViewportArrange, dataAttributeViewport } from '../../classnames';\r\nimport {\r\n  keys,\r\n  noop,\r\n  each,\r\n  assignDeep,\r\n  strMarginBottom,\r\n  strMarginLeft,\r\n  strMarginRight,\r\n  strPaddingBottom,\r\n  strPaddingLeft,\r\n  strPaddingRight,\r\n  strPaddingTop,\r\n  getStyles,\r\n  setStyles,\r\n  removeAttrClass,\r\n  strWidth,\r\n  strOverflowY,\r\n  strOverflowX,\r\n  strScroll,\r\n} from '../../support';\r\n\r\nexport const scrollbarsHidingPluginName = '__osScrollbarsHidingPlugin';\r\n\r\nexport const ScrollbarsHidingPlugin = /* @__PURE__ */ (() => ({\r\n  [scrollbarsHidingPluginName]: {\r\n    static: () => ({\r\n      _viewportArrangement: (\r\n        structureSetupElements: StructureSetupElementsObj,\r\n        structureSetupState: StructureSetupState,\r\n        observersSetupState: ObserversSetupState,\r\n        env: Env,\r\n        checkOptions: OptionsCheckFn<Options>\r\n      ) => {\r\n        const { _viewportIsTarget, _viewport } = structureSetupElements;\r\n        const { _nativeScrollbarsHiding, _nativeScrollbarsOverlaid, _nativeScrollbarsSize } = env;\r\n        const doViewportArrange =\r\n          !_viewportIsTarget &&\r\n          !_nativeScrollbarsHiding &&\r\n          (_nativeScrollbarsOverlaid.x || _nativeScrollbarsOverlaid.y);\r\n        const [showNativeOverlaidScrollbars] = getShowNativeOverlaidScrollbars(checkOptions, env);\r\n\r\n        /**\r\n         * Gets the current overflow state of the viewport.\r\n         */\r\n        const readViewportOverflowState = (): ViewportOverflowState => {\r\n          const getStatePerAxis = (styleKey: StyleObjectKey) => {\r\n            const overflowStyle = getStyles(_viewport, styleKey) as OverflowStyle;\r\n            const overflowScroll = overflowStyle === strScroll;\r\n\r\n            return [overflowStyle, overflowScroll] as const;\r\n          };\r\n\r\n          const [xOverflowStyle, xOverflowScroll] = getStatePerAxis(strOverflowX);\r\n          const [yOverflowStyle, yOverflowScroll] = getStatePerAxis(strOverflowY);\r\n\r\n          return {\r\n            _overflowStyle: {\r\n              x: xOverflowStyle,\r\n              y: yOverflowStyle,\r\n            },\r\n            _overflowScroll: {\r\n              x: xOverflowScroll,\r\n              y: yOverflowScroll,\r\n            },\r\n          };\r\n        };\r\n\r\n        /**\r\n         * Gets the hide offset matching the passed overflow state.\r\n         * @param viewportOverflowState The overflow state of the viewport\r\n         */\r\n        const _getViewportOverflowHideOffset = (viewportOverflowState: ViewportOverflowState) => {\r\n          const { _overflowScroll } = viewportOverflowState;\r\n          const arrangeHideOffset =\r\n            _nativeScrollbarsHiding || showNativeOverlaidScrollbars ? 0 : 42;\r\n\r\n          const getHideOffsetPerAxis = (\r\n            isOverlaid: boolean,\r\n            overflowScroll: boolean,\r\n            nativeScrollbarSize: number\r\n          ) => {\r\n            const nonScrollbarStylingHideOffset = isOverlaid\r\n              ? arrangeHideOffset\r\n              : nativeScrollbarSize;\r\n            const scrollbarsHideOffset =\r\n              overflowScroll && !_nativeScrollbarsHiding ? nonScrollbarStylingHideOffset : 0;\r\n            const scrollbarsHideOffsetArrange = isOverlaid && !!arrangeHideOffset;\r\n\r\n            return [scrollbarsHideOffset, scrollbarsHideOffsetArrange] as const;\r\n          };\r\n\r\n          const [xScrollbarsHideOffset, xScrollbarsHideOffsetArrange] = getHideOffsetPerAxis(\r\n            _nativeScrollbarsOverlaid.x,\r\n            _overflowScroll.x,\r\n            _nativeScrollbarsSize.x\r\n          );\r\n          const [yScrollbarsHideOffset, yScrollbarsHideOffsetArrange] = getHideOffsetPerAxis(\r\n            _nativeScrollbarsOverlaid.y,\r\n            _overflowScroll.y,\r\n            _nativeScrollbarsSize.y\r\n          );\r\n\r\n          return {\r\n            _scrollbarsHideOffset: {\r\n              x: xScrollbarsHideOffset,\r\n              y: yScrollbarsHideOffset,\r\n            },\r\n            _scrollbarsHideOffsetArrange: {\r\n              x: xScrollbarsHideOffsetArrange,\r\n              y: yScrollbarsHideOffsetArrange,\r\n            },\r\n          };\r\n        };\r\n\r\n        /**\r\n         * Hides the native scrollbars according to the passed parameters.\r\n         * @param viewportOverflowState The viewport overflow state.\r\n         * @param directionIsRTL Whether the direction is RTL or not.\r\n         * @param viewportArrange Whether special styles related to the viewport arrange strategy shall be applied.\r\n         * @param viewportStyleObj The viewport style object to which the needed styles shall be applied.\r\n         */\r\n        const _hideNativeScrollbars = (\r\n          viewportOverflowState: ViewportOverflowState,\r\n          { _directionIsRTL }: ObserversSetupState,\r\n          viewportArrange: boolean\r\n        ): StyleObject | undefined => {\r\n          if (!_viewportIsTarget) {\r\n            const viewportStyleObj: StyleObject = assignDeep(\r\n              {},\r\n              {\r\n                [strMarginRight]: 0,\r\n                [strMarginBottom]: 0,\r\n                [strMarginLeft]: 0,\r\n              }\r\n            );\r\n            const { _scrollbarsHideOffset, _scrollbarsHideOffsetArrange } =\r\n              _getViewportOverflowHideOffset(viewportOverflowState);\r\n            const { x: arrangeX, y: arrangeY } = _scrollbarsHideOffsetArrange;\r\n            const { x: hideOffsetX, y: hideOffsetY } = _scrollbarsHideOffset;\r\n            const { _viewportPaddingStyle } = structureSetupState;\r\n            const horizontalMarginKey: keyof StyleObject = _directionIsRTL\r\n              ? strMarginLeft\r\n              : strMarginRight;\r\n            const viewportHorizontalPaddingKey: keyof StyleObject = _directionIsRTL\r\n              ? strPaddingLeft\r\n              : strPaddingRight;\r\n            const horizontalMarginValue = _viewportPaddingStyle[horizontalMarginKey] as number;\r\n            const verticalMarginValue = _viewportPaddingStyle[strMarginBottom] as number;\r\n            const horizontalPaddingValue = _viewportPaddingStyle[\r\n              viewportHorizontalPaddingKey\r\n            ] as number;\r\n            const verticalPaddingValue = _viewportPaddingStyle[strPaddingBottom] as number;\r\n\r\n            // horizontal\r\n            viewportStyleObj[strWidth] = `calc(100% + ${\r\n              hideOffsetY + horizontalMarginValue * -1\r\n            }px)`;\r\n            viewportStyleObj[horizontalMarginKey] = -hideOffsetY + horizontalMarginValue;\r\n\r\n            // vertical\r\n            viewportStyleObj[strMarginBottom] = -hideOffsetX + verticalMarginValue;\r\n\r\n            // viewport arrange additional styles\r\n            if (viewportArrange) {\r\n              viewportStyleObj[viewportHorizontalPaddingKey] =\r\n                horizontalPaddingValue + (arrangeY ? hideOffsetY : 0);\r\n              viewportStyleObj[strPaddingBottom] =\r\n                verticalPaddingValue + (arrangeX ? hideOffsetX : 0);\r\n            }\r\n\r\n            return viewportStyleObj;\r\n          }\r\n        };\r\n\r\n        /**\r\n         * Sets the styles of the viewport arrange element.\r\n         * @param viewportOverflowState The viewport overflow state according to which the scrollbars shall be hidden.\r\n         * @param viewportScrollSize The content scroll size.\r\n         * @param directionIsRTL Whether the direction is RTL or not.\r\n         * @returns A boolean which indicates whether the viewport arrange element was adjusted.\r\n         */\r\n        const _arrangeViewport = (\r\n          viewportOverflowState: ViewportOverflowState,\r\n          viewportScrollSize: WH<number>,\r\n          sizeFraction: WH<number>\r\n        ) => {\r\n          if (doViewportArrange) {\r\n            const { _viewportPaddingStyle } = structureSetupState;\r\n            const { _scrollbarsHideOffset, _scrollbarsHideOffsetArrange } =\r\n              _getViewportOverflowHideOffset(viewportOverflowState);\r\n            const { x: arrangeX, y: arrangeY } = _scrollbarsHideOffsetArrange;\r\n            const { x: hideOffsetX, y: hideOffsetY } = _scrollbarsHideOffset;\r\n            const { _directionIsRTL } = observersSetupState;\r\n            const viewportArrangeHorizontalPaddingKey: keyof StyleObject = _directionIsRTL\r\n              ? strPaddingRight\r\n              : strPaddingLeft;\r\n            const viewportArrangeHorizontalPaddingValue = _viewportPaddingStyle[\r\n              viewportArrangeHorizontalPaddingKey\r\n            ] as number;\r\n            const viewportArrangeVerticalPaddingValue = _viewportPaddingStyle.paddingTop as number;\r\n            const fractionalContentWidth = viewportScrollSize.w + sizeFraction.w;\r\n            const fractionalContenHeight = viewportScrollSize.h + sizeFraction.h;\r\n            const arrangeSize = {\r\n              w:\r\n                hideOffsetY && arrangeY\r\n                  ? `${\r\n                      hideOffsetY + fractionalContentWidth - viewportArrangeHorizontalPaddingValue\r\n                    }px`\r\n                  : '',\r\n              h:\r\n                hideOffsetX && arrangeX\r\n                  ? `${\r\n                      hideOffsetX + fractionalContenHeight - viewportArrangeVerticalPaddingValue\r\n                    }px`\r\n                  : '',\r\n            };\r\n\r\n            setStyles(_viewport, {\r\n              '--os-vaw': arrangeSize.w,\r\n              '--os-vah': arrangeSize.h,\r\n            });\r\n          }\r\n\r\n          return doViewportArrange;\r\n        };\r\n\r\n        /**\r\n         * Removes all styles applied because of the viewport arrange strategy.\r\n         * @param showNativeOverlaidScrollbars Whether native overlaid scrollbars are shown instead of hidden.\r\n         * @param directionIsRTL Whether the direction is RTL or not.\r\n         * @param viewportOverflowState The currentviewport overflow state or undefined if it has to be determined.\r\n         * @returns A object with a function which applies all the removed styles and the determined viewport vverflow state.\r\n         */\r\n        const _undoViewportArrange = (viewportOverflowState?: ViewportOverflowState) => {\r\n          if (doViewportArrange) {\r\n            const finalViewportOverflowState = viewportOverflowState || readViewportOverflowState();\r\n            const { _viewportPaddingStyle: viewportPaddingStyle } = structureSetupState;\r\n            const { _scrollbarsHideOffsetArrange } = _getViewportOverflowHideOffset(\r\n              finalViewportOverflowState\r\n            );\r\n            const { x: arrangeX, y: arrangeY } = _scrollbarsHideOffsetArrange;\r\n            const finalPaddingStyle: StyleObject = {};\r\n            const assignProps = (props: string[]) =>\r\n              each(props, (prop) => {\r\n                finalPaddingStyle[prop as StyleObjectKey] =\r\n                  viewportPaddingStyle[prop as StyleObjectKey];\r\n              });\r\n\r\n            if (arrangeX) {\r\n              assignProps([strMarginBottom, strPaddingTop, strPaddingBottom]);\r\n            }\r\n\r\n            if (arrangeY) {\r\n              assignProps([strMarginLeft, strMarginRight, strPaddingLeft, strPaddingRight]);\r\n            }\r\n\r\n            const prevStyle = getStyles(_viewport, keys(finalPaddingStyle) as StyleObjectKey[]);\r\n            const addArrange = removeAttrClass(\r\n              _viewport,\r\n              dataAttributeViewport,\r\n              dataValueViewportArrange\r\n            );\r\n\r\n            setStyles(_viewport, finalPaddingStyle);\r\n\r\n            return [\r\n              () => {\r\n                setStyles(\r\n                  _viewport,\r\n                  assignDeep(\r\n                    {},\r\n                    prevStyle,\r\n                    _hideNativeScrollbars(\r\n                      finalViewportOverflowState,\r\n                      observersSetupState,\r\n                      doViewportArrange\r\n                    )\r\n                  )\r\n                );\r\n                addArrange();\r\n              },\r\n              finalViewportOverflowState,\r\n            ] as const;\r\n          }\r\n          return [noop] as const;\r\n        };\r\n\r\n        return {\r\n          _getViewportOverflowHideOffset,\r\n          _arrangeViewport,\r\n          _undoViewportArrange,\r\n          _hideNativeScrollbars,\r\n        };\r\n      },\r\n    }),\r\n  },\r\n}))() satisfies StaticPlugin<typeof scrollbarsHidingPluginName>;\r\n", "import type { StaticPlugin } from '../plugins';\r\nimport { animateNumber, noop, selfClearTimeout } from '../../support';\r\n\r\nexport const clickScrollPluginModuleName = '__osClickScrollPlugin';\r\n\r\nexport const ClickScrollPlugin = /* @__PURE__ */ (() => ({\r\n  [clickScrollPluginModuleName]: {\r\n    static:\r\n      () =>\r\n      (\r\n        moveHandleRelative: (deltaMovement: number) => void,\r\n        targetOffset: number,\r\n        handleLength: number,\r\n        onClickScrollCompleted: (stopped: boolean) => void\r\n      ) => {\r\n        // click scroll animation has 2 main parts:\r\n        // 1. the \"click\" which scrolls 100% of the viewport in a certain amount of time\r\n        // 2. the \"press\" which scrolls to the point where the cursor is located, the \"press\" always waits for the \"click\" to finish\r\n        // The \"click\" should not be canceled by a \"pointerup\" event because very fast clicks or taps would cancel it too fast\r\n        // The \"click\" should only be canceled by a subsequent \"pointerdown\" event because otherwise 2 animations would run\r\n        // The \"press\" should be canceld by the next \"pointerup\" event\r\n\r\n        let stopped = false;\r\n        let stopPressAnimation = noop;\r\n        const linearScrollMs = 133;\r\n        const easedScrollMs = 222;\r\n        const [setPressAnimationTimeout, clearPressAnimationTimeout] =\r\n          selfClearTimeout(linearScrollMs);\r\n        const targetOffsetSign = Math.sign(targetOffset);\r\n        const handleLengthWithTargetSign = handleLength * targetOffsetSign;\r\n        const handleLengthWithTargetSignHalf = handleLengthWithTargetSign / 2;\r\n        const easing = (x: number) => 1 - (1 - x) * (1 - x); // easeOutQuad;\r\n        const easedEndPressAnimation = (from: number, to: number) =>\r\n          animateNumber(from, to, easedScrollMs, moveHandleRelative, easing);\r\n        const linearPressAnimation = (linearFrom: number, msFactor: number) =>\r\n          animateNumber(\r\n            linearFrom,\r\n            targetOffset - handleLengthWithTargetSign,\r\n            linearScrollMs * msFactor,\r\n            (progress, _, completed) => {\r\n              moveHandleRelative(progress);\r\n\r\n              if (completed) {\r\n                stopPressAnimation = easedEndPressAnimation(progress, targetOffset);\r\n              }\r\n            }\r\n          );\r\n        const stopClickAnimation = animateNumber(\r\n          0,\r\n          handleLengthWithTargetSign,\r\n          easedScrollMs,\r\n          (clickAnimationProgress, _, clickAnimationCompleted) => {\r\n            moveHandleRelative(clickAnimationProgress);\r\n\r\n            if (clickAnimationCompleted) {\r\n              onClickScrollCompleted(stopped);\r\n\r\n              if (!stopped) {\r\n                const remainingScrollDistance = targetOffset - clickAnimationProgress;\r\n                const continueWithPress =\r\n                  Math.sign(remainingScrollDistance - handleLengthWithTargetSignHalf) ===\r\n                  targetOffsetSign;\r\n\r\n                continueWithPress &&\r\n                  setPressAnimationTimeout(() => {\r\n                    const remainingLinearScrollDistance =\r\n                      remainingScrollDistance - handleLengthWithTargetSign;\r\n                    const linearBridge =\r\n                      Math.sign(remainingLinearScrollDistance) === targetOffsetSign;\r\n\r\n                    stopPressAnimation = linearBridge\r\n                      ? linearPressAnimation(\r\n                          clickAnimationProgress,\r\n                          Math.abs(remainingLinearScrollDistance) / handleLength\r\n                        )\r\n                      : easedEndPressAnimation(clickAnimationProgress, targetOffset);\r\n                  });\r\n              }\r\n            }\r\n          },\r\n          easing\r\n        );\r\n\r\n        return (stopClick?: boolean) => {\r\n          stopped = true;\r\n\r\n          if (stopClick) {\r\n            stopClickAnimation();\r\n          }\r\n\r\n          clearPressAnimationTimeout();\r\n          stopPressAnimation();\r\n        };\r\n      },\r\n  },\r\n}))() satisfies StaticPlugin<typeof clickScrollPluginModuleName>;\r\n", "import type { DeepPartial, DeepReadonly } from './typings';\r\nimport {\r\n  assignDeep,\r\n  each,\r\n  isObject,\r\n  keys,\r\n  isArray,\r\n  hasOwnProperty,\r\n  isFunction,\r\n  isEmptyObject,\r\n  concat,\r\n} from './support';\r\n\r\nexport type OptionsField = string;\r\n\r\nexport type OptionsPrimitiveValue =\r\n  | boolean\r\n  | number\r\n  | string\r\n  | Array<any>\r\n  | ReadonlyArray<any>\r\n  | [any]\r\n  | [any, ...any[]]\r\n  | ((this: any, ...args: any[]) => any)\r\n  | null;\r\n\r\nexport type OptionsObject = {\r\n  [field: OptionsField]: OptionsPrimitiveValue | OptionsObject;\r\n};\r\n\r\ntype OptionsObjectFieldNameTuples<T> = T extends OptionsPrimitiveValue\r\n  ? []\r\n  : {\r\n      [K in Extract<keyof T, OptionsField>]: [K, ...OptionsObjectFieldNameTuples<T[K]>];\r\n    }[Extract<keyof T, OptionsField>];\r\n\r\ntype JoinOptionsObjectFieldTuples<\r\n  T extends OptionsField[],\r\n  IncompletePath extends boolean = false,\r\n> = T extends [infer F]\r\n  ? F\r\n  : T extends [infer F, ...infer R]\r\n    ? F extends OptionsField\r\n      ?\r\n          | (IncompletePath extends true ? F : never)\r\n          | `${F}.${JoinOptionsObjectFieldTuples<Extract<R, OptionsField[]>>}`\r\n      : never\r\n    : OptionsField;\r\n\r\ntype SplitJoinedOptionsObjectFieldTuples<S extends string> = string extends S\r\n  ? OptionsField[]\r\n  : S extends ''\r\n    ? []\r\n    : S extends `${infer T}.${infer U}`\r\n      ? [T, ...SplitJoinedOptionsObjectFieldTuples<U>]\r\n      : [S];\r\n\r\ntype OptionsObjectFieldTuplesType<O, T extends OptionsField[]> = T extends [infer F]\r\n  ? F extends keyof O\r\n    ? O[F]\r\n    : never\r\n  : T extends [infer F, ...infer R]\r\n    ? F extends keyof O\r\n      ? O[F] extends OptionsPrimitiveValue\r\n        ? O[F]\r\n        : OptionsObjectFieldTuplesType<O[F], Extract<R, OptionsField[]>>\r\n      : never\r\n    : never;\r\n\r\ntype OptionsObjectFieldPath<O extends OptionsObject> = JoinOptionsObjectFieldTuples<\r\n  OptionsObjectFieldNameTuples<O>,\r\n  true\r\n>;\r\n\r\ntype OptionsObjectFieldPathType<\r\n  O extends OptionsObject,\r\n  P extends string,\r\n> = OptionsObjectFieldTuplesType<O, SplitJoinedOptionsObjectFieldTuples<P>>;\r\n\r\nconst opsStringify = (value: any) =>\r\n  JSON.stringify(value, (_, val) => {\r\n    if (isFunction(val)) {\r\n      throw 0;\r\n    }\r\n    return val;\r\n  });\r\n\r\nconst getPropByPath = <T>(obj: any, path: string): T =>\r\n  obj\r\n    ? `${path}`\r\n        .split('.')\r\n        .reduce((o, prop) => (o && hasOwnProperty(o, prop) ? o[prop] : undefined), obj)\r\n    : undefined;\r\n\r\n/**\r\n * The overflow behavior of an axis.\r\n */\r\nexport type OverflowBehavior =\r\n  /** No scrolling is possible and the content is clipped. */\r\n  | 'hidden'\r\n  /** No scrolling is possible and the content isn't clipped. */\r\n  | 'visible'\r\n  /** Scrolling is possible if there is an overflow. */\r\n  | 'scroll'\r\n  /**\r\n   * If the other axis has no overflow the behavior is similar to `visible`.\r\n   * If the other axis has overflow the behavior is similar to `hidden`.\r\n   */\r\n  | 'visible-hidden'\r\n  /**\r\n   * If the other axis has no overflow the behavior is similar to `visible`.\r\n   * If the other axis has overflow the behavior is similar to `scroll`.\r\n   */\r\n  | 'visible-scroll';\r\n\r\n/**\r\n * The scrollbars visibility behavior.\r\n */\r\nexport type ScrollbarsVisibilityBehavior =\r\n  /** The scrollbars are always visible. */\r\n  | 'visible'\r\n  /** The scrollbars are always hidden. */\r\n  | 'hidden'\r\n  /** The scrollbars are only visibile if there is overflow. */\r\n  | 'auto';\r\n\r\n/**\r\n * The scrollbars auto hide behavior\r\n */\r\nexport type ScrollbarsAutoHideBehavior =\r\n  /** The scrollbars are never hidden automatically. */\r\n  | 'never'\r\n  /** The scrollbars are hidden unless the user scrolls. */\r\n  | 'scroll'\r\n  /** The scrollbars are hidden unless the pointer moves in the host element or the user scrolls. */\r\n  | 'move'\r\n  /** The scrollbars are hidden if the pointer leaves the host element or unless the user scrolls. */\r\n  | 'leave';\r\n\r\n/**\r\n * The scrollbar click scroll behavior.\r\n */\r\nexport type ScrollbarsClickScrollBehavior = boolean | 'instant';\r\n\r\n/**\r\n * The options of a OverlayScrollbars instance.\r\n */\r\nexport type Options = {\r\n  /** Whether the padding shall be absolute. */\r\n  paddingAbsolute: boolean;\r\n  /** Whether to show the native scrollbars. Has only an effect it the native scrollbars are overlaid. */\r\n  showNativeOverlaidScrollbars: boolean;\r\n  /** Customizes the automatic update behavior. */\r\n  update: {\r\n    /**\r\n     * The given Event(s) from the elements with the given selector(s) will trigger an update.\r\n     * Useful for everything the MutationObserver and ResizeObserver can't detect\r\n     * e.g.: and Images `load` event or the `transitionend` / `animationend` events.\r\n     */\r\n    elementEvents: Array<[elementSelector: string, eventNames: string]> | null;\r\n    /**\r\n     * The debounce which is used to detect content changes.\r\n     * If a tuple is provided you can customize the `timeout` and the `maxWait` in milliseconds.\r\n     * If a single number customizes only the `timeout`.\r\n     *\r\n     * If the `timeout` is `0`, a debounce still exists. (its executed via `requestAnimationFrame`).\r\n     */\r\n    debounce: [timeout: number, maxWait: number] | number | null;\r\n    /**\r\n     * HTML attributes which will trigger an update if they're changed.\r\n     * Basic attributes like `id`, `class`, `style` etc. are always observed and doesn't have to be added explicitly.\r\n     */\r\n    attributes: string[] | null;\r\n    /**\r\n     * A function which makes it possible to ignore a content mutation or null if nothing shall be ignored.\r\n     * @param mutation The MutationRecord from the MutationObserver.\r\n     * @returns A Truthy value if the mutation shall be ignored, a falsy value otherwise.\r\n     */\r\n    ignoreMutation: ((mutation: MutationRecord) => any) | null;\r\n  };\r\n  /** Customizes the overflow behavior per axis. */\r\n  overflow: {\r\n    /** The overflow behavior of the horizontal (x) axis. */\r\n    x: OverflowBehavior;\r\n    /** The overflow behavior of the vertical (y) axis. */\r\n    y: OverflowBehavior;\r\n  };\r\n  /** Customizes appearance of the scrollbars. */\r\n  scrollbars: {\r\n    /**\r\n     * The scrollbars theme.\r\n     * The theme value will be added as `class` to all `scrollbar` elements of the instance.\r\n     */\r\n    theme: string | null;\r\n    /** The scrollbars visibility behavior. */\r\n    visibility: ScrollbarsVisibilityBehavior;\r\n    /** The scrollbars auto hide behavior. */\r\n    autoHide: ScrollbarsAutoHideBehavior;\r\n    /** The scrollbars auto hide delay in milliseconds. */\r\n    autoHideDelay: number;\r\n    /** Whether the scrollbars auto hide behavior is suspended until a scroll happened. */\r\n    autoHideSuspend: boolean;\r\n    /** Whether it is possible to drag the handle of a scrollbar to scroll the viewport. */\r\n    dragScroll: boolean;\r\n    /** Whether it is possible to click the track of a scrollbar to scroll the viewport. */\r\n    clickScroll: ScrollbarsClickScrollBehavior;\r\n    /**\r\n     * An array of pointer types which shall be supported.\r\n     * Common pointer types are: `mouse`, `pen` and `touch`.\r\n     * https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/pointerType\r\n     */\r\n    pointers: string[] | null;\r\n  };\r\n};\r\n\r\nexport type ReadonlyOptions = DeepReadonly<Options>;\r\n\r\nexport type PartialOptions = DeepPartial<Options>;\r\n\r\nexport type OptionsCheckFn<O extends OptionsObject> = <P extends OptionsObjectFieldPath<O>>(\r\n  path: P\r\n) => [value: OptionsObjectFieldPathType<O, P>, changed: boolean];\r\n\r\nexport const defaultOptions: ReadonlyOptions = {\r\n  paddingAbsolute: false,\r\n  showNativeOverlaidScrollbars: false,\r\n  update: {\r\n    elementEvents: [['img', 'load']],\r\n    debounce: [0, 33],\r\n    attributes: null,\r\n    ignoreMutation: null,\r\n  },\r\n  overflow: {\r\n    x: 'scroll',\r\n    y: 'scroll',\r\n  },\r\n  scrollbars: {\r\n    theme: 'os-theme-dark',\r\n    visibility: 'auto',\r\n    autoHide: 'never',\r\n    autoHideDelay: 1300,\r\n    autoHideSuspend: false,\r\n    dragScroll: true,\r\n    clickScroll: false,\r\n    pointers: ['mouse', 'touch', 'pen'],\r\n  },\r\n} satisfies OptionsObject & Options;\r\n\r\nexport const getOptionsDiff = <T>(currOptions: T, newOptions: DeepPartial<T>): DeepPartial<T> => {\r\n  const diff: DeepPartial<T> = {};\r\n  const optionsKeys = concat(keys(newOptions), keys(currOptions)) as Array<\r\n    keyof T & keyof DeepPartial<T>\r\n  >;\r\n\r\n  each(optionsKeys, (optionKey) => {\r\n    const currOptionValue = currOptions[optionKey];\r\n    const newOptionValue = newOptions[optionKey];\r\n\r\n    if (isObject(currOptionValue) && isObject(newOptionValue)) {\r\n      assignDeep((diff[optionKey] = {} as any), getOptionsDiff(currOptionValue, newOptionValue));\r\n      // delete empty nested objects\r\n      if (isEmptyObject(diff[optionKey])) {\r\n        delete diff[optionKey];\r\n      }\r\n    } else if (hasOwnProperty(newOptions, optionKey) && newOptionValue !== currOptionValue) {\r\n      let isDiff = true;\r\n\r\n      if (isArray(currOptionValue) || isArray(newOptionValue)) {\r\n        try {\r\n          if (opsStringify(currOptionValue) === opsStringify(newOptionValue)) {\r\n            isDiff = false;\r\n          }\r\n        } catch {}\r\n      }\r\n\r\n      if (isDiff) {\r\n        // @ts-ignore\r\n        diff[optionKey] = newOptionValue;\r\n      }\r\n    }\r\n  });\r\n\r\n  return diff;\r\n};\r\n\r\nexport const createOptionCheck =\r\n  <T extends OptionsObject>(\r\n    options: T,\r\n    changedOptions: DeepPartial<T>,\r\n    force?: boolean\r\n  ): OptionsCheckFn<T> =>\r\n  (path) => [\r\n    getPropByPath(options, path),\r\n    force || getPropByPath(changedOptions, path) !== undefined,\r\n  ];\r\n", "let nonce: string | undefined;\r\n\r\nexport const getNonce = () => nonce;\r\nexport const setNonce = (newNonce: string | undefined) => {\r\n  nonce = newNonce;\r\n};\r\n", "import type { XY, EventListener } from './support';\r\nimport type { Options, PartialOptions } from './options';\r\nimport type { Initialization, PartialInitialization } from './initialization';\r\nimport type { StyleObjectKey } from './typings';\r\nimport { defaultOptions } from './options';\r\nimport { classNameEnvironment, classNameEnvironmentScrollbarHidden } from './classnames';\r\nimport {\r\n  createDOM,\r\n  addClass,\r\n  appendChildren,\r\n  getFractionalSize,\r\n  getClientSize,\r\n  getOffsetSize,\r\n  removeAttrs,\r\n  removeElements,\r\n  assignDeep,\r\n  createCache,\r\n  equalXY,\r\n  createEventListenerHub,\r\n  scrollT,\r\n  bind,\r\n  wnd,\r\n  getStyles,\r\n  isBodyElement,\r\n  isFunction,\r\n  addEventListener,\r\n} from './support';\r\nimport { getNonce } from './nonce';\r\n\r\ntype EnvironmentEventArgs = {\r\n  r: [scrollbarSizeChanged?: boolean];\r\n};\r\n\r\nexport interface Env {\r\n  readonly _nativeScrollbarsSize: XY;\r\n  readonly _nativeScrollbarsOverlaid: XY<boolean>;\r\n  readonly _nativeScrollbarsHiding: boolean;\r\n  readonly _scrollTimeline: boolean;\r\n  readonly _staticDefaultInitialization: Initialization;\r\n  readonly _staticDefaultOptions: Options;\r\n  _addResizeListener(listener: EventListener<EnvironmentEventArgs, 'r'>): () => void;\r\n  _getDefaultInitialization(): Initialization;\r\n  _setDefaultInitialization(newInitialization: PartialInitialization): Initialization;\r\n  _getDefaultOptions(): Options;\r\n  _setDefaultOptions(newDefaultOptions: PartialOptions): Options;\r\n}\r\n\r\nlet environmentInstance: Env;\r\n\r\nconst createEnvironment = (): Env => {\r\n  const getNativeScrollbarSize = (\r\n    measureElm: HTMLElement,\r\n    measureElmChild: HTMLElement,\r\n    clear?: boolean\r\n  ): XY => {\r\n    // fix weird safari issue where getComputedStyle returns all empty styles by appending twice\r\n    appendChildren(document.body, measureElm);\r\n    appendChildren(document.body, measureElm);\r\n\r\n    const cSize = getClientSize(measureElm);\r\n    const oSize = getOffsetSize(measureElm);\r\n    const fSize = getFractionalSize(measureElmChild);\r\n\r\n    clear && removeElements(measureElm);\r\n\r\n    return {\r\n      x: oSize.h - cSize.h + fSize.h,\r\n      y: oSize.w - cSize.w + fSize.w,\r\n    };\r\n  };\r\n\r\n  const getNativeScrollbarsHiding = (testElm: HTMLElement): boolean => {\r\n    let result = false;\r\n    const revertClass = addClass(testElm, classNameEnvironmentScrollbarHidden);\r\n    try {\r\n      result =\r\n        getStyles(testElm, 'scrollbar-width' as StyleObjectKey) === 'none' ||\r\n        getStyles(testElm, 'display', '::-webkit-scrollbar') === 'none';\r\n    } catch {}\r\n    revertClass();\r\n    return result;\r\n  };\r\n\r\n  // changes to this styles need to be reflected in the \"hide native scrollbars\" section of the structure styles\r\n  const envStyle = `.${classNameEnvironment}{scroll-behavior:auto!important;position:fixed;opacity:0;visibility:hidden;overflow:scroll;height:200px;width:200px;z-index:-1}.${classNameEnvironment} div{width:200%;height:200%;margin:10px 0}.${classNameEnvironmentScrollbarHidden}{scrollbar-width:none!important}.${classNameEnvironmentScrollbarHidden}::-webkit-scrollbar,.${classNameEnvironmentScrollbarHidden}::-webkit-scrollbar-corner{appearance:none!important;display:none!important;width:0!important;height:0!important}`;\r\n  const envDOM = createDOM(\r\n    `<div class=\"${classNameEnvironment}\"><div></div><style>${envStyle}</style></div>`\r\n  );\r\n  const envElm = envDOM[0] as HTMLElement;\r\n  const envChildElm = envElm.firstChild as HTMLElement;\r\n  const styleElm = envElm.lastChild as HTMLStyleElement;\r\n  const nonce = getNonce();\r\n\r\n  if (nonce) {\r\n    styleElm.nonce = nonce;\r\n  }\r\n\r\n  const [addEvent, , triggerEvent] = createEventListenerHub<EnvironmentEventArgs>();\r\n  const [updateNativeScrollbarSizeCache, getNativeScrollbarSizeCache] = createCache(\r\n    {\r\n      _initialValue: getNativeScrollbarSize(envElm, envChildElm),\r\n      _equal: equalXY,\r\n    },\r\n    bind(getNativeScrollbarSize, envElm, envChildElm, true)\r\n  );\r\n  const [nativeScrollbarsSize] = getNativeScrollbarSizeCache();\r\n  const nativeScrollbarsHiding = getNativeScrollbarsHiding(envElm);\r\n  const nativeScrollbarsOverlaid = {\r\n    x: nativeScrollbarsSize.x === 0,\r\n    y: nativeScrollbarsSize.y === 0,\r\n  };\r\n  const staticDefaultInitialization: Initialization = {\r\n    elements: {\r\n      host: null,\r\n      padding: !nativeScrollbarsHiding,\r\n      viewport: (target) => nativeScrollbarsHiding && isBodyElement(target) && target,\r\n      content: false,\r\n    },\r\n    scrollbars: {\r\n      slot: true,\r\n    },\r\n    cancel: {\r\n      nativeScrollbarsOverlaid: false,\r\n      body: null,\r\n    },\r\n  };\r\n  const staticDefaultOptions = assignDeep({}, defaultOptions);\r\n  const getDefaultOptions = bind(\r\n    assignDeep as typeof assignDeep<Options, Options>,\r\n    {} as Options,\r\n    staticDefaultOptions\r\n  );\r\n  const getDefaultInitialization = bind(\r\n    assignDeep as typeof assignDeep<Initialization, Initialization>,\r\n    {} as Initialization,\r\n    staticDefaultInitialization\r\n  );\r\n\r\n  const env: Env = {\r\n    _nativeScrollbarsSize: nativeScrollbarsSize,\r\n    _nativeScrollbarsOverlaid: nativeScrollbarsOverlaid,\r\n    _nativeScrollbarsHiding: nativeScrollbarsHiding,\r\n    _scrollTimeline: !!scrollT,\r\n    _addResizeListener: bind(addEvent, 'r'),\r\n    _getDefaultInitialization: getDefaultInitialization,\r\n    _setDefaultInitialization: (newInitializationStrategy) =>\r\n      assignDeep(staticDefaultInitialization, newInitializationStrategy) &&\r\n      getDefaultInitialization(),\r\n    _getDefaultOptions: getDefaultOptions,\r\n    _setDefaultOptions: (newDefaultOptions) =>\r\n      assignDeep(staticDefaultOptions, newDefaultOptions) && getDefaultOptions(),\r\n    _staticDefaultInitialization: assignDeep({}, staticDefaultInitialization),\r\n    _staticDefaultOptions: assignDeep({}, staticDefaultOptions),\r\n  };\r\n\r\n  removeAttrs(envElm, 'style');\r\n  removeElements(envElm);\r\n\r\n  // needed in case content has css viewport units\r\n  addEventListener(wnd, 'resize', () => {\r\n    triggerEvent('r', []);\r\n  });\r\n\r\n  if (\r\n    isFunction(wnd.matchMedia) &&\r\n    !nativeScrollbarsHiding &&\r\n    (!nativeScrollbarsOverlaid.x || !nativeScrollbarsOverlaid.y)\r\n  ) {\r\n    const addZoomListener = (onZoom: () => void) => {\r\n      const media = wnd.matchMedia(`(resolution: ${wnd.devicePixelRatio}dppx)`);\r\n      addEventListener(\r\n        media,\r\n        'change',\r\n        () => {\r\n          onZoom();\r\n          addZoomListener(onZoom);\r\n        },\r\n        {\r\n          _once: true,\r\n        }\r\n      );\r\n    };\r\n    addZoomListener(() => {\r\n      const [updatedNativeScrollbarSize, nativeScrollbarSizeChanged] =\r\n        updateNativeScrollbarSizeCache();\r\n\r\n      assignDeep(env._nativeScrollbarsSize, updatedNativeScrollbarSize); // keep the object and just re-assign!\r\n      triggerEvent('r', [nativeScrollbarSizeChanged]);\r\n    });\r\n  }\r\n\r\n  return env;\r\n};\r\n\r\nexport const getEnvironment = (): Env => {\r\n  if (!environmentInstance) {\r\n    environmentInstance = createEnvironment();\r\n  }\r\n  return environmentInstance;\r\n};\r\n", "import {\r\n  each,\r\n  noop,\r\n  debounce,\r\n  MutationObserverConstructor,\r\n  addEventListener,\r\n  is,\r\n  find,\r\n  push,\r\n  runEachAndClear,\r\n  bind,\r\n  isEmptyArray,\r\n  deduplicateArray,\r\n  inArray,\r\n  concat,\r\n  getAttr,\r\n  isString,\r\n} from '../support';\r\n\r\ntype DOMContentObserverCallback = (contentChangedThroughEvent: boolean) => any;\r\n\r\ntype DOMTargetObserverCallback = (targetChangedAttrs: string[], targetStyleChanged: boolean) => any;\r\n\r\ninterface DOMObserverOptionsBase {\r\n  _attributes?: string[];\r\n  /**\r\n   * A function which can ignore a changed attribute if it returns true.\r\n   * for DOMTargetObserver this applies to the changes to the observed target\r\n   * for DOMContentObserver this applies to changes to nested targets -> nested targets are elements which match the \"_nestedTargetSelector\" selector\r\n   */\r\n  _ignoreTargetChange?: DOMObserverIgnoreTargetChange;\r\n}\r\n\r\ninterface DOMContentObserverOptions extends DOMObserverOptionsBase {\r\n  _eventContentChange?: DOMObserverEventContentChange; // [selector, eventname(s) | function returning eventname(s)] -> eventnames divided by whitespaces\r\n  _nestedTargetSelector?: string;\r\n  _ignoreContentChange?: DOMObserverIgnoreContentChange; // function which will prevent marking certain dom changes as content change if it returns true\r\n}\r\n\r\ninterface DOMTargetObserverOptions extends DOMObserverOptionsBase {\r\n  /**\r\n   * Marks certain attributes as style changing, should be a subset of the _attributes prop.\r\n   * Used to set the \"targetStyleChanged\" param in the DOMTargetObserverCallback.\r\n   */\r\n  _styleChangingAttributes?: string[] | readonly string[];\r\n}\r\n\r\ntype ContentChangeArrayItem = [selector?: string, eventNames?: string] | null | undefined;\r\n\r\nexport type DOMObserverEventContentChange =\r\n  | Array<ContentChangeArrayItem>\r\n  | false\r\n  | null\r\n  | undefined;\r\n\r\nexport type DOMObserverIgnoreContentChange = (\r\n  mutation: MutationRecord,\r\n  isNestedTarget: boolean,\r\n  domObserverTarget: HTMLElement,\r\n  domObserverOptions?: DOMContentObserverOptions\r\n) => boolean;\r\n\r\nexport type DOMObserverIgnoreTargetChange = (\r\n  target: Node,\r\n  attributeName: string,\r\n  oldAttributeValue: string | null,\r\n  newAttributeValue: string | null\r\n) => boolean;\r\n\r\nexport type DOMObserverCallback<ContentObserver extends boolean> = ContentObserver extends true\r\n  ? DOMContentObserverCallback\r\n  : DOMTargetObserverCallback;\r\n\r\nexport type DOMObserverOptions<ContentObserver extends boolean> = ContentObserver extends true\r\n  ? DOMContentObserverOptions\r\n  : DOMTargetObserverOptions;\r\n\r\nexport type DOMObserver<ContentObserver extends boolean> = [\r\n  construct: () => () => void,\r\n  update: () => void | false | Parameters<DOMObserverCallback<ContentObserver>>,\r\n];\r\n\r\ntype EventContentChangeUpdateElement = (\r\n  getElements?: (selector: string) => Node[],\r\n  removed?: boolean\r\n) => void;\r\ntype EventContentChange = [destroy: () => void, updateElements: EventContentChangeUpdateElement];\r\n\r\n/**\r\n * Creates a set of helper functions to observe events of elements inside the target element.\r\n * @param target The target element of which the children elements shall be observed. (not only direct children but also nested ones)\r\n * @param eventContentChange The event content change array. (array of tuples: selector and eventname(s))\r\n * @param callback Callback which is called if one of the elements emits the corresponding event.\r\n * @returns A object which contains a set of helper functions to destroy and update the observation of elements.\r\n */\r\nconst createEventContentChange = (\r\n  target: HTMLElement,\r\n  callback: (...args: any) => any,\r\n  eventContentChange?: DOMObserverEventContentChange\r\n): EventContentChange => {\r\n  let destroyed = false;\r\n  const map = eventContentChange ? new WeakMap<Node, (() => any)[]>() : false; // weak map to prevent memory leak for detached elements\r\n  const destroy = () => {\r\n    destroyed = true;\r\n  };\r\n  const updateElements: EventContentChangeUpdateElement = (getElements) => {\r\n    if (map && eventContentChange) {\r\n      const eventElmList = eventContentChange.map((item) => {\r\n        const [selector, eventNames] = item || [];\r\n        const elements = eventNames && selector ? (getElements || find)(selector, target) : [];\r\n        return [elements, eventNames] as const;\r\n      });\r\n\r\n      each(eventElmList, (item) =>\r\n        each(item[0], (elm) => {\r\n          const eventNames = item[1];\r\n          const entries = map.get(elm) || [];\r\n          const isTargetChild = target.contains(elm);\r\n\r\n          if (isTargetChild && eventNames) {\r\n            const removeListener = addEventListener(elm, eventNames, (event: Event) => {\r\n              if (destroyed) {\r\n                removeListener();\r\n                map.delete(elm);\r\n              } else {\r\n                callback(event);\r\n              }\r\n            });\r\n            map.set(elm, push(entries, removeListener));\r\n          } else {\r\n            runEachAndClear(entries);\r\n            map.delete(elm);\r\n          }\r\n        })\r\n      );\r\n    }\r\n  };\r\n\r\n  updateElements();\r\n\r\n  return [destroy, updateElements];\r\n};\r\n\r\n/**\r\n * Creates a DOM observer which observes DOM changes to either the target element or its children.\r\n * @param target The element which shall be observed.\r\n * @param isContentObserver Whether this observer is just observing the target or just the targets children. (not only direct children but also nested ones)\r\n * @param callback The callback which gets called if a change was detected.\r\n * @param options The options for DOM change detection.\r\n * @returns A object which represents the instance of the DOM observer.\r\n */\r\nexport const createDOMObserver = <ContentObserver extends boolean>(\r\n  target: HTMLElement,\r\n  isContentObserver: ContentObserver,\r\n  callback: DOMObserverCallback<ContentObserver>,\r\n  options?: DOMObserverOptions<ContentObserver>\r\n): DOMObserver<ContentObserver> => {\r\n  let isConnected = false;\r\n  const {\r\n    _attributes,\r\n    _styleChangingAttributes,\r\n    _eventContentChange,\r\n    _nestedTargetSelector,\r\n    _ignoreTargetChange,\r\n    _ignoreContentChange,\r\n  } = (options as DOMContentObserverOptions & DOMTargetObserverOptions) || {};\r\n  const debouncedEventContentChange = debounce(\r\n    () => isConnected && (callback as DOMContentObserverCallback)(true),\r\n    { _timeout: 33, _maxDelay: 99 }\r\n  );\r\n  const [destroyEventContentChange, updateEventContentChangeElements] = createEventContentChange(\r\n    target,\r\n    debouncedEventContentChange,\r\n    _eventContentChange\r\n  );\r\n\r\n  // MutationObserver\r\n  const finalAttributes = _attributes || [];\r\n  const finalStyleChangingAttributes = _styleChangingAttributes || [];\r\n  const observedAttributes = concat(finalAttributes, finalStyleChangingAttributes);\r\n  const observerCallback = (\r\n    fromRecords: boolean,\r\n    mutations: MutationRecord[]\r\n  ): void | Parameters<DOMObserverCallback<ContentObserver>> => {\r\n    if (!isEmptyArray(mutations)) {\r\n      const ignoreTargetChange = _ignoreTargetChange || noop;\r\n      const ignoreContentChange = _ignoreContentChange || noop;\r\n      const totalChangedNodes: Node[] = [];\r\n      const targetChangedAttrs: string[] = [];\r\n      let targetStyleChanged: boolean | '' | null | undefined = false;\r\n      let contentChanged: boolean | '' | null | undefined = false;\r\n      let childListChanged: boolean | '' | null | undefined = false;\r\n\r\n      each(mutations, (mutation) => {\r\n        const {\r\n          attributeName,\r\n          target: mutationTarget,\r\n          type,\r\n          oldValue,\r\n          addedNodes,\r\n          removedNodes,\r\n        } = mutation;\r\n        const isAttributesType = type === 'attributes';\r\n        const isChildListType = type === 'childList';\r\n        const targetIsMutationTarget = target === mutationTarget;\r\n        const isAttrChange = isAttributesType && attributeName;\r\n        const newValue =\r\n          isAttrChange && getAttr(mutationTarget as HTMLElement, attributeName || '');\r\n        // narrow down attributeValue type to `string` or `null` but don't overwrite `<empty string>` with `null`\r\n        const attributeValue = isString(newValue) ? newValue : null;\r\n        const attributeChanged = isAttrChange && oldValue !== attributeValue;\r\n        const styleChangingAttrChanged =\r\n          inArray(finalStyleChangingAttributes, attributeName) && attributeChanged;\r\n\r\n        // if is content observer and something changed in children\r\n        if (isContentObserver && (isChildListType || !targetIsMutationTarget)) {\r\n          const contentAttrChanged = isAttributesType && attributeChanged;\r\n          const isNestedTarget =\r\n            contentAttrChanged &&\r\n            _nestedTargetSelector &&\r\n            is(mutationTarget, _nestedTargetSelector);\r\n          const baseAssertion = isNestedTarget\r\n            ? !ignoreTargetChange(mutationTarget, attributeName, oldValue, attributeValue)\r\n            : !isAttributesType || contentAttrChanged;\r\n          const contentFinalChanged =\r\n            baseAssertion && !ignoreContentChange(mutation, !!isNestedTarget, target, options);\r\n\r\n          each(addedNodes, (node) => push(totalChangedNodes, node));\r\n          each(removedNodes, (node) => push(totalChangedNodes, node));\r\n\r\n          contentChanged = contentChanged || contentFinalChanged;\r\n          childListChanged = childListChanged || isChildListType;\r\n        }\r\n        // if is target observer and target attr changed\r\n        if (\r\n          !isContentObserver &&\r\n          targetIsMutationTarget &&\r\n          attributeChanged &&\r\n          !ignoreTargetChange(mutationTarget, attributeName!, oldValue, attributeValue)\r\n        ) {\r\n          push(targetChangedAttrs, attributeName);\r\n          targetStyleChanged = targetStyleChanged || styleChangingAttrChanged;\r\n        }\r\n      });\r\n\r\n      // adds / removes the new elements from the event content change\r\n      updateEventContentChangeElements((selector: string) =>\r\n        deduplicateArray(totalChangedNodes).reduce<Node[]>((arr, node) => {\r\n          push(arr, find(selector, node));\r\n          return is(node, selector) ? push(arr, node) : arr;\r\n        }, [])\r\n      );\r\n\r\n      if (isContentObserver) {\r\n        !fromRecords && contentChanged && (callback as DOMContentObserverCallback)(false);\r\n        return [false] satisfies Parameters<DOMObserverCallback<true>> as Parameters<\r\n          DOMObserverCallback<ContentObserver>\r\n        >;\r\n      }\r\n\r\n      if (!isEmptyArray(targetChangedAttrs) || targetStyleChanged) {\r\n        const args = [\r\n          deduplicateArray(targetChangedAttrs),\r\n          targetStyleChanged,\r\n        ] satisfies Parameters<DOMTargetObserverCallback> & Parameters<DOMObserverCallback<false>>;\r\n        !fromRecords && (callback as DOMTargetObserverCallback).apply(0, args);\r\n\r\n        return args as Parameters<DOMObserverCallback<ContentObserver>>;\r\n      }\r\n    }\r\n  };\r\n  const mutationObserver: MutationObserver = new MutationObserverConstructor!(\r\n    bind(observerCallback, false)\r\n  );\r\n\r\n  return [\r\n    () => {\r\n      mutationObserver.observe(target, {\r\n        attributes: true,\r\n        attributeOldValue: true,\r\n        attributeFilter: observedAttributes,\r\n        subtree: isContentObserver,\r\n        childList: isContentObserver,\r\n        characterData: isContentObserver,\r\n      });\r\n      isConnected = true;\r\n\r\n      return () => {\r\n        if (isConnected) {\r\n          destroyEventContentChange();\r\n          mutationObserver.disconnect();\r\n          isConnected = false;\r\n        }\r\n      };\r\n    },\r\n    () => {\r\n      if (isConnected) {\r\n        debouncedEventContentChange._flush();\r\n        return observerCallback(true, mutationObserver.takeRecords());\r\n      }\r\n    },\r\n  ];\r\n};\r\n", "import type { SizeObserverPlugin } from '../plugins';\r\nimport {\r\n  createCache,\r\n  createDOM,\r\n  runEachAndClear,\r\n  addEventListener,\r\n  addClass,\r\n  push,\r\n  ResizeObserverConstructor,\r\n  appendChildren,\r\n  domRectHasDimensions,\r\n  bind,\r\n  noop,\r\n  domRectAppeared,\r\n  concat,\r\n} from '../support';\r\nimport {\r\n  classNameSizeObserver,\r\n  classNameSizeObserverAppear,\r\n  classNameSizeObserverListener,\r\n} from '../classnames';\r\nimport { getStaticPluginModuleInstance, sizeObserverPluginName } from '../plugins';\r\n\r\nexport interface SizeObserverOptions {\r\n  /** Whether appearing should be observed. */\r\n  _appear?: boolean;\r\n}\r\n\r\nexport interface SizeObserverCallbackParams {\r\n  _sizeChanged: boolean;\r\n  _appear?: boolean;\r\n}\r\n\r\nexport type SizeObserver = () => () => void;\r\n\r\n/**\r\n * Creates a size observer which observes any size, padding, border, margin and box-sizing changes of the target element. Depending on the options also direction and appear can be observed.\r\n * @param target The target element which shall be observed.\r\n * @param onSizeChangedCallback The callback which gets called after a size change was detected.\r\n * @param options The options for size detection, whether to observe also direction and appear.\r\n * @returns A object which represents the instance of the size observer.\r\n */\r\nexport const createSizeObserver = (\r\n  target: HTMLElement,\r\n  onSizeChangedCallback: (params: SizeObserverCallbackParams) => any,\r\n  options?: SizeObserverOptions\r\n): SizeObserver => {\r\n  const { _appear: observeAppearChange } = options || {};\r\n  const sizeObserverPlugin =\r\n    getStaticPluginModuleInstance<typeof SizeObserverPlugin>(sizeObserverPluginName);\r\n  const [updateResizeObserverContentRectCache] = createCache<DOMRectReadOnly | false>({\r\n    _initialValue: false,\r\n    _alwaysUpdateValues: true,\r\n  });\r\n\r\n  return () => {\r\n    const destroyFns: (() => void)[] = [];\r\n    const baseElements = createDOM(\r\n      `<div class=\"${classNameSizeObserver}\"><div class=\"${classNameSizeObserverListener}\"></div></div>`\r\n    );\r\n    const sizeObserver = baseElements[0] as HTMLElement;\r\n    const listenerElement = sizeObserver.firstChild as HTMLElement;\r\n    const onSizeChangedCallbackProxy = (sizeChangedContext?: ResizeObserverEntry | boolean) => {\r\n      const isResizeObserverCall = sizeChangedContext instanceof ResizeObserverEntry;\r\n\r\n      let skip = false;\r\n      let appear = false;\r\n\r\n      // if triggered from RO.\r\n      if (isResizeObserverCall) {\r\n        const [currRContentRect, , prevContentRect] = updateResizeObserverContentRectCache(\r\n          sizeChangedContext.contentRect\r\n        );\r\n        const hasDimensions = domRectHasDimensions(currRContentRect);\r\n        appear = domRectAppeared(currRContentRect, prevContentRect);\r\n        skip = !appear && !hasDimensions; // skip if display is none or when window resize\r\n      }\r\n      // else if it triggered with appear from polyfill\r\n      else {\r\n        appear = sizeChangedContext === true;\r\n      }\r\n\r\n      if (!skip) {\r\n        onSizeChangedCallback({\r\n          _sizeChanged: true,\r\n          _appear: appear,\r\n        });\r\n      }\r\n    };\r\n\r\n    if (ResizeObserverConstructor) {\r\n      const resizeObserverInstance = new ResizeObserverConstructor((entries) =>\r\n        onSizeChangedCallbackProxy(entries.pop())\r\n      );\r\n      resizeObserverInstance.observe(listenerElement);\r\n      push(destroyFns, () => {\r\n        resizeObserverInstance.disconnect();\r\n      });\r\n    } else if (sizeObserverPlugin) {\r\n      const [pluginAppearCallback, pluginDestroyFns] = sizeObserverPlugin(\r\n        listenerElement,\r\n        onSizeChangedCallbackProxy,\r\n        observeAppearChange\r\n      );\r\n      push(\r\n        destroyFns,\r\n        concat(\r\n          [\r\n            addClass(sizeObserver, classNameSizeObserverAppear),\r\n            addEventListener(sizeObserver, 'animationstart', pluginAppearCallback),\r\n          ],\r\n          pluginDestroyFns\r\n        )\r\n      );\r\n    } else {\r\n      return noop;\r\n    }\r\n\r\n    return bind(runEachAndClear, push(destroyFns, appendChildren(target, sizeObserver)));\r\n  };\r\n};\r\n", "import type { WH, CacheValues } from '../support';\r\nimport { createSizeObserver } from './sizeObserver';\r\nimport { classNameTrinsicObserver } from '../classnames';\r\nimport {\r\n  createDiv,\r\n  getOffsetSize,\r\n  runEachAndClear,\r\n  createCache,\r\n  push,\r\n  IntersectionObserverConstructor,\r\n  appendChildren,\r\n  bind,\r\n} from '../support';\r\n\r\nexport type TrinsicObserverCallback = (heightIntrinsic: CacheValues<boolean>) => any;\r\nexport type TrinsicObserver = [\r\n  construct: () => () => void,\r\n  update: () => void | false | null | undefined | Parameters<TrinsicObserverCallback>,\r\n];\r\n\r\n/**\r\n * Creates a trinsic observer which observes changes to intrinsic or extrinsic sizing for the height of the target element.\r\n * @param target The element which shall be observed.\r\n * @param onTrinsicChangedCallback The callback which gets called after a change was detected.\r\n * @returns A object which represents the instance of the trinsic observer.\r\n */\r\nexport const createTrinsicObserver = (\r\n  target: HTMLElement,\r\n  onTrinsicChangedCallback: TrinsicObserverCallback\r\n): TrinsicObserver => {\r\n  let intersectionObserverInstance: undefined | IntersectionObserver;\r\n  const isHeightIntrinsic = (ioEntryOrSize: IntersectionObserverEntry | WH<number>): boolean =>\r\n    (ioEntryOrSize as WH<number>).h === 0 ||\r\n    (ioEntryOrSize as IntersectionObserverEntry).isIntersecting ||\r\n    (ioEntryOrSize as IntersectionObserverEntry).intersectionRatio > 0;\r\n  const trinsicObserver = createDiv(classNameTrinsicObserver);\r\n  const [updateHeightIntrinsicCache] = createCache({\r\n    _initialValue: false,\r\n  });\r\n  const triggerOnTrinsicChangedCallback = (\r\n    updateValue: IntersectionObserverEntry | WH<number> | undefined,\r\n    fromRecords?: boolean\r\n  ): void | Parameters<TrinsicObserverCallback> => {\r\n    if (updateValue) {\r\n      const heightIntrinsic = updateHeightIntrinsicCache(isHeightIntrinsic(updateValue));\r\n      const [, heightIntrinsicChanged] = heightIntrinsic;\r\n      return (\r\n        heightIntrinsicChanged &&\r\n        !fromRecords &&\r\n        onTrinsicChangedCallback(heightIntrinsic) && [heightIntrinsic]\r\n      );\r\n    }\r\n  };\r\n  const intersectionObserverCallback = (\r\n    fromRecords: boolean,\r\n    entries: IntersectionObserverEntry[]\r\n  ) => triggerOnTrinsicChangedCallback(entries.pop(), fromRecords);\r\n\r\n  return [\r\n    () => {\r\n      const destroyFns: (() => void)[] = [];\r\n\r\n      if (IntersectionObserverConstructor) {\r\n        intersectionObserverInstance = new IntersectionObserverConstructor(\r\n          bind(intersectionObserverCallback, false),\r\n          { root: target }\r\n        );\r\n        intersectionObserverInstance.observe(trinsicObserver);\r\n        push(destroyFns, () => {\r\n          intersectionObserverInstance!.disconnect();\r\n        });\r\n      } else {\r\n        const onSizeChanged = () => {\r\n          const newSize = getOffsetSize(trinsicObserver);\r\n          triggerOnTrinsicChangedCallback(newSize);\r\n        };\r\n        push(destroyFns, createSizeObserver(trinsicObserver, onSizeChanged)());\r\n        onSizeChanged();\r\n      }\r\n\r\n      return bind(runEachAndClear, push(destroyFns, appendChildren(target, trinsicObserver)));\r\n    },\r\n    () =>\r\n      intersectionObserverInstance &&\r\n      intersectionObserverCallback(true, intersectionObserverInstance.takeRecords()),\r\n  ];\r\n};\r\n", "import type { Options, OptionsCheckFn } from '../../options';\r\nimport type { ScrollbarsHidingPlugin } from '../../plugins';\r\nimport type { SizeObserverCallbackParams } from '../../observers';\r\nimport type { StructureSetupElementsObj } from '../structureSetup/structureSetup.elements';\r\nimport type { Setup, SetupUpdateInfo, StructureSetupState } from '../../setups';\r\nimport type { CacheValues, WH } from '../../support';\r\nimport type { PlainObject } from '../../typings';\r\nimport { getStaticPluginModuleInstance, scrollbarsHidingPluginName } from '../../plugins';\r\nimport {\r\n  classNameScrollbar,\r\n  dataAttributeHost,\r\n  dataAttributeViewport,\r\n  dataValueViewportMeasuring,\r\n  dataValueViewportArrange,\r\n  dataValueNoClipping,\r\n} from '../../classnames';\r\nimport { getEnvironment } from '../../environment';\r\nimport { createDOMObserver, createSizeObserver, createTrinsicObserver } from '../../observers';\r\nimport {\r\n  ResizeObserverConstructor,\r\n  assignDeep,\r\n  closest,\r\n  createCache,\r\n  debounce,\r\n  equalWH,\r\n  getFractionalSize,\r\n  isArray,\r\n  isFunction,\r\n  isNumber,\r\n  keys,\r\n  liesBetween,\r\n  getScrollSize,\r\n  getElementScroll,\r\n  scrollElementTo,\r\n  domRectAppeared,\r\n  concat,\r\n  getStyles,\r\n  hasAttrClass,\r\n} from '../../support';\r\n\r\nexport interface ObserversSetupState {\r\n  _heightIntrinsic: boolean;\r\n  _directionIsRTL: boolean;\r\n}\r\n\r\nexport interface ObserversSetupUpdateInfo extends SetupUpdateInfo {\r\n  _takeRecords?: boolean;\r\n}\r\n\r\nexport type ObserversSetupUpdateHints = {\r\n  _sizeChanged?: boolean;\r\n  _directionChanged?: boolean;\r\n  _heightIntrinsicChanged?: boolean;\r\n  _hostMutation?: boolean;\r\n  _contentMutation?: boolean;\r\n  _appear?: boolean;\r\n  _scrollbarSizeChanged?: boolean;\r\n};\r\n\r\nexport type ObserversSetup = Setup<\r\n  ObserversSetupUpdateInfo,\r\n  ObserversSetupState,\r\n  ObserversSetupUpdateHints\r\n>;\r\n\r\nexport const createObserversSetup = (\r\n  structureSetupElements: StructureSetupElementsObj,\r\n  structureSetupState: StructureSetupState,\r\n  getCurrentOption: OptionsCheckFn<Options>,\r\n  onObserversUpdated: (updateHints: ObserversSetupUpdateHints) => void\r\n): ObserversSetup => {\r\n  let debounceTimeout: number | false | undefined;\r\n  let debounceMaxDelay: number | false | undefined;\r\n  let updateContentMutationObserver: (() => void) | undefined;\r\n  let destroyContentMutationObserver: (() => void) | undefined;\r\n  let prevContentRect: DOMRectReadOnly | undefined;\r\n  let prevDirectionIsRTL: boolean | undefined;\r\n  const hostSelector = `[${dataAttributeHost}]`;\r\n\r\n  // TODO: observer textarea attrs if textarea\r\n\r\n  const viewportSelector = `[${dataAttributeViewport}]`;\r\n  const baseStyleChangingAttrs = ['id', 'class', 'style', 'open', 'wrap', 'cols', 'rows'];\r\n  const {\r\n    _target,\r\n    _host,\r\n    _viewport,\r\n    _scrollOffsetElement,\r\n    _content,\r\n    _viewportIsTarget,\r\n    _isBody,\r\n    _viewportHasClass,\r\n    _viewportAddRemoveClass,\r\n    _removeScrollObscuringStyles,\r\n  } = structureSetupElements;\r\n\r\n  const getDirectionIsRTL = (elm: HTMLElement): boolean => getStyles(elm, 'direction') === 'rtl';\r\n\r\n  const state: ObserversSetupState = {\r\n    _heightIntrinsic: false,\r\n    _directionIsRTL: getDirectionIsRTL(_target),\r\n  };\r\n  const env = getEnvironment();\r\n  const scrollbarsHidingPlugin = getStaticPluginModuleInstance<typeof ScrollbarsHidingPlugin>(\r\n    scrollbarsHidingPluginName\r\n  );\r\n\r\n  const [updateContentSizeCache] = createCache<WH<number>>(\r\n    {\r\n      _equal: equalWH,\r\n      _initialValue: { w: 0, h: 0 },\r\n    },\r\n    () => {\r\n      const _undoViewportArrange =\r\n        scrollbarsHidingPlugin &&\r\n        scrollbarsHidingPlugin._viewportArrangement(\r\n          structureSetupElements,\r\n          structureSetupState,\r\n          state,\r\n          env,\r\n          getCurrentOption\r\n        )._undoViewportArrange;\r\n\r\n      const viewportIsTargetBody = _isBody && _viewportIsTarget;\r\n      const noClipping =\r\n        !viewportIsTargetBody && hasAttrClass(_host, dataAttributeHost, dataValueNoClipping);\r\n      const isArranged = !_viewportIsTarget && _viewportHasClass(dataValueViewportArrange);\r\n      const scrollOffset = isArranged && getElementScroll(_scrollOffsetElement);\r\n      const revertScrollObscuringStyles = scrollOffset && _removeScrollObscuringStyles();\r\n\r\n      const revertMeasuring = _viewportAddRemoveClass(dataValueViewportMeasuring, noClipping);\r\n      const redoViewportArrange = isArranged && _undoViewportArrange && _undoViewportArrange()[0];\r\n      const viewportScroll = getScrollSize(_viewport);\r\n      const fractional = getFractionalSize(_viewport);\r\n\r\n      redoViewportArrange && redoViewportArrange();\r\n\r\n      scrollElementTo(_scrollOffsetElement, scrollOffset);\r\n      revertScrollObscuringStyles && revertScrollObscuringStyles();\r\n      noClipping && revertMeasuring();\r\n\r\n      return {\r\n        w: viewportScroll.w + fractional.w,\r\n        h: viewportScroll.h + fractional.h,\r\n      };\r\n    }\r\n  );\r\n\r\n  const onObserversUpdatedDebounced = debounce(onObserversUpdated, {\r\n    _timeout: () => debounceTimeout,\r\n    _maxDelay: () => debounceMaxDelay,\r\n    _mergeParams(prev, curr) {\r\n      const [prevObj] = prev;\r\n      const [currObj] = curr;\r\n      return [\r\n        concat(keys(prevObj), keys(currObj)).reduce((obj, key) => {\r\n          obj[key] = prevObj[key as keyof typeof prevObj] || currObj[key as keyof typeof currObj];\r\n          return obj;\r\n        }, {} as PlainObject),\r\n      ] as [Partial<ObserversSetupUpdateHints>];\r\n    },\r\n  });\r\n\r\n  const setDirection = (updateHints: ObserversSetupUpdateHints) => {\r\n    const newDirectionIsRTL = getDirectionIsRTL(_target);\r\n    assignDeep(updateHints, { _directionChanged: prevDirectionIsRTL !== newDirectionIsRTL });\r\n    assignDeep(state, { _directionIsRTL: newDirectionIsRTL });\r\n    prevDirectionIsRTL = newDirectionIsRTL;\r\n  };\r\n\r\n  const onTrinsicChanged = (\r\n    heightIntrinsicCache: CacheValues<boolean>,\r\n    fromRecords?: true\r\n  ): ObserversSetupUpdateHints => {\r\n    const [heightIntrinsic, heightIntrinsicChanged] = heightIntrinsicCache;\r\n    const updateHints: ObserversSetupUpdateHints = {\r\n      _heightIntrinsicChanged: heightIntrinsicChanged,\r\n    };\r\n\r\n    assignDeep(state, { _heightIntrinsic: heightIntrinsic });\r\n    !fromRecords && onObserversUpdated(updateHints);\r\n\r\n    return updateHints;\r\n  };\r\n\r\n  const onSizeChanged = ({ _sizeChanged, _appear }: SizeObserverCallbackParams) => {\r\n    const exclusiveSizeChange = _sizeChanged && !_appear;\r\n    const updateFn =\r\n      // use debounceed update:\r\n      // if native scrollbars hiding is supported\r\n      // and if the update is more than just a exclusive sizeChange (e.g. size change + appear, or size change + direction)\r\n      !exclusiveSizeChange && env._nativeScrollbarsHiding\r\n        ? onObserversUpdatedDebounced\r\n        : onObserversUpdated;\r\n\r\n    const updateHints: ObserversSetupUpdateHints = {\r\n      _sizeChanged: _sizeChanged || _appear,\r\n      _appear,\r\n    };\r\n\r\n    setDirection(updateHints);\r\n\r\n    updateFn(updateHints);\r\n  };\r\n\r\n  const onContentMutation = (\r\n    contentChangedThroughEvent: boolean,\r\n    fromRecords?: true\r\n  ): ObserversSetupUpdateHints => {\r\n    const [, _contentMutation] = updateContentSizeCache();\r\n    const updateHints: ObserversSetupUpdateHints = {\r\n      _contentMutation,\r\n    };\r\n\r\n    setDirection(updateHints);\r\n\r\n    // if contentChangedThroughEvent is true its already debounced\r\n    const updateFn = contentChangedThroughEvent ? onObserversUpdated : onObserversUpdatedDebounced;\r\n\r\n    _contentMutation && !fromRecords && updateFn(updateHints);\r\n\r\n    return updateHints;\r\n  };\r\n\r\n  const onHostMutation = (\r\n    targetChangedAttrs: string[],\r\n    targetStyleChanged: boolean,\r\n    fromRecords?: true\r\n  ): ObserversSetupUpdateHints => {\r\n    const updateHints: ObserversSetupUpdateHints = {\r\n      _hostMutation: targetStyleChanged,\r\n    };\r\n\r\n    setDirection(updateHints);\r\n\r\n    if (targetStyleChanged && !fromRecords) {\r\n      onObserversUpdatedDebounced(updateHints);\r\n    }\r\n    /*\r\n    else if (!_viewportIsTarget) {\r\n      updateViewportAttrsFromHost(targetChangedAttrs);\r\n    }\r\n    */\r\n\r\n    return updateHints;\r\n  };\r\n\r\n  const [constructTrinsicObserver, updateTrinsicObserver] = _content\r\n    ? createTrinsicObserver(_host, onTrinsicChanged)\r\n    : [];\r\n\r\n  const constructSizeObserver =\r\n    !_viewportIsTarget &&\r\n    createSizeObserver(_host, onSizeChanged, {\r\n      _appear: true,\r\n    });\r\n\r\n  const [constructHostMutationObserver, updateHostMutationObserver] = createDOMObserver(\r\n    _host,\r\n    false,\r\n    onHostMutation,\r\n    {\r\n      _styleChangingAttributes: baseStyleChangingAttrs,\r\n      _attributes: baseStyleChangingAttrs,\r\n    }\r\n  );\r\n\r\n  const viewportIsTargetResizeObserver =\r\n    _viewportIsTarget &&\r\n    ResizeObserverConstructor &&\r\n    new ResizeObserverConstructor((entries) => {\r\n      const currContentRect = entries[entries.length - 1].contentRect;\r\n      onSizeChanged({\r\n        _sizeChanged: true,\r\n        _appear: domRectAppeared(currContentRect, prevContentRect),\r\n      });\r\n      prevContentRect = currContentRect;\r\n    });\r\n  const onWindowResizeDebounced = debounce(\r\n    () => {\r\n      const [, _contentMutation] = updateContentSizeCache();\r\n      onObserversUpdated({ _contentMutation });\r\n    },\r\n    {\r\n      _timeout: 222,\r\n      _leading: true,\r\n    }\r\n  );\r\n\r\n  return [\r\n    () => {\r\n      // order is matter!\r\n      // updateViewportAttrsFromHost();\r\n      viewportIsTargetResizeObserver && viewportIsTargetResizeObserver.observe(_host);\r\n      const destroySizeObserver = constructSizeObserver && constructSizeObserver();\r\n      const destroyTrinsicObserver = constructTrinsicObserver && constructTrinsicObserver();\r\n      const destroyHostMutationObserver = constructHostMutationObserver();\r\n      const removeResizeListener = env._addResizeListener((_scrollbarSizeChanged) => {\r\n        if (_scrollbarSizeChanged) {\r\n          onObserversUpdatedDebounced({ _scrollbarSizeChanged });\r\n        } else {\r\n          onWindowResizeDebounced();\r\n        }\r\n      });\r\n\r\n      return () => {\r\n        viewportIsTargetResizeObserver && viewportIsTargetResizeObserver.disconnect();\r\n        destroySizeObserver && destroySizeObserver();\r\n        destroyTrinsicObserver && destroyTrinsicObserver();\r\n        destroyContentMutationObserver && destroyContentMutationObserver();\r\n        destroyHostMutationObserver();\r\n        removeResizeListener();\r\n      };\r\n    },\r\n    ({ _checkOption, _takeRecords, _force }) => {\r\n      const updateHints: ObserversSetupUpdateHints = {};\r\n\r\n      const [ignoreMutation] = _checkOption('update.ignoreMutation');\r\n      const [attributes, attributesChanged] = _checkOption('update.attributes');\r\n      const [elementEvents, elementEventsChanged] = _checkOption('update.elementEvents');\r\n      const [debounceValue, debounceChanged] = _checkOption('update.debounce');\r\n      const contentMutationObserverChanged = elementEventsChanged || attributesChanged;\r\n      const takeRecords = _takeRecords || _force;\r\n      const ignoreMutationFromOptions = (mutation: MutationRecord) =>\r\n        isFunction(ignoreMutation) && ignoreMutation(mutation);\r\n\r\n      if (contentMutationObserverChanged) {\r\n        updateContentMutationObserver && updateContentMutationObserver();\r\n        destroyContentMutationObserver && destroyContentMutationObserver();\r\n\r\n        const [construct, update] = createDOMObserver(\r\n          _content || _viewport,\r\n          true,\r\n          onContentMutation,\r\n          {\r\n            _attributes: concat(baseStyleChangingAttrs, attributes || []),\r\n            _eventContentChange: elementEvents,\r\n            _nestedTargetSelector: hostSelector,\r\n            _ignoreContentChange: (mutation, isNestedTarget) => {\r\n              const { target: mutationTarget, attributeName } = mutation;\r\n              const ignore =\r\n                !isNestedTarget && attributeName && !_viewportIsTarget\r\n                  ? liesBetween(mutationTarget, hostSelector, viewportSelector)\r\n                  : false;\r\n              return (\r\n                ignore ||\r\n                !!closest(mutationTarget, `.${classNameScrollbar}`) || // ignore explicitely all scrollbar elements\r\n                !!ignoreMutationFromOptions(mutation)\r\n              );\r\n            },\r\n          }\r\n        );\r\n\r\n        destroyContentMutationObserver = construct();\r\n        updateContentMutationObserver = update;\r\n      }\r\n\r\n      if (debounceChanged) {\r\n        onObserversUpdatedDebounced._flush();\r\n        if (isArray(debounceValue)) {\r\n          const timeout = debounceValue[0];\r\n          const maxWait = debounceValue[1];\r\n          debounceTimeout = isNumber(timeout) && timeout;\r\n          debounceMaxDelay = isNumber(maxWait) && maxWait;\r\n        } else if (isNumber(debounceValue)) {\r\n          debounceTimeout = debounceValue;\r\n          debounceMaxDelay = false;\r\n        } else {\r\n          debounceTimeout = false;\r\n          debounceMaxDelay = false;\r\n        }\r\n      }\r\n\r\n      if (takeRecords) {\r\n        const hostUpdateResult = updateHostMutationObserver();\r\n        const trinsicUpdateResult = updateTrinsicObserver && updateTrinsicObserver();\r\n        const contentUpdateResult =\r\n          updateContentMutationObserver && updateContentMutationObserver();\r\n\r\n        hostUpdateResult &&\r\n          assignDeep(\r\n            updateHints,\r\n            onHostMutation(hostUpdateResult[0], hostUpdateResult[1], takeRecords)\r\n          );\r\n\r\n        trinsicUpdateResult &&\r\n          assignDeep(updateHints, onTrinsicChanged(trinsicUpdateResult[0], takeRecords));\r\n\r\n        contentUpdateResult &&\r\n          assignDeep(updateHints, onContentMutation(contentUpdateResult[0], takeRecords));\r\n      }\r\n\r\n      setDirection(updateHints);\r\n\r\n      return updateHints;\r\n    },\r\n    state,\r\n  ];\r\n};\r\n", "import type { DeepPartial } from './typings';\r\nimport { isFunction, isHTMLElement, isNull, isUndefined } from './support';\r\nimport { getEnvironment } from './environment';\r\n\r\ntype FallbackStaticInitializtationElement<Args extends any[]> =\r\n  Extract<StaticInitializationElement<Args>, (...args: Args) => any> extends (\r\n    ...args: infer P\r\n  ) => any\r\n    ? (...args: P) => HTMLElement\r\n    : never;\r\ntype FallbackDynamicInitializtationElement<Args extends any[]> =\r\n  Extract<DynamicInitializationElement<Args>, (...args: Args) => any> extends (\r\n    ...args: infer P\r\n  ) => any\r\n    ? (...args: P) => HTMLElement\r\n    : never;\r\n\r\nexport type StaticInitialization = HTMLElement | false | null;\r\nexport type DynamicInitialization = HTMLElement | boolean | null;\r\n\r\n/**\r\n * Static elements are elements which MUST be present in the final DOM.\r\n * If an `HTMLElement` is passed the passed element will be taken as the repsective element.\r\n * With `false`, `null` or `undefined` an appropriate element is generated automatically.\r\n */\r\nexport type StaticInitializationElement<Args extends any[]> =\r\n  /** A function which returns the the StaticInitialization value. */\r\n  | ((...args: Args) => StaticInitialization)\r\n  /** The StaticInitialization value. */\r\n  | StaticInitialization;\r\n\r\n/**\r\n * Dynamic elements are elements which CAN be present in the final DOM.\r\n * If an `HTMLElement`is passed the passed element will be taken as the repsective element.\r\n * With `true` an appropriate element is generated automatically.\r\n * With `false`, `null` or `undefined` the element won't be in the DOM.\r\n */\r\nexport type DynamicInitializationElement<Args extends any[]> =\r\n  /** A function which returns the the DynamicInitialization value. */\r\n  | ((...args: Args) => DynamicInitialization)\r\n  /** The DynamicInitialization value. */\r\n  | DynamicInitialization;\r\n\r\n/**\r\n * Describes how a OverlayScrollbar instance should initialize.\r\n */\r\nexport type Initialization = {\r\n  /**\r\n   * Customizes which elements are generated and used.\r\n   * If a function is passed to any of the fields, it receives the `target` element as its argument.\r\n   * Any passed function should be a \"pure\" function. (same input produces same output)\r\n   */\r\n  elements: {\r\n    /**\r\n     * Assign a custom element as the host element.\r\n     * Only relevant if the target element is a Textarea.\r\n     */\r\n    host: StaticInitializationElement<[target: InitializationTargetElement]>;\r\n    /** Assign a custom element as the viewport element. */\r\n    viewport: StaticInitializationElement<[target: InitializationTargetElement]>;\r\n    /** Assign a custom element as the padding element or force the element not to be generated. */\r\n    padding: DynamicInitializationElement<[target: InitializationTargetElement]>;\r\n    /** Assign a custom element as the content element or force the element not to be generated. */\r\n    content: DynamicInitializationElement<[target: InitializationTargetElement]>;\r\n  };\r\n  /**\r\n   * Customizes elements related to the scrollbars.\r\n   * If a function is passed, it receives the `target`, `host` and `viewport` element as arguments.\r\n   */\r\n  scrollbars: {\r\n    slot: DynamicInitializationElement<\r\n      [target: InitializationTargetElement, host: HTMLElement, viewport: HTMLElement]\r\n    >;\r\n  };\r\n  /**\r\n   * Customizes the cancelation behavior.\r\n   */\r\n  cancel: {\r\n    /** Whether the initialization shall be canceled if the native scrollbars are overlaid. */\r\n    nativeScrollbarsOverlaid: boolean;\r\n    /**\r\n     * Whether the initialization shall be canceled if its applied to a body element.\r\n     * With `true` an initialization is always canceled, with `false` its never canceled.\r\n     * With `null` the initialization will only be canceled when the initialization would affect the browsers functionality. (window.scrollTo, mobile browser behavior etc.)\r\n     */\r\n    body: boolean | null;\r\n  };\r\n};\r\n\r\nexport type PartialInitialization = DeepPartial<Initialization>;\r\n\r\n/** The initialization target element. */\r\nexport type InitializationTargetElement = HTMLElement; // | HTMLTextAreaElement;\r\n\r\n/**\r\n * The initialization target object.\r\n * OverlayScrollbars({ target: myElement }) is equivalent to OverlayScrollbars(myElement).\r\n */\r\nexport type InitializationTargetObject = PartialInitialization & {\r\n  target: InitializationTargetElement;\r\n};\r\n\r\n/** The initialization target. */\r\nexport type InitializationTarget = InitializationTargetElement | InitializationTargetObject;\r\n\r\nexport const resolveInitialization = <T extends StaticInitialization | DynamicInitialization>(\r\n  args: any,\r\n  value: any\r\n): T => (isFunction(value) ? value.apply(0, args) : value);\r\n\r\nexport const staticInitializationElement = <Args extends any[]>(\r\n  args: Args,\r\n  fallbackStaticInitializationElement: FallbackStaticInitializtationElement<Args>,\r\n  defaultStaticInitializationElement: StaticInitializationElement<Args>,\r\n  staticInitializationElementValue?: StaticInitializationElement<Args>\r\n): HTMLElement => {\r\n  const staticInitialization = isUndefined(staticInitializationElementValue)\r\n    ? defaultStaticInitializationElement\r\n    : staticInitializationElementValue;\r\n  const resolvedInitialization = resolveInitialization<StaticInitialization>(\r\n    args,\r\n    staticInitialization\r\n  );\r\n  return resolvedInitialization || fallbackStaticInitializationElement.apply(0, args);\r\n};\r\n\r\nexport const dynamicInitializationElement = <Args extends any[]>(\r\n  args: Args,\r\n  fallbackDynamicInitializationElement: FallbackDynamicInitializtationElement<Args>,\r\n  defaultDynamicInitializationElement: DynamicInitializationElement<Args>,\r\n  dynamicInitializationElementValue?: DynamicInitializationElement<Args>\r\n): HTMLElement | false => {\r\n  const dynamicInitialization = isUndefined(dynamicInitializationElementValue)\r\n    ? defaultDynamicInitializationElement\r\n    : dynamicInitializationElementValue;\r\n  const resolvedInitialization = resolveInitialization<DynamicInitialization>(\r\n    args,\r\n    dynamicInitialization\r\n  );\r\n  return (\r\n    !!resolvedInitialization &&\r\n    (isHTMLElement(resolvedInitialization)\r\n      ? resolvedInitialization\r\n      : fallbackDynamicInitializationElement.apply(0, args))\r\n  );\r\n};\r\n\r\nexport const cancelInitialization = (\r\n  isBody: boolean,\r\n  cancelInitializationValue?: DeepPartial<Initialization['cancel']> | false | null | undefined\r\n): boolean => {\r\n  const { nativeScrollbarsOverlaid, body } = cancelInitializationValue || {};\r\n  const { _nativeScrollbarsOverlaid, _nativeScrollbarsHiding, _getDefaultInitialization } =\r\n    getEnvironment();\r\n  const { nativeScrollbarsOverlaid: defaultNativeScrollbarsOverlaid, body: defaultbody } =\r\n    _getDefaultInitialization().cancel;\r\n\r\n  const resolvedNativeScrollbarsOverlaid =\r\n    nativeScrollbarsOverlaid ?? defaultNativeScrollbarsOverlaid;\r\n  const resolvedDocumentScrollingElement = isUndefined(body) ? defaultbody : body;\r\n\r\n  const finalNativeScrollbarsOverlaid =\r\n    (_nativeScrollbarsOverlaid.x || _nativeScrollbarsOverlaid.y) &&\r\n    resolvedNativeScrollbarsOverlaid;\r\n  const finalDocumentScrollingElement =\r\n    isBody &&\r\n    (isNull(resolvedDocumentScrollingElement)\r\n      ? !_nativeScrollbarsHiding\r\n      : resolvedDocumentScrollingElement);\r\n\r\n  return !!finalNativeScrollbarsOverlaid || !!finalDocumentScrollingElement;\r\n};\r\n", "import type { XY } from '../../support';\r\nimport type {\r\n  InitializationTarget,\r\n  InitializationTargetElement,\r\n  InitializationTargetObject,\r\n} from '../../initialization';\r\nimport type { StructureSetupElementsObj } from '../structureSetup/structureSetup.elements';\r\nimport type { ScrollbarsSetupEvents } from './scrollbarsSetup.events';\r\nimport type { StyleObject } from '../../typings';\r\nimport type { StructureSetupState } from '../structureSetup';\r\nimport { dynamicInitializationElement as generalDynamicInitializationElement } from '../../initialization';\r\nimport { getEnvironment } from '../../environment';\r\nimport {\r\n  classNameScrollbar,\r\n  classNameScrollbarHorizontal,\r\n  classNameScrollbarVertical,\r\n  classNameScrollbarTrack,\r\n  classNameScrollbarHandle,\r\n} from '../../classnames';\r\nimport {\r\n  addClass,\r\n  appendChildren,\r\n  createDiv,\r\n  each,\r\n  getTrasformTranslateValue,\r\n  isBoolean,\r\n  parent,\r\n  push,\r\n  removeClass,\r\n  removeElements,\r\n  runEachAndClear,\r\n  scrollT,\r\n  bind,\r\n  getElementScroll,\r\n  numberToCssPx,\r\n  setStyles,\r\n  capNumber,\r\n  getScrollCoordinatesPercent,\r\n  isDefaultDirectionScrollCoordinates,\r\n  roundCssNumber,\r\n} from '../../support';\r\n\r\nexport interface ScrollbarStructure {\r\n  _scrollbar: HTMLElement;\r\n  _track: HTMLElement;\r\n  _handle: HTMLElement;\r\n}\r\n\r\nexport interface ScrollbarsSetupElement {\r\n  _scrollbarStructures: ScrollbarStructure[];\r\n  _clone: () => ScrollbarStructure;\r\n  _style: (\r\n    elmStyle: (\r\n      scrollbarStructure: ScrollbarStructure\r\n    ) => [HTMLElement | false | null | undefined, StyleObject]\r\n  ) => void;\r\n}\r\n\r\nexport interface ScrollbarsSetupElementsObj {\r\n  _scrollbarsAddRemoveClass: (\r\n    classNames: string | false | null | undefined,\r\n    add?: boolean,\r\n    isHorizontal?: boolean\r\n  ) => void;\r\n  _refreshScrollbarsHandleLength: () => void;\r\n  _refreshScrollbarsHandleOffset: () => void;\r\n  _refreshScrollbarsScrollbarOffset: () => void;\r\n  _refreshScrollbarsScrollCoordinates: () => void;\r\n  _horizontal: ScrollbarsSetupElement;\r\n  _vertical: ScrollbarsSetupElement;\r\n}\r\n\r\nexport type ScrollbarsSetupElements = [\r\n  elements: ScrollbarsSetupElementsObj,\r\n  appendElements: () => () => void,\r\n];\r\n\r\ntype ScrollbarStyleFn = (\r\n  scrollbarStructure: ScrollbarStructure\r\n) => [HTMLElement | false | null | undefined, StyleObject | false | null | undefined];\r\n\r\nexport const createScrollbarsSetupElements = (\r\n  target: InitializationTarget,\r\n  structureSetupElements: StructureSetupElementsObj,\r\n  structureSetupState: StructureSetupState,\r\n  scrollbarsSetupEvents: ScrollbarsSetupEvents\r\n): ScrollbarsSetupElements => {\r\n  const cssCustomPropViewportPercent = '--os-viewport-percent';\r\n  const cssCustomPropScrollPercent = '--os-scroll-percent';\r\n  const cssCustomPropScrollDirection = '--os-scroll-direction';\r\n  const { _getDefaultInitialization } = getEnvironment();\r\n  const { scrollbars: defaultInitScrollbars } = _getDefaultInitialization();\r\n  const { slot: defaultInitScrollbarsSlot } = defaultInitScrollbars;\r\n  const {\r\n    _target,\r\n    _host,\r\n    _viewport,\r\n    _targetIsElm,\r\n    _scrollOffsetElement,\r\n    _isBody,\r\n    _viewportIsTarget,\r\n  } = structureSetupElements;\r\n  const { scrollbars: scrollbarsInit } = (_targetIsElm ? {} : target) as InitializationTargetObject;\r\n  const { slot: initScrollbarsSlot } = scrollbarsInit || {};\r\n  const destroyFns: (() => void)[] = [];\r\n  const horizontalScrollbars: ScrollbarStructure[] = [];\r\n  const verticalScrollbars: ScrollbarStructure[] = [];\r\n  const evaluatedScrollbarSlot = generalDynamicInitializationElement<\r\n    [InitializationTargetElement, HTMLElement, HTMLElement]\r\n  >(\r\n    [_target, _host, _viewport],\r\n    () => (_viewportIsTarget && _isBody ? _target : _host),\r\n    defaultInitScrollbarsSlot,\r\n    initScrollbarsSlot\r\n  );\r\n\r\n  const initScrollTimeline = (axis: keyof XY<unknown>) => {\r\n    if (scrollT) {\r\n      let currAnimation: Animation | null = null;\r\n      let currAnimationTransform: string[] = [];\r\n      const timeline = new scrollT({\r\n        source: _scrollOffsetElement,\r\n        axis,\r\n      });\r\n      const cancelAnimation = () => {\r\n        currAnimation && currAnimation.cancel();\r\n        currAnimation = null;\r\n      };\r\n      const _setScrollPercentAnimation = (structure: ScrollbarStructure) => {\r\n        const { _scrollCoordinates } = structureSetupState;\r\n        const defaultDirectionScroll =\r\n          isDefaultDirectionScrollCoordinates(_scrollCoordinates)[axis];\r\n        const isHorizontal = axis === 'x';\r\n        const transformArray = [\r\n          getTrasformTranslateValue(0, isHorizontal),\r\n          getTrasformTranslateValue(`calc(100cq${isHorizontal ? 'w' : 'h'} + -100%)`, isHorizontal),\r\n        ];\r\n        const transform = defaultDirectionScroll ? transformArray : transformArray.reverse();\r\n\r\n        if (\r\n          currAnimationTransform[0] === transform[0] &&\r\n          currAnimationTransform[1] === transform[1]\r\n        ) {\r\n          return cancelAnimation;\r\n        }\r\n\r\n        cancelAnimation();\r\n        currAnimationTransform = transform;\r\n        currAnimation = structure._handle.animate(\r\n          {\r\n            // dummy keyframe which fixes bug where the scrollbar handle is reverted to origin position when it should be at its max position\r\n            clear: ['left'],\r\n            // transform is a temporary fix for: https://github.com/KingSora/OverlayScrollbars/issues/705\r\n            // can be reverted to just animate \"cssCustomPropScrollPercent\" when browsers implement an optimization possibility\r\n            transform,\r\n            // [cssCustomPropScrollPercent]: [0, 1],\r\n          },\r\n          {\r\n            timeline,\r\n          }\r\n        );\r\n\r\n        return cancelAnimation;\r\n      };\r\n\r\n      return {\r\n        _setScrollPercentAnimation,\r\n      };\r\n    }\r\n  };\r\n  const scrollTimeline = {\r\n    x: initScrollTimeline('x'),\r\n    y: initScrollTimeline('y'),\r\n  };\r\n  const getViewportPercent = () => {\r\n    const { _overflowAmount, _overflowEdge } = structureSetupState;\r\n    const getAxisValue = (axisViewportSize: number, axisOverflowAmount: number) =>\r\n      capNumber(0, 1, axisViewportSize / (axisViewportSize + axisOverflowAmount) || 0);\r\n\r\n    return {\r\n      x: getAxisValue(_overflowEdge.x, _overflowAmount.x),\r\n      y: getAxisValue(_overflowEdge.y, _overflowAmount.y),\r\n    };\r\n  };\r\n  const scrollbarStructureAddRemoveClass = (\r\n    scrollbarStructures: ScrollbarStructure[],\r\n    classNames: string | false | null | undefined,\r\n    add?: boolean\r\n  ) => {\r\n    const action = add ? addClass : removeClass;\r\n    each(scrollbarStructures, (scrollbarStructure) => {\r\n      action(scrollbarStructure._scrollbar, classNames);\r\n    });\r\n  };\r\n  const scrollbarStyle = (\r\n    scrollbarStructures: ScrollbarStructure[],\r\n    elmStyle: ScrollbarStyleFn\r\n  ) => {\r\n    each(scrollbarStructures, (scrollbarStructure) => {\r\n      const [elm, styles] = elmStyle(scrollbarStructure);\r\n      setStyles(elm, styles);\r\n    });\r\n  };\r\n  const scrollbarsAddRemoveClass = (\r\n    className: string | false | null | undefined,\r\n    add?: boolean,\r\n    onlyHorizontal?: boolean\r\n  ) => {\r\n    const singleAxis = isBoolean(onlyHorizontal);\r\n    const runHorizontal = singleAxis ? onlyHorizontal : true;\r\n    const runVertical = singleAxis ? !onlyHorizontal : true;\r\n    runHorizontal && scrollbarStructureAddRemoveClass(horizontalScrollbars, className, add);\r\n    runVertical && scrollbarStructureAddRemoveClass(verticalScrollbars, className, add);\r\n  };\r\n  const refreshScrollbarsHandleLength = () => {\r\n    const viewportPercent = getViewportPercent();\r\n    const createScrollbarStyleFn =\r\n      (axisViewportPercent: number): ScrollbarStyleFn =>\r\n      (structure: ScrollbarStructure) => [\r\n        structure._scrollbar,\r\n        {\r\n          [cssCustomPropViewportPercent]: roundCssNumber(axisViewportPercent) + '',\r\n        },\r\n      ];\r\n\r\n    scrollbarStyle(horizontalScrollbars, createScrollbarStyleFn(viewportPercent.x));\r\n    scrollbarStyle(verticalScrollbars, createScrollbarStyleFn(viewportPercent.y));\r\n  };\r\n  const refreshScrollbarsHandleOffset = () => {\r\n    if (!scrollT) {\r\n      const { _scrollCoordinates } = structureSetupState;\r\n      const scrollPercent = getScrollCoordinatesPercent(\r\n        _scrollCoordinates,\r\n        getElementScroll(_scrollOffsetElement)\r\n      );\r\n      const createScrollbarStyleFn =\r\n        (axisScrollPercent: number): ScrollbarStyleFn =>\r\n        (structure: ScrollbarStructure) => [\r\n          structure._scrollbar,\r\n          {\r\n            [cssCustomPropScrollPercent]: roundCssNumber(axisScrollPercent) + '',\r\n          },\r\n        ];\r\n\r\n      scrollbarStyle(horizontalScrollbars, createScrollbarStyleFn(scrollPercent.x));\r\n      scrollbarStyle(verticalScrollbars, createScrollbarStyleFn(scrollPercent.y));\r\n    }\r\n  };\r\n  const refreshScrollbarsScrollCoordinates = () => {\r\n    const { _scrollCoordinates } = structureSetupState;\r\n    const defaultDirectionScroll = isDefaultDirectionScrollCoordinates(_scrollCoordinates);\r\n    const createScrollbarStyleFn =\r\n      (axisIsDefaultDirectionScrollCoordinates: boolean): ScrollbarStyleFn =>\r\n      (structure: ScrollbarStructure) => [\r\n        structure._scrollbar,\r\n        {\r\n          [cssCustomPropScrollDirection]: axisIsDefaultDirectionScrollCoordinates ? '0' : '1',\r\n        },\r\n      ];\r\n\r\n    scrollbarStyle(horizontalScrollbars, createScrollbarStyleFn(defaultDirectionScroll.x));\r\n    scrollbarStyle(verticalScrollbars, createScrollbarStyleFn(defaultDirectionScroll.y));\r\n\r\n    // temporary fix for: https://github.com/KingSora/OverlayScrollbars/issues/705\r\n    if (scrollT) {\r\n      horizontalScrollbars.forEach(scrollTimeline.x!._setScrollPercentAnimation);\r\n      verticalScrollbars.forEach(scrollTimeline.y!._setScrollPercentAnimation);\r\n    }\r\n  };\r\n  const refreshScrollbarsScrollbarOffset = () => {\r\n    if (_viewportIsTarget && !_isBody) {\r\n      const { _overflowAmount, _scrollCoordinates } = structureSetupState;\r\n      const isDefaultDirectionScroll = isDefaultDirectionScrollCoordinates(_scrollCoordinates);\r\n      const scrollPercent = getScrollCoordinatesPercent(\r\n        _scrollCoordinates,\r\n        getElementScroll(_scrollOffsetElement)\r\n      );\r\n      const styleScrollbarPosition: ScrollbarStyleFn = (structure: ScrollbarStructure) => {\r\n        const { _scrollbar } = structure;\r\n        const elm = parent(_scrollbar) === _viewport && _scrollbar;\r\n        const getTranslateValue = (\r\n          axisScrollPercent: number,\r\n          axisOverflowAmount: number,\r\n          axisIsDefaultCoordinates: boolean\r\n        ) => {\r\n          const px = axisOverflowAmount * axisScrollPercent;\r\n          return numberToCssPx(axisIsDefaultCoordinates ? px : -px);\r\n        };\r\n\r\n        return [\r\n          elm,\r\n          elm && {\r\n            transform: getTrasformTranslateValue({\r\n              x: getTranslateValue(scrollPercent.x, _overflowAmount.x, isDefaultDirectionScroll.x),\r\n              y: getTranslateValue(scrollPercent.y, _overflowAmount.y, isDefaultDirectionScroll.y),\r\n            }),\r\n          },\r\n        ];\r\n      };\r\n\r\n      scrollbarStyle(horizontalScrollbars, styleScrollbarPosition);\r\n      scrollbarStyle(verticalScrollbars, styleScrollbarPosition);\r\n    }\r\n  };\r\n  const generateScrollbarDOM = (isHorizontal?: boolean): ScrollbarStructure => {\r\n    const xyKey = isHorizontal ? 'x' : 'y';\r\n    const scrollbarClassName = isHorizontal\r\n      ? classNameScrollbarHorizontal\r\n      : classNameScrollbarVertical;\r\n    const scrollbar = createDiv(`${classNameScrollbar} ${scrollbarClassName}`);\r\n    const track = createDiv(classNameScrollbarTrack);\r\n    const handle = createDiv(classNameScrollbarHandle);\r\n    const result = {\r\n      _scrollbar: scrollbar,\r\n      _track: track,\r\n      _handle: handle,\r\n    };\r\n    const timeline = scrollTimeline[xyKey];\r\n\r\n    push(isHorizontal ? horizontalScrollbars : verticalScrollbars, result);\r\n    push(destroyFns, [\r\n      appendChildren(scrollbar, track),\r\n      appendChildren(track, handle),\r\n      bind(removeElements, scrollbar),\r\n      timeline && timeline._setScrollPercentAnimation(result),\r\n      scrollbarsSetupEvents(result, scrollbarsAddRemoveClass, isHorizontal),\r\n    ]);\r\n\r\n    return result;\r\n  };\r\n  const generateHorizontalScrollbarStructure = bind(generateScrollbarDOM, true);\r\n  const generateVerticalScrollbarStructure = bind(generateScrollbarDOM, false);\r\n  const appendElements = () => {\r\n    appendChildren(evaluatedScrollbarSlot, horizontalScrollbars[0]._scrollbar);\r\n    appendChildren(evaluatedScrollbarSlot, verticalScrollbars[0]._scrollbar);\r\n\r\n    return bind(runEachAndClear, destroyFns);\r\n  };\r\n\r\n  generateHorizontalScrollbarStructure();\r\n  generateVerticalScrollbarStructure();\r\n\r\n  return [\r\n    {\r\n      _refreshScrollbarsHandleLength: refreshScrollbarsHandleLength,\r\n      _refreshScrollbarsHandleOffset: refreshScrollbarsHandleOffset,\r\n      _refreshScrollbarsScrollCoordinates: refreshScrollbarsScrollCoordinates,\r\n      _refreshScrollbarsScrollbarOffset: refreshScrollbarsScrollbarOffset,\r\n      _scrollbarsAddRemoveClass: scrollbarsAddRemoveClass,\r\n      _horizontal: {\r\n        _scrollbarStructures: horizontalScrollbars,\r\n        _clone: generateHorizontalScrollbarStructure,\r\n        _style: bind(scrollbarStyle, horizontalScrollbars),\r\n      },\r\n      _vertical: {\r\n        _scrollbarStructures: verticalScrollbars,\r\n        _clone: generateVerticalScrollbarStructure,\r\n        _style: bind(scrollbarStyle, verticalScrollbars),\r\n      },\r\n    },\r\n    appendElements,\r\n  ];\r\n};\r\n", "import type { XY } from '../../support';\r\nimport type { ClickScrollPlugin } from '../../plugins';\r\nimport type { ReadonlyOptions } from '../../options';\r\nimport type { StructureSetupState } from '../../setups';\r\nimport type { ScrollbarsSetupElementsObj, ScrollbarStructure } from './scrollbarsSetup.elements';\r\nimport type { StructureSetupElementsObj } from '../structureSetup/structureSetup.elements';\r\nimport {\r\n  classNameScrollbarHandle,\r\n  classNameScrollbarInteraction,\r\n  classNameScrollbarWheel,\r\n  dataAttributeHost,\r\n  dataAttributeViewport,\r\n} from '../../classnames';\r\nimport { clickScrollPluginModuleName, getStaticPluginModuleInstance } from '../../plugins';\r\nimport {\r\n  getBoundingClientRect,\r\n  getOffsetSize,\r\n  addEventListener,\r\n  preventDefault,\r\n  runEachAndClear,\r\n  selfClearTimeout,\r\n  parent,\r\n  closest,\r\n  push,\r\n  bind,\r\n  mathRound,\r\n  strWidth,\r\n  strHeight,\r\n  getElementScroll,\r\n  scrollElementTo,\r\n  getFocusedElement,\r\n  setT,\r\n  hasAttr,\r\n  stopAndPrevent,\r\n  isFunction,\r\n  mathAbs,\r\n  focusElement,\r\n} from '../../support';\r\n\r\nexport type ScrollbarsSetupEvents = (\r\n  scrollbarStructure: ScrollbarStructure,\r\n  scrollbarsAddRemoveClass: ScrollbarsSetupElementsObj['_scrollbarsAddRemoveClass'],\r\n  isHorizontal?: boolean\r\n) => () => void;\r\n\r\nexport const createScrollbarsSetupEvents = (\r\n  options: ReadonlyOptions,\r\n  structureSetupElements: StructureSetupElementsObj,\r\n  structureSetupState: StructureSetupState,\r\n  scrollbarHandlePointerInteraction: (event: PointerEvent) => void\r\n): ScrollbarsSetupEvents => {\r\n  return (scrollbarStructure, scrollbarsAddRemoveClass, isHorizontal) => {\r\n    const {\r\n      _host,\r\n      _viewport,\r\n      _viewportIsTarget,\r\n      _scrollOffsetElement,\r\n      _documentElm,\r\n      _removeScrollObscuringStyles,\r\n    } = structureSetupElements;\r\n    const { _scrollbar, _track, _handle } = scrollbarStructure;\r\n    const [wheelTimeout, clearWheelTimeout] = selfClearTimeout(333);\r\n    const [scrollSnapScrollTransitionTimeout, clearScrollSnapScrollTransitionTimeout] =\r\n      selfClearTimeout(444);\r\n    const scrollOffsetElementScrollBy = (coordinates: XY<number>) => {\r\n      isFunction(_scrollOffsetElement.scrollBy) &&\r\n        _scrollOffsetElement.scrollBy({\r\n          behavior: 'smooth',\r\n          left: coordinates.x,\r\n          top: coordinates.y,\r\n        });\r\n    };\r\n\r\n    const createInteractiveScrollEvents = () => {\r\n      const releasePointerCaptureEvents = 'pointerup pointercancel lostpointercapture';\r\n      const clientXYKey = `client${isHorizontal ? 'X' : 'Y'}` as 'clientX' | 'clientY';\r\n      const widthHeightKey = isHorizontal ? strWidth : strHeight;\r\n      const leftTopKey = isHorizontal ? 'left' : 'top';\r\n      const whKey = isHorizontal ? 'w' : 'h';\r\n      const xyKey = isHorizontal ? 'x' : 'y';\r\n\r\n      const createRelativeHandleMove =\r\n        (mouseDownScroll: number, invertedScale: number) => (deltaMovement: number) => {\r\n          const { _overflowAmount } = structureSetupState;\r\n          const handleTrackDiff = getOffsetSize(_track)[whKey] - getOffsetSize(_handle)[whKey];\r\n          const scrollDeltaPercent = (invertedScale * deltaMovement) / handleTrackDiff;\r\n          const scrollDelta = scrollDeltaPercent * _overflowAmount[xyKey];\r\n\r\n          scrollElementTo(_scrollOffsetElement, {\r\n            [xyKey]: mouseDownScroll + scrollDelta,\r\n          });\r\n        };\r\n      const pointerdownCleanupFns: Array<() => void> = [];\r\n\r\n      return addEventListener(_track, 'pointerdown', (pointerDownEvent: PointerEvent) => {\r\n        const isDragScroll =\r\n          closest(pointerDownEvent.target as Node, `.${classNameScrollbarHandle}`) === _handle;\r\n        const pointerCaptureElement = isDragScroll ? _handle : _track;\r\n\r\n        const scrollbarOptions = options.scrollbars;\r\n        const dragClickScrollOption = scrollbarOptions[isDragScroll ? 'dragScroll' : 'clickScroll'];\r\n        const { button, isPrimary, pointerType } = pointerDownEvent;\r\n        const { pointers } = scrollbarOptions;\r\n\r\n        const continuePointerDown =\r\n          button === 0 &&\r\n          isPrimary &&\r\n          dragClickScrollOption &&\r\n          (pointers || []).includes(pointerType);\r\n\r\n        if (continuePointerDown) {\r\n          runEachAndClear(pointerdownCleanupFns);\r\n          clearScrollSnapScrollTransitionTimeout();\r\n\r\n          const instantClickScroll =\r\n            !isDragScroll && (pointerDownEvent.shiftKey || dragClickScrollOption === 'instant');\r\n          const getHandleRect = bind(getBoundingClientRect, _handle);\r\n          const getTrackRect = bind(getBoundingClientRect, _track);\r\n          const getHandleOffset = (handleRect?: DOMRect, trackRect?: DOMRect) =>\r\n            (handleRect || getHandleRect())[leftTopKey] - (trackRect || getTrackRect())[leftTopKey];\r\n          const axisScale =\r\n            mathRound(getBoundingClientRect(_scrollOffsetElement)[widthHeightKey]) /\r\n              getOffsetSize(_scrollOffsetElement)[whKey] || 1;\r\n          const moveHandleRelative = createRelativeHandleMove(\r\n            getElementScroll(_scrollOffsetElement)[xyKey],\r\n            1 / axisScale\r\n          );\r\n          const pointerDownOffset = pointerDownEvent[clientXYKey];\r\n          const handleRect = getHandleRect();\r\n          const trackRect = getTrackRect();\r\n          const handleLength = handleRect[widthHeightKey];\r\n          const handleCenter = getHandleOffset(handleRect, trackRect) + handleLength / 2;\r\n          const relativeTrackPointerOffset = pointerDownOffset - trackRect[leftTopKey];\r\n          const startOffset = isDragScroll ? 0 : relativeTrackPointerOffset - handleCenter;\r\n          const releasePointerCapture = (pointerUpEvent: PointerEvent) => {\r\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\r\n            runEachAndClear(pointerupCleanupFns);\r\n            pointerCaptureElement.releasePointerCapture(pointerUpEvent.pointerId);\r\n          };\r\n          const nonAnimatedScroll = isDragScroll || instantClickScroll;\r\n          const revertScrollObscuringStyles = _removeScrollObscuringStyles();\r\n\r\n          const pointerupCleanupFns = [\r\n            addEventListener(_documentElm, releasePointerCaptureEvents, releasePointerCapture),\r\n            addEventListener(_documentElm, 'selectstart', (event: Event) => preventDefault(event), {\r\n              _passive: false,\r\n            }),\r\n            addEventListener(_track, releasePointerCaptureEvents, releasePointerCapture),\r\n            nonAnimatedScroll &&\r\n              addEventListener(_track, 'pointermove', (pointerMoveEvent: PointerEvent) =>\r\n                moveHandleRelative(\r\n                  startOffset + (pointerMoveEvent[clientXYKey] - pointerDownOffset)\r\n                )\r\n              ),\r\n            nonAnimatedScroll &&\r\n              (() => {\r\n                const withoutSnapScrollOffset = getElementScroll(_scrollOffsetElement);\r\n                revertScrollObscuringStyles();\r\n                const withSnapScrollOffset = getElementScroll(_scrollOffsetElement);\r\n                const snapScrollDiff = {\r\n                  x: withSnapScrollOffset.x - withoutSnapScrollOffset.x,\r\n                  y: withSnapScrollOffset.y - withoutSnapScrollOffset.y,\r\n                };\r\n\r\n                if (mathAbs(snapScrollDiff.x) > 3 || mathAbs(snapScrollDiff.y) > 3) {\r\n                  _removeScrollObscuringStyles();\r\n                  scrollElementTo(_scrollOffsetElement, withoutSnapScrollOffset);\r\n                  scrollOffsetElementScrollBy(snapScrollDiff);\r\n                  scrollSnapScrollTransitionTimeout(revertScrollObscuringStyles);\r\n                }\r\n              }),\r\n          ];\r\n\r\n          pointerCaptureElement.setPointerCapture(pointerDownEvent.pointerId);\r\n\r\n          if (instantClickScroll) {\r\n            moveHandleRelative(startOffset);\r\n          } else if (!isDragScroll) {\r\n            const animateClickScroll = getStaticPluginModuleInstance<typeof ClickScrollPlugin>(\r\n              clickScrollPluginModuleName\r\n            );\r\n            if (animateClickScroll) {\r\n              const stopClickScrollAnimation = animateClickScroll(\r\n                moveHandleRelative,\r\n                startOffset,\r\n                handleLength,\r\n                (stopped) => {\r\n                  // if the scroll animation doesn't continue with a press\r\n                  if (stopped) {\r\n                    revertScrollObscuringStyles();\r\n                  } else {\r\n                    push(pointerupCleanupFns, revertScrollObscuringStyles);\r\n                  }\r\n                }\r\n              );\r\n\r\n              push(pointerupCleanupFns, stopClickScrollAnimation);\r\n              push(pointerdownCleanupFns, bind(stopClickScrollAnimation, true));\r\n            }\r\n          }\r\n        }\r\n      });\r\n    };\r\n\r\n    let wheelScrollBy = true;\r\n\r\n    return bind(runEachAndClear, [\r\n      addEventListener(_handle, 'pointermove pointerleave', scrollbarHandlePointerInteraction),\r\n      addEventListener(_scrollbar, 'pointerenter', () => {\r\n        scrollbarsAddRemoveClass(classNameScrollbarInteraction, true);\r\n      }),\r\n      addEventListener(_scrollbar, 'pointerleave pointercancel', () => {\r\n        scrollbarsAddRemoveClass(classNameScrollbarInteraction, false);\r\n      }),\r\n      // focus viewport when clicking on a scrollbar (mouse only)\r\n      !_viewportIsTarget &&\r\n        addEventListener(_scrollbar, 'mousedown', () => {\r\n          const focusedElement = getFocusedElement();\r\n          if (\r\n            hasAttr(focusedElement, dataAttributeViewport) ||\r\n            hasAttr(focusedElement, dataAttributeHost) ||\r\n            focusedElement === document.body\r\n          ) {\r\n            setT(bind(focusElement, _viewport), 25);\r\n          }\r\n        }),\r\n      // propagate wheel events to viewport when mouse is over scrollbar\r\n      addEventListener(\r\n        _scrollbar,\r\n        'wheel',\r\n        (wheelEvent: WheelEvent) => {\r\n          const { deltaX, deltaY, deltaMode } = wheelEvent;\r\n\r\n          // the first wheel event is swallowed, simulate scroll to compensate for it\r\n          if (wheelScrollBy && deltaMode === 0 && parent(_scrollbar) === _host) {\r\n            scrollOffsetElementScrollBy({\r\n              x: deltaX,\r\n              y: deltaY,\r\n            });\r\n          }\r\n\r\n          wheelScrollBy = false;\r\n          scrollbarsAddRemoveClass(classNameScrollbarWheel, true);\r\n          wheelTimeout(() => {\r\n            wheelScrollBy = true;\r\n            scrollbarsAddRemoveClass(classNameScrollbarWheel);\r\n          });\r\n\r\n          preventDefault(wheelEvent);\r\n        },\r\n        { _passive: false, _capture: true }\r\n      ),\r\n      // solve problem of interaction causing click events\r\n      addEventListener(\r\n        _scrollbar,\r\n        'pointerdown',\r\n        // stopPropagation for stopping event propagation (causing click listeners to be invoked)\r\n        // preventDefault to prevent the pointer to cause any actions (e.g. releasing mouse button over an <a> tag causes an navigation)\r\n        bind(addEventListener, _documentElm, 'click', stopAndPrevent, {\r\n          _once: true,\r\n          _capture: true,\r\n          _passive: false,\r\n        }),\r\n        { _capture: true }\r\n      ),\r\n      createInteractiveScrollEvents(),\r\n      clearWheelTimeout,\r\n      clearScrollSnapScrollTransitionTimeout,\r\n    ]);\r\n  };\r\n};\r\n", "import type { OverflowBehavior, ReadonlyOptions } from '../../options';\r\nimport type { ScrollbarsSetupElementsObj } from './scrollbarsSetup.elements';\r\nimport type {\r\n  ObserversSetupState,\r\n  ObserversSetupUpdateHints,\r\n  Setup,\r\n  SetupUpdateInfo,\r\n  StructureSetupState,\r\n  StructureSetupUpdateHints,\r\n} from '../../setups';\r\nimport type { InitializationTarget } from '../../initialization';\r\nimport type { OverflowStyle } from '../../typings';\r\nimport type { StructureSetupElementsObj } from '../structureSetup/structureSetup.elements';\r\nimport {\r\n  classNameScrollbarThemeNone,\r\n  classNameScrollbarVisible,\r\n  classNameScrollbarUnusable,\r\n  classNameScrollbarCornerless,\r\n  classNameScrollbarAutoHideHidden,\r\n  classNameScrollbarHandleInteractive,\r\n  classNameScrollbarTrackInteractive,\r\n  classNameScrollbarRtl,\r\n  classNameScrollbarAutoHide,\r\n} from '../../classnames';\r\nimport { getEnvironment } from '../../environment';\r\nimport {\r\n  bind,\r\n  noop,\r\n  addEventListener,\r\n  push,\r\n  runEachAndClear,\r\n  selfClearTimeout,\r\n  strScroll,\r\n  strVisible,\r\n} from '../../support';\r\nimport { createScrollbarsSetupElements } from './scrollbarsSetup.elements';\r\nimport { createScrollbarsSetupEvents } from './scrollbarsSetup.events';\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\r\nexport interface ScrollbarsSetupState {}\r\n\r\nexport interface ScrollbarsSetupUpdateInfo extends SetupUpdateInfo {\r\n  _observersUpdateHints?: ObserversSetupUpdateHints;\r\n  _structureUpdateHints?: StructureSetupUpdateHints;\r\n}\r\n\r\nexport type ScrollbarsSetup = [\r\n  ...Setup<ScrollbarsSetupUpdateInfo, ScrollbarsSetupState, void>,\r\n  /** The elements created by the scrollbars setup. */\r\n  ScrollbarsSetupElementsObj,\r\n];\r\n\r\nexport const createScrollbarsSetup = (\r\n  target: InitializationTarget,\r\n  options: ReadonlyOptions,\r\n  observersSetupState: ObserversSetupState,\r\n  structureSetupState: StructureSetupState,\r\n  structureSetupElements: StructureSetupElementsObj,\r\n  onScroll: (event: Event) => void\r\n): ScrollbarsSetup => {\r\n  let mouseInHost: boolean | undefined;\r\n  let autoHideIsMove: boolean | undefined;\r\n  let autoHideIsLeave: boolean | undefined;\r\n  let autoHideIsNever: boolean | undefined;\r\n  let prevTheme: string | null | undefined;\r\n  let instanceAutoHideSuspendScrollDestroyFn = noop;\r\n  let instanceAutoHideDelay = 0;\r\n  const hoverablePointerTypes = ['mouse', 'pen'];\r\n\r\n  // needed to not fire unnecessary operations for pointer events on ios safari which will cause side effects: https://github.com/KingSora/OverlayScrollbars/issues/560\r\n  const isHoverablePointerType = (event: PointerEvent) =>\r\n    hoverablePointerTypes.includes(event.pointerType);\r\n\r\n  const [requestScrollAnimationFrame, cancelScrollAnimationFrame] = selfClearTimeout();\r\n  const [autoHideInstantInteractionTimeout, clearAutoHideInstantInteractionTimeout] =\r\n    selfClearTimeout(100);\r\n  const [autoHideSuspendTimeout, clearAutoHideSuspendTimeout] = selfClearTimeout(100);\r\n  const [auotHideTimeout, clearAutoHideTimeout] = selfClearTimeout(() => instanceAutoHideDelay);\r\n  const [elements, appendElements] = createScrollbarsSetupElements(\r\n    target,\r\n    structureSetupElements,\r\n    structureSetupState,\r\n    createScrollbarsSetupEvents(\r\n      options,\r\n      structureSetupElements,\r\n      structureSetupState,\r\n      (event) =>\r\n        isHoverablePointerType(event) &&\r\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\r\n        manageScrollbarsAutoHideInstantInteraction()\r\n    )\r\n  );\r\n  const { _host, _scrollEventElement, _isBody } = structureSetupElements;\r\n  const {\r\n    _scrollbarsAddRemoveClass,\r\n    _refreshScrollbarsHandleLength,\r\n    _refreshScrollbarsHandleOffset,\r\n    _refreshScrollbarsScrollCoordinates,\r\n    _refreshScrollbarsScrollbarOffset,\r\n  } = elements;\r\n  const manageScrollbarsAutoHide = (removeAutoHide: boolean, delayless?: boolean) => {\r\n    clearAutoHideTimeout();\r\n    if (removeAutoHide) {\r\n      _scrollbarsAddRemoveClass(classNameScrollbarAutoHideHidden);\r\n    } else {\r\n      const hide = bind(_scrollbarsAddRemoveClass, classNameScrollbarAutoHideHidden, true);\r\n      if (instanceAutoHideDelay > 0 && !delayless) {\r\n        auotHideTimeout(hide);\r\n      } else {\r\n        hide();\r\n      }\r\n    }\r\n  };\r\n  const manageScrollbarsAutoHideInstantInteraction = () => {\r\n    if (autoHideIsLeave ? !mouseInHost : !autoHideIsNever) {\r\n      manageScrollbarsAutoHide(true);\r\n      autoHideInstantInteractionTimeout(() => {\r\n        manageScrollbarsAutoHide(false);\r\n      });\r\n    }\r\n  };\r\n  const manageAutoHideSuspension = (add: boolean) => {\r\n    _scrollbarsAddRemoveClass(classNameScrollbarAutoHide, add, true);\r\n    _scrollbarsAddRemoveClass(classNameScrollbarAutoHide, add, false);\r\n  };\r\n  const onHostMouseEnter = (event: PointerEvent) => {\r\n    if (isHoverablePointerType(event)) {\r\n      mouseInHost = autoHideIsLeave;\r\n      autoHideIsLeave && manageScrollbarsAutoHide(true);\r\n    }\r\n  };\r\n  const destroyFns: (() => void)[] = [\r\n    clearAutoHideTimeout,\r\n    clearAutoHideInstantInteractionTimeout,\r\n    clearAutoHideSuspendTimeout,\r\n    cancelScrollAnimationFrame,\r\n    () => instanceAutoHideSuspendScrollDestroyFn(),\r\n\r\n    addEventListener(_host, 'pointerover', onHostMouseEnter, { _once: true }),\r\n    addEventListener(_host, 'pointerenter', onHostMouseEnter),\r\n    addEventListener(_host, 'pointerleave', (event: PointerEvent) => {\r\n      if (isHoverablePointerType(event)) {\r\n        mouseInHost = false;\r\n        autoHideIsLeave && manageScrollbarsAutoHide(false);\r\n      }\r\n    }),\r\n    addEventListener(_host, 'pointermove', (event: PointerEvent) => {\r\n      isHoverablePointerType(event) &&\r\n        autoHideIsMove &&\r\n        manageScrollbarsAutoHideInstantInteraction();\r\n    }),\r\n    addEventListener(_scrollEventElement, 'scroll', (event) => {\r\n      requestScrollAnimationFrame(() => {\r\n        _refreshScrollbarsHandleOffset();\r\n        manageScrollbarsAutoHideInstantInteraction();\r\n      });\r\n\r\n      onScroll(event);\r\n\r\n      _refreshScrollbarsScrollbarOffset();\r\n    }),\r\n  ];\r\n\r\n  return [\r\n    () => bind(runEachAndClear, push(destroyFns, appendElements())),\r\n    ({ _checkOption, _force, _observersUpdateHints, _structureUpdateHints }) => {\r\n      const {\r\n        _overflowEdgeChanged,\r\n        _overflowAmountChanged,\r\n        _overflowStyleChanged,\r\n        _scrollCoordinatesChanged,\r\n      } = _structureUpdateHints || {};\r\n      const { _directionChanged, _appear } = _observersUpdateHints || {};\r\n      const { _directionIsRTL } = observersSetupState;\r\n      const { _nativeScrollbarsOverlaid } = getEnvironment();\r\n      const { _overflowStyle, _hasOverflow } = structureSetupState;\r\n      const [showNativeOverlaidScrollbarsOption, showNativeOverlaidScrollbarsChanged] =\r\n        _checkOption('showNativeOverlaidScrollbars');\r\n      const [theme, themeChanged] = _checkOption('scrollbars.theme');\r\n      const [visibility, visibilityChanged] = _checkOption('scrollbars.visibility');\r\n      const [autoHide, autoHideChanged] = _checkOption('scrollbars.autoHide');\r\n      const [autoHideSuspend, autoHideSuspendChanged] = _checkOption('scrollbars.autoHideSuspend');\r\n      const [autoHideDelay] = _checkOption('scrollbars.autoHideDelay');\r\n      const [dragScroll, dragScrollChanged] = _checkOption('scrollbars.dragScroll');\r\n      const [clickScroll, clickScrollChanged] = _checkOption('scrollbars.clickScroll');\r\n      const [overflow, overflowChanged] = _checkOption('overflow');\r\n      const trulyAppeared = _appear && !_force;\r\n      const hasOverflow = _hasOverflow.x || _hasOverflow.y;\r\n      const updateScrollbars =\r\n        _overflowEdgeChanged ||\r\n        _overflowAmountChanged ||\r\n        _scrollCoordinatesChanged ||\r\n        _directionChanged ||\r\n        _force;\r\n      const updateVisibility = _overflowStyleChanged || visibilityChanged || overflowChanged;\r\n      const showNativeOverlaidScrollbars =\r\n        showNativeOverlaidScrollbarsOption &&\r\n        _nativeScrollbarsOverlaid.x &&\r\n        _nativeScrollbarsOverlaid.y;\r\n\r\n      const setScrollbarVisibility = (\r\n        overflowBehavior: OverflowBehavior,\r\n        overflowStyle: OverflowStyle,\r\n        isHorizontal: boolean\r\n      ) => {\r\n        const isVisible =\r\n          overflowBehavior.includes(strScroll) &&\r\n          (visibility === strVisible || (visibility === 'auto' && overflowStyle === strScroll));\r\n\r\n        _scrollbarsAddRemoveClass(classNameScrollbarVisible, isVisible, isHorizontal);\r\n\r\n        return isVisible;\r\n      };\r\n\r\n      instanceAutoHideDelay = autoHideDelay;\r\n\r\n      if (trulyAppeared) {\r\n        if (autoHideSuspend && hasOverflow) {\r\n          manageAutoHideSuspension(false);\r\n          instanceAutoHideSuspendScrollDestroyFn();\r\n          autoHideSuspendTimeout(() => {\r\n            instanceAutoHideSuspendScrollDestroyFn = addEventListener(\r\n              _scrollEventElement,\r\n              'scroll',\r\n              bind(manageAutoHideSuspension, true),\r\n              {\r\n                _once: true,\r\n              }\r\n            );\r\n          });\r\n        } else {\r\n          manageAutoHideSuspension(true);\r\n        }\r\n      }\r\n\r\n      if (showNativeOverlaidScrollbarsChanged) {\r\n        _scrollbarsAddRemoveClass(classNameScrollbarThemeNone, showNativeOverlaidScrollbars);\r\n      }\r\n\r\n      if (themeChanged) {\r\n        _scrollbarsAddRemoveClass(prevTheme);\r\n        _scrollbarsAddRemoveClass(theme, true);\r\n\r\n        prevTheme = theme;\r\n      }\r\n\r\n      if (autoHideSuspendChanged && !autoHideSuspend) {\r\n        manageAutoHideSuspension(true);\r\n      }\r\n\r\n      if (autoHideChanged) {\r\n        autoHideIsMove = autoHide === 'move';\r\n        autoHideIsLeave = autoHide === 'leave';\r\n        autoHideIsNever = autoHide === 'never';\r\n        manageScrollbarsAutoHide(autoHideIsNever, true);\r\n      }\r\n\r\n      if (dragScrollChanged) {\r\n        _scrollbarsAddRemoveClass(classNameScrollbarHandleInteractive, dragScroll);\r\n      }\r\n\r\n      if (clickScrollChanged) {\r\n        _scrollbarsAddRemoveClass(classNameScrollbarTrackInteractive, !!clickScroll);\r\n      }\r\n\r\n      // always update scrollbar visibility before scrollbar size\r\n      // the scrollbar size is influenced whether both or just one scrollbar is visible (because of the corner element)\r\n      if (updateVisibility) {\r\n        const xVisible = setScrollbarVisibility(overflow.x, _overflowStyle.x, true);\r\n        const yVisible = setScrollbarVisibility(overflow.y, _overflowStyle.y, false);\r\n        const hasCorner = xVisible && yVisible;\r\n\r\n        _scrollbarsAddRemoveClass(classNameScrollbarCornerless, !hasCorner);\r\n      }\r\n\r\n      // always update scrollbar sizes after the visibility\r\n      if (updateScrollbars) {\r\n        _refreshScrollbarsHandleOffset();\r\n        _refreshScrollbarsHandleLength();\r\n        _refreshScrollbarsScrollbarOffset();\r\n        _scrollCoordinatesChanged && _refreshScrollbarsScrollCoordinates();\r\n\r\n        _scrollbarsAddRemoveClass(classNameScrollbarUnusable, !_hasOverflow.x, true);\r\n        _scrollbarsAddRemoveClass(classNameScrollbarUnusable, !_hasOverflow.y, false);\r\n        _scrollbarsAddRemoveClass(classNameScrollbarRtl, _directionIsRTL && !_isBody);\r\n      }\r\n    },\r\n    {},\r\n    elements,\r\n  ];\r\n};\r\n", "import type {\r\n  InitializationTarget,\r\n  InitializationTargetElement,\r\n  InitializationTargetObject,\r\n} from '../../initialization';\r\nimport {\r\n  isHTMLElement,\r\n  appendChildren,\r\n  createDiv,\r\n  contents,\r\n  parent,\r\n  removeElements,\r\n  push,\r\n  runEachAndClear,\r\n  removeAttrs,\r\n  hasAttrClass,\r\n  addEventListener,\r\n  bind,\r\n  inArray,\r\n  addAttrClass,\r\n  addRemoveAttrClass,\r\n  setAttrs,\r\n  getAttr,\r\n  isBodyElement,\r\n  getFocusedElement,\r\n  wnd,\r\n  focusElement,\r\n  stopAndPrevent,\r\n  getOffsetSize,\r\n  getScrollSize,\r\n  getStyles,\r\n  strOverflowX,\r\n  strOverflowY,\r\n} from '../../support';\r\nimport {\r\n  dataAttributeHost,\r\n  dataAttributeInitialize,\r\n  dataAttributeViewport,\r\n  dataValueViewportScrollbarHidden,\r\n  dataAttributePadding,\r\n  dataAttributeContent,\r\n  dataAttributeHtmlBody,\r\n  dataValueHostIsHost,\r\n  dataValueViewportScrolling,\r\n} from '../../classnames';\r\nimport { getEnvironment } from '../../environment';\r\nimport {\r\n  staticInitializationElement as generalStaticInitializationElement,\r\n  dynamicInitializationElement as generalDynamicInitializationElement,\r\n} from '../../initialization';\r\nimport { overflowIsVisible } from './structureSetup.utils';\r\n\r\nexport type StructureSetupElements = [\r\n  elements: StructureSetupElementsObj,\r\n  appendElements: () => () => void,\r\n  canceled: () => void,\r\n];\r\n\r\nexport interface StructureSetupElementsObj {\r\n  _target: InitializationTargetElement;\r\n  _host: HTMLElement;\r\n  _viewport: HTMLElement;\r\n  _padding: HTMLElement | false;\r\n  _content: HTMLElement | false;\r\n  _scrollOffsetElement: HTMLElement;\r\n  _scrollEventElement: HTMLElement | Document;\r\n  _originalScrollOffsetElement: HTMLElement;\r\n  // ctx ----\r\n  _isBody: boolean;\r\n  _documentElm: Document;\r\n  _targetIsElm: boolean;\r\n  _viewportIsTarget: boolean;\r\n  _windowElm: () => Window;\r\n  _viewportHasClass: (viewportAttributeClassName: string) => boolean;\r\n  _viewportAddRemoveClass: (viewportAttributeClassName: string, add?: boolean) => () => void;\r\n  _removeScrollObscuringStyles: () => () => void;\r\n}\r\n\r\nexport const createStructureSetupElements = (\r\n  target: InitializationTarget\r\n): StructureSetupElements => {\r\n  const env = getEnvironment();\r\n  const { _getDefaultInitialization, _nativeScrollbarsHiding } = env;\r\n  const { elements: defaultInitElements } = _getDefaultInitialization();\r\n  const {\r\n    padding: defaultPaddingInitialization,\r\n    viewport: defaultViewportInitialization,\r\n    content: defaultContentInitialization,\r\n  } = defaultInitElements;\r\n  const targetIsElm = isHTMLElement(target);\r\n  const targetStructureInitialization = (targetIsElm ? {} : target) as InitializationTargetObject;\r\n  const { elements: initElements } = targetStructureInitialization;\r\n  const {\r\n    padding: paddingInitialization,\r\n    viewport: viewportInitialization,\r\n    content: contentInitialization,\r\n  } = initElements || {};\r\n\r\n  const targetElement = targetIsElm ? target : targetStructureInitialization.target;\r\n  const isBody = isBodyElement(targetElement);\r\n  const ownerDocument = targetElement.ownerDocument;\r\n  const docElement = ownerDocument.documentElement;\r\n  const getDocumentWindow = () => ownerDocument.defaultView || wnd;\r\n  const staticInitializationElement = bind(generalStaticInitializationElement, [targetElement]);\r\n  const dynamicInitializationElement = bind(generalDynamicInitializationElement, [targetElement]);\r\n  const createNewDiv = bind(createDiv, '');\r\n  const generateViewportElement = bind(\r\n    staticInitializationElement,\r\n    createNewDiv,\r\n    defaultViewportInitialization\r\n  );\r\n  const generateContentElement = bind(\r\n    dynamicInitializationElement,\r\n    createNewDiv,\r\n    defaultContentInitialization\r\n  );\r\n  const elementHasOverflow = (elm: HTMLElement) => {\r\n    const offsetSize = getOffsetSize(elm);\r\n    const scrollSize = getScrollSize(elm);\r\n    const overflowX = getStyles(elm, strOverflowX);\r\n    const overflowY = getStyles(elm, strOverflowY);\r\n\r\n    return (\r\n      (scrollSize.w - offsetSize.w > 0 && !overflowIsVisible(overflowX)) ||\r\n      (scrollSize.h - offsetSize.h > 0 && !overflowIsVisible(overflowY))\r\n    );\r\n  };\r\n  const possibleViewportElement = generateViewportElement(viewportInitialization);\r\n  const viewportIsTarget = possibleViewportElement === targetElement;\r\n  const viewportIsTargetBody = viewportIsTarget && isBody;\r\n  const possibleContentElement = !viewportIsTarget && generateContentElement(contentInitialization);\r\n  // edge case if passed viewportElement is contentElement:\r\n  // viewport element has higher priority and content element will not be generated\r\n  // will act the same way as initialization: `{ elements: { viewport, content: false } }`\r\n  const viewportIsContent = !viewportIsTarget && possibleViewportElement === possibleContentElement;\r\n  const viewportElement = viewportIsTargetBody ? docElement : possibleViewportElement;\r\n  const hostElement = viewportIsTargetBody ? viewportElement : targetElement;\r\n  const paddingElement =\r\n    !viewportIsTarget &&\r\n    dynamicInitializationElement(createNewDiv, defaultPaddingInitialization, paddingInitialization);\r\n  const contentElement = !viewportIsContent && possibleContentElement;\r\n  const generatedElements = [contentElement, viewportElement, paddingElement, hostElement].map(\r\n    (elm) => isHTMLElement(elm) && !parent(elm) && elm\r\n  );\r\n  const elementIsGenerated = (elm: HTMLElement | false) => elm && inArray(generatedElements, elm);\r\n  const originalNonBodyScrollOffsetElement =\r\n    !elementIsGenerated(viewportElement) && elementHasOverflow(viewportElement)\r\n      ? viewportElement\r\n      : targetElement;\r\n  const scrollOffsetElement = viewportIsTargetBody ? docElement : viewportElement;\r\n  const scrollEventElement = viewportIsTargetBody ? ownerDocument : viewportElement;\r\n\r\n  const evaluatedTargetObj: StructureSetupElementsObj = {\r\n    _target: targetElement,\r\n    _host: hostElement,\r\n    _viewport: viewportElement,\r\n    _padding: paddingElement,\r\n    _content: contentElement,\r\n    _scrollOffsetElement: scrollOffsetElement,\r\n    _scrollEventElement: scrollEventElement,\r\n    _originalScrollOffsetElement: isBody ? docElement : originalNonBodyScrollOffsetElement,\r\n    _documentElm: ownerDocument,\r\n    _isBody: isBody,\r\n    _targetIsElm: targetIsElm,\r\n    _viewportIsTarget: viewportIsTarget,\r\n    _windowElm: getDocumentWindow,\r\n    _viewportHasClass: (viewportAttributeClassName: string) =>\r\n      hasAttrClass(viewportElement, dataAttributeViewport, viewportAttributeClassName),\r\n    _viewportAddRemoveClass: (viewportAttributeClassName: string, add?: boolean) =>\r\n      addRemoveAttrClass(viewportElement, dataAttributeViewport, viewportAttributeClassName, add),\r\n    _removeScrollObscuringStyles: () =>\r\n      addRemoveAttrClass(\r\n        scrollOffsetElement,\r\n        dataAttributeViewport,\r\n        dataValueViewportScrolling,\r\n        true\r\n      ),\r\n  };\r\n  const { _target, _host, _padding, _viewport, _content } = evaluatedTargetObj;\r\n  const destroyFns: (() => any)[] = [\r\n    () => {\r\n      // always remove dataAttributeHost & dataAttributeInitialize from host and from <html> element if target is body\r\n      removeAttrs(_host, [dataAttributeHost, dataAttributeInitialize]);\r\n      removeAttrs(_target, dataAttributeInitialize);\r\n      if (isBody) {\r\n        removeAttrs(docElement, [dataAttributeInitialize, dataAttributeHost]);\r\n      }\r\n    },\r\n  ];\r\n  let targetContents = contents(\r\n    [_content, _viewport, _padding, _host, _target].find((elm) => elm && !elementIsGenerated(elm))\r\n  );\r\n  const contentSlot = viewportIsTargetBody ? _target : _content || _viewport;\r\n  const destroy = bind(runEachAndClear, destroyFns);\r\n  const appendElements = () => {\r\n    const docWnd = getDocumentWindow();\r\n    const initActiveElm = getFocusedElement();\r\n    const unwrap = (elm: HTMLElement | false | null | undefined) => {\r\n      appendChildren(parent(elm), contents(elm));\r\n      removeElements(elm);\r\n    };\r\n    // wrapping / unwrapping will cause the focused element to blur, this should prevent those events to surface\r\n    const prepareWrapUnwrapFocus = (activeElement: Element | false | null | undefined) =>\r\n      addEventListener(activeElement, 'focusin focusout focus blur', stopAndPrevent, {\r\n        _capture: true,\r\n        _passive: false,\r\n      });\r\n    const tabIndexStr = 'tabindex';\r\n    const originalViewportTabIndex = getAttr(_viewport, tabIndexStr);\r\n    const undoInitWrapUndwrapFocus = prepareWrapUnwrapFocus(initActiveElm);\r\n    setAttrs(_host, dataAttributeHost, viewportIsTarget ? '' : dataValueHostIsHost);\r\n    setAttrs(_padding, dataAttributePadding, '');\r\n    setAttrs(_viewport, dataAttributeViewport, '');\r\n    setAttrs(_content, dataAttributeContent, '');\r\n\r\n    if (!viewportIsTarget) {\r\n      setAttrs(_viewport, tabIndexStr, originalViewportTabIndex || '-1');\r\n      isBody && setAttrs(docElement, dataAttributeHtmlBody, '');\r\n    }\r\n\r\n    appendChildren(contentSlot, targetContents);\r\n    appendChildren(_host, _padding);\r\n    appendChildren(_padding || _host, !viewportIsTarget && _viewport);\r\n    appendChildren(_viewport, _content);\r\n\r\n    push(destroyFns, [\r\n      undoInitWrapUndwrapFocus,\r\n      () => {\r\n        const destroyActiveElm = getFocusedElement();\r\n        const viewportIsGenerated = elementIsGenerated(_viewport);\r\n        // if the focused element is viewport and viewport will be destroyed shift the focus to target\r\n        // otherwise keep the focused element\r\n        const destroyFocusElement =\r\n          viewportIsGenerated && destroyActiveElm === _viewport ? _target : destroyActiveElm;\r\n        const undoDestroyWrapUndwrapFocus = prepareWrapUnwrapFocus(destroyFocusElement);\r\n        removeAttrs(_padding, dataAttributePadding);\r\n        removeAttrs(_content, dataAttributeContent);\r\n        removeAttrs(_viewport, dataAttributeViewport);\r\n        isBody && removeAttrs(docElement, dataAttributeHtmlBody);\r\n        originalViewportTabIndex\r\n          ? setAttrs(_viewport, tabIndexStr, originalViewportTabIndex)\r\n          : removeAttrs(_viewport, tabIndexStr);\r\n\r\n        elementIsGenerated(_content) && unwrap(_content);\r\n        viewportIsGenerated && unwrap(_viewport);\r\n        elementIsGenerated(_padding) && unwrap(_padding);\r\n        focusElement(destroyFocusElement);\r\n        undoDestroyWrapUndwrapFocus();\r\n      },\r\n    ]);\r\n\r\n    if (_nativeScrollbarsHiding && !viewportIsTarget) {\r\n      addAttrClass(_viewport, dataAttributeViewport, dataValueViewportScrollbarHidden);\r\n      push(destroyFns, bind(removeAttrs, _viewport, dataAttributeViewport));\r\n    }\r\n\r\n    // keep the original focused element focused except when\r\n    // the target is body and viewport is not target, then shift the focus to the viewport element\r\n    focusElement(\r\n      !viewportIsTarget && isBody && initActiveElm === _target && docWnd.top === docWnd\r\n        ? _viewport\r\n        : initActiveElm\r\n    );\r\n    undoInitWrapUndwrapFocus();\r\n\r\n    // @ts-ignore\r\n    targetContents = 0;\r\n\r\n    return destroy;\r\n  };\r\n\r\n  return [evaluatedTargetObj, appendElements, destroy];\r\n};\r\n", "import type { CreateStructureUpdateSegment } from '../structureSetup';\r\nimport { setStyles, strHeight } from '../../../support';\r\n\r\n/**\r\n * Lifecycle with the responsibility to adjust the trinsic behavior of the content element.\r\n * @param structureUpdateHub\r\n * @returns\r\n */\r\nexport const createTrinsicUpdateSegment: CreateStructureUpdateSegment =\r\n  ({ _content }) =>\r\n  ({ _observersUpdateHints, _observersState, _force }) => {\r\n    const { _heightIntrinsicChanged } = _observersUpdateHints || {};\r\n    const { _heightIntrinsic } = _observersState;\r\n    const heightIntrinsicChanged = _content && (_heightIntrinsicChanged || _force);\r\n\r\n    if (heightIntrinsicChanged) {\r\n      setStyles(_content, {\r\n        [strHeight]: _heightIntrinsic && '100%',\r\n      });\r\n    }\r\n  };\r\n", "import type { StyleObject } from '../../../typings';\r\nimport type { CreateStructureUpdateSegment } from '../structureSetup';\r\nimport {\r\n  createCache,\r\n  topRightBottomLeft,\r\n  equalTRBL,\r\n  assignDeep,\r\n  bind,\r\n  strMarginBottom,\r\n  strMarginLeft,\r\n  strMarginRight,\r\n  strPaddingBottom,\r\n  strPaddingLeft,\r\n  strPaddingRight,\r\n  strPaddingTop,\r\n  strWidth,\r\n  setStyles,\r\n} from '../../../support';\r\nimport { getEnvironment } from '../../../environment';\r\n\r\n/**\r\n * Lifecycle with the responsibility to adjust the padding styling of the padding and viewport element.\r\n * @param structureUpdateHub\r\n * @returns\r\n */\r\nexport const createPaddingUpdateSegment: CreateStructureUpdateSegment = (\r\n  { _host, _padding, _viewport, _viewportIsTarget },\r\n  state\r\n) => {\r\n  const [updatePaddingCache, currentPaddingCache] = createCache(\r\n    {\r\n      _equal: equalTRBL,\r\n      _initialValue: topRightBottomLeft(),\r\n    },\r\n    bind(topRightBottomLeft, _host, 'padding', '')\r\n  );\r\n\r\n  return ({ _checkOption, _observersUpdateHints, _observersState, _force }) => {\r\n    let [padding, paddingChanged] = currentPaddingCache(_force);\r\n    const { _nativeScrollbarsHiding } = getEnvironment();\r\n    const { _sizeChanged, _contentMutation, _directionChanged } = _observersUpdateHints || {};\r\n    const { _directionIsRTL } = _observersState;\r\n    const [paddingAbsolute, paddingAbsoluteChanged] = _checkOption('paddingAbsolute');\r\n    const contentMutation = _force || _contentMutation;\r\n\r\n    if (_sizeChanged || paddingChanged || contentMutation) {\r\n      [padding, paddingChanged] = updatePaddingCache(_force);\r\n    }\r\n\r\n    const paddingStyleChanged =\r\n      !_viewportIsTarget && (paddingAbsoluteChanged || _directionChanged || paddingChanged);\r\n\r\n    if (paddingStyleChanged) {\r\n      // if there is no padding element and no scrollbar styling, paddingAbsolute isn't supported\r\n      const paddingRelative = !paddingAbsolute || (!_padding && !_nativeScrollbarsHiding);\r\n      const paddingHorizontal = padding.r + padding.l;\r\n      const paddingVertical = padding.t + padding.b;\r\n\r\n      const paddingStyle: StyleObject = {\r\n        [strMarginRight]: paddingRelative && !_directionIsRTL ? -paddingHorizontal : 0,\r\n        [strMarginBottom]: paddingRelative ? -paddingVertical : 0,\r\n        [strMarginLeft]: paddingRelative && _directionIsRTL ? -paddingHorizontal : 0,\r\n        top: paddingRelative ? -padding.t : 0,\r\n        right: paddingRelative ? (_directionIsRTL ? -padding.r : 'auto') : 0,\r\n        left: paddingRelative ? (_directionIsRTL ? 'auto' : -padding.l) : 0,\r\n        [strWidth]: paddingRelative && `calc(100% + ${paddingHorizontal}px)`,\r\n      };\r\n      const viewportStyle: StyleObject = {\r\n        [strPaddingTop]: paddingRelative ? padding.t : 0,\r\n        [strPaddingRight]: paddingRelative ? padding.r : 0,\r\n        [strPaddingBottom]: paddingRelative ? padding.b : 0,\r\n        [strPaddingLeft]: paddingRelative ? padding.l : 0,\r\n      };\r\n\r\n      // if there is no padding element apply the style to the viewport element instead\r\n      setStyles(_padding || _viewport, paddingStyle);\r\n      setStyles(_viewport, viewportStyle);\r\n\r\n      assignDeep(state, {\r\n        _padding: padding,\r\n        _paddingAbsolute: !paddingRelative,\r\n        _viewportPaddingStyle: _padding\r\n          ? viewportStyle\r\n          : assignDeep({}, paddingStyle, viewportStyle),\r\n      });\r\n    }\r\n\r\n    return {\r\n      _paddingStyleChanged: paddingStyleChanged,\r\n    };\r\n  };\r\n};\r\n", "import type { ScrollCoordinates, WH, XY } from '../../../support';\r\nimport type { ScrollbarsHidingPlugin } from '../../../plugins/scrollbarsHidingPlugin';\r\nimport type { OverflowStyle } from '../../../typings';\r\nimport type { CreateStructureUpdateSegment } from '../structureSetup';\r\nimport {\r\n  createCache,\r\n  getScrollSize,\r\n  getFractionalSize,\r\n  equalWH,\r\n  getClientSize,\r\n  equalXY,\r\n  assignDeep,\r\n  bind,\r\n  wnd,\r\n  mathMax,\r\n  getWindowSize,\r\n  addRemoveAttrClass,\r\n  capitalizeFirstLetter,\r\n  setStyles,\r\n  strVisible,\r\n  strHidden,\r\n  keys,\r\n  strScroll,\r\n  scrollElementTo,\r\n  getElementScroll,\r\n  sanitizeScrollCoordinates,\r\n  getStyles,\r\n  equal,\r\n  getZeroScrollCoordinates,\r\n  hasDimensions,\r\n  addEventListener,\r\n  stopPropagation,\r\n  rAF,\r\n  hasAttrClass,\r\n} from '../../../support';\r\nimport { getEnvironment } from '../../../environment';\r\nimport {\r\n  dataAttributeHost,\r\n  dataValueNoClipping,\r\n  dataValueViewportScrollbarHidden,\r\n  dataAttributePadding,\r\n  dataValueViewportOverflowXPrefix,\r\n  dataValueViewportOverflowYPrefix,\r\n  dataValueViewportNoContent,\r\n  dataValueViewportMeasuring,\r\n} from '../../../classnames';\r\nimport { getStaticPluginModuleInstance, scrollbarsHidingPluginName } from '../../../plugins';\r\nimport {\r\n  createViewportOverflowState,\r\n  getShowNativeOverlaidScrollbars,\r\n  overflowIsVisible,\r\n} from '../structureSetup.utils';\r\n\r\ninterface FlowDirectionStyles {\r\n  display?: string;\r\n  direction?: string;\r\n  flexDirection?: string;\r\n  writingMode?: string;\r\n}\r\n\r\n/**\r\n * Lifecycle with the responsibility to set the correct overflow and scrollbar hiding styles of the viewport element.\r\n * @param structureUpdateHub\r\n * @returns\r\n */\r\nexport const createOverflowUpdateSegment: CreateStructureUpdateSegment = (\r\n  structureSetupElements,\r\n  structureSetupState\r\n) => {\r\n  const env = getEnvironment();\r\n  const {\r\n    _host,\r\n    _padding,\r\n    _viewport,\r\n    _viewportIsTarget,\r\n    _scrollEventElement,\r\n    _scrollOffsetElement,\r\n    _isBody,\r\n    _viewportAddRemoveClass,\r\n    _windowElm,\r\n  } = structureSetupElements;\r\n  const { _nativeScrollbarsHiding } = env;\r\n  const viewportIsTargetBody = _isBody && _viewportIsTarget;\r\n  const max0 = bind(mathMax, 0);\r\n  const flowDirectionCanBeNonDefaultMap: Record<\r\n    keyof FlowDirectionStyles,\r\n    (styleValue: string) => boolean\r\n  > = {\r\n    display: () => false,\r\n    direction: (directionStyle) => directionStyle !== 'ltr',\r\n    flexDirection: (flexDirectionStyle) => flexDirectionStyle.endsWith('-reverse'),\r\n    writingMode: (writingModeStyle) => writingModeStyle !== 'horizontal-tb',\r\n  };\r\n  const flowDirectionStyleArr = keys(flowDirectionCanBeNonDefaultMap) as Array<\r\n    keyof FlowDirectionStyles\r\n  >;\r\n  const whCacheOptions = {\r\n    _equal: equalWH,\r\n    _initialValue: { w: 0, h: 0 },\r\n  };\r\n  const partialXYOptions = {\r\n    _equal: equalXY,\r\n    _initialValue: {},\r\n  };\r\n\r\n  const setMeasuringMode = (active: boolean) => {\r\n    // viewportIsTargetBody never needs measuring\r\n    _viewportAddRemoveClass(dataValueViewportMeasuring, !viewportIsTargetBody && active);\r\n  };\r\n\r\n  const getMeasuredScrollCoordinates = (flowDirectionStyles: FlowDirectionStyles) => {\r\n    const flowDirectionCanBeNonDefault = flowDirectionStyleArr.some((styleName) => {\r\n      const styleValue = flowDirectionStyles[styleName];\r\n      return styleValue && flowDirectionCanBeNonDefaultMap[styleName](styleValue);\r\n    });\r\n\r\n    // if the direction can not be non-default return default scroll coordinates (only the sign of the numbers matters)\r\n    if (!flowDirectionCanBeNonDefault) {\r\n      return {\r\n        _start: { x: 0, y: 0 },\r\n        _end: { x: 1, y: 1 },\r\n      };\r\n    }\r\n\r\n    setMeasuringMode(true);\r\n\r\n    const originalScrollOffset = getElementScroll(_scrollOffsetElement);\r\n    const removeNoContent = _viewportAddRemoveClass(dataValueViewportNoContent, true);\r\n    const removeScrollBlock = addEventListener(\r\n      _scrollEventElement,\r\n      strScroll,\r\n      (event) => {\r\n        const scrollEventScrollOffset = getElementScroll(_scrollOffsetElement);\r\n        // if scroll offset didnt change\r\n        if (\r\n          event.isTrusted &&\r\n          scrollEventScrollOffset.x === originalScrollOffset.x &&\r\n          scrollEventScrollOffset.y === originalScrollOffset.y\r\n        ) {\r\n          stopPropagation(event);\r\n        }\r\n      },\r\n      {\r\n        _capture: true,\r\n        _once: true,\r\n      }\r\n    );\r\n\r\n    scrollElementTo(_scrollOffsetElement, {\r\n      x: 0,\r\n      y: 0,\r\n    });\r\n    removeNoContent();\r\n\r\n    const _start = getElementScroll(_scrollOffsetElement);\r\n    const scrollSize = getScrollSize(_scrollOffsetElement);\r\n    scrollElementTo(_scrollOffsetElement, {\r\n      x: scrollSize.w,\r\n      y: scrollSize.h,\r\n    });\r\n\r\n    const tmp = getElementScroll(_scrollOffsetElement);\r\n    scrollElementTo(_scrollOffsetElement, {\r\n      // if tmp is very close start there porbably wasn't any scroll happening so scroll again in different direction\r\n      x: tmp.x - _start.x < 1 && -scrollSize.w,\r\n      y: tmp.y - _start.y < 1 && -scrollSize.h,\r\n    });\r\n\r\n    const _end = getElementScroll(_scrollOffsetElement);\r\n    scrollElementTo(_scrollOffsetElement, originalScrollOffset);\r\n    rAF(() => removeScrollBlock());\r\n\r\n    return {\r\n      _start,\r\n      _end,\r\n    };\r\n  };\r\n  const getOverflowAmount = (\r\n    viewportScrollSize: WH<number>,\r\n    viewportClientSize: WH<number>\r\n  ): WH<number> => {\r\n    const tollerance = wnd.devicePixelRatio % 1 !== 0 ? 1 : 0;\r\n    const amount = {\r\n      w: max0(viewportScrollSize.w - viewportClientSize.w),\r\n      h: max0(viewportScrollSize.h - viewportClientSize.h),\r\n    };\r\n\r\n    return {\r\n      w: amount.w > tollerance ? amount.w : 0,\r\n      h: amount.h > tollerance ? amount.h : 0,\r\n    };\r\n  };\r\n  const [updateSizeFraction, getCurrentSizeFraction] = createCache<WH<number>>(\r\n    whCacheOptions,\r\n    bind(getFractionalSize, _viewport)\r\n  );\r\n  const [updateViewportScrollSizeCache, getCurrentViewportScrollSizeCache] = createCache<\r\n    WH<number>\r\n  >(whCacheOptions, bind(getScrollSize, _viewport));\r\n  const [updateOverflowAmountCache, getCurrentOverflowAmountCache] =\r\n    createCache<WH<number>>(whCacheOptions);\r\n  const [updateHasOverflowCache] = createCache<Partial<XY<boolean>>>(partialXYOptions);\r\n  const [updateOverflowEdge, getCurrentOverflowEdgeCache] = createCache<WH<number>>(whCacheOptions);\r\n  const [updateOverflowStyleCache] = createCache<Partial<XY<OverflowStyle>>>(partialXYOptions);\r\n  const [updateFlowDirectionStyles] = createCache<FlowDirectionStyles>(\r\n    {\r\n      _equal: (currVal, newValu) => equal(currVal, newValu, flowDirectionStyleArr),\r\n      _initialValue: {},\r\n    },\r\n    () => (hasDimensions(_viewport) ? getStyles(_viewport, flowDirectionStyleArr) : {})\r\n  );\r\n  const [updateMeasuredScrollCoordinates, getCurrentMeasuredScrollCoordinates] =\r\n    createCache<ScrollCoordinates>({\r\n      _equal: (currVal, newVal) =>\r\n        equalXY(currVal._start, newVal._start) && equalXY(currVal._end, newVal._end),\r\n      _initialValue: getZeroScrollCoordinates(),\r\n    });\r\n\r\n  const scrollbarsHidingPlugin = getStaticPluginModuleInstance<typeof ScrollbarsHidingPlugin>(\r\n    scrollbarsHidingPluginName\r\n  );\r\n\r\n  const createViewportOverflowStyleClassName = (\r\n    overflowStyle: OverflowStyle,\r\n    isHorizontal?: boolean\r\n  ) => {\r\n    const prefix = isHorizontal\r\n      ? dataValueViewportOverflowXPrefix\r\n      : dataValueViewportOverflowYPrefix;\r\n    return `${prefix}${capitalizeFirstLetter(overflowStyle)}`;\r\n  };\r\n  const setViewportOverflowStyle = (viewportOverflowStyle: XY<OverflowStyle>) => {\r\n    // `createAllOverflowStyleClassNames` and `allOverflowStyleClassNames` could be one scope further up but would increase bundle size\r\n    const createAllOverflowStyleClassNames = (isHorizontal?: boolean) =>\r\n      ([strVisible, strHidden, strScroll] as OverflowStyle[]).map((style) =>\r\n        createViewportOverflowStyleClassName(style, isHorizontal)\r\n      );\r\n    const allOverflowStyleClassNames = createAllOverflowStyleClassNames(true)\r\n      .concat(createAllOverflowStyleClassNames())\r\n      .join(' ');\r\n\r\n    _viewportAddRemoveClass(allOverflowStyleClassNames);\r\n    _viewportAddRemoveClass(\r\n      (keys(viewportOverflowStyle) as Array<keyof typeof viewportOverflowStyle>)\r\n        .map((axis) =>\r\n          createViewportOverflowStyleClassName(viewportOverflowStyle[axis], axis === 'x')\r\n        )\r\n        .join(' '),\r\n      true\r\n    );\r\n  };\r\n\r\n  return (\r\n    { _checkOption, _observersUpdateHints, _observersState, _force },\r\n    { _paddingStyleChanged }\r\n  ) => {\r\n    const { _sizeChanged, _contentMutation, _directionChanged, _appear, _scrollbarSizeChanged } =\r\n      _observersUpdateHints || {};\r\n    const scrollbarsHidingPluginViewportArrangement =\r\n      scrollbarsHidingPlugin &&\r\n      scrollbarsHidingPlugin._viewportArrangement(\r\n        structureSetupElements,\r\n        structureSetupState,\r\n        _observersState,\r\n        env,\r\n        _checkOption\r\n      );\r\n\r\n    const { _arrangeViewport, _undoViewportArrange, _hideNativeScrollbars } =\r\n      scrollbarsHidingPluginViewportArrangement || {};\r\n\r\n    const [showNativeOverlaidScrollbars, showNativeOverlaidScrollbarsChanged] =\r\n      getShowNativeOverlaidScrollbars(_checkOption, env);\r\n    const [overflow, overflowChanged] = _checkOption('overflow');\r\n    const overflowXVisible = overflowIsVisible(overflow.x);\r\n    const overflowYVisible = overflowIsVisible(overflow.y);\r\n\r\n    const viewportChanged =\r\n      true ||\r\n      _sizeChanged ||\r\n      _paddingStyleChanged ||\r\n      _contentMutation ||\r\n      _directionChanged ||\r\n      _scrollbarSizeChanged ||\r\n      showNativeOverlaidScrollbarsChanged;\r\n\r\n    let sizeFractionCache = getCurrentSizeFraction(_force);\r\n    let viewportScrollSizeCache = getCurrentViewportScrollSizeCache(_force);\r\n    let overflowAmuntCache = getCurrentOverflowAmountCache(_force);\r\n    let overflowEdgeCache = getCurrentOverflowEdgeCache(_force);\r\n\r\n    if (showNativeOverlaidScrollbarsChanged && _nativeScrollbarsHiding) {\r\n      _viewportAddRemoveClass(dataValueViewportScrollbarHidden, !showNativeOverlaidScrollbars);\r\n    }\r\n\r\n    if (viewportChanged) {\r\n      if (hasAttrClass(_host, dataAttributeHost, dataValueNoClipping)) {\r\n        setMeasuringMode(true);\r\n      }\r\n\r\n      const [redoViewportArrange] = _undoViewportArrange ? _undoViewportArrange() : [];\r\n\r\n      const [sizeFraction] = (sizeFractionCache = updateSizeFraction(_force));\r\n      const [viewportScrollSize] = (viewportScrollSizeCache =\r\n        updateViewportScrollSizeCache(_force));\r\n      const viewportClientSize = getClientSize(_viewport);\r\n      const windowInnerSize = viewportIsTargetBody && getWindowSize(_windowElm());\r\n      const overflowAmountScrollSize = {\r\n        w: max0(viewportScrollSize.w + sizeFraction.w),\r\n        h: max0(viewportScrollSize.h + sizeFraction.h),\r\n      };\r\n\r\n      const overflowAmountClientSize = {\r\n        w: max0(\r\n          (windowInnerSize\r\n            ? windowInnerSize.w\r\n            : viewportClientSize.w + max0(viewportClientSize.w - viewportScrollSize.w)) +\r\n            sizeFraction.w\r\n        ),\r\n        h: max0(\r\n          (windowInnerSize\r\n            ? windowInnerSize.h\r\n            : viewportClientSize.h + max0(viewportClientSize.h - viewportScrollSize.h)) +\r\n            sizeFraction.h\r\n        ),\r\n      };\r\n\r\n      redoViewportArrange && redoViewportArrange();\r\n\r\n      overflowEdgeCache = updateOverflowEdge(overflowAmountClientSize);\r\n      overflowAmuntCache = updateOverflowAmountCache(\r\n        getOverflowAmount(overflowAmountScrollSize, overflowAmountClientSize),\r\n        _force\r\n      );\r\n    }\r\n\r\n    const [overflowEdge, overflowEdgeChanged] = overflowEdgeCache;\r\n    const [overflowAmount, overflowAmountChanged] = overflowAmuntCache;\r\n    const [viewportScrollSize, viewportScrollSizeChanged] = viewportScrollSizeCache;\r\n    const [sizeFraction, sizeFractionChanged] = sizeFractionCache;\r\n    const [hasOverflow, hasOverflowChanged] = updateHasOverflowCache({\r\n      x: overflowAmount.w > 0,\r\n      y: overflowAmount.h > 0,\r\n    });\r\n    const removeClipping =\r\n      (overflowXVisible && overflowYVisible && (hasOverflow.x || hasOverflow.y)) ||\r\n      (overflowXVisible && hasOverflow.x && !hasOverflow.y) ||\r\n      (overflowYVisible && hasOverflow.y && !hasOverflow.x);\r\n    const adjustViewportStyle =\r\n      _paddingStyleChanged ||\r\n      _directionChanged ||\r\n      _scrollbarSizeChanged ||\r\n      sizeFractionChanged ||\r\n      viewportScrollSizeChanged ||\r\n      overflowEdgeChanged ||\r\n      overflowAmountChanged ||\r\n      overflowChanged ||\r\n      showNativeOverlaidScrollbarsChanged ||\r\n      viewportChanged;\r\n    const viewportOverflowState = createViewportOverflowState(hasOverflow, overflow);\r\n    const [overflowStyle, overflowStyleChanged] = updateOverflowStyleCache(\r\n      viewportOverflowState._overflowStyle\r\n    );\r\n    const [flowDirectionStyles, flowDirectionStylesChanged] = updateFlowDirectionStyles(_force);\r\n\r\n    const adjustMeasuredScrollCoordinates =\r\n      _directionChanged || _appear || flowDirectionStylesChanged || hasOverflowChanged || _force;\r\n    const [scrollCoordinates, scrollCoordinatesChanged] = adjustMeasuredScrollCoordinates\r\n      ? updateMeasuredScrollCoordinates(getMeasuredScrollCoordinates(flowDirectionStyles), _force)\r\n      : getCurrentMeasuredScrollCoordinates();\r\n\r\n    if (adjustViewportStyle) {\r\n      overflowStyleChanged && setViewportOverflowStyle(viewportOverflowState._overflowStyle);\r\n\r\n      if (_hideNativeScrollbars && _arrangeViewport) {\r\n        setStyles(\r\n          _viewport,\r\n          _hideNativeScrollbars(\r\n            viewportOverflowState,\r\n            _observersState,\r\n            _arrangeViewport(viewportOverflowState, viewportScrollSize, sizeFraction)\r\n          )\r\n        );\r\n      }\r\n    }\r\n\r\n    setMeasuringMode(false);\r\n\r\n    addRemoveAttrClass(_host, dataAttributeHost, dataValueNoClipping, removeClipping);\r\n    addRemoveAttrClass(_padding, dataAttributePadding, dataValueNoClipping, removeClipping);\r\n\r\n    assignDeep(structureSetupState, {\r\n      _overflowStyle: overflowStyle,\r\n      _overflowEdge: {\r\n        x: overflowEdge.w,\r\n        y: overflowEdge.h,\r\n      },\r\n      _overflowAmount: {\r\n        x: overflowAmount.w,\r\n        y: overflowAmount.h,\r\n      },\r\n      _hasOverflow: hasOverflow,\r\n      _scrollCoordinates: sanitizeScrollCoordinates(scrollCoordinates, overflowAmount),\r\n    });\r\n\r\n    return {\r\n      _overflowStyleChanged: overflowStyleChanged,\r\n      _overflowEdgeChanged: overflowEdgeChanged,\r\n      _overflowAmountChanged: overflowAmountChanged,\r\n      _scrollCoordinatesChanged: scrollCoordinatesChanged || overflowAmountChanged,\r\n      _scrolled: adjustMeasuredScrollCoordinates,\r\n    };\r\n  };\r\n};\r\n", "import type { TRBL, XY, ScrollCoordinates } from '../../support';\r\nimport type { StructureSetupElementsObj } from './structureSetup.elements';\r\nimport type {\r\n  ObserversSetupState,\r\n  ObserversSetupUpdateHints,\r\n  Setup,\r\n  SetupUpdateInfo,\r\n} from '../../setups';\r\nimport type { InitializationTarget } from '../../initialization';\r\nimport type { StyleObject, OverflowStyle } from '../../typings';\r\nimport {\r\n  assignDeep,\r\n  each,\r\n  getElementScroll,\r\n  getZeroScrollCoordinates,\r\n  scrollElementTo,\r\n  strHidden,\r\n  strMarginBottom,\r\n  strMarginLeft,\r\n  strMarginRight,\r\n  strPaddingBottom,\r\n  strPaddingLeft,\r\n  strPaddingRight,\r\n  strPaddingTop,\r\n} from '../../support';\r\nimport { getEnvironment } from '../../environment';\r\nimport { createStructureSetupElements } from './structureSetup.elements';\r\nimport {\r\n  createOverflowUpdateSegment,\r\n  createPaddingUpdateSegment,\r\n  createTrinsicUpdateSegment,\r\n} from './updateSegments';\r\n\r\nexport interface StructureSetupState {\r\n  _padding: TRBL;\r\n  _paddingAbsolute: boolean;\r\n  _viewportPaddingStyle: StyleObject;\r\n  _overflowEdge: XY<number>;\r\n  _overflowAmount: XY<number>;\r\n  _overflowStyle: XY<OverflowStyle>;\r\n  _hasOverflow: XY<boolean>;\r\n  _scrollCoordinates: ScrollCoordinates;\r\n}\r\n\r\nexport interface StructureSetupUpdateInfo extends SetupUpdateInfo {\r\n  _observersState: ObserversSetupState;\r\n  _observersUpdateHints?: ObserversSetupUpdateHints;\r\n}\r\n\r\nexport type StructureSetupUpdateHints = {\r\n  _overflowEdgeChanged?: boolean;\r\n  _overflowAmountChanged?: boolean;\r\n  _overflowStyleChanged?: boolean;\r\n  _paddingStyleChanged?: boolean;\r\n  _scrollCoordinatesChanged?: boolean;\r\n};\r\n\r\nexport type StructureSetup = [\r\n  ...Setup<StructureSetupUpdateInfo, StructureSetupState, StructureSetupUpdateHints>,\r\n  /** The elements created by the structure setup. */\r\n  StructureSetupElementsObj,\r\n  /** Function to be called when the initialization was canceled. */\r\n  () => void,\r\n];\r\n\r\nexport type StructureUpdateSegment = (\r\n  updateInfo: StructureSetupUpdateInfo,\r\n  updateHints: Readonly<StructureSetupUpdateHints>\r\n) => StructureSetupUpdateHints | void;\r\n\r\nexport type CreateStructureUpdateSegment = (\r\n  structureSetupElements: StructureSetupElementsObj,\r\n  state: StructureSetupState\r\n) => StructureUpdateSegment;\r\n\r\nexport const createStructureSetup = (target: InitializationTarget): StructureSetup => {\r\n  const [elements, appendStructureElements, canceled] = createStructureSetupElements(target);\r\n  const state: StructureSetupState = {\r\n    _padding: {\r\n      t: 0,\r\n      r: 0,\r\n      b: 0,\r\n      l: 0,\r\n    },\r\n    _paddingAbsolute: false,\r\n    _viewportPaddingStyle: {\r\n      [strMarginRight]: 0,\r\n      [strMarginBottom]: 0,\r\n      [strMarginLeft]: 0,\r\n      [strPaddingTop]: 0,\r\n      [strPaddingRight]: 0,\r\n      [strPaddingBottom]: 0,\r\n      [strPaddingLeft]: 0,\r\n    },\r\n    _overflowEdge: { x: 0, y: 0 },\r\n    _overflowAmount: { x: 0, y: 0 },\r\n    _overflowStyle: {\r\n      x: strHidden,\r\n      y: strHidden,\r\n    },\r\n    _hasOverflow: {\r\n      x: false,\r\n      y: false,\r\n    },\r\n    _scrollCoordinates: getZeroScrollCoordinates(),\r\n  };\r\n  const { _target, _scrollOffsetElement, _viewportIsTarget, _removeScrollObscuringStyles } =\r\n    elements;\r\n  const { _nativeScrollbarsHiding, _nativeScrollbarsOverlaid } = getEnvironment();\r\n  const doViewportArrange =\r\n    !_nativeScrollbarsHiding && (_nativeScrollbarsOverlaid.x || _nativeScrollbarsOverlaid.y);\r\n\r\n  const updateSegments: StructureUpdateSegment[] = [\r\n    createTrinsicUpdateSegment(elements, state),\r\n    createPaddingUpdateSegment(elements, state),\r\n    createOverflowUpdateSegment(elements, state),\r\n  ];\r\n\r\n  return [\r\n    appendStructureElements,\r\n    (updateInfo) => {\r\n      const updateHints: StructureSetupUpdateHints = {};\r\n      const adjustScrollOffset = doViewportArrange;\r\n      const scrollOffset = adjustScrollOffset && getElementScroll(_scrollOffsetElement);\r\n      const revertScrollObscuringStyles = scrollOffset && _removeScrollObscuringStyles();\r\n\r\n      each(updateSegments, (updateSegment) => {\r\n        assignDeep(updateHints, updateSegment(updateInfo, updateHints) || {});\r\n      });\r\n\r\n      scrollElementTo(_scrollOffsetElement, scrollOffset);\r\n      revertScrollObscuringStyles && revertScrollObscuringStyles();\r\n      !_viewportIsTarget && scrollElementTo(_target, 0);\r\n\r\n      return updateHints;\r\n    },\r\n    state,\r\n    elements,\r\n    canceled,\r\n  ];\r\n};\r\n", "import type { OptionsCheckFn, Options, PartialOptions, ReadonlyOptions } from '../options';\r\nimport type { DeepReadonly } from '../typings';\r\nimport type { InitializationTarget } from '../initialization';\r\nimport type { ObserversSetupState, ObserversSetupUpdateHints } from './observersSetup';\r\nimport type { StructureSetupState, StructureSetupUpdateHints } from './structureSetup';\r\nimport type { StructureSetupElementsObj } from './structureSetup/structureSetup.elements';\r\nimport type { ScrollbarsSetupElementsObj } from './scrollbarsSetup/scrollbarsSetup.elements';\r\nimport { createOptionCheck } from '../options';\r\nimport {\r\n  assignDeep,\r\n  bind,\r\n  getElementScroll,\r\n  isEmptyObject,\r\n  keys,\r\n  runEachAndClear,\r\n  scrollElementTo,\r\n} from '../support';\r\nimport { createObserversSetup } from './observersSetup';\r\nimport { createScrollbarsSetup } from './scrollbarsSetup';\r\nimport { createStructureSetup } from './structureSetup';\r\n\r\nexport type SetupUpdateHints = Partial<Record<string, boolean>>;\r\n\r\nexport type SetupUpdateInfo = {\r\n  _checkOption: OptionsCheckFn<Options>;\r\n  _changedOptions: PartialOptions;\r\n  _force: boolean;\r\n};\r\n\r\nexport type Setup<\r\n  U extends SetupUpdateInfo,\r\n  S extends Readonly<Record<string, any>>,\r\n  H extends SetupUpdateHints | void,\r\n> = [\r\n  /** The create function which returns the `destroy` function. */\r\n  _create: () => () => void,\r\n  /** Function which updates the setup and returns the update result. */\r\n  _update: (updateInfo: U) => H,\r\n  /** Function which returns the current state. */\r\n  _state: S,\r\n];\r\n\r\nexport interface SetupsUpdateInfo {\r\n  /** The options that changed or `undefined` if none changed. */\r\n  _changedOptions?: PartialOptions;\r\n  /** Whether chache should be ignored. */\r\n  _force?: boolean;\r\n  /** Whether observers should take their records and thus update as well. */\r\n  _takeRecords?: boolean;\r\n  /** Whether one or more scrollbars has been cloned. */\r\n  _cloneScrollbar?: boolean;\r\n}\r\n\r\nexport interface SetupsUpdateHints {\r\n  readonly _observersUpdateHints: DeepReadonly<ObserversSetupUpdateHints>;\r\n  readonly _structureUpdateHints: DeepReadonly<StructureSetupUpdateHints>;\r\n}\r\n\r\nexport interface SetupsState {\r\n  readonly _observersSetupState: DeepReadonly<ObserversSetupState>;\r\n  readonly _structureSetupState: DeepReadonly<StructureSetupState>;\r\n}\r\n\r\nexport interface SetupsElements {\r\n  readonly _structureSetupElements: DeepReadonly<StructureSetupElementsObj>;\r\n  readonly _scrollbarsSetupElements: DeepReadonly<ScrollbarsSetupElementsObj>;\r\n}\r\n\r\nexport type Setups = [\r\n  construct: () => () => void,\r\n  update: (updateInfo: SetupsUpdateInfo) => boolean,\r\n  getState: () => SetupsState,\r\n  elements: SetupsElements,\r\n  canceled: () => void,\r\n];\r\n\r\nexport const createSetups = (\r\n  target: InitializationTarget,\r\n  options: ReadonlyOptions,\r\n  isDestroyed: () => boolean,\r\n  onUpdated: (updateInfo: SetupsUpdateInfo, updateHints: SetupsUpdateHints) => void,\r\n  onScroll: (scrollEvent: Event) => void\r\n): Setups => {\r\n  let cacheAndOptionsInitialized = false;\r\n  const getCurrentOption = createOptionCheck(options, {});\r\n  const [\r\n    structureSetupCreate,\r\n    structureSetupUpdate,\r\n    structureSetupState,\r\n    structureSetupElements,\r\n    structureSetupCanceled,\r\n  ] = createStructureSetup(target);\r\n  const [observersSetupCreate, observersSetupUpdate, observersSetupState] = createObserversSetup(\r\n    structureSetupElements,\r\n    structureSetupState,\r\n    getCurrentOption,\r\n    (observersUpdateHints) => {\r\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\r\n      update({}, observersUpdateHints);\r\n    }\r\n  );\r\n  const [scrollbarsSetupCreate, scrollbarsSetupUpdate, , scrollbarsSetupElements] =\r\n    createScrollbarsSetup(\r\n      target,\r\n      options,\r\n      observersSetupState,\r\n      structureSetupState,\r\n      structureSetupElements,\r\n      onScroll\r\n    );\r\n\r\n  const updateHintsAreTruthy = (hints: SetupUpdateHints) =>\r\n    keys(hints).some((key) => !!hints[key as keyof typeof hints]);\r\n\r\n  const update = (\r\n    updateInfo: SetupsUpdateInfo,\r\n    observerUpdateHints?: ObserversSetupUpdateHints\r\n  ): boolean => {\r\n    if (isDestroyed()) {\r\n      return false;\r\n    }\r\n\r\n    const {\r\n      _changedOptions: rawChangedOptions,\r\n      _force: rawForce,\r\n      _takeRecords,\r\n      _cloneScrollbar,\r\n    } = updateInfo;\r\n\r\n    const _changedOptions = rawChangedOptions || {};\r\n    const _force = !!rawForce || !cacheAndOptionsInitialized;\r\n    const baseUpdateInfoObj: SetupUpdateInfo = {\r\n      _checkOption: createOptionCheck(options, _changedOptions, _force),\r\n      _changedOptions,\r\n      _force,\r\n    };\r\n\r\n    if (_cloneScrollbar) {\r\n      scrollbarsSetupUpdate(baseUpdateInfoObj);\r\n      return false;\r\n    }\r\n\r\n    const observersHints =\r\n      observerUpdateHints ||\r\n      observersSetupUpdate(\r\n        assignDeep({}, baseUpdateInfoObj, {\r\n          _takeRecords,\r\n        })\r\n      );\r\n\r\n    const structureHints = structureSetupUpdate(\r\n      assignDeep({}, baseUpdateInfoObj, {\r\n        _observersState: observersSetupState,\r\n        _observersUpdateHints: observersHints,\r\n      })\r\n    );\r\n\r\n    scrollbarsSetupUpdate(\r\n      assignDeep({}, baseUpdateInfoObj, {\r\n        _observersUpdateHints: observersHints,\r\n        _structureUpdateHints: structureHints,\r\n      })\r\n    );\r\n\r\n    const truthyObserversHints = updateHintsAreTruthy(observersHints);\r\n    const truthyStructureHints = updateHintsAreTruthy(structureHints);\r\n    const changed =\r\n      truthyObserversHints || truthyStructureHints || !isEmptyObject(_changedOptions) || _force;\r\n\r\n    cacheAndOptionsInitialized = true;\r\n\r\n    changed &&\r\n      onUpdated(updateInfo, {\r\n        _observersUpdateHints: observersHints,\r\n        _structureUpdateHints: structureHints,\r\n      });\r\n\r\n    return changed;\r\n  };\r\n\r\n  return [\r\n    () => {\r\n      const { _originalScrollOffsetElement, _scrollOffsetElement, _removeScrollObscuringStyles } =\r\n        structureSetupElements;\r\n      const initialScroll = getElementScroll(_originalScrollOffsetElement);\r\n      const destroyFns = [observersSetupCreate(), structureSetupCreate(), scrollbarsSetupCreate()];\r\n      const revertScrollObscuringStyles = _removeScrollObscuringStyles();\r\n\r\n      scrollElementTo(_scrollOffsetElement, initialScroll);\r\n      revertScrollObscuringStyles();\r\n\r\n      return bind(runEachAndClear, destroyFns);\r\n    },\r\n    update,\r\n    () => ({\r\n      _observersSetupState: observersSetupState,\r\n      _structureSetupState: structureSetupState,\r\n    }),\r\n    {\r\n      _structureSetupElements: structureSetupElements,\r\n      _scrollbarsSetupElements: scrollbarsSetupElements,\r\n    },\r\n    structureSetupCanceled,\r\n  ];\r\n};\r\n", "import type { OverlayScrollbars } from './overlayscrollbars';\r\n\r\nconst targetInstanceMap: WeakMap<Element, OverlayScrollbars> = new WeakMap();\r\n\r\n/**\r\n * Adds the given OverlayScrollbars instance to the given element.\r\n * @param target The element which is the target of the OverlayScrollbars instance.\r\n * @param osInstance The OverlayScrollbars instance.\r\n */\r\nexport const addInstance = (target: Element, osInstance: OverlayScrollbars): void => {\r\n  targetInstanceMap.set(target, osInstance);\r\n};\r\n\r\n/**\r\n * Removes a OverlayScrollbars instance from the given element.\r\n * @param target The element from which its OverlayScrollbars instance shall be removed.\r\n */\r\nexport const removeInstance = (target: Element): void => {\r\n  targetInstanceMap.delete(target);\r\n};\r\n\r\n/**\r\n * Gets the OverlayScrollbars from the given element or undefined if it doesn't have one.\r\n * @param target The element of which its OverlayScrollbars instance shall be get.\r\n */\r\nexport const getInstance = (target: Element): OverlayScrollbars | undefined =>\r\n  targetInstanceMap.get(target);\r\n", "import type { XY, TRBL } from './support';\r\nimport type { Options, PartialOptions, ReadonlyOptions } from './options';\r\nimport type {\r\n  InferInstancePluginModuleInstance,\r\n  InferStaticPluginModuleInstance,\r\n  InstancePlugin,\r\n  OptionsValidationPlugin,\r\n  Plugin,\r\n  PluginModuleInstance,\r\n  StaticPlugin,\r\n} from './plugins';\r\nimport type { Initialization, InitializationTarget, PartialInitialization } from './initialization';\r\nimport type { OverflowStyle } from './typings';\r\nimport type { EventListenerArgs, EventListener, EventListeners } from './eventListeners';\r\nimport type {\r\n  ScrollbarsSetupElement,\r\n  ScrollbarStructure,\r\n} from './setups/scrollbarsSetup/scrollbarsSetup.elements';\r\nimport {\r\n  addPlugins,\r\n  getStaticPluginModuleInstance,\r\n  optionsValidationPluginModuleName,\r\n  pluginModules,\r\n  registerPluginModuleInstances,\r\n} from './plugins';\r\nimport { createSetups } from './setups';\r\nimport { addInstance, getInstance, removeInstance } from './instances';\r\nimport { cancelInitialization } from './initialization';\r\nimport { getEnvironment } from './environment';\r\nimport { getOptionsDiff } from './options';\r\nimport {\r\n  assignDeep,\r\n  isEmptyObject,\r\n  isFunction,\r\n  isHTMLElement,\r\n  createEventListenerHub,\r\n  isPlainObject,\r\n  keys,\r\n  isArray,\r\n  push,\r\n  runEachAndClear,\r\n  bind,\r\n  removeUndefinedProperties,\r\n} from './support';\r\nimport { setNonce } from './nonce';\r\nimport { setTrustedTypePolicy } from './trustedTypePolicy';\r\n\r\n// Notes:\r\n// Height intrinsic detection use \"content: true\" init strategy - or open ticket for custom height intrinsic observer\r\n\r\n/**\r\n * Describes the OverlayScrollbars environment.\r\n */\r\nexport interface Environment {\r\n  /** The native scrollbars size of the browser / system. */\r\n  scrollbarsSize: XY<number>;\r\n  /** Whether the native scrollbars are overlaid. */\r\n  scrollbarsOverlaid: XY<boolean>;\r\n  /** Whether the browser supports native scrollbars hiding. */\r\n  scrollbarsHiding: boolean;\r\n  /** Whether the browser supports the ScrollTimeline API. */\r\n  scrollTimeline: boolean;\r\n  /** The default Initialization to use if nothing else is specified. */\r\n  staticDefaultInitialization: Initialization;\r\n  /** The default Options to use if nothing else is specified. */\r\n  staticDefaultOptions: Options;\r\n\r\n  /** Returns the current default Initialization. */\r\n  getDefaultInitialization(): Initialization;\r\n  /** Returns the current default Options. */\r\n  getDefaultOptions(): Options;\r\n\r\n  /**\r\n   * Sets a new default Initialization.\r\n   * If the new default Initialization is partially filled, its deeply merged with the current default Initialization.\r\n   * @param newDefaultInitialization The new default Initialization.\r\n   * @returns The current default Initialization.\r\n   */\r\n  setDefaultInitialization(newDefaultInitialization: PartialInitialization): Initialization;\r\n  /**\r\n   * Sets new default Options.\r\n   * If the new default Options are partially filled, they're deeply merged with the current default Options.\r\n   * @param newDefaultOptions The new default Options.\r\n   * @returns The current default options.\r\n   */\r\n  setDefaultOptions(newDefaultOptions: PartialOptions): Options;\r\n}\r\n\r\n/**\r\n * The primary entry point to OverlayScrollbars.\r\n */\r\nexport interface OverlayScrollbarsStatic {\r\n  /**\r\n   * Returns the current OverlayScrollbars instance if the target already has an instance.\r\n   * @param target The initialization target to from which the instance shall be returned.\r\n   */\r\n  (target: InitializationTarget): OverlayScrollbars | undefined;\r\n  /**\r\n   * Initializes a new OverlayScrollbars instance to the given target\r\n   * or returns the current OverlayScrollbars instance if the target already has an instance.\r\n   * @param target The target.\r\n   * @param options The options. (Can be just an empty object)\r\n   * @param eventListeners Optional event listeners.\r\n   */\r\n  (\r\n    target: InitializationTarget,\r\n    options: PartialOptions,\r\n    eventListeners?: EventListeners\r\n  ): OverlayScrollbars;\r\n\r\n  /**\r\n   * Checks whether the passed value is a valid and not destroyed overlayscrollbars instance.\r\n   * @param osInstance The value which shall be checked.\r\n   */\r\n  valid(osInstance: any): osInstance is OverlayScrollbars;\r\n  /**\r\n   * Gets the environment.\r\n   */\r\n  env(): Environment;\r\n  /**\r\n   * Sets the nonce attribute for inline styles.\r\n   */\r\n  nonce(newNonce: string | undefined): void;\r\n  /**\r\n   * Sets the trusted type policy used for DOM operations.\r\n   */\r\n  trustedTypePolicy(newTrustedTypePolicy: unknown | undefined): void;\r\n  /**\r\n   * Adds a single plugin.\r\n   * @param plugin The plugin to be added.\r\n   * @returns The plugins static modules instance or `void` if no instance was found.\r\n   */\r\n  plugin<P extends Plugin>(\r\n    plugin: P\r\n  ): P extends StaticPlugin ? InferStaticPluginModuleInstance<P> : void;\r\n  /**\r\n   * Adds multiple plugins.\r\n   * @param plugins The plugins to be added.\r\n   * @returns The plugins static modules instances or `void` if no instance was found.\r\n   */\r\n  plugin<P extends [Plugin, ...Plugin[]]>(\r\n    plugins: P\r\n  ): P extends [Plugin, ...Plugin[]]\r\n    ? {\r\n        [K in keyof P]: P[K] extends StaticPlugin ? InferStaticPluginModuleInstance<P[K]> : void;\r\n      }\r\n    : void;\r\n}\r\n\r\n/**\r\n * Describes a OverlayScrollbars instances state.\r\n */\r\nexport interface State {\r\n  /** Describes the current padding in pixel. */\r\n  padding: TRBL;\r\n  /** Whether the current padding is absolute. */\r\n  paddingAbsolute: boolean;\r\n  /** The client width (x) & height (y) of the viewport in pixel. */\r\n  overflowEdge: XY<number>;\r\n  /** The overflow amount in pixel. */\r\n  overflowAmount: XY<number>;\r\n  /** The css overflow style of the viewport. */\r\n  overflowStyle: XY<OverflowStyle>;\r\n  /** Whether the viewport has an overflow. */\r\n  hasOverflow: XY<boolean>;\r\n  /** The scroll coordinates of the viewport. */\r\n  scrollCoordinates: {\r\n    /** The start (origin) scroll coordinates for each axis. */\r\n    start: XY<number>;\r\n    /** The end scroll coordinates for each axis. */\r\n    end: XY<number>;\r\n  };\r\n  /** Whether the direction is considered rtl. */\r\n  directionRTL: boolean;\r\n  /** Whether the instance is considered destroyed. */\r\n  destroyed: boolean;\r\n}\r\n\r\n/**\r\n * Describes the elements of a scrollbar.\r\n */\r\nexport interface ScrollbarElements {\r\n  /**\r\n   * The root element of the scrollbar.\r\n   * The HTML structure looks like this:\r\n   * <scrollbar>\r\n   *   <track>\r\n   *     <handle />\r\n   *   </track>\r\n   * </scrollbar>\r\n   */\r\n  scrollbar: HTMLElement;\r\n  /** The track element of the scrollbar. */\r\n  track: HTMLElement;\r\n  /** The handle element of the scrollbar. */\r\n  handle: HTMLElement;\r\n}\r\n\r\n/**\r\n * Describes the elements of a scrollbar and provides the possibility to clone them.\r\n */\r\nexport interface CloneableScrollbarElements extends ScrollbarElements {\r\n  /**\r\n   * Clones the current scrollbar and returns the cloned elements.\r\n   * The returned elements aren't added to the DOM.\r\n   */\r\n  clone(): ScrollbarElements;\r\n}\r\n\r\n/**\r\n * Describes the elements of a OverlayScrollbars instance.\r\n */\r\nexport interface Elements {\r\n  /** The element the instance was applied to. */\r\n  target: HTMLElement;\r\n  /** The host element. Its the root of all other elements. */\r\n  host: HTMLElement;\r\n  /**\r\n   * The element which is responsible to apply correct paddings.\r\n   * Depending on the Initialization it can be the same as the viewport element.\r\n   */\r\n  padding: HTMLElement;\r\n  /** The element which is responsible to do any scrolling. */\r\n  viewport: HTMLElement;\r\n  /**\r\n   * The element which is responsible to hold the content.\r\n   * Depending on the Initialization it can be the same as the viewport element.\r\n   */\r\n  content: HTMLElement;\r\n  /**\r\n   * The element through which you can get the current `scrollLeft` or `scrollTop` offset.\r\n   * Depending on the target element it can be the same as the viewport element.\r\n   */\r\n  scrollOffsetElement: HTMLElement;\r\n  /**\r\n   * The element through which you can add `scroll` events.\r\n   * Depending on the target element it can be the same as the viewport element.\r\n   */\r\n  scrollEventElement: HTMLElement | Document;\r\n  /** The horizontal scrollbar elements. */\r\n  scrollbarHorizontal: CloneableScrollbarElements;\r\n  /** The vertical scrollbar elements. */\r\n  scrollbarVertical: CloneableScrollbarElements;\r\n}\r\n\r\n/**\r\n * Describes a OverlayScrollbars instance.\r\n */\r\nexport interface OverlayScrollbars {\r\n  /** Gets the current options of the instance. */\r\n  options(): Options;\r\n  /**\r\n   * Sets the options of the instance.\r\n   * If the new options are partially filled, they're deeply merged with either the current options or the current default options.\r\n   * @param newOptions The new options which should be applied.\r\n   * @param pure Whether the options should be reset before the new options are added.\r\n   * @returns Returns the current options of the instance.\r\n   */\r\n  options(newOptions: PartialOptions, pure?: boolean): Options;\r\n\r\n  /**\r\n   * Adds event listeners to the instance.\r\n   * @param eventListeners An object which contains the added listeners.\r\n   * @param pure Whether all already added event listeners should be removed before the new listeners are added.\r\n   * @returns Returns a function which removes the added listeners.\r\n   */\r\n  on(eventListeners: EventListeners, pure?: boolean): () => void;\r\n  /**\r\n   * Adds a single event listener to the instance.\r\n   * @param name The name of the event.\r\n   * @param listener The listener which is invoked on that event.\r\n   * @returns Returns a function which removes the added listeners.\r\n   */\r\n  on<N extends keyof EventListenerArgs>(name: N, listener: EventListener<N>): () => void;\r\n  /**\r\n   * Adds multiple event listeners to the instance.\r\n   * @param name The name of the event.\r\n   * @param listener The listeners which are invoked on that event.\r\n   * @returns Returns a function which removes the added listeners.\r\n   */\r\n  on<N extends keyof EventListenerArgs>(name: N, listener: EventListener<N>[]): () => void;\r\n\r\n  /**\r\n   * Removes a single event listener from the instance.\r\n   * @param name The name of the event.\r\n   * @param listener The listener which shall be removed.\r\n   */\r\n  off<N extends keyof EventListenerArgs>(name: N, listener: EventListener<N>): void;\r\n  /**\r\n   * Removes multiple event listeners from the instance.\r\n   * @param name The name of the event.\r\n   * @param listener The listeners which shall be removed.\r\n   */\r\n  off<N extends keyof EventListenerArgs>(name: N, listener: EventListener<N>[]): void;\r\n\r\n  /**\r\n   * Updates the instance.\r\n   * @param force Whether the update should force the cache to be invalidated.\r\n   * @returns A boolean which indicates whether the `update` event was triggered through this update.\r\n   * The update event is only triggered if something changed because of this update.\r\n   */\r\n  update(force?: boolean): boolean;\r\n  /** Returns the state of the instance. */\r\n  state(): State;\r\n  /** Returns the elements of the instance. */\r\n  elements(): Elements;\r\n  /** Destroys the instance and removes all added elements. */\r\n  destroy(): void;\r\n  /** Returns the instance of the passed plugin or `undefined` if no instance was found. */\r\n  plugin<P extends InstancePlugin>(osPlugin: P): InferInstancePluginModuleInstance<P> | undefined;\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-redeclare\r\nexport const OverlayScrollbars: OverlayScrollbarsStatic = (\r\n  target: InitializationTarget,\r\n  options?: PartialOptions,\r\n  eventListeners?: EventListeners\r\n) => {\r\n  const { _getDefaultOptions } = getEnvironment();\r\n  const targetIsElement = isHTMLElement(target);\r\n  const instanceTarget = targetIsElement ? target : target.target;\r\n  const potentialInstance = getInstance(instanceTarget);\r\n  if (options && !potentialInstance) {\r\n    let destroyed = false;\r\n    const destroyFns: (() => void)[] = [];\r\n    const instancePluginModuleInstances: Record<string, PluginModuleInstance> = {};\r\n    const validateOptions = (newOptions: PartialOptions) => {\r\n      const newOptionsWithoutUndefined = removeUndefinedProperties(newOptions, true);\r\n      const pluginValidate = getStaticPluginModuleInstance<typeof OptionsValidationPlugin>(\r\n        optionsValidationPluginModuleName\r\n      );\r\n      return pluginValidate\r\n        ? pluginValidate(newOptionsWithoutUndefined, true)\r\n        : newOptionsWithoutUndefined;\r\n    };\r\n    const currentOptions: ReadonlyOptions = assignDeep(\r\n      {},\r\n      _getDefaultOptions(),\r\n      validateOptions(options)\r\n    );\r\n    const [addPluginEvent, removePluginEvents, triggerPluginEvent] =\r\n      createEventListenerHub<EventListenerArgs>();\r\n    const [addInstanceEvent, removeInstanceEvents, triggerInstanceEvent] =\r\n      createEventListenerHub(eventListeners);\r\n    const triggerEvent: typeof triggerPluginEvent = (name, args) => {\r\n      triggerInstanceEvent(name, args);\r\n      triggerPluginEvent(name, args);\r\n    };\r\n    const [setupsConstruct, setupsUpdate, setupsState, setupsElements, setupsCanceled] =\r\n      createSetups(\r\n        target,\r\n        currentOptions,\r\n        () => destroyed,\r\n        ({ _changedOptions, _force }, { _observersUpdateHints, _structureUpdateHints }) => {\r\n          const {\r\n            _sizeChanged,\r\n            _directionChanged,\r\n            _heightIntrinsicChanged,\r\n            _contentMutation,\r\n            _hostMutation,\r\n            _appear,\r\n          } = _observersUpdateHints;\r\n\r\n          const {\r\n            _overflowEdgeChanged,\r\n            _overflowAmountChanged,\r\n            _overflowStyleChanged,\r\n            _scrollCoordinatesChanged,\r\n          } = _structureUpdateHints;\r\n\r\n          triggerEvent('updated', [\r\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\r\n            instance,\r\n            {\r\n              updateHints: {\r\n                sizeChanged: !!_sizeChanged,\r\n                directionChanged: !!_directionChanged,\r\n                heightIntrinsicChanged: !!_heightIntrinsicChanged,\r\n                overflowEdgeChanged: !!_overflowEdgeChanged,\r\n                overflowAmountChanged: !!_overflowAmountChanged,\r\n                overflowStyleChanged: !!_overflowStyleChanged,\r\n                scrollCoordinatesChanged: !!_scrollCoordinatesChanged,\r\n                contentMutation: !!_contentMutation,\r\n                hostMutation: !!_hostMutation,\r\n                appear: !!_appear,\r\n              },\r\n              changedOptions: _changedOptions || {},\r\n              force: !!_force,\r\n            },\r\n          ]);\r\n        },\r\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\r\n        (scrollEvent) => triggerEvent('scroll', [instance, scrollEvent])\r\n      );\r\n\r\n    const destroy = (canceled: boolean) => {\r\n      removeInstance(instanceTarget);\r\n      runEachAndClear(destroyFns);\r\n\r\n      destroyed = true;\r\n\r\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\r\n      triggerEvent('destroyed', [instance, canceled]);\r\n      removePluginEvents();\r\n      removeInstanceEvents();\r\n    };\r\n\r\n    const instance: OverlayScrollbars = {\r\n      options(newOptions?: PartialOptions, pure?: boolean) {\r\n        if (newOptions) {\r\n          const base = pure ? _getDefaultOptions() : {};\r\n          const changedOptions = getOptionsDiff(\r\n            currentOptions,\r\n            assignDeep(base, validateOptions(newOptions))\r\n          );\r\n          if (!isEmptyObject(changedOptions)) {\r\n            assignDeep(currentOptions, changedOptions);\r\n            setupsUpdate({ _changedOptions: changedOptions });\r\n          }\r\n        }\r\n        return assignDeep({}, currentOptions);\r\n      },\r\n      on: addInstanceEvent,\r\n      off: (name, listener) => {\r\n        name && listener && removeInstanceEvents(name, listener);\r\n      },\r\n      state() {\r\n        const { _observersSetupState, _structureSetupState } = setupsState();\r\n        const { _directionIsRTL } = _observersSetupState;\r\n        const {\r\n          _overflowEdge,\r\n          _overflowAmount,\r\n          _overflowStyle,\r\n          _hasOverflow,\r\n          _padding,\r\n          _paddingAbsolute,\r\n          _scrollCoordinates,\r\n        } = _structureSetupState;\r\n        return assignDeep(\r\n          {},\r\n          {\r\n            overflowEdge: _overflowEdge,\r\n            overflowAmount: _overflowAmount,\r\n            overflowStyle: _overflowStyle,\r\n            hasOverflow: _hasOverflow,\r\n            scrollCoordinates: {\r\n              start: _scrollCoordinates._start,\r\n              end: _scrollCoordinates._end,\r\n            },\r\n            padding: _padding,\r\n            paddingAbsolute: _paddingAbsolute,\r\n            directionRTL: _directionIsRTL,\r\n            destroyed,\r\n          }\r\n        );\r\n      },\r\n      elements() {\r\n        const {\r\n          _target,\r\n          _host,\r\n          _padding,\r\n          _viewport,\r\n          _content,\r\n          _scrollOffsetElement,\r\n          _scrollEventElement,\r\n        } = setupsElements._structureSetupElements;\r\n        const { _horizontal, _vertical } = setupsElements._scrollbarsSetupElements;\r\n        const translateScrollbarStructure = (\r\n          scrollbarStructure: ScrollbarStructure\r\n        ): ScrollbarElements => {\r\n          const { _handle, _track, _scrollbar } = scrollbarStructure;\r\n          return {\r\n            scrollbar: _scrollbar,\r\n            track: _track,\r\n            handle: _handle,\r\n          };\r\n        };\r\n        const translateScrollbarsSetupElement = (\r\n          scrollbarsSetupElement: ScrollbarsSetupElement\r\n        ): CloneableScrollbarElements => {\r\n          const { _scrollbarStructures, _clone } = scrollbarsSetupElement;\r\n          const translatedStructure = translateScrollbarStructure(_scrollbarStructures[0]);\r\n\r\n          return assignDeep({}, translatedStructure, {\r\n            clone: () => {\r\n              const result = translateScrollbarStructure(_clone());\r\n              setupsUpdate({ _cloneScrollbar: true });\r\n              return result;\r\n            },\r\n          });\r\n        };\r\n        return assignDeep(\r\n          {},\r\n          {\r\n            target: _target,\r\n            host: _host,\r\n            padding: _padding || _viewport,\r\n            viewport: _viewport,\r\n            content: _content || _viewport,\r\n            scrollOffsetElement: _scrollOffsetElement,\r\n            scrollEventElement: _scrollEventElement,\r\n            scrollbarHorizontal: translateScrollbarsSetupElement(_horizontal),\r\n            scrollbarVertical: translateScrollbarsSetupElement(_vertical),\r\n          }\r\n        );\r\n      },\r\n      update: (_force?: boolean) => setupsUpdate({ _force, _takeRecords: true }),\r\n      destroy: bind(destroy, false),\r\n      plugin: <P extends InstancePlugin>(plugin: P) =>\r\n        instancePluginModuleInstances[keys(plugin)[0]] as\r\n          | InferInstancePluginModuleInstance<P>\r\n          | undefined,\r\n    };\r\n\r\n    push(destroyFns, [setupsCanceled]);\r\n\r\n    // valid inside plugins\r\n    addInstance(instanceTarget, instance);\r\n\r\n    // init plugins\r\n    registerPluginModuleInstances(pluginModules, OverlayScrollbars, [\r\n      instance,\r\n      addPluginEvent,\r\n      instancePluginModuleInstances,\r\n    ]);\r\n\r\n    if (\r\n      cancelInitialization(\r\n        setupsElements._structureSetupElements._isBody,\r\n        !targetIsElement && target.cancel\r\n      )\r\n    ) {\r\n      destroy(true);\r\n      return instance;\r\n    }\r\n\r\n    push(destroyFns, setupsConstruct());\r\n\r\n    triggerEvent('initialized', [instance]);\r\n\r\n    instance.update();\r\n\r\n    return instance;\r\n  }\r\n  return potentialInstance!;\r\n};\r\n\r\nOverlayScrollbars.plugin = (plugins: Plugin | Plugin[]) => {\r\n  const isArr = isArray(plugins);\r\n  const pluginsToAdd: Plugin<string, void | PluginModuleInstance, void | PluginModuleInstance>[] =\r\n    isArr ? plugins : [plugins];\r\n  const result = pluginsToAdd.map(\r\n    (plugin) => registerPluginModuleInstances(plugin, OverlayScrollbars)[0]\r\n  );\r\n  addPlugins(pluginsToAdd);\r\n  return isArr ? result : (result[0] as any);\r\n};\r\nOverlayScrollbars.valid = (osInstance: any): osInstance is OverlayScrollbars => {\r\n  const hasElmsFn = osInstance && (osInstance as OverlayScrollbars).elements;\r\n  const elements = isFunction(hasElmsFn) && hasElmsFn();\r\n  return isPlainObject(elements) && !!getInstance(elements.target);\r\n};\r\nOverlayScrollbars.env = () => {\r\n  const {\r\n    _nativeScrollbarsSize,\r\n    _nativeScrollbarsOverlaid,\r\n    _nativeScrollbarsHiding,\r\n    _scrollTimeline,\r\n    _staticDefaultInitialization,\r\n    _staticDefaultOptions,\r\n    _getDefaultInitialization,\r\n    _setDefaultInitialization,\r\n    _getDefaultOptions,\r\n    _setDefaultOptions,\r\n  } = getEnvironment();\r\n  return assignDeep(\r\n    {},\r\n    {\r\n      scrollbarsSize: _nativeScrollbarsSize,\r\n      scrollbarsOverlaid: _nativeScrollbarsOverlaid,\r\n      scrollbarsHiding: _nativeScrollbarsHiding,\r\n      scrollTimeline: _scrollTimeline,\r\n      staticDefaultInitialization: _staticDefaultInitialization,\r\n      staticDefaultOptions: _staticDefaultOptions,\r\n\r\n      getDefaultInitialization: _getDefaultInitialization,\r\n      setDefaultInitialization: _setDefaultInitialization,\r\n      getDefaultOptions: _getDefaultOptions,\r\n      setDefaultOptions: _setDefaultOptions,\r\n    }\r\n  );\r\n};\r\nOverlayScrollbars.nonce = setNonce;\r\nOverlayScrollbars.trustedTypePolicy = setTrustedTypePolicy;\r\n"], "names": ["createCache", "options", "update", "_initialValue", "_equal", "_alwaysUpdateValues", "_value", "_previous", "cacheUpdateContextual", "newValue", "force", "curr", "newVal", "changed", "cacheUpdateIsolated", "getCurrentCache", "<PERSON><PERSON><PERSON><PERSON>", "window", "HTMLElement", "document", "wnd", "mathMax", "Math", "max", "mathMin", "min", "mathRound", "round", "mathAbs", "abs", "mathSign", "sign", "cAF", "cancelAnimationFrame", "rAF", "requestAnimationFrame", "setT", "setTimeout", "clearT", "clearTimeout", "getApi", "name", "undefined", "MutationObserverConstructor", "IntersectionObserverConstructor", "ResizeObserverConstructor", "scrollT", "isUndefined", "obj", "isNull", "type", "Object", "prototype", "toString", "call", "replace", "toLowerCase", "isNumber", "isString", "isBoolean", "isFunction", "isArray", "Array", "isObject", "isArrayLike", "length", "lengthCorrectFormat", "isPlainObject", "constructor", "isHTMLElement", "isElement", "Element", "animationCurrentTime", "performance", "now", "animateNumber", "from", "to", "duration", "onFrame", "easing", "animationFrameId", "timeStart", "finalDuration", "frame", "complete", "timeNow", "timeElapsed", "stopAnimation", "percent", "progress", "animationCompleted", "each", "source", "callback", "i", "keys", "key", "inArray", "arr", "item", "indexOf", "concat", "a", "b", "push", "array", "items", "arrayIsSingleItem", "apply", "createOrKeepArray", "value", "isEmptyArray", "deduplicateArray", "Set", "runEachAndClear", "args", "keep", "runFn", "fn", "strPaddingTop", "strPaddingRight", "strPaddingLeft", "strPaddingBottom", "strMarginLeft", "strMarginRight", "strMarginBottom", "strOverflowX", "strOverflowY", "str<PERSON>idth", "strHeight", "strVisible", "strH<PERSON>den", "strScroll", "capitalizeFirstLetter", "str", "finalStr", "String", "toUpperCase", "slice", "equal", "props", "propMutation", "result", "prop", "compareA", "compareB", "equalWH", "equalXY", "equalTRBL", "noop", "bind", "_len", "arguments", "_key", "selfClearTimeout", "timeout", "id", "setTFn", "clearTFn", "debounce", "functionToDebounce", "_ref", "_timeout", "_max<PERSON><PERSON>y", "_leading", "_mergeParams", "maxTimeoutId", "prevArguments", "latestArguments", "leadingInvoked", "clear", "invokeFunctionToDebounce", "this", "mergeParms", "flush", "debouncedFn", "finalTimeout", "hasTimeout", "finalMaxWait", "hasMaxWait", "setTimeoutFn", "clearTimeoutFn", "mergeParamsResult", "invoked<PERSON>rgs", "boundInvoke", "timeoutId", "_flush", "hasOwnProperty", "assignDeep", "target", "object1", "object2", "object3", "object4", "object5", "object6", "sources", "_", "copy", "copyIsArray", "src", "clone", "removeUndefinedProperties", "deep", "isEmptyObject", "capNumber", "number", "getDomTokensArray", "tokens", "split", "filter", "token", "getAttr", "elm", "attrName", "getAttribute", "hasAttr", "hasAttribute", "setAttrs", "attrNames", "setAttribute", "removeAttrs", "removeAttribute", "domTokenListAttr", "initialArr", "setElmAttr", "domTokenListOperation", "operationTokens", "operation", "initialArrSet", "join", "_remove", "removeTokens", "_add", "addTokens", "_has", "hasTokens", "tokenSet", "reduce", "boolean", "includes", "removeAttrClass", "addAttrClass", "addRemoveAttrClass", "add", "hasAttrClass", "createDomTokenListClass", "removeClass", "className", "addClass", "find", "selector", "rootElm", "querySelectorAll", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "is", "matches", "isBodyElement", "contents", "childNodes", "parent", "parentElement", "closest", "getFocusedElement", "doc", "activeElement", "liesBetween", "highBoundarySelector", "deepBoundarySelector", "closestHighBoundaryElm", "closestDeepBoundaryElm", "deepBoundaryIsValid", "removeElements", "nodes", "node", "parentElm", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON><PERSON>", "children", "child", "append<PERSON><PERSON><PERSON>", "trustedTypePolicy", "getTrustedTypePolicy", "setTrustedTypePolicy", "newTrustedTypePolicy", "createDiv", "classNames", "div", "createElement", "createDOM", "html", "createdDiv", "trustedTypesPolicy", "trimmedHtml", "trim", "innerHTML", "createHTML", "getCSSVal", "computedStyle", "getPropertyValue", "validFiniteNumber", "notNaN", "isFinite", "parseToZeroOrNumber", "parseFloat", "roundCssNumber", "numberToCssPx", "setStyles", "styles", "rawValue", "elmStyle", "style", "setProperty", "_unused", "getStyles", "pseudoElm", "getSingleStyle", "getStylesResult", "getComputedStyle", "topRightBottomLeft", "propertyPrefix", "propertySuffix", "finalPrefix", "finalSuffix", "top", "right", "bottom", "left", "t", "r", "l", "getTrasformTranslateValue", "isHorizontal", "x", "y", "elementHasDimensions", "offsetWidth", "offsetHeight", "getClientRects", "zeroObj", "w", "h", "getElmWidthHeightProperty", "property", "getWindowSize", "customWnd", "getOffsetSize", "getClientSize", "getScrollSize", "getFractionalSize", "cssWidth", "cssHeight", "getBoundingClientRect", "hasDimensions", "domRectHasDimensions", "rect", "domRectAppeared", "currContentRect", "prevContentRect", "rectHasDimensions", "rectHadDimensions", "removeEventListener", "eventNames", "listener", "capture", "eventName", "addEventListener", "passive", "_passive", "_capture", "once", "_once", "nativeOptions", "map", "finalListener", "evt", "stopPropagation", "preventDefault", "stopAndPrevent", "scrollElementTo", "position", "scrollLeft", "scrollTop", "getElementScroll", "getZeroScrollCoordinates", "_start", "_end", "sanitizeScrollCoordinates", "rawScrollCoordinates", "overflowAmount", "sanitizeAxis", "start", "end", "amount", "newStart", "newEnd", "startAbs", "endAbs", "_sanitizeAxis", "startX", "endX", "_sanitizeAxis2", "startY", "endY", "isDefaultDirectionScrollCoordinates", "_ref2", "getAxis", "getScrollCoordinatesPercent", "_ref3", "currentScroll", "current", "focusElement", "element", "focus", "preventScroll", "manageListener", "createEventListenerHub", "initialEventListeners", "events", "Map", "removeEvent", "eventSet", "get", "currListener", "for<PERSON>ach", "addEvent", "nameOrEventListeners", "listenerOrPure", "set", "eventListenerKeys", "offFns", "eventListener", "triggerEvent", "event", "pluginModules", "staticPluginModuleInstances", "addPlugins", "addedPlugin", "plugin", "registerPluginModuleInstances", "staticObj", "instanceInfo", "_plugin$name", "os<PERSON>tat<PERSON>", "static", "osInstance", "instance", "instanceObj", "instancePluginMap", "ctor", "getStaticPluginModuleInstance", "pluginModuleName", "_extends", "module", "exports", "assign", "n", "e", "__esModule", "optionsTemplateTypes", "string", "object", "function", "null", "validateRecursive", "template", "doWriteErrors", "prop<PERSON>ath", "validatedOptions", "optionsCopy", "optionsValue", "templateValue", "templateIsComplex", "propPrefix", "_validateRecursive", "validated", "foreign", "<PERSON><PERSON><PERSON><PERSON>", "errorEnumStrings", "errorPossibleTypes", "optionsValueType", "templateValueArr", "currTemplateType", "typeString", "isEnumString", "enumStringSplit", "possibility", "console", "warn", "validateOptions", "optionsValidationPluginModuleName", "numberAllowedValues", "oTypes", "booleanAllowedV<PERSON>ues", "arrayNullV<PERSON>ues", "overflowAllowedValues", "scrollbarsVisibilityAllowedValues", "scrollbarsAutoHideAllowedValues", "scrollbarsClickScrollAllowedValues", "optionsTemplate", "paddingAbsolute", "showNativeOverlaidScrollbars", "elementEvents", "attributes", "ignoreMutation", "overflow", "scrollbars", "theme", "visibility", "autoHide", "autoHideDelay", "autoHideSuspend", "dragScroll", "clickScroll", "pointers", "_validateOptions", "dataAttributePrefix", "classNameEnvironment", "classNameEnvironmentScrollbarHidden", "dataAttributeInitialize", "dataValueNoClipping", "dataAttributeHtmlBody", "dataAttributeHost", "dataValueHostIsHost", "dataAttributeViewport", "dataValueViewportOverflowXPrefix", "dataValueViewportOverflowYPrefix", "dataValueViewportArrange", "dataValueViewportMeasuring", "dataValueViewportScrolling", "dataValueViewportScrollbarHidden", "dataValueViewportNoContent", "dataAttributePadding", "dataAttributeContent", "classNameSizeObserver", "classNameSizeObserverAppear", "classNameSizeObserverListener", "classNameSizeObserverListenerScroll", "classNameSizeObserverListenerItem", "classNameSizeObserverListenerItemFinal", "classNameTrinsicObserver", "classNameScrollbarThemeNone", "classNameScrollbar", "classNameScrollbarRtl", "classNameScrollbarHorizontal", "classNameScrollbarVertical", "classNameScrollbarTrack", "classNameScrollbarHandle", "classNameScrollbarVisible", "classNameScrollbarCornerless", "classNameScrollbarInteraction", "classNameScrollbarUnusable", "classNameScrollbarAutoHide", "classNameScrollbarAutoHideHidden", "classNameScrollbarWheel", "classNameScrollbarTrackInteractive", "classNameScrollbarHandleInteractive", "sizeObserverPluginName", "SizeObserverPlugin", "listenerElement", "onSizeChangedCallback", "observeAppearChange", "_setStyles", "scrollAmount", "scrollEventName", "observer<PERSON>lement<PERSON><PERSON><PERSON><PERSON>", "observerElementChildrenRoot", "shrinkElement", "<PERSON><PERSON><PERSON><PERSON>", "expandElement", "<PERSON><PERSON><PERSON><PERSON>", "expandElementChild", "cacheSize", "currSize", "isDirty", "rAFId", "reset", "onResized", "appear", "onScroll", "scrollEvent", "destroyFns", "getShowNativeOverlaidScrollbars", "checkOption", "env", "_nativeScrollbarsOverlaid", "_checkOption", "showNativeOverlaidScrollbarsOption", "showNativeOverlaidScrollbarsChanged", "overflowIsVisible", "overflowBehavior", "createViewportOverflowState", "hasOverflow", "getAxisOverflowStyle", "axisBehavior", "axisHasOverflow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perpendicularOverflow", "behaviorStyle", "axisOverflowVisible", "perpendicularOverflowVisible", "nonPerpendicularOverflow", "nonOverflow", "_overflowStyle", "_overflowScroll", "scrollbarsHidingPluginName", "ScrollbarsHidingPlugin", "_viewportArrangement", "structureSetupElements", "structureSetupState", "observersSetupState", "checkOptions", "_viewportIsTarget", "_viewport", "_nativeScrollbarsHiding", "_nativeScrollbarsSize", "doViewportArrange", "_getShowNativeOverlai", "readViewportOverflowState", "getStatePerAxis", "styleKey", "overflowStyle", "overflowScroll", "_getStatePerAxis", "xOverflowStyle", "xOverflowScroll", "_getStatePerAxis2", "yOverflowStyle", "yOverflowScroll", "_getViewportOverflowHideOffset", "viewportOverflowState", "arrangeHideOffset", "getHideOffsetPerAxis", "isOverlaid", "nativeScrollbarSize", "nonScrollbarStylingHideOffset", "scrollbarsHideOffset", "scrollbarsHideOffsetArrange", "_getHideOffsetPerAxis", "xScrollbarsHideOffset", "xScrollbarsHideOffsetArrange", "_getHideOffsetPerAxis2", "yScrollbarsHideOffset", "yScrollbarsHideOffsetArrange", "_scrollbarsHideOffset", "_scrollbarsHideOffsetArrange", "_hideNativeScrollbars", "viewportArrange", "_directionIsRTL", "_assignDeep", "viewportStyleObj", "_getViewportOverflowH", "arrangeX", "arrangeY", "hideOffsetX", "hideOffsetY", "_viewportPaddingStyle", "horizontalMarginKey", "viewportHorizontalPaddingKey", "horizontalMarginValue", "verticalMarginValue", "horizontalPaddingValue", "verticalPaddingValue", "_arrangeViewport", "viewportScrollSize", "sizeFraction", "_getViewportOverflowH2", "viewportArrangeHorizontalPaddingKey", "viewportArrangeHorizontalPaddingValue", "viewportArrangeVerticalPaddingValue", "paddingTop", "fractionalContentWidth", "fractionalContenHeight", "arrangeSize", "_undoViewportArrange", "finalViewportOverflowState", "viewportPaddingStyle", "_getViewportOverflowH3", "finalPaddingStyle", "assignProps", "prevStyle", "add<PERSON><PERSON><PERSON>", "clickScrollPluginModuleName", "ClickScrollPlugin", "moveHandleRelative", "targetOffset", "handleLength", "onClickScrollCompleted", "stopped", "stopPressAnimation", "linearScrollMs", "easedScrollMs", "_selfClearTimeout", "setPressAnimationTimeout", "clearPressAnimationTimeout", "targetOffsetSign", "handleLengthWithTargetSign", "handleLengthWithTargetSignHalf", "easedEndPressAnimation", "linearPressAnimation", "linearFrom", "msFactor", "completed", "stopClickAnimation", "clickAnimationProgress", "clickAnimationCompleted", "remainingScrollDistance", "continueWithPress", "remainingLinearScrollDistance", "linearBridge", "stopClick", "opsStringify", "JSON", "stringify", "val", "getPropByPath", "path", "o", "defaultOptions", "getOptionsDiff", "currOptions", "newOptions", "diff", "optionsKeys", "optionKey", "currOptionValue", "newOptionValue", "isDiff", "createOptionCheck", "changedOptions", "nonce", "getNonce", "setNonce", "newNonce", "environmentInstance", "createEnvironment", "getNativeScrollbarSize", "measureElm", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "body", "cSize", "oSize", "fSize", "getNativeScrollbarsHiding", "testElm", "revertClass", "envStyle", "envDOM", "envElm", "env<PERSON><PERSON><PERSON><PERSON><PERSON>", "styleElm", "_createEventListenerH", "_createCache", "updateNativeScrollbarSizeCache", "getNativeScrollbarSizeCache", "_getNativeScrollbarSi", "nativeScrollbarsSize", "nativeScrollbarsHiding", "nativeScrollbarsOverlaid", "staticDefaultInitialization", "elements", "host", "padding", "viewport", "content", "slot", "cancel", "staticDefaultOptions", "getDefaultOptions", "getDefaultInitialization", "_scrollTimeline", "_addResizeListener", "_getDefaultInitialization", "_setDefaultInitialization", "newInitializationStrategy", "_getDefaultOptions", "_setDefaultOptions", "newDefaultOptions", "_staticDefaultInitialization", "_staticDefaultOptions", "matchMedia", "addZoomListener", "onZoom", "media", "devicePixelRatio", "_updateNativeScrollba", "updatedNativeScrollbarSize", "nativeScrollbarSizeChanged", "getEnvironment", "createEventContentChange", "eventContentChange", "destroyed", "WeakMap", "destroy", "updateElements", "getElements", "eventElmList", "entries", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contains", "removeListener", "delete", "createDOMObserver", "isContentObserver", "isConnected", "_attributes", "_styleChangingAttributes", "_eventContentChange", "_nestedTargetSelector", "_ignoreTarget<PERSON><PERSON>e", "_ignoreContentChange", "debouncedEventContentChange", "_createEventContentCh", "destroyEventContentChange", "updateEventContentChangeElements", "finalAttributes", "finalStyleChangingAttributes", "observedAttributes", "observerCallback", "fromRecords", "mutations", "ignoreTargetChange", "ignoreContentChange", "totalChangedNodes", "targetChangedAttrs", "targetStyleChanged", "contentChanged", "mutation", "attributeName", "<PERSON><PERSON><PERSON><PERSON>", "oldValue", "addedNodes", "removedNodes", "isAttributesType", "isChildListType", "targetIsMutationTarget", "isAttrChange", "attributeValue", "attributeChanged", "styleChangingAttrChanged", "contentAttrChanged", "isNestedTarget", "baseAssertion", "contentFinalChanged", "mutationObserver", "observe", "attributeOldValue", "attributeFilter", "subtree", "childList", "characterData", "disconnect", "takeRecords", "createSizeObserver", "_appear", "sizeObserverPlugin", "updateResizeObserverContentRectCache", "baseElements", "sizeObserver", "onSizeChangedCallbackProxy", "sizeChangedContext", "isResizeObserverCall", "ResizeObserverEntry", "skip", "_updateResizeObserver", "contentRect", "currRContentRect", "_sizeChanged", "resizeObserverInstance", "pop", "_sizeObserverPlugin", "pluginAppearCallback", "pluginDestroyFns", "createTrinsicObserver", "onTrinsicChangedCallback", "intersectionObserverInstance", "isHeightIntrinsic", "ioEntryOrSize", "isIntersecting", "intersectionRatio", "trinsicObserver", "updateHeightIntrinsicCache", "triggerOnTrinsicChangedCallback", "updateValue", "heightIntrinsic", "heightIntrinsicChanged", "intersectionObserverCallback", "root", "onSizeChanged", "newSize", "createObserversSetup", "getCurrentOption", "onObserversUpdated", "debounceTimeout", "debounceMaxDelay", "updateContentMutationObserver", "destroyContentMutationObserver", "prevDirectionIsRTL", "hostSelector", "viewportSelector", "baseStyleChangingAttrs", "_target", "_host", "_scrollOffsetElement", "_content", "_isBody", "_viewportHasClass", "_viewportAddRemoveClass", "_removeScrollObscuringStyles", "getDirectionIsRTL", "state", "_heightIntrinsic", "scrollbarsHidingPlugin", "viewportIsTargetBody", "noClipping", "isArranged", "scrollOffset", "revertScrollObscuringStyles", "revertMeasuring", "redoViewportArrange", "viewportScroll", "fractional", "updateContentSizeCache", "onObserversUpdatedDebounced", "prev", "prevObj", "currObj", "setDirection", "updateHints", "newDirectionIsRTL", "_directionChanged", "onTrinsicChanged", "heightIntrinsicCache", "_heightIntrinsicChanged", "exclusiveSizeChange", "updateFn", "onContentMutation", "contentChangedThroughEvent", "_updateContentSizeCac", "_contentMutation", "onHostMutation", "_hostMutation", "constructTrinsicObserver", "updateTrinsicObserver", "constructSizeObserver", "_createDOMObserver", "constructHostMutationObserver", "updateHostMutationObserver", "viewportIsTargetResizeObserver", "onWindowResizeDebounced", "_updateContentSizeCac2", "destroySizeObserver", "destroyTrinsicObserver", "destroyHostMutationObserver", "removeResizeListener", "_scrollbarSizeChanged", "_takeRecords", "_force", "_checkOption2", "_checkOption3", "attributesChanged", "_checkOption4", "elementEventsChanged", "_checkOption5", "debounceValue", "debounce<PERSON><PERSON>ed", "contentMutationObserverChanged", "ignoreMutationFromOptions", "_createDOMObserver2", "ignore", "construct", "max<PERSON><PERSON>", "hostUpdateResult", "trinsicUpdateResult", "contentUpdateResult", "resolveInitialization", "staticInitializationElement", "fallbackStaticInitializationElement", "defaultStaticInitializationElement", "staticInitializationElementValue", "staticInitialization", "resolvedInitialization", "dynamicInitializationElement", "fallbackDynamicInitializationElement", "defaultDynamicInitializationElement", "dynamicInitializationElementValue", "dynamicInitialization", "cancelInitialization", "isBody", "cancelInitializationValue", "_getEnvironment", "_getDefaultInitializa", "defaultNativeScrollbarsOverlaid", "defaultbody", "resolvedNativeScrollbarsOverlaid", "resolvedDocumentScrollingElement", "finalNativeScrollbarsOverlaid", "finalDocumentScrollingElement", "createScrollbarsSetupElements", "scrollbarsSetupEvents", "cssCustomPropViewportPercent", "cssCustomPropScrollPercent", "cssCustomPropScrollDirection", "defaultInitScrollbars", "defaultInitScrollbarsSlot", "_targetIsElm", "scrollbarsInit", "initScrollbarsSlot", "horizontalScrollbars", "verticalScrollbars", "evaluatedScrollbarSlot", "generalDynamicInitializationElement", "initScrollTimeline", "axis", "currAnimation", "currAnimationTransform", "timeline", "cancelAnimation", "_setScrollPercentAnimation", "structure", "_scrollCoordinates", "defaultDirectionScroll", "transformArray", "transform", "reverse", "_handle", "animate", "scrollTimeline", "getViewportPercent", "_overflowAmount", "_overflowEdge", "getAxisValue", "axisViewportSize", "axisOverflowAmount", "scrollbarStructureAddRemoveClass", "scrollbarStructures", "action", "scrollbarStructure", "_scrollbar", "scrollbarStyle", "_elmStyle", "scrollbarsAddRemoveClass", "onlyHorizontal", "singleAxis", "runHorizontal", "runVertical", "refreshScrollbarsHandleLength", "viewportPercent", "createScrollbarStyleFn", "axisViewportPercent", "refreshScrollbarsHandleOffset", "scrollPercent", "axisScrollPercent", "_ref4", "refreshScrollbarsScrollCoordinates", "axisIsDefaultDirectionScrollCoordinates", "_ref5", "refreshScrollbarsScrollbarOffset", "isDefaultDirectionScroll", "styleScrollbarPosition", "getTranslateValue", "axisIsDefaultCoordinates", "px", "generateScrollbarDOM", "xyKey", "scrollbarClassName", "scrollbar", "track", "handle", "_track", "generateHorizontalScrollbarStructure", "generateVerticalScrollbarStructure", "appendElements", "_refreshScrollbarsHandleLength", "_refreshScrollbarsHandleOffset", "_refreshScrollbarsScrollCoordinates", "_refreshScrollbarsScrollbarOffset", "_scrollbarsAddRemoveClass", "_horizontal", "_scrollbarStructures", "_clone", "_style", "_vertical", "createScrollbarsSetupEvents", "scrollbarHandlePointerInteraction", "_documentElm", "wheelTimeout", "clearWheelTimeout", "_selfClearTimeout2", "scrollSnapScrollTransitionTimeout", "clearScrollSnapScrollTransitionTimeout", "scrollOffsetElementScrollBy", "coordinates", "scrollBy", "behavior", "createInteractiveScrollEvents", "releasePointerCaptureEvents", "clientXYKey", "widthHeightKey", "leftTopKey", "wh<PERSON>ey", "createRelativeHandleMove", "mouseDownScroll", "invertedScale", "deltaMovement", "_scrollElementTo", "handleTrackDiff", "scrollDeltaPercent", "scrollDelta", "pointerdownCleanupFns", "pointerDownEvent", "isDragScroll", "pointerCaptureElement", "scrollbarOptions", "dragClickScrollOption", "button", "isPrimary", "pointerType", "continuePointerDown", "instantClickScroll", "shift<PERSON>ey", "getHandleRect", "getTrackRect", "getHandleOffset", "handleRect", "trackRect", "axisScale", "pointerDownOffset", "handleCenter", "relativeTrackPointerOffset", "startOffset", "releasePointerCapture", "pointerUpEvent", "pointerupCleanupFns", "pointerId", "nonAnimatedScroll", "pointerMoveEvent", "withoutSnapScrollOffset", "withSnapScrollOffset", "snapScrollDiff", "setPointerCapture", "animateClickScroll", "stopClickScrollAnimation", "wheelScrollBy", "focusedElement", "wheelEvent", "deltaX", "deltaY", "deltaMode", "createScrollbarsSetup", "mouseInHost", "autoHideIsMove", "autoHideIsLeave", "autoHideIsNever", "prevTheme", "instanceAutoHideSuspendScrollDestroyFn", "instanceAutoHideDelay", "hoverablePointerTypes", "isHoverablePointerType", "requestScrollAnimationFrame", "cancelScrollAnimationFrame", "autoHideInstantInteractionTimeout", "clearAutoHideInstantInteractionTimeout", "_selfClearTimeout3", "autoHideSuspendTimeout", "clearAutoHideSuspendTimeout", "_selfClearTimeout4", "auotHideTimeout", "clearAutoHideTimeout", "_createScrollbarsSetu", "manageScrollbarsAutoHideInstantInteraction", "_scrollEventElement", "manageScrollbarsAutoHide", "removeAutoHide", "delayless", "hide", "manageAutoHideSuspension", "onHostMouseEnter", "_observersUpdateHints", "_structureUpdateHints", "_overflowEdgeChanged", "_overflowAmountChanged", "_overflowStyleChanged", "_scrollCoordinatesChanged", "_hasOverflow", "themeChanged", "visibilityChanged", "autoHideChanged", "_checkOption6", "autoHideSuspendChanged", "_checkOption7", "_checkOption8", "dragScrollChanged", "_checkOption9", "clickScrollChanged", "_checkOption10", "overflowChanged", "trulyAppeared", "updateScrollbars", "updateVisibility", "setScrollbarVisibility", "isVisible", "xVisible", "yVisible", "<PERSON><PERSON><PERSON><PERSON>", "createStructureSetupElements", "defaultInitElements", "defaultPaddingInitialization", "defaultViewportInitialization", "defaultContentInitialization", "targetIsElm", "targetStructureInitialization", "initElements", "paddingInitialization", "viewportInitialization", "contentInitialization", "targetElement", "ownerDocument", "doc<PERSON><PERSON>", "documentElement", "getDocumentWindow", "defaultView", "generalStaticInitializationElement", "createNewDiv", "generateViewportElement", "generateContentElement", "elementHasOverflow", "offsetSize", "scrollSize", "overflowX", "overflowY", "possibleViewportElement", "viewportIsTarget", "possibleContentElement", "viewportIsContent", "viewportElement", "hostElement", "paddingElement", "contentElement", "generatedElements", "elementIsGenerated", "originalNonBodyScrollOffsetElement", "scrollOffsetElement", "scrollEventElement", "evaluatedTargetObj", "_padding", "_originalScrollOffsetElement", "_windowElm", "viewportAttributeClassName", "targetContents", "contentSlot", "docWnd", "initActiveElm", "unwrap", "prepareWrapUnwrapFocus", "tabIndexStr", "originalViewportTabIndex", "undoInitWrapUndwrapFocus", "destroyActiveElm", "viewportIsGenerated", "destroyFocusElement", "undoDestroyWrapUndwrapFocus", "createTrinsicUpdateSegment", "_observersState", "createPaddingUpdateSegment", "updatePaddingCache", "currentPaddingCache", "_currentPaddingCache", "paddingChanged", "paddingAbsoluteChanged", "contentMutation", "_updatePaddingCache", "paddingStyleChanged", "_paddingStyle", "_viewportStyle", "paddingRelative", "paddingHorizontal", "paddingVertical", "paddingStyle", "viewportStyle", "_paddingAbsolute", "_paddingStyleChanged", "createOverflowUpdateSegment", "max0", "flowDirectionCanBeNonDefaultMap", "display", "direction", "directionStyle", "flexDirection", "flexDirectionStyle", "endsWith", "writingMode", "writingModeStyle", "flowDirectionStyleArr", "whCacheOptions", "partialXYOptions", "setMeasuringMode", "active", "getMeasuredScrollCoordinates", "flowDirectionStyles", "flowDirectionCanBeNonDefault", "some", "styleName", "styleValue", "originalScrollOffset", "remove<PERSON><PERSON><PERSON><PERSON>nt", "removeScrollBlock", "scrollEventScrollOffset", "isTrusted", "tmp", "getOverflowAmount", "viewportClientSize", "tollerance", "updateSizeFraction", "getCurrentSizeFraction", "_createCache2", "updateViewportScrollSizeCache", "getCurrentViewportScrollSizeCache", "_createCache3", "updateOverflowAmountCache", "getCurrentOverflowAmountCache", "_createCache4", "updateHasOverflowCache", "_createCache5", "updateOverflowEdge", "getCurrentOverflowEdgeCache", "_createCache6", "updateOverflowStyleCache", "_createCache7", "currVal", "newValu", "updateFlowDirectionStyles", "_createCache8", "updateMeasuredScrollCoordinates", "getCurrentMeasuredScrollCoordinates", "createViewportOverflowStyleClassName", "prefix", "setViewportOverflowStyle", "viewportOverflowStyle", "createAllOverflowStyleClassNames", "allOverflowStyleClassNames", "scrollbarsHidingPluginViewportArrangement", "overflowXVisible", "overflowYVisible", "viewportChanged", "sizeFractionCache", "viewportScrollSizeCache", "overflowAmuntCache", "overflowEdgeCache", "_sizeFractionCache", "_viewportScrollSizeCa", "windowInnerSize", "overflowAmountScrollSize", "overflowAmountClientSize", "_overflowEdgeCache", "overflowEdge", "overflowEdgeChanged", "_overflowAmuntCache", "overflowAmountChanged", "_viewportScrollSizeCa2", "viewportScrollSizeChanged", "_sizeFractionCache2", "sizeFractionChanged", "_updateHasOverflowCac", "hasOverflowChanged", "removeClipping", "adjustViewportStyle", "_updateOverflowStyleC", "overflowStyleChanged", "_updateFlowDirectionS", "flowDirectionStylesChanged", "adjustMeasuredScrollCoordinates", "_ref6", "scrollCoordinates", "scrollCoordinatesChanged", "_scrolled", "createStructureSetup", "_createStructureSetup", "appendStructureElements", "canceled", "updateSegments", "updateInfo", "adjustScrollOffset", "updateSegment", "createSetups", "isDestroyed", "onUpdated", "cacheAndOptionsInitialized", "structureSetupCreate", "structureSetupUpdate", "structureSetupCanceled", "_createObserversSetup", "observersUpdateHints", "observersSetupCreate", "observersSetupUpdate", "scrollbarsSetupCreate", "scrollbarsSetupUpdate", "scrollbarsSetupElements", "updateHintsAreTruthy", "hints", "observerUpdateHints", "rawChangedOptions", "_changedOptions", "rawForce", "_cloneScrollbar", "baseUpdateInfoObj", "observersHints", "structureHints", "truthyObserversHints", "truthyStructureHints", "initialScroll", "_observersSetupState", "_structureSetupState", "_structureSetupElements", "_scrollbarsSetupElements", "targetInstanceMap", "addInstance", "removeInstance", "getInstance", "OverlayScrollbars", "eventListeners", "targetIsElement", "instanceTarget", "potentialInstance", "instancePluginModuleInstances", "newOptionsWithoutUndefined", "pluginValidate", "currentOptions", "addPluginEvent", "removePluginEvents", "triggerPluginEvent", "_createEventListenerH2", "addInstanceEvent", "removeInstanceEvents", "triggerInstanceEvent", "_createSetups", "sizeChanged", "directionChanged", "hostMutation", "setupsConstruct", "setupsUpdate", "setupsState", "setupsElements", "setupsCanceled", "pure", "base", "on", "off", "_setupsState", "directionRTL", "_setupsElements$_stru", "_setupsElements$_scro", "translateScrollbarStructure", "translateScrollbarsSetupElement", "scrollbarsSetupElement", "translatedStructure", "scrollbarHorizontal", "scrollbarVertical", "plugins", "isArr", "pluginsToAdd", "valid", "hasElmsFn", "_getEnvironment2", "scrollbarsSize", "scrollbarsOverlaid", "scrollbarsHiding", "setDefaultInitialization", "setDefaultOptions"], "mappings": ";;;;;;;;;;;EAiCO,IAAMA,IAA2B,SAA3BA,YACXC,GACAC;IAEA,IAAQC,IAA+CF,EAA/CE,GAAeC,IAAgCH,EAAhCG,GAAQC,IAAwBJ,EAAxBI;IAC/B,IAAIC,IAAgBH;IACpB,IAAII;IAEJ,IAAMC,IAAsD,SAAtDA,sBAAuDC,GAAUC;MACrE,IAAMC,IAAOL;MAEb,IAAMM,IAASH;MACf,IAAMI,IAAUH,MAAUN,KAAUA,EAAOO,GAAMC,KAAUD,MAASC;MAEpE,IAAIC,KAAWR,GAAqB;QAClCC,IAASM;QACTL,IAAYI;AACd;MAEA,OAAO,EAACL,GAAQO,GAASN;;IAE3B,IAAMO,IAA0C,SAA1CA,oBAA2CJ;MAAM,OACrDF,EAAsBN,EAAQI,GAAQC,IAAYG;AAAM;IAE1D,IAAMK,IAA0C,SAA1CA,gBAA2CL;MAAe,OAAK,EACnEJ,KACEI,GACFH;AACD;IAED,OAAO,EAACL,IAASY,IAAsBN,GAAuBO;AAGhE;EClEO,IAAMC,WAEJC,WAAW,sBAEXC,gBAAgB,iBAErBD,OAAOE;ECJJ,IAAMC,IAAOJ,IAAYC,SAAS;EAClC,IAAMI,IAAUC,KAAKC;EACrB,IAAMC,IAAUF,KAAKG;EACrB,IAAMC,IAAYJ,KAAKK;EAGvB,IAAMC,IAAUN,KAAKO;EACrB,IAAMC,IAAWR,KAAKS;EACtB,IAAMC,IAAMZ,EAAIa;EAChB,IAAMC,IAAMd,EAAIe;EAChB,IAAMC,IAAOhB,EAAIiB;EACjB,IAAMC,IAASlB,EAAImB;ECX1B,IAAMC,IAAS,SAATA,OAAaC;IAAY,cACrBrB,EAAIqB,OAA8B,cACtCrB,EAAIqB,UACJC;AAAS;EAER,IAAMC,IAA8BH,EAAgC;EACpE,IAAMI,IACXJ,EAAoC;EAC/B,IAAMK,IAA4BL,EAA8B;EAChE,IAAMM,IAAUN,EAAwD;ECTxE,IAAMO,IAAc,SAAdA,YAAeC;IAAQ,OAAuBA,WAAQN;AAAS;EAErE,IAAMO,IAAS,SAATA,OAAUD;IAAQ,OAAkBA,MAAQ;AAAI;EAEtD,IAAME,IAAO,SAAPA,KAAQF;IAAQ,OAC3BD,EAAYC,MAAQC,EAAOD,KACpBA,KAAAA,IACHG,OAAOC,UAAUC,SACdC,KAAKN,GACLO,QAAQ,qBAAqB,MAC7BC;AAAa;EAEf,IAAMC,IAAW,SAAXA,SAAYT;IAAQ,cAA2BA,MAAQ;AAAQ;EAErE,IAAMU,IAAW,SAAXA,SAAYV;IAAQ,cAA2BA,MAAQ;AAAQ;EAErE,IAAMW,IAAY,SAAZA,UAAaX;IAAQ,cAA4BA,MAAQ;AAAS;EAExE,IAAMY,IAAa,SAAbA,WAAcZ;IAAQ,cAA4CA,MAAQ;AAAU;EAE1F,IAAMa,IAAU,SAAVA,QAAoBb;IAAQ,OAAsBc,MAAMD,QAAQb;AAAI;EAE1E,IAAMe,IAAW,SAAXA,SAAYf;IAAQ,cACxBA,MAAQ,aAAaa,EAAQb,OAASC,EAAOD;AAAI;EAMnD,IAAMgB,IAAc,SAAdA,YAA4ChB;IACvD,IAAMiB,MAAWjB,KAAOA,EAAIiB;IAC5B,IAAMC,IAAsBT,EAASQ,MAAWA,KAAU,KAAKA,IAAS,KAAK;IAE7E,OAAOJ,EAAQb,OAAUY,EAAWZ,MAAQkB,IACxCD,IAAS,KAAKF,EAASf,KACrBiB,IAAS,KAAKjB,IACd,OACF;AACN;EAMO,IAAMmB,IAAgB,SAAhBA,cAA0BnB;IAAQ,SAC3CA,KAAOA,EAAIoB,gBAAgBjB;AAAM;EAM9B,IAAMkB,IAAgB,SAAhBA,cAAiBrB;IAAQ,OAAyBA,aAAe9B;AAAW;EAMlF,IAAMoD,IAAY,SAAZA,UAAatB;IAAQ,OAAqBA,aAAeuB;AAAO;ECzC7E,IAAMC,IAAuB,SAAvBA;IAAoB,OAASC,YAAYC;AAAK;EAE7C,IAAMC,IAAgB,SAAhBA,cACXC,GACAC,GACAC,GACAC,GACAC;IAEA,IAAIC,IAAmB;IACvB,IAAMC,IAAYV;IAClB,IAAMW,IAAgB9D,EAAQ,GAAGyD;IACjC,IAAMM,IAAQ,SAARA,MAASC;MACb,IAAMC,IAAUd;MAChB,IAAMe,IAAcD,IAAUJ;MAC9B,IAAMM,IAAgBD,KAAeJ;MACrC,IAAMM,IAAUJ,IACZ,IACA,KAAKhE,EAAQ,GAAG6D,IAAYC,IAAgBG,KAAWH,KAAiB;MAC5E,IAAMO,KACHb,IAAKD,MACHhB,EAAWoB,KACRA,EAAOS,GAASA,IAAUN,GAAe,GAAG,GAAGA,KAC/CM,KACNb;MACF,IAAMe,IAAqBH,KAAiBC,MAAY;MAExDV,KAAWA,EAAQW,GAAUD,GAASE;MAEtCV,IAAmBU,IAAqB,IAAIzD,GAAK;QAAA,OAAMkD;;;IAEzDA;IACA,OAAO,SAACC;MACNrD,EAAKiD;MACLI,KAAYD,EAAMC;;AAEtB;EC7BgB,SAAAO,KACdC,GACAC;IAEA,IAAI9B,EAAY6B;MACd,KAAK,IAAIE,IAAI,GAAGA,IAAIF,EAAO5B,QAAQ8B;QACjC,IAAID,EAASD,EAAOE,IAAIA,GAAGF,OAAY;UACrC;;;WAGC,IAAIA;MAETD,KAAKzC,OAAO6C,KAAKH,KAAS,SAACI;QAAG,OAAKH,EAASD,EAAOI,IAAMA,GAAKJ;;;IAEhE,OAAOA;AACT;EAQO,IAAMK,IAAU,SAAVA,QAAoBC,GAAyBC;IAAO,OAC/DD,EAAIE,QAAQD,MAAS;AAAC;EAQjB,IAAME,IAAS,SAATA,OAAaC,GAA2BC;IAAyB,OAAUD,EAAED,OAAOE;AAAE;EAO5F,IAAMC,IAAO,SAAPA,KAAWC,GAAYC,GAAyBC;KACpClD,EAASiD,MAAU3C,EAAY2C,KAClD7C,MAAMV,UAAUqD,KAAKI,MAAMH,GAAOC,KAClCD,EAAMD,KAAKE;IACf,OAAOD;AACT;EAMO,IAAM9B,IAAO,SAAPA,KAAiBuB;IAA2B,OAAKrC,MAAMc,KAAKuB,KAAO;AAAG;EAQ5E,IAAMW,IAAoB,SAApBA,kBAAwBC;IACnC,IAAIlD,EAAQkD;MACV,OAAOA;;IAET,QAAQrD,EAASqD,MAAU/C,EAAY+C,KAASnC,EAAKmC,KAAS,EAACA;AACjE;EAMO,IAAMC,IAAe,SAAfA,aAAgBN;IAA+B,SAAgBA,MAAUA,EAAMzC;AAAM;EAO3F,IAAMgD,IAAmB,SAAnBA,iBAAqCP;IAAQ,OAAQ9B,EAAK,IAAIsC,IAAIR;AAAY;EAQpF,IAAMS,IAAkB,SAAlBA,gBAAmBhB,GAAoBiB,GAAcC;IAEhE,IAAMC,IAAQ,SAARA,MAASC;MAAe,OAAMA,IAAKA,EAAGV,WAAMnE,GAAW0E,KAAQ,MAAM;;IAC3ExB,KAAKO,GAAKmB;KACTD,MAAUlB,EAAclC,SAAS;AACpC;EClHO,IAAMuD,IAAgB;EACtB,IAAMC,IAAkB;EACxB,IAAMC,IAAiB;EACvB,IAAMC,IAAmB;EACzB,IAAMC,IAAgB;EACtB,IAAMC,IAAiB;EACvB,IAAMC,IAAkB;EACxB,IAAMC,IAAe;EACrB,IAAMC,IAAe;EACrB,IAAMC,IAAW;EACjB,IAAMC,IAAY;EAClB,IAAMC,IAAa;EACnB,IAAMC,IAAY;EAClB,IAAMC,KAAY;EAElB,IAAMC,KAAwB,SAAxBA,sBAAyBC;IACpC,IAAMC,IAAWC,OAAOF,KAAO;IAC/B,OAAOC,IAAWA,EAAS,GAAGE,gBAAgBF,EAASG,MAAM,KAAK;AACpE;ECJO,IAAMC,KAAQ,SAARA,MACXrC,GACAC,GACAqC,GACAC;IAEA,IAAIvC,KAAKC,GAAG;MACV,IAAIuC,IAAS;MACbnD,KAAKiD,IAAO,SAACG;QACX,IAAMC,IAAkD1C,EAAEyC;QAC1D,IAAME,IAAkD1C,EAAEwC;QAC1D,IAAIC,MAAaC;UACfH,IAAS;;AAEb;MACA,OAAOA;AACT;IACA,OAAO;AACT;EAQO,IAAMI,KAAU,SAAVA,QAAc5C,GAAoBC;IAAkB,OAC/DoC,GAAsBrC,GAAGC,GAAG,EAAC,KAAK;AAAK;EAQlC,IAAM4C,KAAU,SAAVA,QAAc7C,GAAoBC;IAAkB,OAC/DoC,GAAsBrC,GAAGC,GAAG,EAAC,KAAK;AAAK;EAQlC,IAAM6C,KAAY,SAAZA,UAAa9C,GAAUC;IAAQ,OAAKoC,GAAYrC,GAAGC,GAAG,EAAC,KAAK,KAAK,KAAK;AAAK;EC1DjF,IAAM8C,KAAO,SAAPA,QAAe;ECmCrB,IAAMC,KAAO,SAAPA,KACXhC;IAAgC,KAAAiC,IAAAA,IAAAC,UAAAxF,QAC7BmD,QAAOtD,MAAA0F,IAAAA,IAAAA,YAAAE,IAAA,GAAAA,IAAAF,GAAAE;MAAPtC,EAAOsC,IAAAD,KAAAA,UAAAC;;IAAA,OACcnC,EAAGgC,KAAI1C,MAAPU,GAAE,EAAM,IAACjB,OAAKc;AAAK;EAOtC,IAAMuC,KAAmB,SAAnBA,iBAAoBC;IAC/B,IAAIC;IACJ,IAAMC,IAASF,IAAUxH,IAAOF;IAChC,IAAM6H,IAAWH,IAAUtH,IAASN;IACpC,OAAO,EACL,SAAC8D;MACCiE,EAASF;MAETA,IAAKC,GAAO;QAAA,OAAMhE;AAAYlC,UAAAA,EAAWgG,KAAWA,MAAYA;AAClE,OACA;MAAA,OAAMG,EAASF;;AAEnB;EAOO,IAAMG,KAAW,SAAXA,SACXC,GACAhK;IAEA,IAAAiK,IAAwDjK,KAAW,CAAE,GAA7DkK,IAAQD,EAARC,GAAUC,IAASF,EAATE,GAAWC,IAAQH,EAARG,GAAUC,IAAYJ,EAAZI;IACvC,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC,IAAQrB;IAEZ,IAAMsB,IAA2B,SAA3BA,yBAAqCxD;MACzCuD;MACArI,EAAOiI;MACPG,IAAiBH,IAAeC,SAAgB9H;MAChDiI,IAAQrB;MAGRW,EAAmBpD,MAAMgE,MAAMzD;;IAGjC,IAAM0D,IAAa,SAAbA,WACJnK;MAAoC,OAEpC2J,KAAgBE,IAAgBF,EAAaE,GAAe7J,KAAQA;AAAI;IAE1E,IAAMoK,IAAQ,SAARA;MAEJ,IAAIJ,MAAUrB;QACZsB,EAAyBE,EAAWL,MAAqBA;;;IAI7D,IAAMO,IAAc,SAAdA;MAEJ,IAAM5D,IAAuCxC,EAAK6E;MAClD,IAAMwB,IAAerH,EAAWuG,KAAYA,MAAaA;MACzD,IAAMe,IAAazH,EAASwH,MAAiBA,KAAgB;MAE7D,IAAIC,GAAY;QACd,IAAMC,IAAevH,EAAWwG,KAAaA,MAAcA;QAC3D,IAAMgB,IAAa3H,EAAS0H,MAAiBA,KAAgB;QAC7D,IAAME,IAAeJ,IAAe,IAAI7I,IAAOF;QAC/C,IAAMoJ,IAAiBL,IAAe,IAAI3I,IAASN;QACnD,IAAMuJ,IAAoBT,EAAW1D;QACrC,IAAMoE,IAAcD,KAAqBnE;QACzC,IAAMqE,IAAcb,EAAyBrB,KAAK,GAAGiC;QACrD,IAAIE;QAMJf;QACA,IAAIN,MAAaK,GAAgB;UAC/Be;UACAf,IAAiB;UAEjBgB,IAAYL,GAAa;YAAA,OAAOX,SAAiBhI;AAAU,cAAEuI;AAC/D,eAAO;UAELS,IAAYL,EAAaI,GAAaR;UAEtC,IAAIG,MAAeb;YACjBA,IAAenI,EAAK2I,GAAOI;;AAE/B;QAEAR,IAAQ,SAARA;UAAK,OAASW,EAAeI;AAAoB;QAEjDlB,IAAgBC,IAAkBe;AACpC;QACEZ,EAAyBxD;;;IAG7B4D,EAAYW,IAASZ;IAErB,OAAOC;AACT;ECrIO,IAAMY,KAAiB,SAAjBA,eAAkB5I,GAAUgG;IAA8B,OACrE7F,OAAOC,UAAUwI,eAAetI,KAAKN,GAAKgG;AAAK;EAM1C,IAAMhD,KAAO,SAAPA,KAAQhD;IAAQ,OAAqBA,IAAMG,OAAO6C,KAAKhD,KAAO;AAAE;EA2BtE,IAAM6I,KAAyB,SAAzBA,WACXC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC;IAEA,IAAMC,IAAsB,EAACN,GAASC,GAASC,GAASC,GAASC,GAASC;IAG1E,YAAYN,MAAW,YAAY7I,EAAO6I,QAAalI,EAAWkI;MAChEA,IAAS,CAAA;;IAGXlG,KAAKyG,IAAS,SAACxG;MAEbD,KAAKC,IAAQ,SAACyG,GAAGrG;QACf,IAAMsG,IAAY1G,EAAOI;QAIzB,IAAI6F,MAAWS;UACb,OAAO;;QAGT,IAAMC,IAAc3I,EAAQ0I;QAG5B,IAAIA,KAAQpI,EAAcoI,IAAO;UAC/B,IAAME,IAAMX,EAAO7F;UACnB,IAAIyG,IAAaD;UAGjB,IAAID,MAAgB3I,EAAQ4I;YAC1BC,IAAQ;iBACH,KAAKF,MAAgBrI,EAAcsI;YACxCC,IAAQ,CAAA;;UAIVZ,EAAO7F,KAAkB4F,GAAWa,GAAOH;AAC7C;UACET,EAAO7F,KAAkBuG,IAAcD,EAAK5D,UAAU4D;;AAE1D;AACF;IAGA,OAAOT;AACT;EAEO,IAAMa,KAA4B,SAA5BA,0BAAoDb,GAAWc;IAAc,OACxFhH,KAAKiG,GAAW,IAAIC,KAAS,SAAC/E,GAAOd,GAAKsG;MACxC,IAAIxF,WAAUrE;eACL6J,EAAKtG;aACP,IAAYc,KAAS5C,EAAc4C;QACxCwF,EAAKtG,KAA4B0G,GAA0B5F;;AAE/D;AAAE;EAMG,IAAM8F,KAAgB,SAAhBA,cAAiB7J;IAAQ,QAAegD,GAAKhD,GAAKiB;AAAM;ECpG9D,IAAM6I,KAAY,SAAZA,UAAarL,GAAaF,GAAawL;IAAc,OAChE1L,EAAQI,GAAKD,EAAQD,GAAKwL;AAAQ;ECH7B,IAAMC,KAAoB,SAApBA,kBAAqBC;IAAiB,OACjDhG,GAAkBpD,EAAQoJ,KAAUA,KAAUA,KAAU,IAAIC,MAAM,MAAMC,QAAO,SAACC;MAAK,OAAKA;AAAK;AAAE;EAQ5F,IAAMC,KAAU,SAAVA,QAAWC,GAA6BC;IAAgB,OACnED,KAAOA,EAAIE,aAAaD;AAAS;EAQ5B,IAAME,KAAU,SAAVA,QAAWH,GAA6BC;IAAgB,OACnED,KAAOA,EAAII,aAAaH;AAAS;EAO5B,IAAMI,KAAW,SAAXA,SACXL,GACAM,GACA7G;IAEAnB,KAAKoH,GAAkBY,KAAY,SAACL;MAClCD,KAAOA,EAAIO,aAAaN,GAAU9E,OAAO1B,KAAS;AACpD;AACF;EAOO,IAAM+G,KAAc,SAAdA,YAAeR,GAA6BM;IACvDhI,KAAKoH,GAAkBY,KAAY,SAACL;MAAQ,OAAKD,KAAOA,EAAIS,gBAAgBR;;AAC9E;EAEO,IAAMS,KAAmB,SAAnBA,iBAAoBV,GAA6BC;IAC5D,IAAMU,IAAajB,GAAkBK,GAAQC,GAAKC;IAClD,IAAMW,IAAa3E,GAAKoE,IAAUL,GAAKC;IACvC,IAAMY,IAAwB,SAAxBA,sBAAyBC,GAA4BC;MACzD,IAAMC,IAAgB,IAAIpH,IAAI+G;MAC9BrI,KAAKoH,GAAkBoB,KAAkB,SAAChB;QACxCkB,EAAcD,GAAWjB;AAC3B;MACA,OAAOxI,EAAK0J,GAAeC,KAAK;;IAGlC,OAAO;MACLC,GAAS,SAATA,QAAUC;QAAuB,OAAKP,EAAWC,EAAsBM,GAAc;AAAU;MAC/FC,GAAM,SAANA,KAAOC;QAAoB,OAAKT,EAAWC,EAAsBQ,GAAW;AAAO;MACnFC,GAAM,SAANA,KAAOC;QACL,IAAMC,IAAW9B,GAAkB6B;QACnC,OAAOC,EAASC,QACd,SAACC,GAAS5B;UAAK,OAAK4B,KAAWf,EAAWgB,SAAS7B;AAAM,YACzD0B,EAAS7K,SAAS;AAEtB;;AAEJ;EAQO,IAAMiL,KAAkB,SAAlBA,gBACX5B,GACAC,GACAxG;IAEAiH,GAAiBV,GAAKC,GAAUiB,EAAQzH;IAExC,OAAOwC,GAAK4F,IAAc7B,GAAKC,GAAUxG;AAC3C;EAQO,IAAMoI,KAAe,SAAfA,aACX7B,GACAC,GACAxG;IAEAiH,GAAiBV,GAAKC,GAAUmB,EAAK3H;IACrC,OAAOwC,GAAK2F,IAAiB5B,GAAKC,GAAUxG;AAC9C;EAEO,IAAMqI,KAAqB,SAArBA,mBACX9B,GACAC,GACAxG,GACAsI;IAAa,QACTA,IAAMF,KAAeD,IAAiB5B,GAAKC,GAAUxG;AAAM;EAS1D,IAAMuI,KAAe,SAAfA,aACXhC,GACAC,GACAxG;IAAgB,OACJiH,GAAiBV,GAAKC,GAAUqB,EAAK7H;AAAM;ECxHzD,IAAMwI,KAA0B,SAA1BA,wBAA2BjC;IAA2B,OAAKU,GAAiBV,GAAK;AAAQ;EAexF,IAAMkC,KAAc,SAAdA,YAAelC,GAA6BmC;IACvDF,GAAwBjC,GAAKkB,EAAQiB;AACvC;EAQO,IAAMC,KAAW,SAAXA,SAAYpC,GAA6BmC;IACpDF,GAAwBjC,GAAKoB,EAAKe;IAClC,OAAOlG,GAAKiG,IAAalC,GAAKmC;AAChC;ECxBO,IAAME,KAAO,SAAPA,KAAQC,GAAkBtC;IACrC,IAAMuC,IAAUvC,IAAMhJ,EAAUgJ,MAAQA,IAAMnM;IAC9C,OAAO0O,IAAUjL,EAAKiL,EAAQC,iBAAiBF,MAAa;AAC9D;EAOO,IAAMG,KAAY,SAAZA,UAAaH,GAAkBtC;IAC1C,IAAMuC,IAAUvC,IAAMhJ,EAAUgJ,MAAQA,IAAMnM;IAC9C,OAAO0O,KAAWA,EAAQG,cAAcJ;AAC1C;EAOO,IAAMK,KAAK,SAALA,GAAM3C,GAAwBsC;IAAgB,OACzDtL,EAAUgJ,MAAQA,EAAI4C,QAAQN;AAAS;EAElC,IAAMO,KAAgB,SAAhBA,cAAiB7C;IAAsB,OAAK2C,GAAG3C,GAAK;AAAO;EAsBjE,IAAM8C,KAAW,SAAXA,SAAY9C;IAAsB,OAC7CA,IAAM1I,EAAK0I,EAAI+C,cAAc;AAAE;EAM1B,IAAMC,KAAS,SAATA,OAAUhD;IAAsB,OAAwBA,KAAOA,EAAIiD;AAAa;EAQtF,IAAMC,KAAU,SAAVA,QAAWlD,GAAwBsC;IAAgB,OAC9DtL,EAAUgJ,MAAQA,EAAIkD,QAAQZ;AAAS;EAMlC,IAAMa,KAAoB,SAApBA,kBAAqBC;IAAc,OAAK,SAAkBC;AAAa;EAQ7E,IAAMC,KAAc,SAAdA,YACXtD,GACAuD,GACAC;IAEA,IAAMC,IAAyBP,GAAQlD,GAAKuD;IAC5C,IAAMG,IAAyB1D,KAAOyC,GAAUe,GAAsBC;IACtE,IAAME,IACJT,GAAQQ,GAAwBH,OAA0BE;IAE5D,OAAOA,KAA0BC,IAC7BD,MAA2BzD,KACzB0D,MAA2B1D,KAC1B2D,KACCT,GAAQA,GAAQlD,GAAKwD,IAAuBD,OAC1CE,IACN;AACN;EC5FO,IAAMG,KAAiB,SAAjBA,eAAkBC;IAC7BvL,KAAKkB,EAAkBqK,KAAQ,SAACC;MAC9B,IAAMC,IAAYf,GAAOc;MACzBA,KAAQC,KAAaA,EAAUC,YAAYF;AAC7C;AACF;EAQO,IAAMG,KAAiB,SAAjBA,eAAkBH,GAAyBI;IAAqC,OAC3FjI,GACE2H,IACAE,KACEI,KACA5L,KAAKkB,EAAkB0K,KAAW,SAACC;MACjCA,KAASL,EAAKM,YAAYD;AAC3B;AACJ;EC5BH,IAAIE;EAEG,IAAMC,KAAuB,SAAvBA;IAAoB,OAASD;AAAiB;EACpD,IAAME,KAAuB,SAAvBA,qBAAwBC;IACnCH,KAAoBG;AACtB;ECEO,IAAMC,KAAY,SAAZA,UAAaC;IACxB,IAAMC,IAAM9Q,SAAS+Q,cAAc;IACnCvE,GAASsE,GAAK,SAASD;IACvB,OAAOC;AACT;EAMO,IAAME,KAAY,SAAZA,UAAaC;IACxB,IAAMC,IAAaN;IACnB,IAAMO,IAAqBV;IAC3B,IAAMW,IAAcH,EAAKI;IACzBH,EAAWI,YAAYH,IAClBA,EAA2BI,WAAWH,KACvCA;IAEJ,OAAO3M,KAAKwK,GAASiC,KAAa,SAAC/E;MAAG,OAAK4D,GAAe5D;;AAC5D;ECdA,IAAMqF,KAAY,SAAZA,UAAaC,GAAoC5J;IAAoB,OACzE4J,EAAcC,iBAAiB7J,MAAS4J,EAAc5J,MAAgB;AAAE;EAE1E,IAAM8J,KAAoB,SAApBA,kBAAqB/F;IACzB,IAAMgG,IAAShG,KAAU;IACzB,OAAOiG,SAASD,KAAUA,IAAS;AACrC;EAEA,IAAME,KAAsB,SAAtBA,oBAAuBlM;IAAc,OAAa+L,GAAkBI,WAAWnM,KAAS;AAAI;EAE3F,IAAMoM,KAAiB,SAAjBA,eAAkBpM;IAAa,OAAKzF,KAAKK,MAAMoF,IAAQ,OAAS;AAAK;EAK3E,IAAMqM,KAAgB,SAAhBA,cAAiBrG;IAAc,OAAQoG,GAAeL,GAAkB/F,MAAQ;AAAA;EAE7E,SAAAsG,UACd/F,GACAgG;IAEAhG,KACEgG,KACA1N,KAAK0N,IAAQ,SAACC,GAA4B9Q;MACxC;QACE,IAAM+Q,IAAWlG,EAAImG;QACrB,IAAM1M,IACJ9D,EAAOsQ,MAAa5P,EAAU4P,KAC1B,KACA9P,EAAS8P,KACPH,GAAcG,KACdA;QAER,IAAI9Q,EAAK4D,QAAQ,UAAU;UACzBmN,EAASE,YAAYjR,GAAMsE;;UAE3ByM,EAAS/Q,KAAesE;;AAE5B,QAAE,OAAA4M,IAAO;AACX;AACJ;WAYgBC,UACdtG,GACAgG,GACAO;IAEA,IAAMC,IAAiBpQ,EAAS4P;IAChC,IAAIS,IAAwCD,IAAiB,KAAK;IAElE,IAAIxG,GAAK;MACP,IAAMsF,IAAgBxR,EAAI4S,iBAAiB1G,GAAKuG,MAAcvG,EAAImG;MAClEM,IAAkBD,IACdnB,GAAUC,GAAeU,KACzB1O,EAAK0O,GAAQvE,QAAO,SAAChG,GAAQ9C;QAC3B8C,EAAO9C,KAAO0M,GAAUC,GAAe3M;QACvC,OAAO8C;AACR,UAAEgL;AACT;IACA,OAAOA;AACT;EAQO,IAAME,KAAqB,SAArBA,mBACX3G,GACA4G,GACAC;IAEA,IAAMC,IAAcF,IAAoBA,UAAoB;IAC5D,IAAMG,IAAcF,IAAqBA,MAAAA,IAAmB;IAC5D,IAAMG,IAASF,IAAW,QAAMC;IAChC,IAAME,IAAWH,IAAW,UAAQC;IACpC,IAAMG,IAAYJ,IAAW,WAASC;IACtC,IAAMI,IAAUL,IAAW,SAAOC;IAClC,IAAMtL,IAAS6K,UAAUtG,GAAK,EAACgH,GAAKC,GAAOC,GAAQC;IACnD,OAAO;MACLC,GAAGzB,GAAoBlK,EAAOuL;MAC9BK,GAAG1B,GAAoBlK,EAAOwL;MAC9B/N,GAAGyM,GAAoBlK,EAAOyL;MAC9BI,GAAG3B,GAAoBlK,EAAO0L;;AAElC;EAEO,IAAMI,KAA4B,SAA5BA,0BACX9N,GACA+N;IAAsB,OAGpB/Q,eAAAA,EAASgD,WAAaA,EAAMgO,IAAKhO,MAAAA,EAAMiO,IAAC,OAASF,IAAe,MAAM,OAAG,MAAI/N,IAC/E;AAAA;EC3GF,IAAMkO,KAAuB,SAAvBA,qBAAwB3H;IAAgB,UACzCA,EAAI4H,eAAe5H,EAAI6H,gBAAgB7H,EAAI8H,iBAAiBnR;AAAO;EACxE,IAAMoR,KAAc;IAClBC,GAAG;IACHC,GAAG;;EAGL,IAAMC,KAA4B,SAA5BA,0BACJC,GACAnI;IAAiC,OAEjCA,IACI;MACEgI,GAAIhI,EAAemI,IAAgB;MACnCF,GAAIjI,EAAemI,IAAQ;QAE7BJ;AAAO;EAKN,IAAMK,KAAgB,SAAhBA,cAAiBC;IAAkB,OAC9CH,GAA0B,SAASG,KAAavU;AAAI;EAM/C,IAAMwU,KAAgBrM,GAAKiM,IAAwC;EAQnE,IAAMK,KAAgBtM,GAAKiM,IAAwC;EAQnE,IAAMM,KAAgBvM,GAAKiM,IAAwC;EAQnE,IAAMO,KAAoB,SAApBA,kBAAqBzI;IAChC,IAAM0I,IAAW9C,WAAWU,UAAUtG,GAAKrF,OAAc;IACzD,IAAMgO,IAAY/C,WAAWU,UAAUtG,GAAKpF,OAAe;IAC3D,OAAO;MACLoN,GAAGU,IAAWtU,EAAUsU;MACxBT,GAAGU,IAAYvU,EAAUuU;;AAE7B;EAMO,IAAMC,KAAwB,SAAxBA,sBAAyB5I;IAAgB,OAAcA,EAAI4I;AAAuB;EAMxF,IAAMC,KAAgB,SAAhBA,cAAiB7I;IAAsB,SAChDA,KAAO2H,GAAqB3H;AAAI;EAK7B,IAAM8I,KAAuB,SAAvBA,qBAAwBC;IAAqC,UACrEA,MAASA,EAAKnO,MAAcmO,EAAKpO;AAAW;EAQ1C,IAAMqO,KAAkB,SAAlBA,gBACXC,GACAC;IAEA,IAAMC,IAAoBL,GAAqBG;IAC/C,IAAMG,IAAoBN,GAAqBI;IAC/C,QAAQE,KAAqBD;AAC/B;EC/EO,IAAME,KAAsB,SAAtBA,oBACX7K,GACA8K,GACAC,GACAC;IAEAlR,KAAKoH,GAAkB4J,KAAa,SAACG;MACnCjL,KAAUA,EAAO6K,oBAAoBI,GAAWF,GAA2BC;AAC7E;AACF;EASO,IAAME,KAAmB,SAAnBA,iBACXlL,GACA8K,GACAC,GACA5W;IACgB,IAAAiK;IAChB,IAAM+M,KAAO/M,IAAIjK,KAAWA,EAAQiX,MAAQ,OAAAhN,IAAK;IACjD,IAAM4M,IAAW7W,KAAWA,EAAQkX,KAAa;IACjD,IAAMC,IAAQnX,KAAWA,EAAQoX,KAAU;IAC3C,IAAMC,IAAyC;MAC7CL,SAAAA;MACAH,SAAAA;;IAGF,OAAOvN,GACLpC,GACA6F,GAAkB4J,GAAYW,KAAI,SAACR;MACjC,IAAMS,IACJJ,IACI,SAACK;QACCd,GAAoB7K,GAAQiL,GAAWS,GAAeV;QACtDD,KAAYA,EAASY;AACtB,UACDZ;MAGN/K,KAAUA,EAAOkL,iBAAiBD,GAAWS,GAAeF;MAC5D,OAAO/N,GAAKoN,IAAqB7K,GAAQiL,GAAWS,GAAeV;AACpE;AAEL;EAwBO,IAAMY,KAAkB,SAAlBA,gBAAmBD;IAAU,OAAWA,EAAIC;AAAiB;EAMnE,IAAMC,KAAiB,SAAjBA,eAAkBF;IAAU,OAAWA,EAAIE;AAAgB;EAMjE,IAAMC,KAAiB,SAAjBA,eAAkBH;IAAU,OACtCC,GAAgBD,MAAuBE,GAAeF;AAAkB;EC9FpE,IAAMI,KAAkB,SAAlBA,gBACXvK,GACAwK;IAEA,IAAA5N,IAAiBzG,EAASqU,KAAY;MAAE/C,GAAG+C;MAAU9C,GAAG8C;QAAaA,KAAY,CAAE,GAA3E/C,IAAC7K,EAAD6K,GAAGC,IAAC9K,EAAD8K;IACXvR,EAASsR,OAAOzH,EAAIyK,aAAahD;IACjCtR,EAASuR,OAAO1H,EAAI0K,YAAYhD;AAClC;EAOO,IAAMiD,KAAmB,SAAnBA,iBAAoB3K;IAAgB,OAAoB;MACnEyH,GAAGzH,EAAIyK;MACP/C,GAAG1H,EAAI0K;;AACR;EAKM,IAAME,KAA2B,SAA3BA;IAAwB,OAA6B;MAChEC,GAAQ;QAAEpD,GAAG;QAAGC,GAAG;;MACnBoD,GAAM;QAAErD,GAAG;QAAGC,GAAG;;;AAClB;EAUM,IAAMqD,KAA4B,SAA5BA,0BACXC,GACAC;IAEA,IAAQJ,IAAiBG,EAAjBH,GAAQC,IAASE,EAATF;IAChB,IAAQ9C,IAASiD,EAATjD,GAAGC,IAAMgD,EAANhD;IACX,IAAMiD,IAAe,SAAfA,aAAgBC,GAAeC,GAAaC;MAChD,IAAIC,IAAW9W,EAAS2W,KAASE;MACjC,IAAIE,IAAS/W,EAAS4W,KAAOC;MAE7B,IAAIC,MAAaC,GAAQ;QACvB,IAAMC,IAAWlX,EAAQ6W;QACzB,IAAMM,IAASnX,EAAQ8W;QAEvBG,IAASC,IAAWC,IAAS,IAAIF;QACjCD,IAAWE,IAAWC,IAAS,IAAIH;AACrC;MAGAA,IAAWA,MAAaC,IAAS,IAAID;MAErC,OAAO,EAACA,IAAW,GAAGC,IAAS;;IAGjC,IAAAG,IAAuBR,EAAaL,EAAOpD,GAAGqD,EAAKrD,GAAGO,IAA/C2D,IAAMD,EAAA,IAAEE,IAAIF,EAAA;IACnB,IAAAG,IAAuBX,EAAaL,EAAOnD,GAAGoD,EAAKpD,GAAGO,IAA/C6D,IAAMD,EAAA,IAAEE,IAAIF,EAAA;IAEnB,OAAO;MACLhB,GAAQ;QACNpD,GAAGkE;QACHjE,GAAGoE;;MAELhB,GAAM;QACJrD,GAAGmE;QACHlE,GAAGqE;;;AAGT;EASO,IAAMC,KAAsC,SAAtCA,oCAAmCC;IAGX,IAFnCpB,IAAMoB,EAANpB,GACAC,IAAImB,EAAJnB;IAEA,IAAMoB,IAAU,SAAVA,QAAWf,GAAeC;MAAW,OAAKD,MAAU,KAAKA,KAASC;AAAG;IAE3E,OAAO;MACL3D,GAAGyE,EAAQrB,EAAOpD,GAAGqD,EAAKrD;MAC1BC,GAAGwE,EAAQrB,EAAOnD,GAAGoD,EAAKpD;;AAE9B;EAOO,IAAMyE,KAA8B,SAA9BA,4BAA2BC,GAEtCC;IACE,IAFAxB,IAAMuB,EAANvB,GAAQC,IAAIsB,EAAJtB;IAGV,IAAMoB,IAAU,SAAVA,QAAWf,GAAeC,GAAakB;MAAe,OAC1D9M,GAAU,GAAG,IAAI2L,IAAQmB,MAAYnB,IAAQC,MAAQ;AAAE;IAEzD,OAAO;MACL3D,GAAGyE,EAAQrB,EAAOpD,GAAGqD,EAAKrD,GAAG4E,EAAc5E;MAC3CC,GAAGwE,EAAQrB,EAAOnD,GAAGoD,EAAKpD,GAAG2E,EAAc3E;;AAE/C;EC3HO,IAAM6E,KAAe,SAAfA,aAAgBC;IAC3B,IAAIA,KAAYA,EAAwBC;MACrCD,EAAwBC,MAAM;QAAEC,eAAe;;;AAEpD;ECoCA,IAAMC,KAAiB,SAAjBA,eACJnU,GACA+Q;IAEAjR,KAAKkB,EAAkB+P,IAAW/Q;AACpC;EAEO,IAAMoU,KAAyB,SAAzBA,uBACXC;IAEA,IAAMC,IAAS,IAAIC;IAEnB,IAAMC,IAAsC,SAAtCA,YAAuC7X,GAAMoU;MACjD,IAAIpU,GAAM;QACR,IAAM8X,IAAWH,EAAOI,IAAI/X;QAC5BwX,IAAe,SAACQ;UACd,IAAIF;YACFA,EAASE,IAAe,WAAW,SAASA;;AAE/C,YAAE5D;AACL,aAAO;QACLuD,EAAOM,SAAQ,SAACH;UACdA,EAAS5P;AACX;QACAyP,EAAOzP;AACT;;IAGF,IAAMgQ,IAAgC,SAAhCA,SACJC,GACAC;MAKA,IAAInX,EAASkX,IAAuB;QAClC,IAAML,IAAWH,EAAOI,IAAII,MAAyB,IAAI1T;QACzDkT,EAAOU,IAAIF,GAAsBL;QAEjCN,IAAe,SAACQ;UACd7W,EAAW6W,MAAiBF,EAASlL,IAAIoL;AAC1C,YAAEI;QAEH,OAAOtR,GACL+Q,GACAM,GACAC;AAEJ;MACA,IAAIlX,EAAUkX,MAAmBA;QAC/BP;;MAGF,IAAMS,IAAoB/U,GAAK4U;MAC/B,IAAMI,IAAyB;MAC/BpV,KAAKmV,IAAmB,SAAC9U;QACvB,IAAMgV,IAAiBL,EAAmD3U;QAC1EgV,KAAiBxU,EAAKuU,GAAQL,EAAS1U,GAAKgV;AAC9C;MAEA,OAAO1R,GAAKpC,GAAiB6T;;IAG/B,IAAME,IAAwC,SAAxCA,aAAyCzY,GAAM2E;MACnDxB,KAAKhB,EAAKwV,EAAOI,IAAI/X,MAAQ,SAAC0Y;QAC5B,IAAI/T,MAASJ,EAAaI;UACvB+T,EAA6DtU,MAAM,GAAGO;;UAEtE+T;;AAEL;;IAGFR,EAASR,KAAyB,CAAA;IAElC,OAAO,EAACQ,GAAUL,GAAaY;AACjC;ECFO,IAAME,KAA8C,CAAA;EAGpD,IAAMC,KAA2E,CAAA;EAOjF,IAAMC,KAAa,SAAbA,WAAcC;IACzB3V,KAAK2V,IAAa,SAACC;MAAM,OACvB5V,KAAK4V,IAAQ,SAAClP,GAAGrG;QACfmV,GAAcnV,KAAOuV,EAAOvV;AAC9B;;AAEJ;EAEO,IAAMwV,KAAgC,SAAhCA,8BACXD,GACAE,GACAC;IAIC,OAED3V,GAAKwV,GAAQjE,KAAI,SAAC9U;MAChB,IAAAmZ,IACEJ,EACA/Y,IAFcoZ,IAAQD,EAAhBE,QAA4BC,IAAUH,EAApBI;MAG1B,IAAA9R,IAAgDyR,KAAgB,IAAzDM,IAAW/R,EAAA,IAAEiR,IAAKjR,EAAA,IAAEgS,IAAiBhS,EAAA;MAC5C,IAAMiS,IAAOR,IAAeI,IAAaF;MACzC,IAAIM,GAAM;QACR,IAAMH,IAAWL,IAEXQ,EAQAF,GAAcd,GAAQO,KAEtBS,EAIAT;QACN,QAASQ,KAAqBb,IAA6B5Y,KAAQuZ;AACrE;AACF;AAAE;EAEG,IAAMI,KAAgC,SAAhCA,8BACXC;IAA6D,OAE7DhB,GAA4BgB;AAAmE;;;;;;;;IC9KjG,SAASC;MACP,OAAQC,EAAAC,UAAiBF,WAAWnZ,OAAOsZ,SAAStZ,OAAOsZ,OAAOlT,SAAS,SAAUmT;QACnF,KAAK,IAAIC,IAAI,GAAGA,IAAIlT,UAAUxF,QAAQ0Y,KAAK;UACzC,IAAIjI,IAAIjL,UAAUkT;UAClB,KAAK,IAAIhI,KAAKD;aAAG,CAAG,GAAE9I,eAAetI,KAAKoR,GAAGC,OAAO+H,EAAE/H,KAAKD,EAAEC;;AAC9D;QACD,OAAO+H;SACNH,cAA4B,MAAMA,EAAOC,QAAQ,aAAaD,EAAOC,SAAUF,SAASzV,MAAM,MAAM4C;AACzG;IACA8S,EAAAC,UAAiBF,UAAUC,EAA4BC,QAAAI,IAAA,MAAML,EAAOC,QAAQ,aAAaD,EAAOC;;;;EC6DhG,IAAMK,KAAuD;IAC3D7N,SAAS;IACTjC,QAAQ;IACR+P,QAAQ;IACRpW,OAAO;IACPqW,QAAQ;IACRC,UAAU;IACVC,MAAM;;EAqBR,IAAMC,KAAoB,SAApBA,kBACJC,GACAld,GACAmd,GACAC;IAEA,IAAMC,IAAmC,CAAA;IACzC,IAAMC,IAAWjB,GAAA,CAAA,GAAwBrc;IACzC,IAAM4I,IAAQ7C,GAAKmX,GAAUhQ,QAAO,SAACnE;MAAI,OAAK4C,GAAe3L,GAAS+I;;IAEtEpD,KAAKiD,IAAO,SAACG;MACX,IAAMwU,IAAoBvd,EAAQ+I;MAClC,IAAMyU,IACJN,EAASnU;MACX,IAAM0U,IAAoBvZ,EAAcsZ;MACxC,IAAME,IAAaN,IAAcA,UAAc;MAG/C,IAAIK,KAAqBvZ,EAAcqZ,IAAe;QACpD,IAAAI,IAA6BV,GAC3BO,GACAD,GACAJ,GACAO,IAAa3U,IAJR6U,IAASD,EAAA,IAAEE,IAAOF,EAAA;QAMzBN,EAAiBtU,KAAQ6U;QACzBN,EAAYvU,KAAQ8U;QAEpBlY,KAAK,EAAC2X,GAAaD,MAAmB,SAACvW;UACrC,IAAI8F,GAAc9F,EAAMiC;mBACfjC,EAAMiC;;AAEjB;AACF,aAAO,KAAK0U,GAAmB;QAC7B,IAAIK,IAAU;QACd,IAAMC,IAAkC;QACxC,IAAMC,IAAoC;QAC1C,IAAMC,IAAmBhb,EAAKsa;QAC9B,IAAMW,IACJrX,EAAkB2W;QAEpB7X,KAAKuY,IAAkB,SAACC;UAEtB,IAAIC;UACJzY,KAAKiX,KAAsB,SAAC9V,GAAed;YACzC,IAAIc,MAAUqX;cACZC,IAAapY;;AAEjB;UACA,IAAMqY,IAAevb,EAAYsb;UACjC,IAAIC,KAAgB5a,EAAS8Z,IAAe;YAE1C,IAAMe,IAAkBH,EAAiBlR,MAAM;YAC/C6Q,MAAYQ,EAAgB5O,MAAK,SAAC6O;cAAW,OAAKA,MAAgBhB;;YAGlE/W,EAAKuX,GAAkBO;AACzB;YACER,IAAUlB,GAAqBqB,OAAsBE;;UAIvD3X,EAAKwX,GAAoBK,IAAezB,GAAqBC,SAASuB;UAGtE,QAAQN;AACV;QAEA,IAAIA;UACFT,EAAiBtU,KAAQwU;eACpB,IAAIJ;UACTqB,QAAQC,KAEJ,iBAAef,IAAa3U,IAA0DkV,yDAAAA,EAAiBxV,gBAAsC8U,2BAAAA,0CACpHS,EAAmB1P,KAAK,MAAM7F,gBAAa,aAEpEsV,EAAiB/Z,SAAS,kCACM+Z,EAAiBzP,KAAK,QAClD,QAAA;;eAKHgP,EAAYvU;AACrB;AACF;IAEA,OAAO,EAACsU,GAAkBC;AAC5B;EAYA,IAAMoB,KAAkB,SAAlBA,gBACJxB,GACAld,GACAmd;IAAuB,OACQF,GAAqBC,GAAUld,GAASmd;AAAc;EC9LhF,IAAMwB,KAAoC;oBAEO,SAAA1U;IAAA,OAAAA,IAAA,CAAA,GAAAA,EACrD0U,MAAoC;MACnC9C,QAAQ,SAARA;QACE,IAAM+C,IAAoDC,GAAO/R;QACjE,IAAMgS,IAAsDD,GAAO9P;QACnE,IAAMgQ,IAA+D,EACnEF,GAAOpY,OACPoY,GAAO7B;QAET,IAAMgC,IACJ;QACF,IAAMC,IACJ;QACF,IAAMC,IACJ;QACF,IAAMC,IACJ,EAACL,GAAsBD,GAAOhC;QAEhC,IAAMuC,IAA4C;UAChDC,iBAAiBP;UACjBQ,8BAA8BR;UAC9B7e,QAAQ;YACNsf,eAAeR;YACfS,YAAYT;YACZhV,UAAU,EAAC8U,GAAO/R,QAAQ+R,GAAOpY,OAAOoY,GAAO7B;YAC/CyC,gBAAgB,EAACZ,GAAO9B,UAAU8B,GAAO7B;;UAE3C0C,UAAU;YACR5K,GAAGkK;YACHjK,GAAGiK;;UAELW,YAAY;YACVC,OAAO,EAACf,GAAOhC,QAAQgC,GAAO7B;YAC9B6C,YAAYZ;YACZa,UAAUZ;YACVa,eAAenB;YACfoB,iBAAiBlB;YACjBmB,YAAYnB;YACZoB,aAAaf;YACbgB,UAAU,EAACtB,GAAOpY,OAAOoY,GAAO7B;;;QAUpC,OAAO,SAAChd,GAAyBmd;UAC/B,IAAAiD,IAA6B1B,GAAgBU,GAAiBpf,GAASmd,IAAhES,IAASwC,EAAA,IAAEvC,IAAOuC,EAAA;UACzB,OAAA/D,GAAA,CAAA,GAAYwB,GAAYD;;AAE5B;OACD3T;AACD,IAvDsD;ECZxD,IAAMoW,KAA8C;EAG7C,IAAMC,KAAuB;EAC7B,IAAMC,KAAyCD,KAAuC;EAGtF,IAAME,KAA6BH,KAAgC;EAGnE,IAAMI,KAAsB;EAG5B,IAAMC,KAA2BL,KAA0B;EAG3D,IAAMM,KAAoBN;EAC1B,IAAMO,KAAsB;EAG5B,IAAMC,KAA2BR,KAA8B;EAC/D,IAAMS,KAAmChZ;EACzC,IAAMiZ,KAAmChZ;EACzC,IAAMiZ,KAA2B;EACjC,IAAMC,KAA6B;EACnC,IAAMC,KAA6B;EACnC,IAAMC,KAAmC;EACzC,IAAMC,KAA6B;EAGnC,IAAMC,KAA0BhB,KAA6B;EAG7D,IAAMiB,KAA0BjB,KAA6B;EAG7D,IAAMkB,KAAwB;EAC9B,IAAMC,KAAiCD,KAA8B;EACrE,IAAME,KAAmCF,KAAgC;EACzE,IAAMG,KAAyCD,KAAsC;EACrF,IAAME,KAAuCF,KAAoC;EACjF,IAAMG,KAA4CD,KAAyC;EAG3F,IAAME,KAA2B;EAGjC,IAAMC,KAA8B;EACpC,IAAMC,KAAqB;EAC3B,IAAMC,KAA2BD,KAAwB;EACzD,IAAME,KAAkCF,KAA+B;EACvE,IAAMG,KAAgCH,KAA6B;EACnE,IAAMI,KAA6BJ,KAA0B;EAC7D,IAAMK,KAA8BL,KAA2B;EAC/D,IAAMM,KAA+BN,KAA4B;EACjE,IAAMO,KAAkCP,KAA+B;EAEvE,IAAMQ,KAAmCR,KAAgC;EACzE,IAAMS,KAAgCT,KAA6B;EACnE,IAAMU,KAAgCV,KAA8B;EACpE,IAAMW,KAAsCD,KAAmC;EAC/E,IAAME,KAA6BZ,KAA0B;EAC7D,IAAMa,KAAwCT,KAAqC;EACnF,IAAMU,KAAyCT,KAAsC;EC1CrF,IAAMU,KAAyB;EAEzBC,IAAAA,qBAAsC,SAAA9Y;IAAA,OAAAA,IAAA,CAAA,GAAAA,EAChD6Y,MAAyB;MACxBjH,QACE,SADFA;QAAM,OAEJ,SACEmH,GACAC,GACAC;UACuD,IAAAC;UACvD,IAAMC,IAAe;UACrB,IAAMC,IAAkB;UACxB,IAAMC,IAA0BpR,GAAS,iBACxByP,KAA4DA,6BAAAA,KAAkDC,mBAAAA,KAAmED,+BAAAA,KAAkDC,mBAAAA;UAEpP,IAAM2B,IAA8BD,EAAwB;UAC5D,IAAME,IAAgBD,EAA4BE;UAClD,IAAMC,IAAgBH,EAA4BI;UAClD,IAAMC,IAAqBF,KAAAA,YAAAA,IAAAA,EAAeC;UAE1C,IAAIE,IAAYlO,GAAc4N;UAC9B,IAAIO,IAAWD;UACf,IAAIE,IAAU;UACd,IAAIC;UAEJ,IAAMC,IAAQ,SAARA;YACJrM,GAAgB8L,GAAeN;YAC/BxL,GAAgB4L,GAAeJ;;UAEjC,IAAMc,IAAY,SAAZA,UAAaC;YACjBH,IAAQ;YACR,IAAID,GAAS;cACXF,IAAYC;cACZb,EAAsBkB,MAAW;AACnC;;UAEF,IAAMC,IAAW,SAAXA,SAAYC;YAChBP,IAAWnO,GAAc4N;YACzBQ,KAAWM,MAAgBnb,GAAQ4a,GAAUD;YAE7C,IAAIQ,GAAa;cACf5M,GAAgB4M;cAEhB,IAAIN,MAAYC,GAAO;gBACrBjiB,EAAKiiB;gBACLA,IAAQ/hB,EAAKiiB;AACf;AACF;cACEA,EAAUG,MAAgB;;YAG5BJ;;UAEF,IAAMK,IAAa,EACjBhT,GAAe0R,GAAiBM,IAChCvM,GAAiB2M,GAAeL,GAAiBe,IACjDrN,GAAiByM,GAAeH,GAAiBe;UAGnD3U,GAASuT,GAAiBtB;UAG1BtO,UAAUwQ,IAAkBT,IAAAA,CAAAA,GAAAA,EACzBnb,KAAWob,GAAYD,EACvBlb,KAAYmb,GAAYD;UAG3BlhB,EAAKgiB;UAEL,OAAO,EAACf,IAAsB5Z,GAAK8a,GAAU,SAASH,GAAOK;;AAC9D;OACJra;AAAA,GAtEgD;ECd5C,IAAMsa,KAAkC,SAAlCA,gCAAmCC,GAAsCC;IACpF,IAAQC,IAA8BD,EAA9BC;IACR,IAAAC,IAAkFH,EAChF,iCADKI,IAAkCD,EAAA,IAAEE,IAAmCF,EAAA;IAI9E,OAAO,EACLC,KACEF,EAA0B5P,KAC1B4P,EAA0B3P,GAC5B8P;AAEJ;EAEO,IAAMC,KAAoB,SAApBA,kBAAqBC;IAAwB,OACxDA,EAAiB3e,QAAQ8B,OAAgB;AAAC;EAQrC,IAAM8c,KAA8B,SAA9BA,4BACXC,GACAF;IAEA,IAAMG,IAAuB,SAAvBA,qBACJC,GACAC,GACAC,GACAC;MAQA,IAAMC,IACJJ,MAAiBjd,IACbC,IACCgd,EAAa7hB,QAAW4E,IAAU,KAAK;MAC9C,IAAMsd,IAAsBV,GAAkBK;MAC9C,IAAMM,IAA+BX,GAAkBO;MAGvD,KAAKD,MAAoBE;QACvB,OAAOnd;;MAIT,IAAIqd,KAAuBC;QACzB,OAAOvd;;MAIT,IAAIsd,GAAqB;QACvB,IAAME,IAA2BN,IAAkBld,IAAaC;QAChE,OAAOid,KAAmBE,IACtBC,IACAG;AACN;MAEA,IAAMC,IACJF,KAAgCH,IAAwBpd,IAAaC;MACvE,OAAOid,IACHG,IACAI;;IAGN,IAAMC,IAAiB;MACrB9Q,GAAGoQ,EAAqBH,EAAiBjQ,GAAGmQ,EAAYnQ,GAAGiQ,EAAiBhQ,GAAGkQ,EAAYlQ;MAC3FA,GAAGmQ,EAAqBH,EAAiBhQ,GAAGkQ,EAAYlQ,GAAGgQ,EAAiBjQ,GAAGmQ,EAAYnQ;;IAG7F,OAAO;MACL8Q,GAAAA;MACAC,GAAiB;QACf/Q,GAAG8Q,EAAe9Q,MAAM1M;QACxB2M,GAAG6Q,EAAe7Q,MAAM3M;;;AAG9B;EC9DO,IAAM0d,KAA6B;EAE7BC,IAAAA,qBAA0C,SAAAzM;IAAA,OAAAA,IAAA,CAAA,GAAAA,EACpDwM,MAA6B;MAC5BjK,QAAQ,SAARA;QAAM,OAAS;UACbmK,GAAsB,SAAtBA,qBACEC,GACAC,GACAC,GACA1B,GACA2B;YAEA,IAAQC,IAAiCJ,EAAjCI,GAAmBC,IAAcL,EAAdK;YAC3B,IAAQC,IAA8E9B,EAA9E8B,GAAyB7B,IAAqDD,EAArDC,GAA2B8B,IAA0B/B,EAA1B+B;YAC5D,IAAMC,KACHJ,MACAE,MACA7B,EAA0B5P,KAAK4P,EAA0B3P;YAC5D,IAAA2R,IAAuCnC,GAAgC6B,GAAc3B,IAA9EnF,IAA4BoH,EAAA;YAKnC,IAAMC,IAA4B,SAA5BA;cACJ,IAAMC,IAAkB,SAAlBA,gBAAmBC;gBACvB,IAAMC,IAAgBnT,UAAU2S,GAAWO;gBAC3C,IAAME,IAAiBD,MAAkB1e;gBAEzC,OAAO,EAAC0e,GAAeC;;cAGzB,IAAAC,IAA0CJ,EAAgB9e,IAAnDmf,IAAcD,EAAA,IAAEE,IAAeF,EAAA;cACtC,IAAAG,IAA0CP,EAAgB7e,IAAnDqf,IAAcD,EAAA,IAAEE,IAAeF,EAAA;cAEtC,OAAO;gBACLvB,GAAgB;kBACd9Q,GAAGmS;kBACHlS,GAAGqS;;gBAELvB,GAAiB;kBACf/Q,GAAGoS;kBACHnS,GAAGsS;;;;YAST,IAAMC,IAAiC,SAAjCA,+BAAkCC;cACtC,IAAQ1B,IAAoB0B,EAApB1B;cACR,IAAM2B,IACJjB,KAA2BjH,IAA+B,IAAI;cAEhE,IAAMmI,IAAuB,SAAvBA,qBACJC,GACAX,GACAY;gBAEA,IAAMC,IAAgCF,IAClCF,IACAG;gBACJ,IAAME,IACJd,MAAmBR,IAA0BqB,IAAgC;gBAC/E,IAAME,IAA8BJ,OAAgBF;gBAEpD,OAAO,EAACK,GAAsBC;;cAGhC,IAAAC,IAA8DN,EAC5D/C,EAA0B5P,GAC1B+Q,EAAgB/Q,GAChB0R,EAAsB1R,IAHjBkT,IAAqBD,EAAA,IAAEE,IAA4BF,EAAA;cAK1D,IAAAG,IAA8DT,EAC5D/C,EAA0B3P,GAC1B8Q,EAAgB9Q,GAChByR,EAAsBzR,IAHjBoT,IAAqBD,EAAA,IAAEE,IAA4BF,EAAA;cAM1D,OAAO;gBACLG,GAAuB;kBACrBvT,GAAGkT;kBACHjT,GAAGoT;;gBAELG,GAA8B;kBAC5BxT,GAAGmT;kBACHlT,GAAGqT;;;;YAYT,IAAMG,IAAwB,SAAxBA,sBACJhB,GAA4Ctd,GAE5Cue;cAC2B,IAFzBC,IAAexe,EAAfwe;cAGF,KAAKpC,GAAmB;gBAAA,IAAAqC;gBACtB,IAAMC,IAAgC/c,GACpC,CAAE,IAAA8c,IAAAA,IAAAA,EAEC9gB,KAAiB,GAAC8gB,EAClB7gB,KAAkB,GAAC6gB,EACnB/gB,KAAgB,GAAC+gB;gBAGtB,IAAAE,IACEtB,EAA+BC,IADzBc,IAAqBO,EAArBP,GAAuBC,IAA4BM,EAA5BN;gBAE/B,IAAWO,IAA0BP,EAA7BxT,GAAgBgU,IAAaR,EAAhBvT;gBACrB,IAAWgU,IAAgCV,EAAnCvT,GAAmBkU,IAAgBX,EAAnBtT;gBACxB,IAAQkU,IAA0B/C,EAA1B+C;gBACR,IAAMC,IAAyCT,IAC3C9gB,IACAC;gBACJ,IAAMuhB,IAAkDV,IACpDhhB,IACAD;gBACJ,IAAM4hB,IAAwBH,EAAsBC;gBACpD,IAAMG,IAAsBJ,EAAsBphB;gBAClD,IAAMyhB,IAAyBL,EAC7BE;gBAEF,IAAMI,IAAuBN,EAAsBvhB;gBAGnDihB,EAAiB3gB,KACfghB,kBAAAA,IAAcI,KAAyB,KACpC;gBACLT,EAAiBO,MAAwBF,IAAcI;gBAGvDT,EAAiB9gB,MAAoBkhB,IAAcM;gBAGnD,IAAIb,GAAiB;kBACnBG,EAAiBQ,KACfG,KAA0BR,IAAWE,IAAc;kBACrDL,EAAiBjhB,KACf6hB,KAAwBV,IAAWE,IAAc;AACrD;gBAEA,OAAOJ;AACT;;YAUF,IAAMa,IAAmB,SAAnBA,iBACJjC,GACAkC,GACAC;cAEA,IAAIjD,GAAmB;gBACrB,IAAQwC,IAA0B/C,EAA1B+C;gBACR,IAAAU,IACErC,EAA+BC,IADzBc,IAAqBsB,EAArBtB,GAAuBC,IAA4BqB,EAA5BrB;gBAE/B,IAAWO,IAA0BP,EAA7BxT,GAAgBgU,IAAaR,EAAhBvT;gBACrB,IAAWgU,IAAgCV,EAAnCvT,GAAmBkU,IAAgBX,EAAnBtT;gBACxB,IAAQ0T,IAAoBtC,EAApBsC;gBACR,IAAMmB,IAAyDnB,IAC3DjhB,IACAC;gBACJ,IAAMoiB,IAAwCZ,EAC5CW;gBAEF,IAAME,IAAsCb,EAAsBc;gBAClE,IAAMC,IAAyBP,EAAmBpU,IAAIqU,EAAarU;gBACnE,IAAM4U,IAAyBR,EAAmBnU,IAAIoU,EAAapU;gBACnE,IAAM4U,IAAc;kBAClB7U,GACE2T,KAAeF,IAETE,IAAcgB,IAAyBH,IACzC,OACA;kBACNvU,GACEyT,KAAeF,IAETE,IAAckB,IAAyBH,IAEzC,OAAA;;gBAGR1W,UAAUkT,GAAW;kBACnB,YAAY4D,EAAY7U;kBACxB,YAAY6U,EAAY5U;;AAE5B;cAEA,OAAOmR;;YAUT,IAAM0D,IAAuB,SAAvBA,qBAAwB5C;cAC5B,IAAId,GAAmB;gBACrB,IAAM2D,IAA6B7C,KAAyBZ;gBAC5D,IAA+B0D,IAAyBnE,EAAhD+C;gBACR,IAAAqB,IAAyChD,EACvC8C,IADM9B,IAA4BgC,EAA5BhC;gBAGR,IAAWO,IAA0BP,EAA7BxT,GAAgBgU,IAAaR,EAAhBvT;gBACrB,IAAMwV,IAAiC,CAAA;gBACvC,IAAMC,IAAc,SAAdA,YAAe5hB;kBAAe,OAClCjD,KAAKiD,IAAO,SAACG;oBACXwhB,EAAkBxhB,KAChBshB,EAAqBthB;AACzB;AAAE;gBAEJ,IAAI8f;kBACF2B,EAAY,EAAC3iB,GAAiBN,GAAeG;;gBAG/C,IAAIohB;kBACF0B,EAAY,EAAC7iB,GAAeC,GAAgBH,GAAgBD;;gBAG9D,IAAMijB,IAAY9W,UAAU2S,GAAWvgB,GAAKwkB;gBAC5C,IAAMG,IAAazb,GACjBqX,GACAzF,IACAG;gBAGF5N,UAAUkT,GAAWiE;gBAErB,OAAO,EACL;kBACEnX,UACEkT,GACA1a,GACE,CAAA,GACA6e,GACAlC,EACE6B,GACAjE,GACAM;kBAINiE;AACD,mBACDN;AAEJ;cACA,OAAO,EAAC/gB;;YAGV,OAAO;cACLie,GAAAA;cACAkC,GAAAA;cACAW,GAAAA;cACA5B,GAAAA;;AAEJ;;AACD;OACFjP;AAAA,GAhRoD;EC/BhD,IAAMqR,KAA8B;EAE9BC,IAAAA,qBAAqC,SAAA3gB;IAAA,OAAAA,IAAA,CAAA,GAAAA,EAC/C0gB,MAA8B;MAC7B9O,QACE,SADFA;QAAM,OAEJ,SACEgP,GACAC,GACAC,GACAC;UASA,IAAIC,IAAU;UACd,IAAIC,IAAqB7hB;UACzB,IAAM8hB,IAAiB;UACvB,IAAMC,IAAgB;UACtB,IAAAC,IACE3hB,GAAiByhB,IADZG,IAAwBD,EAAA,IAAEE,IAA0BF,EAAA;UAE3D,IAAMG,IAAmBnqB,KAAKS,KAAKgpB;UACnC,IAAMW,IAA6BV,IAAeS;UAClD,IAAME,IAAiCD,IAA6B;UACpE,IAAM1mB,IAAS,SAATA,OAAU+P;YAAS,OAAK,KAAK,IAAIA,MAAM,IAAIA;AAAE;UACnD,IAAM6W,IAAyB,SAAzBA,uBAA0BhnB,GAAcC;YAAU,OACtDF,EAAcC,GAAMC,GAAIwmB,GAAeP,GAAoB9lB;AAAO;UACpE,IAAM6mB,IAAuB,SAAvBA,qBAAwBC,GAAoBC;YAAgB,OAChEpnB,EACEmnB,GACAf,IAAeW,GACfN,IAAiBW,IACjB,SAACrmB,GAAU4G,GAAG0f;cACZlB,EAAmBplB;cAEnB,IAAIsmB;gBACFb,IAAqBS,EAAuBlmB,GAAUqlB;;AAE1D;AACD;UACH,IAAMkB,IAAqBtnB,EACzB,GACA+mB,GACAL,IACA,SAACa,GAAwB5f,GAAG6f;YAC1BrB,EAAmBoB;YAEnB,IAAIC,GAAyB;cAC3BlB,EAAuBC;cAEvB,KAAKA,GAAS;gBACZ,IAAMkB,IAA0BrB,IAAemB;gBAC/C,IAAMG,IACJ/qB,KAAKS,KAAKqqB,IAA0BT,OACpCF;gBAEFY,KACEd,GAAyB;kBACvB,IAAMe,IACJF,IAA0BV;kBAC5B,IAAMa,IACJjrB,KAAKS,KAAKuqB,OAAmCb;kBAE/CN,IAAqBoB,IACjBV,EACEK,GACA5qB,KAAKO,IAAIyqB,KAAiCtB,KAE5CY,EAAuBM,GAAwBnB;AACrD;AACJ;AACF;AACD,cACD/lB;UAGF,OAAO,SAACwnB;YACNtB,IAAU;YAEV,IAAIsB;cACFP;;YAGFT;YACAL;;;AAEH;OACJjhB;AAAA,GAzF+C;EC0ElD,IAAMuiB,KAAe,SAAfA,aAAgB1lB;IAAU,OAC9B2lB,KAAKC,UAAU5lB,IAAO,SAACuF,GAAGsgB;MACxB,IAAIhpB,EAAWgpB;QACb,MAAM;;MAER,OAAOA;AACT;AAAE;EAEJ,IAAMC,KAAgB,SAAhBA,cAAoB7pB,GAAU8pB;IAAY,OAC9C9pB,KACO8pB,KAAAA,GACA5f,MAAM,KACN6B,QAAO,SAACge,GAAG/jB;MAAI,OAAM+jB,KAAKnhB,GAAemhB,GAAG/jB,KAAQ+jB,EAAE/jB,UAAQtG;AAAS,QAAGM,UAC7EN;AAAS;EAmIR,IAAMsqB,KAAkC;IAC7C1N,iBAAiB;IACjBC,8BAA8B;IAC9Brf,QAAQ;MACNsf,eAAe,EAAC,EAAC,OAAO;MACxBxV,UAAU,EAAC,GAAG;MACdyV,YAAY;MACZC,gBAAgB;;IAElBC,UAAU;MACR5K,GAAG;MACHC,GAAG;;IAEL4K,YAAY;MACVC,OAAO;MACPC,YAAY;MACZC,UAAU;MACVC,eAAe;MACfC,iBAAiB;MACjBC,YAAY;MACZC,aAAa;MACbC,UAAU,EAAC,SAAS,SAAS;;;EAI1B,IAAM6M,KAAiB,SAAjBA,eAAqBC,GAAgBC;IAChD,IAAMC,IAAuB,CAAA;IAC7B,IAAMC,IAAc/mB,EAAON,GAAKmnB,IAAannB,GAAKknB;IAIlDtnB,KAAKynB,IAAa,SAACC;MACjB,IAAMC,IAAkBL,EAAYI;MACpC,IAAME,IAAiBL,EAAWG;MAElC,IAAIvpB,EAASwpB,MAAoBxpB,EAASypB,IAAiB;QACzD3hB,GAAYuhB,EAAKE,KAAa,CAAA,GAAYL,GAAeM,GAAiBC;QAE1E,IAAI3gB,GAAcugB,EAAKE;iBACdF,EAAKE;;AAEhB,aAAO,IAAI1hB,GAAeuhB,GAAYG,MAAcE,MAAmBD,GAAiB;QACtF,IAAIE,IAAS;QAEb,IAAI5pB,EAAQ0pB,MAAoB1pB,EAAQ2pB;UACtC;YACE,IAAIf,GAAac,OAAqBd,GAAae;cACjDC,IAAS;;AAEb,YAAE,OAAA9Z,IAAO;;QAGX,IAAI8Z;UAEFL,EAAKE,KAAaE;;AAEtB;AACF;IAEA,OAAOJ;AACT;EAEO,IAAMM,KACX,SADWA,kBAETztB,GACA0tB,GACAjtB;IAAe,OAEjB,SAACosB;MAAI,OAAK,EACRD,GAAc5sB,GAAS6sB,IACvBpsB,KAASmsB,GAAcc,GAAgBb,YAAUpqB;AAClD;AAAA;ECtSH,IAAIkrB;EAEG,IAAMC,KAAW,SAAXA;IAAQ,OAASD;AAAK;EAC5B,IAAME,KAAW,SAAXA,SAAYC;IACvBH,KAAQG;AACV;EC0CA,IAAIC;EAEJ,IAAMC,KAAoB,SAApBA;IACJ,IAAMC,IAAyB,SAAzBA,uBACJC,GACAC,GACAzjB;MAGA4G,GAAepQ,SAASktB,MAAMF;MAC9B5c,GAAepQ,SAASktB,MAAMF;MAE9B,IAAMG,IAAQzY,GAAcsY;MAC5B,IAAMI,IAAQ3Y,GAAcuY;MAC5B,IAAMK,IAAQzY,GAAkBqY;MAEhCzjB,KAASuG,GAAeid;MAExB,OAAO;QACLpZ,GAAGwZ,EAAMhZ,IAAI+Y,EAAM/Y,IAAIiZ,EAAMjZ;QAC7BP,GAAGuZ,EAAMjZ,IAAIgZ,EAAMhZ,IAAIkZ,EAAMlZ;;;IAIjC,IAAMmZ,IAA4B,SAA5BA,0BAA6BC;MACjC,IAAI3lB,IAAS;MACb,IAAM4lB,IAAcjf,GAASgf,GAASlO;MACtC;QACEzX,IACE6K,UAAU8a,GAAS,uBAAyC,UAC5D9a,UAAU8a,GAAS,WAAW,2BAA2B;AAC7D,QAAE,OAAA/a,IAAO;MACTgb;MACA,OAAO5lB;;IAIT,IAAM6lB,IAAerO,MAAAA,KAAuJA,qIAAAA,KAAkEC,gDAAAA,KAAuEA,sCAAAA,KAA2DA,0BAAAA,KAAsJ;IACtgB,IAAMqO,IAAS1c,GAAS,iBACPoO,KAA2CqO,yBAAAA;IAE5D,IAAME,IAASD,EAAO;IACtB,IAAME,IAAcD,EAAOlL;IAC3B,IAAMoL,IAAWF,EAAOpL;IACxB,IAAMkK,IAAQC;IAEd,IAAID;MACFoB,EAASpB,QAAQA;;IAGnB,IAAAqB,IAAmC/U,MAA5BS,IAAQsU,EAAA,IAAI/T,IAAY+T,EAAA;IAC/B,IAAAC,IAAsElvB,EACpE;MACEG,GAAe+tB,EAAuBY,GAAQC;MAC9C3uB,GAAQgJ;OAEVG,GAAK2kB,GAAwBY,GAAQC,GAAa,QAL7CI,IAA8BD,EAAA,IAAEE,IAA2BF,EAAA;IAOlE,IAAAG,IAA+BD,KAAxBE,IAAoBD,EAAA;IAC3B,IAAME,IAAyBd,EAA0BK;IACzD,IAAMU,IAA2B;MAC/Bza,GAAGua,EAAqBva,MAAM;MAC9BC,GAAGsa,EAAqBta,MAAM;;IAEhC,IAAMya,IAA8C;MAClDC,UAAU;QACRC,MAAM;QACNC,UAAUL;QACVM,UAAU,SAAVA,SAAW/jB;UAAM,OAAKyjB,KAA0Bpf,GAAcrE,MAAWA;AAAM;QAC/EgkB,SAAS;;MAEXlQ,YAAY;QACVmQ,MAAM;;MAERC,QAAQ;QACNR,0BAA0B;QAC1BnB,MAAM;;;IAGV,IAAM4B,IAAuBpkB,GAAW,CAAE,GAAEmhB;IAC5C,IAAMkD,IAAoB3mB,GACxBsC,IACA,CAAa,GACbokB;IAEF,IAAME,IAA2B5mB,GAC/BsC,IACA,CAAoB,GACpB4jB;IAGF,IAAM/K,IAAW;MACf+B,GAAuB6I;MACvB3K,GAA2B6K;MAC3BhJ,GAAyB+I;MACzBa,KAAmBttB;MACnButB,GAAoB9mB,GAAKoR,GAAU;MACnC2V,IAA2BH;MAC3BI,IAA2B,SAA3BA,0BAA4BC;QAAyB,OACnD3kB,GAAW4jB,GAA6Be,MACxCL;AAA0B;MAC5BM,IAAoBP;MACpBQ,IAAoB,SAApBA,mBAAqBC;QAAiB,OACpC9kB,GAAWokB,GAAsBU,MAAsBT;AAAmB;MAC5EU,IAA8B/kB,GAAW,CAAE,GAAE4jB;MAC7CoB,IAAuBhlB,GAAW,CAAE,GAAEokB;;IAGxCniB,GAAYghB,GAAQ;IACpB5d,GAAe4d;IAGf9X,GAAiB5V,GAAK,WAAU;MAC9B8Z,EAAa,KAAK;AACpB;IAEA,IACEtX,EAAWxC,EAAI0vB,gBACdvB,OACCC,EAAyBza,MAAMya,EAAyBxa,IAC1D;MACA,IAAM+b,IAAkB,SAAlBA,gBAAmBC;QACvB,IAAMC,IAAQ7vB,EAAI0vB,6BAA2B1vB,EAAI8vB,mBAAgB;QACjEla,GACEia,GACA,WACA;UACED;UACAD,EAAgBC;AAClB,YACA;UACE3Z,GAAO;;;MAIb0Z,GAAgB;QACd,IAAAI,IACEhC,KADKiC,IAA0BD,EAAA,IAAEE,IAA0BF,EAAA;QAG7DtlB,GAAW6Y,EAAI+B,GAAuB2K;QACtClW,EAAa,KAAK,EAACmW;AACrB;AACF;IAEA,OAAO3M;AACT;EAEO,IAAM4M,KAAiB,SAAjBA;IACX,KAAKtD;MACHA,KAAsBC;;IAExB,OAAOD;AACT;ECxGA,IAAMuD,KAA2B,SAA3BA,yBACJzlB,GACAhG,GACA0rB;IAEA,IAAIC,IAAY;IAChB,IAAMla,IAAMia,IAAqB,IAAIE,UAAiC;IACtE,IAAMC,IAAU,SAAVA;MACJF,IAAY;;IAEd,IAAMG,IAAkD,SAAlDA,eAAmDC;MACvD,IAAIta,KAAOia,GAAoB;QAC7B,IAAMM,IAAeN,EAAmBja,KAAI,SAACnR;UAC3C,IAAA8D,IAA+B9D,KAAQ,IAAhCwJ,IAAQ1F,EAAA,IAAE0M,IAAU1M,EAAA;UAC3B,IAAMwlB,IAAW9Y,KAAchH,KAAYiiB,KAAeliB,IAAMC,GAAU9D,KAAU;UACpF,OAAO,EAAC4jB,GAAU9Y;AACpB;QAEAhR,KAAKksB,IAAc,SAAC1rB;UAAI,OACtBR,KAAKQ,EAAK,KAAI,SAACkH;YACb,IAAMsJ,IAAaxQ,EAAK;YACxB,IAAM2rB,IAAUxa,EAAIiD,IAAIlN,MAAQ;YAChC,IAAM0kB,IAAgBlmB,EAAOmmB,SAAS3kB;YAEtC,IAAI0kB,KAAiBpb,GAAY;cAC/B,IAAMsb,IAAiBlb,GAAiB1J,GAAKsJ,IAAY,SAACuE;gBACxD,IAAIsW,GAAW;kBACbS;kBACA3a,EAAI4a,OAAO7kB;AACb;kBACExH,EAASqV;;AAEb;cACA5D,EAAIuD,IAAIxN,GAAK7G,EAAKsrB,GAASG;AAC7B,mBAAO;cACL/qB,EAAgB4qB;cAChBxa,EAAI4a,OAAO7kB;AACb;AACF;;AAEJ;;IAGFskB;IAEA,OAAO,EAACD,GAASC;AACnB;EAUO,IAAMQ,KAAoB,SAApBA,kBACXtmB,GACAumB,GACAvsB,GACA7F;IAEA,IAAIqyB,IAAc;IAClB,IAAA/Y,IAOKtZ,KAAoE,CAAE,GANzEsyB,IAAWhZ,EAAXgZ,IACAC,IAAwBjZ,EAAxBiZ,IACAC,IAAmBlZ,EAAnBkZ,IACAC,IAAqBnZ,EAArBmZ,IACAC,IAAmBpZ,EAAnBoZ,IACAC,IAAoBrZ,EAApBqZ;IAEF,IAAMC,IAA8B7oB,IAClC;MAAA,OAAMsoB,KAAgBxsB,EAAwC;QAC9D;MAAEqE,GAAU;MAAIC,GAAW;;IAE7B,IAAA0oB,IAAsEvB,GACpEzlB,GACA+mB,GACAJ,IAHKM,IAAyBD,EAAA,IAAEE,IAAgCF,EAAA;IAOlE,IAAMG,IAAkBV,KAAe;IACvC,IAAMW,IAA+BV,KAA4B;IACjE,IAAMW,IAAqB7sB,EAAO2sB,GAAiBC;IACnD,IAAME,IAAmB,SAAnBA,iBACJC,GACAC;MAEA,KAAKtsB,EAAassB,IAAY;QAC5B,IAAMC,IAAqBZ,KAAuBrpB;QAClD,IAAMkqB,IAAsBZ,KAAwBtpB;QACpD,IAAMmqB,IAA4B;QAClC,IAAMC,IAA+B;QACrC,IAAIC,IAAsD;QAC1D,IAAIC,IAAkD;QAGtDhuB,KAAK0tB,IAAW,SAACO;UACf,IACEC,IAMED,EANFC,eACQC,IAKNF,EALF/nB,QACA5I,IAIE2wB,EAJF3wB,MACA8wB,IAGEH,EAHFG,UACAC,IAEEJ,EAFFI,YACAC,IACEL,EADFK;UAEF,IAAMC,IAAmBjxB,MAAS;UAClC,IAAMkxB,IAAkBlxB,MAAS;UACjC,IAAMmxB,IAAyBvoB,MAAWioB;UAC1C,IAAMO,IAAeH,KAAoBL;UACzC,IAAMrzB,IACJ6zB,KAAgBjnB,GAAQ0mB,GAA+BD,KAAiB;UAE1E,IAAMS,IAAiB7wB,EAASjD,KAAYA,IAAW;UACvD,IAAM+zB,IAAmBF,KAAgBN,MAAaO;UACtD,IAAME,IACJvuB,EAAQgtB,GAA8BY,MAAkBU;UAG1D,IAAInC,MAAsB+B,MAAoBC,IAAyB;YACrE,IAAMK,IAAqBP,KAAoBK;YAC/C,IAAMG,IACJD,KACAhC,KACAziB,GAAG8jB,GAAgBrB;YACrB,IAAMkC,IAAgBD,KACjBpB,EAAmBQ,GAAgBD,GAAeE,GAAUO,MAC5DJ,KAAoBO;YACzB,IAAMG,IACJD,MAAkBpB,EAAoBK,KAAYc,GAAgB7oB,GAAQ7L;YAE5E2F,KAAKquB,IAAY,SAAC7iB;cAAI,OAAK3K,EAAKgtB,GAAmBriB;;YACnDxL,KAAKsuB,IAAc,SAAC9iB;cAAI,OAAK3K,EAAKgtB,GAAmBriB;;YAErDwiB,IAAiBA,KAAkBiB;AAErC;UAEA,KACGxC,KACDgC,KACAG,MACCjB,EAAmBQ,GAAgBD,GAAgBE,GAAUO,IAC9D;YACA9tB,EAAKitB,GAAoBI;YACzBH,IAAqBA,KAAsBc;AAC7C;AACF;QAGAzB,GAAiC,SAACpjB;UAAgB,OAChD3I,EAAiBwsB,GAAmB1kB,QAAe,SAAC5I,GAAKiL;YACvD3K,EAAKN,GAAKwJ,GAAKC,GAAUwB;YACzB,OAAOnB,GAAGmB,GAAMxB,KAAYnJ,EAAKN,GAAKiL,KAAQjL;AAC/C,cAAE;;QAGL,IAAIksB,GAAmB;WACpBgB,KAAeO,KAAmB9tB,EAAwC;UAC3E,OAAO,EAAC;AAGV;QAEA,KAAKkB,EAAa0sB,MAAuBC,GAAoB;UAC3D,IAAMvsB,IAAO,EACXH,EAAiBysB,IACjBC;WAEDN,KAAgBvtB,EAAuCe,MAAM,GAAGO;UAEjE,OAAOA;AACT;AACF;;IAEF,IAAM0tB,IAAqC,IAAInyB,EAC7C4G,GAAK6pB,GAAkB;IAGzB,OAAO,EACL;MACE0B,EAAiBC,QAAQjpB,GAAQ;QAC/B2T,YAAY;QACZuV,mBAAmB;QACnBC,iBAAiB9B;QACjB+B,SAAS7C;QACT8C,WAAW9C;QACX+C,eAAe/C;;MAEjBC,IAAc;MAEd,OAAO;QACL,IAAIA,GAAa;UACfS;UACA+B,EAAiBO;UACjB/C,IAAc;AAChB;;AAEJ,OACA;MACE,IAAIA,GAAa;QACfO,EAA4BlnB;QAC5B,OAAOynB,EAAiB,MAAM0B,EAAiBQ;AACjD;AACF;AAEJ;ECpQO,IAAMC,KAAqB,SAArBA,mBACXzpB,GACAoX,GACAjjB;IAEA,IAAAiK,IAAyCjK,KAAW,CAAE,GAArCkjB,IAAmBjZ,EAA5BsrB;IACR,IAAMC,IACJrZ,GAAyD2G;IAC3D,IAAAmM,IAA+ClvB,EAAqC;MAClFG,GAAe;MACfE,GAAqB;QAFhBq1B,IAAoCxG,EAAA;IAK3C,OAAO;MACL,IAAM3K,IAA6B;MACnC,IAAMoR,IAAexjB,GAAS,iBACbqP,KAAsCE,mBAAAA;MAEvD,IAAMkU,IAAeD,EAAa;MAClC,IAAM1S,IAAkB2S,EAAahS;MACrC,IAAMiS,IAA6B,SAA7BA,2BAA8BC;QAClC,IAAMC,IAAuBD,aAA8BE;QAE3D,IAAIC,IAAO;QACX,IAAI7R,IAAS;QAGb,IAAI2R,GAAsB;UACxB,IAAAG,IAA8CR,EAC5CI,EAAmBK,cADdC,IAAgBF,EAAA,IAAI1f,IAAe0f,EAAA;UAG1C,IAAM/f,IAAgBC,GAAqBggB;UAC3ChS,IAAS9N,GAAgB8f,GAAkB5f;UAC3Cyf,KAAQ7R,MAAWjO;AACrB;UAGEiO,IAAS0R,MAAuB;;QAGlC,KAAKG;UACH/S,EAAsB;YACpBmT,IAAc;YACdb,IAASpR;;;;MAKf,IAAIvhB,GAA2B;QAC7B,IAAMyzB,IAAyB,IAAIzzB,GAA0B,SAACkvB;UAAO,OACnE8D,EAA2B9D,EAAQwE;;QAErCD,EAAuBvB,QAAQ9R;QAC/Bxc,EAAK8d,IAAY;UACf+R,EAAuBjB;AACzB;AACD,aAAM,IAAII,GAAoB;QAC7B,IAAAe,IAAiDf,EAC/CxS,GACA4S,GACA1S,IAHKsT,IAAoBD,EAAA,IAAEE,IAAgBF,EAAA;QAK7C/vB,EACE8d,GACAje,EACE,EACEoJ,GAASkmB,GAAcnU,KACvBzK,GAAiB4e,GAAc,kBAAkBa,MAEnDC;AAGN;QACE,OAAOptB;;MAGT,OAAOC,GAAKpC,GAAiBV,EAAK8d,GAAYhT,GAAezF,GAAQ8pB;;AAEzE;EC9FO,IAAMe,KAAwB,SAAxBA,sBACX7qB,GACA8qB;IAEA,IAAIC;IACJ,IAAMC,IAAoB,SAApBA,kBAAqBC;MAAqD,OAC7EA,EAA6BxhB,MAAM,KACnCwhB,EAA4CC,kBAC5CD,EAA4CE,oBAAoB;AAAC;IACpE,IAAMC,IAAkBnlB,GAAU+P;IAClC,IAAAoN,IAAqClvB,EAAY;MAC/CG,GAAe;QADVg3B,IAA0BjI,EAAA;IAGjC,IAAMkI,IAAkC,SAAlCA,gCACJC,GACAhE;MAEA,IAAIgE,GAAa;QACf,IAAMC,IAAkBH,EAA2BL,EAAkBO;QACrE,IAASE,IAA0BD,EAAe;QAClD,OACEC,MACClE,KACDuD,EAAyBU,MAAoB,EAACA;AAElD;;IAEF,IAAME,IAA+B,SAA/BA,6BACJnE,GACAtB;MAAoC,OACjCqF,EAAgCrF,EAAQwE,OAAOlD;AAAY;IAEhE,OAAO,EACL;MACE,IAAM9O,IAA6B;MAEnC,IAAI3hB,GAAiC;QACnCi0B,IAA+B,IAAIj0B,EACjC2G,GAAKiuB,GAA8B,QACnC;UAAEC,MAAM3rB;;QAEV+qB,EAA6B9B,QAAQmC;QACrCzwB,EAAK8d,IAAY;UACfsS,EAA8BxB;AAChC;AACF,aAAO;QACL,IAAMqC,IAAgB,SAAhBA;UACJ,IAAMC,IAAU/hB,GAAcshB;UAC9BE,EAAgCO;;QAElClxB,EAAK8d,GAAYgR,GAAmB2B,GAAiBQ,EAApCnC;QACjBmC;AACF;MAEA,OAAOnuB,GAAKpC,GAAiBV,EAAK8d,GAAYhT,GAAezF,GAAQorB;AACvE,OACA;MAAA,OACEL,KACAW,EAA6B,MAAMX,EAA6BvB;;AAEtE;ECrBO,IAAMsC,KAAuB,SAAvBA,qBACX1R,GACAC,GACA0R,GACAC;IAEA,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAI1hB;IACJ,IAAI2hB;IACJ,IAAMC,IAAmBxX,MAAAA,KAAoB;IAI7C,IAAMyX,IAAuBvX,MAAAA,KAAwB;IACrD,IAAMwX,IAAyB,EAAC,MAAM,SAAS,SAAS,QAAQ,QAAQ,QAAQ;IAChF,IACEC,IAUErS,EAVFqS,IACAC,IASEtS,EATFsS,IACAjS,IAQEL,EARFK,GACAkS,IAOEvS,EAPFuS,IACAC,IAMExS,EANFwS,IACApS,IAKEJ,EALFI,GACAqS,IAIEzS,EAJFyS,IACAC,IAGE1S,EAHF0S,IACAC,IAEE3S,EAFF2S,IACAC,IACE5S,EADF4S;IAGF,IAAMC,IAAoB,SAApBA,kBAAqBzrB;MAAgB,OAAcsG,UAAUtG,GAAK,iBAAiB;AAAK;IAE9F,IAAM0rB,IAA6B;MACjCC,IAAkB;MAClBvQ,GAAiBqQ,EAAkBR;;IAErC,IAAM7T,IAAM4M;IACZ,IAAM4H,IAAyB9c,GAC7B2J;IAGF,IAAAmJ,IAAiClvB,EAC/B;MACEI,GAAQ+I;MACRhJ,GAAe;QAAEmV,GAAG;QAAGC,GAAG;;QAE5B;MACE,IAAM6U,IACJ8O,KACAA,EAAuBjT,EACrBC,GACAC,GACA6S,GACAtU,GACAmT,GACAzN;MAEJ,IAAM+O,IAAuBR,KAAWrS;MACxC,IAAM8S,KACHD,KAAwB7pB,GAAakpB,GAAO5X,IAAmBF;MAClE,IAAM2Y,KAAc/S,KAAqBsS,EAAkB3X;MAC3D,IAAMqY,IAAeD,KAAcphB,GAAiBwgB;MACpD,IAAMc,IAA8BD,KAAgBR;MAEpD,IAAMU,IAAkBX,EAAwB3X,IAA4BkY;MAC5E,IAAMK,IAAsBJ,KAAcjP,KAAwBA,IAAuB;MACzF,IAAMsP,IAAiB5jB,GAAcyQ;MACrC,IAAMoT,IAAa5jB,GAAkBwQ;MAErCkT,KAAuBA;MAEvB5hB,GAAgB4gB,GAAsBa;MACtCC,KAA+BA;MAC/BH,KAAcI;MAEd,OAAO;QACLlkB,GAAGokB,EAAepkB,IAAIqkB,EAAWrkB;QACjCC,GAAGmkB,EAAenkB,IAAIokB,EAAWpkB;;AAErC,SAtCKqkB,IAAsB1K,EAAA;IAyC7B,IAAM2K,IAA8B7vB,GAAS8tB,GAAoB;MAC/D3tB,GAAU,SAAVA;QAAQ,OAAQ4tB;AAAe;MAC/B3tB,GAAW,SAAXA;QAAS,OAAQ4tB;AAAgB;MACjC1tB,YAAAA,aAAawvB,GAAMn5B;QACjB,IAAOo5B,IAAWD,EAAI;QACtB,IAAOE,IAAWr5B,EAAI;QACtB,OAAO,EACL2F,EAAON,GAAK+zB,IAAU/zB,GAAKg0B,IAAUjrB,QAAO,SAAC/L,GAAKiD;UAChDjD,EAAIiD,KAAO8zB,EAAQ9zB,MAAgC+zB,EAAQ/zB;UAC3D,OAAOjD;AACT,YAAG,CAAA;AAEP;;IAGF,IAAMi3B,IAAe,SAAfA,aAAgBC;MACpB,IAAMC,IAAoBpB,EAAkBR;MAC5C1sB,GAAWquB,GAAa;QAAEE,IAAmBjC,MAAuBgC;;MACpEtuB,GAAWmtB,GAAO;QAAEtQ,GAAiByR;;MACrChC,IAAqBgC;;IAGvB,IAAME,IAAmB,SAAnBA,iBACJC,GACAjH;MAEA,IAAOiE,IAA2CgD,EAAoB,IAA9C/C,IAA0B+C,EAAoB;MACtE,IAAMJ,IAAyC;QAC7CK,IAAyBhD;;MAG3B1rB,GAAWmtB,GAAO;QAAEC,IAAkB3B;;OACrCjE,KAAeyE,EAAmBoC;MAEnC,OAAOA;;IAGT,IAAMxC,IAAgB,SAAhBA,cAAaxtB;MAA6D,IAAvDmsB,IAAYnsB,EAAZmsB,IAAcb,IAAOtrB,EAAPsrB;MACrC,IAAMgF,IAAsBnE,MAAiBb;MAC7C,IAAMiF,KAIHD,KAAuB9V,EAAI8B,IACxBqT,IACA/B;MAEN,IAAMoC,IAAyC;QAC7C7D,IAAcA,KAAgBb;QAC9BA,IAAAA;;MAGFyE,EAAaC;MAEbO,EAASP;;IAGX,IAAMQ,IAAoB,SAApBA,kBACJC,GACAtH;MAEA,IAAAuH,IAA6BhB,KAApBiB,IAAgBD,EAAA;MACzB,IAAMV,IAAyC;QAC7CW,IAAAA;;MAGFZ,EAAaC;MAGb,IAAMO,IAAWE,IAA6B7C,IAAqB+B;MAEnEgB,MAAqBxH,KAAeoH,EAASP;MAE7C,OAAOA;;IAGT,IAAMY,IAAiB,SAAjBA,eACJpH,GACAC,GACAN;MAEA,IAAM6G,IAAyC;QAC7Ca,IAAepH;;MAGjBsG,EAAaC;MAEb,IAAIvG,MAAuBN;QACzBwG,EAA4BK;;MAQ9B,OAAOA;;IAGT,IAAA3gB,IAA0Dmf,IACtD/B,GAAsB6B,GAAO6B,KAC7B,IAFGW,IAAwBzhB,EAAA,IAAE0hB,IAAqB1hB,EAAA;IAItD,IAAM2hB,KACH5U,KACDiP,GAAmBiD,GAAOd,GAAe;MACvClC,IAAS;;IAGb,IAAA2F,IAAoE/I,GAClEoG,GACA,OACAsC,GACA;MACEtI,IAA0B8F;MAC1B/F,IAAa+F;QANV8C,IAA6BD,EAAA,IAAEE,IAA0BF,EAAA;IAUhE,IAAMG,IACJhV,KACAzjB,KACA,IAAIA,GAA0B,SAACkvB;MAC7B,IAAMxb,IAAkBwb,EAAQA,EAAQ9tB,SAAS,GAAGkyB;MACpDuB,EAAc;QACZrB,IAAc;QACdb,IAASlf,GAAgBC,GAAiBC;;MAE5CA,IAAkBD;AACpB;IACF,IAAMglB,IAA0BvxB,IAC9B;MACE,IAAAwxB,IAA6B5B,KAApBiB,IAAgBW,EAAA;MACzB1D,EAAmB;QAAE+C,IAAAA;;AACvB,QACA;MACE1wB,GAAU;MACVE,GAAU;;IAId,OAAO,EACL;MAGEixB,KAAkCA,EAA+BvG,QAAQyD;MACzE,IAAMiD,IAAsBP,KAAyBA;MACrD,IAAMQ,IAAyBV,KAA4BA;MAC3D,IAAMW,IAA8BP;MACpC,IAAMQ,IAAuBlX,EAAI2L,GAAmB,SAACwL;QACnD,IAAIA;UACFhC,EAA4B;YAAEgC,IAAAA;;;UAE9BN;;AAEJ;MAEA,OAAO;QACLD,KAAkCA,EAA+BjG;QACjEoG,KAAuBA;QACvBC,KAA0BA;QAC1BxD,KAAkCA;QAClCyD;QACAC;;AAEH,OACD,SAAAliB;MAA2C,IAAxCkL,IAAYlL,EAAZkL,IAAckX,IAAYpiB,EAAZoiB,IAAcC,IAAMriB,EAANqiB;MAC7B,IAAM7B,IAAyC,CAAA;MAE/C,IAAA8B,IAAyBpX,EAAa,0BAA/BlF,IAAcsc,EAAA;MACrB,IAAAC,IAAwCrX,EAAa,sBAA9CnF,IAAUwc,EAAA,IAAEC,IAAiBD,EAAA;MACpC,IAAAE,IAA8CvX,EAAa,yBAApDpF,IAAa2c,EAAA,IAAEC,IAAoBD,EAAA;MAC1C,IAAAE,IAAyCzX,EAAa,oBAA/C0X,IAAaD,EAAA,IAAEE,IAAeF,EAAA;MACrC,IAAMG,IAAiCJ,KAAwBF;MAC/D,IAAM5G,IAAcwG,KAAgBC;MACpC,IAAMU,IAA4B,SAA5BA,0BAA6B5I;QAAwB,OACzDjwB,EAAW8b,MAAmBA,EAAemU;AAAS;MAExD,IAAI2I,GAAgC;QAClCvE,KAAiCA;QACjCC,KAAkCA;QAElC,IAAAwE,IAA4BtK,GAC1BsG,KAAYnS,GACZ,MACAmU,GACA;UACEnI,IAAajsB,EAAOgyB,GAAwB7Y,KAAc;UAC1DgT,IAAqBjT;UACrBkT,IAAuB0F;UACvBxF,IAAsB,SAAtBA,qBAAuBiB,GAAUc;YAC/B,IAAgBZ,IAAkCF,EAA1C/nB,QAAwBgoB,IAAkBD,EAAlBC;YAChC,IAAM6I,KACHhI,KAAkBb,MAAkBxN,IACjC1V,GAAYmjB,GAAgBqE,GAAcC,KAC1C;YACN,OACEsE,OACEnsB,GAAQujB,GAAc,MAAM/R,SAC5Bya,EAA0B5I;AAEhC;YAnBG+I,IAASF,EAAA,IAAEx8B,IAAMw8B,EAAA;QAuBxBxE,IAAiC0E;QACjC3E,IAAgC/3B;AAClC;MAEA,IAAIq8B,GAAiB;QACnB1C,EAA4BluB;QAC5B,IAAI9H,EAAQy4B,IAAgB;UAC1B,IAAM1yB,IAAU0yB,EAAc;UAC9B,IAAMO,IAAUP,EAAc;UAC9BvE,IAAkBt0B,EAASmG,MAAYA;UACvCouB,IAAmBv0B,EAASo5B,MAAYA;AAC1C,eAAO,IAAIp5B,EAAS64B,IAAgB;UAClCvE,IAAkBuE;UAClBtE,IAAmB;AACrB,eAAO;UACLD,IAAkB;UAClBC,IAAmB;AACrB;AACF;MAEA,IAAI1C,GAAa;QACf,IAAMwH,IAAmBzB;QACzB,IAAM0B,IAAsB9B,KAAyBA;QACrD,IAAM+B,IACJ/E,KAAiCA;QAEnC6E,KACEjxB,GACEquB,GACAY,EAAegC,EAAiB,IAAIA,EAAiB,IAAIxH;QAG7DyH,KACElxB,GAAWquB,GAAaG,EAAiB0C,EAAoB,IAAIzH;QAEnE0H,KACEnxB,GAAWquB,GAAaQ,EAAkBsC,EAAoB,IAAI1H;AACtE;MAEA2E,EAAaC;MAEb,OAAOA;AACR,OACDlB;AAEJ;ECrSO,IAAMiE,KAAwB,SAAxBA,sBACX71B,GACAL;IAAU,OACHnD,EAAWmD,KAASA,EAAMF,MAAM,GAAGO,KAAQL;AAAK;EAElD,IAAMm2B,KAA8B,SAA9BA,4BACX91B,GACA+1B,GACAC,GACAC;IAEA,IAAMC,IAAuBv6B,EAAYs6B,KACrCD,IACAC;IACJ,IAAME,IAAyBN,GAC7B71B,GACAk2B;IAEF,OAAOC,KAA0BJ,EAAoCt2B,MAAM,GAAGO;AAChF;EAEO,IAAMo2B,KAA+B,SAA/BA,6BACXp2B,GACAq2B,GACAC,GACAC;IAEA,IAAMC,IAAwB76B,EAAY46B,KACtCD,IACAC;IACJ,IAAMJ,IAAyBN,GAC7B71B,GACAw2B;IAEF,SACIL,MACDl5B,EAAck5B,KACXA,IACAE,EAAqC52B,MAAM,GAAGO;AAEtD;EAEO,IAAMy2B,KAAuB,SAAvBA,qBACXC,GACAC;IAEA,IAAA7zB,IAA2C6zB,KAA6B,CAAE,GAAlEvO,IAAwBtlB,EAAxBslB,0BAA0BnB,IAAInkB,EAAJmkB;IAClC,IAAA2P,IACE1M,MADM3M,IAAyBqZ,EAAzBrZ,GAA2B6B,IAAuBwX,EAAvBxX,GAAyB8J,IAAyB0N,EAAzB1N;IAE5D,IAAA2N,IACE3N,IAA4BN,QADIkO,IAA+BD,EAAzDzO,0BAAiE2O,IAAWF,EAAjB5P;IAGnE,IAAM+P,IACJ5O,KAAAA,OAAAA,IAA4B0O;IAC9B,IAAMG,IAAmCt7B,EAAYsrB,KAAQ8P,IAAc9P;IAE3E,IAAMiQ,KACH3Z,EAA0B5P,KAAK4P,EAA0B3P,MAC1DopB;IACF,IAAMG,IACJT,MACC76B,EAAOo7B,MACH7X,IACD6X;IAEN,SAASC,OAAmCC;AAC9C;EC1FO,IAAMC,KAAgC,SAAhCA,8BACX1yB,GACAoa,GACAC,GACAsY;IAEA,IAAMC,IAA+B;IACrC,IAAMC,IAA6B;IACnC,IAAMC,IAA+B;IACrC,IAAAZ,IAAsC1M,MAA9BhB,IAAyB0N,EAAzB1N;IACR,IAAA2N,IAA8C3N,KAA1BuO,IAAqBZ,EAAjCre;IACR,IAAckf,IAA8BD,EAApC9O;IACR,IACEwI,IAOErS,EAPFqS,IACAC,IAMEtS,EANFsS,IACAjS,IAKEL,EALFK,GACAwY,IAIE7Y,EAJF6Y,IACAtG,IAGEvS,EAHFuS,IACAE,IAEEzS,EAFFyS,IACArS,IACEJ,EADFI;IAEF,IAAApc,IAAwC60B,IAAe,CAAA,IAAKjzB,GAAxCkzB,IAAc90B,EAA1B0V;IACR,IAAArG,IAAqCylB,KAAkB,CAAE,GAA3CC,IAAkB1lB,EAAxBwW;IACR,IAAMxL,IAA6B;IACnC,IAAM2a,IAA6C;IACnD,IAAMC,IAA2C;IACjD,IAAMC,IAAyBC,GAG7B,EAAC9G,GAASC,GAAOjS,MACjB;MAAA,OAAOD,KAAqBqS,IAAUJ,IAAUC;AAAK,QACrDsG,GACAG;IAGF,IAAMK,IAAqB,SAArBA,mBAAsBC;MAC1B,IAAIz8B,GAAS;QACX,IAAI08B,IAAkC;QACtC,IAAIC,IAAmC;QACvC,IAAMC,IAAW,IAAI58B,EAAQ;UAC3B+C,QAAQ4yB;UACR8G,MAAAA;;QAEF,IAAMI,IAAkB,SAAlBA;UACJH,KAAiBA,EAAcxP;UAC/BwP,IAAgB;;QAElB,IAAMI,IAA6B,SAA7BA,2BAA8BC;UAClC,IAAQC,IAAuB3Z,EAAvB2Z;UACR,IAAMC,IACJzmB,GAAoCwmB,GAAoBP;UAC1D,IAAMzqB,IAAeyqB,MAAS;UAC9B,IAAMS,IAAiB,EACrBnrB,GAA0B,GAAGC,IAC7BD,GAAuCC,gBAAAA,IAAe,MAAM,OAAgBA,aAAAA;UAE9E,IAAMmrB,IAAYF,IAAyBC,IAAiBA,EAAeE;UAE3E,IACET,EAAuB,OAAOQ,EAAU,MACxCR,EAAuB,OAAOQ,EAAU;YAExC,OAAON;;UAGTA;UACAF,IAAyBQ;UACzBT,IAAgBK,EAAUM,GAAQC,QAChC;YAEEz1B,OAAO,EAAC;YAGRs1B,WAAAA;aAGF;YACEP,UAAAA;;UAIJ,OAAOC;;QAGT,OAAO;UACLC,IAAAA;;AAEJ;;IAEF,IAAMS,IAAiB;MACrBtrB,GAAGuqB,EAAmB;MACtBtqB,GAAGsqB,EAAmB;;IAExB,IAAMgB,IAAqB,SAArBA;MACJ,IAAQC,IAAmCpa,EAAnCoa,IAAiBC,IAAkBra,EAAlBqa;MACzB,IAAMC,IAAe,SAAfA,aAAgBC,GAA0BC;QAA0B,OACxE7zB,GAAU,GAAG,GAAG4zB,KAAoBA,IAAmBC,MAAuB;AAAE;MAElF,OAAO;QACL5rB,GAAG0rB,EAAaD,EAAczrB,GAAGwrB,EAAgBxrB;QACjDC,GAAGyrB,EAAaD,EAAcxrB,GAAGurB,EAAgBvrB;;;IAGrD,IAAM4rB,IAAmC,SAAnCA,iCACJC,GACA7uB,GACA3C;MAEA,IAAMyxB,IAASzxB,IAAMK,KAAWF;MAChC5J,KAAKi7B,IAAqB,SAACE;QACzBD,EAAOC,EAAmBC,IAAYhvB;AACxC;;IAEF,IAAMivB,IAAiB,SAAjBA,eACJJ,GACArtB;MAEA5N,KAAKi7B,IAAqB,SAACE;QACzB,IAAAG,IAAsB1tB,EAASutB,IAAxBzzB,IAAG4zB,EAAA,IAAE5tB,IAAM4tB,EAAA;QAClB7tB,UAAU/F,GAAKgG;AACjB;;IAEF,IAAM6tB,IAA2B,SAA3BA,yBACJ1xB,GACAJ,GACA+xB;MAEA,IAAMC,IAAa19B,EAAUy9B;MAC7B,IAAME,IAAgBD,IAAaD,IAAiB;MACpD,IAAMG,IAAcF,KAAcD,IAAiB;MACnDE,KAAiBV,EAAiC1B,GAAsBzvB,GAAWJ;MACnFkyB,KAAeX,EAAiCzB,GAAoB1vB,GAAWJ;;IAEjF,IAAMmyB,IAAgC,SAAhCA;MACJ,IAAMC,IAAkBnB;MACxB,IAAMoB,IACJ,SADIA,uBACHC;QAA2B,OAC5B,SAAC9B;UAA6B,IAAAnmB;UAAA,OAAK,EACjCmmB,EAAUmB,KAAUtnB,QAAAA,EAEjBglB,KAA+BvrB,GAAewuB,KAAuB,IAAEjoB;AAE3E;AAAA;MAEHunB,EAAe/B,GAAsBwC,EAAuBD,EAAgB1sB;MAC5EksB,EAAe9B,GAAoBuC,EAAuBD,EAAgBzsB;;IAE5E,IAAM4sB,IAAgC,SAAhCA;MACJ,KAAK9+B,GAAS;QACZ,IAAQg9B,IAAuB3Z,EAAvB2Z;QACR,IAAM+B,IAAgBpoB,GACpBqmB,GACA7nB,GAAiBwgB;QAEnB,IAAMiJ,IACJ,SADIA,uBACHI;UAAyB,OAC1B,SAACjC;YAA6B,IAAAkC;YAAA,OAAK,EACjClC,EAAUmB,KAAUe,QAAAA,EAEjBpD,KAA6BxrB,GAAe2uB,KAAqB,IAAEC;AAEvE;AAAA;QAEHd,EAAe/B,GAAsBwC,EAAuBG,EAAc9sB;QAC1EksB,EAAe9B,GAAoBuC,EAAuBG,EAAc7sB;AAC1E;;IAEF,IAAMgtB,IAAqC,SAArCA;MACJ,IAAQlC,IAAuB3Z,EAAvB2Z;MACR,IAAMC,IAAyBzmB,GAAoCwmB;MACnE,IAAM4B,IACJ,SADIA,uBACHO;QAAgD,OACjD,SAACpC;UAA6B,IAAAqC;UAAA,OAAK,EACjCrC,EAAUmB,KAAUkB,QAAAA,EAEjBtD,KAA+BqD,IAA0C,MAAM,KAAGC;AAEtF;AAAA;MAEHjB,EAAe/B,GAAsBwC,EAAuB3B,EAAuBhrB;MACnFksB,EAAe9B,GAAoBuC,EAAuB3B,EAAuB/qB;MAGjF,IAAIlS,GAAS;QACXo8B,EAAqBxkB,QAAQ2lB,EAAetrB,EAAG6qB;QAC/CT,EAAmBzkB,QAAQ2lB,EAAerrB,EAAG4qB;AAC/C;;IAEF,IAAMuC,IAAmC,SAAnCA;MACJ,IAAI7b,MAAsBqS,GAAS;QACjC,IAAQ4H,IAAwCpa,EAAxCoa,IAAiBT,IAAuB3Z,EAAvB2Z;QACzB,IAAMsC,IAA2B9oB,GAAoCwmB;QACrE,IAAM+B,IAAgBpoB,GACpBqmB,GACA7nB,GAAiBwgB;QAEnB,IAAM4J,IAA2C,SAA3CA,uBAA4CxC;UAChD,IAAQmB,IAAenB,EAAfmB;UACR,IAAM1zB,IAAMgD,GAAO0wB,OAAgBza,KAAaya;UAChD,IAAMsB,IAAoB,SAApBA,kBACJR,GACAnB,GACA4B;YAEA,IAAMC,IAAK7B,IAAqBmB;YAChC,OAAO1uB,GAAcmvB,IAA2BC,KAAMA;;UAGxD,OAAO,EACLl1B,GACAA,KAAO;YACL2yB,WAAWprB,GAA0B;cACnCE,GAAGutB,EAAkBT,EAAc9sB,GAAGwrB,EAAgBxrB,GAAGqtB,EAAyBrtB;cAClFC,GAAGstB,EAAkBT,EAAc7sB,GAAGurB,EAAgBvrB,GAAGotB,EAAyBptB;;;;QAM1FisB,EAAe/B,GAAsBmD;QACrCpB,EAAe9B,GAAoBkD;AACrC;;IAEF,IAAMI,IAAuB,SAAvBA,qBAAwB3tB;MAC5B,IAAM4tB,IAAQ5tB,IAAe,MAAM;MACnC,IAAM6tB,IAAqB7tB,IACvBoN,KACAC;MACJ,IAAMygB,IAAY7wB,GAAaiQ,KAAkB,MAAI2gB;MACrD,IAAME,IAAQ9wB,GAAUqQ;MACxB,IAAM0gB,IAAS/wB,GAAUsQ;MACzB,IAAMtZ,IAAS;QACbi4B,IAAY4B;QACZG,IAAQF;QACR1C,IAAS2C;;MAEX,IAAMpD,IAAWW,EAAeqC;MAEhCj8B,EAAKqO,IAAeoqB,IAAuBC,GAAoBp2B;MAC/DtC,EAAK8d,GAAY,EACfhT,GAAeqxB,GAAWC,IAC1BtxB,GAAesxB,GAAOC,IACtBv5B,GAAK2H,IAAgB0xB,IACrBlD,KAAYA,EAASE,GAA2B72B,IAChD01B,EAAsB11B,GAAQo4B,GAA0BrsB;MAG1D,OAAO/L;;IAET,IAAMi6B,IAAuCz5B,GAAKk5B,GAAsB;IACxE,IAAMQ,IAAqC15B,GAAKk5B,GAAsB;IACtE,IAAMS,IAAiB,SAAjBA;MACJ3xB,GAAe6tB,GAAwBF,EAAqB,GAAG8B;MAC/DzvB,GAAe6tB,GAAwBD,EAAmB,GAAG6B;MAE7D,OAAOz3B,GAAKpC,GAAiBod;;IAG/Bye;IACAC;IAEA,OAAO,EACL;MACEE,IAAgC3B;MAChC4B,IAAgCxB;MAChCyB,IAAqCrB;MACrCsB,IAAmCnB;MACnCoB,IAA2BpC;MAC3BqC,IAAa;QACXC,IAAsBvE;QACtBwE,IAAQV;QACRW,IAAQp6B,GAAK03B,GAAgB/B;;MAE/B0E,IAAW;QACTH,IAAsBtE;QACtBuE,IAAQT;QACRU,IAAQp6B,GAAK03B,GAAgB9B;;OAGjC+D;AAEJ;EC7TO,IAAMW,KAA8B,SAA9BA,4BACX5jC,GACAimB,GACAC,GACA2d;IAEA,OAAO,SAAC/C,GAAoBI,GAA0BrsB;MACpD,IACE0jB,IAMEtS,EANFsS,IACAjS,IAKEL,EALFK,GACAD,IAIEJ,EAJFI,GACAmS,IAGEvS,EAHFuS,IACAsL,IAEE7d,EAFF6d,IACAjL,IACE5S,EADF4S;MAEF,IAAQkI,IAAgCD,EAAhCC,IAAY+B,IAAoBhC,EAApBgC,IAAQ5C,IAAYY,EAAZZ;MAC5B,IAAA7U,IAA0C3hB,GAAiB,MAApDq6B,IAAY1Y,EAAA,IAAE2Y,IAAiB3Y,EAAA;MACtC,IAAA4Y,IACEv6B,GAAiB,MADZw6B,IAAiCD,EAAA,IAAEE,IAAsCF,EAAA;MAEhF,IAAMG,IAA8B,SAA9BA,4BAA+BC;QACnC1gC,EAAW60B,EAAqB8L,aAC9B9L,EAAqB8L,SAAS;UAC5BC,UAAU;UACV/vB,MAAM6vB,EAAYvvB;UAClBT,KAAKgwB,EAAYtvB;;;MAIvB,IAAMyvB,IAAgC,SAAhCA;QACJ,IAAMC,IAA8B;QACpC,IAAMC,IAAuB7vB,YAAAA,IAAe,MAAM;QAClD,IAAM8vB,IAAiB9vB,IAAe7M,IAAWC;QACjD,IAAM28B,IAAa/vB,IAAe,SAAS;QAC3C,IAAMgwB,IAAQhwB,IAAe,MAAM;QACnC,IAAM4tB,IAAQ5tB,IAAe,MAAM;QAEnC,IAAMiwB,IACJ,SADIA,yBACHC,GAAyBC;UAAqB,OAAK,SAACC;YAAyB,IAAAC;YAC5E,IAAQ5E,IAAoBpa,EAApBoa;YACR,IAAM6E,IAAkBxvB,GAAcmtB,GAAQ+B,KAASlvB,GAAcuqB,GAAS2E;YAC9E,IAAMO,IAAsBJ,IAAgBC,IAAiBE;YAC7D,IAAME,IAAcD,IAAqB9E,EAAgBmC;YAEzD7qB,GAAgB4gB,IAAoB0M,QAAAA,EACjCzC,KAAQsC,IAAkBM,GAAWH;;AAEzC;QACH,IAAMI,IAA2C;QAEjD,OAAOvuB,GAAiB+rB,GAAQ,gBAAe,SAACyC;UAC9C,IAAMC,IACJj1B,GAAQg1B,EAAiB15B,QAAoBuW,MAAAA,QAAgC8d;UAC/E,IAAMuF,IAAwBD,IAAetF,IAAU4C;UAEvD,IAAM4C,IAAmB1lC,EAAQ2f;UACjC,IAAMgmB,IAAwBD,EAAiBF,IAAe,eAAe;UAC7E,IAAQI,IAAmCL,EAAnCK,QAAQC,IAA2BN,EAA3BM,WAAWC,IAAgBP,EAAhBO;UAC3B,IAAQ3lB,IAAaulB,EAAbvlB;UAER,IAAM4lB,IACJH,MAAW,KACXC,KACAF,MACCxlB,KAAY,IAAInR,SAAS82B;UAE5B,IAAIC,GAAqB;YACvB7+B,EAAgBo+B;YAChBnB;YAEA,IAAM6B,KACHR,MAAiBD,EAAiBU,YAAYN,MAA0B;YAC3E,IAAMO,IAAgB58B,GAAK2M,IAAuBiqB;YAClD,IAAMiG,IAAe78B,GAAK2M,IAAuB6sB;YACjD,IAAMsD,IAAkB,SAAlBA,gBAAmBC,GAAsBC;cAAmB,QAC/DD,KAAcH,KAAiBtB,MAAe0B,KAAaH,KAAgBvB;AAAW;YACzF,IAAM2B,IACJ9kC,EAAUwU,GAAsBuiB,GAAsBmM,MACpDhvB,GAAc6iB,GAAsBqM,MAAU;YAClD,IAAMha,IAAqBia,EACzB9sB,GAAiBwgB,GAAsBiK,IACvC,IAAI8D;YAEN,IAAMC,IAAoBjB,EAAiBb;YAC3C,IAAM2B,IAAaH;YACnB,IAAMI,IAAYH;YAClB,IAAMpb,IAAesb,EAAW1B;YAChC,IAAM8B,IAAeL,EAAgBC,GAAYC,KAAavb,IAAe;YAC7E,IAAM2b,IAA6BF,IAAoBF,EAAU1B;YACjE,IAAM+B,IAAcnB,IAAe,IAAIkB,IAA6BD;YACpE,IAAMG,IAAwB,SAAxBA,sBAAyBC;cAE7B3/B,EAAgB4/B;cAChBrB,EAAsBmB,sBAAsBC,EAAeE;;YAE7D,IAAMC,IAAoBxB,KAAgBQ;YAC1C,IAAM1M,IAA8BT;YAEpC,IAAMiO,IAAsB,EAC1B/vB,GAAiB+sB,GAAcW,GAA6BmC,IAC5D7vB,GAAiB+sB,GAAc,gBAAe,SAAC5oB;cAAY,OAAKxD,GAAewD;gBAAQ;cACrFjE,GAAU;gBAEZF,GAAiB+rB,GAAQ2B,GAA6BmC,IACtDI,KACEjwB,GAAiB+rB,GAAQ,gBAAe,SAACmE;cAA8B,OACrEpc,EACE8b,KAAeM,EAAiBvC,KAAe8B;iBAGrDQ,KACG;cACC,IAAME,IAA0BlvB,GAAiBwgB;cACjDc;cACA,IAAM6N,IAAuBnvB,GAAiBwgB;cAC9C,IAAM4O,IAAiB;gBACrBtyB,GAAGqyB,EAAqBryB,IAAIoyB,EAAwBpyB;gBACpDC,GAAGoyB,EAAqBpyB,IAAImyB,EAAwBnyB;;cAGtD,IAAIpT,EAAQylC,EAAetyB,KAAK,KAAKnT,EAAQylC,EAAeryB,KAAK,GAAG;gBAClE8jB;gBACAjhB,GAAgB4gB,GAAsB0O;gBACtC9C,EAA4BgD;gBAC5BlD,EAAkC5K;AACpC;AACF;YAGJmM,EAAsB4B,kBAAkB9B,EAAiBwB;YAEzD,IAAIf;cACFnb,EAAmB8b;mBACd,KAAKnB,GAAc;cACxB,IAAM8B,IAAqBnrB,GACzBwO;cAEF,IAAI2c,GAAoB;gBACtB,IAAMC,IAA2BD,EAC/Bzc,GACA8b,GACA5b,IACA,SAACE;kBAEC,IAAIA;oBACFqO;;oBAEA9yB,EAAKsgC,GAAqBxN;;AAE9B;gBAGF9yB,EAAKsgC,GAAqBS;gBAC1B/gC,EAAK8+B,GAAuBh8B,GAAKi+B,GAA0B;AAC7D;AACF;AACF;AACF;;MAGF,IAAIC,IAAgB;MAEpB,OAAOl+B,GAAKpC,GAAiB,EAC3B6P,GAAiBmpB,GAAS,4BAA4B2D,IACtD9sB,GAAiBgqB,GAAY,iBAAgB;QAC3CG,EAAyB3e,IAA+B;AACzD,WACDxL,GAAiBgqB,GAAY,+BAA8B;QACzDG,EAAyB3e,IAA+B;AACzD,YAEA8D,KACCtP,GAAiBgqB,GAAY,cAAa;QACxC,IAAM0G,IAAiBj3B;QACvB,IACEhD,GAAQi6B,GAAgB5mB,OACxBrT,GAAQi6B,GAAgB9mB,OACxB8mB,MAAmBvmC,SAASktB;UAE5BjsB,EAAKmH,GAAKsQ,IAAc0M,IAAY;;AAEvC,WAEHvP,GACEgqB,GACA,UACA,SAAC2G;QACC,IAAQC,IAA8BD,EAA9BC,QAAQC,IAAsBF,EAAtBE,QAAQC,IAAcH,EAAdG;QAGxB,IAAIL,KAAiBK,MAAc,KAAKx3B,GAAO0wB,OAAgBxI;UAC7D6L,EAA4B;YAC1BtvB,GAAG6yB;YACH5yB,GAAG6yB;;;QAIPJ,IAAgB;QAChBtG,EAAyBve,IAAyB;QAClDohB,GAAa;UACXyD,IAAgB;UAChBtG,EAAyBve;AAC3B;QAEAjL,GAAegwB;AACjB,UACA;QAAEzwB,GAAU;QAAOC,GAAU;UAG/BH,GACEgqB,GACA,eAGAz3B,GAAKyN,IAAkB+sB,GAAc,SAASnsB,IAAgB;QAC5DP,GAAO;QACPF,GAAU;QACVD,GAAU;UAEZ;QAAEC,GAAU;UAEdstB,KACAR,GACAG;;AAGN;EC1NO,IAAM2D,KAAwB,SAAxBA,sBACXj8B,GACA7L,GACAmmB,GACAD,GACAD,GACA7B;IAEA,IAAI2jB;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC,IAAyC/+B;IAC7C,IAAIg/B,IAAwB;IAC5B,IAAMC,IAAwB,EAAC,SAAS;IAGxC,IAAMC,IAAyB,SAAzBA,uBAA0BrtB;MAAmB,OACjDotB,EAAsBt5B,SAASkM,EAAM4qB;AAAY;IAEnD,IAAAza,IAAkE3hB,MAA3D8+B,IAA2Bnd,EAAA,IAAEod,IAA0Bpd,EAAA;IAC9D,IAAA4Y,IACEv6B,GAAiB,MADZg/B,IAAiCzE,EAAA,IAAE0E,IAAsC1E,EAAA;IAEhF,IAAA2E,IAA8Dl/B,GAAiB,MAAxEm/B,IAAsBD,EAAA,IAAEE,IAA2BF,EAAA;IAC1D,IAAAG,IAAgDr/B,IAAiB;MAAA,OAAM2+B;SAAhEW,IAAeD,EAAA,IAAEE,IAAoBF,EAAA;IAC5C,IAAAG,IAAmC3K,GACjC1yB,GACAoa,GACAC,GACA0d,GACE5jC,GACAimB,GACAC,IACA,SAAChL;MAAK,OACJqtB,EAAuBrtB,MAEvBiuB;AAA4C,UAX3C1Z,IAAQyZ,EAAA,IAAEjG,IAAciG,EAAA;IAc/B,IAAQ3Q,IAAwCtS,EAAxCsS,IAAO6Q,IAAiCnjB,EAAjCmjB,IAAqB1Q,IAAYzS,EAAZyS;IACpC,IACE4K,IAKE7T,EALF6T,IACAJ,IAIEzT,EAJFyT,IACAC,IAGE1T,EAHF0T,IACAC,IAEE3T,EAFF2T,IACAC,IACE5T,EADF4T;IAEF,IAAMgG,IAA2B,SAA3BA,yBAA4BC,GAAyBC;MACzDN;MACA,IAAIK;QACFhG,EAA0B5gB;aACrB;QACL,IAAM8mB,IAAOlgC,GAAKg6B,GAA2B5gB,IAAkC;QAC/E,IAAI2lB,IAAwB,MAAMkB;UAChCP,EAAgBQ;;UAEhBA;;AAEJ;;IAEF,IAAML,IAA6C,SAA7CA;MACJ,IAAIlB,KAAmBF,KAAeG,GAAiB;QACrDmB,EAAyB;QACzBX,GAAkC;UAChCW,EAAyB;AAC3B;AACF;;IAEF,IAAMI,IAA2B,SAA3BA,yBAA4Br6B;MAChCk0B,EAA0B7gB,IAA4BrT,GAAK;MAC3Dk0B,EAA0B7gB,IAA4BrT,GAAK;;IAE7D,IAAMs6B,IAAmB,SAAnBA,iBAAoBxuB;MACxB,IAAIqtB,EAAuBrtB,IAAQ;QACjC6sB,IAAcE;QACdA,KAAmBoB,EAAyB;AAC9C;;IAEF,IAAM/kB,IAA6B,EACjC2kB,GACAN,GACAG,GACAL,GACA;MAAA,OAAML;AAAwC,OAE9CrxB,GAAiBwhB,GAAO,eAAemR,GAAkB;MAAEtyB,GAAO;QAClEL,GAAiBwhB,GAAO,gBAAgBmR,IACxC3yB,GAAiBwhB,GAAO,iBAAgB,SAACrd;MACvC,IAAIqtB,EAAuBrtB,IAAQ;QACjC6sB,IAAc;QACdE,KAAmBoB,EAAyB;AAC9C;AACD,SACDtyB,GAAiBwhB,GAAO,gBAAe,SAACrd;MACtCqtB,EAAuBrtB,MACrB8sB,KACAmB;AACH,SACDpyB,GAAiBqyB,GAAqB,WAAU,SAACluB;MAC/CstB,GAA4B;QAC1BrF;QACAgG;AACF;MAEA/kB,EAASlJ;MAETmoB;AACD;IAGH,OAAO,EACL;MAAA,OAAM/5B,GAAKpC,GAAiBV,EAAK8d,GAAY2e;AAC7C,OAAA,SAAAh5B;MAA2E,IAAxE0a,IAAY1a,EAAZ0a,IAAcmX,IAAM7xB,EAAN6xB,IAAQ6N,IAAqB1/B,EAArB0/B,IAAuBC,IAAqB3/B,EAArB2/B;MAC9C,IAAAtwB,IAKIswB,KAAyB,CAAE,GAJ7BC,IAAoBvwB,EAApBuwB,IACAC,IAAsBxwB,EAAtBwwB,IACAC,IAAqBzwB,EAArBywB,IACAC,IAAyB1wB,EAAzB0wB;MAEF,IAAAvwB,IAAuCkwB,KAAyB,CAAE,GAA1DxP,IAAiB1gB,EAAjB0gB,IAAmB5E,IAAO9b,EAAP8b;MAC3B,IAAQ9M,IAAoBtC,EAApBsC;MACR,IAAAsV,IAAsC1M,MAA9B3M,IAAyBqZ,EAAzBrZ;MACR,IAAQkB,IAAiCM,EAAjCN,GAAgBqkB,IAAiB/jB,EAAjB+jB;MACxB,IAAAlO,IACEpX,EAAa,iCADRC,IAAkCmX,EAAA,IAAElX,IAAmCkX,EAAA;MAE9E,IAAAC,IAA8BrX,EAAa,qBAApC/E,IAAKoc,EAAA,IAAEkO,IAAYlO,EAAA;MAC1B,IAAAE,IAAwCvX,EAAa,0BAA9C9E,IAAUqc,EAAA,IAAEiO,IAAiBjO,EAAA;MACpC,IAAAE,IAAoCzX,EAAa,wBAA1C7E,IAAQsc,EAAA,IAAEgO,IAAehO,EAAA;MAChC,IAAAiO,IAAkD1lB,EAAa,+BAAxD3E,IAAeqqB,EAAA,IAAEC,IAAsBD,EAAA;MAC9C,IAAAE,IAAwB5lB,EAAa,6BAA9B5E,IAAawqB,EAAA;MACpB,IAAAC,KAAwC7lB,EAAa,0BAA9C1E,KAAUuqB,GAAA,IAAEC,KAAiBD,GAAA;MACpC,IAAAE,KAA0C/lB,EAAa,2BAAhDzE,KAAWwqB,GAAA,IAAEC,KAAkBD,GAAA;MACtC,IAAAE,KAAoCjmB,EAAa,aAA1CjF,KAAQkrB,GAAA,IAAEC,KAAeD,GAAA;MAChC,IAAME,KAAgBvV,MAAYuG;MAClC,IAAM7W,KAAcglB,EAAan1B,KAAKm1B,EAAal1B;MACnD,IAAMg2B,KACJlB,KACAC,KACAE,KACA7P,KACA2B;MACF,IAAMkP,KAAmBjB,KAAyBI,KAAqBU;MACvE,IAAMvrB,KACJsF,KACAF,EAA0B5P,KAC1B4P,EAA0B3P;MAE5B,IAAMk2B,KAAyB,SAAzBA,uBACJlmB,GACA+B,GACAjS;QAEA,IAAMq2B,IACJnmB,EAAiB/V,SAAS5G,QACzByX,MAAe3X,KAAe2X,MAAe,UAAUiH,MAAkB1e;QAE5Ek7B,EAA0BjhB,IAA2B6oB,GAAWr2B;QAEhE,OAAOq2B;;MAGT7C,IAAwBtoB;MAExB,IAAI+qB;QACF,IAAI9qB,KAAmBiF,IAAa;UAClCwkB,EAAyB;UACzBrB;UACAS,GAAuB;YACrBT,IAAyCrxB,GACvCqyB,GACA,UACA9/B,GAAKmgC,GAA0B,OAC/B;cACEryB,GAAO;;AAGb;AACF;UACEqyB,EAAyB;;;MAI7B,IAAI5kB;QACFye,EAA0BxhB,IAA6BxC;;MAGzD,IAAI4qB,GAAc;QAChB5G,EAA0B6E;QAC1B7E,EAA0B1jB,GAAO;QAEjCuoB,IAAYvoB;AACd;MAEA,IAAI0qB,MAA2BtqB;QAC7BypB,EAAyB;;MAG3B,IAAIW,GAAiB;QACnBpC,IAAiBloB,MAAa;QAC9BmoB,IAAkBnoB,MAAa;QAC/BooB,IAAkBpoB,MAAa;QAC/BupB,EAAyBnB,GAAiB;AAC5C;MAEA,IAAIuC;QACFnH,EAA0BzgB,IAAqC5C;;MAGjE,IAAI0qB;QACFrH,EAA0B1gB,MAAsC1C;;MAKlE,IAAI8qB,IAAkB;QACpB,IAAMG,KAAWF,GAAuBvrB,GAAS5K,GAAG8Q,EAAe9Q,GAAG;QACtE,IAAMs2B,KAAWH,GAAuBvrB,GAAS3K,GAAG6Q,EAAe7Q,GAAG;QACtE,IAAMs2B,KAAYF,MAAYC;QAE9B9H,EAA0BhhB,KAA+B+oB;AAC3D;MAGA,IAAIN,IAAkB;QACpB5H;QACAD;QACAG;QACA2G,KAA6B5G;QAE7BE,EAA0B9gB,KAA6BynB,EAAan1B,GAAG;QACvEwuB,EAA0B9gB,KAA6BynB,EAAal1B,GAAG;QACvEuuB,EAA0BthB,IAAuByG,MAAoBiQ;AACvE;AACF,OACA,CAAA,GACAjJ;AAEJ;ECpNO,IAAM6b,KAA+B,SAA/BA,6BACXz/B;IAEA,IAAM4Y,IAAM4M;IACZ,IAAQhB,IAAuD5L,EAAvD4L,IAA2B9J,IAA4B9B,EAA5B8B;IACnC,IAAAyX,IAA0C3N,KAAxBkb,IAAmBvN,EAA7BvO;IACR,IACW+b,IAGPD,EAHF5b,SACU8b,IAERF,EAFF3b,UACS8b,IACPH,EADF1b;IAEF,IAAM8b,IAAcvnC,EAAcyH;IAClC,IAAM+/B,IAAiCD,IAAc,CAAE,IAAG9/B;IAC1D,IAAkBggC,IAAiBD,EAA3Bnc;IACR,IAAAxlB,IAII4hC,KAAgB,CAAE,GAHXC,IAAqB7hC,EAA9B0lB,SACUoc,IAAsB9hC,EAAhC2lB,UACSoc,IAAqB/hC,EAA9B4lB;IAGF,IAAMoc,IAAgBN,IAAc9/B,IAAS+/B,EAA8B//B;IAC3E,IAAMgyB,IAAS3tB,GAAc+7B;IAC7B,IAAMC,IAAgBD,EAAcC;IACpC,IAAMC,IAAaD,EAAcE;IACjC,IAAMC,IAAoB,SAApBA;MAAiB,OAASH,EAAcI,eAAenrC;AAAG;IAChE,IAAM87B,IAA8B3zB,GAAKijC,IAAoC,EAACN;IAC9E,IAAM1O,IAA+Bj0B,GAAK81B,IAAqC,EAAC6M;IAChF,IAAMO,IAAeljC,GAAKwI,IAAW;IACrC,IAAM26B,IAA0BnjC,GAC9B2zB,GACAuP,GACAf;IAEF,IAAMiB,IAAyBpjC,GAC7Bi0B,GACAiP,GACAd;IAEF,IAAMiB,IAAqB,SAArBA,mBAAsBt/B;MAC1B,IAAMu/B,IAAaj3B,GAActI;MACjC,IAAMw/B,IAAah3B,GAAcxI;MACjC,IAAMy/B,IAAYn5B,UAAUtG,GAAKvF;MACjC,IAAMilC,IAAYp5B,UAAUtG,GAAKtF;MAEjC,OACG8kC,EAAWx3B,IAAIu3B,EAAWv3B,IAAI,MAAMyP,GAAkBgoB,MACtDD,EAAWv3B,IAAIs3B,EAAWt3B,IAAI,MAAMwP,GAAkBioB;;IAG3D,IAAMC,IAA0BP,EAAwBV;IACxD,IAAMkB,IAAmBD,MAA4Bf;IACrD,IAAM/S,IAAuB+T,KAAoBpP;IACjD,IAAMqP,KAA0BD,KAAoBP,EAAuBV;IAI3E,IAAMmB,KAAqBF,KAAoBD,MAA4BE;IAC3E,IAAME,IAAkBlU,IAAuBiT,IAAaa;IAC5D,IAAMK,IAAcnU,IAAuBkU,IAAkBnB;IAC7D,IAAMqB,KACHL,KACD1P,EAA6BiP,GAAchB,GAA8BM;IAC3E,IAAMyB,KAAkBJ,KAAqBD;IAC7C,IAAMM,IAAoB,EAACD,GAAgBH,GAAiBE,GAAgBD,IAAa/1B,KACvF,SAACjK;MAAG,OAAKjJ,EAAciJ,OAASgD,GAAOhD,MAAQA;;IAEjD,IAAMogC,IAAqB,SAArBA,mBAAsBpgC;MAAwB,OAAKA,KAAOpH,EAAQunC,GAAmBngC;AAAI;IAC/F,IAAMqgC,KACHD,EAAmBL,MAAoBT,EAAmBS,KACvDA,IACAnB;IACN,IAAM0B,IAAsBzU,IAAuBiT,IAAaiB;IAChE,IAAMQ,IAAqB1U,IAAuBgT,IAAgBkB;IAElE,IAAMS,IAAgD;MACpDvV,IAAS2T;MACT1T,IAAO8U;MACP/mB,GAAW8mB;MACXU,IAAUR;MACV7U,IAAU8U;MACV/U,IAAsBmV;MACtBvE,IAAqBwE;MACrBG,IAA8BlQ,IAASsO,IAAauB;MACpD5J,IAAcoI;MACdxT,IAASmF;MACTiB,IAAc6M;MACdtlB,GAAmB4mB;MACnBe,IAAY3B;MACZ1T,IAAmB,SAAnBA,kBAAoBsV;QAAkC,OACpD5+B,GAAa+9B,GAAiBvsB,IAAuBotB;AAA2B;MAClFrV,IAAyB,SAAzBA,wBAA0BqV,GAAoC7+B;QAAa,OACzED,GAAmBi+B,GAAiBvsB,IAAuBotB,GAA4B7+B;AAAI;MAC7FypB,IAA8B,SAA9BA;QAA4B,OAC1B1pB,GACEw+B,GACA9sB,IACAK,IACA;AACD;;IAEL,IAAQoX,IAAkDuV,EAAlDvV,IAASC,IAAyCsV,EAAzCtV,IAAOuV,IAAkCD,EAAlCC,IAAUxnB,IAAwBunB,EAAxBvnB,GAAWmS,IAAaoV,EAAbpV;IAC7C,IAAMnU,KAA4B,EAChC;MAEEzW,GAAY0qB,GAAO,EAAC5X,IAAmBH;MACvC3S,GAAYyqB,GAAS9X;MACrB,IAAIqd;QACFhwB,GAAYs+B,GAAY,EAAC3rB,IAAyBG;;AAEtD;IAEF,IAAIutB,KAAiB/9B,GACnB,EAACsoB,GAAUnS,GAAWwnB,GAAUvV,GAAOD,IAAS5oB,MAAK,SAACrC;MAAG,OAAKA,MAAQogC,EAAmBpgC;AAAI;IAE/F,IAAM8gC,KAAcjV,IAAuBZ,IAAUG,KAAYnS;IACjE,IAAMoL,KAAUpoB,GAAKpC,GAAiBod;IACtC,IAAM2e,KAAiB,SAAjBA;MACJ,IAAMmL,IAAS/B;MACf,IAAMgC,IAAgB79B;MACtB,IAAM89B,IAAS,SAATA,OAAUjhC;QACdiE,GAAejB,GAAOhD,IAAM8C,GAAS9C;QACrC4D,GAAe5D;;MAGjB,IAAMkhC,IAAyB,SAAzBA,uBAA0B79B;QAAiD,OAC/EqG,GAAiBrG,GAAe,+BAA+BiH,IAAgB;UAC7ET,GAAU;UACVD,GAAU;;AACV;MACJ,IAAMu3B,IAAc;MACpB,IAAMC,IAA2BrhC,GAAQkZ,GAAWkoB;MACpD,IAAME,IAA2BH,EAAuBF;MACxD3gC,GAAS6qB,GAAO5X,IAAmBssB,IAAmB,KAAKrsB;MAC3DlT,GAASogC,GAAUzsB,IAAsB;MACzC3T,GAAS4Y,GAAWzF,IAAuB;MAC3CnT,GAAS+qB,GAAUnX,IAAsB;MAEzC,KAAK2rB,GAAkB;QACrBv/B,GAAS4Y,GAAWkoB,GAAaC,KAA4B;QAC7D5Q,KAAUnwB,GAASy+B,GAAYzrB,IAAuB;AACxD;MAEApP,GAAe68B,IAAaD;MAC5B58B,GAAeinB,GAAOuV;MACtBx8B,GAAew8B,KAAYvV,IAAQ0U,KAAoB3mB;MACvDhV,GAAegV,GAAWmS;MAE1BjyB,EAAK8d,IAAY,EACfoqB,GACA;QACE,IAAMC,IAAmBn+B;QACzB,IAAMo+B,IAAsBnB,EAAmBnnB;QAG/C,IAAMuoB,IACJD,KAAuBD,MAAqBroB,IAAYgS,IAAUqW;QACpE,IAAMG,IAA8BP,EAAuBM;QAC3DhhC,GAAYigC,GAAUzsB;QACtBxT,GAAY4qB,GAAUnX;QACtBzT,GAAYyY,GAAWzF;QACvBgd,KAAUhwB,GAAYs+B,GAAYzrB;QAClC+tB,IACI/gC,GAAS4Y,GAAWkoB,GAAaC,KACjC5gC,GAAYyY,GAAWkoB;QAE3Bf,EAAmBhV,MAAa6V,EAAO7V;QACvCmW,KAAuBN,EAAOhoB;QAC9BmnB,EAAmBK,MAAaQ,EAAOR;QACvCl0B,GAAai1B;QACbC;AACD;MAGH,IAAIvoB,MAA4B0mB,GAAkB;QAChD/9B,GAAaoX,GAAWzF,IAAuBM;QAC/C3a,EAAK8d,IAAYhb,GAAKuE,IAAayY,GAAWzF;AAChD;MAIAjH,IACGqzB,KAAoBpP,KAAUwQ,MAAkB/V,KAAW8V,EAAO/5B,QAAQ+5B,IACvE9nB,IACA+nB;MAENK;MAGAR,KAAiB;MAEjB,OAAOxc;;IAGT,OAAO,EAACmc,GAAoB5K,IAAgBvR;AAC9C;ECxQO,IAAMqd,KACX,SADWA,2BAA0B9kC;IAAA,IAClCwuB,IAAQxuB,EAARwuB;IAAQ,OACX,SAAAnf;MAAuD,IAApDqwB,IAAqBrwB,EAArBqwB,IAAuBqF,IAAe11B,EAAf01B,IAAiBlT,IAAMxiB,EAANwiB;MACzC,IAAAriB,IAAoCkwB,KAAyB,CAAE,GAAvDrP,IAAuB7gB,EAAvB6gB;MACR,IAAQtB,IAAqBgW,EAArBhW;MACR,IAAM1B,IAAyBmB,MAAa6B,KAA2BwB;MAEvE,IAAIxE,GAAwB;QAAA,IAAAnU;QAC1B/P,UAAUqlB,IAAQtV,QAAAA,EACflb,KAAY+wB,KAAoB,QAAM7V;AAE3C;;AACD;ECKI,IAAM8rB,KAA2D,SAA3DA,2BAA0BhlC,GAErC8uB;IACE,IAFAR,IAAKtuB,EAALsuB,IAAOuV,IAAQ7jC,EAAR6jC,IAAUxnB,IAASrc,EAATqc,GAAWD,IAAiBpc,EAAjBoc;IAG9B,IAAA4I,IAAkDlvB,EAChD;MACEI,GAAQiJ;MACRlJ,GAAe8T;OAEjB1K,GAAK0K,IAAoBukB,GAAO,WAAW,MALtC2W,IAAkBjgB,EAAA,IAAEkgB,IAAmBlgB,EAAA;IAQ9C,OAAO,SAAA3V;MAAqE,IAAlEqL,IAAYrL,EAAZqL,IAAcglB,IAAqBrwB,EAArBqwB,IAAuBqF,IAAe11B,EAAf01B,IAAiBlT,IAAMxiB,EAANwiB;MAC9D,IAAAsT,IAAgCD,EAAoBrT,IAA/CnM,IAAOyf,EAAA,IAAEC,IAAcD,EAAA;MAC5B,IAAArR,IAAoC1M,MAA5B9K,IAAuBwX,EAAvBxX;MACR,IAAA9M,IAA8DkwB,KAAyB,CAAE,GAAjFvT,IAAY3c,EAAZ2c,IAAcwE,IAAgBnhB,EAAhBmhB,IAAkBT,IAAiB1gB,EAAjB0gB;MACxC,IAAQ1R,IAAoBumB,EAApBvmB;MACR,IAAAsT,IAAkDpX,EAAa,oBAAxDtF,IAAe0c,EAAA,IAAEuT,IAAsBvT,EAAA;MAC9C,IAAMwT,IAAkBzT,KAAUlB;MAElC,IAAIxE,KAAgBiZ,KAAkBE,GAAiB;QAAA,IAAAC,IACzBN,EAAmBpT;QAA9CnM,IAAO6f,EAAA;QAAEH,IAAcG,EAAA;AAC1B;MAEA,IAAMC,KACHppB,MAAsBipB,KAA0BnV,KAAqBkV;MAExE,IAAII,GAAqB;QAAA,IAAAC,GAAAC;QAEvB,IAAMC,KAAmBvwB,MAAqByuB,MAAavnB;QAC3D,IAAMspB,IAAoBlgB,EAAQjb,IAAIib,EAAQhb;QAC9C,IAAMm7B,IAAkBngB,EAAQlb,IAAIkb,EAAQppB;QAE5C,IAAMwpC,KAAYL,IAAA,IAAAA,EACf9nC,KAAiBgoC,MAAoBnnB,KAAmBonB,IAAoB,GAACH,EAC7E7nC,KAAkB+nC,KAAmBE,IAAkB,GAACJ,EACxD/nC,KAAgBioC,KAAmBnnB,KAAmBonB,IAAoB;QAACH,EAC5Er7B,MAAKu7B,KAAmBjgB,EAAQlb,IAAI,GAACi7B,EACrCp7B,QAAOs7B,IAAmBnnB,KAAmBkH,EAAQjb,IAAI,SAAU,GAACg7B,EACpEl7B,OAAMo7B,IAAmBnnB,IAAkB,UAAUkH,EAAQhb,IAAK;QAAC+6B,EAClE1nC,KAAW4nC,sBAAkCC,IAAiB,OAAKH;QAEtE,IAAMM,KAAaL,IAAAA,CAAAA,GAAAA,EAChBpoC,KAAgBqoC,IAAkBjgB,EAAQlb,IAAI,GAACk7B,EAC/CnoC,KAAkBooC,IAAkBjgB,EAAQjb,IAAI,GAACi7B,EACjDjoC,KAAmBkoC,IAAkBjgB,EAAQppB,IAAI,GAACopC,EAClDloC,KAAiBmoC,IAAkBjgB,EAAQhb,IAAI;QAACg7B;QAInDv8B,UAAU06B,KAAYxnB,GAAWypB;QACjC38B,UAAUkT,GAAW0pB;QAErBpkC,GAAWmtB,GAAO;UAChB+U,IAAUne;UACVsgB,KAAmBL;UACnB3mB,GAAuB6kB,IACnBkC,IACApkC,GAAW,CAAE,GAAEmkC,GAAcC;;AAErC;MAEA,OAAO;QACLE,IAAsBT;;;AAG5B;EC1BO,IAAMU,KAA4D,SAA5DA,4BACXlqB,GACAC;IAEA,IAAMzB,IAAM4M;IACZ,IACEkH,IASEtS,EATFsS,IACAuV,IAQE7nB,EARF6nB,IACAxnB,IAOEL,EAPFK,GACAD,IAMEJ,EANFI,GACA+iB,IAKEnjB,EALFmjB,IACA5Q,IAIEvS,EAJFuS,IACAE,IAGEzS,EAHFyS,IACAE,IAEE3S,EAFF2S,IACAoV,IACE/nB,EADF+nB;IAEF,IAAQznB,IAA4B9B,EAA5B8B;IACR,IAAM2S,IAAuBR,KAAWrS;IACxC,IAAM+pB,IAAO9mC,GAAKlI,GAAS;IAC3B,IAAMivC,IAGF;MACFC,SAAS,SAATA;QAAO,OAAQ;AAAK;MACpBC,WAAW,SAAXA,UAAYC;QAAc,OAAKA,MAAmB;AAAK;MACvDC,eAAe,SAAfA,cAAgBC;QAAkB,OAAKA,EAAmBC,SAAS;AAAW;MAC9EC,aAAa,SAAbA,YAAcC;QAAgB,OAAKA,MAAqB;AAAe;;IAEzE,IAAMC,IAAwB/qC,GAAKsqC;IAGnC,IAAMU,IAAiB;MACrB5wC,GAAQ+I;MACRhJ,GAAe;QAAEmV,GAAG;QAAGC,GAAG;;;IAE5B,IAAM07B,IAAmB;MACvB7wC,GAAQgJ;MACRjJ,GAAe,CAAE;;IAGnB,IAAM+wC,IAAmB,SAAnBA,iBAAoBC;MAExBtY,EAAwB3X,KAA6BiY,KAAwBgY;;IAG/E,IAAMC,IAA+B,SAA/BA,6BAAgCC;MACpC,IAAMC,IAA+BP,EAAsBQ,MAAK,SAACC;QAC/D,IAAMC,IAAaJ,EAAoBG;QACvC,OAAOC,KAAcnB,EAAgCkB,GAAWC;AAClE;MAGA,KAAKH;QACH,OAAO;UACLn5B,GAAQ;YAAEpD,GAAG;YAAGC,GAAG;;UACnBoD,GAAM;YAAErD,GAAG;YAAGC,GAAG;;;;MAIrBk8B,EAAiB;MAEjB,IAAMQ,IAAuBz5B,GAAiBwgB;MAC9C,IAAMkZ,IAAkB9Y,EAAwBxX,IAA4B;MAC5E,IAAMuwB,IAAoB56B,GACxBqyB,GACAhhC,KACA,SAAC8S;QACC,IAAM02B,IAA0B55B,GAAiBwgB;QAEjD,IACEtd,EAAM22B,aACND,EAAwB98B,MAAM28B,EAAqB38B,KACnD88B,EAAwB78B,MAAM08B,EAAqB18B;UAEnD0C,GAAgByD;;AAEpB,UACA;QACEhE,GAAU;QACVE,GAAO;;MAIXQ,GAAgB4gB,GAAsB;QACpC1jB,GAAG;QACHC,GAAG;;MAEL28B;MAEA,IAAMx5B,IAASF,GAAiBwgB;MAChC,IAAMqU,IAAah3B,GAAc2iB;MACjC5gB,GAAgB4gB,GAAsB;QACpC1jB,GAAG+3B,EAAWx3B;QACdN,GAAG83B,EAAWv3B;;MAGhB,IAAMw8B,IAAM95B,GAAiBwgB;MAC7B5gB,GAAgB4gB,GAAsB;QAEpC1jB,GAAGg9B,EAAIh9B,IAAIoD,EAAOpD,IAAI,MAAM+3B,EAAWx3B;QACvCN,GAAG+8B,EAAI/8B,IAAImD,EAAOnD,IAAI,MAAM83B,EAAWv3B;;MAGzC,IAAM6C,IAAOH,GAAiBwgB;MAC9B5gB,GAAgB4gB,GAAsBiZ;MACtCxvC,GAAI;QAAA,OAAM0vC;;MAEV,OAAO;QACLz5B,GAAAA;QACAC,GAAAA;;;IAGJ,IAAM45B,IAAoB,SAApBA,kBACJtoB,GACAuoB;MAEA,IAAMC,IAAa9wC,EAAI8vB,mBAAmB,MAAM,IAAI,IAAI;MACxD,IAAMvY,IAAS;QACbrD,GAAG+6B,EAAK3mB,EAAmBpU,IAAI28B,EAAmB38B;QAClDC,GAAG86B,EAAK3mB,EAAmBnU,IAAI08B,EAAmB18B;;MAGpD,OAAO;QACLD,GAAGqD,EAAOrD,IAAI48B,IAAav5B,EAAOrD,IAAI;QACtCC,GAAGoD,EAAOpD,IAAI28B,IAAav5B,EAAOpD,IAAI;;;IAG1C,IAAA2Z,IAAqDlvB,EACnDgxC,GACAznC,GAAKwM,IAAmBwQ,KAFnB4rB,IAAkBjjB,EAAA,IAAEkjB,IAAsBljB,EAAA;IAIjD,IAAAmjB,IAA2EryC,EAEzEgxC,GAAgBznC,GAAKuM,IAAeyQ,KAF/B+rB,IAA6BD,EAAA,IAAEE,IAAiCF,EAAA;IAGvE,IAAAG,IACExyC,EAAwBgxC,IADnByB,IAAyBD,EAAA,IAAEE,IAA6BF,EAAA;IAE/D,IAAAG,IAAiC3yC,EAAkCixC,IAA5D2B,IAAsBD,EAAA;IAC7B,IAAAE,IAA0D7yC,EAAwBgxC,IAA3E8B,IAAkBD,EAAA,IAAEE,IAA2BF,EAAA;IACtD,IAAAG,IAAmChzC,EAAwCixC,IAApEgC,IAAwBD,EAAA;IAC/B,IAAAE,IAAoClzC,EAClC;MACEI,GAAQ,SAARA,OAAS+yC,GAASC;QAAO,OAAKxqC,GAAMuqC,GAASC,GAASrC;AAAsB;MAC5E5wC,GAAe,CAAE;QAEnB;MAAA,OAAOgW,GAAcoQ,KAAa3S,UAAU2S,GAAWwqB,KAAyB;AAAE,SAL7EsC,IAAyBH,EAAA;IAOhC,IAAAI,IACEtzC,EAA+B;MAC7BI,GAAQ,SAARA,OAAS+yC,GAASvyC;QAAM,OACtBwI,GAAQ+pC,EAAQh7B,GAAQvX,EAAOuX,MAAW/O,GAAQ+pC,EAAQ/6B,GAAMxX,EAAOwX;AAAK;MAC9EjY,GAAe+X;QAJZq7B,IAA+BD,EAAA,IAAEE,IAAmCF,EAAA;IAO3E,IAAMpa,IAAyB9c,GAC7B2J;IAGF,IAAM0tB,IAAuC,SAAvCA,qCACJ1sB,GACAjS;MAEA,IAAM4+B,IAAS5+B,IACXiM,KACAC;MACJ,OAAA,KAAU0yB,IAASprC,GAAsBye;;IAE3C,IAAM4sB,IAA2B,SAA3BA,yBAA4BC;MAEhC,IAAMC,IAAmC,SAAnCA,iCAAoC/+B;QAAsB,OAC7D,EAAC3M,GAAYC,GAAWC,KAA+BkP,KAAI,SAAC9D;UAAK,OAChEggC,EAAqChgC,GAAOqB;;AAC7C;MACH,IAAMg/B,IAA6BD,EAAiC,MACjEvtC,OAAOutC,KACPtlC,KAAK;MAERsqB,EAAwBib;MACxBjb,EACG7yB,GAAK4tC,GACHr8B,KAAI,SAACgoB;QAAI,OACRkU,EAAqCG,EAAsBrU,IAAOA,MAAS;AAAI,UAEhFhxB,KAAK,MACR;;IAIJ,OAAO,SAAArE,GAAAqP;MAGH,IAFAqL,IAAY1a,EAAZ0a,IAAcglB,IAAqB1/B,EAArB0/B,IAAuBqF,IAAe/kC,EAAf+kC,IAAiBlT,IAAM7xB,EAAN6xB;MAAM,IAC5DoU,IAAoB52B,EAApB42B;MAEF,IAAAz2B,IACEkwB,KAAyB,IADaxP,IAAiB1gB,EAAjB0gB,IAAmB5E,IAAO9b,EAAP8b,IAASqG,IAAqBniB,EAArBmiB;MAEpE,IAAMkY,IACJ7a,KACAA,EAAuBjT,EACrBC,GACAC,GACA8oB,GACAvqB,GACAE;MAGJ,IAAAmd,IACEgS,KAA6C,CAAE,GADzCtqB,IAAgBsY,EAAhBtY,GAAkBW,IAAoB2X,EAApB3X,GAAsB5B,IAAqBuZ,EAArBvZ;MAGhD,IAAA7B,IACEnC,GAAgCI,GAAcF,IADzCnF,IAA4BoH,EAAA,IAAE7B,IAAmC6B,EAAA;MAExE,IAAAqV,IAAoCpX,EAAa,aAA1CjF,IAAQqc,EAAA,IAAE8O,IAAe9O,EAAA;MAChC,IAAMgY,IAAmBjvB,GAAkBpF,EAAS5K;MACpD,IAAMk/B,IAAmBlvB,GAAkBpF,EAAS3K;MAEpD,IAAMk/B,IACJ;MAQF,IAAIC,KAAoB/B,EAAuBrW;MAC/C,IAAIqY,KAA0B7B,EAAkCxW;MAChE,IAAIsY,KAAqB3B,EAA8B3W;MACvD,IAAIuY,KAAoBvB,EAA4BhX;MAEpD,IAAIjX,KAAuC0B;QACzCqS,EAAwBzX,KAAmC7B;;MAI3D,IAAIjQ,GAAakpB,GAAO5X,IAAmBF;QACzCwwB,EAAiB;;MAGnB,IAAAhP,KAA8B9X,IAAuBA,MAAyB,IAAvEqP,KAAmByI,GAAA;MAE1B,IAAAqS,KAAwBJ,KAAoBhC,EAAmBpW,IAAxDpS,KAAY4qB,GAAA;MACnB,IAAAC,KAA8BJ,KAC5B9B,EAA8BvW,IADzBrS,KAAkB8qB,GAAA;MAEzB,IAAMvC,KAAqBp8B,GAAc0Q;MACzC,IAAMkuB,KAAkBtb,KAAwBzjB,GAAcu4B;MAC9D,IAAMyG,KAA2B;QAC/Bp/B,GAAG+6B,EAAK3mB,GAAmBpU,IAAIqU,GAAarU;QAC5CC,GAAG86B,EAAK3mB,GAAmBnU,IAAIoU,GAAapU;;MAG9C,IAAMo/B,KAA2B;QAC/Br/B,GAAG+6B,GACAoE,KACGA,GAAgBn/B,IAChB28B,GAAmB38B,IAAI+6B,EAAK4B,GAAmB38B,IAAIoU,GAAmBpU,MACxEqU,GAAarU;QAEjBC,GAAG86B,GACAoE,KACGA,GAAgBl/B,IAChB08B,GAAmB18B,IAAI86B,EAAK4B,GAAmB18B,IAAImU,GAAmBnU,MACxEoU,GAAapU;;MAInBkkB,MAAuBA;MAEvB6a,KAAoBxB,EAAmB6B;MACvCN,KAAqB5B,EACnBT,EAAkB0C,IAA0BC,KAC5C5Y;MAIJ,IAAA6Y,KAA4CN,IAArCO,KAAYD,GAAA,IAAEE,KAAmBF,GAAA;MACxC,IAAAG,KAAgDV,IAAzC97B,KAAcw8B,GAAA,IAAEC,KAAqBD,GAAA;MAC5C,IAAAE,KAAwDb,IAAjD1qB,KAAkBurB,GAAA,IAAEC,KAAyBD,GAAA;MACpD,IAAAE,KAA4ChB,IAArCxqB,KAAYwrB,GAAA,IAAEC,KAAmBD,GAAA;MACxC,IAAAE,KAA0CzC,EAAuB;QAC/D79B,GAAGwD,GAAejD,IAAI;QACtBN,GAAGuD,GAAehD,IAAI;UAFjB2P,KAAWmwB,GAAA,IAAEC,KAAkBD,GAAA;MAItC,IAAME,KACHvB,KAAoBC,MAAqB/uB,GAAYnQ,KAAKmQ,GAAYlQ,MACtEg/B,KAAoB9uB,GAAYnQ,MAAMmQ,GAAYlQ,KAClDi/B,KAAoB/uB,GAAYlQ,MAAMkQ,GAAYnQ;MACrD,IAAMygC,KACJrF,KACA/V,KACAyB,KACAuZ,MACAF,MACAJ,MACAE,MACAlK,KACAhmB,KACAovB;MACF,IAAM1sB,KAAwBvC,GAA4BC,IAAavF;MACvE,IAAA81B,KAA8CxC,EAC5CzrB,GAAsB3B,IADjBkB,KAAa0uB,GAAA,IAAEC,KAAoBD,GAAA;MAG1C,IAAAE,KAA0DtC,EAA0BtX,IAA7EsV,KAAmBsE,GAAA,IAAEC,KAA0BD,GAAA;MAEtD,IAAME,KACJzb,KAAqB5E,KAAWogB,MAA8BN,MAAsBvZ;MACtF,IAAA+Z,KAAsDD,KAClDtC,EAAgCnC,EAA6BC,KAAsBtV,KACnFyX,KAFGuC,KAAiBD,GAAA,IAAEE,KAAwBF,GAAA;MAIlD,IAAIN,IAAqB;QACvBE,MAAwB/B,EAAyBnsB,GAAsB3B;QAEvE,IAAI2C,KAAyBiB;UAC3BpW,UACEkT,GACAiC,EACEhB,IACAynB,GACAxlB,EAAiBjC,IAAuBkC,IAAoBC;;AAIpE;MAEAunB,EAAiB;MAEjB9hC,GAAmBopB,GAAO5X,IAAmBF,IAAqB60B;MAClEnmC,GAAmB2+B,GAAUzsB,IAAsBZ,IAAqB60B;MAExE1pC,GAAWsa,GAAqB;QAC9BN,GAAgBkB;QAChByZ,IAAe;UACbzrB,GAAG8/B,GAAav/B;UAChBN,GAAG6/B,GAAat/B;;QAElBgrB,IAAiB;UACfxrB,GAAGwD,GAAejD;UAClBN,GAAGuD,GAAehD;;QAEpB20B,IAAchlB;QACd4a,IAAoBznB,GAA0B09B,IAAmBx9B;;MAGnE,OAAO;QACLyxB,IAAuB0L;QACvB5L,IAAsBgL;QACtB/K,IAAwBiL;QACxB/K,IAA2B+L,MAA4BhB;QACvDiB,IAAWJ;;;AAGjB;EClVO,IAAMK,KAAuB,SAAvBA,qBAAwBpqC;IAAgD,IAAAod;IACnF,IAAAitB,IAAsD5K,GAA6Bz/B,IAA5E4jB,IAAQymB,EAAA,IAAEC,IAAuBD,EAAA,IAAEE,IAAQF,EAAA;IAClD,IAAMnd,IAA6B;MACjC+U,IAAU;QACRr5B,GAAG;QACHC,GAAG;QACHnO,GAAG;QACHoO,GAAG;;MAELs7B,IAAkB;MAClBhnB,IAAqBA,IAAAA,CAAAA,GAAAA,EAClBrhB,KAAiB,GAACqhB,EAClBphB,KAAkB,GAACohB,EACnBthB,KAAgB,GAACshB,EACjB1hB,KAAgB,GAAC0hB,EACjBzhB,KAAkB,GAACyhB,EACnBvhB,KAAmB,GAACuhB,EACpBxhB,KAAiB;MAACwhB;MAErBsX,IAAe;QAAEzrB,GAAG;QAAGC,GAAG;;MAC1BurB,IAAiB;QAAExrB,GAAG;QAAGC,GAAG;;MAC5B6Q,GAAgB;QACd9Q,GAAG3M;QACH4M,GAAG5M;;MAEL8hC,IAAc;QACZn1B,GAAG;QACHC,GAAG;;MAEL8qB,IAAoB5nB;;IAEtB,IAAQqgB,IACN7I,EADM6I,IAASE,IACf/I,EADe+I,IAAsBnS,IACrCoJ,EADqCpJ,GAAmBwS,IACxDpJ,EADwDoJ;IAE1D,IAAAkF,IAA+D1M,MAAvD9K,IAAuBwX,EAAvBxX,GAAyB7B,IAAyBqZ,EAAzBrZ;IACjC,IAAM+B,KACHF,MAA4B7B,EAA0B5P,KAAK4P,EAA0B3P;IAExF,IAAMshC,IAA2C,EAC/CtH,GAA2Btf,IAC3Bwf,GAA2Bxf,GAAUsJ,IACrCoX,GAA4B1gB,GAAUsJ;IAGxC,OAAO,EACLod,GACA,SAACG;MACC,IAAMrc,IAAyC,CAAA;MAC/C,IAAMsc,IAAqB9vB;MAC3B,IAAM4S,IAAekd,KAAsBv+B,GAAiBwgB;MAC5D,IAAMc,IAA8BD,KAAgBR;MAEpDlzB,KAAK0wC,IAAgB,SAACG;QACpB5qC,GAAWquB,GAAauc,EAAcF,GAAYrc,MAAgB,CAAA;AACpE;MAEAriB,GAAgB4gB,GAAsBa;MACtCC,KAA+BA;OAC9BjT,KAAqBzO,GAAgB0gB,GAAS;MAE/C,OAAO2B;AACT,OACAlB,GACAtJ,GACA2mB;AAEJ;EChEO,IAAMK,KAAe,SAAfA,aACX5qC,GACA7L,GACA02C,GACAC,GACAvyB;IAEA,IAAIwyB,IAA6B;IACjC,IAAMhf,IAAmBnK,GAAkBztB,GAAS,CAAE;IACtD,IAAAk2C,IAMID,GAAqBpqC,IALvBgrC,IAAoBX,EAAA,IACpBY,IAAoBZ,EAAA,IACpBhwB,IAAmBgwB,EAAA,IACnBjwB,IAAsBiwB,EAAA,IACtBa,IAAsBb,EAAA;IAExB,IAAAc,IAA0Erf,GACxE1R,GACAC,GACA0R,IACA,SAACqf;MAECh3C,EAAO,CAAA,GAAIg3C;AACb,SAPKC,IAAoBF,EAAA,IAAEG,IAAoBH,EAAA,IAAE7wB,IAAmB6wB,EAAA;IAStE,IAAA9N,IACEpB,GACEj8B,GACA7L,GACAmmB,GACAD,GACAD,GACA7B,IAPGgzB,IAAqBlO,EAAA,IAAEmO,IAAqBnO,EAAA,IAAIoO,IAAuBpO,EAAA;IAU9E,IAAMqO,IAAuB,SAAvBA,qBAAwBC;MAAuB,OACnDzxC,GAAKyxC,GAAOlG,MAAK,SAACtrC;QAAG,SAAOwxC,EAAMxxC;;AAA2B;IAE/D,IAAM/F,IAAS,SAATA,OACJq2C,GACAmB;MAEA,IAAIf;QACF,OAAO;;MAGT,IACmBgB,IAIfpB,EAJFqB,IACQC,IAGNtB,EAHFxa,IACAD,IAEEya,EAFFza,IACAgc,IACEvB,EADFuB;MAGF,IAAMF,IAAkBD,KAAqB;MAC7C,IAAM5b,MAAW8b,MAAahB;MAC9B,IAAMkB,IAAqC;QACzCnzB,IAAc8I,GAAkBztB,GAAS23C,GAAiB7b;QAC1D6b,IAAAA;QACA7b,IAAAA;;MAGF,IAAI+b,GAAiB;QACnBR,EAAsBS;QACtB,OAAO;AACT;MAEA,IAAMC,IACJN,KACAN,EACEvrC,GAAW,CAAA,GAAIksC,GAAmB;QAChCjc,IAAAA;;MAIN,IAAMmc,IAAiBlB,EACrBlrC,GAAW,CAAA,GAAIksC,GAAmB;QAChC9I,IAAiB7oB;QACjBwjB,IAAuBoO;;MAI3BV,EACEzrC,GAAW,CAAE,GAAEksC,GAAmB;QAChCnO,IAAuBoO;QACvBnO,IAAuBoO;;MAI3B,IAAMC,IAAuBV,EAAqBQ;MAClD,IAAMG,IAAuBX,EAAqBS;MAClD,IAAMp3C,IACJq3C,KAAwBC,MAAyBtrC,GAAc+qC,MAAoB7b;MAErF8a,IAA6B;MAE7Bh2C,KACE+1C,EAAUL,GAAY;QACpB3M,IAAuBoO;QACvBnO,IAAuBoO;;MAG3B,OAAOp3C;;IAGT,OAAO,EACL;MACE,IAAQmtC,IACN9nB,EADM8nB,IAA8BvV,IACpCvS,EADoCuS,IAAsBK,IAC1D5S,EAD0D4S;MAE5D,IAAMsf,IAAgBngC,GAAiB+1B;MACvC,IAAMzpB,IAAa,EAAC4yB,KAAwBL,KAAwBO;MACpE,IAAM9d,IAA8BT;MAEpCjhB,GAAgB4gB,GAAsB2f;MACtC7e;MAEA,OAAOhwB,GAAKpC,GAAiBod;AAC9B,OACDrkB,GACA;MAAA,OAAO;QACLm4C,IAAsBjyB;QACtBkyB,IAAsBnyB;;AACvB,OACD;MACEoyB,IAAyBryB;MACzBsyB,IAA0BjB;OAE5BP;AAEJ;EC1MA,IAAMyB,KAAyD,IAAI/mB;EAO5D,IAAMgnB,KAAc,SAAdA,YAAe5sC,GAAiBiQ;IAC3C08B,GAAkB39B,IAAIhP,GAAQiQ;AAChC;EAMO,IAAM48B,KAAiB,SAAjBA,eAAkB7sC;IAC7B2sC,GAAkBtmB,OAAOrmB;AAC3B;EAMO,IAAM8sC,KAAc,SAAdA,YAAe9sC;IAAe,OACzC2sC,GAAkBj+B,IAAI1O;AAAO;EC+RlB+sC,IAAAA,KAA6C,SAA7CA,kBACX/sC,GACA7L,GACA64C;IAEA,IAAA9a,IAA+B1M,MAAvBb,IAAkBuN,EAAlBvN;IACR,IAAMsoB,IAAkB10C,EAAcyH;IACtC,IAAMktC,IAAiBD,IAAkBjtC,IAASA,EAAOA;IACzD,IAAMmtC,IAAoBL,GAAYI;IACtC,IAAI/4C,MAAYg5C,GAAmB;MACjC,IAAIxnB,IAAY;MAChB,IAAMlN,IAA6B;MACnC,IAAM20B,IAAsE,CAAA;MAC5E,IAAMv6B,IAAkB,SAAlBA,gBAAmBwO;QACvB,IAAMgsB,IAA6BxsC,GAA0BwgB;QAC7D,IAAMisB,IAAiBh9B,GACrBwC;QAEF,OAAOw6B,IACHA,EAAeD,GAA4B,QAC3CA;;MAEN,IAAME,IAAkCxtC,GACtC,CAAE,GACF4kB,KACA9R,EAAgB1e;MAElB,IAAAgvB,IACE/U,MADKo/B,IAAcrqB,EAAA,IAAEsqB,IAAkBtqB,EAAA,IAAEuqB,IAAkBvqB,EAAA;MAE7D,IAAAwqB,IACEv/B,GAAuB4+B,IADlBY,IAAgBD,EAAA,IAAEE,IAAoBF,EAAA,IAAEG,IAAoBH,EAAA;MAEnE,IAAMv+B,IAA0C,SAA1CA,aAA2CzY,GAAM2E;QACrDwyC,EAAqBn3C,GAAM2E;QAC3BoyC,EAAmB/2C,GAAM2E;;MAE3B,IAAAyyC,IACEnD,GACE5qC,GACAutC,IACA;QAAA,OAAM5nB;AAAS,WACf,SAAAvnB,GAAAqP;QAAkF,IAA/Eq+B,IAAe1tC,EAAf0tC,IAAiB7b,IAAM7xB,EAAN6xB;QAAM,IAAM6N,IAAqBrwB,EAArBqwB,IAAuBC,IAAqBtwB,EAArBswB;QACrD,IACExT,IAMEuT,EANFvT,IACA+D,IAKEwP,EALFxP,IACAG,IAIEqP,EAJFrP,IACAM,IAGE+O,EAHF/O,IACAE,IAEE6O,EAFF7O,IACAvF,IACEoU,EADFpU;QAGF,IACEsU,IAIED,EAJFC,IACAC,IAGEF,EAHFE,IACAC,IAEEH,EAFFG,IACAC,IACEJ,EADFI;QAGF/uB,EAAa,WAAW,EAEtBc,GACA;UACEke,aAAa;YACX4f,eAAezjB;YACf0jB,oBAAoB3f;YACpB7C,0BAA0BgD;YAC1Bua,uBAAuBhL;YACvBkL,yBAAyBjL;YACzB2L,wBAAwB1L;YACxBgM,4BAA4B/L;YAC5BuF,mBAAmB3U;YACnBmf,gBAAgBjf;YAChB3W,UAAUoR;;UAEZ7H,gBAAgBiqB,KAAmB,CAAE;UACrCl3C,SAASq7B;;AAGd,WAED,SAACzX;QAAW,OAAKpJ,EAAa,UAAU,EAACc,GAAUsI;WA5ChD21B,IAAeJ,EAAA,IAAEK,IAAYL,EAAA,IAAEM,IAAWN,EAAA,IAAEO,IAAcP,EAAA,IAAEQ,IAAcR,EAAA;MA+CjF,IAAMloB,IAAU,SAAVA,QAAW0kB;QACfsC,GAAeK;QACf7xC,EAAgBod;QAEhBkN,IAAY;QAGZvW,EAAa,aAAa,EAACc,GAAUq6B;QACrCkD;QACAI;;MAGF,IAAM39B,IAA8B;QAClC/b,kBAAAA,QAAQktB,GAA6BmtB;UACnC,IAAIntB,GAAY;YACd,IAAMotB,IAAOD,IAAO7pB,MAAuB,CAAA;YAC3C,IAAM9C,IAAiBV,GACrBosB,GACAxtC,GAAW0uC,GAAM57B,EAAgBwO;YAEnC,KAAKtgB,GAAc8gB,IAAiB;cAClC9hB,GAAWwtC,GAAgB1rB;cAC3BusB,EAAa;gBAAEtC,IAAiBjqB;;AAClC;AACF;UACA,OAAO9hB,GAAW,IAAIwtC;AACvB;QACDmB,IAAId;QACJe,KAAK,SAALA,IAAMh4C,GAAMoU;UACVpU,KAAQoU,KAAY8iC,EAAqBl3C,GAAMoU;AAChD;QACDmiB,OAAK,SAALA;UACE,IAAA0hB,IAAuDP,KAA/C9B,IAAoBqC,EAApBrC,IAAsBC,IAAoBoC,EAApBpC;UAC9B,IAAQ5vB,IAAoB2vB,EAApB3vB;UACR,IACE8X,IAOE8X,EAPF9X,IACAD,IAME+X,EANF/X,IACA1a,IAKEyyB,EALFzyB,GACAqkB,IAIEoO,EAJFpO,IACA6D,IAGEuK,EAHFvK,IACAmC,IAEEoI,EAFFpI,IACApQ,IACEwY,EADFxY;UAEF,OAAOj0B,GACL,CAAA,GACA;YACEgpC,cAAcrU;YACdjoB,gBAAgBgoB;YAChBxZ,eAAelB;YACfX,aAAaglB;YACb6L,mBAAmB;cACjBt9B,OAAOqnB,EAAmB3nB;cAC1BO,KAAKonB,EAAmB1nB;;YAE1BwX,SAASme;YACTzuB,iBAAiB4wB;YACjByK,cAAcjyB;YACd+I,WAAAA;;AAGL;QACD/B,UAAQ,SAARA;UACE,IAAAkrB,IAQIR,EAAe7B,IAPjBhgB,IAAOqiB,EAAPriB,IACAC,IAAKoiB,EAALpiB,IACAuV,IAAQ6M,EAAR7M,IACAxnB,IAASq0B,EAATr0B,GACAmS,IAAQkiB,EAARliB,IACAD,IAAoBmiB,EAApBniB,IACA4Q,IAAmBuR,EAAnBvR;UAEF,IAAAwR,IAAmCT,EAAe5B,IAA1ChV,IAAWqX,EAAXrX,IAAaI,IAASiX,EAATjX;UACrB,IAAMkX,IAA8B,SAA9BA,4BACJ/Z;YAEA,IAAQZ,IAAgCY,EAAhCZ,IAAS4C,IAAuBhC,EAAvBgC,IAAQ/B,IAAeD,EAAfC;YACzB,OAAO;cACL4B,WAAW5B;cACX6B,OAAOE;cACPD,QAAQ3C;;;UAGZ,IAAM4a,IAAkC,SAAlCA,gCACJC;YAEA,IAAQvX,IAAiCuX,EAAjCvX,IAAsBC,IAAWsX,EAAXtX;YAC9B,IAAMuX,IAAsBH,EAA4BrX,EAAqB;YAE7E,OAAO53B,GAAW,CAAE,GAAEovC,GAAqB;cACzCvuC,OAAO,SAAPA;gBACE,IAAM3D,IAAS+xC,EAA4BpX;gBAC3CwW,EAAa;kBAAEpC,IAAiB;;gBAChC,OAAO/uC;AACT;;;UAGJ,OAAO8C,GACL,CAAA,GACA;YACEC,QAAQysB;YACR5I,MAAM6I;YACN5I,SAASme,KAAYxnB;YACrBsJ,UAAUtJ;YACVuJ,SAAS4I,KAAYnS;YACrBqnB,qBAAqBnV;YACrBoV,oBAAoBxE;YACpB6R,qBAAqBH,EAAgCvX;YACrD2X,mBAAmBJ,EAAgCnX;;AAGxD;QACD1jC,QAAQ,SAARA,OAAS67B;UAAgB,OAAKme,EAAa;YAAEne,IAAAA;YAAQD,IAAc;;AAAO;QAC1EnK,SAASpoB,GAAKooB,GAAS;QACvBnW,QAAQ,SAARA,OAAmCA;UAAS,OAC1C09B,EAA8BlzC,GAAKwV,GAAQ;AAE9B;;MAGjB/U,EAAK8d,GAAY,EAAC81B;MAGlB3B,GAAYM,GAAgBh9B;MAG5BP,GAA8BL,IAAey9B,IAAmB,EAC9D78B,GACAs9B,GACAJ;MAGF,IACErb,GACEuc,EAAe7B,GAAwB5f,KACtCogB,KAAmBjtC,EAAOkkB,SAE7B;QACA2B,EAAQ;QACR,OAAO3V;AACT;MAEAvV,EAAK8d,GAAY01B;MAEjB/+B,EAAa,eAAe,EAACc;MAE7BA,EAAS9b;MAET,OAAO8b;AACT;IACA,OAAOi9B;AACT;EAEAJ,GAAkBr9B,SAAS,SAAC4/B;IAC1B,IAAMC,IAAQx3C,EAAQu3C;IACtB,IAAME,IACJD,IAAQD,IAAU,EAACA;IACrB,IAAMryC,IAASuyC,EAAa/jC,KAC1B,SAACiE;MAAM,OAAKC,GAA8BD,GAAQq9B,IAAmB;;IAEvEv9B,GAAWggC;IACX,OAAOD,IAAQtyC,IAAUA,EAAO;AAClC;EACA8vC,GAAkB0C,QAAQ,SAACx/B;IACzB,IAAMy/B,IAAYz/B,KAAeA,EAAiC2T;IAClE,IAAMA,IAAW9rB,EAAW43C,MAAcA;IAC1C,OAAOr3C,EAAcurB,QAAekpB,GAAYlpB,EAAS5jB;AAC3D;EACA+sC,GAAkBn0B,MAAM;IACtB,IAAA+2B,IAWInqB,MAVF7K,IAAqBg1B,EAArBh1B,GACA9B,IAAyB82B,EAAzB92B,GACA6B,IAAuBi1B,EAAvBj1B,GACA4J,IAAeqrB,EAAfrrB,GACAQ,IAA4B6qB,EAA5B7qB,IACAC,IAAqB4qB,EAArB5qB,IACAP,IAAyBmrB,EAAzBnrB,IACAC,IAAyBkrB,EAAzBlrB,IACAE,IAAkBgrB,EAAlBhrB,IACAC,IAAkB+qB,EAAlB/qB;IAEF,OAAO7kB,GACL,CAAA,GACA;MACE6vC,gBAAgBj1B;MAChBk1B,oBAAoBh3B;MACpBi3B,kBAAkBp1B;MAClB6Z,gBAAgBjQ;MAChBX,6BAA6BmB;MAC7BX,sBAAsBY;MAEtBV,0BAA0BG;MAC1BurB,0BAA0BtrB;MAC1BL,mBAAmBO;MACnBqrB,mBAAmBprB;;AAGzB;EACAmoB,GAAkBjrB,QAAQE;EAC1B+qB,GAAkBlnC,oBAAoBE;;;;;;", "x_google_ignoreList": [26]}