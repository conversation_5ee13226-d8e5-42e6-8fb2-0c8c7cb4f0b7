using BaseGIS.Core.Entities;

namespace BaseGIS.Web.Models
{
    public class UpdateWizardViewModel
    {
        public string Id { get; set; }
        public string PathFile { get; set; }
        public List<FieldInfo> FieldInfos { get; set; } = new List<FieldInfo>();
        public List<string> ShapeFileColumns { get; set; } = new List<string>();
        public List<string> ExcelColumns { get; set; } = new List<string>();
        public string FileType { get; set; } // "shapefile" or "excel"
    }
}
