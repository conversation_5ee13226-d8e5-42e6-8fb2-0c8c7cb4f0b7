﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <Compile Remove="SeedData.cs" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BaseGIS.Core\BaseGIS.Core.csproj" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

	<ItemGroup>
	 
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.3" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.3" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite" Version="9.0.3" />
		<PackageReference Include="NetTopologySuite.IO.GeoJSON" Version="4.0.0" />
		<PackageReference Include="NetTopologySuite.IO.Shapefile" Version="2.1.0" />
		<PackageReference Include="GeoJSON.Net" Version="1.2.19" />
		<PackageReference Include="ProjNet" Version="2.0.0" />
		<PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.0.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Migrations\" />
	</ItemGroup>

	<ItemGroup>
	  <None Include="Persistence\SeedData.cs" />
	</ItemGroup>
</Project>
