using System;

namespace BaseGIS.Core.Entities
{
    public static class FieldInfoUtils
    {
        public static bool TryConvertValue(object value, FieldInfo fieldInfo, ref object convertedValue)
        {
            try
            {
                switch (fieldInfo.FieldType)
                {
                    case "domain":
                    case "multidomain":
                        convertedValue = Convert.ToString(value);
                        return true;
                    case "int":
                        int intVal;
                        bool intParsed = Int32.TryParse(Convert.ToString(value), out intVal);
                        convertedValue = intVal;
                        return intParsed;
                    case "bigint":
                        long bigintVal;
                        bool longParsed = Int64.TryParse(Convert.ToString(value), out bigintVal);
                        convertedValue = bigintVal;
                        return longParsed;
                    case "numeric":
                        double dblVal;
                        bool dblParsed = double.TryParse(Convert.ToString(value), out dblVal);
                        convertedValue = dblVal;
                        return dblParsed;
                    case "datetime":
                        convertedValue = Convert.ToString(value);
                        return true;
                    case "nvarchar":
                        convertedValue = Convert.ToString(value);
                        return true;
                    default:
                        return false;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        public static Type GetFieldDataType(FieldInfo fieldInfo)
        {
            switch (fieldInfo.FieldType)
            {
                case "domain":
                case "multidomain":
                    return typeof(string);
                case "int":
                    return typeof(Int32);
                case "bigint":
                    return typeof(Int64);
                case "numeric":
                    return typeof(double);
                case "datetime":
                    return typeof(string);
                default:
                    return typeof(string);
            }
        }
    }
} 