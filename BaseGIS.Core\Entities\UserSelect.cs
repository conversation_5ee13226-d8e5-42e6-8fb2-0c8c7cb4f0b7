using System.ComponentModel.DataAnnotations;

namespace BaseGIS.Core.Entities
{
    public class UserSelect
    {
        [Key]
        public int ID { get; set; }

        [StringLength(10, ErrorMessage = "نام حداکثر می بایست 100 کارکتر باشد")]
        public string UserID { get; set; }

        [StringLength(10, ErrorMessage = "نام حداکثر می بایست 100 کارکتر باشد")]
        public string PageID { get; set; }

        public string Table { get; set; }
        public string OIDs { get; set; }
        public string Date { get; set; }

        public bool IsHighlight { get; set; }
        public string Where { get; set; }

    }
}