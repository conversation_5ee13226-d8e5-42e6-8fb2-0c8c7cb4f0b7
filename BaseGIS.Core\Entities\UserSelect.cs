using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BaseGIS.Core.Entities
{
    /// <summary>
    /// جدول ذخیره انتخاب‌های کاربران برای لایه‌ها
    /// </summary>
    [Table("UserSelects")]
    public class UserSelect
    {
        /// <summary>
        /// شناسه یکتا
        /// </summary>
        [Key]
        public int ID { get; set; }

        /// <summary>
        /// شناسه کاربر
        /// </summary>
        [Required]
        [StringLength(450)]
        public string UserID { get; set; } = string.Empty;

        /// <summary>
        /// شناسه صفحه (برای سازگاری با کد قبلی)
        /// </summary>
        [StringLength(50)]
        public string? PageID { get; set; }

        /// <summary>
        /// نام جدول/لایه
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Table { get; set; } = string.Empty;

        /// <summary>
        /// شناسه‌های رکوردهای انتخاب شده (جدا شده با کاما)
        /// </summary>
        [Required]
        public string OIDs { get; set; } = string.Empty;

        /// <summary>
        /// تاریخ انتخاب
        /// </summary>
        [Required]
        public string Date { get; set; } = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss");

        /// <summary>
        /// آیا انتخاب باید هایلایت شود
        /// </summary>
        public bool IsHighlight { get; set; } = true;

        /// <summary>
        /// شرط WHERE برای انتخاب
        /// </summary>
        public string? Where { get; set; }

        /// <summary>
        /// شناسه لایه (TableInfo.Id) - فیلد جدید
        /// </summary>
        public int? LayerId { get; set; }

        /// <summary>
        /// نوع انتخاب (geometry, manual, query)
        /// </summary>
        [StringLength(50)]
        public string SelectionType { get; set; } = "geometry";

        /// <summary>
        /// اطلاعات اضافی انتخاب (JSON)
        /// </summary>
        [StringLength(2000)]
        public string? SelectionMetadata { get; set; }

        /// <summary>
        /// آیا انتخاب فعال است
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// رابطه با جدول TableInfo
        /// </summary>
        [ForeignKey("LayerId")]
        public virtual TableInfo? TableInfo { get; set; }
    }
}