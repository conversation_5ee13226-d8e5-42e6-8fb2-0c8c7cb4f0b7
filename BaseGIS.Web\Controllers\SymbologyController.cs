﻿using BaseGIS.Core.Entities;
using BaseGIS.Infrastructure.Persistence;
using BaseGIS.Web.Helper;
using BaseGIS.Web.ViewModels;

using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace BaseGIS.Web.Controllers
{
    public class SymbologyController : Controller
    {
        private static ApplicationDbContext dbContext;
        private readonly IConfiguration _configuration;

        public SymbologyController(ApplicationDbContext _dbContext, IConfiguration configuration)
        {
            dbContext = _dbContext;
            _configuration = configuration;
        }
        //
        // GET: /Symbology/

        public ActionResult Index()
        {
            SymbologyInfo sym = null;
            string id = null;
            string tblId = null;

            if (Request.Query.TryGetValue("id", out var idValue))
            {
                id = idValue.ToString();
            }

            if (Request.Query.TryGetValue("tblid", out var tblIdValue))
            {
                tblId = tblIdValue.ToString();
            }

            if (!string.IsNullOrEmpty(id))
            {
                int ids = int.Parse(id);
                sym = dbContext.SymbologyInfos.Include("TableInfo").Include("TableInfo.FieldInfos").Where(a => a.Id == ids).FirstOrDefault();
            }
            else
            {
                int ids = int.Parse(tblId);
                var tblInfo = dbContext.TableInfos.Where(a => a.Id == ids).FirstOrDefault();

                Symbology symbo = new Symbology();
                if (tblInfo.DatasetType.ToString().ToLower() == "point")
                {
                    Random random = new Random();
                    //var color = System.Drawing.Color.FromArgb(rand.Next(0, 255), rand.Next(0, 255), rand.Next(0, 255));
                    var color = String.Format("#{0:X6}", random.Next(0x1000000));
                    SimpleSymbol symbol = new SimpleSymbol()
                    {
                        Angle = 0,
                        Color = color,
                        Offset = new Position2D(0, 0),
                        Opacity = 1,
                        OutlineColor = "#000000",
                        OutlineOpacity = 1,
                        OutlineWidth = 1,
                        PointShape = (PointShape)random.Next(1, 7),
                        Size = new Size2D(10, 10),
                        SymbolType = SymbolType.Simple,
                        UseOutline = true,
                        XmlColor = color,
                        XmlOutlineColor = "#ffffff"
                    };

                    PointCategory cat = new PointCategory();
                    cat.FilterExpression = "";
                    cat.LegendText = "";
                    cat.Symbol = symbol;

                    List<Category> cats = new List<Category>();

                    cats.Add(cat);
                    symbo.GeometryType = GeometryType.Point;
                    symbo.RenderType = RenderType.Simple;
                    symbo.Categories = cats;

                }
                else if (tblInfo.DatasetType.ToString().ToLower() == "polyline")
                {
                    Random random = new Random();
                    //var color = System.Drawing.Color.FromArgb(rand.Next(0, 255), rand.Next(0, 255), rand.Next(0, 255));
                    var color = String.Format("#{0:X6}", random.Next(0x1000000));
                    Outline outline = new Outline()
                    {
                        Color = color,
                        DashStyle = DashStyle.Solid,
                        Opacity = 1,
                        Width = 1

                    };


                    LineCategory cat = new LineCategory();
                    cat.FilterExpression = "";
                    cat.LegendText = "";
                    cat.Outline = outline;

                    List<Category> cats = new List<Category>();

                    cats.Add(cat);
                    symbo.GeometryType = GeometryType.Polyline;
                    symbo.RenderType = RenderType.Simple;
                    symbo.Categories = cats;
                }
                else if (tblInfo.DatasetType.ToString().ToLower() == "polygon")
                {
                    Random random = new Random();
                    //var color = System.Drawing.Color.FromArgb(rand.Next(0, 255), rand.Next(0, 255), rand.Next(0, 255));
                    var color = String.Format("#{0:X6}", random.Next(0x1000000));
                    HatchPattern sm = new HatchPattern();
                    sm.PatType = PatType.Hatch;
                    sm.HatchStyle = (HatchStyle)random.Next(0, 30);// System.Drawing.Drawing2D.HatchStyle.Cross;
                    sm.ForeColor = color;
                    sm.BackColor = System.Drawing.Color.Transparent;
                    sm.ForeColorOpacity = 1;
                    sm.UseOutline = true;
                    sm.Outline = new Outline() { Color = "#000000", DashStyle = DashStyle.DashDot, Opacity = 1, Width = 2 };

                    PolygonCategory cat = new PolygonCategory();
                    cat.FilterExpression = "";
                    cat.LegendText = "";
                    cat.Pattern = sm;

                    List<Category> cats = new List<Category>();

                    cats.Add(cat);
                    symbo.GeometryType = GeometryType.Polygon;
                    symbo.RenderType = RenderType.Simple;
                    symbo.Categories = cats;
                }


                int tblIds = int.Parse(tblId);
                sym = new SymbologyInfo()
                {
                    Type = SymbologyType.Simple,
                    Id = 0,
                    TableInfo = dbContext.TableInfos.Include("FieldInfos").Where(a => a.Id == tblIds).FirstOrDefault(),
                    Json = Newtonsoft.Json.JsonConvert.SerializeObject(symbo)
                };
            }

            return View(sym);
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult _Save(Microsoft.AspNetCore.Http.IFormCollection form)
        {

            try
            {
                var id = form["ID"];
                var tableID = form["TblID"];
                var symField = form["symField"];
                var symType = form["SymType"];
                var geomType = form["geomType"];
                var symName = form["symName"];
                var symDefault = form["symDefault"];

                List<Category> cats = new List<Category>();
                foreach (var key in form.Keys)
                {
                    if (key.ToString().Contains("symjson"))
                    {
                        string lbl = null;
                        string ex = null;
                        string symjson = null;
                        var keysList = form.Keys.ToList();
                        int index = keysList.IndexOf(key.ToString());

                        if (index >= 2)
                        {
                            lbl = form[keysList[index - 2]].ToString();
                            ex = form[keysList[index - 1]].ToString();
                            symjson = form[keysList[index]].ToString();
                        }

                        if (symjson != null)
                            if (geomType.ToString().ToLower() == "point")
                            {
                                PointCategory cat = new PointCategory();
                                cat.FilterExpression = ex;
                                cat.LegendText = lbl;
                                var symbol = Newtonsoft.Json.JsonConvert.DeserializeObject<Symbol>(symjson);

                                if (symbol.SymbolType == ViewModels.SymbolType.Simple)
                                {
                                    var simplesymbol = Newtonsoft.Json.JsonConvert.DeserializeObject<SimpleSymbol>(symjson);
                                    cat.Symbol = simplesymbol;
                                }
                                else if (symbol.SymbolType == ViewModels.SymbolType.Character)
                                {
                                    var csymbol = Newtonsoft.Json.JsonConvert.DeserializeObject<CharacterSymbol>(symjson);
                                    cat.Symbol = csymbol;
                                }
                                else if (symbol.SymbolType == ViewModels.SymbolType.Picture)
                                {
                                    var csymbol = Newtonsoft.Json.JsonConvert.DeserializeObject<PictureSymbol>(symjson);
                                    cat.Symbol = csymbol;
                                }
                                cats.Add(cat);
                            }
                            else if (geomType.ToString().ToLower() == "polyline")
                            {
                                LineCategory cat = new LineCategory();
                                cat.FilterExpression = ex;
                                cat.LegendText = lbl;
                                var outline = Newtonsoft.Json.JsonConvert.DeserializeObject<Outline>(symjson);
                                cat.Outline = outline;

                                cats.Add(cat);
                            }
                            else if (geomType.ToString().ToLower() == "polygon")
                            {
                                PolygonCategory cat = new PolygonCategory();
                                cat.FilterExpression = ex;
                                cat.LegendText = lbl;
                                var pat = Newtonsoft.Json.JsonConvert.DeserializeObject<Pattern>(symjson);
                                if (pat.PatType == PatType.Simple)
                                {
                                    var cpattern = Newtonsoft.Json.JsonConvert.DeserializeObject<SimplePattern>(symjson);
                                    cat.Pattern = cpattern;
                                }
                                else if (pat.PatType == PatType.Hatch)
                                {
                                    var cpattern = Newtonsoft.Json.JsonConvert.DeserializeObject<HatchPattern>(symjson);
                                    cat.Pattern = cpattern;
                                }
                                else if (pat.PatType == PatType.Gradient)
                                {
                                    var cpattern = Newtonsoft.Json.JsonConvert.DeserializeObject<GradientPattern>(symjson);
                                    cat.Pattern = cpattern;
                                }
                                else if (pat.PatType == PatType.Picture)
                                {
                                    var cpattern = Newtonsoft.Json.JsonConvert.DeserializeObject<PicturePattern>(symjson);
                                    cat.Pattern = cpattern;
                                }


                                cats.Add(cat);
                            }
                    }
                }


                Symbology symbo = new Symbology();

                if (geomType.ToString().ToLower() == "point")
                    symbo.GeometryType = GeometryType.Point;
                else if (geomType.ToString().ToLower() == "polyline")
                    symbo.GeometryType = GeometryType.Polyline;
                else if (geomType.ToString().ToLower() == "polygon")
                    symbo.GeometryType = GeometryType.Polygon;

                if (symType.ToString().ToLower() == "simple")
                    symbo.RenderType = RenderType.Simple;
                else if (symType.ToString().ToLower() == "unique")
                    symbo.RenderType = RenderType.Unique;
                else if (symType.ToString().ToLower() == "quantity")
                    symbo.RenderType = RenderType.Quantity;


                symbo.Categories = cats;

                int tblIds = int.Parse(tableID);
                if (string.IsNullOrEmpty(id.ToString().Trim()))
                {
                    SymbologyInfo sym = new SymbologyInfo();
                    sym.Name = symName;

                    sym.IsDefault = symDefault == "on" ? true : false;
                    if (sym.IsDefault)
                    {
                        var lstisDefaultForfalse = dbContext.SymbologyInfos.Include("TableInfo").Where(a => a.TableInfo.Id == tblIds).ToList();
                        for (int i = 0; i < lstisDefaultForfalse.Count(); i++)
                            lstisDefaultForfalse[i].IsDefault = false;
                    }
                    sym.IsDefault = symDefault == "on" ? true : false;
                    if (symType.ToString().ToLower() == "simple")
                        sym.Type = SymbologyType.Simple;
                    else if (symType.ToString().ToLower() == "unique")
                        sym.Type = SymbologyType.Unique;
                    else if (symType.ToString().ToLower() == "quantity")
                        sym.Type = SymbologyType.Quantity;

                    sym.TableInfo = dbContext.TableInfos.Include("FieldInfos").Where(a => a.Id == tblIds).FirstOrDefault();
                    sym.Json = Newtonsoft.Json.JsonConvert.SerializeObject(symbo);


                    if (symType.ToString().ToLower() == "unique" || symType.ToString().ToLower() == "quantity")
                    {
                        sym.FieldName = symField;
                        var fld = sym.TableInfo.FieldInfos.Where(a => a.Name == symField && a.TableInfo.Id.ToString() == tableID).FirstOrDefault();
                        sym.FieldAlias = fld.AliasName;
                    }
                    else
                    {
                        sym.FieldName = null;
                        sym.FieldAlias = null;
                    }

                    dbContext.SymbologyInfos.Add(sym);
                    dbContext.SaveChanges();
                }
                else
                {
                    int ids = int.Parse(id);
                    SymbologyInfo sym = dbContext.SymbologyInfos.Where(a => a.Id == ids).FirstOrDefault();
                    sym.Name = symName;

                    sym.IsDefault = symDefault == "on" ? true : false;
                    if (sym.IsDefault)
                    {
                        var lstisDefaultForfalse = dbContext.SymbologyInfos.Include("TableInfo").Where(a => a.TableInfo.Id == tblIds).ToList();
                        for (int i = 0; i < lstisDefaultForfalse.Count(); i++)
                            lstisDefaultForfalse[i].IsDefault = false;
                    }
                    sym.IsDefault = symDefault == "on" ? true : false;


                    if (symType.ToString().ToLower() == "simple")
                        sym.Type = SymbologyType.Simple;
                    else if (symType.ToString().ToLower() == "unique")
                        sym.Type = SymbologyType.Unique;
                    else if (symType.ToString().ToLower() == "quantity")
                        sym.Type = SymbologyType.Quantity;

                    sym.TableInfo = dbContext.TableInfos.Include("FieldInfos").Where(a => a.Id == tblIds).FirstOrDefault();
                    sym.Json = Newtonsoft.Json.JsonConvert.SerializeObject(symbo);

                    if (symType.ToString().ToLower() == "unique" || symType.ToString().ToLower() == "quantity")
                    {
                        sym.FieldName = symField;
                        var fld = sym.TableInfo.FieldInfos.Where(a => a.Name == symField && a.TableInfo.Id.ToString() == tableID).FirstOrDefault();
                        sym.FieldAlias = fld.AliasName;
                    }
                    else
                    {
                        sym.FieldName = null;
                        sym.FieldAlias = null;
                    }


                    dbContext.SaveChanges();
                }

                //string filePath = "";

                return Json(new { success = true, responseText = "ok" });
            }
            catch (Exception exc)
            {
                return Json(new { success = true, responseText = exc.Message });
            }
        }

        [HttpPost]
        public ActionResult _Del(string id)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    // int id = int.Parse(collection["ID"]);

                    if (id != null)
                    {
                        int ids = int.Parse(id);
                        var item = dbContext.SymbologyInfos.Where(a => a.Id == ids).FirstOrDefault();//.Find(ids);// _fieldInfoRepository.GetFieldInfoByID(ids);



                        dbContext.SymbologyInfos.Remove(item);
                        dbContext.SaveChanges();
                        //return "با موفقیت ذخیره شد.";
                        return Json(new { success = true, responseText = "با موفقیت حذف شد" });
                    }
                    else
                        return Json(new { success = true, responseText = "هیچ فیلدی انتخاب نشده است" });

                }
            }
            catch (Exception exc)
            {

                return Json(new { success = false, responseText = exc.Message });

            }
            return null;


        }


        public ActionResult _Point_List()
        {
            SymbologyInfo sym = null;
            string id = null;
            string tblId = null;

            if (Request.Query.TryGetValue("id", out var idValue))
            {
                id = idValue.ToString();
            }

            if (Request.Query.TryGetValue("tblid", out var tblIdValue))
            {
                tblId = tblIdValue.ToString();
            }

            if (!string.IsNullOrEmpty(id))
            {
                int ids = int.Parse(id);
                sym = dbContext.SymbologyInfos.Include("TableInfo").Include("TableInfo.FieldInfos").Where(a => a.Id == ids).FirstOrDefault();
            }
            else
            {
                SimpleSymbol si1 = new SimpleSymbol()
                {
                    Angle = 0,
                    Color = "#00FF00",
                    Offset = new Position2D(0, 0),
                    Opacity = 1,
                    OutlineColor = "#000000",
                    OutlineOpacity = 1,
                    OutlineWidth = 1,
                    PointShape = PointShape.Star,
                    Size = new Size2D(10, 10),
                    SymbolType = SymbolType.Simple,
                    UseOutline = true,
                    XmlColor = "Green",
                    XmlOutlineColor = "#ffffff"


                };

                int tblIds = int.Parse(tblId);
                sym = new SymbologyInfo()
                {
                    Type = SymbologyType.Simple,
                    Id = 0,
                    TableInfo = dbContext.TableInfos.Include("FieldInfos").Where(a => a.Id == tblIds).FirstOrDefault(),
                    Json = Newtonsoft.Json.JsonConvert.SerializeObject(si1)
                };
            }
            return PartialView(sym);
        }



        public JsonResult GetDistinict(int tableId, string fieldName)
        {
            List<string> data = new List<string>();
            try
            {
                var tblinfo = dbContext.TableInfos.Where(a => a.Id == tableId).FirstOrDefault();

                string query = "SELECT  DISTINCT Top 100 " + fieldName + " FROM " + tblinfo.Name;
                DBManagement db = new DBManagement(_configuration);
                var dt = db.SelectTableSQL(query);

                for (int i = 0; i < dt.Rows.Count; i++)
                    data.Add(dt.Rows[i][0].ToString());
            }
            catch (Exception exc)
            {
            }
            return Json(new { Data = data });
        }

        public JsonResult GetQuantity(int tableId, string fieldName, string Num)
        {
            int number = 5;
            if (Num != null)
                number = int.Parse(Num);
            List<string> data = new List<string>();
            try
            {
                var tblinfo = dbContext.TableInfos.Where(a => a.Id == tableId).FirstOrDefault();

                string query = "SELECT Max( " + fieldName + ") as max,Min( " + fieldName + ") as min FROM " + tblinfo.Name;

                DBManagement db = new DBManagement(_configuration);
                var dt = db.SelectTableSQL(query);
                if (dt.Rows.Count > 0)
                {
                    double max = double.Parse(dt.Rows[0]["max"].ToString());
                    double min = double.Parse(dt.Rows[0]["min"].ToString());
                    var interval = (max - min) / number;
                    // data.Add("[" + FName + "] > " + min.ToString());
                    for (int i = 0; i < number; i++)
                        if (i == 0)
                            data.Add("[" + fieldName + "] <=" + (min + (i + 1) * interval));
                        else if (i == number - 1)
                            data.Add("[" + fieldName + "] > " + (min + i * interval));
                        else
                            data.Add((min + i * interval) + " < [" + fieldName + "] AND [" + fieldName + "] <=" + (min + (i + 1) * interval));
                }
            }
            catch (Exception exc)
            {
            }

            return Json(new { Data = data });
        }

    }
}
