using BaseGIS.Core.Entities;
using BaseGIS.Core.Interfaces;
using BaseGIS.Application.DTOs;

namespace BaseGIS.Application.UseCases
{
    public class CreateTableInfoUseCase
    {
        private readonly ITableInfoRepository _repository;

        public CreateTableInfoUseCase(ITableInfoRepository repository)
        {
            _repository = repository;
        }

        public async Task ExecuteAsync(TableInfoDto dto)
        {
            var tableInfo = new TableInfo
            {
                Name = dto.Name,
                AliasName = dto.AliasName,
                GroupInfoId = dto.GroupInfoId,
                DatasetType = dto.DatasetType,
                ShortName = dto.ShortName,
                ValidationRule = dto.ValidationRule,
                MinScale = dto.MinScale,
                MaxScale = dto.MaxScale,
                MinLabelScale = dto.MinLabelScale,
                MaxLabelScale = dto.MaxLabelScale,
                SimplifyFactor = dto.SimplifyFactor,
                FieldInfos = new List<FieldInfo>(),
                Symbologies = new List<SymbologyInfo>()
            };

            await _repository.AddAsync(tableInfo);
        }
    }
}