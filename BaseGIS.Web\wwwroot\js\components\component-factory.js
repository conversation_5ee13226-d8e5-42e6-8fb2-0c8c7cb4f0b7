/**
 * Component Factory
 * کارخانه تولید کامپوننت‌ها - فایل جداگانه برای سازماندهی بهتر
 */

class ComponentFactory {
    constructor() {
        this.components = new Map();
        this.instances = new Map();
        this.globalInstances = new Map();
    }

    /**
     * ثبت نوع کامپوننت
     */
    register(name, componentClass) {
        this.components.set(name, componentClass);
        console.log(`Component '${name}' registered`);
        return this;
    }

    /**
     * ایجاد کامپوننت
     */
    create(name, elementOrId, options = {}) {
        const ComponentClass = this.components.get(name);
        if (!ComponentClass) {
            console.error(`Component type '${name}' not registered`);
            return null;
        }

        let element;
        if (typeof elementOrId === 'string') {
            element = document.getElementById(elementOrId);
            if (!element) {
                console.error(`Element with id '${elementOrId}' not found`);
                return null;
            }
        } else {
            element = elementOrId;
        }

        try {
            const instance = new ComponentClass(element, options);
            this.instances.set(element, instance);
            this.globalInstances.set(name, instance);
            
            // Mark element as having a component
            if (element) {
                element.setAttribute('data-component', name);
            }
            
            console.log(`Component '${name}' created successfully`);
            return instance;
        } catch (error) {
            console.error(`Error creating component '${name}':`, error);
            return null;
        }
    }

    /**
     * دریافت کامپوننت از عنصر
     */
    getInstance(element) {
        return this.instances.get(element);
    }

    /**
     * دریافت کامپوننت از نام
     */
    getGlobalInstance(name) {
        return this.globalInstances.get(name);
    }

    /**
     * نابودی کامپوننت
     */
    destroy(element) {
        const instance = this.instances.get(element);
        if (instance) {
            instance.destroy();
            this.instances.delete(element);
            
            // Remove from global instances
            for (const [name, inst] of this.globalInstances.entries()) {
                if (inst === instance) {
                    this.globalInstances.delete(name);
                    break;
                }
            }
        }
    }

    /**
     * مقداردهی خودکار کامپوننت‌ها
     */
    autoInit(container = document) {
        const elements = container.querySelectorAll('[data-component]');
        elements.forEach(element => {
            const componentType = element.getAttribute('data-component');
            if (componentType && !this.instances.has(element)) {
                try {
                    this.create(componentType, element);
                } catch (error) {
                    console.error(`Error auto-initializing component ${componentType}:`, error);
                }
            }
        });
    }

    /**
     * مقداردهی تمام کامپوننت‌ها
     */
    initializeAll() {
        console.log('Initializing all components...');
        this.autoInit();
        
        // Initialize specific components that might not have DOM elements yet
        this.initializeSpecialComponents();
    }

    /**
     * مقداردهی کامپوننت‌های خاص
     */
    initializeSpecialComponents() {
        // These components might be initialized programmatically
        const specialComponents = [
            'measurement-tools',
            'spatial-analysis', 
            'goto-xy',
            'drawing-tools',
            'property-tools'
        ];

        specialComponents.forEach(name => {
            if (!this.globalInstances.has(name)) {
                const container = document.getElementById(`${name}-container`);
                if (container) {
                    this.create(name, container);
                }
            }
        });
    }

    /**
     * مقداردهی یک کامپوننت خاص
     */
    initializeComponent(name) {
        if (this.globalInstances.has(name)) {
            console.log(`Component '${name}' already initialized`);
            return this.globalInstances.get(name);
        }

        const container = document.getElementById(`${name}-container`);
        if (container) {
            return this.create(name, container);
        } else {
            console.warn(`Container for component '${name}' not found`);
            return null;
        }
    }

    /**
     * دریافت لیست کامپوننت‌های ثبت شده
     */
    getRegisteredComponents() {
        return Array.from(this.components.keys());
    }

    /**
     * دریافت لیست کامپوننت‌های فعال
     */
    getActiveInstances() {
        return Array.from(this.globalInstances.keys());
    }

    /**
     * بررسی وجود کامپوننت
     */
    hasComponent(name) {
        return this.components.has(name);
    }

    /**
     * بررسی فعال بودن کامپوننت
     */
    isActive(name) {
        return this.globalInstances.has(name);
    }

    /**
     * نابودی تمام کامپوننت‌ها
     */
    destroyAll() {
        console.log('Destroying all components...');
        
        for (const [element, instance] of this.instances.entries()) {
            try {
                instance.destroy();
            } catch (error) {
                console.error('Error destroying component:', error);
            }
        }
        
        this.instances.clear();
        this.globalInstances.clear();
    }

    /**
     * ری‌ست کردن factory
     */
    reset() {
        this.destroyAll();
        this.components.clear();
        console.log('Component factory reset');
    }

    /**
     * اطلاعات debug
     */
    debug() {
        console.group('Component Factory Debug Info');
        console.log('Registered components:', this.getRegisteredComponents());
        console.log('Active instances:', this.getActiveInstances());
        console.log('Total instances:', this.instances.size);
        console.groupEnd();
    }
}

// Global component factory instance
if (!window.ComponentFactory) {
    window.ComponentFactory = new ComponentFactory();
}

// Auto-initialize components when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (window.ComponentFactory) {
        window.ComponentFactory.autoInit();
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ComponentFactory;
}
