Stack trace:
Frame         Function      Args
0007FFFFB730  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFB730, 0007FFFFA630) msys-2.0.dll+0x2118E
0007FFFFB730  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFBA08) msys-2.0.dll+0x69BA
0007FFFFB730  0002100469F2 (00021028DF99, 0007FFFFB5E8, 0007FFFFB730, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB730  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB730  00021006A545 (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFBA10  00021006B9A5 (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD08EC0000 ntdll.dll
7FFD08140000 KERNEL32.DLL
7FFD06700000 KERNELBASE.dll
7FFD073B0000 USER32.dll
7FFD06350000 win32u.dll
7FFD08610000 GDI32.dll
7FFD06380000 gdi32full.dll
7FFD06A90000 msvcp_win.dll
7FFD064A0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD08650000 advapi32.dll
7FFD088D0000 msvcrt.dll
7FFD06E40000 sechost.dll
7FFD087A0000 RPCRT4.dll
7FFD05AD0000 CRYPTBASE.DLL
7FFD06680000 bcryptPrimitives.dll
7FFD07720000 IMM32.DLL
