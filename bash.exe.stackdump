Stack trace:
Frame         Function      Args
0007FFFFA8E0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFA8E0, 0007FFFF97E0) msys-2.0.dll+0x2118E
0007FFFFA8E0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFABB8) msys-2.0.dll+0x69BA
0007FFFFA8E0  0002100469F2 (00021028DF99, 0007FFFFA798, 0007FFFFA8E0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFA8E0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFA8E0  00021006A545 (0007FFFFA8F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFABC0  00021006B9A5 (0007FFFFA8F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD0CA80000 ntdll.dll
7FFD0BE90000 KERNEL32.DLL
7FFD0A210000 KERNELBASE.dll
7FFD0AA00000 USER32.dll
7FFD0A6C0000 win32u.dll
7FFD0AE70000 GDI32.dll
7FFD0A5A0000 gdi32full.dll
7FFD09FD0000 msvcp_win.dll
7FFD0A070000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD0C390000 advapi32.dll
7FFD0BFE0000 msvcrt.dll
7FFD0ACA0000 sechost.dll
7FFD0C7C0000 RPCRT4.dll
7FFD09690000 CRYPTBASE.DLL
7FFD0A190000 bcryptPrimitives.dll
7FFD0AE30000 IMM32.DLL
