/**
 * Search Tools Component
 * کامپوننت ابزارهای جستجو
 */

class SearchTools extends BaseComponent {
    getDefaultOptions() {
        return {
            ...super.getDefaultOptions(),
            searchTypes: ['quick', 'spatial', 'descriptive'],
            defaultSearchType: 'quick',
            maxResults: 100,
            showResultsOnMap: true,
            onSearchComplete: null,
            onResultSelect: null
        };
    }

    beforeInit() {
        this.currentSearchType = this.options.defaultSearchType;
        this.searchResults = new Map();
        this.activeSearch = null;
    }

    render() {
        this.renderSearchInterface();
        this.initializeSearchComponents();
    }

    renderSearchInterface() {
        const html = `
            <div class="search-tools-container">
                <div class="search-tabs">
                    ${this.renderSearchTabs()}
                </div>
                <div class="search-content">
                    ${this.renderSearchContent()}
                </div>
                <div class="search-results">
                    ${this.renderResultsContainer()}
                </div>
            </div>
        `;
        
        this.element.innerHTML = html;
    }

    renderSearchTabs() {
        const tabs = this.options.searchTypes.map(type => {
            const isActive = type === this.currentSearchType;
            const label = this.getSearchTypeLabel(type);
            const icon = this.getSearchTypeIcon(type);
            
            return `
                <button class="search-tab ${isActive ? 'active' : ''}" 
                        data-search-type="${type}">
                    <i class="${icon}"></i>
                    ${label}
                </button>
            `;
        }).join('');
        
        return `<div class="search-tab-list">${tabs}</div>`;
    }

    renderSearchContent() {
        return `
            <div class="search-panels">
                ${this.options.searchTypes.map(type => this.renderSearchPanel(type)).join('')}
            </div>
        `;
    }

    renderSearchPanel(searchType) {
        const isActive = searchType === this.currentSearchType;
        
        switch (searchType) {
            case 'quick':
                return this.renderQuickSearchPanel(isActive);
            case 'spatial':
                return this.renderSpatialSearchPanel(isActive);
            case 'descriptive':
                return this.renderDescriptiveSearchPanel(isActive);
            default:
                return '';
        }
    }

    renderQuickSearchPanel(isActive) {
        return `
            <div class="search-panel quick-search ${isActive ? 'active' : ''}" data-search-type="quick">
                <div class="form-group">
                    <label for="quick-search-input">عبارت جستجو:</label>
                    <div class="input-group">
                        <input type="text" id="quick-search-input" class="form-control" 
                               placeholder="متن مورد نظر را وارد کنید...">
                        <div class="input-group-append">
                            <button class="btn btn-primary search-btn" type="button">
                                <i class="fas fa-search"></i> جستجو
                            </button>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="quick-search-layer">لایه:</label>
                    <select id="quick-search-layer" class="form-control">
                        <option value="">همه لایه‌ها</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="quick-search-field">فیلد:</label>
                    <select id="quick-search-field" class="form-control">
                        <option value="">همه فیلدها</option>
                    </select>
                </div>
                <div class="search-options">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="exact-match">
                        <label class="form-check-label" for="exact-match">
                            تطبیق دقیق
                        </label>
                    </div>
                </div>
            </div>
        `;
    }

    renderSpatialSearchPanel(isActive) {
        return `
            <div class="search-panel spatial-search ${isActive ? 'active' : ''}" data-search-type="spatial">
                <div class="form-group">
                    <label>نوع جستجوی مکانی:</label>
                    <div class="spatial-tools">
                        <button class="btn btn-outline-secondary spatial-tool" data-tool="point">
                            <i class="fas fa-map-pin"></i> نقطه
                        </button>
                        <button class="btn btn-outline-secondary spatial-tool" data-tool="rectangle">
                            <i class="fas fa-square"></i> مستطیل
                        </button>
                        <button class="btn btn-outline-secondary spatial-tool" data-tool="polygon">
                            <i class="fas fa-draw-polygon"></i> چندضلعی
                        </button>
                        <button class="btn btn-outline-secondary spatial-tool" data-tool="circle">
                            <i class="fas fa-circle"></i> دایره
                        </button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="spatial-relation">رابطه مکانی:</label>
                    <select id="spatial-relation" class="form-control">
                        <option value="intersects">تقاطع</option>
                        <option value="contains">شامل</option>
                        <option value="within">درون</option>
                        <option value="touches">مماس</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="buffer-distance">فاصله بافر (متر):</label>
                    <input type="number" id="buffer-distance" class="form-control" value="0" min="0">
                </div>
                <div class="form-group">
                    <label>لایه‌های هدف:</label>
                    <div class="target-layers">
                        <!-- Will be populated dynamically -->
                    </div>
                </div>
            </div>
        `;
    }

    renderDescriptiveSearchPanel(isActive) {
        return `
            <div class="search-panel descriptive-search ${isActive ? 'active' : ''}" data-search-type="descriptive">
                <div class="form-group">
                    <label for="desc-search-layer">لایه:</label>
                    <select id="desc-search-layer" class="form-control" required>
                        <option value="">انتخاب لایه...</option>
                    </select>
                </div>
                <div class="search-criteria">
                    <div class="criteria-header">
                        <label>معیارهای جستجو:</label>
                        <button class="btn btn-sm btn-success add-criteria">
                            <i class="fas fa-plus"></i> افزودن معیار
                        </button>
                    </div>
                    <div class="criteria-list">
                        <!-- Criteria will be added dynamically -->
                    </div>
                </div>
                <div class="form-group">
                    <label for="logical-operator">عملگر منطقی:</label>
                    <select id="logical-operator" class="form-control">
                        <option value="AND">و (AND)</option>
                        <option value="OR">یا (OR)</option>
                    </select>
                </div>
                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="return-geometry" checked>
                        <label class="form-check-label" for="return-geometry">
                            بازگردانی هندسه
                        </label>
                    </div>
                </div>
            </div>
        `;
    }

    renderResultsContainer() {
        return `
            <div class="search-results-container" style="display: none;">
                <div class="results-header">
                    <h6>نتایج جستجو</h6>
                    <div class="results-actions">
                        <button class="btn btn-sm btn-outline-secondary export-results">
                            <i class="fas fa-download"></i> صادرات
                        </button>
                        <button class="btn btn-sm btn-outline-secondary clear-results">
                            <i class="fas fa-times"></i> پاک کردن
                        </button>
                    </div>
                </div>
                <div class="results-content">
                    <div class="results-summary"></div>
                    <div class="results-list"></div>
                </div>
            </div>
        `;
    }

    initializeSearchComponents() {
        this.loadAvailableLayers();
        this.setupSearchCriteria();
    }

    bindEvents() {
        super.bindEvents();

        // Tab switching
        $(this.element).on('click', '.search-tab', (e) => {
            this.switchSearchType(e.target.dataset.searchType);
        });

        // Search execution
        $(this.element).on('click', '.search-btn', () => {
            this.executeSearch();
        });

        // Quick search enter key
        $(this.element).on('keypress', '#quick-search-input', (e) => {
            if (e.which === 13) {
                this.executeSearch();
            }
        });

        // Spatial tools
        $(this.element).on('click', '.spatial-tool', (e) => {
            this.activateSpatialTool(e.target.dataset.tool);
        });

        // Descriptive search layer change
        $(this.element).on('change', '#desc-search-layer', (e) => {
            this.loadLayerFields(e.target.value);
        });

        // Add criteria
        $(this.element).on('click', '.add-criteria', () => {
            this.addSearchCriteria();
        });

        // Remove criteria
        $(this.element).on('click', '.remove-criteria', (e) => {
            this.removeCriteria(e.target.closest('.criteria-item'));
        });

        // Results actions
        $(this.element).on('click', '.export-results', () => {
            this.exportResults();
        });

        $(this.element).on('click', '.clear-results', () => {
            this.clearResults();
        });

        // Result item click
        $(this.element).on('click', '.result-item', (e) => {
            this.selectResult(e.target.closest('.result-item'));
        });
    }

    switchSearchType(searchType) {
        this.currentSearchType = searchType;
        
        // Update tabs
        this.findAll('.search-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.searchType === searchType);
        });
        
        // Update panels
        this.findAll('.search-panel').forEach(panel => {
            panel.classList.toggle('active', panel.dataset.searchType === searchType);
        });
    }

    async executeSearch() {
        try {
            this.showLoading('در حال جستجو...');
            
            let searchRequest;
            let endpoint;
            
            switch (this.currentSearchType) {
                case 'quick':
                    searchRequest = this.buildQuickSearchRequest();
                    endpoint = '/GeoMap/PerformQuickSearch';
                    break;
                case 'spatial':
                    searchRequest = this.buildSpatialSearchRequest();
                    endpoint = '/GeoMap/PerformSpatialSearch';
                    break;
                case 'descriptive':
                    searchRequest = this.buildDescriptiveSearchRequest();
                    endpoint = '/GeoMap/PerformDescriptiveSearch';
                    break;
                default:
                    throw new Error('نوع جستجوی نامشخص');
            }
            
            if (!searchRequest) {
                this.showWarning('لطفاً معیارهای جستجو را تکمیل کنید');
                return;
            }
            
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(searchRequest)
            });
            
            const result = await response.json();
            
            if (result.ok) {
                this.displaySearchResults(result.data);
                this.trigger('searchComplete', { 
                    searchType: this.currentSearchType, 
                    results: result.data 
                });
                
                if (this.options.onSearchComplete) {
                    this.options.onSearchComplete(this.currentSearchType, result.data);
                }
            } else {
                this.showError(result.msg);
            }
            
        } catch (error) {
            console.error('Error executing search:', error);
            this.showError('خطا در انجام جستجو');
        } finally {
            this.hideLoading();
        }
    }

    buildQuickSearchRequest() {
        const searchTerm = this.find('#quick-search-input')?.value?.trim();
        if (!searchTerm) return null;
        
        return {
            searchTerm: searchTerm,
            layerId: this.find('#quick-search-layer')?.value || null,
            fieldName: this.find('#quick-search-field')?.value || null,
            maxResults: this.options.maxResults,
            exactMatch: this.find('#exact-match')?.checked || false
        };
    }

    buildSpatialSearchRequest() {
        // Implementation would build spatial search request
        // This requires geometry from map drawing tools
        return null;
    }

    buildDescriptiveSearchRequest() {
        const layerId = this.find('#desc-search-layer')?.value;
        if (!layerId) return null;
        
        const criteria = this.collectSearchCriteria();
        if (criteria.length === 0) return null;
        
        return {
            layerId: layerId,
            criteria: criteria,
            logicalOperator: this.find('#logical-operator')?.value || 'AND',
            maxResults: this.options.maxResults,
            returnGeometry: this.find('#return-geometry')?.checked || true
        };
    }

    collectSearchCriteria() {
        const criteria = [];
        const criteriaItems = this.findAll('.criteria-item');
        
        criteriaItems.forEach(item => {
            const fieldName = item.querySelector('.criteria-field')?.value;
            const operator = item.querySelector('.criteria-operator')?.value;
            const value = item.querySelector('.criteria-value')?.value;
            
            if (fieldName && operator && value) {
                criteria.push({
                    fieldName: fieldName,
                    operator: operator,
                    value: value,
                    dataType: 'string' // Would be determined from field info
                });
            }
        });
        
        return criteria;
    }

    displaySearchResults(results) {
        const container = this.find('.search-results-container');
        const summary = this.find('.results-summary');
        const list = this.find('.results-list');
        
        if (!container || !summary || !list) return;
        
        // Show results container
        container.style.display = 'block';
        
        // Update summary
        const count = Array.isArray(results) ? results.length : 
                     (results.features ? results.features.length : 0);
        summary.innerHTML = `یافت شد: ${count} نتیجه`;
        
        // Display results
        this.renderResultsList(results, list);
        
        // Store results
        this.searchResults.set(this.currentSearchType, results);
        
        // Add to map if enabled
        if (this.options.showResultsOnMap) {
            this.addResultsToMap(results);
        }
    }

    renderResultsList(results, container) {
        let html = '';
        const items = Array.isArray(results) ? results : 
                     (results.features ? results.features : []);
        
        items.forEach((item, index) => {
            html += `
                <div class="result-item" data-index="${index}">
                    <div class="result-title">${item.displayText || item.id}</div>
                    <div class="result-details">
                        ${item.layerName ? `<span class="layer-name">${item.layerName}</span>` : ''}
                        ${this.renderResultAttributes(item.attributes)}
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }

    renderResultAttributes(attributes) {
        if (!attributes || typeof attributes !== 'object') return '';
        
        const attrs = Object.entries(attributes)
            .slice(0, 3) // Show only first 3 attributes
            .map(([key, value]) => `<span class="attr">${key}: ${value}</span>`)
            .join(' | ');
        
        return attrs;
    }

    addResultsToMap(results) {
        // Implementation would add results to map
        console.log('Adding search results to map:', results);
    }

    async loadAvailableLayers() {
        try {
            // Load available layers for search
            // This would typically come from the layer tree or a separate API
            console.log('Loading available layers for search');
        } catch (error) {
            console.error('Error loading available layers:', error);
        }
    }

    async loadLayerFields(layerId) {
        if (!layerId) return;
        
        try {
            const response = await fetch('/GeoMap/GetLayerFields', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ ID: layerId })
            });
            
            const result = await response.json();
            
            if (result.ok) {
                this.updateFieldSelectors(result.data);
            }
        } catch (error) {
            console.error('Error loading layer fields:', error);
        }
    }

    updateFieldSelectors(fields) {
        const quickFieldSelect = this.find('#quick-search-field');
        if (quickFieldSelect) {
            quickFieldSelect.innerHTML = '<option value="">همه فیلدها</option>';
            fields.forEach(field => {
                quickFieldSelect.innerHTML += `<option value="${field.name}">${field.aliasName}</option>`;
            });
        }
    }

    setupSearchCriteria() {
        // Add initial criteria for descriptive search
        this.addSearchCriteria();
    }

    addSearchCriteria() {
        const criteriaList = this.find('.criteria-list');
        if (!criteriaList) return;
        
        const criteriaHtml = `
            <div class="criteria-item">
                <div class="row">
                    <div class="col-md-3">
                        <select class="form-control criteria-field">
                            <option value="">انتخاب فیلد...</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-control criteria-operator">
                            <option value="=">=</option>
                            <option value="!=">!=</option>
                            <option value=">">></option>
                            <option value="<"><</option>
                            <option value=">=">>=</option>
                            <option value="<="><=</option>
                            <option value="LIKE">شامل</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <input type="text" class="form-control criteria-value" placeholder="مقدار...">
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-sm btn-danger remove-criteria">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        criteriaList.insertAdjacentHTML('beforeend', criteriaHtml);
    }

    removeCriteria(criteriaItem) {
        if (criteriaItem) {
            criteriaItem.remove();
        }
    }

    activateSpatialTool(toolType) {
        // Activate spatial drawing tool
        console.log('Activating spatial tool:', toolType);
        
        // Update tool buttons
        this.findAll('.spatial-tool').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tool === toolType);
        });
    }

    selectResult(resultItem) {
        const index = parseInt(resultItem.dataset.index);
        const results = this.searchResults.get(this.currentSearchType);
        
        if (results && index >= 0) {
            const selectedResult = Array.isArray(results) ? results[index] : 
                                  (results.features ? results.features[index] : null);
            
            if (selectedResult) {
                this.trigger('resultSelect', { result: selectedResult, index });
                
                if (this.options.onResultSelect) {
                    this.options.onResultSelect(selectedResult, index);
                }
                
                // Highlight on map
                this.highlightResult(selectedResult);
            }
        }
    }

    highlightResult(result) {
        // Implementation would highlight result on map
        console.log('Highlighting result:', result);
    }

    exportResults() {
        const results = this.searchResults.get(this.currentSearchType);
        if (!results) return;
        
        // Implementation would export results
        console.log('Exporting search results:', results);
    }

    clearResults() {
        const container = this.find('.search-results-container');
        if (container) {
            container.style.display = 'none';
        }
        
        this.searchResults.delete(this.currentSearchType);
        
        // Clear results from map
        console.log('Clearing search results');
    }

    getSearchTypeLabel(searchType) {
        const labels = {
            'quick': 'جستجوی سریع',
            'spatial': 'جستجوی مکانی',
            'descriptive': 'جستجوی توصیفی'
        };
        return labels[searchType] || searchType;
    }

    getSearchTypeIcon(searchType) {
        const icons = {
            'quick': 'fas fa-search',
            'spatial': 'fas fa-map-marked-alt',
            'descriptive': 'fas fa-filter'
        };
        return icons[searchType] || 'fas fa-search';
    }

    showLoading(message) {
        console.log('Loading:', message);
    }

    hideLoading() {
        console.log('Loading hidden');
    }

    showError(message) {
        if (window.toastr) {
            toastr.error(message, 'خطا');
        }
    }

    showWarning(message) {
        if (window.toastr) {
            toastr.warning(message, 'هشدار');
        }
    }
}

// Register component
window.ComponentFactory.register('search-tools', SearchTools);
