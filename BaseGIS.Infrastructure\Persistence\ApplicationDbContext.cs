﻿using BaseGIS.Core.Entities;
using BaseGIS.Infrastructure.Persistence.Configurations;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace BaseGIS.Infrastructure.Persistence
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser, ApplicationRole, string>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }
        public DbSet<AppSetting> AppSettings { get; set; }
        public DbSet<GroupInfo> GroupInfos { get; set; } = default!;
        public DbSet<TableInfo> TableInfos { get; set; } = default!;
        public DbSet<FieldInfo> FieldInfos { get; set; } = default!;
        public DbSet<DomainInfo> DomainInfos { get; set; } = default!;
        public DbSet<UserTableAccess> UserTableAccesses { get; set; } = default!;
        public DbSet<SymbologyInfo> SymbologyInfos { get; set; } = default!;
        public DbSet<UserMapView> UserMapViews { get; set; } = default!;
        public DbSet<FeatureVersion> FeatureVersions { get; set; } = default!;
        public DbSet<TileMeta> TileMetas { get; set; } = default!;
        public DbSet<UserSelect> UserSelects { get; set; }

        public DbSet<RelationInfo> RelationInfos { get; set; }
        public DbSet<CalFieldConfig> CalFieldConfigs { get; set; }

        public DbSet<Dashboard> Dashboards { get; set; }
        public DbSet<DashboardSQL> DashboardSQLs { get; set; }
        public DbSet<DashboardPanel> DashboardPanels { get; set; }
        public DbSet<DashboardColor> DashboardColors { get; set; }
        public DbSet<DashboardUserParameter> DashboardUserParameters { get; set; }

        public DbSet<Token> Tokens { get; set; }
        public DbSet<BaseMap> BaseMaps { get; set; }

        public DbSet<MapView> MapViews { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<ApplicationRole>().Property(r => r.Description).HasMaxLength(256);
            modelBuilder.Entity<ApplicationRole>().Property(r => r.TableAccess).HasColumnType("nvarchar(max)");
            modelBuilder.Entity<ApplicationRole>().Property(r => r.ToolAccess).HasColumnType("nvarchar(max)");

            modelBuilder.ApplyConfiguration(new GroupInfoConfiguration());
            modelBuilder.ApplyConfiguration(new TableInfoConfiguration());
            modelBuilder.ApplyConfiguration(new FieldInfoConfiguration());
            modelBuilder.ApplyConfiguration(new DomainInfoConfiguration());
            modelBuilder.ApplyConfiguration(new UserTableAccessConfiguration());
            modelBuilder.ApplyConfiguration(new SymbologyInfoConfiguration());
            modelBuilder.ApplyConfiguration(new UserMapViewConfiguration());
            modelBuilder.ApplyConfiguration(new FeatureVersionConfiguration());
            modelBuilder.ApplyConfiguration(new TileMetaConfiguration());
            modelBuilder.ApplyConfiguration(new RelationInfoConfiguration());
            modelBuilder.ApplyConfiguration(new CalFieldConfigConfiguration());

            //modelBuilder.Entity<RelationInfo>()
            //    .HasOne<TableInfo>()
            //    .WithMany()
            //    .HasForeignKey(r => r.RelatedTableId)
            //    .OnDelete(DeleteBehavior.NoAction);

            //modelBuilder.Entity<CalFieldConfig>()
            //    .HasOne<TableInfo>()
            //    .WithMany()
            //    .HasForeignKey(c => c.RelTableId)
            //    .OnDelete(DeleteBehavior.NoAction);

            //modelBuilder.Entity<FieldInfo>()
            //    .HasOne(f => f.TableInfo)
            //    .WithMany()
            //    .HasForeignKey(f => f.TableInfoId)
            //    .OnDelete(DeleteBehavior.NoAction);
        }
    }
}