using BaseGIS.Core.Entities;
using BaseGIS.Core.Interfaces;
using BaseGIS.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace BaseGIS.Infrastructure.Repositories
{
    public class FieldInfoRepository : IFieldInfoRepository
    {
        private readonly ApplicationDbContext _context;

        public FieldInfoRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<FieldInfo> GetByIdAsync(int id)
        {
            return await _context.FieldInfos
                .Include(f => f.TableInfo)
                .Include(f => f.DomainInfos)
                .FirstOrDefaultAsync(f => f.Id == id);
        }

        public async Task<List<FieldInfo>> GetByTableInfoIdAsync(int tableInfoId)
        {
            return await _context.FieldInfos
                .Where(f => f.TableInfoId == tableInfoId)
                .Include(f => f.TableInfo)
                .Include(f => f.DomainInfos)
                .ToListAsync();
        }

        public async Task AddAsync(FieldInfo fieldInfo)
        {
            await _context.FieldInfos.AddAsync(fieldInfo);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(FieldInfo fieldInfo)
        {
            _context.FieldInfos.Update(fieldInfo);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var fieldInfo = await _context.FieldInfos.FindAsync(id);
            if (fieldInfo != null)
            {
                _context.FieldInfos.Remove(fieldInfo);
                await _context.SaveChangesAsync();
            }
        }
    }
}