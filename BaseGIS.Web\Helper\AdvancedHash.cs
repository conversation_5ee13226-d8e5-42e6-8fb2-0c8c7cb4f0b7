﻿using System.Security.Cryptography;
using System.Text;

namespace BaseGIS.Web.Helper
{
    public class AdvancedTokenParameter
    {
        public string UserName { get; set; }
        public DateTime ExpireDate { get; set; }
        public bool IsHash { get; set; }
        public string IP { get; set; }
    }


    public class AdvancedHash
    {
        private static readonly byte[] Salt = Encoding.ASCII.GetBytes("H@S&123Ali");
        private static readonly string sharedSecret = "FaranegashtRWebGISHouse1394";

        public static string GenerateHash(string MapParameter, string Token, string UserName, string ServerIP)
        {
            var usr = EncryptStringAES(UserName + ";" + DateTime.Now.ToString());
            string Text = MapParameter + "&Token=" + Token.ToString() + "&SinnamUNS=" + usr + "&ServerIP=" + ServerIP;
            var hash = EncryptStringAES(Text);

            return hash;
        }

        public static string ParseHash(string Hash)
        {
            try
            {
                string txt = DecryptStringAES(Hash);


                return txt;
            }
            catch
            { }

            return null;
        }

        public static string EncryptStringAES(string plainText)
        {
            if (string.IsNullOrEmpty(plainText))
                throw new ArgumentNullException("plainText");
            if (string.IsNullOrEmpty(sharedSecret))
                throw new ArgumentNullException("sharedSecret");

            string outStr;
            RijndaelManaged aesAlg = null;
            try
            {
                // generate the key from the shared secret and the salt
                var key = new Rfc2898DeriveBytes(sharedSecret, Salt);

                // Create a RijndaelManaged object
                // with the specified key and IV.
                aesAlg = new RijndaelManaged();
                aesAlg.Key = key.GetBytes(aesAlg.KeySize / 8);
                aesAlg.IV = key.GetBytes(aesAlg.BlockSize / 8);

                // Create a decrytor to perform the stream transform.
                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                // Create the streams used for encryption.
                using (var msEncrypt = new MemoryStream())
                {
                    using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        using (var swEncrypt = new StreamWriter(csEncrypt))
                        {

                            //Write all data to the stream.
                            swEncrypt.Write(plainText);
                        }
                    }
                    outStr = Convert.ToBase64String(msEncrypt.ToArray());
                }
            }
            finally
            {
                // Clear the RijndaelManaged object.
                if (aesAlg != null)
                    aesAlg.Clear();
            }

            // Return the encrypted bytes from the memory stream.
            return outStr;
        }

        public static string DecryptStringAES(string cipherText)
        {
            if (string.IsNullOrEmpty(cipherText))
                throw new ArgumentNullException("cipherText");
            if (string.IsNullOrEmpty(sharedSecret))
                throw new ArgumentNullException("sharedSecret");

            // Declare the RijndaelManaged object
            // used to decrypt the data.
            RijndaelManaged aesAlg = null;

            // Declare the string used to hold
            // the decrypted text.
            string plaintext;

            try
            {
                // generate the key from the shared secret and the salt
                var key = new Rfc2898DeriveBytes(sharedSecret, Salt);

                // Create a RijndaelManaged object
                // with the specified key and IV.
                aesAlg = new RijndaelManaged();
                aesAlg.Key = key.GetBytes(aesAlg.KeySize / 8);
                aesAlg.IV = key.GetBytes(aesAlg.BlockSize / 8);

                // Create a decrytor to perform the stream transform.
                var decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);
                // Create the streams used for decryption.                
                var bytes = Convert.FromBase64String(cipherText);
                using (var msDecrypt = new MemoryStream(bytes))
                {
                    using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    {
                        using (var srDecrypt = new StreamReader(csDecrypt))

                            // Read the decrypted bytes from the decrypting stream
                            // and place them in a string.
                            plaintext = srDecrypt.ReadToEnd();
                    }
                }
            }
            finally
            {
                // Clear the RijndaelManaged object.
                if (aesAlg != null)
                    aesAlg.Clear();
            }

            return plaintext;
        }

    }

    public class AdvancedToken
    {
        public static string GenerateToken(AdvancedTokenParameter parameter)
        {
            var txt = parameter.UserName + ";" + parameter.IP + ";" + parameter.ExpireDate.ToString() + ";" + parameter.IsHash.ToString();
            var token = AdvancedHash.EncryptStringAES(txt);
            return token;
        }

        public static AdvancedTokenParameter ParseToken(string token)
        {
            try
            {
                string txt = AdvancedHash.DecryptStringAES(token);
                var splt = txt.Split(';');
                AdvancedTokenParameter parameter = new AdvancedTokenParameter();
                parameter.UserName = splt[0];
                parameter.IP = splt[1];
                parameter.ExpireDate = DateTime.Parse(splt[2]);
                parameter.IsHash = bool.Parse(splt[3]);

                return parameter;
            }
            catch
            { }

            return null;
        }

        public static bool Validation(string token, string db_username, out string msg)
        {
            msg = "Token Invalid";
            bool ok = false;
            try
            {
                var parameter = ParseToken(token);
                if (parameter != null)
                {
                    if (db_username != parameter.UserName)
                        msg = "User Invalid";


                    else if (DateTime.Now > parameter.ExpireDate)
                        msg = "Expire";
                    else
                    {
                        ok = true;
                        msg = "Ok";
                    }

                }
            }
            catch (Exception exc)
            {
                msg = exc.Message;
            }


            return ok;
        }

        public static bool Validation(string token, string db_username, string ip, out string msg)
        {

            msg = "Token Invalid";
            bool ok = false;
            try
            {
                var parameter = ParseToken(token);
                if (parameter != null)
                {
                    if (db_username != parameter.UserName)
                        msg = "User Invalid";
                    else if (ip != parameter.IP)
                        msg = "Invalid IP";
                    else if (DateTime.Now > parameter.ExpireDate)
                        msg = "Expire";
                    else
                    {
                        ok = true;
                        msg = "Ok";
                    }

                }
            }
            catch (Exception exc)
            {
                msg = exc.Message;
            }


            return ok;
        }

    }
}
