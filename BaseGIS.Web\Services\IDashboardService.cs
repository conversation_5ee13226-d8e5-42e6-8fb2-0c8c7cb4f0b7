using BaseGIS.Core.Entities;
using BaseGIS.Web.Models;

namespace BaseGIS.Web.Services
{
    /// <summary>
    /// سرویس مدیریت داشبورد
    /// </summary>
    public interface IDashboardService
    {
        /// <summary>
        /// دریافت داشبورد بر اساس ID
        /// </summary>
        Task<Dashboard?> GetDashboardAsync(int id, string userId, bool isAdmin);

        /// <summary>
        /// دریافت لیست داشبوردها
        /// </summary>
        Task<IEnumerable<Dashboard>> GetDashboardsAsync(string userId, bool isAdmin);

        /// <summary>
        /// ایجاد یا بروزرسانی داشبورد
        /// </summary>
        Task<ServiceResult<int>> SaveDashboardAsync(DashboardViewModel model, string userId, bool isAdmin);

        /// <summary>
        /// حذف داشبورد
        /// </summary>
        Task<ServiceResult> DeleteDashboardAsync(int id, string userId, bool isAdmin);

        /// <summary>
        /// دریافت پنل‌های داشبورد
        /// </summary>
        Task<ServiceResult<IEnumerable<PanelGroupViewModel>>> GetDashboardPanelsAsync(
            int dashboardId, 
            string? parentPanelId, 
            Dictionary<string, string> inputs, 
            string userId, 
            bool isAdmin);

        /// <summary>
        /// دریافت پنل بر اساس ID
        /// </summary>
        Task<ServiceResult<DashboardPanel?>> GetPanelAsync(int id, string userId, bool isAdmin);

        /// <summary>
        /// ایجاد یا بروزرسانی پنل
        /// </summary>
        Task<ServiceResult<int>> SavePanelAsync(PanelViewModel model, string userId, bool isAdmin);

        /// <summary>
        /// حذف پنل
        /// </summary>
        Task<ServiceResult> DeletePanelAsync(int id, string userId, bool isAdmin);

        /// <summary>
        /// دریافت داده‌های نمودار
        /// </summary>
        Task<ServiceResult<ChartDataViewModel>> GetChartDataAsync(
            int panelId, 
            Dictionary<string, string> inputs, 
            string? whereClause,
            string userId);

        /// <summary>
        /// دریافت داده‌های جدول
        /// </summary>
        Task<ServiceResult<TableDataViewModel>> GetTableDataAsync(
            int panelId, 
            Dictionary<string, string> inputs, 
            string? whereClause,
            string userId);

        /// <summary>
        /// ذخیره تنظیمات نمودار کاربر
        /// </summary>
        Task<ServiceResult> SaveUserChartParametersAsync(
            int dashboardId, 
            int panelId, 
            string parameters, 
            string userId);

        /// <summary>
        /// دریافت تنظیمات نمودار کاربر
        /// </summary>
        Task<ChartParameter?> GetUserChartParametersAsync(int dashboardId, int panelId, string userId);

        /// <summary>
        /// اعتبارسنجی دسترسی به داشبورد
        /// </summary>
        Task<bool> ValidateDashboardAccessAsync(int dashboardId, string userId, bool isAdmin);

        /// <summary>
        /// اعتبارسنجی دسترسی به پنل
        /// </summary>
        Task<bool> ValidatePanelAccessAsync(int panelId, string userId, bool isAdmin);
    }

    /// <summary>
    /// نتیجه عملیات سرویس
    /// </summary>
    public class ServiceResult
    {
        public bool IsSuccess { get; set; }
        public string? Message { get; set; }
        public string? ErrorCode { get; set; }

        public static ServiceResult Success(string? message = null)
        {
            return new ServiceResult { IsSuccess = true, Message = message };
        }

        public static ServiceResult Failure(string message, string? errorCode = null)
        {
            return new ServiceResult { IsSuccess = false, Message = message, ErrorCode = errorCode };
        }
    }

    /// <summary>
    /// نتیجه عملیات سرویس با داده
    /// </summary>
    public class ServiceResult<T> : ServiceResult
    {
        public T? Data { get; set; }

        public static ServiceResult<T> Success(T data, string? message = null)
        {
            return new ServiceResult<T> { IsSuccess = true, Data = data, Message = message };
        }

        public static new ServiceResult<T> Failure(string message, string? errorCode = null)
        {
            return new ServiceResult<T> { IsSuccess = false, Message = message, ErrorCode = errorCode };
        }
    }

    /// <summary>
    /// مدل نمایش گروه پنل‌ها
    /// </summary>
    public class PanelGroupViewModel
    {
        public int? Group { get; set; }
        public string GroupName { get; set; } = string.Empty;
        public List<PanelItemViewModel> Items { get; set; } = new();
    }

    /// <summary>
    /// مدل نمایش آیتم پنل
    /// </summary>
    public class PanelItemViewModel
    {
        public int ID { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Name2 { get; set; }
        public int Size { get; set; }
        public double? SizeH { get; set; }
        public int Order { get; set; }
        public string? HTML { get; set; }
        public string? Parameters { get; set; }
        public int? Group { get; set; }
        public int? Padding { get; set; }
        public string? Panels { get; set; }
        public int? SQLID { get; set; }
        public string? FilterByField { get; set; }
        public string? InputMapping { get; set; }
        public DashboardType1 Type1 { get; set; }
        public DashboardType2 Type2 { get; set; }
        public bool? HideHeader { get; set; }
        public string? BackgroundColor { get; set; }
        public string? URL { get; set; }
        public string? MessagePanel { get; set; }
        public string? MessageColor { get; set; }
    }

    /// <summary>
    /// مدل داده‌های نمودار
    /// </summary>
    public class ChartDataViewModel
    {
        public string Type { get; set; } = "line";
        public string TypeTwoBar { get; set; } = "t1";
        public bool Label { get; set; }
        public bool IsList { get; set; }
        public double SizeH { get; set; }
        public string Items { get; set; } = "[]";
        public string? Color { get; set; }
        public string? ColorTitle { get; set; }
        public bool ShowHelp { get; set; }
        public string? Grid { get; set; }
        public int Angle { get; set; }
        public int AngleX { get; set; }
        public bool ShowDecimal { get; set; }
        public string? SizeLabel { get; set; }
        public ChartTargetParameters? TargetParameters { get; set; }
    }

    /// <summary>
    /// مدل داده‌های جدول
    /// </summary>
    public class TableDataViewModel
    {
        public List<Dictionary<string, object>> Data { get; set; } = new();
        public List<TableColumnViewModel> Columns { get; set; } = new();
        public bool HasClickableRows { get; set; }
        public string? ClickableFields { get; set; }
    }

    /// <summary>
    /// مدل ستون جدول
    /// </summary>
    public class TableColumnViewModel
    {
        public string Field { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Type { get; set; } = "text";
        public bool Sortable { get; set; } = true;
        public bool Searchable { get; set; } = true;
    }

    /// <summary>
    /// پارامترهای Target نمودار
    /// </summary>
    public class ChartTargetParameters
    {
        public int TargetColor { get; set; }
        public int TargetBaseColor { get; set; }
        public bool TargetShow { get; set; }
        public bool TargetLabel { get; set; }
        public int TargetAngle { get; set; }
        public int TargetSize { get; set; } = 12;
        public int TargetWidth { get; set; } = 30;
        public bool TargetShowDecimal { get; set; }
        public int TargetHeight { get; set; } = 1;
    }

    /// <summary>
    /// مدل ViewModel داشبورد
    /// </summary>
    public class DashboardViewModel
    {
        public int ID { get; set; }
        public string Name { get; set; } = string.Empty;
        public bool IsPublic { get; set; }
        public string? CssClass { get; set; }
        public int FilterType { get; set; }
    }

    /// <summary>
    /// مدل ViewModel پنل
    /// </summary>
    public class PanelViewModel
    {
        public int ID { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Name2 { get; set; }
        public DashboardType1 Type1 { get; set; }
        public DashboardType2 Type2 { get; set; }
        public int Size { get; set; } = 6;
        public double? SizeH { get; set; }
        public int Order { get; set; } = 1;
        public string? HTML { get; set; }
        public string? Parameters { get; set; }
        public int? Group { get; set; }
        public int? Padding { get; set; }
        public string? Panels { get; set; }
        public int? SQLID { get; set; }
        public string? FilterByField { get; set; }
        public string? InputMapping { get; set; }
        public string? BackgroundColor { get; set; } = "#ffffff";
        public bool? HideHeader { get; set; }
        public string? URL { get; set; }
        public string? MessagePanel { get; set; }
        public string? MessageColor { get; set; }
        public int DashboardID { get; set; }
    }
}
