!function(e,o){"function"==typeof define&&define.amd?define(["exports","echarts"],o):"object"==typeof exports&&"string"!=typeof exports.nodeName?o(0,require("echarts/lib/echarts")):o(0,e.echarts)}(this,function(e,o){o?o.registerTheme("sakura",{color:["#e52c3c","#f7b1ab","#fa506c","#f59288","#f8c4d8","#e54f5c","#f06d5c","#e54f80","#f29c9f","#eeb5b7"],title:{textStyle:{fontWeight:"normal",color:"#e52c3c"}},visualMap:{color:["#e52c3c","#f7b1ab"]},dataRange:{color:["#e52c3c","#f7b1ab"]},candlestick:{itemStyle:{color:"#e52c3c",color0:"#f59288"},lineStyle:{width:1,color:"#e52c3c",color0:"#f59288"},areaStyle:{color:"#fa506c",color0:"#f8c4d8"}},map:{itemStyle:{color:"#e52c3c",borderColor:"#fff",borderWidth:1},areaStyle:{color:"#ccc"},label:{color:"rgba(139,69,19,1)",show:!1}},graph:{itemStyle:{color:"#f2385a"},nodeStyle:{brushType:"both",strokeColor:"#e54f5c"},linkStyle:{color:"#f2385a",strokeColor:"#e54f5c"},label:{color:"#f2385a",show:!1}},gauge:{axisLine:{lineStyle:{color:[[.2,"#e52c3c"],[.8,"#f7b1ab"],[1,"#fa506c"]],width:8}}}}):"undefined"!=typeof console&&console&&console.error&&console.error("ECharts is not Loaded")});