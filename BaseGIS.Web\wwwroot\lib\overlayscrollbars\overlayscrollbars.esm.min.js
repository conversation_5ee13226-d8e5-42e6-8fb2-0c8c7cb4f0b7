const createCache=(e,t)=>{const{o:r,i:n,u:s}=e;let a=r,i;const o=(e,t)=>{var r=a,t=t||(n?!n(r,e):r!==e);return(t||s)&&(a=e,i=r),[a,t,i]};return[t?e=>o(t(a,i),e):o,e=>[a,!!e,i]]},t="undefined"!=typeof window&&"undefined"!=typeof HTMLElement&&!!window.document,n=t?window:{},o=Math.max,s=Math.min,e=Math.round,c=Math.abs,r=Math.sign,l=n.cancelAnimationFrame,i=n.requestAnimationFrame,a=n.setTimeout,u=n.clearTimeout,getApi=e=>void 0!==n[e]?n[e]:void 0,_=getApi("MutationObserver"),d=getApi("IntersectionObserver"),f=getApi("ResizeObserver"),p=getApi("ScrollTimeline"),isUndefined=e=>void 0===e,isNull=e=>null===e,isNumber=e=>"number"==typeof e,isString=e=>"string"==typeof e,isBoolean=e=>"boolean"==typeof e,isFunction=e=>"function"==typeof e,isArray=e=>Array.isArray(e),isObject=e=>"object"==typeof e&&!isArray(e)&&!isNull(e),isArrayLike=e=>{var t=!!e&&e.length,r=isNumber(t)&&-1<t&&t%1==0;return!!(isArray(e)||!isFunction(e)&&r)&&(!(0<t&&isObject(e))||t-1 in e)},isPlainObject=e=>!!e&&e.constructor===Object,isHTMLElement=e=>e instanceof HTMLElement,isElement=e=>e instanceof Element,animationCurrentTime=()=>performance.now(),animateNumber=(n,s,e,a,c)=>{let d=0;const u=animationCurrentTime(),p=o(0,e),v=e=>{var t=animationCurrentTime(),r=t-u>=p,e=e?1:1-(o(0,u+p-t)/p||0),t=(s-n)*(isFunction(c)?c(e,e*p,0,1,p):e)+n,r=r||1==e;a&&a(t,e,r),d=r?0:i(()=>v())};return v(),e=>{l(d),e&&v(e)}};function each(t,r){if(isArrayLike(t))for(let e=0;e<t.length&&!1!==r(t[e],e,t);e++);else t&&each(Object.keys(t),e=>r(t[e],e,t));return t}const inArray=(e,t)=>0<=e.indexOf(t),concat=(e,t)=>e.concat(t),push=(e,t,r)=>(!isString(t)&&isArrayLike(t)?Array.prototype.push.apply(e,t):e.push(t),e),from=e=>Array.from(e||[]),createOrKeepArray=e=>isArray(e)?e:!isString(e)&&isArrayLike(e)?from(e):[e],isEmptyArray=e=>!!e&&!e.length,deduplicateArray=e=>from(new Set(e)),runEachAndClear=(e,t,r)=>{each(e,e=>!e||e.apply(void 0,t||[])),r||(e.length=0)},v="paddingTop",h="paddingRight",g="paddingLeft",b="paddingBottom",w="marginLeft",y="marginRight",S="marginBottom",m="overflowX",O="overflowY",$="width",C="height",x="visible",H="hidden",E="scroll",capitalizeFirstLetter=e=>{const t=String(e||"");return t?t[0].toUpperCase()+t.slice(1):""},equal=(r,n,e,t)=>{if(r&&n){let t=!0;return each(e,e=>{r[e]!==n[e]&&(t=!1)}),t}return!1},equalWH=(e,t)=>equal(e,t,["w","h"]),equalXY=(e,t)=>equal(e,t,["x","y"]),equalTRBL=(e,t)=>equal(e,t,["t","r","b","l"]),noop=()=>{},bind=(e,...t)=>e.bind(0,...t),selfClearTimeout=t=>{let r;const n=t?a:i,s=t?u:l;return[e=>{s(r),r=n(()=>e(),isFunction(t)?t():t)},()=>s(r)]},debounce=(t,e)=>{const{_:p,p:v,v:m,S:r}=e||{};let g,h,y,b,f=noop;const E=function(e){f(),u(g),b=g=h=void 0,f=noop,t.apply(this,e)},S=e=>r&&h?r(h,e):e,C=()=>{f!==noop&&E(S(y)||y)};function n(){var t=from(arguments),r=isFunction(p)?p():p;const n=isNumber(r)&&0<=r;if(n){const p=isFunction(v)?v():v,n=isNumber(p)&&0<=p,o=0<r?a:i,c=0<r?u:l;var s=S(t)||t;const d=E.bind(0,s);let e;f(),m&&!b?(d(),b=!0,e=o(()=>b=void 0,r)):(e=o(d,r),n&&!g&&(g=a(C,p))),f=()=>c(e),h=y=s}else E(t)}return n.m=C,n},hasOwnProperty=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),keys=e=>e?Object.keys(e):[],assignDeep=(a,e,t,r,n,s,i)=>{e=[e,t,r,n,s,i];return"object"==typeof a&&!isNull(a)||isFunction(a)||(a={}),each(e,s=>{each(s,(e,t)=>{const r=s[t];if(a===r)return!0;var n=isArray(r);if(r&&isPlainObject(r)){const s=a[t];let e=s;n&&!isArray(s)?e=[]:n||isPlainObject(s)||(e={}),a[t]=assignDeep(e,r)}else a[t]=n?r.slice():r})}),a},removeUndefinedProperties=(e,t)=>each(assignDeep({},e),(e,t,r)=>{void 0===e?delete r[t]:e&&isPlainObject(e)&&(r[t]=removeUndefinedProperties(e))}),isEmptyObject=e=>!keys(e).length,capNumber=(e,t,r)=>o(e,s(t,r)),getDomTokensArray=e=>deduplicateArray((isArray(e)?e:(e||"").split(" ")).filter(e=>e)),getAttr=(e,t)=>e&&e.getAttribute(t),hasAttr=(e,t)=>e&&e.hasAttribute(t),setAttrs=(t,e,r)=>{each(getDomTokensArray(e),e=>{t&&t.setAttribute(e,String(r||""))})},removeAttrs=(t,e)=>{each(getDomTokensArray(e),e=>t&&t.removeAttribute(e))},domTokenListAttr=(e,t)=>{const n=getDomTokensArray(getAttr(e,t)),r=bind(setAttrs,e,t),s=(e,t)=>{const r=new Set(n);return each(getDomTokensArray(e),e=>{r[t](e)}),from(r).join(" ")};return{O:e=>r(s(e,"delete")),$:e=>r(s(e,"add")),C:e=>{const t=getDomTokensArray(e);return t.reduce((e,t)=>e&&n.includes(t),0<t.length)}}},removeAttrClass=(e,t,r)=>(domTokenListAttr(e,t).O(r),bind(addAttrClass,e,t,r)),addAttrClass=(e,t,r)=>(domTokenListAttr(e,t).$(r),bind(removeAttrClass,e,t,r)),addRemoveAttrClass=(e,t,r,n)=>(n?addAttrClass:removeAttrClass)(e,t,r),hasAttrClass=(e,t,r)=>domTokenListAttr(e,t).C(r),createDomTokenListClass=e=>domTokenListAttr(e,"class"),removeClass=(e,t)=>{createDomTokenListClass(e).O(t)},addClass=(e,t)=>(createDomTokenListClass(e).$(t),bind(removeClass,e,t)),find=(e,t)=>{const r=t?isElement(t)&&t:document;return r?from(r.querySelectorAll(e)):[]},findFirst=(e,t)=>{const r=t?isElement(t)&&t:document;return r&&r.querySelector(e)},is=(e,t)=>isElement(e)&&e.matches(t),isBodyElement=e=>is(e,"body"),contents=e=>e?from(e.childNodes):[],parent=e=>e&&e.parentElement,closest=(e,t)=>isElement(e)&&e.closest(t),getFocusedElement=e=>document.activeElement,liesBetween=(e,t,r)=>{var n=closest(e,t),s=e&&findFirst(r,n),a=closest(s,t)===n;return!(!n||!s)&&(n===e||s===e||a&&closest(closest(e,r),t)!==n)},removeElements=e=>{each(createOrKeepArray(e),e=>{const t=parent(e);e&&t&&t.removeChild(e)})},appendChildren=(t,e)=>bind(removeElements,t&&e&&each(createOrKeepArray(e),e=>{e&&t.appendChild(e)}));let z;const getTrustedTypePolicy=()=>z,setTrustedTypePolicy=e=>{z=e},createDiv=e=>{var t=document.createElement("div");return setAttrs(t,"class",e),t},createDOM=e=>{const t=createDiv(),r=getTrustedTypePolicy();e=e.trim();return t.innerHTML=r?r.createHTML(e):e,each(contents(t),e=>removeElements(e))},getCSSVal=(e,t)=>e.getPropertyValue(t)||e[t]||"",validFiniteNumber=e=>{e=e||0;return isFinite(e)?e:0},parseToZeroOrNumber=e=>validFiniteNumber(parseFloat(e||"")),roundCssNumber=e=>Math.round(1e4*e)/1e4,numberToCssPx=e=>roundCssNumber(validFiniteNumber(e))+"px";function setStyles(s,e){s&&e&&each(e,(e,t)=>{try{const n=s.style;var r=isNull(e)||isBoolean(e)?"":isNumber(e)?numberToCssPx(e):e;0===t.indexOf("--")?n.setProperty(t,r):n[t]=r}catch(e){}})}function getStyles(e,t,r){var s=isString(t);let a=s?"":{};if(e){const i=n.getComputedStyle(e,r)||e.style;a=s?getCSSVal(i,t):from(t).reduce((e,t)=>(e[t]=getCSSVal(i,t),e),a)}return a}const topRightBottomLeft=(e,t,r)=>{var t=t?t+"-":"",r=r?"-"+r:"",n=t+"top"+r,s=t+"right"+r,a=t+"bottom"+r,t=t+"left"+r,r=getStyles(e,[n,s,a,t]);return{t:parseToZeroOrNumber(r[n]),r:parseToZeroOrNumber(r[s]),b:parseToZeroOrNumber(r[a]),l:parseToZeroOrNumber(r[t])}},getTrasformTranslateValue=(e,t)=>"translate"+(isObject(e)?`(${e.x},${e.y})`:`${t?"X":"Y"}(${e})`),elementHasDimensions=e=>!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length),I={w:0,h:0},getElmWidthHeightProperty=(e,t)=>t?{w:t[e+"Width"],h:t[e+"Height"]}:I,getWindowSize=e=>getElmWidthHeightProperty("inner",e||n),A=bind(getElmWidthHeightProperty,"offset"),D=bind(getElmWidthHeightProperty,"client"),M=bind(getElmWidthHeightProperty,"scroll"),getFractionalSize=t=>{var r=parseFloat(getStyles(t,$))||0,t=parseFloat(getStyles(t,C))||0;return{w:r-e(r),h:t-e(t)}},getBoundingClientRect=e=>e.getBoundingClientRect(),hasDimensions=e=>!!e&&elementHasDimensions(e),domRectHasDimensions=e=>!(!e||!e[C]&&!e[$]),domRectAppeared=(e,t)=>{e=domRectHasDimensions(e);return!domRectHasDimensions(t)&&e},removeEventListener=(t,e,r,n)=>{each(getDomTokensArray(e),e=>{t&&t.removeEventListener(e,r,n)})},addEventListener=(n,e,s,t)=>{var r=null==(r=t&&t.H)||r;const a=t&&t.I||!1,i=t&&t.A||!1,o={passive:r,capture:a};return bind(runEachAndClear,getDomTokensArray(e).map(t=>{const r=i?e=>{removeEventListener(n,t,r,a),s&&s(e)}:s;return n&&n.addEventListener(t,r,o),bind(removeEventListener,n,t,r,a)}))},stopPropagation=e=>e.stopPropagation(),preventDefault=e=>e.preventDefault(),stopAndPrevent=e=>stopPropagation(e)||preventDefault(e),scrollElementTo=(e,t)=>{var{x:t,y:r}=isNumber(t)?{x:t,y:t}:t||{};isNumber(t)&&(e.scrollLeft=t),isNumber(r)&&(e.scrollTop=r)},getElementScroll=e=>({x:e.scrollLeft,y:e.scrollTop}),getZeroScrollCoordinates=()=>({D:{x:0,y:0},M:{x:0,y:0}}),sanitizeScrollCoordinates=(e,t)=>{var{D:e,M:n}=e,{w:t,h:s}=t,a=(e,t,n)=>{let s=r(e)*n,a=r(t)*n;if(s===a){const n=c(e),r=c(t);a=n>r?0:a,s=n<r?0:s}return[(s=s===a?0:s)+0,a+0]},[t,i]=a(e.x,n.x,t),[a,e]=a(e.y,n.y,s);return{D:{x:t,y:a},M:{x:i,y:e}}},isDefaultDirectionScrollCoordinates=({D:e,M:t})=>{var r=(e,t)=>0===e&&e<=t;return{x:r(e.x,t.x),y:r(e.y,t.y)}},getScrollCoordinatesPercent=({D:e,M:t},r)=>{var n=(e,t,r)=>capNumber(0,1,(e-r)/(e-t)||0);return{x:n(e.x,t.x,r.x),y:n(e.y,t.y,r.y)}},focusElement=e=>{e&&e.focus&&e.focus({preventScroll:!0})},manageListener=(e,t)=>{each(createOrKeepArray(t),e)},createEventListenerHub=e=>{const s=new Map,a=(e,t)=>{if(e){const r=s.get(e);manageListener(e=>{r&&r[e?"delete":"clear"](e)},t)}else s.forEach(e=>{e.clear()}),s.clear()},i=(r,e)=>{if(isString(r)){const t=s.get(r)||new Set;return s.set(r,t),manageListener(e=>{isFunction(e)&&t.add(e)},e),bind(a,r,e)}isBoolean(e)&&e&&a();const t=keys(r),n=[];return each(t,e=>{var t=r[e];t&&push(n,i(e,t))}),bind(runEachAndClear,n)};return i(e||{}),[i,a,(e,t)=>{each(from(s.get(e)),e=>{t&&!isEmptyArray(t)?e.apply(0,t):e()})}]},T={},k={},addPlugins=e=>{each(e,r=>each(r,(e,t)=>{T[t]=r[t]}))},registerPluginModuleInstances=(o,l,c)=>keys(o).map(e=>{var{static:t,instance:r}=o[e];const[n,s,a]=c||[],i=c?r:t;if(i){const o=c?i(n,s,l):i(l);return(a||k)[e]=o}}),getStaticPluginModuleInstance=e=>k[e],R="__osOptionsValidationPlugin",V="data-overlayscrollbars",L="os-environment",U=L+"-scrollbar-hidden",P=V+"-initialize",N="noClipping",q=V+"-body",B=V,F="host",j=V+"-viewport",X=m,Y=O,W="arrange",J="measuring",G="scrolling",K="scrollbarHidden",Q="noContent",Z=V+"-padding",tt=V+"-content",nt="os-size-observer",ot=nt+"-appear",st=nt+"-listener",et=st+"-scroll",ct=st+"-item",rt=ct+"-final",lt="os-trinsic-observer",it="os-theme-none",at="os-scrollbar",ut=at+"-rtl",_t=at+"-horizontal",dt=at+"-vertical",ft=at+"-track",pt=at+"-handle",vt=at+"-visible",ht=at+"-cornerless",gt=at+"-interaction",bt=at+"-unusable",wt=at+"-auto-hide",yt=wt+"-hidden",St=at+"-wheel",mt=ft+"-interactive",Ot=pt+"-interactive",$t="__osSizeObserverPlugin",Ct=(()=>({[$t]:{static:()=>(e,t,r)=>{const n=3333333;var s=createDOM(`<div class="${ct}" dir="ltr"><div class="${ct}"><div class="${rt}"></div></div><div class="${ct}"><div class="${rt}" style="width: 200%; height: 200%"></div></div></div>`);const a=s[0],o=a.lastChild,c=a.firstChild;var d=null==c?void 0:c.firstChild;let u=A(a),p=u,v=!1,m;const g=()=>{scrollElementTo(c,n),scrollElementTo(o,n)},h=e=>{m=0,v&&(u=p,t(!0===e))};var y=e=>{p=A(a),v=!e||!equalWH(p,u),e?(stopPropagation(e),v&&!m&&(l(m),m=i(h))):h(!1===e),g()},s=[appendChildren(e,s),addEventListener(c,"scroll",y),addEventListener(o,"scroll",y)];return addClass(e,et),setStyles(d,{[$]:n,[C]:n}),i(g),[r?bind(y,!1):g,s]}}}))(),getShowNativeOverlaidScrollbars=(e,t)=>{var t=t["T"],[e,r]=e("showNativeOverlaidScrollbars");return[e&&t.x&&t.y,r]},overflowIsVisible=e=>0===e.indexOf(x),createViewportOverflowState=(e,t)=>{var r=(e,t,r,n)=>{var s=e===x?H:e.replace(x+"-",""),e=overflowIsVisible(e),r=overflowIsVisible(r);if(!t&&!n)return H;if(e&&r)return x;if(e){const e=t?x:H;return t&&n?s:e}e=r&&n?x:H;return t?s:e},r={x:r(t.x,e.x,t.y,e.y),y:r(t.y,e.y,t.x,e.x)};return{k:r,R:{x:r.x===E,y:r.y===E}}},xt="__osScrollbarsHidingPlugin",Ht=(()=>({[xt]:{static:()=>({V:(e,f,l,t,r)=>{const{L:C,U:c}=e,{P:a,T:i,N:o}=t,d=!C&&!a&&(i.x||i.y),[u]=getShowNativeOverlaidScrollbars(r,t),A=e=>{e=e.R;const n=a||u?0:42;var t=(e,t,r)=>{r=e?n:r;return[t&&!a?r:0,e&&!!n]},[r,s]=t(i.x,e.x,o.x),[t,e]=t(i.y,e.y,o.y);return{q:{x:r,y:t},B:{x:s,y:e}}},p=(e,{F:t},r)=>{if(!C){const u=assignDeep({},{[y]:0,[S]:0,[w]:0}),{q:C,B:p}=A(e);var{x:e,y:n}=p,{x:s,y:a}=C,i=f["j"],o=t?w:y,t=t?g:h,l=i[o],c=i[S],d=i[t],i=i[b];return u[$]=`calc(100% + ${a+-1*l}px)`,u[o]=-a+l,u[S]=-s+c,r&&(u[t]=d+(n?a:0),u[b]=i+(e?s:0)),u}};return{X:A,Y:(e,t,r)=>{if(d){var n=f["j"],{q:e,B:s}=A(e);const{x:o,y:d}=s;var{x:s,y:e}=e,a=l["F"],a=n[a?h:g],n=n.paddingTop,i=t.w+r.w,t=t.h+r.h,r={w:e&&d?e+i-a+"px":"",h:s&&o?s+t-n+"px":""};setStyles(c,{"--os-vaw":r.w,"--os-vah":r.h})}return d},W:e=>{if(d){const n=e||(()=>{var e=e=>{e=getStyles(c,e);return[e,e===E]},[t,r]=e(m),[e,n]=e(O);return{k:{x:t,y:e},R:{x:r,y:n}}})(),s=f["j"];var e=A(n)["B"],{x:e,y:t}=e;const a={};var r=e=>each(e,e=>{a[e]=s[e]});e&&r([S,v,b]),t&&r([w,y,g,h]);const i=getStyles(c,keys(a)),o=removeAttrClass(c,j,W);return setStyles(c,a),[()=>{setStyles(c,assignDeep({},i,p(n,l,d))),o()},n]}return[noop]},J:p}}})}}))(),Et="__osClickScrollPlugin",zt=(()=>({[Et]:{static:()=>(n,s,a,i)=>{let o=!1,l=noop;const r=222,[c,t]=selfClearTimeout(133),d=Math.sign(s),u=a*d,p=u/2,v=e=>1-(1-e)*(1-e),m=(e,t)=>animateNumber(e,t,r,n,v),g=(e,t)=>animateNumber(e,s-u,133*t,(e,t,r)=>{n(e),r&&(l=m(e,s))}),h=animateNumber(0,u,r,(r,e,t)=>{if(n(r),t&&(i(o),!o)){const n=s-r,i=Math.sign(n-p)===d;i&&c(()=>{var e=n-u,t=Math.sign(e)===d;l=t?g(r,Math.abs(e)/a):m(r,s)})}},v);return e=>{o=!0,e&&h(),t(),l()}}}}))(),opsStringify=e=>JSON.stringify(e,(e,t)=>{if(isFunction(t))throw 0;return t}),getPropByPath=(e,t)=>e?(""+t).split(".").reduce((e,t)=>e&&hasOwnProperty(e,t)?e[t]:void 0,e):void 0,It={paddingAbsolute:!1,showNativeOverlaidScrollbars:!1,update:{elementEvents:[["img","load"]],debounce:[0,33],attributes:null,ignoreMutation:null},overflow:{x:"scroll",y:"scroll"},scrollbars:{theme:"os-theme-dark",visibility:"auto",autoHide:"never",autoHideDelay:1300,autoHideSuspend:!1,dragScroll:!0,clickScroll:!1,pointers:["mouse","touch","pen"]}},getOptionsDiff=(e,s)=>{const a={};return each(concat(keys(s),keys(e)),t=>{var r=e[t],n=s[t];if(isObject(r)&&isObject(n))assignDeep(a[t]={},getOptionsDiff(r,n)),isEmptyObject(a[t])&&delete a[t];else if(hasOwnProperty(s,t)&&n!==r){let e=!0;if(isArray(r)||isArray(n))try{opsStringify(r)===opsStringify(n)&&(e=!1)}catch(e){}e&&(a[t]=n)}}),a},createOptionCheck=(t,r,n)=>e=>[getPropByPath(t,e),n||void 0!==getPropByPath(r,e)];let At;const getNonce=()=>At,setNonce=e=>{At=e};let Dt;const createEnvironment=()=>{var e=(e,t,r)=>{appendChildren(document.body,e),appendChildren(document.body,e);var n=D(e),s=A(e),t=getFractionalSize(t);return r&&removeElements(e),{x:s.h-n.h+t.h,y:s.w-n.w+t.w}},t=`.${L}{scroll-behavior:auto!important;position:fixed;opacity:0;visibility:hidden;overflow:scroll;height:200px;width:200px;z-index:-1}.${L} div{width:200%;height:200%;margin:10px 0}.${U}{scrollbar-width:none!important}.${U}::-webkit-scrollbar,.${U}::-webkit-scrollbar-corner{appearance:none!important;display:none!important;width:0!important;height:0!important}`,t=createDOM(`<div class="${L}"><div></div><style>${t}</style></div>`)[0],r=t.firstChild;const s=t.lastChild;var a=getNonce();a&&(s.nonce=a);const[i,,o]=createEventListenerHub(),[l,c]=createCache({o:e(t,r),i:equalXY},bind(e,t,r,!0));var[a]=c();const d=(e=>{let t=!1;const r=addClass(e,U);try{t="none"===getStyles(e,"scrollbar-width")||"none"===getStyles(e,"display","::-webkit-scrollbar")}catch(e){}return r(),t})(t);e={x:0===a.x,y:0===a.y};const u={elements:{host:null,padding:!d,viewport:e=>d&&isBodyElement(e)&&e,content:!1},scrollbars:{slot:!0},cancel:{nativeScrollbarsOverlaid:!1,body:null}},v=assignDeep({},It),m=bind(assignDeep,{},v),g=bind(assignDeep,{},u),h={N:a,T:e,P:d,G:!!p,K:bind(i,"r"),Z:g,tt:e=>assignDeep(u,e)&&g(),nt:m,ot:e=>assignDeep(v,e)&&m(),st:assignDeep({},u),et:assignDeep({},v)};if(removeAttrs(t,"style"),removeElements(t),addEventListener(n,"resize",()=>{o("r",[])}),isFunction(n.matchMedia)&&!d&&(!e.x||!e.y)){const y=e=>{var t=n.matchMedia(`(resolution: ${n.devicePixelRatio}dppx)`);addEventListener(t,"change",()=>{e(),y(e)},{A:!0})};y(()=>{var[e,t]=l();assignDeep(h.N,e),o("r",[t])})}return h},getEnvironment=()=>Dt=Dt||createEnvironment(),createEventContentChange=(s,a,e)=>{let i=!1;const o=!!e&&new WeakMap;var t=r=>{o&&e&&each(e.map(e=>{var[e,t]=e||[];return[t&&e?(r||find)(e,s):[],t]}),n=>each(n[0],t=>{var e=n[1],r=o.get(t)||[];if(s.contains(t)&&e){const s=addEventListener(t,e,e=>{i?(s(),o.delete(t)):a(e)});o.set(t,push(r,s))}else runEachAndClear(r),o.delete(t)}))};return t(),[()=>{i=!0},t]},createDOMObserver=(f,E,r,S)=>{let e=!1;const{ct:t,rt:n,lt:s,it:C,ut:a,_t:i}=S||{},o=debounce(()=>e&&r(!0),{_:33,p:99}),[l,c]=createEventContentChange(f,o,s);var d=t||[];const A=n||[],u=concat(d,A),p=(e,t)=>{if(!isEmptyArray(t)){const g=a||noop,h=i||noop,y=[],b=[];let v=!1,m=!1;if(each(t,e=>{const{attributeName:t,target:r,type:n,oldValue:s,addedNodes:a,removedNodes:i}=e;var o="attributes"===n,l="childList"===n,c=f===r,d=o&&t,u=d&&getAttr(r,t||""),u=isString(u)?u:null,d=d&&s!==u,p=inArray(A,t)&&d;if(E&&(l||!c)){const E=o&&d,n=E&&C&&is(r,C),b=n?!g(r,t,s,u):!o||E,v=b&&!h(e,!!n,f,S);each(a,e=>push(y,e)),each(i,e=>push(y,e)),m=m||v}!E&&c&&d&&!g(r,t,s,u)&&(push(b,t),v=v||p)}),c(r=>deduplicateArray(y).reduce((e,t)=>(push(e,find(r,t)),is(t,r)?push(e,t):e),[])),E)return!e&&m&&r(!1),[!1];if(!isEmptyArray(b)||v){const f=[deduplicateArray(b),v];return e||r.apply(0,f),f}}},v=new _(bind(p,!1));return[()=>(v.observe(f,{attributes:!0,attributeOldValue:!0,attributeFilter:u,subtree:E,childList:E,characterData:E}),e=!0,()=>{e&&(l(),v.disconnect(),e=!1)}),()=>{if(e)return o.m(),p(!0,v.takeRecords())}]},createSizeObserver=(s,a,e)=>{const i=(e||{})["dt"],o=getStaticPluginModuleInstance($t),[l]=createCache({o:!1,u:!0});return()=>{var e=[],t=createDOM(`<div class="${nt}"><div class="${st}"></div></div>`)[0],r=t.firstChild;const n=e=>{const t=e instanceof ResizeObserverEntry;let r=!1,n=!1;if(t){const[a,,t]=l(e.contentRect);var s=domRectHasDimensions(a);n=domRectAppeared(a,t),r=!n&&!s}else n=!0===e;r||a({ft:!0,dt:n})};if(f){const s=new f(e=>n(e.pop()));s.observe(r),push(e,()=>{s.disconnect()})}else{if(!o)return noop;{const[s,a]=o(r,n,i);push(e,concat([addClass(t,ot),addEventListener(t,"animationstart",s)],a))}}return bind(runEachAndClear,push(e,appendChildren(s,t)))}},createTrinsicObserver=(r,n)=>{let s;const a=e=>0===e.h||e.isIntersecting||0<e.intersectionRatio,i=createDiv(lt),[o]=createCache({o:!1}),l=(e,t)=>{var r;if(e)return[,r]=e=o(a(e)),r&&!t&&n(e)&&[e]},c=(e,t)=>l(t.pop(),e);return[()=>{var e,t=[];return d?((s=new d(bind(c,!1),{root:r})).observe(i),push(t,()=>{s.disconnect()})):(e=()=>{var e=A(i);l(e)},push(t,createSizeObserver(i,e)()),e()),bind(runEachAndClear,push(t,appendChildren(r,i)))},()=>s&&c(!0,s.takeRecords())]},createObserversSetup=(l,c,d,a)=>{let u,p,v,m,t,r;const g=`[${B}]`,h=`[${j}]`,y=["id","class","style","open","wrap","cols","rows"],{vt:n,ht:b,U:E,gt:S,bt:C,L:A,wt:w,yt:x,St:U,Ot:V}=l,s=e=>"rtl"===getStyles(e,"direction"),D={$t:!1,F:s(n)},O=getEnvironment(),T=getStaticPluginModuleInstance(xt),[i]=createCache({i:equalWH,o:{w:0,h:0}},()=>{const e=T&&T.V(l,c,D,O,d).W;var t=!(w&&A)&&hasAttrClass(b,B,N),r=!A&&x(W),n=r&&getElementScroll(S);const s=n&&V(),a=U(J,t),i=r&&e&&e()[0];var r=M(E),o=getFractionalSize(E);return i&&i(),scrollElementTo(S,n),s&&s(),t&&a(),{w:r.w+o.w,h:r.h+o.h}}),L=debounce(a,{_:()=>u,p:()=>p,S(e,t){const[r]=e,[n]=t;return[concat(keys(r),keys(n)).reduce((e,t)=>(e[t]=r[t]||n[t],e),{})]}}),P=e=>{var t=s(n);assignDeep(e,{Ct:r!==t}),assignDeep(D,{F:t}),r=t},k=(e,t)=>{var[e,r]=e,r={xt:r};return assignDeep(D,{$t:e}),t||a(r),r},o=({ft:e,dt:t})=>{const r=!(e&&!t)&&O.P?L:a;e={ft:e||t,dt:t};P(e),r(e)},H=(e,t)=>{var[,r]=i(),n={Ht:r};P(n);const s=e?a:L;return r&&!t&&s(n),n},I=(e,t,r)=>{var n={Et:t};return P(n),t&&!r&&L(n),n},[z,F]=C?createTrinsicObserver(b,k):[],R=!A&&createSizeObserver(b,o,{dt:!0}),[q,Z]=createDOMObserver(b,!1,I,{rt:y,ct:y}),$=A&&f&&new f(e=>{e=e[e.length-1].contentRect;o({ft:!0,dt:domRectAppeared(e,t)}),t=e}),_=debounce(()=>{var[,e]=i();a({Ht:e})},{_:222,v:!0});return[()=>{$&&$.observe(b);const e=R&&R(),t=z&&z(),r=q(),n=O.K(e=>{e?L({zt:e}):_()});return()=>{$&&$.disconnect(),e&&e(),t&&t(),m&&m(),r(),n()}},({It:e,At:t,Dt:r})=>{var n={};const[s]=e("update.ignoreMutation");var[a,i]=e("update.attributes"),[o,l]=e("update.elementEvents"),[c,e]=e("update.debounce"),d=t||r;if(l||i){v&&v(),m&&m();const[e,t]=createDOMObserver(C||E,!0,H,{ct:concat(y,a||[]),lt:o,it:g,_t:(e,t)=>{var{target:r,attributeName:n}=e;return!(t||!n||A)&&liesBetween(r,g,h)||!!closest(r,"."+at)||(t=e,!(!isFunction(s)||!s(t)))}});m=e(),v=t}if(e)if(L.m(),isArray(c)){const e=c[0],t=c[1];u=isNumber(e)&&e,p=isNumber(t)&&t}else p=(u=!!isNumber(c)&&c,!1);if(d){const e=Z(),t=F&&F(),r=v&&v();e&&assignDeep(n,I(e[0],e[1],d)),t&&assignDeep(n,k(t[0],d)),r&&assignDeep(n,H(r[0],d))}return P(n),n},D]},resolveInitialization=(e,t)=>isFunction(t)?t.apply(0,e):t,staticInitializationElement=(e,t,r,n)=>{r=isUndefined(n)?r:n;return resolveInitialization(e,r)||t.apply(0,e)},dynamicInitializationElement=(e,t,r,n)=>{r=isUndefined(n)?r:n,n=resolveInitialization(e,r);return!!n&&(isHTMLElement(n)?n:t.apply(0,e))},cancelInitialization=(e,t)=>{var{nativeScrollbarsOverlaid:t,body:r}=t||{};const{T:n,P:s,Z:a}=getEnvironment();var{nativeScrollbarsOverlaid:i,body:o}=a().cancel,t=null!=t?t:i,i=isUndefined(r)?o:r,o=(n.x||n.y)&&t,r=e&&(isNull(i)?!s:i);return!!o||!!r},createScrollbarsSetupElements=(e,t,c,o)=>{const r=getEnvironment()["Z"];var n=r()["scrollbars"],n=n["slot"];const{vt:s,ht:a,U:i,Mt:l,gt:d,wt:u,L:v}=t;t=(l?{}:e).scrollbars,e=(t||{}).slot;const m=[],g=[],h=[],y=dynamicInitializationElement([s,a,i],()=>v&&u?s:a,n,e);t=i=>{if(p){let s=null,a=[];const o=new p({source:d,axis:i}),l=()=>{s&&s.cancel(),s=null};return{Rt:e=>{var t=c["Tt"],t=isDefaultDirectionScrollCoordinates(t)[i],r="x"===i;const n=[getTrasformTranslateValue(0,r),getTrasformTranslateValue(`calc(100cq${r?"w":"h"} + -100%)`,r)];r=t?n:n.reverse();return a[0]===r[0]&&a[1]===r[1]||(l(),a=r,s=e.kt.animate({clear:["left"],transform:r},{timeline:o})),l}}}};const b={x:t("x"),y:t("y")},f=(e,t,r)=>{const n=r?addClass:removeClass;each(e,e=>{n(e.Ut,t)})},E=(e,r)=>{each(e,e=>{var[e,t]=r(e);setStyles(e,t)})},S=(e,t,r)=>{var n=isBoolean(r),s=!n||!r;n&&!r||f(g,e,t),s&&f(h,e,t)};n=e=>{var t=e?"x":"y",r=e?_t:dt,r=createDiv(at+" "+r),n=createDiv(ft),s=createDiv(pt),a={Ut:r,Pt:n,kt:s};const i=b[t];return push(e?g:h,a),push(m,[appendChildren(r,n),appendChildren(n,s),bind(removeElements,r),i&&i.Rt(a),o(a,S,e)]),a};const C=bind(n,!0),A=bind(n,!1);return C(),A(),[{Nt:()=>{var e=(()=>{var{Vt:e,Lt:t}=c,r=(e,t)=>capNumber(0,1,e/(e+t)||0);return{x:r(t.x,e.x),y:r(t.y,e.y)}})(),t=t=>e=>[e.Ut,{"--os-viewport-percent":roundCssNumber(t)+""}];E(g,t(e.x)),E(h,t(e.y))},qt:()=>{var e,t;p||(e=c["Tt"],e=getScrollCoordinatesPercent(e,getElementScroll(d)),t=t=>e=>[e.Ut,{"--os-scroll-percent":roundCssNumber(t)+""}],E(g,t(e.x)),E(h,t(e.y)))},Bt:()=>{var e=c["Tt"],e=isDefaultDirectionScrollCoordinates(e),t=t=>e=>[e.Ut,{"--os-scroll-direction":t?"0":"1"}];E(g,t(e.x)),E(h,t(e.y)),p&&(g.forEach(b.x.Rt),h.forEach(b.y.Rt))},Ft:()=>{if(v&&!u){const{Vt:r,Tt:t}=c,n=isDefaultDirectionScrollCoordinates(t),s=getScrollCoordinatesPercent(t,getElementScroll(d));var e=e=>{var e=e["Ut"],e=parent(e)===i&&e,t=(e,t,r)=>{t*=e;return numberToCssPx(r?t:-t)};return[e,e&&{transform:getTrasformTranslateValue({x:t(s.x,r.x,n.x),y:t(s.y,r.y,n.y)})}]};E(g,e),E(h,e)}},jt:S,Xt:{Yt:g,Wt:C,Jt:bind(E,g)},Gt:{Yt:h,Wt:A,Jt:bind(E,h)}},()=>(appendChildren(y,g[0].Ut),appendChildren(y,h[0].Ut),bind(runEachAndClear,m))]},createScrollbarsSetupEvents=(F,v,R,m)=>(t,s,r)=>{const{ht:i,U:n,L:o,gt:L,Kt:P,Ot:k}=v,{Ut:l,Pt:M,kt:N}=t,[d,u]=selfClearTimeout(333),[H,I]=selfClearTimeout(444),z=e=>{isFunction(L.scrollBy)&&L.scrollBy({behavior:"smooth",left:e.x,top:e.y})};let p=!0;return bind(runEachAndClear,[addEventListener(N,"pointermove pointerleave",m),addEventListener(l,"pointerenter",()=>{s(gt,!0)}),addEventListener(l,"pointerleave pointercancel",()=>{s(gt,!1)}),!o&&addEventListener(l,"mousedown",()=>{var e=getFocusedElement();(hasAttr(e,j)||hasAttr(e,B)||e===document.body)&&a(bind(focusElement,n),25)}),addEventListener(l,"wheel",e=>{var{deltaX:t,deltaY:r,deltaMode:n}=e;p&&0===n&&parent(l)===i&&z({x:t,y:r}),p=!1,s(St,!0),d(()=>{p=!0,s(St)}),preventDefault(e)},{H:!1,I:!0}),addEventListener(l,"pointerdown",bind(addEventListener,P,"click",stopAndPrevent,{A:!0,I:!0,H:!1}),{I:!0}),(()=>{const E="pointerup pointercancel lostpointercapture",S="client"+(r?"X":"Y"),w=r?$:C,x=r?"left":"top",D=r?"w":"h",O=r?"x":"y",T=[];return addEventListener(M,"pointerdown",t=>{var r=closest(t.target,"."+pt)===N;const n=r?N:M,s=F.scrollbars;var a,i,o=s[r?"dragScroll":"clickScroll"];const{button:l,isPrimary:d,pointerType:u}=t,p=s["pointers"],v=0===l&&d&&o&&(p||[]).includes(u);if(v){runEachAndClear(T),I();const F=!r&&(t.shiftKey||"instant"===o),s=bind(getBoundingClientRect,N),l=bind(getBoundingClientRect,M);const d=e(getBoundingClientRect(L)[w])/A(L)[D]||1,u=(a=getElementScroll(L)[O],i=1/d,e=>{var t=R["Vt"],r=A(M)[D]-A(N)[D],e=i*e/r*t[O];scrollElementTo(L,{[O]:a+e})}),p=t[S],v=s();var o=l(),m=v[w],g=(g=v,h=o,(g||s())[x]-(h||l())[x]+m/2),h=p-o[x];const y=r?0:h-g;o=e=>{runEachAndClear(f),n.releasePointerCapture(e.pointerId)},h=r||F;const b=k(),f=[addEventListener(P,E,o),addEventListener(P,"selectstart",e=>preventDefault(e),{H:!1}),addEventListener(M,E,o),h&&addEventListener(M,"pointermove",e=>u(y+(e[S]-p))),h&&(()=>{var e=getElementScroll(L),t=(b(),getElementScroll(L)),t={x:t.x-e.x,y:t.y-e.y};(3<c(t.x)||3<c(t.y))&&(k(),scrollElementTo(L,e),z(t),H(b))})];if(n.setPointerCapture(t.pointerId),F)u(y);else if(!r){const F=getStaticPluginModuleInstance(Et);if(F){const E=F(u,y,m,e=>{e?b():push(f,b)});push(f,E),push(T,bind(E,!0))}}}})})(),u,I])},createScrollbarsSetup=(e,t,k,M,r,n)=>{let s,N,H,I,z,F=noop,R=0;const a=["mouse","pen"],i=e=>a.includes(e.pointerType),[o,l]=selfClearTimeout(),[c,d]=selfClearTimeout(100),[j,u]=selfClearTimeout(100),[p,v]=selfClearTimeout(()=>R),[m,g]=createScrollbarsSetupElements(e,r,M,createScrollbarsSetupEvents(t,r,M,e=>i(e)&&y())),{ht:h,Qt:B,wt:$}=r,{jt:U,Nt:V,qt:q,Bt:W,Ft:Z}=m,_=(e,t)=>{if(v(),e)U(yt);else{const e=bind(U,yt,!0);0<R&&!t?p(e):e()}},y=()=>{(H?s:I)||(_(!0),c(()=>{_(!1)}))},Y=e=>{U(wt,e,!0),U(wt,e,!1)};e=e=>{i(e)&&(s=H)&&_(!0)};const b=[v,d,u,l,()=>F(),addEventListener(h,"pointerover",e,{A:!0}),addEventListener(h,"pointerenter",e),addEventListener(h,"pointerleave",e=>{i(e)&&(s=!1,H&&_(!1))}),addEventListener(h,"pointermove",e=>{i(e)&&N&&y()}),addEventListener(B,"scroll",e=>{o(()=>{q(),y()}),n(e),Z()})];return[()=>bind(runEachAndClear,push(b,g())),({It:e,Dt:t,Zt:r,tn:n})=>{var{nn:n,sn:s,en:a,cn:i}=n||{},{Ct:r,dt:o}=r||{},l=k["F"],c=getEnvironment()["T"],{k:d,rn:u}=M,[p,v]=e("showNativeOverlaidScrollbars"),[m,g]=e("scrollbars.theme");const[h,y]=e("scrollbars.visibility");var[b,f]=e("scrollbars.autoHide"),[S,C]=e("scrollbars.autoHideSuspend"),[A]=e("scrollbars.autoHideDelay"),[w,D]=e("scrollbars.dragScroll"),[O,T]=e("scrollbars.clickScroll"),[L,e]=e("overflow"),o=o&&!t,P=u.x||u.y,n=n||s||i||r||t,s=a||y||e,r=p&&c.x&&c.y,a=(e,t,r)=>{e=e.includes(E)&&(h===x||"auto"===h&&t===E);return U(vt,e,r),e};if(R=A,o&&(S&&P?(Y(!1),F(),j(()=>{F=addEventListener(B,"scroll",bind(Y,!0),{A:!0})})):Y(!0)),v&&U(it,r),g&&(U(z),U(m,!0),z=m),C&&!S&&Y(!0),f&&(N="move"===b,H="leave"===b,I="never"===b,_(I,!0)),D&&U(Ot,w),T&&U(mt,!!O),s){const e=a(L.x,d.x,!0),t=a(L.y,d.y,!1),k=e&&t;U(ht,!k)}n&&(q(),V(),Z(),i&&W(),U(bt,!u.x,!0),U(bt,!u.y,!1),U(ut,l&&!$))},{},m]},createStructureSetupElements=e=>{const{Z:t,P:o}=getEnvironment();var r=t()["elements"],{padding:r,viewport:s,content:a}=r,i=isHTMLElement(e),l=i?{}:e,c=l["elements"],{padding:c,viewport:d,content:u}=c||{},e=i?e:l.target;const p=isBodyElement(e),v=e.ownerDocument,g=v.documentElement,h=()=>v.defaultView||n;l=bind(staticInitializationElement,[e]);const y=bind(dynamicInitializationElement,[e]);var b=bind(createDiv,"");const f=bind(l,b,s),E=bind(y,b,a);l=f(d);const S=l===e;s=S&&p,a=!S&&E(u),d=!S&&l===a;const C=s?g:l,w=s?C:e,x=!S&&y(b,r,c),D=!d&&a,T=[D,C,x,w].map(e=>isHTMLElement(e)&&!parent(e)&&e),L=e=>e&&inArray(T,e);c=!L(C)&&(u=C,l=A(u),b=M(u),r=getStyles(u,m),u=getStyles(u,O),0<b.w-l.w&&!overflowIsVisible(r)||0<b.h-l.h&&!overflowIsVisible(u))?C:e;const k=s?g:C;d=s?v:C,a={vt:e,ht:w,U:C,ln:x,bt:D,gt:k,Qt:d,an:p?g:c,Kt:v,wt:p,Mt:i,L:S,un:h,yt:e=>hasAttrClass(C,j,e),St:(e,t)=>addRemoveAttrClass(C,j,e,t),Ot:()=>addRemoveAttrClass(k,j,G,!0)};const{vt:N,ht:H,ln:I,U:z,bt:R}=a,$=[()=>{removeAttrs(H,[B,P]),removeAttrs(N,P),p&&removeAttrs(g,[P,B])}];let U=contents([R,z,I,H,N].find(e=>e&&!L(e)));const V=s?N:R||z,W=bind(runEachAndClear,$);return[a,()=>{var e=h(),t=getFocusedElement();const n=e=>{appendChildren(parent(e),contents(e)),removeElements(e)},s=e=>addEventListener(e,"focusin focusout focus blur",stopAndPrevent,{I:!0,H:!1}),a="tabindex",i=getAttr(z,a),r=s(t);return setAttrs(H,B,S?"":F),setAttrs(I,Z,""),setAttrs(z,j,""),setAttrs(R,tt,""),S||(setAttrs(z,a,i||"-1"),p&&setAttrs(g,q,"")),appendChildren(V,U),appendChildren(H,I),appendChildren(I||H,!S&&z),appendChildren(z,R),push($,[r,()=>{var e=getFocusedElement(),t=L(z),e=t&&e===z?N:e;const r=s(e);removeAttrs(I,Z),removeAttrs(R,tt),removeAttrs(z,j),p&&removeAttrs(g,q),i?setAttrs(z,a,i):removeAttrs(z,a),L(R)&&n(R),t&&n(z),L(I)&&n(I),focusElement(e),r()}]),o&&!S&&(addAttrClass(z,j,K),push($,bind(removeAttrs,z,j))),focusElement(!S&&p&&t===N&&e.top===e?z:t),r(),U=0,W},W]},createTrinsicUpdateSegment=({bt:n})=>({Zt:e,_n:t,Dt:r})=>{e=(e||{}).xt,t=t.$t;n&&(e||r)&&setStyles(n,{[C]:t&&"100%"})},createPaddingUpdateSegment=({ht:e,ln:d,U:u,L:p},m)=>{const[f,E]=createCache({i:equalTRBL,o:topRightBottomLeft()},bind(topRightBottomLeft,e,"padding",""));return({It:e,Zt:t,_n:r,Dt:n})=>{let[s,a]=E(n);var i=getEnvironment()["P"],{ft:t,Ht:o,Ct:l}=t||{},r=r["F"],[c,e]=e("paddingAbsolute"),t=((t||a||(n||o))&&([s,a]=f(n)),!p&&(e||l||a));if(t){const e=!c||!d&&!i,p=s.r+s.l,f=s.t+s.b,E={[y]:e&&!r?-p:0,[S]:e?-f:0,[w]:e&&r?-p:0,top:e?-s.t:0,right:e?r?-s.r:"auto":0,left:e?r?"auto":-s.l:0,[$]:e&&`calc(100% + ${p}px)`},t={[v]:e?s.t:0,[h]:e?s.r:0,[b]:e?s.b:0,[g]:e?s.l:0};setStyles(d||u,E),setStyles(u,t),assignDeep(m,{ln:s,dn:!e,j:d?t:assignDeep({},E,t)})}return{fn:t}}},createOverflowUpdateSegment=(P,k)=>{const I=getEnvironment(),{ht:z,ln:F,U:R,L:e,Qt:j,gt:$,wt:t,St:U,un:V}=P,q=I["P"],W=t&&e,_=bind(o,0),G={display:()=>!1,direction:e=>"ltr"!==e,flexDirection:e=>e.endsWith("-reverse"),writingMode:e=>"horizontal-tb"!==e},ee=keys(G);var r={i:equalWH,o:{w:0,h:0}},s={i:equalXY,o:{}};const te=e=>{U(J,!W&&e)},[re,ne]=createCache(r,bind(getFractionalSize,R)),[se,ae]=createCache(r,bind(M,R)),[ie,oe]=createCache(r),[le]=createCache(s),[ce,de]=createCache(r),[ue]=createCache(s),[pe]=createCache({i:(e,t)=>equal(e,t,ee),o:{}},()=>hasDimensions(R)?getStyles(R,ee):{}),[ve,me]=createCache({i:(e,t)=>equalXY(e.D,t.D)&&equalXY(e.M,t.M),o:getZeroScrollCoordinates()}),ge=getStaticPluginModuleInstance(xt),he=(e,t)=>{return""+(t?X:Y)+capitalizeFirstLetter(e)};return({It:e,Zt:t,_n:r,Dt:s},{fn:a})=>{var{Ct:t,dt:o,zt:l}=t||{};const{Y:c,W:d,J:u}=ge&&ge.V(P,k,r,I,e)||{};var[p,v]=getShowNativeOverlaidScrollbars(e,I),[e,m]=e("overflow"),g=overflowIsVisible(e.x),h=overflowIsVisible(e.y);ne(s),ae(s),oe(s),de(s);v&&q&&U(K,!p);{hasAttrClass(z,B,N)&&te(!0);const[P]=d?d():[],[e]=p=re(s),[t]=A=se(s),k=D(R),I=W&&getWindowSize(V()),F={w:_(t.w+e.w),h:_(t.h+e.h)},r={w:_((I?I.w:k.w+_(k.w-t.w))+e.w),h:_((I?I.h:k.h+_(k.h-t.h))+e.h)};P&&P(),b=ce(r),S=ie(((e,t)=>{var r=n.devicePixelRatio%1!=0?1:0;const s=_(e.w-t.w),a=_(e.h-t.h);return{w:s>r?s:0,h:a>r?a:0}})(F,r),s)}var y,[b,f]=b,[S,C]=S,[A,w]=A,[p,O]=p,[T,L]=le({x:0<S.w,y:0<S.h}),g=g&&h&&(T.x||T.y)||g&&T.x&&!T.y||h&&T.y&&!T.x,h=a||t||l||O||w||f||C||m||v||!0,a=createViewportOverflowState(T,e),[l,O]=ue(a.k),[w,m]=pe(s),v=t||o||m||L||s,[e,t]=v?ve((r=>{if(!ee.some(e=>{var t=r[e];return t&&G[e](t)}))return{D:{x:0,y:0},M:{x:1,y:1}};te(!0);const n=getElementScroll($),e=U(Q,!0),t=addEventListener(j,E,e=>{var t=getElementScroll($);e.isTrusted&&t.x===n.x&&t.y===n.y&&stopPropagation(e)},{I:!0,A:!0});scrollElementTo($,{x:0,y:0}),e();var s=getElementScroll($),a=M($),o=(scrollElementTo($,{x:a.w,y:a.h}),getElementScroll($)),o=(scrollElementTo($,{x:o.x-s.x<1&&-a.w,y:o.y-s.y<1&&-a.h}),getElementScroll($));return scrollElementTo($,n),i(()=>t()),{D:s,M:o}})(w),s):me();return h&&(O&&(y=a.k,o=(o=t=>[x,H,E].map(e=>he(e,t)))(!0).concat(o()).join(" "),U(o),U(keys(y).map(e=>he(y[e],"x"===e)).join(" "),!0)),u&&c&&setStyles(R,u(a,r,c(a,A,p)))),te(!1),addRemoveAttrClass(z,B,N,g),addRemoveAttrClass(F,Z,N,g),assignDeep(k,{k:l,Lt:{x:b.w,y:b.h},Vt:{x:S.w,y:S.h},rn:T,Tt:sanitizeScrollCoordinates(e,S)}),{en:O,nn:f,sn:C,cn:t||C,pn:v}}},createStructureSetup=e=>{var[e,t,r]=createStructureSetupElements(e),n={ln:{t:0,r:0,b:0,l:0},dn:!1,j:{[y]:0,[S]:0,[w]:0,[v]:0,[h]:0,[b]:0,[g]:0},Lt:{x:0,y:0},Vt:{x:0,y:0},k:{x:H,y:H},rn:{x:!1,y:!1},Tt:getZeroScrollCoordinates()};const{vt:s,gt:a,L:i,Ot:o}=e;var{P:l,T:c}=getEnvironment();const d=!l&&(c.x||c.y),u=[createTrinsicUpdateSegment(e),createPaddingUpdateSegment(e,n),createOverflowUpdateSegment(e,n)];return[t,t=>{const r={};var e=d&&getElementScroll(a);const n=e&&o();return each(u,e=>{assignDeep(r,e(t,r)||{})}),scrollElementTo(a,e),n&&n(),i||scrollElementTo(s,0),r},n,e,r]},createSetups=(e,o,l,c,t)=>{let d=!1;var r=createOptionCheck(o,{});const[i,u,n,p,s]=createStructureSetup(e),[v,m,g]=createObserversSetup(p,n,r,e=>{f({},e)}),[h,y,,a]=createScrollbarsSetup(e,o,g,n,p,t),b=t=>keys(t).some(e=>!!t[e]),f=(e,t)=>{if(l())return!1;var{vn:r,Dt:n,At:s,hn:a}=e,r=r||{},n=!!n||!d,i={It:createOptionCheck(o,r,n),vn:r,Dt:n};if(a)return y(i),!1;a=t||m(assignDeep({},i,{At:s})),t=u(assignDeep({},i,{_n:g,Zt:a})),y(assignDeep({},i,{Zt:a,tn:t})),s=b(a),i=b(t),s=s||i||!isEmptyObject(r)||n;return d=!0,s&&c(e,{Zt:a,tn:t}),s};return[()=>{const{an:e,gt:t,Ot:r}=p;var n=getElementScroll(e),s=[v(),i(),h()];const a=r();return scrollElementTo(t,n),a(),bind(runEachAndClear,s)},f,()=>({gn:g,bn:n}),{wn:p,yn:a},s]},Mt=new WeakMap,addInstance=(e,t)=>{Mt.set(e,t)},removeInstance=e=>{Mt.delete(e)},getInstance=e=>Mt.get(e),OverlayScrollbars=(e,t,r)=>{const n=getEnvironment()["nt"];var s=isHTMLElement(e);const a=s?e:e.target;var i=getInstance(a);if(!t||i)return i;{let l=!1;const o=[],c={},d=e=>{e=removeUndefinedProperties(e);const t=getStaticPluginModuleInstance(R);return t?t(e,!0):e},u=assignDeep({},n(),d(t)),[p,v,m]=createEventListenerHub(),[g,h,y]=createEventListenerHub(r),b=(e,t)=>{y(e,t),m(e,t)},[f,E,S,C,A]=createSetups(e,u,()=>l,({vn:e,Dt:t},{Zt:r,tn:n})=>{var{ft:r,Ct:s,xt:a,Ht:i,Et:o,dt:l}=r,{nn:n,sn:c,en:d,cn:u}=n;b("updated",[w,{updateHints:{sizeChanged:!!r,directionChanged:!!s,heightIntrinsicChanged:!!a,overflowEdgeChanged:!!n,overflowAmountChanged:!!c,overflowStyleChanged:!!d,scrollCoordinatesChanged:!!u,contentMutation:!!i,hostMutation:!!o,appear:!!l},changedOptions:e||{},force:!!t}])},e=>b("scroll",[w,e]));i=e=>{removeInstance(a),runEachAndClear(o),l=!0,b("destroyed",[w,e]),v(),h()};const w={options(e,t){return e&&(t=t?n():{},t=getOptionsDiff(u,assignDeep(t,d(e))),isEmptyObject(t)||(assignDeep(u,t),E({vn:t}))),assignDeep({},u)},on:g,off:(e,t)=>{e&&t&&h(e,t)},state(){var{gn:e,bn:t}=S(),e=e["F"],{Lt:t,Vt:r,k:n,rn:s,ln:a,dn:i,Tt:o}=t;return assignDeep({},{overflowEdge:t,overflowAmount:r,overflowStyle:n,hasOverflow:s,scrollCoordinates:{start:o.D,end:o.M},padding:a,paddingAbsolute:i,directionRTL:e,destroyed:l})},elements(){var{vt:e,ht:t,ln:r,U:n,bt:s,gt:a,Qt:i}=C.wn,{Xt:o,Gt:l}=C.yn;const c=e=>{var{kt:e,Pt:t,Ut:r}=e;return{scrollbar:r,track:t,handle:e}};var d=e=>{const{Yt:t,Wt:r}=e;e=c(t[0]);return assignDeep({},e,{clone:()=>{var e=c(r());return E({hn:!0}),e}})};return assignDeep({},{target:e,host:t,padding:r||n,viewport:n,content:s||n,scrollOffsetElement:a,scrollEventElement:i,scrollbarHorizontal:d(o),scrollbarVertical:d(l)})},update:e=>E({Dt:e,At:!0}),destroy:bind(i,!1),plugin:e=>c[keys(e)[0]]};return(push(o,[A]),addInstance(a,w),registerPluginModuleInstances(T,OverlayScrollbars,[w,p,c]),cancelInitialization(C.wn.wt,!s&&e.cancel))?(i(!0),w):(push(o,f()),b("initialized",[w]),w.update(),w)}};OverlayScrollbars.plugin=e=>{var t=isArray(e);const r=t?e:[e];e=r.map(e=>registerPluginModuleInstances(e,OverlayScrollbars)[0]);return addPlugins(r),t?e:e[0]},OverlayScrollbars.valid=e=>{const t=e&&e.elements;e=isFunction(t)&&t();return isPlainObject(e)&&!!getInstance(e.target)},OverlayScrollbars.env=()=>{var{N:e,T:t,P:r,G:n,st:s,et:a,Z:i,tt:o,nt:l,ot:c}=getEnvironment();return assignDeep({},{scrollbarsSize:e,scrollbarsOverlaid:t,scrollbarsHiding:r,scrollTimeline:n,staticDefaultInitialization:s,staticDefaultOptions:a,getDefaultInitialization:i,setDefaultInitialization:o,getDefaultOptions:l,setDefaultOptions:c})},OverlayScrollbars.nonce=setNonce,OverlayScrollbars.trustedTypePolicy=setTrustedTypePolicy;export{zt as ClickScrollPlugin,OverlayScrollbars,Ht as ScrollbarsHidingPlugin,Ct as SizeObserverPlugin};