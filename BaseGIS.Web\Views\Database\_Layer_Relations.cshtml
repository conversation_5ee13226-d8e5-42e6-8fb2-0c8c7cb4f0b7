@{
    string id = Context.Request.Query["id"];
    List<BaseGIS.Core.Entities.RelationInfo> listRelations = Model;
    int counter = 1;
}

<div>
    @Html.AntiForgeryToken()
    <div class="card border-danger mb-3 samanFont" id="wid-id-42">
        <div class="card-header bg-danger text-white">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h5 class="card-title samanFont mb-0">لایه های مرتبط</h5>
                    <span class="spinner-border spinner-border-sm text-light d-none ms-2"></span>
                </div>
                <div>
                    <a data-bs-toggle="modal" href="~/database/_Layer_Relation?tblid=@id" data-bs-target="#ModalLayerRelation" class="btn btn-sm btn-outline-light" title="ایجاد لایه جدید"><i class="fa fa-plus"></i></a>
                </div>
            </div>
        </div>

        <!-- widget div-->
        <div class="collapse show" id="relationContent">
            <div class="card-body p-0">
                <div class="table-responsive" style="height:350px; overflow-y: auto;">
                    <table class="table table-bordered table-striped mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th>نام لایه مرتبط</th>
                                <th>نوع ارتباط</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in listRelations)
                            {
                                if (item.RelatedTable.Id.ToString() != id)
                                {
                                    <tr>
                                        <td>@(counter++)</td>
                                        <td>@item.RelatedTable.AliasName</td>
                                        <td>@(item.RelationType == "1" ? "1 - n" : " n - m")</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a data-bs-toggle="modal" href="~/database/_Layer_Relation?tblid=@id&id=@item.Id" data-bs-target="#ModalLayerRelation" class="btn btn-outline-primary" title="ویرایش">
                                                    <i class="fa fa-pencil"></i>
                                                </a>
                                                <button type="button" onclick="frmLayerRelationDel(@item.Id);" class="btn btn-outline-danger" title="حذف">
                                                    <i class="fa fa-times"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                                else
                                {
                                    <tr>
                                        <td>@(counter++)</td>
                                        <td>@item.MainTable.AliasName</td>
                                        <td>@(item.RelationType == "1" ? "1 - n" : " n - m")</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a data-bs-toggle="modal" href="~/database/_Layer_Relation?tblid=@(item.MainTable.Id)&id=@item.Id" data-bs-target="#ModalLayerRelation" class="btn btn-outline-primary" title="ویرایش">
                                                    <i class="fa fa-pencil"></i>
                                                </a>
                                                <button type="button" onclick="frmLayerRelationDel(@item.Id);" class="btn btn-outline-danger" title="حذف">
                                                    <i class="fa fa-times"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="ModalLayerRelation" tabindex="-1" aria-labelledby="ModalLayerRelationLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <!-- Modal content will be loaded here -->
        </div>
    </div>
</div>

<script type="text/javascript">
       $(document).ready(function () {
           $('a[data-bs-toggle="modal"][data-bs-target="#ModalLayerRelation"]').on('click', function (e) {
        e.preventDefault();
        var url = $(this).attr('href');

        $.ajax({
            url: url,
            cache: false,
            success: function (data) {
                $("#ModalLayerRelation .modal-content").html(data);
                var modalElement = document.getElementById('ModalLayerRelation');
                var modal = new bootstrap.Modal(modalElement);
                modal.show();

                // Add event listener for when modal is hidden
                modalElement.addEventListener('hidden.bs.modal', function () {
                    Refresh_Layer_Relations(@id);

                    // Remove backdrop manually if it's still present
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }
                    // Also remove modal-open class from body if needed
                    document.body.classList.remove('modal-open');
                    // And remove inline styles that Bootstrap might have added
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';
                });
            },
            error: function (data) {
                console.error("Error loading modal content:", data);
            }
        });
    });
       });

       function Refresh_Layer_Relations(id) {
           $("#_Layer_Relations").load('@Url.Action("_Layer_Relations", "database")' + '?id=' + id);
       }

       function frmLayerRelationDel(idRelation) {
           if (idRelation) {
               Swal.fire({
                   title: 'تأیید حذف',
                   html: "<b class='SamanFont'> آیا می خواهید حذف کنید؟ </b>",
                   icon: 'warning',
                   showCancelButton: true,
                   confirmButtonColor: '#d33',
                   cancelButtonColor: '#3085d6',
                   confirmButtonText: 'حذف',
                   cancelButtonText: 'لغو',
                   reverseButtons: true,
                   customClass: {
                       popup: 'samanFont',
                       title: 'samanFont',
                       htmlContainer: 'samanFont',
                       confirmButton: 'samanFont',
                       cancelButton: 'samanFont'
                   }
               }).then((result) => {
                   if (result.isConfirmed) {
                       $.ajax({
                           type: 'POST',
                           url: "../database/_Layer_RelationDel",
                           data: {
                               id: idRelation,
                               "__RequestVerificationToken": $("input[name='__RequestVerificationToken']").last().val()
                           },
                           dataType: 'json',
                           success: function (data) {
                               if (data.success) {
                                   Refresh_Layer_Relations(@id);
                                   Swal.fire({
                                       title: 'موفق',
                                       html: data.responseText,
                                       icon: 'success',
                                       timer: 2000
                                   });
                               } else {
                                   Swal.fire({
                                       title: 'خطا',
                                       html: data.responseText,
                                       icon: 'error'
                                   });
                               }
                           },
                           error: function (data) {
                               Swal.fire({
                                   title: 'خطا',
                                   html: data.statusText,
                                   icon: 'error'
                               });
                           }
                       });
                   }
               });
           }
       }
</script>



































































