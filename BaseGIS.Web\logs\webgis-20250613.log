2025-06-13 00:00:02.119 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-13 00:00:02.137 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-13 00:00:02.232 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:00:02.239 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.6252ms.
2025-06-13 00:00:02.257 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:00:02.417 +03:30 [INF] Executed ViewResult - view Index executed in 163.7696ms.
2025-06-13 00:00:02.424 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 280.2942ms
2025-06-13 00:00:02.427 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-13 00:00:02.436 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 21677.0075ms
2025-06-13 00:00:02.516 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-13 00:00:02.517 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - null null
2025-06-13 00:00:02.517 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-13 00:00:02.873 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 00:00:03.109 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 00:00:03.127 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-13 00:00:03.128 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-13 00:00:03.127 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-13 00:00:03.143 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 308.3573ms
2025-06-13 00:00:03.143 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - 499 106394 text/css 626.5933ms
2025-06-13 00:00:03.145 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - 499 232911 text/css 629.6917ms
2025-06-13 00:00:03.148 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - 499 14806 text/css 631.2293ms
2025-06-13 00:00:03.190 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 205.573ms
2025-06-13 00:00:03.196 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-13 00:00:03.359 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-13 00:00:03.361 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 499 5430 image/x-icon 165.3694ms
2025-06-13 00:00:19.999 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-13 00:00:20.049 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:00:20.055 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-13 00:00:20.094 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:00:21.465 +03:30 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 00:00:21.873 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 1775.5392ms.
2025-06-13 00:00:21.878 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:00:22.261 +03:30 [INF] Executed ViewResult - view Index executed in 383.4968ms.
2025-06-13 00:00:22.264 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 2206.3115ms
2025-06-13 00:00:22.267 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:00:22.279 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 2279.5021ms
2025-06-13 00:00:22.317 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site_map.css?v=aoP519SFZBIvvQFvf5ec-er8O88aoHY0TCfrrJFfnUw - null null
2025-06-13 00:00:22.333 +03:30 [INF] Sending file. Request path: '/css/site_map.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site_map.css'
2025-06-13 00:00:22.336 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site_map.css?v=aoP519SFZBIvvQFvf5ec-er8O88aoHY0TCfrrJFfnUw - 200 20799 text/css 19.1717ms
2025-06-13 00:00:22.336 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 00:00:22.337 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 00:00:22.344 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/proj4/dist/proj4.js - null null
2025-06-13 00:00:22.364 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 27.1105ms
2025-06-13 00:00:22.371 +03:30 [INF] The file /lib/proj4/dist/proj4.js was not modified
2025-06-13 00:00:22.377 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/proj4/dist/proj4.js - 304 null text/javascript 34.6854ms
2025-06-13 00:00:22.382 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 46.5666ms
2025-06-13 00:00:22.449 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-13 00:00:22.463 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:00:22.487 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-13 00:00:22.607 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:00:22.713 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-13 00:00:22.718 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 108.329ms.
2025-06-13 00:00:22.728 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType25`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType26`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-13 00:00:22.764 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 267.6138ms
2025-06-13 00:00:22.767 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:00:22.768 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 318.8053ms
2025-06-13 00:00:35.219 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-13 00:00:35.223 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:00:35.224 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-13 00:00:35.247 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:00:35.248 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-13 00:00:35.253 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-13 00:00:35.255 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 7.3681ms
2025-06-13 00:00:35.349 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 00:00:35.357 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 107.9517ms.
2025-06-13 00:00:35.360 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:00:35.365 +03:30 [INF] Executed ViewResult - view Index executed in 5.0525ms.
2025-06-13 00:00:35.367 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 139.9882ms
2025-06-13 00:00:35.368 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:00:35.369 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 150.1951ms
2025-06-13 00:00:35.994 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - null null
2025-06-13 00:00:35.998 +03:30 [INF] The file /lib/leaflet/leaflet.js.map was not modified
2025-06-13 00:00:35.999 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - 304 null text/plain 5.0664ms
2025-06-13 00:00:38.915 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4510396.165051681%2C5190379.968676608%2C7059112.436192598%2C2524256.422089661&size=521%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B%2C10000010007%3Bfalse%3B%3B3&f=image - null null
2025-06-13 00:00:38.926 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-13 00:00:38.929 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-13 00:00:38.948 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:00:38.954 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.RedirectToActionResult in 1.9467ms.
2025-06-13 00:00:38.960 +03:30 [INF] Executing RedirectResult, redirecting to /rest/export?bbox=4510396.165051681,5190379.968676608,7059112.436192598,2524256.422089661&size=521,545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006;Name;;,10000010007;false;;3&f=image.
2025-06-13 00:00:38.963 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 31.9743ms
2025-06-13 00:00:38.964 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-13 00:00:38.966 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4510396.165051681%2C5190379.968676608%2C7059112.436192598%2C2524256.422089661&size=521%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B%2C10000010007%3Bfalse%3B%3B3&f=image - 302 0 null 51.0435ms
2025-06-13 00:00:38.978 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/export?bbox=4510396.165051681,5190379.968676608,7059112.436192598,2524256.422089661&size=521,545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006;Name;;,10000010007;false;;3&f=image - null null
2025-06-13 00:00:38.983 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web)'
2025-06-13 00:00:38.988 +03:30 [INF] Route matched with {action = "ExportMapImage", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ExportMapImage(System.String, System.String, System.String, System.String, System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-13 00:00:39.008 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:00:39.114 +03:30 [INF] Executed DbCommand (30ms) [Parameters=[@__tableIdInt_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableIdInt_0
2025-06-13 00:00:39.159 +03:30 [INF] Executed DbCommand (12ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-13 00:00:39.194 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-13 00:00:39.527 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableIdInt_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableIdInt_0
2025-06-13 00:00:39.550 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__8__locals1_tableInfo_Id_0='?' (DbType = Int32), @__symbologyId_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__8__locals1_tableInfo_Id_0 AND [s].[Id] = @__symbologyId_1
2025-06-13 00:00:39.576 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-13 00:00:44.972 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 5960.2632ms.
2025-06-13 00:00:44.977 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-13 00:00:44.984 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web) in 5993.7568ms
2025-06-13 00:00:44.987 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web)'
2025-06-13 00:00:44.990 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/export?bbox=4510396.165051681,5190379.968676608,7059112.436192598,2524256.422089661&size=521,545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006;Name;;,10000010007;false;;3&f=image - 200 203583 image/png 6011.2366ms
2025-06-13 00:09:16.815 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-13 00:09:16.891 +03:30 [ERR] An error occurred while seeding the database
System.InvalidOperationException: No service for type 'Microsoft.AspNetCore.Identity.RoleManager`1[Microsoft.AspNetCore.Identity.IdentityRole]' has been registered.
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<Main>$(String[] args) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\Program.cs:line 164
2025-06-13 00:09:17.417 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-13 00:09:17.419 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-13 00:09:17.485 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-13 00:09:17.487 +03:30 [INF] Hosting environment: Development
2025-06-13 00:09:17.489 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-13 00:09:17.492 +03:30 [INF] The application has started
2025-06-13 00:09:19.591 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-13 00:09:40.905 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-13 00:09:40.921 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-13 00:09:41.012 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:09:41.019 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.6995ms.
2025-06-13 00:09:41.035 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:09:41.262 +03:30 [INF] Executed ViewResult - view Index executed in 232.3812ms.
2025-06-13 00:09:41.272 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 344.6354ms
2025-06-13 00:09:41.276 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-13 00:09:41.289 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 21705.384ms
2025-06-13 00:09:41.322 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-13 00:09:41.323 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-13 00:09:41.323 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - null null
2025-06-13 00:09:41.562 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 00:09:41.638 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 00:09:41.843 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-13 00:09:41.843 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-13 00:09:41.843 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-13 00:09:41.860 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - 499 14806 text/css 537.0538ms
2025-06-13 00:09:41.863 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - 499 232911 text/css 540.6407ms
2025-06-13 00:09:41.858 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 400.5233ms
2025-06-13 00:09:41.873 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - 499 106394 text/css 550.3627ms
2025-06-13 00:09:41.907 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 305.3386ms
2025-06-13 00:09:41.919 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-13 00:09:42.126 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-13 00:09:42.129 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 499 5430 image/x-icon 210.1993ms
2025-06-13 00:09:43.173 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-13 00:09:43.227 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:09:43.232 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-13 00:09:43.266 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:09:44.685 +03:30 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 00:09:45.097 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 1826.6118ms.
2025-06-13 00:09:45.103 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:09:45.506 +03:30 [INF] Executed ViewResult - view Index executed in 403.7145ms.
2025-06-13 00:09:45.510 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 2273.5374ms
2025-06-13 00:09:45.513 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:09:45.525 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 2351.8883ms
2025-06-13 00:09:45.569 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site_map.css?v=aoP519SFZBIvvQFvf5ec-er8O88aoHY0TCfrrJFfnUw - null null
2025-06-13 00:09:45.575 +03:30 [INF] The file /css/site_map.css was not modified
2025-06-13 00:09:45.577 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site_map.css?v=aoP519SFZBIvvQFvf5ec-er8O88aoHY0TCfrrJFfnUw - 304 null text/css 8.3671ms
2025-06-13 00:09:45.588 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 00:09:45.588 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 00:09:45.607 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 18.5947ms
2025-06-13 00:09:45.620 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 32.8917ms
2025-06-13 00:09:45.679 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-13 00:09:45.691 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:09:45.712 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-13 00:09:45.773 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:09:45.860 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-13 00:09:45.868 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 92.5741ms.
2025-06-13 00:09:45.875 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType25`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType26`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-13 00:09:45.909 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 191.2302ms
2025-06-13 00:09:45.911 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:09:45.913 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 233.2472ms
2025-06-13 00:09:50.843 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-13 00:09:50.849 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-13 00:09:50.851 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 8.6452ms
2025-06-13 00:11:26.274 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-13 00:11:26.351 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:11:26.355 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-13 00:11:26.393 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:11:26.475 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 00:11:26.485 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 90.2496ms.
2025-06-13 00:11:26.489 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:11:26.866 +03:30 [INF] Executed ViewResult - view Index executed in 378.3608ms.
2025-06-13 00:11:26.868 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 510.5148ms
2025-06-13 00:11:26.870 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:11:26.872 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 597.2172ms
2025-06-13 00:11:27.048 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/proj4/dist/proj4.js - null null
2025-06-13 00:11:27.050 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 00:11:27.096 +03:30 [INF] The file /lib/proj4/dist/proj4.js was not modified
2025-06-13 00:11:27.091 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 00:11:27.113 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/proj4/dist/proj4.js - 304 null text/javascript 64.6918ms
2025-06-13 00:11:27.162 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 111.9054ms
2025-06-13 00:11:27.217 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 110.8888ms
2025-06-13 00:11:27.098 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-13 00:11:27.258 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-13 00:11:27.260 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 163.1012ms
2025-06-13 00:11:27.459 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-13 00:11:27.465 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:11:27.472 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-13 00:11:27.499 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:11:27.506 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-13 00:11:27.509 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 8.0059ms.
2025-06-13 00:11:27.511 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType25`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType26`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-13 00:11:27.522 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 47.3659ms
2025-06-13 00:11:27.524 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:11:27.525 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 65.927ms
2025-06-13 00:13:43.443 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-13 00:13:43.492 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:13:43.498 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-13 00:13:43.529 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:13:43.534 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 00:13:43.543 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 11.0381ms.
2025-06-13 00:13:43.548 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:13:44.013 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site_map.css?v=aoP519SFZBIvvQFvf5ec-er8O88aoHY0TCfrrJFfnUw - null null
2025-06-13 00:13:44.051 +03:30 [INF] Executed ViewResult - view Index executed in 504.4648ms.
2025-06-13 00:13:44.055 +03:30 [INF] The file /css/site_map.css was not modified
2025-06-13 00:13:44.057 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 555.9326ms
2025-06-13 00:13:44.062 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site_map.css?v=aoP519SFZBIvvQFvf5ec-er8O88aoHY0TCfrrJFfnUw - 304 null text/css 49.3385ms
2025-06-13 00:13:44.066 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:13:44.073 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 630.1407ms
2025-06-13 00:13:44.097 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-13 00:13:44.101 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 00:13:44.101 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-13 00:13:44.102 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 00:13:44.107 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 10.1308ms
2025-06-13 00:13:44.114 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 11.794ms
2025-06-13 00:13:44.123 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 22.851ms
2025-06-13 00:13:44.270 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-13 00:13:44.280 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:13:44.287 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-13 00:13:44.351 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:13:44.357 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-13 00:13:44.362 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 8.9634ms.
2025-06-13 00:13:44.365 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType25`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType26`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-13 00:13:44.373 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 83.4471ms
2025-06-13 00:13:44.375 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:13:44.377 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 107.5986ms
2025-06-13 00:14:07.989 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-13 00:14:08.040 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:14:08.043 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-13 00:14:08.071 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:14:08.075 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 00:14:08.081 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 6.9874ms.
2025-06-13 00:14:08.083 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:14:08.486 +03:30 [INF] Executed ViewResult - view Index executed in 404.1857ms.
2025-06-13 00:14:08.489 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 442.7302ms
2025-06-13 00:14:08.492 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:14:08.495 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 505.4264ms
2025-06-13 00:14:08.575 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 00:14:08.582 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 00:14:08.629 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 54.0627ms
2025-06-13 00:14:08.631 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 49.2222ms
2025-06-13 00:14:08.640 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-13 00:14:08.647 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-13 00:14:08.649 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 8.4662ms
2025-06-13 00:14:09.564 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-13 00:14:09.601 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:14:09.630 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-13 00:14:09.792 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:14:09.834 +03:30 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-13 00:14:09.837 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 25.3733ms.
2025-06-13 00:14:09.847 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType25`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType26`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-13 00:14:09.854 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 95.3438ms
2025-06-13 00:14:09.860 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:14:09.862 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 298.0928ms
2025-06-13 00:14:27.798 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-13 00:14:27.834 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:14:27.838 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-13 00:14:27.878 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:14:27.882 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 00:14:27.891 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 11.5752ms.
2025-06-13 00:14:27.895 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:14:28.242 +03:30 [INF] Executed ViewResult - view Index executed in 347.9897ms.
2025-06-13 00:14:28.245 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 405.063ms
2025-06-13 00:14:28.247 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:14:28.249 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 450.8027ms
2025-06-13 00:14:28.302 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 00:14:28.303 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 00:14:28.310 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 6.7853ms
2025-06-13 00:14:28.312 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 9.689ms
2025-06-13 00:14:29.134 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-13 00:14:29.142 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:14:29.150 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-13 00:14:29.203 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:14:29.213 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-13 00:14:29.216 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 9.4714ms.
2025-06-13 00:14:29.218 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType25`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType26`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-13 00:14:29.229 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 73.3356ms
2025-06-13 00:14:29.231 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:14:29.232 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 97.7851ms
2025-06-13 00:14:39.532 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-13 00:14:39.537 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:14:39.539 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-13 00:14:39.572 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:14:39.578 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 00:14:39.589 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 13.7001ms.
2025-06-13 00:14:39.594 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:14:39.597 +03:30 [INF] Executed ViewResult - view Index executed in 3.109ms.
2025-06-13 00:14:39.599 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 58.3686ms
2025-06-13 00:14:39.601 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:14:39.603 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 70.4413ms
2025-06-13 00:14:39.657 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-13 00:14:39.661 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-13 00:14:39.662 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 5.4006ms
2025-06-13 00:15:22.625 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-13 00:15:22.669 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:15:22.671 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-13 00:15:22.703 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:15:22.707 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 00:15:22.712 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 6.6856ms.
2025-06-13 00:15:22.717 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:15:23.069 +03:30 [INF] Executed ViewResult - view Index executed in 353.9087ms.
2025-06-13 00:15:23.072 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 398.4772ms
2025-06-13 00:15:23.073 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:15:23.075 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 450.271ms
2025-06-13 00:15:23.167 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 00:15:23.169 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 00:15:23.179 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 9.3742ms
2025-06-13 00:15:23.195 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 28.0196ms
2025-06-13 00:15:23.260 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-13 00:15:23.266 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:15:23.271 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-13 00:15:23.305 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:15:23.310 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-13 00:15:23.313 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 6.4945ms.
2025-06-13 00:15:23.316 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType25`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType26`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-13 00:15:23.324 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 50.5342ms
2025-06-13 00:15:23.326 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:15:23.327 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 67.2449ms
2025-06-13 00:15:23.755 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-13 00:15:23.765 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-13 00:15:23.768 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 12.1147ms
2025-06-13 00:15:33.258 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Rest/Legend - application/x-www-form-urlencoded; charset=UTF-8 34
2025-06-13 00:15:33.266 +03:30 [INF] CORS policy execution successful.
2025-06-13 00:15:33.270 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web)'
2025-06-13 00:15:33.273 +03:30 [INF] Route matched with {action = "Legend", controller = "REST"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult Legend(System.String, Int32) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-13 00:15:33.305 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:15:33.475 +03:30 [INF] Executed DbCommand (125ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[Id] = @__p_0
2025-06-13 00:15:33.521 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-13 00:15:33.899 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 591.4869ms.
2025-06-13 00:15:33.903 +03:30 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[BaseGIS.Web.Controllers.NodeTree, BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 00:15:33.929 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web) in 653.9686ms
2025-06-13 00:15:33.932 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web)'
2025-06-13 00:15:33.934 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Rest/Legend - 200 null application/json; charset=utf-8 676.3032ms
2025-06-13 00:15:42.854 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=3067265.0710275527%2C5190379.968676608%2C8507135.500026979%2C2524256.422089661&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B%2C10000010007%3Bfalse%3B%3B3&f=image - null null
2025-06-13 00:15:42.859 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-13 00:15:42.861 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-13 00:15:42.880 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:15:42.887 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.RedirectToActionResult in 2.73ms.
2025-06-13 00:15:42.895 +03:30 [INF] Executing RedirectResult, redirecting to /rest/export?bbox=3067265.0710275527,5190379.968676608,8507135.500026979,2524256.422089661&size=1112,545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006;Name;;,10000010007;false;;3&f=image.
2025-06-13 00:15:42.898 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 35.5007ms
2025-06-13 00:15:42.901 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-13 00:15:42.904 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=3067265.0710275527%2C5190379.968676608%2C8507135.500026979%2C2524256.422089661&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B%2C10000010007%3Bfalse%3B%3B3&f=image - 302 0 null 50.447ms
2025-06-13 00:15:42.912 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/export?bbox=3067265.0710275527,5190379.968676608,8507135.500026979,2524256.422089661&size=1112,545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006;Name;;,10000010007;false;;3&f=image - null null
2025-06-13 00:15:42.916 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web)'
2025-06-13 00:15:42.920 +03:30 [INF] Route matched with {action = "ExportMapImage", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ExportMapImage(System.String, System.String, System.String, System.String, System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-13 00:15:42.942 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:15:43.017 +03:30 [INF] Executed DbCommand (15ms) [Parameters=[@__tableIdInt_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableIdInt_0
2025-06-13 00:15:43.061 +03:30 [INF] Executed DbCommand (12ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-13 00:15:43.100 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-13 00:15:43.419 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableIdInt_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableIdInt_0
2025-06-13 00:15:43.434 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals1_tableInfo_Id_0='?' (DbType = Int32), @__symbologyId_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__8__locals1_tableInfo_Id_0 AND [s].[Id] = @__symbologyId_1
2025-06-13 00:15:43.440 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-13 00:15:48.032 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 5086.7414ms.
2025-06-13 00:15:48.037 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-13 00:15:48.046 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web) in 5120.6818ms
2025-06-13 00:15:48.048 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ExportMapImage (BaseGIS.Web)'
2025-06-13 00:15:48.050 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/export?bbox=3067265.0710275527,5190379.968676608,8507135.500026979,2524256.422089661&size=1112,545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006;Name;;,10000010007;false;;3&f=image - 200 212344 image/png 5137.5416ms
2025-06-13 00:15:56.437 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-13 00:15:56.497 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-13 00:15:56.500 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-13 00:15:56.534 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:15:56.539 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0191ms.
2025-06-13 00:15:56.543 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:15:56.549 +03:30 [INF] Executed ViewResult - view Index executed in 6.3452ms.
2025-06-13 00:15:56.553 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 50.7244ms
2025-06-13 00:15:56.555 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-13 00:15:56.558 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 120.9421ms
2025-06-13 00:15:56.633 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 00:15:56.634 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 00:15:56.651 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 17.5573ms
2025-06-13 00:15:56.657 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 23.1684ms
2025-06-13 00:15:58.520 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-13 00:15:58.567 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:15:58.569 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-13 00:15:58.609 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:15:58.613 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 00:15:58.620 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 8.7437ms.
2025-06-13 00:15:58.625 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:15:58.628 +03:30 [INF] Executed ViewResult - view Index executed in 3.2483ms.
2025-06-13 00:15:58.630 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 56.2429ms
2025-06-13 00:15:58.632 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:15:58.634 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 113.4208ms
2025-06-13 00:15:58.700 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 00:15:58.700 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 00:15:58.772 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 71.3427ms
2025-06-13 00:15:58.774 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 73.6767ms
2025-06-13 00:15:58.820 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-13 00:15:58.824 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:15:58.825 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-13 00:15:58.854 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:15:58.860 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-13 00:15:58.864 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 7.7356ms.
2025-06-13 00:15:58.867 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType25`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType26`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-13 00:15:58.873 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 45.4593ms
2025-06-13 00:15:58.878 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:15:58.880 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 59.9059ms
2025-06-13 00:16:15.127 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-13 00:16:15.163 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-13 00:16:15.165 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-13 00:16:15.198 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:16:15.201 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0188ms.
2025-06-13 00:16:15.205 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:16:15.208 +03:30 [INF] Executed ViewResult - view Index executed in 2.2285ms.
2025-06-13 00:16:15.209 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 40.9638ms
2025-06-13 00:16:15.212 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-13 00:16:15.214 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 87.1792ms
2025-06-13 00:16:15.275 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 00:16:15.276 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 00:16:15.285 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 8.7853ms
2025-06-13 00:16:15.289 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 13.9343ms
2025-06-13 00:16:16.394 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-13 00:16:16.429 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:16:16.432 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-13 00:16:16.479 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:16:16.484 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 00:16:16.490 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 7.732ms.
2025-06-13 00:16:16.493 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:16:16.496 +03:30 [INF] Executed ViewResult - view Index executed in 3.0083ms.
2025-06-13 00:16:16.498 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 60.8671ms
2025-06-13 00:16:16.500 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:16:16.502 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 108.4447ms
2025-06-13 00:16:16.566 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 00:16:16.573 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 7.0576ms
2025-06-13 00:16:16.657 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-13 00:16:16.661 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:16:16.662 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-13 00:16:16.692 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:16:16.696 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-13 00:16:16.699 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 4.0324ms.
2025-06-13 00:16:16.701 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType25`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType26`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-13 00:16:16.706 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 40.9556ms
2025-06-13 00:16:16.708 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:16:16.710 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 53.1733ms
2025-06-13 00:16:29.061 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-13 00:16:29.064 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-13 00:16:29.065 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 4.5225ms
2025-06-13 00:17:44.364 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-13 00:17:44.415 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:17:44.417 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-13 00:17:44.461 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:17:44.466 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 00:17:44.478 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 13.44ms.
2025-06-13 00:17:44.480 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 00:17:44.497 +03:30 [INF] Executed ViewResult - view Index executed in 16.6006ms.
2025-06-13 00:17:44.513 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 93.095ms
2025-06-13 00:17:44.516 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-13 00:17:44.517 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 152.9122ms
2025-06-13 00:17:44.584 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site_map.css?v=aoP519SFZBIvvQFvf5ec-er8O88aoHY0TCfrrJFfnUw - null null
2025-06-13 00:17:44.588 +03:30 [INF] The file /css/site_map.css was not modified
2025-06-13 00:17:44.590 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site_map.css?v=aoP519SFZBIvvQFvf5ec-er8O88aoHY0TCfrrJFfnUw - 304 null text/css 5.8761ms
2025-06-13 00:17:44.614 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 00:17:44.615 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 00:17:44.620 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 4.8306ms
2025-06-13 00:17:44.637 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 22.8497ms
2025-06-13 00:17:44.801 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-13 00:17:44.805 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:17:44.806 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-13 00:17:44.842 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 00:17:44.848 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-13 00:17:44.851 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 6.7997ms.
2025-06-13 00:17:44.853 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType25`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType26`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-13 00:17:44.859 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 49.3329ms
2025-06-13 00:17:44.861 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-13 00:17:44.863 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 61.2056ms
2025-06-13 00:17:45.382 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-13 00:17:45.387 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-13 00:17:45.392 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 10.1765ms
2025-06-13 16:58:13.282 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-13 16:58:13.609 +03:30 [ERR] An error occurred while seeding the database
System.InvalidOperationException: No service for type 'Microsoft.AspNetCore.Identity.RoleManager`1[Microsoft.AspNetCore.Identity.IdentityRole]' has been registered.
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<Main>$(String[] args) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\Program.cs:line 164
2025-06-13 16:58:17.458 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-13 16:58:17.460 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-13 16:58:17.551 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-13 16:58:17.555 +03:30 [INF] Hosting environment: Development
2025-06-13 16:58:17.558 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-13 16:58:17.565 +03:30 [INF] The application has started
2025-06-13 16:58:20.027 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-13 16:58:21.374 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-13 16:58:21.401 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-13 16:58:21.650 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 16:58:21.657 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.6027ms.
2025-06-13 16:58:21.707 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-13 16:58:22.145 +03:30 [INF] Executed ViewResult - view Index executed in 470.5135ms.
2025-06-13 16:58:22.157 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 753.0446ms
2025-06-13 16:58:22.179 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-13 16:58:22.195 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 2194.7194ms
2025-06-13 16:58:22.210 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-13 16:58:22.213 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - null null
2025-06-13 16:58:22.230 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - null null
2025-06-13 16:58:22.210 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - null null
2025-06-13 16:58:22.210 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-13 16:58:22.244 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - null null
2025-06-13 16:58:23.014 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.css'
2025-06-13 16:58:23.034 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-13 16:58:23.014 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.css'
2025-06-13 16:58:23.014 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-13 16:58:22.281 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - null null
2025-06-13 16:58:22.308 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - null null
2025-06-13 16:58:22.319 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - null null
2025-06-13 16:58:23.119 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-13 16:58:23.124 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - 499 23932 text/css 894.728ms
2025-06-13 16:58:23.127 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - 499 232911 text/css 917.5075ms
2025-06-13 16:58:23.130 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - 499 5267 text/css 917.6075ms
2025-06-13 16:58:23.133 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - 499 14806 text/css 922.4132ms
2025-06-13 16:58:23.135 +03:30 [INF] Sending file. Request path: '/lib/source-sans-3/index.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\source-sans-3\index.css'
2025-06-13 16:58:23.144 +03:30 [INF] The file /lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js was not modified
2025-06-13 16:58:23.149 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - 499 106394 text/css 938.4025ms
2025-06-13 16:58:23.154 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-13 16:58:23.155 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-13 16:58:23.159 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-13 16:58:23.163 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - null null
2025-06-13 16:58:23.506 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-13 16:58:23.678 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-13 16:58:23.168 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=Nmb9k2AhsnpOv4cI_Fjp8KgGhTAxQTOTwjkpS20OWjE - null null
2025-06-13 16:58:23.177 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 16:58:23.169 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - 200 2520 text/css 925.181ms
2025-06-13 16:58:23.368 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-13 16:58:23.171 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - 304 null text/javascript 863.3858ms
2025-06-13 16:58:23.163 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/avatars/male.png - null null
2025-06-13 16:58:23.819 +03:30 [INF] Sending file. Request path: '/assets/img/AdminLTELogo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\AdminLTELogo.png'
2025-06-13 16:58:23.820 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 499 4983 image/jpeg 665.2886ms
2025-06-13 16:58:23.822 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 499 3398 image/jpeg 663.6904ms
2025-06-13 16:58:23.828 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - null null
2025-06-13 16:58:23.834 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 16:58:23.837 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - null null
2025-06-13 16:58:23.838 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 499 2750 image/jpeg 683.3962ms
2025-06-13 16:58:23.990 +03:30 [INF] Sending file. Request path: '/js/site.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\site.js'
2025-06-13 16:58:23.995 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 817.6294ms
2025-06-13 16:58:23.983 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - 499 2637 image/png 819.7563ms
2025-06-13 16:58:23.998 +03:30 [INF] The file /css/site.css was not modified
2025-06-13 16:58:24.007 +03:30 [INF] The file /lib/popper.js/dist/umd/popper.min.js was not modified
2025-06-13 16:58:23.844 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/styles/overlayscrollbars.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\styles\overlayscrollbars.min.css'
2025-06-13 16:58:23.982 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-13 16:58:24.022 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=Nmb9k2AhsnpOv4cI_Fjp8KgGhTAxQTOTwjkpS20OWjE - 200 5215 text/javascript 854.6771ms
2025-06-13 16:58:24.028 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/bootstrap-icons.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\bootstrap-icons.min.css'
2025-06-13 16:58:24.036 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - 304 null text/css 207.8263ms
2025-06-13 16:58:24.037 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - 304 null text/javascript 200.5517ms
2025-06-13 16:58:24.040 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - 200 13202 text/css 1789.1934ms
2025-06-13 16:58:24.043 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/avatars/male.png - 499 268 image/png 880.3927ms
2025-06-13 16:58:24.052 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - 200 85875 text/css 1732.9291ms
2025-06-13 16:58:24.088 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 253.9465ms
2025-06-13 16:58:24.105 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-13 16:58:24.290 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-13 16:58:24.342 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-13 16:58:24.342 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - null null
2025-06-13 16:58:24.342 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-13 16:58:24.513 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 499 45276 application/font-woff 407.7476ms
2025-06-13 16:58:24.512 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-13 16:58:24.658 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/webfonts/fa-solid-900.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\webfonts\fa-solid-900.woff2'
2025-06-13 16:58:24.668 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 499 5430 image/x-icon 435.4163ms
2025-06-13 16:58:24.670 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - 499 158220 font/woff2 328.1504ms
2025-06-13 16:58:24.673 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-13 16:58:24.682 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - 200 130396 font/woff2 340.1166ms
2025-06-13 16:58:37.928 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting - null null
2025-06-13 16:58:38.026 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-13 16:58:38.034 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-13 16:58:38.120 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 16:58:43.638 +03:30 [INF] Executed DbCommand (51ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 16:58:43.905 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 5783.081ms.
2025-06-13 16:58:43.927 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-13 16:58:44.001 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 90.9206ms.
2025-06-13 16:58:44.005 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 5965.6269ms
2025-06-13 16:58:44.009 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-13 16:58:44.023 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting - 200 null text/html; charset=utf-8 6094.8763ms
2025-06-13 16:58:44.064 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css - null null
2025-06-13 16:58:44.074 +03:30 [INF] The file /lib/jquery.fancytree/skin-win8/ui.fancytree.min.css was not modified
2025-06-13 16:58:44.078 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 16:58:44.078 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 16:58:44.083 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/jquery.fancytree-all.min.js - null null
2025-06-13 16:58:44.079 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css - 304 null text/css 14.4945ms
2025-06-13 16:58:44.092 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 13.9203ms
2025-06-13 16:58:44.093 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 15.562ms
2025-06-13 16:58:44.095 +03:30 [INF] The file /lib/jquery.fancytree/jquery.fancytree-all.min.js was not modified
2025-06-13 16:58:44.111 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/jquery.fancytree-all.min.js - 304 null text/javascript 27.8981ms
2025-06-13 16:59:18.533 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager - null null
2025-06-13 16:59:18.633 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-13 16:59:18.637 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-13 16:59:18.708 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 16:59:18.804 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 16:59:18.813 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 65.5662ms.
2025-06-13 16:59:18.817 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-13 16:59:18.840 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 23.8332ms.
2025-06-13 16:59:18.852 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 210.8646ms
2025-06-13 16:59:18.862 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-13 16:59:18.871 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager - 200 null text/html; charset=utf-8 337.7321ms
2025-06-13 16:59:18.964 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 16:59:18.967 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 16:59:18.991 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 23.5651ms
2025-06-13 16:59:18.999 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 34.1231ms
2025-06-13 16:59:28.183 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-13 16:59:28.244 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-13 16:59:28.249 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-13 16:59:28.306 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 16:59:28.531 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 222.3734ms.
2025-06-13 16:59:28.538 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-13 16:59:28.641 +03:30 [INF] Executed ViewResult - view Insert executed in 104.8997ms.
2025-06-13 16:59:28.644 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 391.6002ms
2025-06-13 16:59:28.647 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-13 16:59:28.650 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 466.7187ms
2025-06-13 16:59:28.697 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/dropzone/min/dropzone.min.css - null null
2025-06-13 16:59:28.701 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/smartwizard/dist/css/smart_wizard_all.min.css - null null
2025-06-13 16:59:28.703 +03:30 [INF] Sending file. Request path: '/lib/dropzone/min/dropzone.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\dropzone\min\dropzone.min.css'
2025-06-13 16:59:28.703 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/select2/css/select2.min.css - null null
2025-06-13 16:59:28.779 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/dropzone/min/dropzone.min.css - 200 9830 text/css 81.6569ms
2025-06-13 16:59:28.720 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 16:59:28.754 +03:30 [INF] The file /lib/smartwizard/dist/css/smart_wizard_all.min.css was not modified
2025-06-13 16:59:28.820 +03:30 [INF] The file /lib/select2/css/select2.min.css was not modified
2025-06-13 16:59:28.720 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/dropzone/min/dropzone.min.js - null null
2025-06-13 16:59:28.720 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 16:59:28.753 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/smartwizard/dist/js/jquery.smartWizard.min.js - null null
2025-06-13 16:59:28.754 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/select2/js/i18n/fa.js - null null
2025-06-13 16:59:28.774 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/select2/js/select2.min.js - null null
2025-06-13 16:59:28.850 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/smartwizard/dist/css/smart_wizard_all.min.css - 304 null text/css 148.8401ms
2025-06-13 16:59:28.853 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/select2/css/select2.min.css - 304 null text/css 151.0809ms
2025-06-13 16:59:28.857 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 137.8223ms
2025-06-13 16:59:28.862 +03:30 [INF] The file /lib/dropzone/min/dropzone.min.js was not modified
2025-06-13 16:59:28.870 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 149.4698ms
2025-06-13 16:59:28.876 +03:30 [INF] The file /lib/smartwizard/dist/js/jquery.smartWizard.min.js was not modified
2025-06-13 16:59:28.880 +03:30 [INF] The file /lib/select2/js/i18n/fa.js was not modified
2025-06-13 16:59:28.885 +03:30 [INF] The file /lib/select2/js/select2.min.js was not modified
2025-06-13 16:59:28.902 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/dropzone/min/dropzone.min.js - 304 null text/javascript 182.1814ms
2025-06-13 16:59:28.911 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/smartwizard/dist/js/jquery.smartWizard.min.js - 304 null text/javascript 157.5109ms
2025-06-13 16:59:28.913 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/select2/js/i18n/fa.js - 304 null text/javascript 159.0538ms
2025-06-13 16:59:28.915 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/select2/js/select2.min.js - 304 null text/javascript 140.1752ms
2025-06-13 16:59:28.989 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749821368888 - null null
2025-06-13 16:59:29.021 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-13 16:59:29.029 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-13 16:59:29.063 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 16:59:29.367 +03:30 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-13 16:59:29.403 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 16:59:29.587 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 520.6464ms.
2025-06-13 16:59:29.595 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-13 16:59:29.877 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 283.0757ms.
2025-06-13 16:59:29.879 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 844.6187ms
2025-06-13 16:59:29.882 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-13 16:59:29.884 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749821368888 - 200 null text/html; charset=utf-8 895.6997ms
2025-06-13 16:59:38.329 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Update - null null
2025-06-13 16:59:38.411 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web)'
2025-06-13 16:59:38.417 +03:30 [INF] Route matched with {action = "Update", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Update() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-13 16:59:38.496 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 16:59:38.751 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 250.3864ms.
2025-06-13 16:59:38.757 +03:30 [INF] Executing ViewResult, running view Update.
2025-06-13 16:59:38.784 +03:30 [INF] Executed ViewResult - view Update executed in 28.208ms.
2025-06-13 16:59:38.788 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web) in 365.1546ms
2025-06-13 16:59:38.792 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web)'
2025-06-13 16:59:38.794 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Update - 200 null text/html; charset=utf-8 464.8255ms
2025-06-13 16:59:38.878 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 16:59:38.879 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 16:59:38.903 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 24.3395ms
2025-06-13 16:59:38.904 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 26.4293ms
2025-06-13 16:59:38.990 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Update_List?_=1749821378917 - null null
2025-06-13 16:59:38.995 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web)'
2025-06-13 16:59:38.999 +03:30 [INF] Route matched with {action = "_Update_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Update_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-13 16:59:39.031 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 16:59:39.141 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-13 16:59:39.152 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 16:59:39.160 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 125.9889ms.
2025-06-13 16:59:39.166 +03:30 [INF] Executing PartialViewResult, running view _Update_List.
2025-06-13 16:59:39.388 +03:30 [INF] Executed PartialViewResult - view _Update_List executed in 223.0112ms.
2025-06-13 16:59:39.392 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web) in 388.5379ms
2025-06-13 16:59:39.397 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web)'
2025-06-13 16:59:39.400 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Update_List?_=1749821378917 - 200 null text/html; charset=utf-8 409.7158ms
2025-06-13 16:59:57.627 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Update_Wizard?id=6&_=1749821378918 - null null
2025-06-13 16:59:57.633 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_Wizard (BaseGIS.Web)'
2025-06-13 16:59:57.656 +03:30 [INF] Route matched with {action = "_Update_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Update_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-13 16:59:57.721 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Update_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-13 16:59:58.029 +03:30 [INF] Executed DbCommand (151ms) [Parameters=[@__ids_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__ids_0
2025-06-13 16:59:58.109 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Update_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 384.5686ms.
2025-06-13 16:59:58.114 +03:30 [INF] Executing PartialViewResult, running view _Update_Wizard.
2025-06-13 16:59:58.149 +03:30 [INF] Executed PartialViewResult - view null executed in 35.455ms.
2025-06-13 16:59:58.151 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Update_Wizard (BaseGIS.Web) in 490.8334ms
2025-06-13 16:59:58.154 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_Wizard (BaseGIS.Web)'
2025-06-13 16:59:58.159 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Update_Wizard?id=6&_=1749821378918 - 200 null text/html; charset=utf-8 532.1723ms
2025-06-13 17:00:10.528 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-13 17:00:10.582 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-13 17:00:10.584 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-13 17:00:10.639 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 17:00:10.643 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0327ms.
2025-06-13 17:00:10.647 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-13 17:00:10.652 +03:30 [INF] Executed ViewResult - view Insert executed in 4.8379ms.
2025-06-13 17:00:10.656 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 65.9881ms
2025-06-13 17:00:10.661 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-13 17:00:10.665 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 136.783ms
2025-06-13 17:00:10.770 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 17:00:10.774 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 17:00:10.780 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 6.4395ms
2025-06-13 17:00:10.781 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 11.4569ms
2025-06-13 17:00:10.880 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749821410803 - null null
2025-06-13 17:00:10.885 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-13 17:00:10.887 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-13 17:00:10.928 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 17:00:10.934 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-13 17:00:10.947 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 17:00:10.955 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 23.5789ms.
2025-06-13 17:00:10.959 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-13 17:00:10.962 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 3.3088ms.
2025-06-13 17:00:10.966 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 75.2459ms
2025-06-13 17:00:10.971 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-13 17:00:10.973 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749821410803 - 200 null text/html; charset=utf-8 93.0625ms
2025-06-13 17:00:18.228 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749821410804 - null null
2025-06-13 17:00:18.235 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-13 17:00:18.240 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-13 17:00:18.291 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-13 17:00:18.352 +03:30 [INF] Executed DbCommand (7ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-13 17:00:18.507 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 212.8568ms.
2025-06-13 17:00:18.512 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-13 17:00:18.600 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 88.2787ms.
2025-06-13 17:00:18.604 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 358.9396ms
2025-06-13 17:00:18.609 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-13 17:00:18.611 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749821410804 - 200 null text/html; charset=utf-8 383.3538ms
2025-06-13 17:00:27.241 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Database/_FileUpload - multipart/form-data; boundary=----geckoformboundarya7c718f364e3aa5395cc0d36fe400ac 2476843
2025-06-13 17:00:27.271 +03:30 [INF] CORS policy execution successful.
2025-06-13 17:00:27.277 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-13 17:00:27.298 +03:30 [INF] Route matched with {action = "_FileUpload", controller = "Database"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] _FileUpload(System.String, Microsoft.AspNetCore.Http.IFormFile) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-13 17:00:27.598 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 17:00:28.429 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-13 17:00:28.439 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 836.8639ms.
2025-06-13 17:00:28.448 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType2`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-13 17:00:28.469 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) in 1161.3523ms
2025-06-13 17:00:28.473 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-13 17:00:28.477 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Database/_FileUpload - 200 null application/json; charset=utf-8 1236.7203ms
2025-06-13 17:00:28.483 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=37940904 - null null
2025-06-13 17:00:28.489 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-13 17:00:28.491 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-13 17:00:28.532 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 17:00:28.540 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-13 17:00:28.610 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 71.9261ms.
2025-06-13 17:00:28.616 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-13 17:00:28.621 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 5.2619ms.
2025-06-13 17:00:28.624 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 129.3232ms
2025-06-13 17:00:28.628 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-13 17:00:28.632 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=37940904 - 200 null text/html; charset=utf-8 149.4146ms
2025-06-13 17:00:54.873 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting - null null
2025-06-13 17:00:54.937 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-13 17:00:54.940 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-13 17:00:55.004 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-13 17:00:55.009 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-13 17:00:55.016 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 8.4189ms.
2025-06-13 17:00:55.019 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-13 17:00:55.073 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 54.4194ms.
2025-06-13 17:00:55.076 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 129.7751ms
2025-06-13 17:00:55.079 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-13 17:00:55.083 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting - 200 null text/html; charset=utf-8 210.5409ms
2025-06-13 17:00:55.111 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=Nmb9k2AhsnpOv4cI_Fjp8KgGhTAxQTOTwjkpS20OWjE - null null
2025-06-13 17:00:55.118 +03:30 [INF] The file /js/site.js was not modified
2025-06-13 17:00:55.119 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=Nmb9k2AhsnpOv4cI_Fjp8KgGhTAxQTOTwjkpS20OWjE - 304 null text/javascript 8.7438ms
2025-06-13 17:00:55.122 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-13 17:00:55.122 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 17:00:55.135 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 12.3923ms
2025-06-13 17:00:55.152 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 30.1893ms
