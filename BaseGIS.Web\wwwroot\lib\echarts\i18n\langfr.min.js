!function(e,t){"function"==typeof define&&define.amd?define(["exports","echarts"],t):"object"==typeof exports&&"string"!=typeof exports.nodeName?t(0,require("echarts/lib/echarts")):t(0,e.echarts)}(this,function(e,t){t.registerLocale("FR",{time:{month:["Janvier","Février","Mars","Avril","Mai","<PERSON><PERSON>","Juillet","Août","Septembre","Octobre","Novembre","Décembre"],monthAbbr:["Jan","Fév","Mars","Avr","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>û<PERSON>","Sept","Oct","Nov","Déc"],dayOfWeek:["Dimanche","Lundi","Mar<PERSON>","Mercredi","<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>"],dayOfWeekAbbr:["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","V<PERSON>","<PERSON>"]},legend:{selector:{all:"Tout",inverse:"Inverse"}},toolbox:{brush:{title:{rect:"Sélection rectangulaire",polygon:"Sélection au lasso",lineX:"Sélectionner horizontalement",lineY:"Sélectionner verticalement",keep:"Garder la sélection",clear:"Effacer la sélection"}},dataView:{title:"Visualisation des données",lang:["Visualisation des données","Fermer","Rafraîchir"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Remise à zéro"}},magicType:{title:{line:"Changer pour Ligne",bar:"Changer pour Histogramme",stack:"Superposition",tiled:"Tuile"}},restore:{title:"Restaurer"},saveAsImage:{title:"Sauvegarder l'image",lang:["Clic droit pour sauvegarder l'image"]}},series:{typeNames:{pie:"Camembert",bar:"Histogramme",line:"Ligne",scatter:"Nuage de points",effectScatter:"Nuage de points stylisé",radar:"Radar",tree:"Arbre",treemap:"Treemap",boxplot:"Boîte à moustaches",candlestick:"Chandelier",k:"Linéaire K",heatmap:"Carte de fréquentation",map:"Carte",parallel:"Données parallèles",lines:"Lignes",graph:"Graphe",sankey:"Sankey",funnel:"Entonnoir",gauge:"Jauge",pictorialBar:"Barres à images",themeRiver:"Stream Graph",sunburst:"Sunburst"}},aria:{general:{withTitle:'Cette carte est intitulée "{title}"',withoutTitle:"C'est une carte"},series:{single:{prefix:"",withName:" Avec le type de {seriesType} qui s'appelle {seriesName}.",withoutName:" Avec le type de {seriesType}."},multiple:{prefix:" Elle comprend {seriesCount} séries.",withName:" La série {seriesId} représente {seriesName} de {seriesType}.",withoutName:" La série {seriesId} est un/une {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"Les données sont: ",partialData:"Les premiers {displayCnt} éléments sont : ",withName:"Les données pour {name} sont {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}})});