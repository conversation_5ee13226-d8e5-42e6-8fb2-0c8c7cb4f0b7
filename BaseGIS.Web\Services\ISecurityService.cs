using System.Text.RegularExpressions;
using System.Web;

namespace BaseGIS.Web.Services
{
    /// <summary>
    /// سرویس امنیتی
    /// </summary>
    public interface ISecurityService
    {
        /// <summary>
        /// اعتبارسنجی و پاکسازی ورودی
        /// </summary>
        string SanitizeInput(string input);

        /// <summary>
        /// محافظت از SQL Injection
        /// </summary>
        bool ValidateSqlInput(string input);

        /// <summary>
        /// محافظت از XSS
        /// </summary>
        string PreventXss(string input);

        /// <summary>
        /// اعتبارسنجی پارامترهای SQL
        /// </summary>
        bool ValidateSqlParameters(Dictionary<string, string> parameters);

        /// <summary>
        /// رمزگذاری داده‌های حساس
        /// </summary>
        string EncryptSensitiveData(string data);

        /// <summary>
        /// رمزگشایی داده‌های حساس
        /// </summary>
        string DecryptSensitiveData(string encryptedData);

        /// <summary>
        /// تولید Token امن
        /// </summary>
        string GenerateSecureToken();

        /// <summary>
        /// اعتبارسنجی دسترسی کاربر
        /// </summary>
        Task<bool> ValidateUserAccessAsync(string userId, string resource, string action);

        /// <summary>
        /// ثبت رویداد امنیتی
        /// </summary>
        Task LogSecurityEventAsync(SecurityEvent securityEvent);
    }

    /// <summary>
    /// پیاده‌سازی سرویس امنیتی
    /// </summary>
    public class SecurityService : ISecurityService
    {
        private readonly ILogger<SecurityService> _logger;
        private readonly IConfiguration _configuration;

        // SQL Injection patterns
        private static readonly string[] SqlInjectionPatterns = {
            @"(\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE){0,1}|INSERT( +INTO){0,1}|MERGE|SELECT|UPDATE|UNION( +ALL){0,1})\b)",
            @"(\b(AND|OR)\b.{1,6}?(=|>|<|\!|<=|>=))",
            @"(\b(CHAR|NCHAR|VARCHAR|NVARCHAR|ALTER|BEGIN|CAST|CREATE|CURSOR|DECLARE|DELETE|DROP|END|EXEC|EXECUTE|FETCH|INSERT|KILL|SELECT|SYS|SYSOBJECTS|SYSCOLUMNS|TABLE|UPDATE)\b)",
            @"(\%27)|(\')|(\-\-)|(\%23)|(#)",
            @"((\%3D)|(=))[^\n]*((\%27)|(\')|(\-\-)|(\%3B)|(;))",
            @"(\w*((\%27)|(\'))((\%6F)|o|(\%4F))((\%72)|r|(\%52)))",
            @"((\%27)|(\'))union",
            @"exec(\s|\+)+(s|x)p\w+",
            @"UNION(?:\s+ALL)?\s+SELECT",
            @"1\s*=\s*1",
            @"1\s*=\s*0"
        };

        // XSS patterns
        private static readonly string[] XssPatterns = {
            @"<script[^>]*>.*?</script>",
            @"javascript:",
            @"vbscript:",
            @"onload\s*=",
            @"onerror\s*=",
            @"onclick\s*=",
            @"onmouseover\s*=",
            @"<iframe[^>]*>.*?</iframe>",
            @"<object[^>]*>.*?</object>",
            @"<embed[^>]*>.*?</embed>",
            @"<link[^>]*>",
            @"<meta[^>]*>",
            @"expression\s*\(",
            @"url\s*\(",
            @"@import"
        };

        public SecurityService(ILogger<SecurityService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// اعتبارسنجی و پاکسازی ورودی
        /// </summary>
        public string SanitizeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            try
            {
                // Remove null characters
                input = input.Replace("\0", string.Empty);

                // Trim whitespace
                input = input.Trim();

                // Limit length
                if (input.Length > 4000)
                {
                    input = input.Substring(0, 4000);
                    _logger.LogWarning("Input truncated due to length: {Length}", input.Length);
                }

                // HTML encode
                input = HttpUtility.HtmlEncode(input);

                return input;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sanitizing input: {Input}", input);
                return string.Empty;
            }
        }

        /// <summary>
        /// محافظت از SQL Injection
        /// </summary>
        public bool ValidateSqlInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return true;

            try
            {
                foreach (var pattern in SqlInjectionPatterns)
                {
                    if (Regex.IsMatch(input, pattern, RegexOptions.IgnoreCase | RegexOptions.Multiline))
                    {
                        _logger.LogWarning("SQL Injection attempt detected: {Input}", input);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating SQL input: {Input}", input);
                return false;
            }
        }

        /// <summary>
        /// محافظت از XSS
        /// </summary>
        public string PreventXss(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            try
            {
                var sanitized = input;

                foreach (var pattern in XssPatterns)
                {
                    sanitized = Regex.Replace(sanitized, pattern, string.Empty, RegexOptions.IgnoreCase | RegexOptions.Multiline);
                }

                // Additional XSS prevention
                sanitized = sanitized.Replace("<", "&lt;")
                                   .Replace(">", "&gt;")
                                   .Replace("\"", "&quot;")
                                   .Replace("'", "&#x27;")
                                   .Replace("/", "&#x2F;");

                return sanitized;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error preventing XSS in input: {Input}", input);
                return HttpUtility.HtmlEncode(input);
            }
        }

        /// <summary>
        /// اعتبارسنجی پارامترهای SQL
        /// </summary>
        public bool ValidateSqlParameters(Dictionary<string, string> parameters)
        {
            try
            {
                foreach (var parameter in parameters)
                {
                    if (!ValidateSqlInput(parameter.Value))
                    {
                        _logger.LogWarning("Invalid SQL parameter detected: {Key}={Value}", parameter.Key, parameter.Value);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating SQL parameters");
                return false;
            }
        }

        /// <summary>
        /// رمزگذاری داده‌های حساس
        /// </summary>
        public string EncryptSensitiveData(string data)
        {
            if (string.IsNullOrEmpty(data))
                return string.Empty;

            try
            {
                // Simple Base64 encoding for demo - use proper encryption in production
                var bytes = System.Text.Encoding.UTF8.GetBytes(data);
                return Convert.ToBase64String(bytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error encrypting sensitive data");
                return string.Empty;
            }
        }

        /// <summary>
        /// رمزگشایی داده‌های حساس
        /// </summary>
        public string DecryptSensitiveData(string encryptedData)
        {
            if (string.IsNullOrEmpty(encryptedData))
                return string.Empty;

            try
            {
                // Simple Base64 decoding for demo - use proper decryption in production
                var bytes = Convert.FromBase64String(encryptedData);
                return System.Text.Encoding.UTF8.GetString(bytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error decrypting sensitive data");
                return string.Empty;
            }
        }

        /// <summary>
        /// تولید Token امن
        /// </summary>
        public string GenerateSecureToken()
        {
            try
            {
                var bytes = new byte[32];
                using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
                {
                    rng.GetBytes(bytes);
                }
                return Convert.ToBase64String(bytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating secure token");
                return Guid.NewGuid().ToString();
            }
        }

        /// <summary>
        /// اعتبارسنجی دسترسی کاربر
        /// </summary>
        public async Task<bool> ValidateUserAccessAsync(string userId, string resource, string action)
        {
            try
            {
                // Implement your access control logic here
                // This is a simplified example
                
                if (string.IsNullOrEmpty(userId))
                    return false;

                // Log access attempt
                await LogSecurityEventAsync(new SecurityEvent
                {
                    UserId = userId,
                    EventType = SecurityEventType.AccessAttempt,
                    Resource = resource,
                    Action = action,
                    Timestamp = DateTime.UtcNow,
                    Success = true
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating user access for user {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// ثبت رویداد امنیتی
        /// </summary>
        public async Task LogSecurityEventAsync(SecurityEvent securityEvent)
        {
            try
            {
                _logger.LogInformation("Security Event: {EventType} by user {UserId} on resource {Resource} - Action: {Action} - Success: {Success}",
                    securityEvent.EventType,
                    securityEvent.UserId,
                    securityEvent.Resource,
                    securityEvent.Action,
                    securityEvent.Success);

                // Store in database or security log system
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging security event");
            }
        }
    }

    /// <summary>
    /// رویداد امنیتی
    /// </summary>
    public class SecurityEvent
    {
        public string UserId { get; set; } = string.Empty;
        public SecurityEventType EventType { get; set; }
        public string Resource { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public bool Success { get; set; }
        public string? Details { get; set; }
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
    }

    /// <summary>
    /// نوع رویداد امنیتی
    /// </summary>
    public enum SecurityEventType
    {
        Login,
        Logout,
        AccessAttempt,
        AccessDenied,
        DataAccess,
        DataModification,
        SqlInjectionAttempt,
        XssAttempt,
        SuspiciousActivity
    }

    /// <summary>
    /// Middleware برای امنیت
    /// </summary>
    public class SecurityMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ISecurityService _securityService;
        private readonly ILogger<SecurityMiddleware> _logger;

        public SecurityMiddleware(RequestDelegate next, ISecurityService securityService, ILogger<SecurityMiddleware> logger)
        {
            _next = next;
            _securityService = securityService;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                // Validate request parameters
                if (context.Request.HasFormContentType)
                {
                    var form = await context.Request.ReadFormAsync();
                    var parameters = form.ToDictionary(x => x.Key, x => x.Value.ToString());
                    
                    if (!_securityService.ValidateSqlParameters(parameters))
                    {
                        context.Response.StatusCode = 400;
                        await context.Response.WriteAsync("Invalid request parameters");
                        return;
                    }
                }

                // Add security headers
                context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
                context.Response.Headers.Add("X-Frame-Options", "DENY");
                context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");
                context.Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");

                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in security middleware");
                await _next(context);
            }
        }
    }
}
