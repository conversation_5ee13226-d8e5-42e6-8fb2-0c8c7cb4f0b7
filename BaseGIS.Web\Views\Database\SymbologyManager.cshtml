﻿@using BaseGIS.Core.Entities
@{
    List<TableInfo> tblInfo = Model;
    var GroupList = tblInfo.Select(x => x.GroupInfo).Distinct().ToList();
    string idTbl = Context.Request.Query["id"];
}

@section Styles {
    <link href="/lib/jqueryui/themes/base/jquery-ui.min.css" rel="stylesheet" />
    <link href="~/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css" rel="stylesheet">
    <style>
        .fancytree-container {
            border: none;
            background: transparent;
        }

        .fancytree-node {
            border: none;
        }

        .fancytree-title {
            color: #333;
            text-decoration: none;
        }

        .fancytree-title:hover {
            background-color: #f8f9fa;
            text-decoration: none;
        }

        .fancytree-selected .fancytree-title {
            background-color: #007bff;
            color: white;
            border-radius: 4px;
        }

        .fancytree-active .fancytree-title {
            background-color: #0056b3;
            color: var(--secondary-color);
        }

        .txt-color-red { color: #dc3545; }
        .txt-color-green { color: #28a745; }
        .txt-color-blue { color: #007bff; }
        .txt-color-yellow { color: #ffc107; }

        .fancytree-title a {
            color: inherit;
            text-decoration: none;
            padding: 2px;
        }

        .fancytree-title a:hover {
            color: inherit;
            text-decoration: none;
        }

        .fancytree-node .fancytree-title {
            padding: 2px 4px;
            border-radius: 3px;
        }

        .fancytree-folder .fancytree-title {
            font-weight: 500;
        }

        .fancytree-node:hover .fancytree-title {
            background-color: #e9ecef;
        }
    </style>
}

<!-- MAIN CONTENT -->
<div>
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6"><h5 class="mb-0">مدیریت نماد لایه</h5></div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="#">مدیریت داده</a></li>
                        <li class="breadcrumb-item active" aria-current="page">مدیریت نماد لایه</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->
    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div id="_Symbol_LayerList" class="col-sm-12 col-md-12 col-lg-4">
                    <div class="card border-danger mb-3 samanFont" id="wid-id-51">
                        <div class="card-header bg-danger text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <h5 class="card-title samanFont mb-0">فهرست لایه ها</h5>
                                </div>
                            </div>
                        </div>
                        <!-- Content area -->
                        <div class="collapse show" id="layerListContent">
                            <div class="card-body p-0">
                                <div class="p-2 border-bottom">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fa fa-search"></i></span>
                                        <input class="form-control" id="FilterTree_Symbol" placeholder="جستجو" type="text">
                                    </div>
                                </div>
                                <div class="custom-scroll tree" id="treeLayer" style="height: 350px; overflow-y: auto;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="_Layer_Symbols" class="col-sm-12 col-md-12 col-lg-8">
                    <!-- محتوای اولیه یا نشانگر لودینگ -->
                    <div class="loading-spinner" style="display: none; text-align: center;">
                        <i class="fa fa-spinner fa-spin"></i> در حال بارگذاری...
                    </div>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</div>
<!-- END MAIN CONTENT -->

@section Scripts {
    <script src="~/lib/jqueryui/jquery-ui.min.js"></script>
    <script src="~/lib/jquery.fancytree/jquery.fancytree-all.min.js"></script>
    <script>
        var idTbl = '@idTbl';
        $(document).ready(function () {
            initializeFancytree();
            loadLayerSymbols(idTbl);
        });

        function initializeFancytree() {
            $("#treeLayer").fancytree({
                extensions: ["filter"],
                source: [
                    {
                        title: "لایه‌ها",
                        key: "root",
                        folder: true,
                        expanded: true,
                        children: [
@foreach (var group in GroupList)
{
    var list = tblInfo.Where(a => a.GroupInfo == group).ToList();
    if (list.Any())
    {
        <text>
                            {
                                title: "@(group != null ? group.AliasName : "بدون گروه‌بندی")",
                                key: "group_@(group?.Id ?? 0)",
                                folder: true,
                                children: [
            @foreach (var item in list)
            {
                string symbol = "fa-table txt-color-yellow";
                if (item.DatasetType.ToLower() == "point") { symbol = "fa-map-marker txt-color-red"; }
                        else if (item.DatasetType.ToLower() == "polyline") { symbol = "fa-bolt txt-color-green"; }
                else if (item.DatasetType.ToLower() == "polygon") { symbol = "fa-square txt-color-blue"; }

                <text>
                                    {
                                        title: "<span><i class='fa @symbol'></i> <a href='?id=@item.Id'>@item.AliasName</a></span>",
                                        key: "<EMAIL>",
                                        selected: "@idTbl" === "@item.Id"
                                    },
                </text>
            }
                                ]
                            },
        </text>
    }
}
                        ]
                    }
                ],
                filter: {
                    autoApply: true,
                    autoExpand: true,
                    counter: false,
                    fuzzy: false,
                    hideExpandedCounter: true,
                    hideExpanders: false,
                    highlight: true,
                    leavesOnly: false,
                    nodata: "هیچ نتیجه‌ای یافت نشد.",
                    mode: "hide"
                },
                escapeTitles: false, // اجازه رندر HTML در عنوان‌ها
                rtl: true,
                activate: function (event, data) {
                    // اگر روی لایه کلیک شد، سمبولوژی آن را بارگذاری کن
                    if (data.node.key.startsWith('layer_')) {
                        var layerId = data.node.key.replace('layer_', '');
                        loadLayerSymbols(layerId);

                        // به‌روزرسانی URL بدون رفرش صفحه
                        var newUrl = window.location.pathname + '?id=' + layerId;
                        window.history.pushState({ path: newUrl }, '', newUrl);

                        // به‌روزرسانی متغیر idTbl
                        idTbl = layerId;
                    }
                },
                click: function (event, data) {
                    // جلوگیری از عمل پیش‌فرض برای لینک‌ها
                    if ($(event.target).is('a')) {
                        event.preventDefault();
                        return false;
                    }
                }
            });

            // جستجو
            $("#FilterTree_Symbol").on("keyup", function () {
                var filter = $(this).val();
                $("#treeLayer").fancytree("getTree").filterNodes(filter, { autoExpand: true });
            });

            // فعال کردن لایه انتخاب شده (اگر وجود دارد)
            if (idTbl) {
                var tree = $("#treeLayer").fancytree("getTree");
                var node = tree.getNodeByKey("layer_" + idTbl);
                if (node) {
                    node.setActive(true);
                    node.setSelected(true);
                    node.makeVisible();
                }
            }
        }

        function loadLayerSymbols(id) {
            if (!id) {
                $("#_Layer_Symbols").html("<p>لطفاً یک لایه انتخاب کنید.</p>");
                return;
            }
            $("#_Layer_Symbols .loading-spinner").show();
            $.ajax({
                url: '@Url.Action("_Layer_Symbols", "Database")',
                type: 'GET',
                data: { id: id },
                success: function (data) {
                    $("#_Layer_Symbols").html(data);
                },
                error: function (xhr, status, error) {
                    $("#_Layer_Symbols").html("<p class='text-danger'>خطا در بارگذاری اطلاعات: " + error + "</p>");
                },
                complete: function () {
                    $("#_Layer_Symbols .loading-spinner").hide();
                }
            });
        }
    </script>
}




