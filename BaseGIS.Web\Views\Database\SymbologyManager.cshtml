@using BaseGIS.Core.Entities
@{
    List<TableInfo> tblInfo = Model;
    var GroupList = tblInfo.Select(x => x.GroupInfo).Distinct().ToList();
    string idTbl = Context.Request.Query["id"];
}

<!-- MAIN CONTENT -->
<div id="content" class="samanFont" style="padding-left:40px;">

    <!-- widget grid -->
    <section id="widget-grid" class="">
        <!-- row -->
        <div class="row">
            <div id="_Symbol_LayerList" class="col-sm-12 col-md-12 col-lg-4">
                <div class="card border-danger mb-3 samanFont" id="wid-id-51">
                <div class="card-header bg-danger text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="card-title samanFont mb-0">فهرست لایه ها</h5>
                        </div>
                    </div>
                </div>

                <!-- Content area -->
                <div class="collapse show" id="layerListContent">
                    <div class="card-body p-0">
                        <div class="p-2 border-bottom">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fa fa-search"></i></span>
                                <input class="form-control" id="FilterTree_Symbol" placeholder="جستجو" type="text">
                            </div>
                        </div>

                        <div class="custom-scroll tree" id="treeLayer" style="height: 350px; overflow-y: auto;">
                            <ul class="list-unstyled m-0 p-0">
                                <li class="p-2">
                                    <div class="tree-item">
                                        <span class="label label-success">
                                            <i class="fa fa-minus-circle me-1"></i> لایه ها
                                        </span>
                                        <ul class="list-unstyled ms-3 mt-2">
                                            @if (GroupList.Count() > 0)
                                            {
                                                foreach (var group in GroupList)
                                                {
                                                    var list = tblInfo.Where(a => a.GroupInfo == group);

                                                    if (list.Count() > 0)
                                                    {
                                                        <li class="p-1">
                                                            <div class="tree-item">
                                                                @if (group != null)
                                                                {
                                                                    <span class="label label-success">
                                                                        <i class="fa fa-lg fa-plus-circle"></i> @group.AliasName
                                                                    </span>
                                                                }
                                                                else
                                                                {
                                                                    <span class="label label-success">
                                                                        <i class="fa fa-lg fa-plus-circle"></i> بدون گروه بندی
                                                                    </span>
                                                                }
                                                                <ul class="list-unstyled ms-3 mt-2">
                                                                    @foreach (var item in list)
                                                                    {
                                                                        string bgColor = "";
                                                                        if (idTbl == item.Id.ToString())
                                                                        {
                                                                            bgColor = "bg-info";
                                                                        }
                                                                        string symbol = "fa-table text-warning";
                                                                        if (item.DatasetType.ToLower() == "point")
                                                                        { symbol = "fa-map-marker text-danger"; }
                                                                        else if (item.DatasetType.ToLower() == "polyline")
                                                                        { symbol = "fa-flash text-success"; }
                                                                        else if (item.DatasetType.ToLower() == "polygon")
                                                                        { symbol = "fa-square-o text-primary"; }
                                                                        <li class="p-1 d-none layer-item">
                                                                            <span class="d-flex align-items-center @bgColor rounded px-2">
                                                                                <i class="fa @symbol me-1"></i>
                                                                                <a href="?id=@item.Id" class="text-decoration-none text-dark stretched-link">@item.AliasName</a>
                                                                            </span>
                                                                        </li>
                                                                    }
                                                                </ul>
                                                            </div>
                                                        </li>
                                                    }
                                                }
                                            }
                                        </ul>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            </div>
            <div id="_Layer_Symbols" class="col-sm-12 col-md-12 col-lg-8">
                <!-- محتوای اولیه یا نشانگر لودینگ -->
                <div class="loading-spinner" style="display: none; text-align: center;">
                    <i class="fa fa-spinner fa-spin"></i> در حال بارگذاری...
                </div>
            </div>
        </div>
    </section>
    <!-- end widget grid -->
</div>
<!-- END MAIN CONTENT -->

@section Scripts {
    <script>
        var idTbl = '@idTbl';
        $(document).ready(function () {
           initTreeView();

            // Initialize collapse functionality
            var collapseElementList = [].slice.call(document.querySelectorAll('[data-bs-toggle="collapse"]'));
            collapseElementList.map(function (collapseEl) {
                collapseEl.addEventListener('click', function() {
                    var icon = this.querySelector('i');
                    if (icon) {
                        icon.classList.toggle('fa-minus');
                        icon.classList.toggle('fa-plus');
                    }
                });
            });
            
            loadLayerSymbols(idTbl);
        });


        function initTreeView() {
            // Find all tree items with children
            $('.tree-item').each(function() {
                var $this = $(this);
                var $children = $this.find('> ul > li');

                if ($children.length > 0) {
                    $this.find('> span').first().css('cursor', 'pointer').attr('title', 'Collapse this branch');

                    // Add click event to toggle children visibility
                    $this.find('> span').first().on('click', function(e) {
                        var $icon = $(this).find('> i');

                        if ($children.first().hasClass('d-none')) {
                            // Show children
                            $children.removeClass('d-none');
                            $icon.removeClass('fa-plus-circle').addClass('fa-minus-circle');
                            $(this).attr('title', 'Collapse this branch');
                        } else {
                            // Hide children
                            $children.addClass('d-none');
                            $icon.removeClass('fa-minus-circle').addClass('fa-plus-circle');
                            $(this).attr('title', 'Expand this branch');
                        }

                        e.stopPropagation();
                    });
                }
            });

            // Show first level by default
            $('.tree-item > ul > li.layer-item').removeClass('d-none');
        }

        // Search filter functionality
        $("#FilterTree_Symbol").on('keyup', function() {
            var filter = $(this).val().toLowerCase();

            $("#treeLayer ul li").each(function() {
                var text = $(this).text().toLowerCase();

                if (text.indexOf(filter) > -1) {
                    // Show this item and its parent
                    $(this).removeClass('d-none');
                    $(this).parents('li').removeClass('d-none');

                    // Show the branch by toggling the icon if needed
                    var $parentSpan = $(this).closest('.tree-item').find('> span');
                    var $icon = $parentSpan.find('> i');

                    if ($icon.hasClass('fa-plus-circle')) {
                        $icon.removeClass('fa-plus-circle').addClass('fa-minus-circle');
                    }
                } else {
                    // If it doesn't have children matching the filter, hide it
                    if ($(this).find('li:visible').length === 0) {
                        $(this).addClass('d-none');
                    }
                }
            });
        });
        function loadLayerSymbols(id) {
            if (!id) {
                $("#_Layer_Symbols").html("<p>لطفاً یک لایه انتخاب کنید.</p>");
                return;
            }
            $("#_Layer_Symbols .loading-spinner").show();
            $.ajax({
                url: '@Url.Action("_Layer_Symbols", "Database")',
                type: 'GET',
                data: { id: id },
                success: function (data) {
                    $("#_Layer_Symbols").html(data);
                },
                error: function (xhr, status, error) {
                    $("#_Layer_Symbols").html("<p class='text-danger'>خطا در بارگذاری اطلاعات: " + error + "</p>");
                },
                complete: function () {
                    $("#_Layer_Symbols .loading-spinner").hide();
                }
            });
        }
    </script>
}




