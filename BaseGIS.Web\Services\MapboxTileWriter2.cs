﻿using NetTopologySuite.Features;
using NetTopologySuite.Geometries;
using NetTopologySuite.IO.VectorTiles;
using NetTopologySuite.IO.VectorTiles.Mapbox;
using ProtoBuf;

namespace BaseGIS.Web.Services
{
    /// <summary>
    /// Static class for writing VectorTiles to a stream or file.
    /// This class extends the functionality of NetTopologySuite.IO.VectorTiles
    /// to handle geometry transformation (clipping and scaling to extent)
    /// for Mapbox Vector Tile specification.
    /// </summary>
    public static class MapboxTileWriter2
    {
        public const uint DefaultMinLinealExtent = 1u;
        public const uint DefaultMinPolygonalExtent = 2u;
        public const string DefaultIdAttributeName = "id";

        [Obsolete("Use overload that can specify minLineal- and minPolygonalExtent")]
        public static void Write(this VectorTileTree tree, string path, uint extent = 4096u)
        {
            tree.Write(path, 1u, 2u, extent);
        }

        public static void Write(this VectorTileTree tree, string path, uint minLinealExtent, uint minPolygonalExtent, uint extent = 4096u, string idAttributeName = "id")
        {
            GetTiles().Write(path, minLinealExtent, minPolygonalExtent, extent, idAttributeName);
            IEnumerable<VectorTile> GetTiles()
            {
                foreach (ulong item in tree)
                {
                    yield return tree[item];
                }
            }
        }

        [Obsolete("Use overload that can specify minLineal- and minPolygonalExtent")]
        public static void Write(this IEnumerable<VectorTile> vectorTiles, string path, uint extent = 4096u)
        {
            vectorTiles.Write(path, 1u, 2u, extent);
        }

        public static void Write(this IEnumerable<VectorTile> vectorTiles, string path, uint minLinealExtent, uint minPolygonalExtent, uint extent = 4096u, string idAttributeName = "id")
        {
            foreach (VectorTile vectorTile in vectorTiles)
            {
                NetTopologySuite.IO.VectorTiles.Tiles.Tile tile = new
                NetTopologySuite.IO.VectorTiles.Tiles.Tile(vectorTile.TileId);
                string text = Path.Combine(path, tile.Zoom.ToString());
                if (!Directory.Exists(text))
                {
                    Directory.CreateDirectory(text);
                }
                string text2 = Path.Combine(text, tile.X.ToString());
                if (!Directory.Exists(text2))
                {
                    Directory.CreateDirectory(text2);
                }
                using FileStream stream = File.Open(Path.Combine(text2, $"{tile.Y}.mvt"),
                FileMode.Create);
                vectorTile.Write(stream, minLinealExtent, minPolygonalExtent, extent,
                idAttributeName);
            }
        }

        [Obsolete("Use overload that can specify minLineal- and minPolygonalExtent")]
        public static void Write(this VectorTile vectorTile, Stream stream, uint extent = 4096u,
        string idAttributeName = "id")
        {
            vectorTile.Write(stream, 1u, 2u, extent, idAttributeName);
        }

        public static void Write(this VectorTile vectorTile, Stream stream, uint minLinealExtent,
        uint minPolygonalExtent, uint extent = 4096u, string idAttributeName =
        "id")
        {
            if (minPolygonalExtent < 1)
            {
                minPolygonalExtent = 1u;
            }
            NetTopologySuite.IO.VectorTiles.Tiles.Tile tileIdInfo = new
            NetTopologySuite.IO.VectorTiles.Tiles.Tile(vectorTile.TileId); // Renamed for clarity

            // TileGeometryTransform به روز شده برای انجام صحیح مقیاس‌بندی
            TileGeometryTransform tgt = new TileGeometryTransform(tileIdInfo, extent);

            Tile tileToWrite = new Tile();

            foreach (Layer layer2 in vectorTile.Layers)
            {
                Tile.Layer layer = new Tile.Layer
                {
                    Version = 2u,
                    Name = layer2.Name,
                    Extent = extent
                };
                Dictionary<string, uint> dictionary = new Dictionary<string, uint>();
                Dictionary<Tile.Value, uint> dictionary2 = new
                Dictionary<Tile.Value, uint>();

                foreach (IFeature feature2 in layer2.Features)
                {
                    Tile.Feature feature = new Tile.Feature();
                    if (feature2.Geometry.IsEmpty)
                    {
                        continue;
                    }

                    Geometry geometry = feature2.Geometry;

                    // currentX و currentY باید برای هر فیچر جدید صفر شوند.
                    // این متغیرها برای محاسبه دلتاها در داخل هر هندسه استفاده می شوند.
                    // *** این خطوط تنها جایی هستند که currentX و currentY باید ریست شوند. ***
                    int currentX = 0;
                    int currentY = 0;

                    if (!(geometry is IPuntal puntal))
                    {
                        if (!(geometry is ILineal lineal))
                        {
                            if (geometry is IPolygonal polygonal)
                            {
                                feature.Type = Tile.GeomType.Polygon;
                                EncodeTo(feature.Geometry, polygonal, minPolygonalExtent, tgt, ref
                                currentX, ref currentY);
                            }
                            else
                            {
                                feature.Type = Tile.GeomType.Unknown;
                            }
                        }
                        else
                        {
                            feature.Type = Tile.GeomType.LineString;
                            EncodeTo(feature.Geometry, lineal, minLinealExtent, tgt, ref currentX,
                            ref currentY);
                        }
                    }
                    else
                    {
                        feature.Type = Tile.GeomType.Point;
                        EncodeTo(feature.Geometry, puntal, tgt, ref currentX, ref currentY);
                    }

                    if (feature.Geometry.Count == 0)
                    {
                        continue;
                    }
                    AddAttributes(feature.Tags, dictionary, dictionary2, feature2.Attributes);
                    object optionalValue =
                    feature2.Attributes.GetOptionalValue(idAttributeName);
                    if (optionalValue != null)
                    {
                        ulong result;
                        if (optionalValue is ulong id)
                        {
                            feature.Id = id;
                        }
                        else if (ulong.TryParse(optionalValue.ToString(), out result))
                        {
                            feature.Id = result;
                        }
                    }
                    layer.Features.Add(feature);
                }
                layer.Keys.AddRange(dictionary.Keys);
                layer.Values.AddRange(dictionary2.Keys);
                tileToWrite.Layers.Add(layer);
            }
            Serializer.Serialize(stream, tileToWrite);
        }

        private static void AddAttributes(List<uint> tags, Dictionary<string, uint> keys,
        Dictionary<Tile.Value, uint> values, IAttributesTable attributes)
        {
            if (attributes == null || attributes.Count == 0)
            {
                return;
            }
            string[] names = attributes.GetNames();
            object[] values2 = attributes.GetValues();

            uint nextKeyId = (uint)keys.Count;
            uint nextValueId = (uint)values.Count;

            for (int i = 0; i < names.Length; i++)
            {
                string text = names[i];
                if (!string.IsNullOrEmpty(text))
                {
                    Tile.Value value = ToTileValue(values2[i]);
                    if (value != null)
                    {
                        if (!keys.TryGetValue(text, out uint keyId))
                        {
                            keyId = nextKeyId++;
                            keys.Add(text, keyId);
                        }
                        tags.Add(keyId);

                        if (!values.TryGetValue(value, out uint valueId))
                        {
                            valueId = nextValueId++;
                            values.Add(value, valueId);
                        }
                        tags.Add(valueId);
                    }
                }
            }
        }

        private static Tile.Value ToTileValue(object value)
        {
            if (value is bool boolValue)
            {
                return new Tile.Value { BoolValue = boolValue };
            }
            if (value is sbyte b)
            {
                return new Tile.Value { IntValue = b };
            }
            if (value is short num)
            {
                return new Tile.Value { IntValue = num };
            }
            if (value is int num2)
            {
                return new Tile.Value { IntValue = num2 };
            }
            if (value is long intValue)
            {
                return new Tile.Value { IntValue = intValue };
            }
            if (value is byte b2)
            {
                return new Tile.Value { UintValue = b2 };
            }
            if (value is ushort num3)
            {
                return new Tile.Value { UintValue = num3 };
            }
            if (value is uint num4)
            {
                return new Tile.Value { UintValue = num4 };
            }
            if (value is ulong uintValue)
            {
                return new Tile.Value { UintValue = uintValue };
            }
            if (value is double doubleValue)
            {
                return new Tile.Value { DoubleValue = doubleValue };
            }
            if (value is float floatValue)
            {
                return new Tile.Value { FloatValue = floatValue };
            }
            if (value is string stringValue)
            {
                return new Tile.Value { StringValue = stringValue };
            }
            return null;
        }

        // --- متدهای EncodeTo با حذف ریست currentX/Y در ابتدای هر بخش هندسی ---

        private static void EncodeTo(List<uint> destination, IPuntal puntal,
        TileGeometryTransform tgt, ref int currentX, ref int currentY)
        {
            Geometry geometry = (Geometry)puntal;
            int count = destination.Count;
            destination.Add(0u); // Placeholder for command and count

            // *** خطوط currentX = 0; currentY = 0; در اینجا حذف شدند! ***

            for (int i = 0; i < geometry.NumGeometries; i++)
            {
                Point point = (Point)geometry.GetGeometryN(i);
                if (!point.IsEmpty)
                {
                    var (deltaX, deltaY) = tgt.Transform(point.CoordinateSequence, 0, ref
                    currentX, ref currentY);

                    destination.Add(GenerateParameterInteger(deltaX));
                    destination.Add(GenerateParameterInteger(deltaY));
                }
            }
            destination[count] = GenerateCommandInteger(MapboxCommandType.MoveTo,
            (destination.Count - count) / 2);
        }

        private static void EncodeTo(List<uint> destination, ILineal lineal, uint
        minLinealExtent, TileGeometryTransform tgt, ref int currentX, ref int currentY)
        {
            Geometry geometry = (Geometry)lineal;
            for (int i = 0; i < geometry.NumGeometries; i++)
            {
                LineString lineString = (LineString)geometry.GetGeometryN(i);
                // *** خطوط currentX = 0; currentY = 0; در اینجا حذف شدند! ***

                if (HasValidLength(tgt.ExtentInScaledPixel(lineString.EnvelopeInternal),
                minLinealExtent))
                {
                    EncodeTo(destination, lineString.CoordinateSequence, tgt, ref currentX,
                    ref currentY);
                }
            }
            bool HasValidLength((long x, long y) tpl, uint minExtent)
            {
                long absX = Math.Abs(tpl.x);
                long absY = Math.Abs(tpl.y);
                return absX >= minExtent || absY >= minExtent;
            }
        }

        private static void EncodeTo(List<uint> destination, IPolygonal polygonal, uint
        minPolygonalExtent, TileGeometryTransform tgt, ref int currentX, ref int
        currentY)
        {
            Geometry geometry = (Geometry)polygonal;
            if (!HasValidExtent(tgt.ExtentInScaledPixel(geometry.EnvelopeInternal),
            minPolygonalExtent))
            {
                return;
            }
            for (int i = 0; i < geometry.NumGeometries; i++)
            {
                Polygon polygon = (Polygon)geometry.GetGeometryN(i);
                // *** خطوط currentX = 0; currentY = 0; در اینجا حذف شدند! ***

                if (!HasValidExtent(tgt.ExtentInScaledPixel(polygon.ExteriorRing.EnvelopeInternal),
                minPolygonalExtent))
                {
                    continue;
                }
                EncodeTo(destination, polygon.ExteriorRing.CoordinateSequence, tgt, ref
                currentX, ref currentY, ring: true);
                LineString[] interiorRings = polygon.InteriorRings;
                foreach (LineString lineString in interiorRings)
                {
                    if (HasValidExtent(tgt.ExtentInScaledPixel(lineString.EnvelopeInternal),
                    minPolygonalExtent))
                    {
                        // *** خطوط currentX = 0; currentY = 0; در اینجا حذف شدند! ***
                        EncodeTo(destination, lineString.CoordinateSequence, tgt, ref currentX,
                        ref currentY, ring: true, ccw: true);
                    }
                }
            }
            bool HasValidExtent((long x, long y) tpl, uint minExtent)
            {
                long absX = Math.Abs(tpl.x);
                long absY = Math.Abs(tpl.y);
                return absX >= minExtent && absY >= minExtent;
            }
        }

        private static void EncodeTo(List<uint> destination, CoordinateSequence sequence,
        TileGeometryTransform tgt, ref int currentX, ref int currentY, bool ring =
        false, bool ccw = false)
        {
            int num = (ring ? (sequence.Count - 1) : sequence.Count);
            if (num == 0)
            {
                return;
            }

            // Note: currentX and currentY are passed by ref. They are reset to 0
            // at the start of each *feature* (in the main Write method for VectorTile).
            // For the first point of *this* path, the delta will be relative to (0,0) as
            // currentX/Y are 0 for the start of the feature. Subsequent points in the same path
            // will have deltas relative to the *last transformed point*.

            int count = destination.Count;
            destination.Add(GenerateCommandInteger(MapboxCommandType.MoveTo, 1));

            // Transform first point
            (int xDelta, int yDelta) = tgt.Transform(sequence, 0, ref currentX, ref currentY);
            destination.Add(GenerateParameterInteger(xDelta));
            destination.Add(GenerateParameterInteger(yDelta));

            int lineToCount = 0;
            destination.Add(GenerateCommandInteger(MapboxCommandType.LineTo,
            lineToCount));

            // Transform remaining points
            for (int i = 1; i < num; i++)
            {
                (xDelta, yDelta) = tgt.Transform(sequence, i, ref currentX, ref
                currentY);
                if (xDelta != 0 || yDelta != 0)
                {
                    destination.Add(GenerateParameterInteger(xDelta));
                    destination.Add(GenerateParameterInteger(yDelta));
                    lineToCount++;
                }
            }
            if (lineToCount > 0)
            {
                destination[count + 3] =
                GenerateCommandInteger(MapboxCommandType.LineTo, lineToCount);
            }
            if (ring)
            {
                if (destination.Count - count - 2 >= 6) // Ensure at least 3 points for a valid ring before ClosePath
                {
                    destination.Add(GenerateCommandInteger(MapboxCommandType.ClosePath, 1));
                }
                else
                {
                    destination.RemoveRange(count, destination.Count - count);
                }
            }
            else if (destination.Count - count - 2 < 2) // For LineStrings/Points, ensure at least one point (2 parameters)
            {
                destination.RemoveRange(count, destination.Count - count);
            }

            // If the geometry was removed due to being invalid, we don't need to do anything with
            // currentX/Y, as they would be reset for the next feature/path.
        }

        private static uint GenerateCommandInteger(MapboxCommandType command, int count)
        {
            return (uint)((int)command & 0x7) | (uint)(count << 3);
        }

        private static uint GenerateParameterInteger(int value)
        {
            // ZigZag encoding for signed integers
            return (uint)((value << 1) ^ (value >> 31));
        }
    }
}