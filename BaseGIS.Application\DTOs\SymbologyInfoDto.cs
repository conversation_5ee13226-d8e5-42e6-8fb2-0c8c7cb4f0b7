namespace BaseGIS.Application.DTOs
{
    public class SymbologyInfoDto
    {
        public int Id { get; set; }
        public string Type { get; set; } // Simple, Unique, Quantity
        public int TableInfoId { get; set; }
        public string Name { get; set; }
        public bool IsDefault { get; set; }
        public string Json { get; set; }
        public string FieldAlias { get; set; }
        public string FieldName { get; set; }
    }
}