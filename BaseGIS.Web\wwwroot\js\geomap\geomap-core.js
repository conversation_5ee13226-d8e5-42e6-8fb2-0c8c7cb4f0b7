/**
 * GeoMap Core Module
 * مدیریت اصلی نقشه جغرافیایی
 */

class GeoMapCore {
    constructor() {
        this.map = null;
        this.layerTree = null;
        this.selectedLayers = new Map();
        this.searchResults = new Map();
        this.currentExtent = null;
        this.isInitialized = false;
        
        this.init();
    }

    /**
     * مقداردهی اولیه
     */
    init() {
        this.initializeMap();
        this.initializeLayerTree();
        this.bindEvents();
        this.loadUserSettings();
        
        this.isInitialized = true;
        console.log('GeoMap Core initialized');
    }

    /**
     * مقداردهی نقشه
     */
    initializeMap() {
        // Initialize map container
        const mapContainer = document.getElementById('map-container');
        if (!mapContainer) {
            console.error('Map container not found');
            return;
        }

        // Map configuration would depend on your mapping library (OpenLayers, Leaflet, etc.)
        this.map = this.createMapInstance(mapContainer);
        
        // Set default extent
        this.setDefaultExtent();
    }

    /**
     * ایجاد نمونه نقشه
     */
    createMapInstance(container) {
        // This would be implemented based on your mapping library
        // Example for OpenLayers or Leaflet
        console.log('Creating map instance in container:', container);
        return null; // Placeholder
    }

    /**
     * مقداردهی درخت لایه‌ها
     */
    initializeLayerTree() {
        const treeContainer = document.getElementById('layer-tree-container');
        if (!treeContainer) {
            console.error('Layer tree container not found');
            return;
        }

        // Initialize layer tree (could use FancyTree or similar)
        this.layerTree = this.createLayerTree(treeContainer);
    }

    /**
     * ایجاد درخت لایه‌ها
     */
    createLayerTree(container) {
        // Implementation would depend on tree library
        console.log('Creating layer tree in container:', container);
        return null; // Placeholder
    }

    /**
     * اتصال رویدادها
     */
    bindEvents() {
        // Map events
        this.bindMapEvents();
        
        // Layer tree events
        this.bindLayerTreeEvents();
        
        // Toolbar events
        this.bindToolbarEvents();
        
        // Search events
        this.bindSearchEvents();
    }

    /**
     * اتصال رویدادهای نقشه
     */
    bindMapEvents() {
        if (!this.map) return;

        // Map click event
        this.map.on('click', (e) => {
            this.handleMapClick(e);
        });

        // Map extent change
        this.map.on('moveend', (e) => {
            this.handleExtentChange(e);
        });
    }

    /**
     * اتصال رویدادهای درخت لایه‌ها
     */
    bindLayerTreeEvents() {
        // Layer visibility toggle
        $(document).on('change', '.layer-checkbox', (e) => {
            this.toggleLayerVisibility(e.target);
        });

        // Layer properties
        $(document).on('click', '.layer-properties', (e) => {
            this.showLayerProperties(e.target.dataset.layerId);
        });

        // Layer symbology
        $(document).on('click', '.layer-symbology', (e) => {
            this.showLayerSymbology(e.target.dataset.layerId);
        });
    }

    /**
     * اتصال رویدادهای نوار ابزار
     */
    bindToolbarEvents() {
        // Search tools
        $('.toolbar-search').on('click', (e) => {
            this.openSearchTool(e.target.dataset.searchType);
        });

        // Drawing tools
        $('.toolbar-draw').on('click', (e) => {
            this.activateDrawingTool(e.target.dataset.drawType);
        });

        // Measurement tools
        $('.toolbar-measure').on('click', (e) => {
            this.activateMeasurementTool(e.target.dataset.measureType);
        });

        // Print tools
        $('.toolbar-print').on('click', () => {
            this.openPrintDialog();
        });

        // Import tools
        $('.toolbar-import').on('click', () => {
            this.openImportDialog();
        });
    }

    /**
     * اتصال رویدادهای جستجو
     */
    bindSearchEvents() {
        // Quick search
        $('#quick-search-input').on('keypress', (e) => {
            if (e.which === 13) {
                this.performQuickSearch(e.target.value);
            }
        });

        $('#quick-search-btn').on('click', () => {
            const searchTerm = $('#quick-search-input').val();
            this.performQuickSearch(searchTerm);
        });
    }

    /**
     * مدیریت کلیک روی نقشه
     */
    handleMapClick(event) {
        const coordinate = event.coordinate;
        console.log('Map clicked at:', coordinate);
        
        // Identify features at click location
        this.identifyFeatures(coordinate);
    }

    /**
     * مدیریت تغییر محدوده نقشه
     */
    handleExtentChange(event) {
        this.currentExtent = this.map.getView().calculateExtent();
        console.log('Map extent changed:', this.currentExtent);
    }

    /**
     * تغییر وضعیت نمایش لایه
     */
    toggleLayerVisibility(checkbox) {
        const layerId = checkbox.dataset.layerId;
        const isVisible = checkbox.checked;
        
        console.log(`Toggle layer ${layerId} visibility:`, isVisible);
        
        // Update layer visibility on map
        this.setLayerVisibility(layerId, isVisible);
        
        // Update selected layers
        if (isVisible) {
            this.selectedLayers.set(layerId, true);
        } else {
            this.selectedLayers.delete(layerId);
        }
    }

    /**
     * تنظیم نمایش لایه
     */
    setLayerVisibility(layerId, visible) {
        // Implementation would depend on mapping library
        console.log(`Setting layer ${layerId} visibility to:`, visible);
    }

    /**
     * نمایش خصوصیات لایه
     */
    async showLayerProperties(layerId) {
        try {
            const response = await fetch(`/GeoMap/Property?Layerid=${layerId}`);
            const html = await response.text();
            
            // Show in modal or sidebar
            this.showModal('خصوصیات لایه', html);
        } catch (error) {
            console.error('Error loading layer properties:', error);
            this.showError('خطا در بارگذاری خصوصیات لایه');
        }
    }

    /**
     * نمایش نمادشناسی لایه
     */
    showLayerSymbology(layerId) {
        console.log('Show symbology for layer:', layerId);
        // Implementation for symbology dialog
    }

    /**
     * باز کردن ابزار جستجو
     */
    async openSearchTool(searchType) {
        try {
            let url;
            switch (searchType) {
                case 'quick':
                    url = '/GeoMap/QuickSearch';
                    break;
                case 'spatial':
                    url = '/GeoMap/SpatialSearch';
                    break;
                case 'descriptive':
                    url = '/GeoMap/DescriptiveSearch';
                    break;
                default:
                    console.error('Unknown search type:', searchType);
                    return;
            }

            const response = await fetch(url);
            const html = await response.text();
            
            this.showModal(`جستجوی ${this.getSearchTypeLabel(searchType)}`, html);
        } catch (error) {
            console.error('Error loading search tool:', error);
            this.showError('خطا در بارگذاری ابزار جستجو');
        }
    }

    /**
     * دریافت برچسب نوع جستجو
     */
    getSearchTypeLabel(searchType) {
        const labels = {
            'quick': 'سریع',
            'spatial': 'مکانی',
            'descriptive': 'توصیفی'
        };
        return labels[searchType] || searchType;
    }

    /**
     * انجام جستجوی سریع
     */
    async performQuickSearch(searchTerm) {
        if (!searchTerm || searchTerm.trim() === '') {
            this.showWarning('لطفاً عبارت جستجو را وارد کنید');
            return;
        }

        try {
            this.showLoading('در حال جستجو...');

            const request = {
                searchTerm: searchTerm.trim(),
                maxResults: 50
            };

            const response = await fetch('/GeoMap/PerformQuickSearch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(request)
            });

            const result = await response.json();

            if (result.ok) {
                this.displaySearchResults(result.data, 'quick');
            } else {
                this.showError(result.msg);
            }
        } catch (error) {
            console.error('Error performing quick search:', error);
            this.showError('خطا در انجام جستجو');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * نمایش نتایج جستجو
     */
    displaySearchResults(results, searchType) {
        console.log(`Displaying ${searchType} search results:`, results);
        
        // Store results
        this.searchResults.set(searchType, results);
        
        // Clear previous results
        this.clearSearchResults();
        
        // Add results to map
        results.forEach(result => {
            this.addSearchResultToMap(result);
        });
        
        // Show results panel
        this.showSearchResultsPanel(results, searchType);
    }

    /**
     * پاک کردن نتایج جستجو
     */
    clearSearchResults() {
        // Remove search result layers from map
        console.log('Clearing search results');
    }

    /**
     * اضافه کردن نتیجه جستجو به نقشه
     */
    addSearchResultToMap(result) {
        if (result.geometry) {
            // Add geometry to map
            console.log('Adding search result to map:', result);
        }
    }

    /**
     * نمایش پنل نتایج جستجو
     */
    showSearchResultsPanel(results, searchType) {
        const panel = document.getElementById('search-results-panel');
        if (!panel) return;

        let html = `<h5>نتایج جستجوی ${this.getSearchTypeLabel(searchType)}</h5>`;
        html += '<div class="search-results-list">';
        
        results.forEach((result, index) => {
            html += `
                <div class="search-result-item" data-index="${index}">
                    <div class="result-title">${result.displayText}</div>
                    <div class="result-layer">${result.layerName}</div>
                </div>
            `;
        });
        
        html += '</div>';
        panel.innerHTML = html;
        panel.style.display = 'block';
    }

    /**
     * شناسایی فیچرها
     */
    async identifyFeatures(coordinate) {
        console.log('Identifying features at:', coordinate);
        // Implementation for feature identification
    }

    /**
     * فعال‌سازی ابزار رسم
     */
    activateDrawingTool(drawType) {
        console.log('Activating drawing tool:', drawType);
        // Implementation for drawing tools
    }

    /**
     * فعال‌سازی ابزار اندازه‌گیری
     */
    activateMeasurementTool(measureType) {
        console.log('Activating measurement tool:', measureType);
        // Implementation for measurement tools
    }

    /**
     * باز کردن دیالوگ چاپ
     */
    async openPrintDialog() {
        try {
            const response = await fetch('/GeoMap/PrintTools');
            const html = await response.text();
            this.showModal('ابزارهای چاپ', html);
        } catch (error) {
            console.error('Error loading print dialog:', error);
            this.showError('خطا در بارگذاری ابزارهای چاپ');
        }
    }

    /**
     * باز کردن دیالوگ ایمپورت
     */
    async openImportDialog() {
        try {
            const response = await fetch('/GeoMap/ImportShp');
            const html = await response.text();
            this.showModal('ایمپورت فایل Shapefile', html);
        } catch (error) {
            console.error('Error loading import dialog:', error);
            this.showError('خطا در بارگذاری ابزار ایمپورت');
        }
    }

    /**
     * تنظیم محدوده پیش‌فرض
     */
    setDefaultExtent() {
        // Set default map extent
        console.log('Setting default map extent');
    }

    /**
     * بارگذاری تنظیمات کاربر
     */
    async loadUserSettings() {
        try {
            // Load user-specific map settings
            console.log('Loading user map settings');
        } catch (error) {
            console.error('Error loading user settings:', error);
        }
    }

    /**
     * ذخیره تنظیمات کاربر
     */
    async saveUserSettings() {
        try {
            // Save user-specific map settings
            console.log('Saving user map settings');
        } catch (error) {
            console.error('Error saving user settings:', error);
        }
    }

    /**
     * نمایش modal
     */
    showModal(title, content) {
        // Implementation for showing modal dialog
        console.log('Showing modal:', title);
    }

    /**
     * نمایش loading
     */
    showLoading(message = 'در حال بارگذاری...') {
        console.log('Loading:', message);
    }

    /**
     * مخفی کردن loading
     */
    hideLoading() {
        console.log('Loading hidden');
    }

    /**
     * نمایش پیام خطا
     */
    showError(message) {
        console.error('Error:', message);
        if (window.toastr) {
            toastr.error(message, 'خطا');
        }
    }

    /**
     * نمایش پیام هشدار
     */
    showWarning(message) {
        console.warn('Warning:', message);
        if (window.toastr) {
            toastr.warning(message, 'هشدار');
        }
    }

    /**
     * نمایش پیام موفقیت
     */
    showSuccess(message) {
        console.log('Success:', message);
        if (window.toastr) {
            toastr.success(message, 'موفقیت');
        }
    }
}

// Global instance
window.geoMapCore = new GeoMapCore();
