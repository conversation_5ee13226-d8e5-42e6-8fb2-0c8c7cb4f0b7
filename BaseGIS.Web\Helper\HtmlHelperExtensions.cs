﻿using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace BaseGIS.Web.Helper
{
    public static class HtmlHelperExtensions
    {
        public static IHtmlContent RouteIf(this IHtmlHelper htmlHelper, string value, string className)
        {
            var currentController = htmlHelper.ViewContext.RouteData.Values["controller"]?.ToString();
            var currentAction = htmlHelper.ViewContext.RouteData.Values["action"]?.ToString();

            bool hasController = string.Equals(currentController, value, StringComparison.OrdinalIgnoreCase);
            var hasAction = string.Equals(currentAction, value, StringComparison.OrdinalIgnoreCase);

            return hasAction || hasController ? new HtmlString(className) : new HtmlString(string.Empty);
        }

        public static IHtmlContent RouteIf(this IHtmlHelper htmlHelper, string value, string value2, string className)
        {
            var currentController = htmlHelper.ViewContext.RouteData.Values["controller"]?.ToString();
            var currentAction = htmlHelper.ViewContext.RouteData.Values["action"]?.ToString();

            bool hasController = string.Equals(currentController, value, StringComparison.OrdinalIgnoreCase);
            var hasAction = string.Equals(currentAction, value2, StringComparison.OrdinalIgnoreCase);

            return hasAction && hasController ? new HtmlString(className) : new HtmlString(string.Empty);
        }
    }
}
