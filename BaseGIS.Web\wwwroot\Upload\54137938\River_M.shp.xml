<?xml version="1.0" encoding="UTF-8"?>
<metadata xml:lang="en"><Esri><CreaDate>20180621</CreaDate><CreaTime>12091800</CreaTime><SyncOnce>FALSE</SyncOnce><DataProperties><lineage><Process Name="Stream to Feature" ToolSource="C:\Program Files\ArcGIS\ArcToolbox\Toolboxes\Spatial Analyst Tools.tbx\StreamToFeature" Date="20101228" Time="130337">StreamToFeature C:\0\Proj\Tadghigh\Rivers_v2\1\gcs_utm38\so3 C:\0\Proj\Tadghigh\Rivers_v2\1\gcs_utm38\fd3 C:\0\Proj\Tadghigh\Rivers_v2\1\gcs_utm38\Rivers3.shp SIMPLIFY</Process><Process ToolSource="C:\Program Files\ArcGIS\ArcToolbox\Toolboxes\Tadghigh.tbx\Model122" Date="20101228" Time="130337">Model122 C:\0\Proj\Tadghigh\Rivers_v2\1\gcs_utm38\Rivers3.shp C:\0\Proj\Tadghigh\Rivers_v2\1\gcs_utm38\f3 C:\0\Proj\Tadghigh\Rivers_v2\1\gcs_utm38\fd3 C:\0\Proj\Tadghigh\Rivers_v2\1\gcs_utm38\fa3 C:\0\Proj\Tadghigh\Rivers_v2\1\gcs_utm38\sn3 C:\0\Proj\Tadghigh\Rivers_v2\1\gcs_utm38\so3 dem38b</Process><Process ToolSource="C:\Program Files\ArcGIS\ArcToolbox\Toolboxes\Data Management Tools.tbx\Merge" Date="20110128" Time="142433">Merge C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b1_38_Rivers_Final.shp;C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b1_39_Rivers_Final.shp;C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b1_40_Rivers_Final.shp;C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_38_Rivers_Final_v2.shp;C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_39_Rivers_Final.shp;C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_40_Rivers_Final.shp;C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_41_Rivers_Final.shp;C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b4_39_Rivers_Final.shp;C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b4_40_Rivers_Final_v2.shp;C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b5_41_Rivers_Final.shp;C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b6_41_Rivers_Final.shp C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\Merge_utm_v3.shp "ARCID 'ARCID' true true false 6 Long 0 6 ,First,#,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b1_38_Rivers_Final.shp,ARCID,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b1_39_Rivers_Final.shp,ARCID,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b1_40_Rivers_Final.shp,ARCID,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_38_Rivers_Final_v2.shp,ARCID,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_39_Rivers_Final.shp,ARCID,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_40_Rivers_Final.shp,ARCID,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_41_Rivers_Final.shp,ARCID,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b4_39_Rivers_Final.shp,ARCID,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b4_40_Rivers_Final_v2.shp,ARCID,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b5_41_Rivers_Final.shp,ARCID,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b6_41_Rivers_Final.shp,ARCID,-1,-1;GRID_CODE 'GRID_CODE' true true false 6 Long 0 6 ,First,#,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b1_38_Rivers_Final.shp,GRID_CODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b1_39_Rivers_Final.shp,GRID_CODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b1_40_Rivers_Final.shp,GRID_CODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_38_Rivers_Final_v2.shp,GRID_CODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_39_Rivers_Final.shp,GRID_CODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_40_Rivers_Final.shp,GRID_CODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_41_Rivers_Final.shp,GRID_CODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b4_39_Rivers_Final.shp,GRID_CODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b4_40_Rivers_Final_v2.shp,GRID_CODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b5_41_Rivers_Final.shp,GRID_CODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b6_41_Rivers_Final.shp,GRID_CODE,-1,-1;FROM_NODE 'FROM_NODE' true true false 6 Long 0 6 ,First,#,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b1_38_Rivers_Final.shp,FROM_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b1_39_Rivers_Final.shp,FROM_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b1_40_Rivers_Final.shp,FROM_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_38_Rivers_Final_v2.shp,FROM_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_39_Rivers_Final.shp,FROM_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_40_Rivers_Final.shp,FROM_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_41_Rivers_Final.shp,FROM_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b4_39_Rivers_Final.shp,FROM_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b4_40_Rivers_Final_v2.shp,FROM_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b5_41_Rivers_Final.shp,FROM_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b6_41_Rivers_Final.shp,FROM_NODE,-1,-1;TO_NODE 'TO_NODE' true true false 6 Long 0 6 ,First,#,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b1_38_Rivers_Final.shp,TO_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b1_39_Rivers_Final.shp,TO_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b1_40_Rivers_Final.shp,TO_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_38_Rivers_Final_v2.shp,TO_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_39_Rivers_Final.shp,TO_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_40_Rivers_Final.shp,TO_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b2_41_Rivers_Final.shp,TO_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b4_39_Rivers_Final.shp,TO_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b4_40_Rivers_Final_v2.shp,TO_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b5_41_Rivers_Final.shp,TO_NODE,-1,-1,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\b6_41_Rivers_Final.shp,TO_NODE,-1,-1"</Process><Process ToolSource="C:\Program Files\ArcGIS\ArcToolbox\Toolboxes\Data Management Tools.tbx\Project" Date="20110128" Time="142541">Project Merge_utm_v3 C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\Merge_gcs_v3.shp GEOGCS['GCS_WGS_1984',DATUM['D_WGS_1984',SPHEROID['WGS_1984',6378137.0,298.257223563]],PRIMEM['Greenwich',0.0],UNIT['Degree',0.0174532925199433]] # PROJCS['WGS_1984_UTM_Zone_38N',GEOGCS['GCS_WGS_1984',DATUM['D_WGS_1984',SPHEROID['WGS_1984',6378137.0,298.257223563]],PRIMEM['Greenwich',0.0],UNIT['Degree',0.0174532925199433]],PROJECTION['Transverse_Mercator'],PARAMETER['False_Easting',500000.0],PARAMETER['False_Northing',0.0],PARAMETER['Central_Meridian',45.0],PARAMETER['Scale_Factor',0.9996],PARAMETER['Latitude_Of_Origin',0.0],UNIT['Meter',1.0]],VERTCS['Unknown VCS from ArcInfo Workstation',VDATUM['Unknown'],PARAMETER['Vertical_Shift',0.0],PARAMETER['Direction',1.0],UNIT['Meter',1.0]]</Process><Process ToolSource="C:\Program Files\ArcGIS\ArcToolbox\Toolboxes\Analysis Tools.tbx\SpatialJoin" Date="20110128" Time="142734">SpatialJoin Merge_gcs_v3 Cuts C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\Merge_gcs_named_v3.shp JOIN_ONE_TO_MANY KEEP_ALL "ARCID 'ARCID' true true false 6 Long 0 6 ,First,#,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\Merge_gcs_v3.shp,ARCID,-1,-1;GRID_CODE 'GRID_CODE' true true false 6 Long 0 6 ,First,#,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\Merge_gcs_v3.shp,GRID_CODE,-1,-1;FROM_NODE 'FROM_NODE' true true false 6 Long 0 6 ,First,#,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\Merge_gcs_v3.shp,FROM_NODE,-1,-1;TO_NODE 'TO_NODE' true true false 6 Long 0 6 ,First,#,C:\0\Proj\Tadghigh\Rivers_v2\All_Rivers_Final\Merge_gcs_v3.shp,TO_NODE,-1,-1;Id 'Id' true true false 6 Long 0 6 ,First,#,C:\0\Proj\Tadghigh\Rivers_v2\Base_Layers\Cuts.shp,Id,-1,-1;River_Name 'River_Name' true true false 50 Text 0 0 ,First,#,C:\0\Proj\Tadghigh\Rivers_v2\Base_Layers\Cuts.shp,River_Name,-1,-1" INTERSECTS "0 DecimalDegrees" #</Process><Process ToolSource="C:\Program Files\ArcGIS\ArcToolbox\Toolboxes\Analysis Tools.tbx\Clip" Date="20110916" Time="170643">Clip Rivers_modify_sep11_v02 Basin1 C:\0\UNSW\Proj\2_Projects\Tadghigh\Borders_Modify_Sep2011\Border_Modify\River\Seperated\Rivers_b1_sep11_v2.shp #</Process><Process ToolSource="C:\Program Files\ArcGIS\ArcToolbox\Toolboxes\Data Management Tools.tbx\Merge" Date="20110917" Time="090358">Merge D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b1_sep11_v2.shp;D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b2_sep11_v2.shp;D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b3_sep11_v2.shp;D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b4_sep11_v2.shp;D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b5_sep11_v2.shp;D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b6_sep11_v2.shp D:\APB\Proj\IranBorders\River\SRTM_90_6_22\River_Merge_By_Aliparast.shp "Join_Count 'Join_Count' true true false 9 Long 0 9 ,First,#,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b1_sep11_v2.shp,Join_Count,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b2_sep11_v2.shp,Join_Count,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b3_sep11_v2.shp,Join_Count,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b4_sep11_v2.shp,Join_Count,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b5_sep11_v2.shp,Join_Count,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b6_sep11_v2.shp,Join_Count,-1,-1;ARCID 'ARCID' true true false 6 Long 0 6 ,First,#,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b1_sep11_v2.shp,ARCID,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b2_sep11_v2.shp,ARCID,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b3_sep11_v2.shp,ARCID,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b4_sep11_v2.shp,ARCID,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b5_sep11_v2.shp,ARCID,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b6_sep11_v2.shp,ARCID,-1,-1;GRID_CODE 'GRID_CODE' true true false 6 Long 0 6 ,First,#,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b1_sep11_v2.shp,GRID_CODE,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b2_sep11_v2.shp,GRID_CODE,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b3_sep11_v2.shp,GRID_CODE,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b4_sep11_v2.shp,GRID_CODE,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b5_sep11_v2.shp,GRID_CODE,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b6_sep11_v2.shp,GRID_CODE,-1,-1;FROM_NODE 'FROM_NODE' true true false 6 Long 0 6 ,First,#,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b1_sep11_v2.shp,FROM_NODE,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b2_sep11_v2.shp,FROM_NODE,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b3_sep11_v2.shp,FROM_NODE,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b4_sep11_v2.shp,FROM_NODE,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b5_sep11_v2.shp,FROM_NODE,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b6_sep11_v2.shp,FROM_NODE,-1,-1;TO_NODE 'TO_NODE' true true false 6 Long 0 6 ,First,#,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b1_sep11_v2.shp,TO_NODE,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b2_sep11_v2.shp,TO_NODE,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b3_sep11_v2.shp,TO_NODE,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b4_sep11_v2.shp,TO_NODE,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b5_sep11_v2.shp,TO_NODE,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b6_sep11_v2.shp,TO_NODE,-1,-1;Id 'Id' true true false 6 Long 0 6 ,First,#,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b1_sep11_v2.shp,Id,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b2_sep11_v2.shp,Id,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b3_sep11_v2.shp,Id,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b4_sep11_v2.shp,Id,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b5_sep11_v2.shp,Id,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b6_sep11_v2.shp,Id,-1,-1;River_Name 'River_Name' true true false 50 Text 0 0 ,First,#,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b1_sep11_v2.shp,River_Name,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b2_sep11_v2.shp,River_Name,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b3_sep11_v2.shp,River_Name,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b4_sep11_v2.shp,River_Name,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b5_sep11_v2.shp,River_Name,-1,-1,D:\APB\Proj\IranBorders\River\SRTM_90_6_22\Rivers_Seperated\Rivers_b6_sep11_v2.shp,River_Name,-1,-1"</Process><Process ToolSource="C:\Program Files\ArcGIS\Desktop10.0\ArcToolbox\Toolboxes\Cartography Tools.tbx\SmoothLine" Date="20111124" Time="072956">SmoothLine River_Iran_SRTM_Not_Smooth D:\APB\Proj\IranBorders\Basins\Refined_90_7_11_With_Physio_River_Profile\Iran\River_SRTM\Smooth\River_Iran_SRTM_Smooth_500m_Final.shp PAEK "500 Meters" FIXED_CLOSED_ENDPOINT NO_CHECK</Process><Process ToolSource="C:\Program Files (x86)\ArcGIS\ArcToolbox\Toolboxes\Data Management Tools.tbx\CopyFeatures" Date="20140526" Time="141056">CopyFeatures D:\Data_base_93_03_05\Main_river.shp D:\Data_base_93_03_05\Data_Base.gdb\River\Main_river # 0 0 0</Process><Process ToolSource="c:\program files (x86)\arcgis\desktop10.2\ArcToolbox\Toolboxes\Data Management Tools.tbx\CopyFeatures" Date="20141108" Time="120335">CopyFeatures "Database Connections\IWPWebGIS.sde\IWPWebGIS.DBO.River\IWPWebGIS.DBO.Main_river" "Database Connections\AbnirooGDB.sde\AbnirooGDB.DBO.River\AbnirooGDB.DBO.Main_river" # 0 0 0</Process></lineage><itemProps><itemLocation><linkage Sync="TRUE">Server=gis; Service=sde:sqlserver:gis; Database=FloodWarningGDB; User=sa2; Version=sde.DEFAULT</linkage><protocol Sync="TRUE">ArcSDE Connection</protocol></itemLocation><itemName Sync="TRUE">AbnirooGDB.DBO.Main_river</itemName><imsContentType Sync="TRUE">002</imsContentType><itemSize Sync="TRUE">0.000</itemSize></itemProps><copyHistory><copy source="D:\APB\Proj\IranBorders\River\SRTM_89_12_2\Merge_gcs_named_v9" dest="\\CHANGEME1\D$\APB\Proj\IranBorders\River\SRTM_89_12_2\Merge_gcs_named_v9Copy" date="20110308" time="16532300"/></copyHistory><coordRef><type Sync="TRUE">Projected</type><geogcsn Sync="TRUE">GCS_WGS_1984</geogcsn><peXml Sync="TRUE">&lt;ProjectedCoordinateSystem xsi:type='typens:ProjectedCoordinateSystem' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance' xmlns:xs='http://www.w3.org/2001/XMLSchema' xmlns:typens='http://www.esri.com/schemas/ArcGIS/10.1'&gt;&lt;WKT&gt;PROJCS[&amp;quot;WGS_1984_Web_Mercator_Auxiliary_Sphere&amp;quot;,GEOGCS[&amp;quot;GCS_WGS_1984&amp;quot;,DATUM[&amp;quot;D_WGS_1984&amp;quot;,SPHEROID[&amp;quot;WGS_1984&amp;quot;,6378137.0,298.257223563]],PRIMEM[&amp;quot;Greenwich&amp;quot;,0.0],UNIT[&amp;quot;Degree&amp;quot;,0.0174532925199433]],PROJECTION[&amp;quot;Mercator_Auxiliary_Sphere&amp;quot;],PARAMETER[&amp;quot;False_Easting&amp;quot;,0.0],PARAMETER[&amp;quot;False_Northing&amp;quot;,0.0],PARAMETER[&amp;quot;Central_Meridian&amp;quot;,0.0],PARAMETER[&amp;quot;Standard_Parallel_1&amp;quot;,0.0],PARAMETER[&amp;quot;Auxiliary_Sphere_Type&amp;quot;,0.0],UNIT[&amp;quot;Meter&amp;quot;,1.0],AUTHORITY[&amp;quot;EPSG&amp;quot;,3857]]&lt;/WKT&gt;&lt;XOrigin&gt;-20037700&lt;/XOrigin&gt;&lt;YOrigin&gt;-30241100&lt;/YOrigin&gt;&lt;XYScale&gt;10000&lt;/XYScale&gt;&lt;ZOrigin&gt;0&lt;/ZOrigin&gt;&lt;ZScale&gt;1&lt;/ZScale&gt;&lt;MOrigin&gt;0&lt;/MOrigin&gt;&lt;MScale&gt;1&lt;/MScale&gt;&lt;XYTolerance&gt;0.001&lt;/XYTolerance&gt;&lt;ZTolerance&gt;0.001&lt;/ZTolerance&gt;&lt;MTolerance&gt;0.001&lt;/MTolerance&gt;&lt;HighPrecision&gt;true&lt;/HighPrecision&gt;&lt;WKID&gt;102100&lt;/WKID&gt;&lt;LatestWKID&gt;3857&lt;/LatestWKID&gt;&lt;/ProjectedCoordinateSystem&gt;</peXml><csUnits Sync="TRUE">Linear Unit: Meter (1.000000)</csUnits><projcsn Sync="TRUE">WGS_1984_Web_Mercator_Auxiliary_Sphere</projcsn></coordRef></DataProperties><SyncDate>20141108</SyncDate><SyncTime>12031200</SyncTime><ModDate>20141108</ModDate><ModTime>12031200</ModTime><ArcGISFormat>1.0</ArcGISFormat></Esri><idinfo><citation><citeinfo><onlink Sync="TRUE">\\GHIASVAND-G2\D$\Data_base_93_03_05\Data_Base.gdb</onlink><origin>REQUIRED: The name of an organization or individual that developed the data set.</origin><pubdate>REQUIRED: The date when the data set is published or otherwise made available for release.</pubdate><title Sync="TRUE">Main_river</title><ftname Sync="TRUE">Main_river</ftname><geoform Sync="TRUE">vector digital data</geoform></citeinfo></citation><native Sync="TRUE">Microsoft Windows Vista Version 6.1 (Build 7601) Service Pack 1; ESRI ArcCatalog 9.3.0.1770</native><descript><langdata Sync="TRUE">fa</langdata><abstract>REQUIRED: A brief narrative summary of the data set.</abstract><purpose>REQUIRED: A summary of the intentions with which the data set was developed.</purpose></descript><timeperd><current>REQUIRED: The basis on which the time period of content information is determined.</current><timeinfo><sngdate><caldate>REQUIRED: The year (and optionally month, or month and day) for which the data set corresponds to the ground.</caldate></sngdate></timeinfo></timeperd><status><progress>REQUIRED: The state of the data set.</progress><update>REQUIRED: The frequency with which changes and additions are made to the data set after the initial data set is completed.</update></status><spdom><bounding><westbc Sync="TRUE">REQUIRED: Western-most coordinate of the limit of coverage expressed in longitude.</westbc><eastbc Sync="TRUE">REQUIRED: Eastern-most coordinate of the limit of coverage expressed in longitude.</eastbc><northbc Sync="TRUE">REQUIRED: Northern-most coordinate of the limit of coverage expressed in latitude.</northbc><southbc Sync="TRUE">REQUIRED: Southern-most coordinate of the limit of coverage expressed in latitude.</southbc></bounding></spdom><keywords><theme><themekt>REQUIRED: Reference to a formally registered thesaurus or a similar authoritative source of theme keywords.</themekt><themekey>REQUIRED: Common-use word or phrase used to describe the subject of the data set.</themekey></theme></keywords><accconst>REQUIRED: Restrictions and legal prerequisites for accessing the data set.</accconst><useconst>REQUIRED: Restrictions and legal prerequisites for using the data set after access is granted.</useconst><natvform Sync="TRUE">File Geodatabase Feature Class</natvform></idinfo><distInfo><distributor><distorTran><onLineSrc><linkage Sync="TRUE">file://\\GHIASVAND-G2\D$\Data_base_93_03_05\Data_Base.gdb</linkage><protocol Sync="TRUE">Local Area Network</protocol><orDesc Sync="TRUE">002</orDesc></onLineSrc><transSize Sync="TRUE">0.000</transSize></distorTran><distorFormat><formatName Sync="TRUE">File Geodatabase Feature Class</formatName></distorFormat></distributor><distFormat><formatName Sync="TRUE">SDE Feature Class</formatName></distFormat><distTranOps><transSize Sync="TRUE">0.000</transSize></distTranOps></distInfo><dataqual><lineage><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20101228</procdate><proctime Sync="TRUE">13392100</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE">C:\0\Proj\Tadghigh\Rivers_v2\1\gcs_utm38\b1_38_Rivers_Final</srcused><procdate Sync="TRUE">20110103</procdate><proctime Sync="TRUE">12030600</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20110128</procdate><proctime Sync="TRUE">14253800</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20110207</procdate><proctime Sync="TRUE">15153800</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20110211</procdate><proctime Sync="TRUE">14503600</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20110218</procdate><proctime Sync="TRUE">09364500</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20110221</procdate><proctime Sync="TRUE">10263600</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20110413</procdate><proctime Sync="TRUE">14390200</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20110425</procdate><proctime Sync="TRUE">16433600</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20110425</procdate><proctime Sync="TRUE">16500700</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20110425</procdate><proctime Sync="TRUE">17191500</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20110731</procdate><proctime Sync="TRUE">08175000</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20110731</procdate><proctime Sync="TRUE">11065600</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20110803</procdate><proctime Sync="TRUE">22383800</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE">D:\APB\Proj\IranBorders\River\SRTM_90_5_15\Rivers_jul2011_v02</srcused><procdate Sync="TRUE">20110824</procdate><proctime Sync="TRUE">09330700</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20110908</procdate><proctime Sync="TRUE">16462400</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20110916</procdate><proctime Sync="TRUE">16491900</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE">D:\APB\Proj\IranBorders\River\SRTM_90_6_22\River_Merge_By_Aliparast</srcused><procdate Sync="TRUE">20110917</procdate><proctime Sync="TRUE">10083600</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset moved.</procdesc><srcused Sync="TRUE">D:\APB\Proj\IranBorders\Basins\Refined_90_6_20\Iran\River_SRTM\River_Iran_SRTM_Not_Smooth</srcused><procdate Sync="TRUE">20110917</procdate><proctime Sync="TRUE">10100000</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE">C:\D\GIS\GIS DATA\From Lotfi\marz basins-new\Basin_Border_River\Iran\River_SRTM\Smooth\River_Iran_SRTM_Smooth_500m</srcused><procdate Sync="TRUE">20130102</procdate><proctime Sync="TRUE">13180100</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20131211</procdate><proctime Sync="TRUE">17071100</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20131212</procdate><proctime Sync="TRUE">10502600</proctime></procstep><procstep><procdesc Sync="TRUE">Dataset copied.</procdesc><srcused Sync="TRUE"/><procdate Sync="TRUE">20140526</procdate><proctime Sync="TRUE">12261000</proctime></procstep></lineage></dataqual><dataIdInfo><envirDesc Sync="TRUE">Microsoft Windows Server 2008 R2 Version 6.1 (Build 7601) Service Pack 1; Esri ArcGIS 10.2.1.3497</envirDesc><dataLang><languageCode Sync="TRUE" value="fas" country="IRN"/><countryCode value="IRN" Sync="TRUE"/></dataLang><idCitation><resTitle Sync="TRUE">AbnirooGDB.DBO.Main_river</resTitle><presForm><PresFormCd Sync="TRUE" value="005"/></presForm></idCitation><spatRpType><SpatRepTypCd Sync="TRUE" value="001"/></spatRpType><descKeys><thesaName uuidref="723f6998-058e-11dc-8314-0800200c9a66"/><keyword Sync="TRUE">002</keyword></descKeys></dataIdInfo><metainfo><langmeta Sync="TRUE">fa</langmeta><metstdn Sync="TRUE">FGDC Content Standards for Digital Geospatial Metadata</metstdn><metstdv Sync="TRUE">FGDC-STD-001-1998</metstdv><mettc Sync="TRUE">local time</mettc><metc><cntinfo><cntorgp><cntper>REQUIRED: The person responsible for the metadata information.</cntper><cntorg>REQUIRED: The organization responsible for the metadata information.</cntorg></cntorgp><cntaddr><addrtype>REQUIRED: The mailing and/or physical address for the organization or individual.</addrtype><city>REQUIRED: The city of the address.</city><state>REQUIRED: The state or province of the address.</state><postal>REQUIRED: The ZIP or other postal code of the address.</postal></cntaddr><cntvoice>REQUIRED: The telephone number by which individuals can speak to the organization or individual.</cntvoice></cntinfo></metc><metd Sync="TRUE">20140526</metd></metainfo><mdLang><languageCode Sync="TRUE" value="fas"/><countryCode value="IRN" Sync="TRUE"/></mdLang><mdStanName Sync="TRUE">ISO 19115 Geographic Information - Metadata</mdStanName><mdStanVer Sync="TRUE">DIS_ESRI1.0</mdStanVer><mdChar><CharSetCd Sync="TRUE" value="004"/></mdChar><mdHrLv><ScopeCd Sync="TRUE" value="005"/></mdHrLv><mdHrLvName Sync="TRUE">dataset</mdHrLvName><distinfo><resdesc Sync="TRUE">Downloadable Data</resdesc><stdorder><digform><digtinfo><transize Sync="TRUE">0.000</transize><dssize Sync="TRUE">0.000</dssize></digtinfo></digform></stdorder></distinfo><spdoinfo><direct Sync="TRUE">Vector</direct><ptvctinf><esriterm Name="AbnirooGDB.DBO.Main_river"><efeatyp Sync="TRUE">Simple</efeatyp><efeageom code="3" Sync="TRUE"/><esritopo Sync="TRUE">FALSE</esritopo><efeacnt Sync="TRUE">0</efeacnt><spindex Sync="TRUE">TRUE</spindex><linrefer Sync="TRUE">FALSE</linrefer></esriterm></ptvctinf></spdoinfo><spref><horizsys><cordsysn><geogcsn Sync="TRUE">GCS_WGS_1984</geogcsn></cordsysn><geodetic><horizdn Sync="TRUE">D_WGS_1984</horizdn><ellips Sync="TRUE">WGS_1984</ellips><semiaxis Sync="TRUE">6378137.000000</semiaxis><denflat Sync="TRUE">298.257224</denflat></geodetic></horizsys><vertdef><altsys><altenc Sync="TRUE">Explicit elevation coordinate included with horizontal coordinates</altenc><altres Sync="TRUE">0.000100</altres></altsys></vertdef></spref><refSysInfo><RefSystem><refSysID><identCode Sync="TRUE" code="3857">GCS_WGS_1984</identCode><idCodeSpace Sync="TRUE">EPSG</idCodeSpace><idVersion Sync="TRUE">8.2.6</idVersion></refSysID></RefSystem></refSysInfo><spatRepInfo><VectSpatRep><geometObjs Name="AbnirooGDB.DBO.Main_river"><geoObjTyp><GeoObjTypCd value="002" Sync="TRUE"/></geoObjTyp><geoObjCnt Sync="TRUE">0</geoObjCnt></geometObjs><topLvl><TopoLevCd value="001" Sync="TRUE"/></topLvl></VectSpatRep></spatRepInfo><eainfo><detailed Name="AbnirooGDB.DBO.Main_river"><enttyp><enttypl Sync="TRUE">AbnirooGDB.DBO.Main_river</enttypl><enttypt Sync="TRUE">Feature Class</enttypt><enttypc Sync="TRUE">0</enttypc></enttyp><attr><attrlabl Sync="TRUE">OBJECTID</attrlabl><attalias Sync="TRUE">OBJECTID</attalias><attrtype Sync="TRUE">OID</attrtype><attwidth Sync="TRUE">4</attwidth><atprecis Sync="TRUE">10</atprecis><attscale Sync="TRUE">0</attscale><attrdef Sync="TRUE">Internal feature number.</attrdef><attrdefs Sync="TRUE">ESRI</attrdefs><attrdomv><udom Sync="TRUE">Sequential unique whole numbers that are automatically generated.</udom></attrdomv></attr><attr><attrlabl Sync="TRUE">Shape</attrlabl><attalias Sync="TRUE">Shape</attalias><attrtype Sync="TRUE">Geometry</attrtype><attwidth Sync="TRUE">4</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale><attrdef Sync="TRUE">Feature geometry.</attrdef><attrdefs Sync="TRUE">ESRI</attrdefs><attrdomv><udom Sync="TRUE">Coordinates defining the features.</udom></attrdomv></attr><attr><attrlabl Sync="TRUE">GRID_CODE</attrlabl><attalias Sync="TRUE">رتبه</attalias><attrtype Sync="TRUE">Integer</attrtype><attwidth Sync="TRUE">4</attwidth><atprecis Sync="TRUE">10</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">River_Name</attrlabl><attalias Sync="TRUE">نام</attalias><attrtype Sync="TRUE">String</attrtype><attwidth Sync="TRUE">50</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr><attr><attrlabl Sync="TRUE">Shape.STLength()</attrlabl><attalias Sync="TRUE">Shape.STLength()</attalias><attrtype Sync="TRUE">Double</attrtype><attwidth Sync="TRUE">0</attwidth><atprecis Sync="TRUE">0</atprecis><attscale Sync="TRUE">0</attscale></attr></detailed></eainfo><mdDateSt Sync="TRUE">20141108</mdDateSt></metadata>
