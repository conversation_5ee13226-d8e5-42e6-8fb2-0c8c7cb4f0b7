﻿@using BaseGIS.Core.Entities
@{
    string tblid = Context.Request.Query["tblid"];
    string id = Context.Request.Query["id"];
    FieldInfo fieldInfo = Model;

    List<TableInfo> relTableList = ViewBag.RelTableList;
    List<TableInfo> tableList = ViewBag.TableList;
}
<form action="/Database/_Layer_Field" method="post" id="formLayerField">
    @Html.AntiForgeryToken()
    <input id="ID" name="ID" value="@id" type="hidden" />
    <input id="TBLID" name="TBLID" value="@tblid" type="hidden" />

    <div class="modal-header bg-light">
        <h5 class="modal-title samanFont">اطلاعات فیلد</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>

    <div class="modal-body">
        @if (fieldInfo.Name.ToLower() == "gcode" || fieldInfo.Name.ToLower() == "area" || 
            fieldInfo.Name.ToLower() == "length" || fieldInfo.Name.ToLower() == "objectid" || 
            fieldInfo.Name.ToLower() == "creatoruser" || fieldInfo.Name.ToLower() == "createtime" || 
            fieldInfo.Name.ToLower() == "lastuser" || fieldInfo.Name.ToLower() == "lasttime" || 
            fieldInfo.Name.ToLower() == "usergroup" || fieldInfo.Name.ToLower() == "org")
        {
            <div class="mb-1">
                <label for="FieldIndex" class="form-label">ایندکس:</label>
                <div class="input-group">
                    <input id="FieldIndex" name="FieldIndex" class="form-control" type="text" 
                    placeholder="ایندکس" value="@fieldInfo.FieldIndex">
                    <span class="input-group-text"><i class="fa fa-database"></i></span>
                </div>
            </div>
        }
        else
        {
            <div class="d-flex justify-content-end mb-1">
                @if (fieldInfo != null)
                {
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-danger" title="حذف" onclick="frmFieldDel(@fieldInfo.Id)">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                }
            </div>

            <div class="row g-3">
                <div class="col-12">
                    <label for="Name" class="form-label">نام فیلد</label>
                    <div class="input-group">
                        <input id="Name" name="Name" class="form-control" type="text" 
                        placeholder="نام لاتین بر اساس استاندارد" @(id != null ? "disabled" : "") 
                        value="@fieldInfo.Name">
                        <span class="input-group-text"><i class="fa fa-database"></i></span>
                    </div>
                </div>

                <div class="col-12">
                    <label for="AliasName" class="form-label">نام فارسی</label>
                    <div class="input-group">
                        <textarea id="AliasName" name="AliasName" class="form-control" 
                        placeholder="Alias" rows="2" required>@fieldInfo.AliasName</textarea>
                        <span class="input-group-text"><i class="fa fa-tag"></i></span>
                    </div>
                </div>

                <div class="col-md-6">
                    <label for="UnitName" class="form-label">واحد:</label>
                    <div class="input-group">
                        <input id="UnitName" name="UnitName" class="form-control" type="text" 
                        placeholder="واحد" value="@fieldInfo.UnitName">
                        <span class="input-group-text"><i class="fa fa-database"></i></span>
                    </div>
                </div>

                <div class="col-md-6">
                    <label for="FieldType" class="form-label">نوع فیلد:</label>
                    <select class="form-select" id="FieldType" name="FieldType" @(id != null ? "disabled" : "")>
                        <option value="">@(fieldInfo != null ? fieldInfo.FieldType : "")</option>
                        <option value="nvarchar">nvarchar</option>
                        <option value="int">int</option>
                        <option value="bigint">bigint</option>
                        <option value="numeric">numeric</option>
                        <option value="bool">bool</option>
                        <option value="datetime">date</option>
                        <option value="domain">domain</option>
                        <option value="multidomain">multidomain</option>
                    </select>
                </div>

                <div class="col-md-6">
                    <label for="FieldLength" class="form-label">طول فیلد:</label>
                    <div class="input-group">
                        <input id="FieldLength" name="FieldLength" class="form-control" type="text" 
                        placeholder="طول فیلد" value="@fieldInfo.FieldLength" @(id != null ? "disabled" : "")>
                        <span class="input-group-text"><i class="fa fa-database"></i></span>
                    </div>
                </div>

                <div class="col-md-6">
                    <label for="FieldIndex" class="form-label">ایندکس:</label>
                    <div class="input-group">
                        <input id="FieldIndex" name="FieldIndex" class="form-control" type="text" 
                        placeholder="ایندکس" value="@fieldInfo.FieldIndex">
                        <span class="input-group-text"><i class="fa fa-database"></i></span>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="IsRequired" name="IsRequired" 
                        @(fieldInfo.IsRequired ? "checked" : "") @(id != null ? "disabled" : "")>
                        <label class="form-check-label" for="IsRequired">اجباری</label>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="IsUnique" name="IsUnique" 
                        @(fieldInfo.IsUnique ? "checked" : "") @(id != null ? "disabled" : "")>
                        <label class="form-check-label" for="IsUnique">منحصربفرد</label>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="Editable" name="Editable" 
                        @(fieldInfo.Editable ? "checked" : "")>
                        <label class="form-check-label" for="Editable">قابل ویرایش</label>
                    </div>
                </div>

                <div class="col-md-8">
                    <label for="WebService_URL" class="form-label">سرویس:</label>
                    <div class="input-group">
                        <input id="WebService_URL" name="WebService_URL" class="form-control" type="text" 
                        placeholder="آدرس وب سرویس" value="@(fieldInfo.WebService_URL != null ? fieldInfo.WebService_URL : "")">
                        <span class="input-group-text"><i class="fa fa-internet-explorer"></i></span>
                    </div>
                </div>

                <div class="col-md-4">
                    <label for="WebService_Period" class="form-label">مدت زمان آپدیت:</label>
                    <div class="input-group">
                        <input id="WebService_Period" name="WebService_Period" class="form-control" type="number" 
                        placeholder="بر اساس دقیقه" value="@(fieldInfo.WebService_Period != null ? fieldInfo.WebService_Period.ToString() : "60")">
                        <span class="input-group-text"><i class="fa fa-clock-o"></i></span>
                    </div>
                </div>

                <div class="col-md-8">
                    <label for="SQL_Calc" class="form-label">فیلد محاسباتی:</label>
                    <div class="input-group">
                        <textarea id="SQL_Calc" name="SQL_Calc" class="form-control" 
                        placeholder="دستور SQL" rows="2">@(fieldInfo.SQLCalc)</textarea>
                        <span class="input-group-text"><i class="fa fa-database"></i></span>
                    </div>

                    <div class="mt-2">
                        <label for="Calc_Period" class="form-label">مدت زمان آپدیت:</label>
                        <div class="input-group">
                            <input id="Calc_Period" name="Calc_Period" class="form-control" type="number" 
                            placeholder="بر اساس دقیقه" value="@(fieldInfo.CalcPeriod > 0 ? fieldInfo.CalcPeriod.ToString() : "60")">
                            <span class="input-group-text"><i class="fa fa-clock-o"></i></span>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <label for="cal_RelTable" class="form-label">جداول مرتبط با فیلد محاسباتی:</label>
                    <select multiple id="cal_RelTable" class="chosen-select form-select" name="cal_RelTable" 
                    style="height: 120px;">
                        <option value=""></option>
                        @for (int i = 0; i < tableList.Count; i++)
                        {
                            if (relTableList != null && relTableList.FindIndex(t => t.Id == tableList[i].Id) >= 0)
                            {
                                <option value="@tableList[i].Id" selected>@tableList[i].AliasName</option>
                            }
                            else
                            {
                                <option value="@tableList[i].Id" >@tableList[i].AliasName</option>
                            }
                        }
                    </select>
                </div>
            </div>
        }
    </div>
    
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            انصراف
        </button>
        <button id="submitLayerField" type="button" class="btn btn-success">
            ذخیره
        </button>
    </div>
</form>

<script>
    $(document).ready(function() {
        $('.chosen-select').chosen({ width: '100%', rtl: true, no_results_text: "یافت نشد" });
        
        $("#submitLayerField").click(function () {
            saveForm();
        });
    });
    
    function saveForm() {
        var form = $("#formLayerField").serialize();
        $.ajax({
            type: 'POST',
            url: "../Database/_Layer_Field",
            data: form,
            dataType: 'json',
            success: function (data) {
                if (data.success) {
                    $('#ModalLayerField').modal('hide');
                    $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "green", iconSmall: "fa fa-thumbs-up bounce animated", timeout: 4000 });
                } else
                    $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
            },
            error: function (data) {
                $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
            }
        });
    }
    
    function frmFieldDel(idField) {
        if (idField) {
            Swal.fire({
                title: 'تأیید حذف',
                html: "<b class='samanFont'>آیا مطمئن به حذف فیلد انتخاب شده هستید؟</b>",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545', // Bootstrap 5 danger color
                cancelButtonColor: '#6c757d', // Bootstrap 5 secondary color
                confirmButtonText: 'حذف',
                cancelButtonText: 'لغو',
                reverseButtons: true,
                customClass: {
                    popup: 'samanFont',
                    title: 'samanFont',
                    htmlContainer: 'samanFont',
                    confirmButton: 'samanFont btn btn-danger',
                    cancelButton: 'samanFont btn btn-secondary'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        type: 'POST',
                        url: "../database/_Layer_FieldDel",
                        data: {
                            id: idField,
                            "__RequestVerificationToken": $("input[name='__RequestVerificationToken']").last().val()
                        },
                        dataType: 'json',
                        success: function (data) {
                            if (data.success) {
                                $('#ModalLayerField').modal('hide');
                                Swal.fire({
                                    title: 'موفق',
                                    html: data.responseText,
                                    icon: 'success',
                                    timer: 2000
                                });
                            } else {
                                Swal.fire({
                                    title: 'خطا',
                                    html: data.responseText,
                                    icon: 'error'
                                });
                            }
                        },
                        error: function (data) {
                            Swal.fire({
                                title: 'خطا',
                                html: data.statusText,
                                icon: 'error'
                            });
                        }
                    });
                }
            });
        } else {
            Swal.fire({
                title: 'خطا',
                html: 'لطفا یک فیلد را انتخاب نمایید',
                icon: 'error'
            });
        }
    }
     
</script>







