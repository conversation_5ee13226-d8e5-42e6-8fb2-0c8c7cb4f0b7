﻿@using BaseGIS.Core.Entities

@{
    List<TableInfo> tableInfos = Model;
    var GroupList = tableInfos.Select(x => x.GroupInfo).Distinct().ToList();
    string id = Context.Request.Query["id"];
    int selectedId = 0;
    if (!string.IsNullOrEmpty(id) && int.TryParse(id, out int parsedId))
    {
        selectedId = parsedId;
    }

    ApplicationRole Role = ViewBag.Role;
    TableAccess Role_TableAccess = null;
    if (Role != null)
    {
        Role_TableAccess = Role.ConvertTable(Role.TableAccess);
    }
}

<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />

<div class="col-12" id="InsertList">
    <div class="card border-danger mb-3 samanFont">
        <div class="card-header bg-danger text-white">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h5 class="card-title samanFont mb-0">فهرست لایه‌ها</h5>
                </div>
            </div>
        </div>
        <div class="collapse show" id="layerListContent">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-lg-10 col-md-12">
                        <select class="form-select select2" id="GList" data-placeholder="یک جدول انتخاب کنید">
                            <option value="0"></option>
                            @foreach (var group in GroupList)
                            {
                                var list = tableInfos.Where(a => a.GroupInfo == group).ToList();
                                if (list.Any())
                                {
                                    var lblG = group != null ? group.AliasName : "بدون گروه‌بندی";
                                    <optgroup label="@lblG">
                                        @foreach (var item in list)
                                        {
                                            bool access = true;
                                            if (Role_TableAccess != null)
                                            {
                                                access = false;
                                                var RoleAccessTable = Role_TableAccess.Tables.Find(a => a.Name == item.Name);
                                                if (RoleAccessTable != null && RoleAccessTable.IsEdit)
                                                {
                                                    access = true;
                                                }
                                            }
                                            if (access)
                                            {
                                                if (item.Id == selectedId)
                                                {
                                                    <option value="@item.Id" selected >@item.AliasName</option>
                                                }
                                                else
                                                {
                                                    <option value="@item.Id" >@item.AliasName</option>
                                                }
                                            }
                                        }
                                    </optgroup>
                                }
                            }
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-12">
                        <button type="button" onclick="selectLayer();" class="btn btn-outline-primary w-100">
                            تایید
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="~/lib/select2/js/select2.min.js"></script>
<script src="~/lib/select2/js/i18n/fa.js"></script>
<script type="text/javascript">
    $(document).ready(function () {
        $('.select2').select2({
            theme: 'bootstrap-5',
            language: 'fa',
            dir: 'rtl',
            placeholder: 'یک جدول انتخاب کنید',
            allowClear: true,
            width: '100%'
        });
    });

    function selectLayer() {
        var selectedId = $('#GList').val();
        if (selectedId && selectedId !== '0') {
            window.onItemSelected(selectedId); // فراخوانی تابع از ویوی اصلی
        } else {
            $("#_Insert_Wizard").hide();
        }
    }
</script>
