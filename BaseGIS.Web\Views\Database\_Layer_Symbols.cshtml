﻿@using BaseGIS.Core.Entities
@{
    var tblid = Context.Request.Query["id"];
    TableInfo tblInfo = Model;
}

<div class="card border-danger mb-3 samanFont" id="wid-id-50">
    <div class="card-header bg-danger text-white">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <h5 class="card-title samanFont mb-0">نماد لایه</h5>
                <span class="spinner-border spinner-border-sm text-light d-none ms-2"></span>
            </div>
            <div>
                <a class="btn btn-sm btn-outline-light" data-bs-toggle="modal" href="~/symbology/index?tblid=@tblid"
                   data-bs-target="#ModalList">
                    <i class="fa fa-plus"></i> <span class="d-none d-md-inline">اضافه کردن</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Content area -->
    <div class="collapse show" id="symbolLayerContent">
        <div class="card-body p-0">
            <div class="p-3" style="height: 350px; overflow-y: auto;">
                @if (tblInfo != null)
                {
                    <h6 class="samanFont mb-3">نوع: @tblInfo.DatasetType</h6>

                    <ul class="list-group">
                        @foreach (var sym in tblInfo.Symbologies)
                        {
                            <li class="list-group-item p-3">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <img class="rounded" style="width:64px; height:64px;" src="content/img/demo/64x64.png" alt="Symbol image">
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <h6 class="samanFont mb-1">
                                                <a href="~/symbology/index?tblid=@tblid&id=@sym.Id" class="text-decoration-none">@sym.Name</a>
                                                @if (sym.IsDefault)
                                                {
                                                    <span class="badge bg-secondary ms-1">پیش فرض</span>
                                                }
                                            </h6>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="delSymbology(@sym.Id);" title="حذف">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                        <p class="mb-0 text-muted">
                                            نوع: @(sym.Type == SymbologyType.Simple ? "ساده" :
                                           (sym.Type == SymbologyType.Unique ? "مقادیر یکتا" : "دسته بندی عددی"))
                                        </p>
                                    </div>
                                </div>
                            </li>
                        }
                    </ul>

                    if (!tblInfo.Symbologies.Any())
                    {
                        <div class="text-center text-muted py-5">
                            <i class="fa fa-info-circle fa-2x mb-3"></i>
                            <p>هیچ نمادی یافت نشد</p>
                        </div>
                    }
                }
                else
                {
                    <div class="text-center text-muted py-5">
                        <i class="fa fa-exclamation-triangle fa-2x mb-3"></i>
                        <p>لطفا یک لایه را انتخاب کنید</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<div class="modal" id="ModalList" tabindex="-1" aria-labelledby="ModalListLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <!-- Modal content will be loaded here -->
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {
        // Handle modal loading
        $('a[data-bs-toggle="modal"][data-bs-target="#ModalList"]').on('click', function (e) {
            e.preventDefault();
            var url = $(this).attr('href');

            $.ajax({
                url: url,
                cache: false,
                success: function (data) {
                    $("#ModalList .modal-content").html(data);
                    var modalElement = document.getElementById('ModalList');
                    var modal = new bootstrap.Modal(modalElement);
                    modal.show();

                    // Call GenerateGraphic function if it exists
                    if (typeof GenerateGraphic === 'function') {
                        GenerateGraphic();
                    }
                },
                error: function (data) {
                    console.error("Error loading modal content:", data);
                    showToast('خطا در بارگذاری محتوا', 'danger');
                }
            });
        });

        // Initialize collapse functionality
        var collapseElementList = [].slice.call(document.querySelectorAll('[data-bs-toggle="collapse"]'));
        collapseElementList.map(function (collapseEl) {
            collapseEl.addEventListener('click', function() {
                var icon = this.querySelector('i');
                if (icon) {
                    icon.classList.toggle('fa-minus');
                    icon.classList.toggle('fa-plus');
                }
            });
        });

        // Handle modal close event
        $('#ModalList').on('hidden.bs.modal', function () {
            Refresh_Symbologys(Context.Request.Query["id"]);

            // Remove backdrop manually if it's still present
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
            // Also remove modal-open class from body if needed
            document.body.classList.remove('modal-open');
            // And remove inline styles that Bootstrap might have added
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        });
    });

    function Refresh_Symbologys(id) {
        if (id)
            $("#_Symbol_Layer").load('../database/_Symbol_Layer?id=' + id);
    }

    function delSymbology(id) {
        if (id) {
            Swal.fire({
                title: 'تأیید حذف',
                html: "<b class='samanFont'>آیا مطمئن به حذف نماد انتخاب شده هستید؟</b>",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545', // Bootstrap 5 danger color
                cancelButtonColor: '#6c757d', // Bootstrap 5 secondary color
                confirmButtonText: 'حذف',
                cancelButtonText: 'لغو',
                reverseButtons: true,
                customClass: {
                    popup: 'samanFont',
                    title: 'samanFont',
                    htmlContainer: 'samanFont',
                    confirmButton: 'samanFont btn btn-danger',
                    cancelButton: 'samanFont btn btn-secondary'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        type: 'POST',
                        url: "../symbology/_Del",
                        data: { id: id },
                        dataType: 'json',
                        success: function (data) {
                            if (data.success) {
                                Refresh_Symbologys(Context.Request.Query["id"]);
                                $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "green", iconSmall: "fa fa-thumbs-up bounce animated", timeout: 4000 });
                            } else {
                                $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
                            }
                        },
                        error: function (data) {
                            $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
                        }
                    });
                }
            });
        } else {
            $.smallBox({ title: "<b class='samanFont'>" + 'لطفا یک نماد را انتخاب نمایید' + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
        }
    }

</script>

