﻿using BaseGIS.Web.ViewModels;
using Mapsui.Styles;
using Mapsui.Styles.Thematics;
using Newtonsoft.Json;

namespace BaseGIS.Web.Utilities
{
    public static class MapsuiSymbologyConverter
    {
        /// <summary>
        /// Converts a hex color string (#RRGGBB or #AARRGGBB) to Mapsui.Styles.Color.
        /// </summary>
        public static Mapsui.Styles.Color HexToMapsuiColor(string hex)
        {
            if (string.IsNullOrEmpty(hex)) return Mapsui.Styles.Color.Gray;

            if (hex.StartsWith("#"))
            {
                try
                {
                    if (hex.Length == 9) // AARRGGBB
                    {
                        var a = byte.Parse(hex.Substring(1, 2), System.Globalization.NumberStyles.HexNumber);
                        var r = byte.Parse(hex.Substring(3, 2), System.Globalization.NumberStyles.HexNumber);
                        var g = byte.Parse(hex.Substring(5, 2), System.Globalization.NumberStyles.HexNumber);
                        var b = byte.Parse(hex.Substring(7, 2), System.Globalization.NumberStyles.HexNumber);
                        return new Mapsui.Styles.Color(r, g, b, a);
                    }
                    else if (hex.Length == 7) // RRGGBB
                    {
                        var r = byte.Parse(hex.Substring(1, 2), System.Globalization.NumberStyles.HexNumber);
                        var g = byte.Parse(hex.Substring(3, 2), System.Globalization.NumberStyles.HexNumber);
                        var b = byte.Parse(hex.Substring(5, 2), System.Globalization.NumberStyles.HexNumber);
                        return new Mapsui.Styles.Color(r, g, b, 255); // Opaque
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error converting hex '{hex}' to Mapsui.Styles.Color: {ex.Message}");
                }
            }
            return Mapsui.Styles.Color.Gray;
        }

        /// <summary>
        /// Converts System.Drawing.Color to Mapsui.Styles.Color.
        /// </summary>
        public static Mapsui.Styles.Color ToMapsuiColor(System.Drawing.Color sdColor)
        {
            return new Mapsui.Styles.Color(sdColor.R, sdColor.G, sdColor.B, sdColor.A);
        }

        /// <summary>
        /// Converts DotSpatial opacity (0.0-1.0) to Mapsui alpha (0-255).
        /// </summary>
        public static byte ConvertDotSpatialOpacityToMapsuiAlpha(float dotSpatialOpacity)
        {
            return (byte)(dotSpatialOpacity * 255);
        }

        /// <summary>
        /// Converts DotSpatial PointShape enum to Mapsui.Styles.SymbolType.
        /// </summary>
        public static Mapsui.Styles.SymbolType ConvertDotSpatialPointShapeToMapsuiSymbolType(ViewModels.PointShape pointShape)
        {
            switch (pointShape)
            {
                case ViewModels.PointShape.Ellipse: return Mapsui.Styles.SymbolType.Ellipse;
                case ViewModels.PointShape.Rectangle: return Mapsui.Styles.SymbolType.Rectangle;
                case ViewModels.PointShape.Triangle: return Mapsui.Styles.SymbolType.Triangle;
                // Mapsui doesn't have direct equivalents for Diamond, Hexagon, Pentagon, Star.
                // You might need to use custom SVG or default to Ellipse/Rectangle.
                case ViewModels.PointShape.Diamond: return Mapsui.Styles.SymbolType.Rectangle; // Or a custom SVG
                case ViewModels.PointShape.Hexagon: return Mapsui.Styles.SymbolType.Ellipse;
                case ViewModels.PointShape.Pentagon: return Mapsui.Styles.SymbolType.Ellipse;
                case ViewModels.PointShape.Star: return Mapsui.Styles.SymbolType.Ellipse;
                default: return Mapsui.Styles.SymbolType.Ellipse;
            }
        }

        /// <summary>
        /// Converts DotSpatial DashStyle to Mapsui.Styles.Pen.DashStyle.
        /// Mapsui only has a limited set. Custom requires DashArray.
        /// </summary>
        public static PenStyle ConvertDotSpatialDashStyleToMapsuiDashStyle(ViewModels.DashStyle dashStyle)
        {
            switch (dashStyle)
            {
                case ViewModels.DashStyle.Solid: return PenStyle.Solid;
                case ViewModels.DashStyle.Dash: return PenStyle.Dash;
                case ViewModels.DashStyle.Dot: return PenStyle.Dot;
                case ViewModels.DashStyle.DashDot: return PenStyle.DashDot;
                case ViewModels.DashStyle.DashDotDot: return PenStyle.DashDotDot;
                // Mapsui does not have a direct 'Custom' style enum.
                // For custom, you'd need to set Pen.DashArray based on your custom pattern.
                case ViewModels.DashStyle.Custom: return PenStyle.Solid; // Fallback
                default: return PenStyle.Solid;
            }
        }

        /// <summary>
        /// Creates a Mapsui.Styles.Pen from a DotSpatial-like Outline.
        /// </summary>
        public static Mapsui.Styles.Pen CreateMapsuiPenFromOutline(ViewModels.Outline outline)
        {
            if (outline == null) return null;

            var penColor = HexToMapsuiColor(outline.Color);
            penColor.A = ConvertDotSpatialOpacityToMapsuiAlpha(outline.Opacity);

            var pen = new Mapsui.Styles.Pen(penColor, outline.Width)
            {
                PenStyle = ConvertDotSpatialDashStyleToMapsuiDashStyle(outline.DashStyle)
                // Mapsui's Pen doesn't directly support OutlineColor/OutlineOpacity for internal outlines
                // The main color of the Pen IS its outline color.
            };
            return pen;
        }

        /// <summary>
        /// Creates a Mapsui.Styles.Font from DotSpatial-like font properties.
        /// Mapsui Font handles size and family. Bold/Italic are often part of family name.
        /// </summary>
        public static Mapsui.Styles.Font CreateMapsuiFont(string fontFamilyName, double size, ViewModels.FontStyle fontStyle)
        {
            string actualFontFamily = fontFamilyName;
            if (fontStyle.HasFlag(ViewModels.FontStyle.Bold)) actualFontFamily += " Bold";
            if (fontStyle.HasFlag(ViewModels.FontStyle.Italic)) actualFontFamily += " Italic";
            // Other styles (Underline, Strikeout) are not directly supported by Mapsui Font.

            return new Mapsui.Styles.Font { FontFamily = actualFontFamily, Size = size };
        }

        /// <summary>
        /// Creates a Mapsui.Styles.IStyle from a DotSpatial-like Symbol (for Point geometries).
        /// </summary>
        public static Mapsui.Styles.IStyle CreateMapsuiStyleFromDotSpatialSymbol(ViewModels.Symbol symbol)
        {
            if (symbol == null) return null;

            // Common properties
            var mapsuiOffset = new Mapsui.Styles.Offset(symbol.Offset?.X ?? 0, symbol.Offset?.Y ?? 0);
            var mapsuiRotation = symbol.Angle;
            var mapsuiOpacity = (float)symbol.Opacity;

            switch (symbol.SymbolType)
            {
                case ViewModels.SymbolType.Simple:
                    if (symbol is ViewModels.SimpleSymbol simpleSymbol)
                    {
                        var fillColor = HexToMapsuiColor(simpleSymbol.Color);
                        fillColor.A = ConvertDotSpatialOpacityToMapsuiAlpha(simpleSymbol.Opacity);

                        Mapsui.Styles.Pen outlinePen = null;
                        if (simpleSymbol.UseOutline)
                        {
                            var outlineColor = HexToMapsuiColor(simpleSymbol.OutlineColor);
                            outlineColor.A = ConvertDotSpatialOpacityToMapsuiAlpha(simpleSymbol.OutlineOpacity);
                            outlinePen = new Mapsui.Styles.Pen(outlineColor, simpleSymbol.OutlineWidth);
                        }

                        return new Mapsui.Styles.SymbolStyle
                        {
                            Fill = new Mapsui.Styles.Brush(fillColor),
                            SymbolScale = simpleSymbol.Size.Width / 32,
                            SymbolType = ConvertDotSpatialPointShapeToMapsuiSymbolType(simpleSymbol.PointShape),
                            Outline = outlinePen,
                            SymbolOffset = mapsuiOffset,
                            SymbolRotation = mapsuiRotation,
                            Opacity = mapsuiOpacity
                        };
                    }
                    break;
                case ViewModels.SymbolType.Character:
                    if (symbol is ViewModels.CharacterSymbol charSymbol)
                    {
                        var textColor = ToMapsuiColor(charSymbol.Color);
                        textColor.A = ConvertDotSpatialOpacityToMapsuiAlpha(charSymbol.Opacity);
                        return new Mapsui.Styles.LabelStyle
                        {
                            Text = charSymbol.Character.ToString(),
                            ForeColor = textColor,
                            Font = CreateMapsuiFont(charSymbol.FontFamilyName, charSymbol.Size.Height, charSymbol.Style),
                            // TextAlignment = Mapsui.Styles.LabelStyle.LineTextAlignment.Center, // Default alignment
                            // VerticalAlignment = Mapsui.Styles.LabelStyle.VerticalAlignment.Center,
                            Offset = mapsuiOffset, // Offset for label
                            //Orientation = Mapsui.Styles.LabelStyle.OrientationType.Rotated, // Enable rotation
                            //Rotation = mapsuiRotation,
                            Opacity = mapsuiOpacity
                        };
                    }
                    break;
                case ViewModels.SymbolType.Picture:
                    if (symbol is ViewModels.PictureSymbol picSymbol)
                    {
                        if (!string.IsNullOrEmpty(picSymbol.ImageBase64String))
                        {
                            try
                            {
                                var imageBytes = Convert.FromBase64String(picSymbol.ImageBase64String);
                                var bitmapId = BitmapRegistry.Instance.Register(imageBytes);

                                Mapsui.Styles.Pen outlinePen = null;
                                if (picSymbol.UseOutline)
                                {
                                    outlinePen = new Mapsui.Styles.Pen(ToMapsuiColor(picSymbol.OutlineColor), picSymbol.OutlineWidth)
                                    {
                                        Color = { A = ConvertDotSpatialOpacityToMapsuiAlpha(picSymbol.OutlineOpacity) }
                                    };
                                }

                                return new Mapsui.Styles.SymbolStyle
                                {
                                    BitmapId = bitmapId,
                                    //Size = picSymbol.Size.Width, // Assuming width and height are similar for sizing
                                    SymbolType = Mapsui.Styles.SymbolType.Image,
                                    SymbolRotation = mapsuiRotation,
                                    SymbolOffset = mapsuiOffset,
                                    Opacity = mapsuiOpacity,
                                    Outline = outlinePen
                                };
                            }
                            catch (Exception imgEx)
                            {
                                Console.WriteLine($"Error loading picture symbol image from Base64: {imgEx.Message}");
                                return null; // Let the caller use a default style
                            }
                        }
                    }
                    break;
            }
            return null; // Return null if conversion fails or type is unsupported
        }

        /// <summary>
        /// Creates a Mapsui.Styles.Brush from a DotSpatial-like Pattern (for Polygon geometries).
        /// </summary>
        public static new Mapsui.Styles.VectorStyle CreateMapsuiStyleFromDotSpatialPattern(ViewModels.Pattern pattern)
        {
            if (pattern == null) return null;
            var penColor = HexToMapsuiColor(pattern.Outline?.Color ?? "#000000");
            penColor.A = ConvertDotSpatialOpacityToMapsuiAlpha(pattern.Outline?.Opacity ?? 1.0f);
            var pen = new Mapsui.Styles.Pen(penColor, pattern.Outline?.Width ?? 1.0)
            {
                PenStyle = ConvertDotSpatialDashStyleToMapsuiDashStyle(pattern.Outline?.DashStyle ?? DashStyle.Solid)
            };
            switch (pattern.PatType)
            {
                case ViewModels.PatType.Simple:
                    if (pattern is ViewModels.SimplePattern simplePattern)
                    {
                        var fillColor = HexToMapsuiColor(simplePattern.FillColor);
                        fillColor.A = ConvertDotSpatialOpacityToMapsuiAlpha(simplePattern.Opacity);
                        var fill = new Mapsui.Styles.Brush(fillColor);

                        return new Mapsui.Styles.VectorStyle
                        {
                            Fill = fill,
                            Outline = pen
                        };
                    }
                    break;
                case ViewModels.PatType.Gradient:
                    if (pattern is ViewModels.GradientPattern gradientPattern)
                    {
                        // Mapsui has LinearGradientBrush and RadialGradientBrush.
                        // This is a simplified mapping. For full fidelity, you'd need to map start/end points.
                        var colors = gradientPattern.Colors?.Select(ToMapsuiColor).ToArray();
                        if (colors != null && colors.Length > 0)
                        {
                            // Mapsui doesn't directly support angle for simple Brush colors array,
                            // but LinearGradientBrush does.
                            // For a true LinearGradient:
                            // new Mapsui.Styles.LinearGradientBrush(new Mapsui.Styles.Color[] { color1, color2 }, new Mapsui.Styles.Offset(x1, y1), new Mapsui.Styles.Offset(x2, y2))
                            // You would need to convert angle to offset points.
                            // For simplicity, returning a solid color with the first color or a blend.
                            var fill = new Mapsui.Styles.Brush(colors.First()); // Use the first color for simplicity

                            return new Mapsui.Styles.VectorStyle
                            {
                                Fill = fill,
                                Outline = pen
                            };
                        }
                    }
                    break;
                case ViewModels.PatType.Hatch:
                    if (pattern is ViewModels.HatchPattern hatchPattern)
                    {
                        // Mapsui does not have a direct HatchPattern.
                        // This requires rendering the hatch to a bitmap and then using a BitmapBrush.
                        // This is a complex task. For simplicity, falling back to a solid color.
                        Console.WriteLine($"HatchPattern '{hatchPattern.HatchStyle}' not directly supported by Mapsui. Falling back to simple fill.");
                        var hatchColor = HexToMapsuiColor(hatchPattern.ForeColor);
                        hatchColor.A = ConvertDotSpatialOpacityToMapsuiAlpha(hatchPattern.ForeColorOpacity);
                        var fill = new Mapsui.Styles.Brush(hatchColor);
                        return new Mapsui.Styles.VectorStyle
                        {
                            Fill = fill,
                            Outline = pen
                        };
                    }
                    break;
                case ViewModels.PatType.Picture:
                    if (pattern is ViewModels.PicturePattern picPattern)
                    {
                        if (!string.IsNullOrEmpty(picPattern.ImageBase64String))
                        {
                            try
                            {
                                var imageBytes = Convert.FromBase64String(picPattern.ImageBase64String);
                                var bitmapId = BitmapRegistry.Instance.Register(imageBytes);
                                //return new Mapsui.Styles.Brush(bitmapId) { FillStyle = Mapsui.Styles.FillStyle.Bitmap };
                                var fill = new Mapsui.Styles.Brush(Mapsui.Styles.Color.Pink);
                                return new Mapsui.Styles.VectorStyle
                                {
                                    Fill = fill,
                                    Outline = pen
                                };
                            }
                            catch (Exception imgEx)
                            {
                                Console.WriteLine($"Error loading picture pattern image from Base64: {imgEx.Message}");
                                var fill = new Mapsui.Styles.Brush(Mapsui.Styles.Color.Pink); // Fallback
                                return new Mapsui.Styles.VectorStyle
                                {
                                    Fill = fill,
                                    Outline = pen
                                };
                            }
                        }
                    }
                    break;
            }
            return null; // Return null if conversion fails or type is unsupported
        }

        public static IStyle CreateMapsuiStyleFromLineCategory(LineCategory lineCat)
        {
            var penColor = HexToMapsuiColor(lineCat.Outline.Color);
            penColor.A = ConvertDotSpatialOpacityToMapsuiAlpha(lineCat.Outline.Opacity);

            var pen = new Mapsui.Styles.Pen(penColor, lineCat.Outline.Width)
            {
                PenStyle = ConvertDotSpatialDashStyleToMapsuiDashStyle(lineCat.Outline.DashStyle)
            };
            return new Mapsui.Styles.VectorStyle
            {
                Line = pen
            };
        }

        public static IStyle GetPolygonSimplePattern(SimplePattern simplePattern)
        {
            var fillColor = HexToMapsuiColor(simplePattern.FillColor);
            fillColor.A = ConvertDotSpatialOpacityToMapsuiAlpha(simplePattern.Opacity);
            var fill = new Mapsui.Styles.Brush(fillColor);

            var penColor = HexToMapsuiColor(simplePattern.Outline?.Color ?? "#000000");
            penColor.A = ConvertDotSpatialOpacityToMapsuiAlpha(simplePattern.Outline?.Opacity ?? 1.0f);
            var pen = new Mapsui.Styles.Pen(penColor, simplePattern.Outline?.Width ?? 1.0)
            {
                PenStyle = ConvertDotSpatialDashStyleToMapsuiDashStyle(simplePattern.Outline?.DashStyle ?? DashStyle.Solid)
            };

            return new Mapsui.Styles.VectorStyle
            {
                Fill = fill,
                Outline = pen
            };
        }

        /// <summary>
        /// Creates a default Mapsui style based on geometry type.
        /// </summary>
        public static Mapsui.Styles.IStyle CreateDefaultMapsuiStyle(string geometryType)
        {
            switch (geometryType?.ToLower())
            {
                case "point":
                    return new Mapsui.Styles.SymbolStyle
                    {
                        Fill = new Mapsui.Styles.Brush(Mapsui.Styles.Color.Blue),
                        SymbolScale = 0.3,
                        SymbolType = Mapsui.Styles.SymbolType.Ellipse,
                        Outline = new Mapsui.Styles.Pen(Mapsui.Styles.Color.Yellow, 1)
                    };
                case "linestring":
                case "multilinestring":
                case "polyline":
                    return new Mapsui.Styles.VectorStyle
                    {
                        Line = new Pen
                        {
                            Color = Color.Blue,
                            Width = 2.0
                        }
                    };
                case "polygon":
                case "multipolygon":
                    return new Mapsui.Styles.VectorStyle
                    {
                        Fill = new Mapsui.Styles.Brush(Mapsui.Styles.Color.FromArgb(128, 0, 255, 0)),
                        Outline = new Mapsui.Styles.Pen(Mapsui.Styles.Color.Black, 1)
                    };
                default:
                    Console.WriteLine($"Unknown geometry type '{geometryType}' for default style. Using a generic point style.");
                    return new Mapsui.Styles.SymbolStyle { Fill = new Mapsui.Styles.Brush(Mapsui.Styles.Color.Gray), SymbolScale = 0.3 };
            }
        }

        /// <summary>
        /// Parses a DotSpatial-like symbology JSON string and converts it to Mapsui.Styles.IStyle (or ThemeStyle).
        /// This is the core method replacing your SetLayerSymbology logic.
        /// </summary>
        /// <param name="json">The JSON string representing the symbology (from SymbologyInfo.Json).</param>
        /// <param name="layerGeometryTypeString">The geometry type of the layer (e.g., "Point", "LineString", "Polygon").</param>
        /// <param name="symbologyFieldName">The field name for unique/classified renders (from SymbologyInfo.FieldName).</param>
        /// <returns>A Mapsui.Styles.IStyle or ThemeStyle.</returns>
        public static Mapsui.Styles.IStyle CreateMapsuiStyleFromDotSpatialSymbologyJson(
            string json,
            string layerGeometryTypeString,
            string symbologyFieldName)
        {
            if (string.IsNullOrEmpty(json))
            {
                Console.WriteLine($"Symbology JSON is empty. Creating default style for {layerGeometryTypeString}.");
                return CreateDefaultMapsuiStyle(layerGeometryTypeString);
            }

            try
            {
                var settings = new JsonSerializerSettings
                {
                    TypeNameHandling = TypeNameHandling.Auto, // Required for polymorphic deserialization
                    NullValueHandling = NullValueHandling.Ignore
                };

                // Deserialize the root Symbology object from your ViewModels structure
                var symbologyDefinition = JsonConvert.DeserializeObject<ViewModels.Symbology>(json, settings);
                var symsObject = Newtonsoft.Json.JsonConvert.DeserializeObject(json);
                if (symbologyDefinition == null || symbologyDefinition.Categories == null || !symbologyDefinition.Categories.Any())
                {
                    Console.WriteLine($"Deserialized symbology is empty or has no categories. Creating default style for {layerGeometryTypeString}.");
                    return CreateDefaultMapsuiStyle(layerGeometryTypeString);
                }

                // Convert layerGeometryTypeString to enum for easier comparison
                if (!Enum.TryParse<ViewModels.GeometryType>(layerGeometryTypeString, true, out var layerGeometryTypeEnum))
                {
                    Console.WriteLine($"Invalid layer geometry type string: '{layerGeometryTypeString}'. Creating default style.");
                    return CreateDefaultMapsuiStyle(layerGeometryTypeString);
                }

                if (symbologyDefinition.GeometryType == GeometryType.Point)
                {
                    if (symbologyDefinition.RenderType == RenderType.Simple)
                    {
                        var cat = symbologyDefinition.Categories[0];
                        var catObject = ((Newtonsoft.Json.Linq.JContainer)(symsObject))["Categories"][0];
                        PointCategory ptc = Newtonsoft.Json.JsonConvert.DeserializeObject<PointCategory>(catObject.ToString());

                        if (ptc.Symbol == null)
                        {
                            return CreateDefaultMapsuiStyle("point");
                        }

                        if (ptc.Symbol.SymbolType == ViewModels.SymbolType.Simple)
                        {
                            var cSymbol = Newtonsoft.Json.JsonConvert.DeserializeObject<SimpleSymbol>(catObject["Symbol"].ToString());
                            return CreateMapsuiStyleFromDotSpatialSymbol(cSymbol);
                        }
                        else if (ptc.Symbol.SymbolType == ViewModels.SymbolType.Character)
                        {
                            var cSymbol = Newtonsoft.Json.JsonConvert.DeserializeObject<CharacterSymbol>(catObject["Symbol"].ToString());
                            return CreateMapsuiStyleFromDotSpatialSymbol(cSymbol);
                        }
                        else if (ptc.Symbol.SymbolType == ViewModels.SymbolType.Picture)
                        {
                            var cSymbol = Newtonsoft.Json.JsonConvert.DeserializeObject<PictureSymbol>(catObject["Symbol"].ToString());
                            return CreateMapsuiStyleFromDotSpatialSymbol(cSymbol);
                        }
                        else
                        {
                            return CreateDefaultMapsuiStyle("point");
                        }
                    }
                    else if (symbologyDefinition.RenderType == RenderType.Quantity || symbologyDefinition.RenderType == RenderType.Unique)
                    {
                        var cats = symbologyDefinition.Categories;
                        List<PointCategory> ptcs = new List<PointCategory>();
                        for (int ca = 0; ca < cats.Count; ca++)
                        {
                            var catObject = ((Newtonsoft.Json.Linq.JContainer)(symsObject))["Categories"][ca];
                            PointCategory ptc = Newtonsoft.Json.JsonConvert.DeserializeObject<PointCategory>(catObject.ToString());

                            if (ptc.Symbol == null)
                            {
                                continue; // Skip if symbol is null
                            }

                            if (ptc.Symbol.SymbolType == ViewModels.SymbolType.Simple)
                            {
                                var cSymbol = Newtonsoft.Json.JsonConvert.DeserializeObject<SimpleSymbol>(catObject["Symbol"].ToString());
                                ptc.Symbol = cSymbol;
                            }
                            else if (ptc.Symbol.SymbolType == ViewModels.SymbolType.Character)
                            {
                                var cSymbol = Newtonsoft.Json.JsonConvert.DeserializeObject<CharacterSymbol>(catObject["Symbol"].ToString());
                                ptc.Symbol = cSymbol;
                            }
                            else if (ptc.Symbol.SymbolType == ViewModels.SymbolType.Picture)
                            {
                                var cSymbol = Newtonsoft.Json.JsonConvert.DeserializeObject<PictureSymbol>(catObject["Symbol"].ToString());
                                ptc.Symbol = cSymbol;
                            }
                            ptcs.Add(ptc);
                        }

                        if (!ptcs.Any())
                        {
                            return CreateDefaultMapsuiStyle("point");
                        }

                        // Create a ThemeStyle for Unique or Quantity rendering
                        return new ThemeStyle(feature =>
                        {
                            if (feature == null || !feature.Fields.Contains(symbologyFieldName))
                            {
                                return CreateDefaultMapsuiStyle("point");
                            }

                            var featureValue = feature[symbologyFieldName]?.ToString();
                            if (string.IsNullOrEmpty(featureValue))
                            {
                                return CreateDefaultMapsuiStyle("point");
                            }

                            var matchingCategory = ptcs.FirstOrDefault(cat =>
                                (symbologyDefinition.RenderType == RenderType.Unique &&
                                 IsMatchForUnique(cat.FilterExpression, featureValue)) ||
                                (symbologyDefinition.RenderType == RenderType.Quantity &&
                                 TryParseQuantityRange(cat.FilterExpression, out double from, out double to) &&
                                 double.TryParse(featureValue, out double value) && value >= from && value <= to));

                            return matchingCategory != null
                                ? CreateMapsuiStyleFromDotSpatialSymbol(matchingCategory.Symbol)
                                : CreateDefaultMapsuiStyle("point");
                        });
                    }
                }
                else if (symbologyDefinition.GeometryType == GeometryType.Polyline)
                {
                    if (symbologyDefinition.RenderType == RenderType.Simple)
                    {
                        var catObject = ((Newtonsoft.Json.Linq.JContainer)(symsObject))["Categories"][0];
                        ViewModels.LineCategory lineCat = Newtonsoft.Json.JsonConvert.DeserializeObject<ViewModels.LineCategory>(catObject.ToString());
                        if (lineCat.Outline == null)
                        {
                            return CreateDefaultMapsuiStyle("polyline");
                        }

                        return CreateMapsuiStyleFromLineCategory(lineCat);
                    }
                    else
                    {
                        var cats = symbologyDefinition.Categories;
                        List<LineCategory> ptcs = new List<LineCategory>();
                        for (int ca = 0; ca < cats.Count; ca++)
                        {
                            var catObject = ((Newtonsoft.Json.Linq.JContainer)(symsObject))["Categories"][ca];
                            LineCategory ptc = Newtonsoft.Json.JsonConvert.DeserializeObject<LineCategory>(catObject.ToString());

                            if (ptc.Outline == null)
                            {
                                continue; // Skip if outline is null
                            }

                            ptcs.Add(ptc);
                        }

                        if (!ptcs.Any())
                        {
                            return CreateDefaultMapsuiStyle("polyline");
                        }

                        // Create a ThemeStyle for Unique or Quantity rendering
                        return new ThemeStyle(feature =>
                        {
                            if (feature == null || !feature.Fields.Contains(symbologyFieldName))
                            {
                                return CreateDefaultMapsuiStyle("polyline");
                            }

                            var featureValue = feature[symbologyFieldName]?.ToString();
                            if (string.IsNullOrEmpty(featureValue))
                            {
                                return CreateDefaultMapsuiStyle("polyline");
                            }

                            var matchingCategory = ptcs.FirstOrDefault(cat =>
                                (symbologyDefinition.RenderType == RenderType.Unique &&
                                 IsMatchForUnique(cat.FilterExpression, featureValue)) ||
                                (symbologyDefinition.RenderType == RenderType.Quantity &&
                                 TryParseQuantityRange(cat.FilterExpression, out double from, out double to) &&
                                 double.TryParse(featureValue, out double value) && value >= from && value <= to));
                            return matchingCategory != null
                                ? CreateMapsuiStyleFromLineCategory(matchingCategory)
                                : CreateDefaultMapsuiStyle("polyline");
                        });
                    }
                }
                else if (symbologyDefinition.GeometryType == GeometryType.Polygon)
                {
                    if (symbologyDefinition.RenderType == RenderType.Simple)
                    {
                        var catObject = ((Newtonsoft.Json.Linq.JContainer)(symsObject))["Categories"][0];
                        ViewModels.PolygonCategory polygonCat = Newtonsoft.Json.JsonConvert.DeserializeObject<ViewModels.PolygonCategory>(catObject.ToString());
                        if (polygonCat.Pattern == null)
                        {
                            return CreateDefaultMapsuiStyle("polygon");
                        }

                        switch (polygonCat.Pattern.PatType)
                        {
                            case PatType.Simple:
                                var simplePattern = Newtonsoft.Json.JsonConvert.DeserializeObject<SimplePattern>(catObject["Pattern"].ToString());
                                return GetPolygonSimplePattern(simplePattern);

                            case PatType.Gradient:
                                var gradientPattern = Newtonsoft.Json.JsonConvert.DeserializeObject<GradientPattern>(catObject["Pattern"].ToString());
                                // Mapsui supports LinearGradientBrush, but requires start/end points
                                var firstColor = gradientPattern.Colors?.Length > 0 ? ToMapsuiColor(gradientPattern.Colors[0]) : Mapsui.Styles.Color.Gray;
                                //firstColor.A = ConvertDotSpatialOpacityToMapsuiAlpha(gradientPattern.Opacity ?? 1.0f);
                                return new Mapsui.Styles.VectorStyle
                                {
                                    Fill = new Mapsui.Styles.Brush(firstColor), // Simplified to first color
                                    Outline = CreateMapsuiPenFromOutline(polygonCat.Outline)
                                };

                            case PatType.Hatch:
                                var hatchPattern = Newtonsoft.Json.JsonConvert.DeserializeObject<HatchPattern>(catObject["Pattern"].ToString());
                                var hatchColor = HexToMapsuiColor(hatchPattern.ForeColor);
                                hatchColor.A = ConvertDotSpatialOpacityToMapsuiAlpha(hatchPattern.ForeColorOpacity);
                                return new Mapsui.Styles.VectorStyle
                                {
                                    Fill = new Mapsui.Styles.Brush(hatchColor),
                                    Outline = CreateMapsuiPenFromOutline(polygonCat.Outline)
                                };

                            case PatType.Picture:
                                var picturePattern = Newtonsoft.Json.JsonConvert.DeserializeObject<PicturePattern>(catObject["Pattern"].ToString());
                                if (!string.IsNullOrEmpty(picturePattern.ImageBase64String))
                                {
                                    try
                                    {
                                        var imageBytes = Convert.FromBase64String(picturePattern.ImageBase64String);
                                        var bitmapId = BitmapRegistry.Instance.Register(imageBytes);
                                        return new Mapsui.Styles.VectorStyle
                                        {
                                            Fill = new Mapsui.Styles.Brush(Mapsui.Styles.Color.FromArgb(128, 255, 0, 0)), // Placeholder
                                            Outline = CreateMapsuiPenFromOutline(polygonCat.Outline)
                                        };
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"Error loading picture pattern: {ex.Message}");
                                        return CreateDefaultMapsuiStyle("polygon");
                                    }
                                }
                                break;
                        }

                        return CreateDefaultMapsuiStyle("polygon");
                    }
                    else
                    {
                        var cats = symbologyDefinition.Categories;
                        List<PolygonCategory> ptcs = new List<PolygonCategory>();
                        for (int ca = 0; ca < cats.Count; ca++)
                        {
                            var catObject = ((Newtonsoft.Json.Linq.JContainer)(symsObject))["Categories"][ca];
                            PolygonCategory ptc = Newtonsoft.Json.JsonConvert.DeserializeObject<PolygonCategory>(catObject.ToString());
                            if (ptc.Pattern == null)
                            {
                                continue; // Skip if symbol is null
                            }

                            if (ptc.Pattern.PatType == ViewModels.PatType.Simple)
                            {
                                var pat = Newtonsoft.Json.JsonConvert.DeserializeObject<SimplePattern>(catObject["Pattern"].ToString());
                                ptc.Pattern = pat;
                            }
                            else if (ptc.Pattern.PatType == ViewModels.PatType.Gradient)
                            {
                                var pat = Newtonsoft.Json.JsonConvert.DeserializeObject<GradientPattern>(catObject["Pattern"].ToString());
                                ptc.Pattern = pat;
                            }
                            else if (ptc.Pattern.PatType == ViewModels.PatType.Hatch)
                            {
                                var pat = Newtonsoft.Json.JsonConvert.DeserializeObject<HatchPattern>(catObject["Pattern"].ToString());
                                ptc.Pattern = pat;
                            }
                            else if (ptc.Pattern.PatType == ViewModels.PatType.Picture)
                            {
                                var pat = Newtonsoft.Json.JsonConvert.DeserializeObject<PicturePattern>(catObject["Pattern"].ToString());
                                ptc.Pattern = pat;
                            }
                            if (ptc.Pattern == null)
                            {
                                continue;
                            }
                            ptcs.Add(ptc);
                        }


                        if (!ptcs.Any())
                        {
                            return CreateDefaultMapsuiStyle("polygon");
                        }
                        return new ThemeStyle(feature =>
                        {
                            if (feature == null || !feature.Fields.Contains(symbologyFieldName))
                            {
                                return CreateDefaultMapsuiStyle("polygon");
                            }

                            var featureValue = feature[symbologyFieldName]?.ToString();
                            if (string.IsNullOrEmpty(featureValue))
                            {
                                return CreateDefaultMapsuiStyle("polygon");
                            }

                            var matchingCategory = ptcs.FirstOrDefault(cat =>
                                (symbologyDefinition.RenderType == RenderType.Unique &&
                                 IsMatchForUnique(cat.FilterExpression, featureValue)) ||
                                (symbologyDefinition.RenderType == RenderType.Quantity &&
                                 TryParseQuantityRange(cat.FilterExpression, out double from, out double to) &&
                                 double.TryParse(featureValue, out double value) && value >= from && value <= to));
                            return matchingCategory != null
                                ? CreateMapsuiStyleFromDotSpatialPattern(matchingCategory.Pattern)
                                : CreateDefaultMapsuiStyle("polygon");
                        });
                    }
                }
                var defaultStyle = CreateDefaultMapsuiStyle(layerGeometryTypeString);
                return defaultStyle;
            }
            catch (JsonSerializationException ex)
            {
                Console.WriteLine($"JSON deserialization error for symbology: {ex.Message}. JSON: {json}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing symbology: {ex.Message}");
            }

            // Fallback for any parsing errors
            return CreateDefaultMapsuiStyle(layerGeometryTypeString);
        }
        public static bool IsMatchForUnique(string filterExpression, string featureValue)
        {
            if (string.IsNullOrEmpty(filterExpression) || string.IsNullOrEmpty(featureValue)) return false;

            // Remove extra spaces
            filterExpression = filterExpression.Replace(" ", "");

            // Check for equality or inequality condition (e.g., [ObjectId] = 1 or [ObjectId] != 1)
            var equalityMatch = System.Text.RegularExpressions.Regex.Match(filterExpression, @"\[(.*?)\]\s*(=|<|>|!=)\s*(['""]?([^'""\s]+)['""]?|\d+\.?\d*)");
            if (equalityMatch.Success)
            {
                var field = equalityMatch.Groups[1].Value;
                var op = equalityMatch.Groups[2].Value;
                var value = equalityMatch.Groups[3].Value.Trim('\'', '"');

                if (op == "=")
                {
                    return value.Equals(featureValue, StringComparison.OrdinalIgnoreCase);
                }
                else if (op == "!=")
                {
                    return !value.Equals(featureValue, StringComparison.OrdinalIgnoreCase);
                }
                else if (double.TryParse(value, out double numericValue) && double.TryParse(featureValue, out double featureNumericValue))
                {
                    if (op == ">") return featureNumericValue > numericValue;
                    if (op == "<") return featureNumericValue < numericValue;
                }
                // If numeric comparison fails, fall back to string comparison for > and <
                else if (op == ">") return string.Compare(featureValue, value, StringComparison.OrdinalIgnoreCase) > 0;
                else if (op == "<") return string.Compare(featureValue, value, StringComparison.OrdinalIgnoreCase) < 0;
            }

            // Check for LIKE condition (e.g., [Title] LIKE '%فرودگاه%')
            var likeMatch = System.Text.RegularExpressions.Regex.Match(filterExpression, @"\[(.*?)\]\s*LIKE\s*['""]?(.+?)['""]?");
            if (likeMatch.Success)
            {
                var field = likeMatch.Groups[1].Value;
                var pattern = likeMatch.Groups[2].Value;
                string regexPattern = "^" + pattern.Replace("%", ".*").Replace("_", ".") + "$";
                return System.Text.RegularExpressions.Regex.IsMatch(featureValue, regexPattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            }

            return false;
        }
        public static bool TryParseQuantityRange(string filterExpression, out double from, out double to)
        {
            from = double.MinValue;
            to = double.MaxValue;

            if (string.IsNullOrEmpty(filterExpression)) return false;

            // Remove extra spaces for better parsing
            filterExpression = filterExpression.Replace(" ", "");

            // Try to find a range with AND condition (e.g., 50<=[Id]AND[Id]<=100)
            var andMatch = System.Text.RegularExpressions.Regex.Match(filterExpression, @"(\d+\.?\d*)(<=|>=|<|>)\[(.*?)\]AND\[(.*?)\](<=|>=|<|>)([\d.]+)");
            if (andMatch.Success)
            {
                var value1Str = andMatch.Groups[1].Value; // First number
                var op1 = andMatch.Groups[2].Value;
                var field1 = andMatch.Groups[3].Value;
                var field2 = andMatch.Groups[4].Value;
                var op2 = andMatch.Groups[5].Value;
                var value2Str = andMatch.Groups[6].Value;

                // Check if both conditions apply to the same field
                if (field1 == field2 && double.TryParse(value1Str, out double value1) && double.TryParse(value2Str, out double value2))
                {
                    // Determine from and to based on operators
                    var bounds = new List<(double Value, string Op)>
            {
                (value1, op1),
                (value2, op2)
            };

                    var lowerBound = bounds.Where(b => b.Op == ">=" || b.Op == ">").Select(b => b.Value).DefaultIfEmpty(double.MaxValue).Min();
                    var upperBound = bounds.Where(b => b.Op == "<=" || b.Op == "<").Select(b => b.Value).DefaultIfEmpty(double.MinValue).Max();

                    // If no >= or >, use the minimum value with <= or <
                    if (lowerBound == double.MaxValue) lowerBound = bounds.Where(b => b.Op == "<=" || b.Op == "<").Min(b => b.Value);
                    // If no <= or <, use the maximum value with >= or >
                    if (upperBound == double.MinValue) upperBound = bounds.Where(b => b.Op == ">=" || b.Op == ">").Max(b => b.Value);

                    from = lowerBound;
                    to = upperBound;

                    // Ensure from is less than or equal to to
                    if (from > to)
                    {
                        (from, to) = (to, from); // Swap if necessary
                    }

                    return true;
                }
            }

            // Try to find a single condition with number after field (e.g., [ObjectId]<50000)
            var singleMatchAfter = System.Text.RegularExpressions.Regex.Match(filterExpression, @"\[(.*?)\](<=|>=|<|>)([\d.]+)");
            if (singleMatchAfter.Success)
            {
                var field = singleMatchAfter.Groups[1].Value;
                var op = singleMatchAfter.Groups[2].Value;
                var valueStr = singleMatchAfter.Groups[3].Value;
                if (double.TryParse(valueStr, out double value))
                {
                    if (op == ">=" || op == ">") to = value; // Reverse logic for upper bound
                    else if (op == "<=" || op == "<") from = value; // Reverse logic for lower bound
                    return true;
                }
            }

            // Try to find a single condition with number before field (e.g., 50000>=[ObjectId])
            var singleMatchBefore = System.Text.RegularExpressions.Regex.Match(filterExpression, @"(\d+\.?\d*)(<=|>=|<|>)\[(.*?)\]");
            if (singleMatchBefore.Success)
            {
                var valueStr = singleMatchBefore.Groups[1].Value;
                var op = singleMatchBefore.Groups[2].Value;
                if (double.TryParse(valueStr, out double value))
                {
                    if (op == ">=" || op == ">") from = value;
                    else if (op == "<=" || op == "<") to = value;
                    return true;
                }
            }

            return false;
        }




    }
}