/**
 * Property Tools Component
 * کامپوننت ابزارهای خصوصیات
 */

class PropertyTools extends BaseComponent {
    getDefaultOptions() {
        return {
            ...super.getDefaultOptions(),
            showGeometry: true,
            showAttributes: true,
            showCoordinates: true,
            showArea: true,
            showLength: true,
            coordinateFormat: 'decimal', // decimal, dms
            areaUnit: 'square_meters', // square_meters, hectares, square_kilometers
            lengthUnit: 'meters', // meters, kilometers, feet, miles
            maxAttributeLength: 100,
            onPropertyShow: null,
            onPropertyHide: null,
            onPropertyUpdate: null,
            onFeatureSelect: null,
            onFeatureDeselect: null
        };
    }

    beforeInit() {
        this.map = window.map;
        this.editableLayers = window.editableLayers;
        this.selectedFeature = null;
        this.propertyPanel = null;
        this.isVisible = false;
        this.currentLayer = null;
        this.attributeData = {};
    }

    render() {
        this.renderToolbar();
        this.renderPropertyPanel();
    }

    renderToolbar() {
        const toolbar = this.createToolbar();
        this.element.appendChild(toolbar);
    }

    createToolbar() {
        const toolbar = document.createElement('div');
        toolbar.className = 'property-tools-toolbar';
        toolbar.innerHTML = `
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary property-btn" 
                        title="نمایش خصوصیات">
                    <i class="fas fa-info-circle"></i>
                </button>
                <button type="button" class="btn btn-outline-secondary identify-btn" 
                        title="شناسایی عارضه">
                    <i class="fas fa-search"></i>
                </button>
                <button type="button" class="btn btn-outline-info settings-btn" 
                        title="تنظیمات نمایش">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        `;
        return toolbar;
    }

    renderPropertyPanel() {
        const panel = this.createPropertyPanel();
        document.body.appendChild(panel);
    }

    createPropertyPanel() {
        const panel = document.createElement('div');
        panel.className = 'property-panel';
        panel.id = 'propertyPanel';
        panel.innerHTML = `
            <div class="panel-header">
                <h5>خصوصیات عارضه</h5>
                <div class="panel-controls">
                    <button type="button" class="btn btn-sm btn-outline-secondary" 
                            data-action="minimize" title="کوچک کردن">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" 
                            data-action="close" title="بستن">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="panel-body">
                <div class="property-content">
                    <div class="no-selection text-center text-muted">
                        <i class="fas fa-mouse-pointer fa-2x mb-2"></i>
                        <p>روی یک عارضه کلیک کنید تا خصوصیات آن نمایش داده شود</p>
                    </div>
                </div>
            </div>
            <div class="panel-footer">
                <div class="panel-actions">
                    <button type="button" class="btn btn-sm btn-primary" 
                            data-action="edit" style="display: none;">
                        <i class="fas fa-edit"></i> ویرایش
                    </button>
                    <button type="button" class="btn btn-sm btn-success" 
                            data-action="save" style="display: none;">
                        <i class="fas fa-save"></i> ذخیره
                    </button>
                    <button type="button" class="btn btn-sm btn-secondary" 
                            data-action="cancel" style="display: none;">
                        <i class="fas fa-times"></i> انصراف
                    </button>
                </div>
            </div>
        `;
        return panel;
    }

    bindEvents() {
        super.bindEvents();

        // Toolbar events
        this.bindToolbarEvents();
        
        // Panel events
        this.bindPanelEvents();
        
        // Map events
        this.bindMapEvents();
    }

    bindToolbarEvents() {
        // Property button
        this.find('.property-btn')?.addEventListener('click', () => {
            this.togglePropertyPanel();
        });

        // Identify button
        this.find('.identify-btn')?.addEventListener('click', () => {
            this.toggleIdentifyMode();
        });

        // Settings button
        this.find('.settings-btn')?.addEventListener('click', () => {
            this.showSettingsDialog();
        });
    }

    bindPanelEvents() {
        const panel = document.getElementById('propertyPanel');
        if (!panel) return;

        // Panel control buttons
        panel.querySelectorAll('[data-action]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.handlePanelAction(action);
            });
        });

        // Make panel draggable
        this.makePanelDraggable(panel);
    }

    bindMapEvents() {
        if (!this.map) return;

        // Feature click event
        this.map.on('click', (e) => {
            if (this.isIdentifyMode) {
                this.identifyFeature(e);
            }
        });

        // Layer events
        if (this.editableLayers) {
            this.editableLayers.on('click', (e) => {
                this.selectFeature(e.layer);
            });
        }
    }

    // ========================================
    // Property Panel Management
    // ========================================

    togglePropertyPanel() {
        const panel = document.getElementById('propertyPanel');
        if (!panel) return;

        if (this.isVisible) {
            this.hidePropertyPanel();
        } else {
            this.showPropertyPanel();
        }
    }

    showPropertyPanel() {
        const panel = document.getElementById('propertyPanel');
        if (!panel) return;

        panel.style.display = 'block';
        this.isVisible = true;

        // Update button state
        const propertyBtn = this.find('.property-btn');
        if (propertyBtn) {
            propertyBtn.classList.add('active');
        }

        if (this.options.onPropertyShow) {
            this.options.onPropertyShow();
        }

        this.trigger('propertyShow');
    }

    hidePropertyPanel() {
        const panel = document.getElementById('propertyPanel');
        if (!panel) return;

        panel.style.display = 'none';
        this.isVisible = false;

        // Update button state
        const propertyBtn = this.find('.property-btn');
        if (propertyBtn) {
            propertyBtn.classList.remove('active');
        }

        if (this.options.onPropertyHide) {
            this.options.onPropertyHide();
        }

        this.trigger('propertyHide');
    }

    handlePanelAction(action) {
        switch (action) {
            case 'minimize':
                this.minimizePanel();
                break;
            case 'close':
                this.hidePropertyPanel();
                break;
            case 'edit':
                this.enableEditMode();
                break;
            case 'save':
                this.saveChanges();
                break;
            case 'cancel':
                this.cancelEdit();
                break;
        }
    }

    minimizePanel() {
        const panel = document.getElementById('propertyPanel');
        if (!panel) return;

        const body = panel.querySelector('.panel-body');
        const footer = panel.querySelector('.panel-footer');
        
        if (body && footer) {
            const isMinimized = body.style.display === 'none';
            body.style.display = isMinimized ? 'block' : 'none';
            footer.style.display = isMinimized ? 'block' : 'none';
            
            const minimizeBtn = panel.querySelector('[data-action="minimize"] i');
            if (minimizeBtn) {
                minimizeBtn.className = isMinimized ? 'fas fa-minus' : 'fas fa-plus';
            }
        }
    }

    makePanelDraggable(panel) {
        const header = panel.querySelector('.panel-header');
        if (!header) return;

        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;

        header.addEventListener('mousedown', (e) => {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;

            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
                header.style.cursor = 'grabbing';
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;

                xOffset = currentX;
                yOffset = currentY;

                panel.style.transform = `translate(${currentX}px, ${currentY}px)`;
            }
        });

        document.addEventListener('mouseup', () => {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
            header.style.cursor = 'grab';
        });

        header.style.cursor = 'grab';
    }

    // ========================================
    // Feature Identification
    // ========================================

    toggleIdentifyMode() {
        this.isIdentifyMode = !this.isIdentifyMode;
        
        const identifyBtn = this.find('.identify-btn');
        if (identifyBtn) {
            if (this.isIdentifyMode) {
                identifyBtn.classList.add('active');
                this.map.getContainer().style.cursor = 'crosshair';
                this.showToast('حالت شناسایی فعال شد - روی عارضه کلیک کنید', 'info');
            } else {
                identifyBtn.classList.remove('active');
                this.map.getContainer().style.cursor = '';
                this.showToast('حالت شناسایی غیرفعال شد', 'info');
            }
        }
    }

    identifyFeature(e) {
        const latlng = e.latlng;
        let foundFeature = null;

        // Search in editable layers
        if (this.editableLayers) {
            this.editableLayers.eachLayer((layer) => {
                if (this.isPointInLayer(latlng, layer)) {
                    foundFeature = layer;
                    return false; // Break the loop
                }
            });
        }

        if (foundFeature) {
            this.selectFeature(foundFeature);
            this.showPropertyPanel();
        } else {
            this.showToast('هیچ عارضه‌ای در این موقعیت یافت نشد', 'warning');
        }
    }

    isPointInLayer(latlng, layer) {
        if (layer instanceof L.Marker) {
            const markerLatLng = layer.getLatLng();
            const distance = latlng.distanceTo(markerLatLng);
            return distance < 10; // 10 meters tolerance
        } else if (layer instanceof L.Polygon || layer instanceof L.Polyline) {
            // Use Leaflet's built-in method if available
            if (layer.getBounds && layer.getBounds().contains(latlng)) {
                return true;
            }
        } else if (layer instanceof L.Circle) {
            const center = layer.getLatLng();
            const radius = layer.getRadius();
            const distance = latlng.distanceTo(center);
            return distance <= radius;
        }
        return false;
    }

    selectFeature(feature) {
        // Deselect previous feature
        if (this.selectedFeature) {
            this.deselectFeature();
        }

        this.selectedFeature = feature;
        this.currentLayer = feature;

        // Highlight feature
        this.highlightFeature(feature);

        // Load feature properties
        this.loadFeatureProperties(feature);

        // Show property panel
        if (!this.isVisible) {
            this.showPropertyPanel();
        }

        if (this.options.onFeatureSelect) {
            this.options.onFeatureSelect(feature);
        }

        this.trigger('featureSelect', { feature });
    }

    deselectFeature() {
        if (this.selectedFeature) {
            this.unhighlightFeature(this.selectedFeature);
            
            if (this.options.onFeatureDeselect) {
                this.options.onFeatureDeselect(this.selectedFeature);
            }

            this.trigger('featureDeselect', { feature: this.selectedFeature });
        }

        this.selectedFeature = null;
        this.currentLayer = null;
        this.clearPropertyContent();
    }

    highlightFeature(feature) {
        if (feature.setStyle) {
            feature.originalStyle = {
                color: feature.options.color,
                weight: feature.options.weight,
                fillColor: feature.options.fillColor,
                fillOpacity: feature.options.fillOpacity
            };

            feature.setStyle({
                color: '#ff0000',
                weight: 3,
                fillColor: '#ff0000',
                fillOpacity: 0.5
            });
        }
    }

    unhighlightFeature(feature) {
        if (feature.setStyle && feature.originalStyle) {
            feature.setStyle(feature.originalStyle);
            delete feature.originalStyle;
        }
    }

    // ========================================
    // Property Content Management
    // ========================================

    loadFeatureProperties(feature) {
        const content = this.generatePropertyContent(feature);
        this.updatePropertyContent(content);
        this.showEditButton();
    }

    generatePropertyContent(feature) {
        const properties = this.extractFeatureProperties(feature);
        const geometry = this.extractGeometryInfo(feature);

        let content = '<div class="property-sections">';

        // Basic Info Section
        content += this.generateBasicInfoSection(feature);

        // Geometry Section
        if (this.options.showGeometry) {
            content += this.generateGeometrySection(geometry);
        }

        // Attributes Section
        if (this.options.showAttributes && properties) {
            content += this.generateAttributesSection(properties);
        }

        // Coordinates Section
        if (this.options.showCoordinates) {
            content += this.generateCoordinatesSection(feature);
        }

        content += '</div>';
        return content;
    }

    generateBasicInfoSection(feature) {
        const featureType = this.getFeatureType(feature);
        const featureId = feature.id || feature._leaflet_id || 'نامشخص';

        return `
            <div class="property-section">
                <h6 class="section-title">
                    <i class="fas fa-info-circle"></i> اطلاعات کلی
                </h6>
                <div class="section-content">
                    <div class="property-item">
                        <span class="property-label">نوع:</span>
                        <span class="property-value">${featureType}</span>
                    </div>
                    <div class="property-item">
                        <span class="property-label">شناسه:</span>
                        <span class="property-value">${featureId}</span>
                    </div>
                    <div class="property-item">
                        <span class="property-label">تاریخ ایجاد:</span>
                        <span class="property-value">${feature.createdAt ? new Date(feature.createdAt).toLocaleDateString('fa-IR') : 'نامشخص'}</span>
                    </div>
                </div>
            </div>
        `;
    }

    generateGeometrySection(geometry) {
        let content = `
            <div class="property-section">
                <h6 class="section-title">
                    <i class="fas fa-shapes"></i> هندسه
                </h6>
                <div class="section-content">
                    <div class="property-item">
                        <span class="property-label">نوع هندسه:</span>
                        <span class="property-value">${geometry.type}</span>
                    </div>
        `;

        if (geometry.area && this.options.showArea) {
            content += `
                <div class="property-item">
                    <span class="property-label">مساحت:</span>
                    <span class="property-value">${this.formatArea(geometry.area)}</span>
                </div>
            `;
        }

        if (geometry.length && this.options.showLength) {
            content += `
                <div class="property-item">
                    <span class="property-label">طول:</span>
                    <span class="property-value">${this.formatLength(geometry.length)}</span>
                </div>
            `;
        }

        if (geometry.coordinates) {
            content += `
                <div class="property-item">
                    <span class="property-label">تعداد نقاط:</span>
                    <span class="property-value">${geometry.coordinates}</span>
                </div>
            `;
        }

        content += '</div></div>';
        return content;
    }

    generateAttributesSection(properties) {
        let content = `
            <div class="property-section">
                <h6 class="section-title">
                    <i class="fas fa-list"></i> ویژگی‌ها
                </h6>
                <div class="section-content">
        `;

        for (const [key, value] of Object.entries(properties)) {
            if (key !== 'geometry' && key !== 'type') {
                const displayValue = this.formatAttributeValue(value);
                content += `
                    <div class="property-item" data-attribute="${key}">
                        <span class="property-label">${key}:</span>
                        <span class="property-value" data-editable="true">${displayValue}</span>
                    </div>
                `;
            }
        }

        content += '</div></div>';
        return content;
    }

    generateCoordinatesSection(feature) {
        const coordinates = this.extractCoordinates(feature);

        let content = `
            <div class="property-section">
                <h6 class="section-title">
                    <i class="fas fa-map-marker-alt"></i> مختصات
                </h6>
                <div class="section-content">
        `;

        coordinates.forEach((coord, index) => {
            const formatted = this.formatCoordinate(coord.lat, coord.lng);
            content += `
                <div class="property-item">
                    <span class="property-label">${coordinates.length > 1 ? `نقطه ${index + 1}:` : 'موقعیت:'}</span>
                    <span class="property-value">${formatted}</span>
                </div>
            `;
        });

        content += '</div></div>';
        return content;
    }

    updatePropertyContent(content) {
        const panel = document.getElementById('propertyPanel');
        if (!panel) return;

        const propertyContent = panel.querySelector('.property-content');
        if (propertyContent) {
            propertyContent.innerHTML = content;
        }
    }

    clearPropertyContent() {
        const panel = document.getElementById('propertyPanel');
        if (!panel) return;

        const propertyContent = panel.querySelector('.property-content');
        if (propertyContent) {
            propertyContent.innerHTML = `
                <div class="no-selection text-center text-muted">
                    <i class="fas fa-mouse-pointer fa-2x mb-2"></i>
                    <p>روی یک عارضه کلیک کنید تا خصوصیات آن نمایش داده شود</p>
                </div>
            `;
        }
        this.hideEditButton();
    }

    // ========================================
    // Edit Mode Management
    // ========================================

    enableEditMode() {
        if (!this.selectedFeature) return;

        const panel = document.getElementById('propertyPanel');
        if (!panel) return;

        // Make attribute values editable
        const editableElements = panel.querySelectorAll('[data-editable="true"]');
        editableElements.forEach(element => {
            const currentValue = element.textContent;
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentValue;
            input.className = 'form-control form-control-sm';
            input.dataset.originalValue = currentValue;

            element.replaceWith(input);
        });

        // Show save/cancel buttons
        this.showEditButtons();
        this.hideEditButton();
    }

    saveChanges() {
        if (!this.selectedFeature) return;

        const panel = document.getElementById('propertyPanel');
        if (!panel) return;

        // Collect changed values
        const inputs = panel.querySelectorAll('input[data-original-value]');
        const changes = {};
        let hasChanges = false;

        inputs.forEach(input => {
            const originalValue = input.dataset.originalValue;
            const newValue = input.value;

            if (originalValue !== newValue) {
                const attributeName = input.closest('[data-attribute]')?.dataset.attribute;
                if (attributeName) {
                    changes[attributeName] = newValue;
                    hasChanges = true;
                }
            }
        });

        if (hasChanges) {
            // Apply changes to feature
            this.applyChangesToFeature(changes);

            // Reload property display
            this.loadFeatureProperties(this.selectedFeature);

            this.showToast('تغییرات با موفقیت ذخیره شد', 'success');

            if (this.options.onPropertyUpdate) {
                this.options.onPropertyUpdate(this.selectedFeature, changes);
            }

            this.trigger('propertyUpdate', { feature: this.selectedFeature, changes });
        } else {
            this.cancelEdit();
        }
    }

    cancelEdit() {
        if (!this.selectedFeature) return;

        // Reload original property display
        this.loadFeatureProperties(this.selectedFeature);
    }

    applyChangesToFeature(changes) {
        if (!this.selectedFeature) return;

        // Update feature properties
        for (const [key, value] of Object.entries(changes)) {
            if (this.selectedFeature.feature && this.selectedFeature.feature.properties) {
                this.selectedFeature.feature.properties[key] = value;
            } else if (this.selectedFeature.options) {
                this.selectedFeature.options[key] = value;
            }
        }

        // Update popup if exists
        if (this.selectedFeature.getPopup()) {
            this.selectedFeature.setPopupContent(this.generatePopupContent(this.selectedFeature));
        }
    }

    showEditButton() {
        const panel = document.getElementById('propertyPanel');
        if (!panel) return;

        const editBtn = panel.querySelector('[data-action="edit"]');
        if (editBtn) {
            editBtn.style.display = 'inline-block';
        }
    }

    hideEditButton() {
        const panel = document.getElementById('propertyPanel');
        if (!panel) return;

        const editBtn = panel.querySelector('[data-action="edit"]');
        if (editBtn) {
            editBtn.style.display = 'none';
        }
    }

    showEditButtons() {
        const panel = document.getElementById('propertyPanel');
        if (!panel) return;

        const saveBtn = panel.querySelector('[data-action="save"]');
        const cancelBtn = panel.querySelector('[data-action="cancel"]');

        if (saveBtn) saveBtn.style.display = 'inline-block';
        if (cancelBtn) cancelBtn.style.display = 'inline-block';
    }

    hideEditButtons() {
        const panel = document.getElementById('propertyPanel');
        if (!panel) return;

        const saveBtn = panel.querySelector('[data-action="save"]');
        const cancelBtn = panel.querySelector('[data-action="cancel"]');

        if (saveBtn) saveBtn.style.display = 'none';
        if (cancelBtn) cancelBtn.style.display = 'none';
    }

    // ========================================
    // Utility Methods
    // ========================================

    extractFeatureProperties(feature) {
        if (feature.feature && feature.feature.properties) {
            return feature.feature.properties;
        } else if (feature.options) {
            return feature.options;
        } else if (feature.properties) {
            return feature.properties;
        }
        return {};
    }

    extractGeometryInfo(feature) {
        const info = {
            type: this.getFeatureType(feature),
            area: null,
            length: null,
            coordinates: null
        };

        try {
            if (feature instanceof L.Polygon || feature instanceof L.Circle) {
                const geojson = feature.toGeoJSON();
                info.area = turf.area(geojson);
                info.length = turf.length(geojson, { units: 'meters' });
            } else if (feature instanceof L.Polyline) {
                const geojson = feature.toGeoJSON();
                info.length = turf.length(geojson, { units: 'meters' });
            }

            // Count coordinates
            if (feature.getLatLngs) {
                const latlngs = feature.getLatLngs();
                info.coordinates = Array.isArray(latlngs[0]) ? latlngs[0].length : latlngs.length;
            } else if (feature.getLatLng) {
                info.coordinates = 1;
            }
        } catch (error) {
            console.warn('Error extracting geometry info:', error);
        }

        return info;
    }

    extractCoordinates(feature) {
        const coordinates = [];

        if (feature.getLatLng) {
            // Point feature
            coordinates.push(feature.getLatLng());
        } else if (feature.getLatLngs) {
            // Line or polygon feature
            const latlngs = feature.getLatLngs();
            if (Array.isArray(latlngs[0])) {
                // Polygon
                latlngs[0].forEach(latlng => coordinates.push(latlng));
            } else {
                // Line
                latlngs.forEach(latlng => coordinates.push(latlng));
            }
        }

        return coordinates;
    }

    getFeatureType(feature) {
        if (feature instanceof L.Marker) {
            return 'نقطه';
        } else if (feature instanceof L.Circle) {
            return 'دایره';
        } else if (feature instanceof L.Rectangle) {
            return 'مستطیل';
        } else if (feature instanceof L.Polygon) {
            return 'چندضلعی';
        } else if (feature instanceof L.Polyline) {
            return 'خط';
        }
        return 'نامشخص';
    }

    formatArea(area) {
        switch (this.options.areaUnit) {
            case 'hectares':
                return `${(area / 10000).toFixed(2)} هکتار`;
            case 'square_kilometers':
                return `${(area / 1000000).toFixed(4)} کیلومتر مربع`;
            default:
                return `${area.toFixed(2)} متر مربع`;
        }
    }

    formatLength(length) {
        switch (this.options.lengthUnit) {
            case 'kilometers':
                return `${(length / 1000).toFixed(3)} کیلومتر`;
            case 'feet':
                return `${(length * 3.28084).toFixed(2)} فوت`;
            case 'miles':
                return `${(length * 0.000621371).toFixed(4)} مایل`;
            default:
                return `${length.toFixed(2)} متر`;
        }
    }

    formatCoordinate(lat, lng) {
        if (this.options.coordinateFormat === 'dms') {
            return `${this.toDMS(lat, 'lat')}, ${this.toDMS(lng, 'lng')}`;
        } else {
            return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
        }
    }

    toDMS(coordinate, type) {
        const absolute = Math.abs(coordinate);
        const degrees = Math.floor(absolute);
        const minutesNotTruncated = (absolute - degrees) * 60;
        const minutes = Math.floor(minutesNotTruncated);
        const seconds = Math.floor((minutesNotTruncated - minutes) * 60);

        const direction = type === 'lat'
            ? (coordinate >= 0 ? 'N' : 'S')
            : (coordinate >= 0 ? 'E' : 'W');

        return `${degrees}° ${minutes}' ${seconds}" ${direction}`;
    }

    formatAttributeValue(value) {
        if (value === null || value === undefined) {
            return 'خالی';
        }

        const stringValue = String(value);
        if (stringValue.length > this.options.maxAttributeLength) {
            return stringValue.substring(0, this.options.maxAttributeLength) + '...';
        }

        return stringValue;
    }

    generatePopupContent(feature) {
        const properties = this.extractFeatureProperties(feature);
        const featureType = this.getFeatureType(feature);

        let content = `<div class="feature-popup"><strong>${featureType}</strong><br>`;

        // Show first few properties
        let count = 0;
        for (const [key, value] of Object.entries(properties)) {
            if (count >= 3) break;
            if (key !== 'geometry' && key !== 'type') {
                content += `${key}: ${this.formatAttributeValue(value)}<br>`;
                count++;
            }
        }

        content += `<button onclick="window.propertyTools.selectFeature(this.feature)" class="btn btn-sm btn-primary mt-1">
            <i class="fas fa-info"></i> جزئیات
        </button></div>`;

        return content;
    }

    showSettingsDialog() {
        // Create and show settings dialog
        const dialog = this.createSettingsDialog();
        document.body.appendChild(dialog);
        dialog.style.display = 'block';
    }

    createSettingsDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'property-settings-dialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-header">
                    <h5>تنظیمات نمایش خصوصیات</h5>
                    <button type="button" class="btn-close" onclick="this.closest('.property-settings-dialog').remove()"></button>
                </div>
                <div class="dialog-body">
                    <div class="settings-form">
                        <div class="mb-3">
                            <label class="form-label">نمایش اطلاعات:</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showGeometry" ${this.options.showGeometry ? 'checked' : ''}>
                                <label class="form-check-label" for="showGeometry">هندسه</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showAttributes" ${this.options.showAttributes ? 'checked' : ''}>
                                <label class="form-check-label" for="showAttributes">ویژگی‌ها</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showCoordinates" ${this.options.showCoordinates ? 'checked' : ''}>
                                <label class="form-check-label" for="showCoordinates">مختصات</label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="coordinateFormat" class="form-label">فرمت مختصات:</label>
                            <select class="form-select" id="coordinateFormat">
                                <option value="decimal" ${this.options.coordinateFormat === 'decimal' ? 'selected' : ''}>اعشاری</option>
                                <option value="dms" ${this.options.coordinateFormat === 'dms' ? 'selected' : ''}>درجه، دقیقه، ثانیه</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="areaUnit" class="form-label">واحد مساحت:</label>
                            <select class="form-select" id="areaUnit">
                                <option value="square_meters" ${this.options.areaUnit === 'square_meters' ? 'selected' : ''}>متر مربع</option>
                                <option value="hectares" ${this.options.areaUnit === 'hectares' ? 'selected' : ''}>هکتار</option>
                                <option value="square_kilometers" ${this.options.areaUnit === 'square_kilometers' ? 'selected' : ''}>کیلومتر مربع</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="lengthUnit" class="form-label">واحد طول:</label>
                            <select class="form-select" id="lengthUnit">
                                <option value="meters" ${this.options.lengthUnit === 'meters' ? 'selected' : ''}>متر</option>
                                <option value="kilometers" ${this.options.lengthUnit === 'kilometers' ? 'selected' : ''}>کیلومتر</option>
                                <option value="feet" ${this.options.lengthUnit === 'feet' ? 'selected' : ''}>فوت</option>
                                <option value="miles" ${this.options.lengthUnit === 'miles' ? 'selected' : ''}>مایل</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button type="button" class="btn btn-secondary" onclick="this.closest('.property-settings-dialog').remove()">انصراف</button>
                    <button type="button" class="btn btn-primary" onclick="window.propertyTools.applySettings(this.closest('.property-settings-dialog'))">اعمال</button>
                </div>
            </div>
        `;
        return dialog;
    }

    applySettings(dialog) {
        // Update options from dialog
        this.options.showGeometry = dialog.querySelector('#showGeometry').checked;
        this.options.showAttributes = dialog.querySelector('#showAttributes').checked;
        this.options.showCoordinates = dialog.querySelector('#showCoordinates').checked;
        this.options.coordinateFormat = dialog.querySelector('#coordinateFormat').value;
        this.options.areaUnit = dialog.querySelector('#areaUnit').value;
        this.options.lengthUnit = dialog.querySelector('#lengthUnit').value;

        // Refresh property display if feature is selected
        if (this.selectedFeature) {
            this.loadFeatureProperties(this.selectedFeature);
        }

        // Close dialog
        dialog.remove();

        this.showToast('تنظیمات اعمال شد', 'success');
    }

    showToast(message, type = 'info', duration = 3000) {
        // Use existing toast system or create simple alert
        if (window.showToast) {
            window.showToast(message, type, duration);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    // ========================================
    // Public API
    // ========================================

    getSelectedFeature() {
        return this.selectedFeature;
    }

    setSelectedFeature(feature) {
        this.selectFeature(feature);
    }

    clearSelection() {
        this.deselectFeature();
    }

    isPropertyPanelVisible() {
        return this.isVisible;
    }

    updateDisplayOptions(options) {
        Object.assign(this.options, options);

        if (this.selectedFeature) {
            this.loadFeatureProperties(this.selectedFeature);
        }
    }

    // ========================================
    // Cleanup
    // ========================================

    destroy() {
        // Deselect feature
        this.deselectFeature();

        // Remove property panel
        const panel = document.getElementById('propertyPanel');
        if (panel) {
            panel.remove();
        }

        // Remove settings dialog if exists
        const settingsDialog = document.querySelector('.property-settings-dialog');
        if (settingsDialog) {
            settingsDialog.remove();
        }

        // Reset cursor
        if (this.map) {
            this.map.getContainer().style.cursor = '';
        }

        super.destroy();
    }
}

// Register component
window.ComponentFactory.register('property-tools', PropertyTools);
