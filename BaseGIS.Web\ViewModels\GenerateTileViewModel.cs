﻿namespace BaseGIS.Web.ViewModels
{
    public class GenerateTileViewModel
    {
        public int TableInfoId { get; set; }
        public int MinZoom { get; set; }
        public int MaxZoom { get; set; }
        public double? MinLat { get; set; }
        public double? MinLon { get; set; }
        public double? MaxLat { get; set; }
        public double? MaxLon { get; set; }
        public bool UseBatchData { get; set; }
    }
}
