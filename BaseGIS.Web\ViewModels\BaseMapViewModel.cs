namespace BaseGIS.Web.ViewModels
{
    /// <summary>
    /// مدل نمایش نقشه پایه
    /// </summary>
    public class BaseMapViewModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public string Image { get; set; } = string.Empty;
        public string? WmsLayers { get; set; }
        public string? Styles { get; set; }
        public bool IsWms { get; set; }
        public string? DateTime { get; set; }
    }

    /// <summary>
    /// مدل تنظیمات نقشه‌های پایه برای JavaScript
    /// </summary>
    public class BaseMapConfigViewModel
    {
        public List<BaseMapViewModel> BaseMaps { get; set; } = new();
        public int DefaultBaseMapId { get; set; }
        public string EsriBaseUrl { get; set; } = string.Empty;
        public string DefaultImagePath { get; set; } = string.Empty;
    }

    /// <summary>
    /// مدل درخواست تغییر نقشه پایه
    /// </summary>
    public class ChangeBaseMapRequest
    {
        public int BaseMapId { get; set; }
        public bool SetAsDefault { get; set; } = false;
    }

    /// <summary>
    /// مدل پاسخ تغییر نقشه پایه
    /// </summary>
    public class ChangeBaseMapResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public BaseMapViewModel? BaseMap { get; set; }
    }
}
