﻿using Mapbox.VectorTile;
using Mapbox.VectorTile.Geometry;
namespace TileParser
{
    class Program
    {
        /// <summary>
        /// یک فایل MVT را از مسیر مشخص شده دیکد می‌کند.
        /// </summary>
        /// <param name="filePath">مسیر فایل MVT.</param>
        public static void DecodeMvtFromFile(string filePath)
        {
            if (!File.Exists(filePath))
            {
                Console.WriteLine($"خطا: فایل در مسیر '{filePath}' یافت نشد.");
                return;
            }

            try
            {
                // خواندن تمام بایت‌های فایل
                byte[] tileBytes = File.ReadAllBytes(filePath);

                // دیکد کردن بایت‌ها
                DecodeMvtFromBytes(tileBytes);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطا در هنگام خواندن یا دیکد کردن فایل: {ex.Message}");
            }
        }

        /// <summary>
        /// یک رشته Base64 حاوی داده MVT را دیکد می‌کند.
        /// </summary>
        /// <param name="base64String">رشته Base64 حاوی داده MVT.</param>
        public static void DecodeMvtFromBase64String(string base64String)
        {
            try
            {
                // تبدیل رشته Base64 به آرایه بایت
                byte[] tileBytes = Convert.FromBase64String(base64String);

                // دیکد کردن بایت‌ها
                DecodeMvtFromBytes(tileBytes);
            }
            catch (FormatException ex)
            {
                Console.WriteLine($"خطا: رشته ورودی یک رشته Base64 معتبر نیست. {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطا در هنگام دیکد کردن رشته Base64: {ex.Message}");
            }
        }

        /// <summary>
        /// آرایه بایت حاوی داده MVT را دیکد و اطلاعات آن را نمایش می‌دهد.
        /// </summary>
        /// <param name="mvtBytes">آرایه بایت داده MVT.</param>
        public static void DecodeMvtFromBytes(byte[] mvtBytes)
        {
            if (mvtBytes == null || mvtBytes.Length == 0)
            {
                Console.WriteLine("خطا: داده‌های MVT برای دیکد کردن خالی است.");
                return;
            }

            try
            {
                // ایجاد یک VectorTile از روی آرایه بایت
                // اولین پارامتر آرایه بایت و دومین پارامتر نشان می‌دهد که آیا بایت‌ها فشرده (gzipped) هستند یا خیر.
                // اکثر فایل‌های MVT به صورت gzip فشرده شده‌اند. اگر فایل شما فشرده نیست، false قرار دهید.
                // کتابخانه Mapbox.VectorTile به طور خودکار سعی در تشخیص و مدیریت gzip می‌کند اگر از MemoryStream استفاده شود.
                VectorTile tile;
                using (var stream = new MemoryStream(mvtBytes))
                {
                    // کتابخانه به طور خودکار gzip را مدیریت می‌کند اگر از استریم خوانده شود.
                    tile = new VectorTile(mvtBytes);
                }

                Console.WriteLine($"تعداد لایه‌ها: {tile.LayerNames().Count()}");

                foreach (var layerName in tile.LayerNames())
                {
                    VectorTileLayer layer = tile.GetLayer(layerName);
                    Console.WriteLine($"\n--- لایه: {layer.Name} ---");
                    Console.WriteLine($"تعداد ویژگی‌ها (Features): {layer.FeatureCount()}");
                    Console.WriteLine($"Extent: {layer.Extent}");
                    Console.WriteLine($"Version: {layer.Version}");

                    for (int i = 0; i < layer.FeatureCount(); i++)
                    {
                        VectorTileFeature feature = layer.GetFeature(i);
                        Console.WriteLine($"  ویژگی ID: {feature.Id}");
                        Console.WriteLine($"  نوع هندسه: {feature.GeometryType}"); // Point, LineString, Polygon

                        // نمایش خصوصیات (Properties) ویژگی
                        Console.WriteLine("  خصوصیات:");
                        foreach (var prop in feature.GetProperties())
                        {
                            Console.WriteLine($"    {prop.Key}: {prop.Value}");
                        }

                        // نمایش مختصات هندسه (مثال برای نقاط)
                        // هندسه به صورت مختصات محلی در تایل ذخیره شده و نیاز به تبدیل دارد.
                        // کتابخانه معمولاً توابعی برای کار با این مختصات ارائه می‌دهد.
                        // هندسه به صورت لیستی از Point2d<long> برگردانده می‌شود.
                        List<List<Point2d<long>>> geometry = feature.Geometry<long>();
                        Console.WriteLine("  هندسه (مختصات خام):");
                        foreach (var item in geometry)
                        {
                            foreach (var point in item)
                            {
                                Console.WriteLine($"    X: {point.X}, Y: {point.Y}");
                            }
                        }
                        // برای تفسیر دقیق هندسه، به مستندات فرمت MVT و کتابخانه مراجعه کنید.
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطا در هنگام دیکد کردن داده‌های MVT: {ex.Message}");
                // ممکن است داده‌ها خراب باشند یا فرمت MVT صحیحی نداشته باشند.
                // همچنین بررسی کنید که آیا داده‌ها gzip شده‌اند و کتابخانه به درستی آن را مدیریت می‌کند.
            }
        }

        public static void Main(string[] args)
        {
            // --- مثال ۱: دیکد کردن از فایل ---
            string filePath = "D:\\Backups\\BaseProject\\BaseGIS\\BaseGIS.Web\\wwwroot\\tiles\\12\\8\\163\\100.mvt"; // مسیر فایل MVT خود را اینجا قرار دهید
            DecodeMvtFromFile(filePath);

            Console.WriteLine("\n===================================================\n");

            // --- مثال ۲: دیکد کردن از رشته Base64 ---
            // این یک رشته Base64 نمونه و بسیار کوچک است و احتمالاً یک MVT کامل و معتبر نیست.
            // شما باید رشته Base64 واقعی خود را جایگزین کنید.
            string base64MvtString = "GvLNBQoGbGF5ZXIwEtIQEgYAAAEB...";
            //DecodeMvtFromBase64String(base64MvtString);

            // --- مثال ۳: دیکد کردن از یک آرایه بایت فرضی (اگر مستقیم بایت ها را دارید) ---
            // این آرایه بایت باید حاوی داده‌های یک فایل MVT معتبر باشد.
            // byte[] sampleMvtBytes = new byte[] { ... }; // بایت‌های MVT خود را اینجا قرار دهید
            // DecodeMvtFromBytes(sampleMvtBytes);

            Console.WriteLine("برای تست، یکی از مثال‌های بالا را از حالت کامنت خارج کرده و اجرا کنید.");
            Console.WriteLine("مطمئن شوید که مسیر فایل صحیح است یا رشته Base64 معتبری دارید.");
        }
    }
}
