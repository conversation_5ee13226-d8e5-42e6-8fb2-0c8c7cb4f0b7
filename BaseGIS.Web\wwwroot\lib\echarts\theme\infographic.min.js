!function(e,o){"function"==typeof define&&define.amd?define(["exports","echarts"],o):"object"==typeof exports&&"string"!=typeof exports.nodeName?o(0,require("echarts/lib/echarts")):o(0,e.echarts)}(this,function(e,o){o?o.registerTheme("infographic",{color:o=["#C1232B","#27727B","#FCCE10","#E87C25","#B5C334","#FE8463","#9BCA63","#FAD860","#F3A43B","#60C0DD","#D7504B","#C6E579","#F4E001","#F0805A","#26C0C0"],title:{textStyle:{fontWeight:"normal",color:"#27727B"}},visualMap:{color:["#C1232B","#FCCE10"]},toolbox:{iconStyle:{borderColor:o[0]}},tooltip:{backgroundColor:"rgba(50,50,50,0.5)",axisPointer:{type:"line",lineStyle:{color:"#27727B",type:"dashed"},crossStyle:{color:"#27727B"},shadowStyle:{color:"rgba(200,200,200,0.3)"}}},dataZoom:{dataBackgroundColor:"rgba(181,195,52,0.3)",fillerColor:"rgba(181,195,52,0.2)",handleColor:"#27727B"},categoryAxis:{axisLine:{lineStyle:{color:"#27727B"}},splitLine:{show:!1}},valueAxis:{axisLine:{show:!1},splitArea:{show:!1},splitLine:{lineStyle:{color:["#ccc"],type:"dashed"}}},timeline:{itemStyle:{color:"#27727B"},lineStyle:{color:"#27727B"},controlStyle:{color:"#27727B",borderColor:"#27727B"},symbol:"emptyCircle",symbolSize:3},line:{itemStyle:{borderWidth:2,borderColor:"#fff",lineStyle:{width:3}},emphasis:{itemStyle:{borderWidth:0}},symbol:"circle",symbolSize:3.5},candlestick:{itemStyle:{color:"#c1232b",color0:"#b5c334"},lineStyle:{width:1,color:"#c1232b",color0:"#b5c334"},areaStyle:{color:"#c1232b",color0:"#27727b"}},graph:{itemStyle:{color:"#c1232b"},linkStyle:{color:"#b5c334"}},map:{itemStyle:{color:"#f2385a",areaColor:"#ddd",borderColor:"#eee"},areaStyle:{color:"#fe994e"},label:{color:"#c1232b"}},gauge:{axisLine:{lineStyle:{color:[[.2,"#B5C334"],[.8,"#27727B"],[1,"#C1232B"]]}},axisTick:{splitNumber:2,length:5,lineStyle:{color:"#fff"}},axisLabel:{color:"#fff"},splitLine:{length:"5%",lineStyle:{color:"#fff"}},title:{offsetCenter:[0,-20]}}}):"undefined"!=typeof console&&console&&console.error&&console.error("ECharts is not Loaded")});