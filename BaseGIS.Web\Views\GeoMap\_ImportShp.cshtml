<!--begin::Import SHP Content-->
<div class="container-fluid h-100">
    <div class="row h-100">
        <div class="col-12">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fa fa-upload"></i> ایمپورت فایل SHP
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- مرحله انتخاب فایل -->
                        <div class="col-md-4">
                            <div class="import-steps">
                                <div class="mb-4">
                                    <h6 class="border-bottom pb-2">مرحله 1: انتخاب فایل</h6>
                                    <div class="upload-area border rounded p-4 text-center" id="uploadArea">
                                        <i class="fa fa-cloud-upload fa-3x text-muted mb-3"></i>
                                        <p class="mb-3">فایل‌های SHP را اینجا بکشید یا کلیک کنید</p>
                                        <input type="file" id="shpFileInput" multiple accept=".shp,.shx,.dbf,.prj,.cpg" style="display: none;">
                                        <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('shpFileInput').click()">
                                            <i class="fa fa-folder-open"></i> انتخاب فایل‌ها
                                        </button>
                                    </div>

                                    <div class="mt-3">
                                        <small class="text-muted">
                                            <strong>فایل‌های مورد نیاز:</strong><br>
                                            • .shp (هندسه)<br>
                                            • .shx (ایندکس)<br>
                                            • .dbf (اطلاعات)<br>
                                            • .prj (سیستم مختصات - اختیاری)
                                        </small>
                                    </div>
                                </div>

                                <div class="mb-4" id="fileListSection" style="display: none;">
                                    <h6 class="border-bottom pb-2">فایل‌های انتخاب شده</h6>
                                    <div id="selectedFilesList" class="list-group">
                                        <!-- لیست فایل‌ها اینجا نمایش داده می‌شود -->
                                    </div>
                                </div>

                                <div class="mb-4" id="validationSection" style="display: none;">
                                    <h6 class="border-bottom pb-2">وضعیت اعتبارسنجی</h6>
                                    <div id="validationResults">
                                        <!-- نتایج اعتبارسنجی اینجا نمایش داده می‌شود -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تنظیمات ایمپورت -->
                        <div class="col-md-4">
                            <div class="import-settings" id="importSettings" style="display: none;">
                                <div class="mb-4">
                                    <h6 class="border-bottom pb-2">مرحله 2: تنظیمات ایمپورت</h6>

                                    <div class="mb-3">
                                        <label for="layerName" class="form-label">نام لایه:</label>
                                        <input type="text" class="form-control" id="layerName" placeholder="نام لایه جدید...">
                                    </div>

                                    <div class="mb-3">
                                        <label for="layerDescription" class="form-label">توضیحات:</label>
                                        <textarea class="form-control" id="layerDescription" rows="3" placeholder="توضیحات لایه..."></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label for="coordinateSystem" class="form-label">سیستم مختصات:</label>
                                        <select class="form-select" id="coordinateSystem">
                                            <option value="auto">تشخیص خودکار</option>
                                            <option value="4326">WGS84 (EPSG:4326)</option>
                                            <option value="3857">Web Mercator (EPSG:3857)</option>
                                            <option value="32639">UTM Zone 39N (EPSG:32639)</option>
                                            <option value="32640">UTM Zone 40N (EPSG:32640)</option>
                                            <option value="32641">UTM Zone 41N (EPSG:32641)</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="encoding" class="form-label">کدگذاری متن:</label>
                                        <select class="form-select" id="encoding">
                                            <option value="UTF-8">UTF-8</option>
                                            <option value="Windows-1256">Windows-1256 (فارسی)</option>
                                            <option value="ISO-8859-1">ISO-8859-1</option>
                                        </select>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="createSpatialIndex" checked>
                                        <label class="form-check-label" for="createSpatialIndex">
                                            ایجاد ایندکس مکانی
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="validateGeometry" checked>
                                        <label class="form-check-label" for="validateGeometry">
                                            اعتبارسنجی هندسه
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <h6 class="border-bottom pb-2">مرحله 3: نگاشت فیلدها</h6>
                                    <div id="fieldMappingContainer">
                                        <div class="text-muted text-center">
                                            <i class="fa fa-table fa-2x mb-2"></i>
                                            <p>ابتدا فایل‌ها را انتخاب کنید</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- پیش‌نمایش و نتایج -->
                        <div class="col-md-4">
                            <div class="import-preview">
                                <div class="mb-4">
                                    <h6 class="border-bottom pb-2">پیش‌نمایش داده‌ها</h6>
                                    <div id="dataPreview" style="height: 300px; overflow: auto; border: 1px solid #ddd; border-radius: 4px;">
                                        <div class="text-center text-muted p-4">
                                            <i class="fa fa-eye fa-3x mb-3"></i>
                                            <p>پیش‌نمایش داده‌ها اینجا نمایش داده می‌شود</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <h6 class="border-bottom pb-2">آمار فایل</h6>
                                    <div id="fileStats">
                                        <div class="text-muted text-center">
                                            <i class="fa fa-chart-bar fa-2x mb-2"></i>
                                            <p>آمار فایل اینجا نمایش داده می‌شود</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-success" id="startImportBtn" onclick="startImport()" disabled>
                                        <i class="fa fa-play"></i> شروع ایمپورت
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="resetImport()">
                                        <i class="fa fa-refresh"></i> شروع مجدد
                                    </button>
                                </div>

                                <!-- نوار پیشرفت -->
                                <div class="mt-3" id="progressSection" style="display: none;">
                                    <div class="progress">
                                        <div class="progress-bar" id="importProgress" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <div class="text-center mt-2">
                                        <small id="progressText">آماده ایمپورت...</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .upload-area {
        border: 2px dashed #ccc !important;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .upload-area:hover {
        border-color: #007bff !important;
        background-color: rgba(0, 123, 255, 0.05);
    }

    .upload-area.drag-over {
        border-color: #007bff !important;
        background-color: rgba(0, 123, 255, 0.1);
    }

    .file-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 5px;
    }

    .file-item.valid {
        border-color: #28a745;
        background-color: rgba(40, 167, 69, 0.1);
    }

    .file-item.invalid {
        border-color: #dc3545;
        background-color: rgba(220, 53, 69, 0.1);
    }

    .validation-badge {
        font-size: 0.75em;
        padding: 2px 6px;
    }
</style>

<script>
    // متغیرهای ایمپورت SHP
    let selectedFiles = [];
    let shpData = null;
    let importInProgress = false;

    // مقداردهی اولیه
    document.addEventListener('DOMContentLoaded', function() {
        initializeImportShp();
    });

    /**
     * مقداردهی ایمپورت SHP
     */
    function initializeImportShp() {
        setupFileUpload();
        setupDragAndDrop();
    }

    /**
     * تنظیم آپلود فایل
     */
    function setupFileUpload() {
        const fileInput = document.getElementById('shpFileInput');
        fileInput.addEventListener('change', handleFileSelection);
    }

    /**
     * تنظیم Drag and Drop
     */
    function setupDragAndDrop() {
        const uploadArea = document.getElementById('uploadArea');

        uploadArea.addEventListener('click', () => {
            document.getElementById('shpFileInput').click();
        });

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('drag-over');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            handleFileSelection({ target: { files: e.dataTransfer.files } });
        });
    }

    /**
     * مدیریت انتخاب فایل‌ها
     */
    function handleFileSelection(event) {
        const files = Array.from(event.target.files);
        selectedFiles = files;

        if (files.length === 0) return;

        displaySelectedFiles();
        validateFiles();
        loadShpData();
    }

    /**
     * نمایش فایل‌های انتخاب شده
     */
    function displaySelectedFiles() {
        const fileListSection = document.getElementById('fileListSection');
        const selectedFilesList = document.getElementById('selectedFilesList');

        if (selectedFiles.length === 0) {
            fileListSection.style.display = 'none';
            return;
        }

        let html = '';
        selectedFiles.forEach((file, index) => {
            const extension = file.name.split('.').pop().toLowerCase();
            const icon = getFileIcon(extension);
            const size = formatFileSize(file.size);

            html += `
                <div class="file-item" id="file_${index}">
                    <div>
                        <i class="fa ${icon}"></i>
                        <span class="ms-2">${file.name}</span>
                        <small class="text-muted ms-2">(${size})</small>
                    </div>
                    <span class="validation-badge badge" id="badge_${index}">
                        <i class="fa fa-clock"></i> در انتظار
                    </span>
                </div>
            `;
        });

        selectedFilesList.innerHTML = html;
        fileListSection.style.display = 'block';
    }

    /**
     * اعتبارسنجی فایل‌ها
     */
    function validateFiles() {
        const validationSection = document.getElementById('validationSection');
        const validationResults = document.getElementById('validationResults');

        const requiredExtensions = ['shp', 'shx', 'dbf'];
        const optionalExtensions = ['prj', 'cpg'];
        const allExtensions = [...requiredExtensions, ...optionalExtensions];

        const fileExtensions = selectedFiles.map(f => f.name.split('.').pop().toLowerCase());

        let validationHTML = '';
        let allValid = true;

        // بررسی فایل‌های ضروری
        requiredExtensions.forEach(ext => {
            const exists = fileExtensions.includes(ext);
            const status = exists ? 'success' : 'danger';
            const icon = exists ? 'check' : 'times';

            validationHTML += `
                <div class="alert alert-${status} py-2">
                    <i class="fa fa-${icon}"></i> فایل .${ext} ${exists ? 'موجود است' : 'مورد نیاز است'}
                </div>
            `;

            if (!exists) allValid = false;
        });

        // بررسی فایل‌های اختیاری
        optionalExtensions.forEach(ext => {
            const exists = fileExtensions.includes(ext);
            if (exists) {
                validationHTML += `
                    <div class="alert alert-info py-2">
                        <i class="fa fa-info-circle"></i> فایل .${ext} موجود است
                    </div>
                `;
            }
        });

        // به‌روزرسانی وضعیت فایل‌ها
        selectedFiles.forEach((file, index) => {
            const extension = file.name.split('.').pop().toLowerCase();
            const fileItem = document.getElementById(`file_${index}`);
            const badge = document.getElementById(`badge_${index}`);

            if (allExtensions.includes(extension)) {
                fileItem.classList.add('valid');
                badge.className = 'validation-badge badge bg-success';
                badge.innerHTML = '<i class="fa fa-check"></i> معتبر';
            } else {
                fileItem.classList.add('invalid');
                badge.className = 'validation-badge badge bg-danger';
                badge.innerHTML = '<i class="fa fa-times"></i> نامعتبر';
                allValid = false;
            }
        });

        validationResults.innerHTML = validationHTML;
        validationSection.style.display = 'block';

        // فعال/غیرفعال کردن مرحله بعد
        if (allValid) {
            document.getElementById('importSettings').style.display = 'block';
            document.getElementById('startImportBtn').disabled = false;
        }
    }

    /**
     * بارگذاری داده‌های SHP
     */
    function loadShpData() {
        // شبیه‌سازی بارگذاری داده‌ها
        setTimeout(() => {
            shpData = {
                geometryType: 'Polygon',
                featureCount: 1250,
                fields: [
                    { name: 'ID', type: 'Integer' },
                    { name: 'NAME', type: 'String' },
                    { name: 'TYPE', type: 'String' },
                    { name: 'AREA', type: 'Double' },
                    { name: 'POPULATION', type: 'Integer' }
                ],
                extent: {
                    minX: 51.2, maxX: 51.6,
                    minY: 35.5, maxY: 35.8
                },
                sampleFeatures: [
                    { ID: 1, NAME: 'منطقه 1', TYPE: 'مسکونی', AREA: 1250.5, POPULATION: 15000 },
                    { ID: 2, NAME: 'منطقه 2', TYPE: 'تجاری', AREA: 850.2, POPULATION: 8500 },
                    { ID: 3, NAME: 'منطقه 3', TYPE: 'صنعتی', AREA: 2100.8, POPULATION: 5200 }
                ]
            };

            displayDataPreview();
            displayFileStats();
            displayFieldMapping();

            // تنظیم نام پیش‌فرض لایه
            const shpFile = selectedFiles.find(f => f.name.endsWith('.shp'));
            if (shpFile) {
                const layerName = shpFile.name.replace('.shp', '');
                document.getElementById('layerName').value = layerName;
            }
        }, 1000);
    }

    /**
     * نمایش پیش‌نمایش داده‌ها
     */
    function displayDataPreview() {
        const dataPreview = document.getElementById('dataPreview');

        if (!shpData) return;

        let html = `
            <div class="p-3">
                <h6>نمونه رکوردها:</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                ${shpData.fields.map(field => `<th>${field.name}</th>`).join('')}
                            </tr>
                        </thead>
                        <tbody>
                            ${shpData.sampleFeatures.map(feature => `
                                <tr>
                                    ${shpData.fields.map(field => `<td>${feature[field.name]}</td>`).join('')}
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        dataPreview.innerHTML = html;
    }

    /**
     * نمایش آمار فایل
     */
    function displayFileStats() {
        const fileStats = document.getElementById('fileStats');

        if (!shpData) return;

        const html = `
            <div class="row text-center">
                <div class="col-6">
                    <div class="border rounded p-2 mb-2">
                        <h6 class="text-primary">${shpData.featureCount.toLocaleString()}</h6>
                        <small>تعداد عارضه</small>
                    </div>
                </div>
                <div class="col-6">
                    <div class="border rounded p-2 mb-2">
                        <h6 class="text-success">${shpData.fields.length}</h6>
                        <small>تعداد فیلد</small>
                    </div>
                </div>
                <div class="col-12">
                    <div class="border rounded p-2 mb-2">
                        <h6 class="text-info">${shpData.geometryType}</h6>
                        <small>نوع هندسه</small>
                    </div>
                </div>
            </div>
            <div class="mt-2">
                <small class="text-muted">
                    <strong>محدوده:</strong><br>
                    X: ${shpData.extent.minX} - ${shpData.extent.maxX}<br>
                    Y: ${shpData.extent.minY} - ${shpData.extent.maxY}
                </small>
            </div>
        `;

        fileStats.innerHTML = html;
    }

    /**
     * نمایش نگاشت فیلدها
     */
    function displayFieldMapping() {
        const fieldMappingContainer = document.getElementById('fieldMappingContainer');

        if (!shpData) return;

        let html = '<div class="table-responsive"><table class="table table-sm">';
        html += '<thead><tr><th>فیلد مبدأ</th><th>نوع</th><th>نگاشت</th></tr></thead><tbody>';

        shpData.fields.forEach(field => {
            html += `
                <tr>
                    <td>${field.name}</td>
                    <td><span class="badge bg-secondary">${field.type}</span></td>
                    <td>
                        <select class="form-select form-select-sm">
                            <option value="${field.name}" selected>${field.name}</option>
                            <option value="ignore">نادیده گرفتن</option>
                        </select>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        fieldMappingContainer.innerHTML = html;
    }

    /**
     * شروع ایمپورت
     */
    function startImport() {
        if (importInProgress) return;

        const layerName = document.getElementById('layerName').value.trim();
        if (!layerName) {
            alert('لطفاً نام لایه را وارد کنید.');
            return;
        }

        importInProgress = true;
        document.getElementById('startImportBtn').disabled = true;
        document.getElementById('progressSection').style.display = 'block';

        simulateImport();
    }

    /**
     * شبیه‌سازی فرآیند ایمپورت
     */
    function simulateImport() {
        const progressBar = document.getElementById('importProgress');
        const progressText = document.getElementById('progressText');

        const steps = [
            { progress: 10, text: 'خواندن فایل‌ها...' },
            { progress: 25, text: 'تجزیه هندسه‌ها...' },
            { progress: 40, text: 'اعتبارسنجی داده‌ها...' },
            { progress: 60, text: 'تبدیل سیستم مختصات...' },
            { progress: 80, text: 'ذخیره در پایگاه داده...' },
            { progress: 95, text: 'ایجاد ایندکس مکانی...' },
            { progress: 100, text: 'ایمپورت کامل شد!' }
        ];

        let currentStep = 0;

        const updateProgress = () => {
            if (currentStep < steps.length) {
                const step = steps[currentStep];
                progressBar.style.width = step.progress + '%';
                progressBar.textContent = step.progress + '%';
                progressText.textContent = step.text;

                if (step.progress === 100) {
                    progressBar.classList.add('bg-success');
                    setTimeout(() => {
                        alert('ایمپورت با موفقیت انجام شد!');
                        resetImport();
                    }, 1000);
                } else {
                    currentStep++;
                    setTimeout(updateProgress, 1000 + Math.random() * 1000);
                }
            }
        };

        updateProgress();
    }

    /**
     * ریست کردن ایمپورت
     */
    function resetImport() {
        selectedFiles = [];
        shpData = null;
        importInProgress = false;

        document.getElementById('shpFileInput').value = '';
        document.getElementById('fileListSection').style.display = 'none';
        document.getElementById('validationSection').style.display = 'none';
        document.getElementById('importSettings').style.display = 'none';
        document.getElementById('progressSection').style.display = 'none';
        document.getElementById('startImportBtn').disabled = true;

        // ریست کردن پیش‌نمایش
        document.getElementById('dataPreview').innerHTML = `
            <div class="text-center text-muted p-4">
                <i class="fa fa-eye fa-3x mb-3"></i>
                <p>پیش‌نمایش داده‌ها اینجا نمایش داده می‌شود</p>
            </div>
        `;

        document.getElementById('fileStats').innerHTML = `
            <div class="text-muted text-center">
                <i class="fa fa-chart-bar fa-2x mb-2"></i>
                <p>آمار فایل اینجا نمایش داده می‌شود</p>
            </div>
        `;

        document.getElementById('fieldMappingContainer').innerHTML = `
            <div class="text-muted text-center">
                <i class="fa fa-table fa-2x mb-2"></i>
                <p>ابتدا فایل‌ها را انتخاب کنید</p>
            </div>
        `;

        // ریست کردن فرم
        document.getElementById('layerName').value = '';
        document.getElementById('layerDescription').value = '';
        document.getElementById('coordinateSystem').value = 'auto';
        document.getElementById('encoding').value = 'UTF-8';
    }

    // توابع کمکی
    function getFileIcon(extension) {
        const icons = {
            'shp': 'fa-map',
            'shx': 'fa-list',
            'dbf': 'fa-database',
            'prj': 'fa-globe',
            'cpg': 'fa-font'
        };
        return icons[extension] || 'fa-file';
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
</script>
<!--end::Import SHP Content-->