{"version": 3, "targets": {"net9.0": {"Mapbox.VectorTile/1.0.4-alpha2": {"type": "package", "compile": {"lib/net35/Mapbox.VectorTile.ExtensionMethods.dll": {}, "lib/net35/Mapbox.VectorTile.Geometry.dll": {}, "lib/net35/Mapbox.VectorTile.PbfReader.dll": {}, "lib/net35/Mapbox.VectorTile.VectorTileReader.dll": {}}, "runtime": {"lib/net35/Mapbox.VectorTile.ExtensionMethods.dll": {}, "lib/net35/Mapbox.VectorTile.Geometry.dll": {}, "lib/net35/Mapbox.VectorTile.PbfReader.dll": {}, "lib/net35/Mapbox.VectorTile.VectorTileReader.dll": {}}}}}, "libraries": {"Mapbox.VectorTile/1.0.4-alpha2": {"sha512": "fwK+WL1cueszQ+OO2m5g0HyaYf6ErsKH7dW6jVDc6qUKW8pMwBB0GaeHSCAd1QRFcUJ+/bGQM1sg4Xml85FHxg==", "type": "package", "path": "mapbox.vectortile/1.0.4-alpha2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net20/Mapbox.VectorTile.ExtensionMethods.dll", "lib/net20/Mapbox.VectorTile.Geometry.dll", "lib/net20/Mapbox.VectorTile.PbfReader.dll", "lib/net20/Mapbox.VectorTile.VectorTileReader.dll", "lib/net35/Mapbox.VectorTile.ExtensionMethods.dll", "lib/net35/Mapbox.VectorTile.Geometry.dll", "lib/net35/Mapbox.VectorTile.PbfReader.dll", "lib/net35/Mapbox.VectorTile.VectorTileReader.dll", "lib/net462/Mapbox.VectorTile.ExtensionMethods.dll", "lib/net462/Mapbox.VectorTile.Geometry.dll", "lib/net462/Mapbox.VectorTile.PbfReader.dll", "lib/net462/Mapbox.VectorTile.VectorTileReader.dll", "lib/uap10/Mapbox.VectorTile.ExtensionMethods.dll", "lib/uap10/Mapbox.VectorTile.Geometry.dll", "lib/uap10/Mapbox.VectorTile.PbfReader.dll", "lib/uap10/Mapbox.VectorTile.VectorTileReader.dll", "mapbox.vectortile.1.0.4-alpha2.nupkg.sha512", "mapbox.vectortile.nuspec"]}}, "projectFileDependencyGroups": {"net9.0": ["Mapbox.VectorTile >= 1.0.4-alpha2"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Backups\\BaseProject\\BaseGIS\\TileParser\\TileParser.csproj", "projectName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectPath": "D:\\Backups\\BaseProject\\BaseGIS\\TileParser\\TileParser.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Backups\\BaseProject\\BaseGIS\\TileParser\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Mapbox.VectorTile": {"target": "Package", "version": "[1.0.4-alpha2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Package 'Mapbox.VectorTile 1.0.4-alpha2' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net9.0'. This package may not be fully compatible with your project.", "libraryId": "Mapbox.VectorTile", "targetGraphs": ["net9.0"]}]}