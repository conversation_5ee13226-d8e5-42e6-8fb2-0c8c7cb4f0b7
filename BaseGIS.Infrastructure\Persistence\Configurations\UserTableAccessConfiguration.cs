using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BaseGIS.Core.Entities;

namespace BaseGIS.Infrastructure.Persistence.Configurations
{
    public class UserTableAccessConfiguration : IEntityTypeConfiguration<UserTableAccess>
    {
        public void Configure(EntityTypeBuilder<UserTableAccess> builder)
        {
            builder.HasKey(x => x.Id);
            builder.Property(x => x.UserId).IsRequired();
            builder.HasOne(x => x.TableInfo)
                   .WithMany()
                   .HasForeignKey(x => x.TableInfoId);
        }
    }
} 