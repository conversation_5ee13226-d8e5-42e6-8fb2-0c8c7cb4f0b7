﻿using NetTopologySuite.Geometries; // For Coordinate
using Newtonsoft.Json; // Make sure this NuGet package is installed
using System.ComponentModel;
using System.Globalization;

namespace BaseGIS.Web.ViewModels
{


    public enum RenderType
    {
        Simple = 0,
        Unique = 1,
        Quantity = 2 // Classified
    }

    public enum SymbolType // For Point Symbols (DotSpatial-like)
    {
        Character = 0, // DotSpatial default might be 0, adjust if yours is different
        Custom = 1,
        Picture = 2,
        Simple = 3
    }

    public enum PatType // For Polygon Patterns (DotSpatial-like)
    {
        Simple = 0,
        Gradient = 1,
        Hatch = 2,
        Picture = 3
    }

    public enum DashStyle
    {
        Solid = 0,
        Dash = 1,
        Dot = 2,
        DashDot = 3,
        DashDotDot = 4,
        Custom = 5
    }

    public enum GradientType
    {
        Circular,
        Contour,
        Linear,
        Rectangular
    }

    public enum HatchStyle
    {
        Horizontal = 0,
        Vertical = 1,
        ForwardDiagonal = 2,
        BackwardDiagonal = 3,
        Cross = 4,
        DiagonalCross = 5,
    }

    public enum PointShape
    {
        Diamond,
        Ellipse, // Typically default for Mapsui
        Hexagon,
        Rectangle,
        Pentagon,
        Star,
        Triangle,
        Undefined
    }

    public enum FontStyle
    {
        Regular = 0,
        Bold = 1,
        Italic = 2,
        Underline = 4,
        Strikeout = 8
    }

    public enum WrapMode // For PicturePattern, Mapsui may handle differently
    {
        Tile,
        TileFlipX,
        TileFlipY,
        TileFlipXY,
        Clamp
    }


    // --- Base Classes ---
    public abstract class JsonBase
    {
        [JsonProperty("$type")] // For Newtonsoft.Json TypeNameHandling.Auto
        public string TypeDiscriminator => GetType().AssemblyQualifiedName;
    }

    public class Symbology : JsonBase
    {
        public List<Category> Categories { get; set; } = new List<Category>();
        public GeometryType GeometryType { get; set; }
        public RenderType RenderType { get; set; }
        public string FieldName { get; set; }
    }

    public class Category : JsonBase
    {
        public string LegendText { get; set; }
        public string FilterExpression { get; set; }
    }

    public class Symbol : JsonBase
    {
        public double Angle { get; set; }
        public Position2D Offset { get; set; }
        public Size2D Size { get; set; }
        public float Opacity { get; set; }
        public SymbolType SymbolType { get; set; }
        public string XmlColor { get; set; }
    }

    public class Pattern : JsonBase
    {
        public PatType PatType { get; set; } // Specific to DotSpatial
        public Outline Outline { get; set; } // Polygon outline
        public bool UseOutline { get; set; }
    }

    // --- Derived Category Classes ---
    public class PointCategory : Category
    {
        public Symbol Symbol { get; set; }
    }

    public class LineCategory : Category
    {
        public Outline Outline { get; set; }
    }

    public class PolygonCategory : Category
    {
        public Pattern Pattern { get; set; }
        public Outline Outline { get; set; }
    }


    // --- Derived Symbol Classes ---
    public class SimpleSymbol : Symbol
    {
        public string Color { get; set; } // Hex color #RRGGBB or #AARRGGBB
        public string OutlineColor { get; set; }
        public float OutlineOpacity { get; set; }
        public double OutlineWidth { get; set; }
        public PointShape PointShape { get; set; }
        public bool UseOutline { get; set; }
        public string XmlOutlineColor { get; set; } // DotSpatial internal color
    }

    public class CharacterSymbol : Symbol
    {
        public UnicodeCategory Category { get; set; } // From System.Globalization
        public char Character { get; set; }
        public byte CharacterSet { get; set; }
        public byte Code { get; set; }
        public System.Drawing.Color Color { get; set; } // System.Drawing.Color here
        public string FontFamilyName { get; set; }
        public FontStyle Style { get; set; } // System.Drawing.FontStyle
    }

    public class PictureSymbol : Symbol
    {
        public string ImageBase64String { get; set; }
        public string ImageFilename { get; set; }
        public System.Drawing.Color OutlineColor { get; set; } // System.Drawing.Color here
        public float OutlineOpacity { get; set; }
        public double OutlineWidth { get; set; }
        public bool UseOutline { get; set; }
        public string XmlOutlineColor { get; set; }
    }

    // --- Derived Pattern Classes ---
    public class SimplePattern : Pattern
    {
        public string FillColor { get; set; } // Hex color
        public float Opacity { get; set; } // 0.0 - 1.0
    }

    public class GradientPattern : Pattern
    {
        public float Angle { get; set; }
        public System.Drawing.Color[] Colors { get; set; } // System.Drawing.Color here
        public float[] Positions { get; set; }
        public GradientType GradientType { get; set; }
    }

    public class HatchPattern : Pattern
    {
        public HatchStyle HatchStyle { get; set; }
        public string ForeColor { get; set; } // Hex color
        public float ForeColorOpacity { get; set; }
        public System.Drawing.Color BackColor { get; set; } // System.Drawing.Color here
        public float BackColorOpacity { get; set; }
    }

    public class PicturePattern : Pattern
    {
        public float Angle { get; set; }
        public string PictureFilename { get; set; } // Not used by Mapsui
        public Position2D Scale { get; set; } // Mapsui handles scale via Brush properties
        public WrapMode WrapMode { get; set; } // Mapsui handles this via FillStyle
        public string ImageBase64String { get; set; } // Added for Mapsui
    }

    // --- Helper Structs ---
    [TypeConverter(typeof(Position2DConverter))]
    public class Position2D
    {
        public double X;
        public double Y;

        public Position2D()
        {
        }

        public Position2D(double x, double y)
        {
            X = x;
            Y = y;
        }
        public Coordinate ToCoordinate()
        {
            return new Coordinate(X, Y);
        }
    }

    [TypeConverter(typeof(Size2DConverter))]
    public class Size2D
    {
        public double Height;

        public double Width;

        public Size2D()
        {
        }

        public Size2D(double width, double height)
        {
            Width = width;
            Height = height;
        }

        public override bool Equals(object obj)
        {
            if (obj == null)
            {
                return false;
            }

            if (!(obj is Size2D size))
            {
                return false;
            }

            return Equals(size);
        }

        public bool Equals(Size2D size)
        {
            if ((object)size == null)
            {
                return false;
            }

            if (size.Width == Width)
            {
                return size.Height == Height;
            }

            return false;
        }

        public override string ToString()
        {
            return Width + ", " + Height;
        }

        public static bool operator ==(Size2D a, Size2D b)
        {
            if ((object)a == null && (object)b == null)
            {
                return true;
            }

            if ((object)a == null)
            {
                return false;
            }

            if ((object)b == null)
            {
                return false;
            }

            if (a.Width == b.Width)
            {
                return a.Height == b.Height;
            }

            return false;
        }

        public static bool operator !=(Size2D a, Size2D b)
        {
            if ((object)a == null && (object)b == null)
            {
                return false;
            }

            if ((object)a == null)
            {
                return true;
            }

            if ((object)b == null)
            {
                return true;
            }

            if (a.Width == b.Width)
            {
                return a.Height != b.Height;
            }

            return true;
        }
    }

    public class Outline // For LineCategory
    {
        public string Color { get; set; } // Hex Color
        public DashStyle DashStyle { get; set; }
        public float Opacity { get; set; } // 0.0 - 1.0
        public double Width { get; set; }
    }


    public class Position2DConverter : ExpandableObjectConverter
    {
        public override bool CanConvertFrom(ITypeDescriptorContext context, Type sourceType)
        {
            if (sourceType == typeof(string))
            {
                return true;
            }

            return base.CanConvertFrom(context, sourceType);
        }

        public override object ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, object value)
        {
            if (value is string)
            {
                try
                {
                    string text = (string)value;
                    string[] array = text.Split(',');
                    double x;
                    double y;
                    if (array.Length > 1)
                    {
                        x = double.Parse(array[0].Trim());
                        y = double.Parse(array[1].Trim());
                    }
                    else if (array.Length == 1)
                    {
                        x = double.Parse(array[0].Trim());
                        y = 0.0;
                    }
                    else
                    {
                        x = 0.0;
                        y = 0.0;
                    }

                    return new Position2D(x, y);
                }
                catch
                {
                    throw new ArgumentException(string.Concat("Cannot convert [", value, "] to pointF"));
                }
            }

            return base.ConvertFrom(context, culture, value);
        }

        public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType)
        {
            if (destinationType == typeof(string) && value.GetType() == typeof(Position2D))
            {
                Position2D position2D = (Position2D)value;
                return $"{position2D.X}, {position2D.Y}";
            }

            return base.ConvertTo(context, culture, value, destinationType);
        }
    }


    public class Size2DConverter : ExpandableObjectConverter
    {
        public override bool CanConvertFrom(ITypeDescriptorContext context, Type sourceType)
        {
            if (sourceType == typeof(string))
            {
                return true;
            }

            if (sourceType == typeof(Size2D))
            {
                return true;
            }

            return base.CanConvertFrom(context, sourceType);
        }

        public override object ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, object value)
        {
            if (value is string)
            {
                try
                {
                    string text = (string)value;
                    string[] array = text.Split(',');
                    double width;
                    double height;
                    if (array.Length > 1)
                    {
                        width = double.Parse(array[0].Trim());
                        height = double.Parse(array[1].Trim());
                    }
                    else if (array.Length == 1)
                    {
                        width = double.Parse(array[0].Trim());
                        height = 0.0;
                    }
                    else
                    {
                        width = 0.0;
                        height = 0.0;
                    }

                    return new Size2D(width, height);
                }
                catch
                {
                    throw new ArgumentException(string.Concat("Cannot convert [", value, "] to Size2D"));
                }
            }

            if (value is Size2D)
            {
                return value;
            }

            return base.ConvertFrom(context, culture, value);
        }

        public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType)
        {
            if (destinationType == typeof(string) && value.GetType() == typeof(Size2D))
            {
                Size2D size2D = (Size2D)value;
                return $"{size2D.Width}, {size2D.Height}";
            }

            if (destinationType == typeof(Size2D) && value.GetType() == typeof(Size2D))
            {
                return value;
            }

            return base.ConvertTo(context, culture, value, destinationType);
        }
    }
}