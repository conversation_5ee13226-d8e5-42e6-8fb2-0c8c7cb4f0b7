using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BaseGIS.Core.Entities;

namespace BaseGIS.Infrastructure.Persistence.Configurations
{
    public class DomainInfoConfiguration : IEntityTypeConfiguration<DomainInfo>
    {
        public void Configure(EntityTypeBuilder<DomainInfo> builder)
        {
            builder.HasKey(d => d.Id);
            builder.Property(d => d.Name).IsRequired().HasMaxLength(50);
            builder.HasOne(d => d.FieldInfo)
                   .WithMany(f => f.DomainInfos)
                   .HasForeignKey(d => d.FieldInfoId);
        }
    }
} 