﻿@model BaseGIS.Web.ViewModels.GenerateTileViewModel

@{
    ViewData["Title"] = "تولید تایل";
}

@section Styles {
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" />
    <style>
        .form-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

            .form-group label {
                font-weight: bold;
            }

            .form-group input:not([type=checkbox]) {
                width: 100%;
                padding: 8px;
                border: 1px solid #ccc;
                border-radius: 4px;
                font-family: Shabnam, sans-serif;
            }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn-submit {
            background-color: var(--primary-color, #007bff);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

            .btn-submit:hover {
                background-color: darken(var(--primary-color, #007bff), 10%);
            }

        .btn-cancel {
            background-color: var(--danger-color, #dc3545);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

            .btn-cancel:hover {
                background-color: darken(var(--danger-color, #dc3545), 10%);
            }

        #progressContainer {
            display: none;
            margin-top: 20px;
        }
    </style>
}

<div class="card shadow-sm form-container">
    <div class="card-header">
        <h6 class="mb-0">پارامترهای تولید تایل</h6>
    </div>
    <div class="card-body">
        <form id="generateTileForm" asp-action="GenerateTile" asp-controller="Database" method="post">
            @Html.AntiForgeryToken()
            <input type="hidden" asp-for="TableInfoId" />
            <input type="hidden" name="operationId" id="operationId" />

            <div class="form-group">
                <label asp-for="MinZoom">حداقل زوم</label>
                <input asp-for="MinZoom" type="number" min="0" max="20" required class="form-control" />
                <span asp-validation-for="MinZoom" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="MaxZoom">حداکثر زوم</label>
                <input asp-for="MaxZoom" type="number" min="0" max="20" required class="form-control" />
                <span asp-validation-for="MaxZoom" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="MinLat">حداقل عرض جغرافیایی (اختیاری)</label>
                <input asp-for="MinLat" type="number" step="any" class="form-control" />
                <span asp-validation-for="MinLat" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="MinLon">حداقل طول جغرافیایی (اختیاری)</label>
                <input asp-for="MinLon" type="number" step="any" class="form-control" />
                <span asp-validation-for="MinLon" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="MaxLat">حداکثر عرض جغرافیایی (اختیاری)</label>
                <input asp-for="MaxLat" type="number" step="any" class="form-control" />
                <span asp-validation-for="MaxLat" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="MaxLon">حداکثر طول جغرافیایی (اختیاری)</label>
                <input asp-for="MaxLon" type="number" step="any" class="form-control" />
                <span asp-validation-for="MaxLon" class="text-danger"></span>
            </div>

            <div class="form-group checkbox-group">
                <input asp-for="UseBatchData" type="checkbox" id="useBatchData" />
                <label for="useBatchData">خواندن تمام داده‌ها (یک اتصال به دیتابیس)</label>
            </div>

            <div class="form-group text-center">
                <button type="submit" class="btn-submit">تولید تایل</button>
                <a href="@Url.Action("Tables", "Database")" class="btn-cancel">لغو</a>
            </div>

            <div id="progressContainer">
                <div class="progress" role="progressbar" aria-label="Progress" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%"></div>
                </div>
                <div class="text-center mt-2">
                    <span id="progressText">0% کامل شده</span>
                </div>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/lib/jquery-validation/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
    <script src="~/lib/sweetalert2/sweetalert2.min.js"></script>
    <script src="~/lib/signalr/dist/browser/signalr.min.js"></script>
    <script>
        $(document).ready(function () {
            // Generate a unique operation ID
            const operationId = 'op_' + Math.random().toString(36).substr(2, 9);
            $('#operationId').val(operationId);

            // Set up SignalR
            const connection = new signalR.HubConnectionBuilder()
                .withUrl("/tileProgressHub")
                .withAutomaticReconnect()
                .build();

            // Handle progress updates
            connection.on("ReceiveProgress", function (progress, message) {
                $("#progressContainer").show();
                $("#progressBar").css("width", progress + "%").attr("aria-valuenow", progress);
                $("#progressText").text(`${progress}% کامل شده`);
                if (progress >= 100) {
                    $("#progressBar").removeClass("progress-bar-animated");
                    Swal.close();
                }
            });

            // Handle errors
            connection.on("ReceiveError", function (errorMessage) {
                Swal.close();
                $("#progressContainer").hide();
                showToast(errorMessage, "error");
                // Leave group after error
                connection.invoke("LeaveOperationGroup", operationId).catch(err => console.error(err));
            });

            // Start SignalR connection
            connection.start().then(function () {
                // Join the operation group
                connection.invoke("JoinOperationGroup", operationId).catch(err => console.error(err));
            }).catch(function (err) {
                console.error("SignalR Connection Error: ", err.toString());
                showToast("خطا در اتصال به سرور.", "error");
            });

            $("#generateTileForm").on("submit", function (e) {
                e.preventDefault();
                var form = $(this);

                if (form.valid()) {
                    Swal.fire({
                        title: 'در حال پردازش...',
                        text: 'لطفاً منتظر بمانید، تایل‌ها در حال تولید هستند.',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                    $("#progressContainer").show();
                    $("#progressBar").css("width", "0%").attr("aria-valuenow", 0);
                    $("#progressText").text("0% کامل شده");
                    $("#progressBar").addClass("progress-bar-animated");

                    $.ajax({
                        url: form.attr("action"),
                        type: "POST",
                        data: form.serialize(),
                        headers: {
                            'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function (response) {
                            if (response.success) {
                                showToast(response.message, "success");
                                // Leave group after success
                                connection.invoke("LeaveOperationGroup", operationId).catch(err => console.error(err));
                                setTimeout(() => {
                                    window.location.href = '@Url.Action("Map", "Map")';
                                }, 2000);
                            } else {
                                Swal.close();
                                $("#progressContainer").hide();
                                showToast(response.message, "error");
                                connection.invoke("LeaveOperationGroup", operationId).catch(err => console.error(err));
                            }
                        },
                        error: function () {
                            Swal.close();
                            $("#progressContainer").hide();
                            showToast("خطا در ارتباط با سرور.", "error");
                            connection.invoke("LeaveOperationGroup", operationId).catch(err => console.error(err));
                        }
                    });
                }
            });

            function showToast(message, type) {
                Swal.fire({
                    toast: true,
                    position: 'top-end',
                    icon: type,
                    title: message,
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true
                });
            }
        });
    </script>
}