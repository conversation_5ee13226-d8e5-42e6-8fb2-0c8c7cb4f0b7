using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BaseGIS.Core.Entities;

namespace BaseGIS.Infrastructure.Persistence.Configurations
{
    public class TileMetaConfiguration : IEntityTypeConfiguration<TileMeta>
    {
        public void Configure(EntityTypeBuilder<TileMeta> builder)
        {
            builder.HasKey(t => t.Id);
            builder.Property(t => t.FilePath).IsRequired();
            builder.Property(t => t.Size).IsRequired();
            builder.Property(t => t.CreatedAt).IsRequired();
        }
    }
} 