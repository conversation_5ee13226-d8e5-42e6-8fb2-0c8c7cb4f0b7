using System;

namespace BaseGIS.Core.Entities
{
    public class TileMeta
    {
        public int Id { get; set; }
        public int TableInfoId { get; set; }
        public int Z { get; set; }
        public int X { get; set; }
        public int Y { get; set; }
        public string FilePath { get; set; } = string.Empty;
        public long Size { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
} 