﻿using System.Data;

namespace BaseGIS.Web.Helper
{
    public class Convertion
    {
        public static List<T> ConvertToList<T>(DataTable dt)
        {
            var columnNames = dt.Columns.Cast<DataColumn>()
                    .Select(c => c.ColumnName)
                    .ToList();
            var properties = typeof(T).GetProperties();
            return dt.AsEnumerable().Select(row =>
            {
                var objT = Activator.CreateInstance<T>();
                foreach (var pro in properties)
                {
                    if (columnNames.Contains(pro.Name))
                    {
                        System.Reflection.PropertyInfo pI = objT.GetType().GetProperty(pro.Name);
                        pro.SetValue(objT, row[pro.Name] == DBNull.Value ? null : Convert.ChangeType(row[pro.Name], pI.PropertyType));
                    }
                }
                return objT;
            }).ToList();
        }


        public static string ConvertDateTimeToPersian(DateTime? datetime)
        {

            try
            {
                if (datetime == null)
                    return "";

                DateTime dt = DateTime.Parse(datetime.ToString());
                System.Globalization.PersianCalendar pc = new System.Globalization.PersianCalendar();
                return pc.GetYear(dt) + "/" + pc.GetMonth(dt).ToString("00") + "/" + pc.GetDayOfMonth(dt).ToString("00");
            }
            catch
            {

            }
            return "";


        }
        public static string ConvertDateTimeToPersianLong(DateTime datetime)
        {

            try
            {
                System.Globalization.PersianCalendar pc = new System.Globalization.PersianCalendar();
                return pc.GetYear(datetime) + "/" + pc.GetMonth(datetime).ToString("00") + "/" + pc.GetDayOfMonth(datetime).ToString("00") + " " + pc.GetHour(datetime).ToString("00") + ":" + pc.GetMinute(datetime).ToString("00");
            }
            catch
            {

            }
            return "";


        }

        public static string ConvertDateTimeToPersianLong(string datetime1)
        {

            try
            {
                DateTime datetime = DateTime.Parse(datetime1);
                System.Globalization.PersianCalendar pc = new System.Globalization.PersianCalendar();
                return pc.GetYear(datetime) + "/" + pc.GetMonth(datetime).ToString("00") + "/" + pc.GetDayOfMonth(datetime).ToString("00") + " " + pc.GetHour(datetime).ToString("00") + ":" + pc.GetMinute(datetime).ToString("00");
            }
            catch
            {

            }
            return "";


        }

        public static string GetDayInMonth(DateTime datetime)
        {

            try
            {
                System.Globalization.PersianCalendar pc = new System.Globalization.PersianCalendar();
                return pc.GetDayOfMonth(datetime).ToString();
            }
            catch
            {

            }
            return "";
        }

        public static string GetMonthName(DateTime date)
        {

            try
            {
                System.Globalization.PersianCalendar jc = new System.Globalization.PersianCalendar();
                string pdate = string.Format("{0:0000}/{1:00}/{2:00}", jc.GetYear(date), jc.GetMonth(date), jc.GetDayOfMonth(date));

                string[] dates = pdate.Split('/');
                int month = Convert.ToInt32(dates[1]);

                switch (month)
                {
                    case 1: return "فررودين";
                    case 2: return "ارديبهشت";
                    case 3: return "خرداد";
                    case 4: return "تير‏";
                    case 5: return "مرداد";
                    case 6: return "شهريور";
                    case 7: return "مهر";
                    case 8: return "آبان";
                    case 9: return "آذر";
                    case 10: return "دي";
                    case 11: return "بهمن";
                    case 12: return "اسفند";
                    default: return "";
                }
            }
            catch
            {
            }
            return "";

        }



        public static double ConvertDouble(string value)
        {
            double val = double.NaN;
            double.TryParse(value, out val);
            return val;
        }


        public static DateTime? ConvertPersianToMiladi(string datetime)
        {

            try
            {
                string[] param = datetime.Split('/');
                System.Globalization.PersianCalendar pc = new System.Globalization.PersianCalendar();
                return pc.ToDateTime(int.Parse(param[0]), int.Parse(param[1]), int.Parse(param[2]), 0, 0, 0, 0);
            }
            catch
            {

            }
            return null;


        }

    }
}
