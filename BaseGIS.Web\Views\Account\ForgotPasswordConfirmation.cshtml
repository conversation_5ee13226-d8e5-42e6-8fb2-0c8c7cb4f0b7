﻿@{
    ViewData["Title"] = "تأیید فراموشی رمز عبور";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}
<div class="container-fluid login-container">
    <div class="row min-vh-100">
        <!-- Left Section -->
        <div class="col-md-6 d-flex align-items-center justify-content-center text-white p-5">
            <div>
                <h4 class="mb-3">
                    <i class="fas fa-cube me-2"></i> LandInquiryApp
                </h4>
                <h1 class="mb-4">تأیید بازنشانی رمز عبور</h1>
                <p class="lead mb-4">
                    لینک بازنشانی رمز عبور به ایمیل شما ارسال شد. لطفاً ایمیل خود را بررسی کنید.
                </p>
                <a href="#" class="text-white text-decoration-none fw-bold">بیشتر بدانید <i class="fas fa-arrow-right ms-2"></i></a>
                <footer class="mt-5 Atmos-white-50 small">
                    1404 © LandInquiryApp
                </footer>
            </div>
        </div>

        <!-- Right Section -->
        <div class="col-md-6 d-flex align-items-center justify-content-center p-5">
            <div class="card shadow-lg login-card">
                <div class="card-body p-5">
                    <div class="text-end mb-4">
                        <a asp-action="Login" class="text-muted small">بازگشت به ورود</a>
                    </div>
                    <h5 class="mb-4">تأیید ارسال لینک</h5>
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success">
                            @TempData["SuccessMessage"]
                        </div>
                    }
                    <p>لطفاً ایمیل خود را بررسی کنید و روی لینک بازنشانی کلیک کنید.</p>
                    <a asp-action="Login" class="btn btn-primary w-100">بازگشت به ورود</a>
                </div>
            </div>
        </div>
    </div>
</div>