﻿@using Microsoft.AspNetCore.Identity
@using BaseGIS.Core.Entities
@inject UserManager<ApplicationUser> UserManager
@{
    var userFullName = "";
    var userRoleName = "";
    var userImage = "../../img/avatars/male.png";
    var companyName = "BaseGIS";

    if (User?.Identity?.IsAuthenticated == true)
    {
        var user = await UserManager.GetUserAsync(User);
        if (user != null)
        {
            userFullName = user.FullName ?? user.UserName;
            var roles = await UserManager.GetRolesAsync(user);
            userRoleName = roles.FirstOrDefault() ?? "";
            userImage = user.Image ?? userImage;
        }
    }
}

<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - BaseGIS</title>
    <link rel="stylesheet" href="~/lib/bootstrap/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="~/lib/font-awesome/css/all.css" />
    <link rel="stylesheet" href="~/lib/leaflet/leaflet.css" />
    <link rel="stylesheet" href="~/lib/leaflet-draw/dist/leaflet.draw.css" />
    <link rel="stylesheet" href="~/lib/sweetalert2/sweetalert2.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    @await RenderSectionAsync("Styles", required: false)
</head>
<body class="d-flex flex-column min-vh-100 sidebar_open">
    <!-- Toast -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>

    <!-- هدر -->
    <header id="header_main" class="border-bottom fixed-top header-app">
        <div class="container-fluid d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center gap-3">
                <img src="~/img/logo.png" alt="Logo" style="height: 40px;" />
                <div class="header-brand">@companyName</div>
            </div>
            <div class="d-flex align-items-center gap-3">
                <div>
                    <a asp-controller="Account" asp-action="Profile" class="header-item">@userFullName</a>
                    <p class="d-block font-small header-item mb-0">@userRoleName</p>
                </div>
                <div class="dropdown">
                    <img src="@userImage" alt="Avatar" class="rounded-circle" style="width: 40px; height: 40px; cursor: pointer;" data-bs-toggle="dropdown" />
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" asp-controller="Account" asp-action="Profile"><i class="fas fa-user me-2"></i>@userFullName (@userRoleName)</a></li>
                        <li>
                            <form asp-controller="Account" asp-action="Logout" method="post">
                                <button type="submit" class="dropdown-item"><i class="fas fa-sign-out-alt me-2"></i>خروج</button>
                            </form>
                        </li>
                    </ul>
                </div>
                <button id="settings_btn" class="btn btn-link" title="تنظیمات">
                    <i class="fas fa-cog fa-lg"></i>
                </button>
                <button id="sidebar_toggle" class="btn btn-link" title="تغییر حالت سایدبار">
                    <i class="fas fa-bars fa-lg"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- سایدبار -->
    <aside id="sidebar_main" class="text-white sidebar-nav">
        <div class="sidebar_content p-1">
            <ul class="nav flex-column">
                @if (User?.Identity?.IsAuthenticated == true)
                {
                    <li class="nav-item">
                        <a class="nav-link text-white @IsActive("Home", "Index")" asp-controller="Home" asp-action="Index">
                            <i class="fas fa-home me-1"></i>
                            <span class="menu-text">داشبورد</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white @IsActive("Map", "Index")" asp-controller="Map" asp-action="Index">
                            <i class="fas fa-map me-1"></i>
                            <span class="menu-text">نقشه</span>
                        </a>
                    </li>

                    <!-- منوی مدیریت پایگاه داده -->
                    <li class="nav-item">
                        <a class="nav-link text-white dropdown-toggle @IsActive("Database", null)" href="#" data-bs-toggle="collapse" data-bs-target="#databaseSubmenu" aria-expanded="false">
                            <i class="fas fa-database me-1"></i>
                            <span class="menu-text">مدیریت پایگاه داده</span>
                        </a>
                        <div id="databaseSubmenu" class="collapse @((ViewContext.RouteData.Values["controller"]?.ToString() == "Database") ? "show" : "")">
                            <ul class="nav flex-column ms-3">
                                <li class="nav-item">
                                    <a class="nav-link text-white @IsActive("Database", "Groups")" asp-controller="Database" asp-action="Groups">
                                        <i class="fas fa-users me-1"></i>
                                        <span class="menu-text">گروه‌ها</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link text-white @IsActive("Database", "Tables")" asp-controller="Database" asp-action="Tables">
                                        <i class="fas fa-table me-1"></i>
                                        <span class="menu-text">جداول</span>
                                    </a>
                                </li> 
                                <li class="nav-item">
                                    <a class="nav-link text-white @IsActive("Database", "SymbologyManager")" asp-controller="Database" asp-action="SymbologyManager">
                                        <i class="fas fa-table me-1"></i>
                                        <span class="menu-text">نمادگذاری</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>

                    @if (User.IsInRole("Admin"))
                    {
                        <li class="nav-item">
                            <a class="nav-link text-white @IsActive("User", "Index")" asp-controller="Admin" asp-action="Index">
                                <i class="fas fa-cogs me-1"></i>
                                <span class="menu-text">مدیریت</span>
                            </a>
                        </li>
                    }
                }
                else
                {
                    <li class="nav-item">
                        <a class="nav-link text-white" asp-controller="Account" asp-action="Login">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            <span class="menu-text">ورود</span>
                        </a>
                    </li>
                }
            </ul>
        </div>
    </aside>

    <!-- پنل تنظیمات -->
    <div id="settings_panel" class="bg-white shadow">
        <div class="settings_content">
            <div class="card h-100 shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title">تنظیمات</h6>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="color_palette">پالت رنگی</label>
                        <select id="color_palette" class="form-control">
                            <option value="default">پیش‌فرض</option>
                            <option value="blue">آبی</option>
                            <option value="green">سبز</option>
                            <option value="red">قرمز</option>
                            <option value="purple">بنفش</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="font_size">سایز فونت</label>
                        <select id="font_size" class="form-control">
                            <option value="small">کوچک</option>
                            <option value="medium">متوسط</option>
                            <option value="large">بزرگ</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="font_color">رنگ فونت</label>
                        <select id="font_color" class="form-control">
                            <option value="default">پیش‌فرض</option>
                            <option value="dark">تیره</option>
                            <option value="light">روشن</option>
                        </select>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="dark_mode">
                        <label class="form-check-label" for="dark_mode">حالت شب</label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- محتوای اصلی -->
    <div id="page_content">
        @RenderBody()
    </div>

    <!-- اسکریپت‌ها -->
    <script src="~/lib/jquery/jquery.min.js"></script>
    <script src="~/lib/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js"></script>

    <script src="~/lib/leaflet/leaflet.js"></script>
    <script src="~/lib/leaflet-draw/dist/leaflet.draw.js"></script>
    <script src="~/lib/sweetalert2/sweetalert2.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)

    <script>
        $(document).ready(function () {
            const $body = $('body');
            const $sidebarToggle = $('#sidebar_toggle');
            const $settingsBtn = $('#settings_btn');
            const $colorPaletteSelect = $('#color_palette');
            const $fontSizeSelect = $('#font_size');
            const $fontColorSelect = $('#font_color');
            const $darkModeCheckbox = $('#dark_mode');

            // مقداردهی اولیه از localStorage
            let savedPalette = localStorage.getItem('colorPalette') || 'default';
            let savedFontSize = localStorage.getItem('fontSize') || 'medium';
            let savedFontColor = localStorage.getItem('fontColor') || 'dark';
            let isDarkMode = localStorage.getItem('darkMode') === 'true';

            $colorPaletteSelect.val(savedPalette);
            $fontSizeSelect.val(savedFontSize);
            $fontColorSelect.val(savedFontColor);
            $darkModeCheckbox.prop('checked', isDarkMode);

            applySettings(savedPalette, savedFontSize, isDarkMode, savedFontColor);

            let savedSidebarState = localStorage.getItem('sidebarState') || 'sidebar_open';
            $body.removeClass('sidebar_mini sidebar_open').addClass(savedSidebarState === 'mini' ? 'sidebar_mini' : 'sidebar_open');

            $sidebarToggle.on('click', function () {
                if ($body.hasClass('sidebar_mini')) {
                    $body.removeClass('sidebar_mini').addClass('sidebar_open');
                    localStorage.setItem('sidebarState', 'open');
                } else {
                    $body.removeClass('sidebar_open').addClass('sidebar_mini');
                    localStorage.setItem('sidebarState', 'mini');
                }
            });

            $settingsBtn.on('click', function () {
                $body.toggleClass('settings_open');
            });

            $colorPaletteSelect.on('change', function () {
                savedPalette = $(this).val();
                localStorage.setItem('colorPalette', savedPalette);
                applySettings(savedPalette, savedFontSize, isDarkMode, savedFontColor);
            });

            $fontSizeSelect.on('change', function () {
                savedFontSize = $(this).val();
                localStorage.setItem('fontSize', savedFontSize);
                applySettings(savedPalette, savedFontSize, isDarkMode, savedFontColor);
            });

            $fontColorSelect.on('change', function () {
                savedFontColor = $(this).val();
                localStorage.setItem('fontColor', savedFontColor);
                applySettings(savedPalette, savedFontSize, isDarkMode, savedFontColor);
            });

            $darkModeCheckbox.on('change', function () {
                isDarkMode = $(this).is(':checked');
                localStorage.setItem('darkMode', isDarkMode);
                applySettings(savedPalette, savedFontSize, isDarkMode, savedFontColor);
            });

            function applySettings(palette, fontSize, isDarkMode, fontColor) {
                $body.removeClass('palette-blue palette-green palette-red palette-purple palette-dark font-small font-medium font-large font-color-dark font-color-light');
                if (isDarkMode) {
                    $body.addClass('palette-dark');
                } else if (palette !== 'default') {
                    $body.addClass(`palette-${palette}`);
                }
                $body.addClass(`font-${fontSize} font-color-${fontColor}`);
            }

          // نمایش پیام‌های Toast
        @if (TempData["SuccessMessage"] != null)
        {
            <text>showToast("@TempData["SuccessMessage"]", "success");</text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>showToast("@TempData["ErrorMessage"]", "error");</text>
        }
        });

    </script>
</body>
</html>

@functions {
    private string IsActive(string controller, string action)
    {
        var currentController = ViewContext.RouteData.Values["controller"]?.ToString();
        var currentAction = ViewContext.RouteData.Values["action"]?.ToString();
        return (currentController == controller && (action == null || currentAction == action)) ? "active" : "";
    }
}