using BaseGIS.Core.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BaseGIS.Core.Interfaces
{
    public interface ITableInfoRepository
    {
        /// <summary>
        /// دریافت TableInfo بر اساس شناسه
        /// </summary>
        /// <param name="id">شناسه TableInfo</param>
        /// <returns>TableInfo یا null اگر پیدا نشود</returns>
        Task<TableInfo> GetByIdAsync(int id);

         
        Task<List<TableInfo>> GetAllAsync();
 
        Task AddAsync(TableInfo tableInfo);
 
        Task UpdateAsync(TableInfo tableInfo);
 
        Task DeleteAsync(int id);
    }
}