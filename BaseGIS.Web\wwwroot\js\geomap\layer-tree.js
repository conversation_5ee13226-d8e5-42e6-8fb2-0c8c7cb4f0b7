/**
 * Layer Tree Component
 * کامپوننت درخت لایه‌ها
 */

class LayerTree extends BaseComponent {
    getDefaultOptions() {
        return {
            ...super.getDefaultOptions(),
            treeData: null,
            layerGroups: [],
            multiSelect: true,
            showCheckboxes: true,
            showContextMenu: true,
            expandAll: false,
            onLayerToggle: null,
            onLayerSelect: null,
            onLayerProperties: null,
            onLayerSymbology: null
        };
    }

    beforeInit() {
        this.selectedLayers = new Set();
        this.expandedGroups = new Set();
        this.contextMenu = null;
    }

    render() {
        this.renderTreeStructure();
        this.initializeFancyTree();
        this.setupContextMenu();
    }

    renderTreeStructure() {
        const treeHtml = this.generateTreeHtml();
        this.element.innerHTML = treeHtml;
    }

    generateTreeHtml() {
        if (!this.options.layerGroups || this.options.layerGroups.length === 0) {
            return '<div class="no-layers">هیچ لایه‌ای یافت نشد</div>';
        }

        let html = '<div class="layer-tree-container">';
        html += '<div class="tree-toolbar">';
        html += '<button class="btn btn-sm btn-outline-secondary expand-all" title="باز کردن همه"><i class="fas fa-expand-alt"></i></button>';
        html += '<button class="btn btn-sm btn-outline-secondary collapse-all" title="بستن همه"><i class="fas fa-compress-alt"></i></button>';
        html += '<button class="btn btn-sm btn-outline-secondary refresh-tree" title="بروزرسانی"><i class="fas fa-sync-alt"></i></button>';
        html += '</div>';
        html += '<div id="layer-tree" class="fancytree-container"></div>';
        html += '</div>';

        return html;
    }

    initializeFancyTree() {
        const treeData = this.convertToFancyTreeData();
        
        $("#layer-tree").fancytree({
            extensions: ["glyph", "wide"],
            glyph: {
                preset: "awesome5",
                map: {
                    folder: "fas fa-folder",
                    folderOpen: "fas fa-folder-open",
                    doc: "fas fa-layer-group",
                    docOpen: "fas fa-layer-group"
                }
            },
            wide: {
                iconWidth: "1em",
                iconSpacing: "0.5em",
                levelOfs: "1.5em"
            },
            checkbox: this.options.showCheckboxes,
            selectMode: this.options.multiSelect ? 3 : 1,
            source: treeData,
            activate: (event, data) => {
                this.handleNodeActivate(data.node);
            },
            select: (event, data) => {
                this.handleNodeSelect(data.node, data.node.isSelected());
            },
            expand: (event, data) => {
                this.handleNodeExpand(data.node, data.node.isExpanded());
            },
            renderNode: (event, data) => {
                this.renderCustomNode(data.node);
            }
        });

        this.tree = $("#layer-tree").fancytree("getTree");
    }

    convertToFancyTreeData() {
        const treeData = [];

        this.options.layerGroups.forEach(group => {
            const groupNode = {
                title: group.aliasName || group.name,
                key: `G${group.id}`,
                folder: true,
                expanded: this.options.expandAll || group.isExpanded,
                children: []
            };

            group.layers.forEach(layer => {
                const layerNode = {
                    title: this.generateLayerTitle(layer),
                    key: `L${layer.id}`,
                    selected: layer.isVisible,
                    data: {
                        layerId: layer.id,
                        layerName: layer.name,
                        aliasName: layer.aliasName,
                        datasetType: layer.datasetType,
                        isEditable: layer.isEditable,
                        tableId: layer.tableId
                    },
                    icon: this.getLayerIcon(layer.datasetType)
                };

                groupNode.children.push(layerNode);
            });

            treeData.push(groupNode);
        });

        return treeData;
    }

    generateLayerTitle(layer) {
        const title = layer.aliasName || layer.name;
        const maxLength = 25;
        
        if (title.length > maxLength) {
            return title.substring(0, maxLength) + '...';
        }
        
        return title;
    }

    getLayerIcon(datasetType) {
        const icons = {
            'point': 'fas fa-map-pin',
            'line': 'fas fa-route',
            'polygon': 'fas fa-draw-polygon',
            'raster': 'fas fa-image',
            'table': 'fas fa-table'
        };
        
        return icons[datasetType?.toLowerCase()] || 'fas fa-layer-group';
    }

    renderCustomNode(node) {
        if (!node.data) return;

        const $nodeSpan = $(node.span);
        
        // Add layer controls
        if (!node.folder) {
            const controls = this.createLayerControls(node.data);
            $nodeSpan.append(controls);
        }
    }

    createLayerControls(layerData) {
        const controls = $(`
            <div class="layer-controls">
                <button class="btn btn-xs btn-outline-secondary layer-properties" 
                        data-layer-id="${layerData.layerId}" 
                        title="خصوصیات لایه">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="btn btn-xs btn-outline-secondary layer-symbology" 
                        data-layer-id="${layerData.layerId}" 
                        title="نمادشناسی">
                    <i class="fas fa-palette"></i>
                </button>
                <button class="btn btn-xs btn-outline-secondary layer-zoom" 
                        data-layer-id="${layerData.layerId}" 
                        title="زوم به لایه">
                    <i class="fas fa-search-plus"></i>
                </button>
            </div>
        `);

        return controls;
    }

    setupContextMenu() {
        if (!this.options.showContextMenu) return;

        $("#layer-tree").contextmenu({
            delegate: ".fancytree-node",
            menu: [
                { title: "خصوصیات لایه", cmd: "properties", uiIcon: "fas fa-cog" },
                { title: "نمادشناسی", cmd: "symbology", uiIcon: "fas fa-palette" },
                { title: "زوم به لایه", cmd: "zoom", uiIcon: "fas fa-search-plus" },
                "----",
                { title: "صادرات لایه", cmd: "export", uiIcon: "fas fa-download" },
                { title: "حذف از نقشه", cmd: "remove", uiIcon: "fas fa-trash" }
            ],
            select: (event, ui) => {
                this.handleContextMenuSelect(ui.cmd, ui.target);
            },
            beforeOpen: (event, ui) => {
                const node = $.ui.fancytree.getNode(ui.target);
                return !node.folder; // Only show context menu for layers, not groups
            }
        });
    }

    bindEvents() {
        super.bindEvents();

        // Tree toolbar events
        this.find('.expand-all').addEventListener('click', () => this.expandAll());
        this.find('.collapse-all').addEventListener('click', () => this.collapseAll());
        this.find('.refresh-tree').addEventListener('click', () => this.refreshTree());

        // Layer control events
        $(this.element).on('click', '.layer-properties', (e) => {
            e.stopPropagation();
            this.showLayerProperties(e.target.dataset.layerId);
        });

        $(this.element).on('click', '.layer-symbology', (e) => {
            e.stopPropagation();
            this.showLayerSymbology(e.target.dataset.layerId);
        });

        $(this.element).on('click', '.layer-zoom', (e) => {
            e.stopPropagation();
            this.zoomToLayer(e.target.dataset.layerId);
        });
    }

    handleNodeActivate(node) {
        if (node.folder) return;

        this.trigger('layerSelect', {
            layerId: node.data.layerId,
            layerData: node.data,
            node: node
        });

        if (this.options.onLayerSelect) {
            this.options.onLayerSelect(node.data.layerId, node.data);
        }
    }

    handleNodeSelect(node, isSelected) {
        if (node.folder) return;

        const layerId = node.data.layerId;
        
        if (isSelected) {
            this.selectedLayers.add(layerId);
        } else {
            this.selectedLayers.delete(layerId);
        }

        this.trigger('layerToggle', {
            layerId: layerId,
            isVisible: isSelected,
            layerData: node.data,
            node: node
        });

        if (this.options.onLayerToggle) {
            this.options.onLayerToggle(layerId, isSelected, node.data);
        }
    }

    handleNodeExpand(node, isExpanded) {
        const groupId = node.key;
        
        if (isExpanded) {
            this.expandedGroups.add(groupId);
        } else {
            this.expandedGroups.delete(groupId);
        }
    }

    handleContextMenuSelect(command, target) {
        const node = $.ui.fancytree.getNode(target);
        if (!node || node.folder) return;

        const layerId = node.data.layerId;

        switch (command) {
            case 'properties':
                this.showLayerProperties(layerId);
                break;
            case 'symbology':
                this.showLayerSymbology(layerId);
                break;
            case 'zoom':
                this.zoomToLayer(layerId);
                break;
            case 'export':
                this.exportLayer(layerId);
                break;
            case 'remove':
                this.removeLayer(layerId);
                break;
        }
    }

    async showLayerProperties(layerId) {
        try {
            this.trigger('layerProperties', { layerId });
            
            if (this.options.onLayerProperties) {
                this.options.onLayerProperties(layerId);
            } else if (window.geoMapCore) {
                await window.geoMapCore.showLayerProperties(layerId);
            }
        } catch (error) {
            console.error('Error showing layer properties:', error);
        }
    }

    showLayerSymbology(layerId) {
        this.trigger('layerSymbology', { layerId });
        
        if (this.options.onLayerSymbology) {
            this.options.onLayerSymbology(layerId);
        } else if (window.geoMapCore) {
            window.geoMapCore.showLayerSymbology(layerId);
        }
    }

    zoomToLayer(layerId) {
        this.trigger('layerZoom', { layerId });
        
        // Implementation would zoom map to layer extent
        console.log('Zooming to layer:', layerId);
    }

    exportLayer(layerId) {
        this.trigger('layerExport', { layerId });
        
        // Implementation would export layer data
        console.log('Exporting layer:', layerId);
    }

    removeLayer(layerId) {
        const node = this.tree.getNodeByKey(`L${layerId}`);
        if (node) {
            node.setSelected(false);
            this.selectedLayers.delete(layerId);
            
            this.trigger('layerRemove', { layerId });
        }
    }

    expandAll() {
        if (this.tree) {
            this.tree.expandAll();
        }
    }

    collapseAll() {
        if (this.tree) {
            this.tree.expandAll(false);
        }
    }

    async refreshTree() {
        try {
            // Reload tree data
            this.trigger('beforeRefresh');
            
            // This would reload the tree data from server
            await this.reloadTreeData();
            
            this.trigger('refreshed');
        } catch (error) {
            console.error('Error refreshing tree:', error);
        }
    }

    async reloadTreeData() {
        // Implementation would reload tree data from server
        console.log('Reloading tree data...');
    }

    getSelectedLayers() {
        return Array.from(this.selectedLayers);
    }

    selectLayer(layerId, select = true) {
        const node = this.tree?.getNodeByKey(`L${layerId}`);
        if (node) {
            node.setSelected(select);
        }
    }

    expandGroup(groupId, expand = true) {
        const node = this.tree?.getNodeByKey(`G${groupId}`);
        if (node) {
            node.setExpanded(expand);
        }
    }

    updateLayerVisibility(layerId, visible) {
        this.selectLayer(layerId, visible);
    }

    destroy() {
        if (this.tree) {
            this.tree.destroy();
        }
        
        super.destroy();
    }
}

// Register component
window.ComponentFactory.register('layer-tree', LayerTree);

// Global functions for backward compatibility
window.refreshTree = function() {
    const instance = window.ComponentFactory.getGlobalInstance('layer-tree');
    if (instance && instance.refreshTree) {
        instance.refreshTree();
    }
};

window.SettingLayer = function(layerid) {
    const instance = window.ComponentFactory.getGlobalInstance('layer-tree');
    if (instance && instance.showLayerProperties) {
        instance.showLayerProperties(layerid);
    }
};

window.setSymbologyByID = function(layerid, sym) {
    const instance = window.ComponentFactory.getGlobalInstance('layer-tree');
    if (instance && instance.setSymbology) {
        instance.setSymbology(layerid, sym);
    }
};

window.setPropertyProps = function(layerid) {
    const instance = window.ComponentFactory.getGlobalInstance('layer-tree');
    if (instance && instance.setPropertyProps) {
        instance.setPropertyProps(layerid);
    }
};

window.setlableopacity = function() {
    const instance = window.ComponentFactory.getGlobalInstance('layer-tree');
    if (instance && instance.setLabelOpacity) {
        instance.setLabelOpacity();
    }
};

window.setSymbology = function() {
    const instance = window.ComponentFactory.getGlobalInstance('layer-tree');
    if (instance && instance.setSymbology) {
        instance.setSymbology();
    }
};

window.getVisibleLayerIds = function() {
    const instance = window.ComponentFactory.getGlobalInstance('layer-tree');
    if (instance && instance.getSelectedLayers) {
        return instance.getSelectedLayers();
    }
    return [];
};

window.getVisibleLayers = function() {
    const instance = window.ComponentFactory.getGlobalInstance('layer-tree');
    if (instance && instance.getVisibleLayers) {
        return instance.getVisibleLayers();
    }
    return '';
};

window.ZoomToExtentLayer = function(layerId, selected) {
    const instance = window.ComponentFactory.getGlobalInstance('layer-tree');
    if (instance && instance.zoomToLayer) {
        instance.zoomToLayer(layerId);
    }
};

window.refreshMapLayer = function(layerId) {
    const instance = window.ComponentFactory.getGlobalInstance('layer-tree');
    if (instance && instance.refreshMapLayer) {
        instance.refreshMapLayer(layerId);
    }
};
