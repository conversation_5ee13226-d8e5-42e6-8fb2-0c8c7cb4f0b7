﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <ItemGroup>
    <ProjectReference Include="..\BaseGIS.Core\BaseGIS.Core.csproj" />
    <ProjectReference Include="..\BaseGIS.Application\BaseGIS.Application.csproj" />
    <ProjectReference Include="..\BaseGIS.Infrastructure\BaseGIS.Infrastructure.csproj" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
	<ItemGroup>
		 
		<PackageReference Include="GeoAPI.CoordinateSystems" Version="1.7.5" />
		 
		<PackageReference Include="Mapsui" Version="4.1.9" />		 
		<PackageReference Include="Mapsui.Nts" Version="4.1.9" />		 
		<PackageReference Include="Mapsui.Rendering.Skia" Version="4.1.9" />		 
		<PackageReference Include="Mapsui.Tiling" Version="4.1.9" />
		
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.5" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.5" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite" Version="9.0.5" />
		<PackageReference Include="NetTopologySuite.IO.GeoJSON" Version="4.0.0" />
		<PackageReference Include="NetTopologySuite.IO.Shapefile" Version="2.1.0" />
		<PackageReference Include="GeoJSON.Net" Version="1.4.1" />
		<PackageReference Include="NetTopologySuite.IO.SqlServerBytes" Version="2.1.0" />
		<PackageReference Include="NetTopologySuite.IO.VectorTiles" Version="1.1.0" />
		<PackageReference Include="NetTopologySuite.IO.VectorTiles.Mapbox" Version="1.1.0" />
		<PackageReference Include="NPOI" Version="2.7.3" />
		<PackageReference Include="ProjNet" Version="2.0.0" />
		<PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
		<PackageReference Include="System.Drawing.Common" Version="9.0.5" />
		<PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.4" /> 
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="SharpZipLib" Version="1.4.2" />
		<PackageReference Include="Microsoft.SqlServer.Types" Version="160.1000.6" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>
	<ItemGroup>
	  <Folder Include="DTOs\" />
	  <Folder Include="SqlServerTypes\" />
	  <Folder Include="ViewComponents\" />
	  <Folder Include="wwwroot\mapService\img\" />
	  <Folder Include="wwwroot\tiles\" />
	</ItemGroup>

  <ItemGroup>
    <None Update="SqlServerTypes\**\*.*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
