/**
 * GoTo XY Component Styles
 * استایل‌های کامپوننت رفتن به مختصات
 */

/* ========================================
   GoTo XY Toolbar
======================================== */
.goto-xy-toolbar {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.goto-xy-toolbar .btn-group {
    display: flex;
    gap: 2px;
}

.goto-xy-toolbar .btn {
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 4px;
    transition: all 0.2s ease;
    border: 1px solid #dee2e6;
    background: #fff;
    color: #495057;
}

.goto-xy-toolbar .btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.goto-xy-toolbar .btn.btn-primary {
    background: #007bff;
    border-color: #007bff;
    color: #fff;
}

/* ========================================
   GoTo XY Dialog
======================================== */
.goto-xy-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1050;
    display: none;
    width: 500px;
    max-width: 90vw;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid #dee2e6;
    animation: fadeInScale 0.3s ease-out;
}

.goto-xy-dialog .dialog-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.goto-xy-dialog .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.goto-xy-dialog .dialog-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.goto-xy-dialog .dialog-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
    max-height: 70vh;
}

.goto-xy-dialog .dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* ========================================
   GoTo Controls
======================================== */
.goto-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.coordinate-inputs .row {
    margin: 0;
}

.coordinate-inputs .col-md-6 {
    padding: 0 5px;
}

.coordinate-inputs .col-md-6:first-child {
    padding-left: 0;
}

.coordinate-inputs .col-md-6:last-child {
    padding-right: 0;
}

.quick-locations {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
}

.quick-locations h6 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

.quick-locations .btn-group-vertical {
    gap: 5px;
}

.quick-locations .btn {
    text-align: right;
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 4px;
}

/* ========================================
   Zoom Level Control
======================================== */
.form-range {
    width: 100%;
    height: 6px;
    background: #dee2e6;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.form-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.form-range::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* ========================================
   Bookmark Controls
======================================== */
.bookmark-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.bookmark-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 10px;
}

.bookmark-item {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 10px;
    background: #f8f9fa;
    transition: all 0.2s ease;
}

.bookmark-item:last-child {
    margin-bottom: 0;
}

.bookmark-item:hover {
    border-color: #007bff;
    background: #f0f8ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.bookmark-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.bookmark-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.bookmark-actions {
    display: flex;
    gap: 5px;
}

.bookmark-actions .btn {
    padding: 4px 8px;
    font-size: 11px;
    line-height: 1;
}

.bookmark-details {
    font-size: 11px;
    color: #6c757d;
    line-height: 1.4;
}

.no-bookmarks {
    padding: 40px 20px;
}

.no-bookmarks i {
    color: #dee2e6;
}

.no-bookmarks p {
    margin: 0;
    font-size: 13px;
}

/* ========================================
   Location Marker Popup
======================================== */
.location-marker-popup {
    font-size: 12px;
    line-height: 1.4;
    min-width: 150px;
}

.location-marker-popup strong {
    display: block;
    margin-bottom: 5px;
    color: #333;
}

/* ========================================
   Form Controls
======================================== */
.goto-xy-dialog .form-label {
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 5px;
}

.goto-xy-dialog .form-control,
.goto-xy-dialog .form-select {
    font-size: 13px;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.goto-xy-dialog .form-control:focus,
.goto-xy-dialog .form-select:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.goto-xy-dialog .form-check {
    display: flex;
    align-items: center;
    gap: 8px;
}

.goto-xy-dialog .form-check-input {
    margin: 0;
}

.goto-xy-dialog .form-check-label {
    font-size: 13px;
    color: #495057;
    margin: 0;
}

/* ========================================
   Responsive Design
======================================== */
@media (max-width: 768px) {
    .goto-xy-dialog {
        width: 95vw;
        max-height: 90vh;
    }
    
    .goto-xy-toolbar {
        flex-wrap: wrap;
        gap: 3px;
    }
    
    .goto-xy-toolbar .btn {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .coordinate-inputs .row {
        flex-direction: column;
    }
    
    .coordinate-inputs .col-md-6 {
        padding: 0;
        margin-bottom: 10px;
    }
    
    .coordinate-inputs .col-md-6:last-child {
        margin-bottom: 0;
    }
    
    .quick-locations .btn-group-vertical {
        flex-direction: column;
    }
    
    .bookmark-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .bookmark-actions {
        align-self: flex-end;
    }
}

/* ========================================
   Animation Effects
======================================== */
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.goto-xy-toolbar {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.bookmark-item {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ========================================
   Dark Theme Support
======================================== */
@media (prefers-color-scheme: dark) {
    .goto-xy-toolbar {
        background: rgba(33, 37, 41, 0.9);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .goto-xy-dialog {
        background: #212529;
        border-color: #495057;
        color: #fff;
    }
    
    .goto-xy-dialog .dialog-header,
    .goto-xy-dialog .dialog-footer {
        background: #343a40;
        border-color: #495057;
    }
    
    .goto-xy-dialog .dialog-header h5 {
        color: #fff;
    }
    
    .goto-xy-dialog .form-control,
    .goto-xy-dialog .form-select {
        background: #495057;
        border-color: #6c757d;
        color: #fff;
    }
    
    .goto-xy-dialog .form-control:focus,
    .goto-xy-dialog .form-select:focus {
        background: #495057;
        border-color: #007bff;
        color: #fff;
    }
    
    .bookmark-item {
        background: #343a40;
        border-color: #495057;
        color: #fff;
    }
    
    .bookmark-item:hover {
        background: #495057;
        border-color: #007bff;
    }
    
    .bookmark-name {
        color: #fff;
    }
    
    .form-range {
        background: #495057;
    }
}
