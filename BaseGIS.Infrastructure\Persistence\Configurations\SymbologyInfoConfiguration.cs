using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BaseGIS.Core.Entities;

namespace BaseGIS.Infrastructure.Persistence.Configurations
{
    public class SymbologyInfoConfiguration : IEntityTypeConfiguration<SymbologyInfo>
    {
        public void Configure(EntityTypeBuilder<SymbologyInfo> builder)
        {
            builder.HasKey(s => s.Id);
            builder.Property(s => s.Type).IsRequired();
            builder.Property(s => s.Name).HasMaxLength(100);
            builder.Property(s => s.Json).IsRequired();
            builder.HasOne(s => s.TableInfo)
                   .WithMany(t => t.Symbologies)
                   .HasForeignKey(s => s.TableInfoId);
        }
    }
} 