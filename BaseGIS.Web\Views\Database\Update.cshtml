﻿@{
    var id = Context.Request.Query["id"];
}

<!-- MAIN CONTENT -->
<div id="content" class="samanFont">
    <!-- row -->
    <div class="row">
        <div class="col-12 col-sm-7 col-md-7 col-lg-4">
            <h3 class="page-title txt-color-blueDark samanFont">
                <i class="fa-fw fa fa-puzzle-piece"></i>
                بروزرسانی
            </h3>
        </div>
    </div>
    <div class="row">
        <div class="col-12 col-lg-4">
            @await Html.PartialAsync("_Update_List")
        </div>
        <div class="col-12 col-lg-8">
            @if (!string.IsNullOrEmpty(id))
            {
                @await Html.PartialAsync("_Update_Wizard")
            }
        </div>
    </div>
</div>

@section scripts {
    <script src="~/Scripts/plugin/dropzone/min/dropzone.min.js"></script>
    <script src="~/Scripts/plugin/bootstrap-wizard/jquery.bootstrap.wizard.min.js"></script>
    <script src="~/Scripts/plugin/fuelux/wizard/wizard.min.js"></script>
    <script type="text/javascript">
        Dropzone.autoDiscover = false;
    </script>
}