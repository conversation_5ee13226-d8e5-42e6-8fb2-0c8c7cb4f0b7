﻿using System.ComponentModel.DataAnnotations;

namespace BaseGIS.Core.Entities
{
    public class RelationInfo
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "نام حتما باید وارد شود")]
        [StringLength(50, ErrorMessage = "نام حداکثر می بایست 50 کارکتر باشد")]
        public string Name { get; set; }

        [Required(ErrorMessage = "نام فارسی حتما باید وارد شود")]
        [StringLength(100, ErrorMessage = "نام فارسی حداکثر می بایست 100 کارکتر باشد")]
        public string AliasName { get; set; }
        public int MainTableId { get; set; }
        public virtual TableInfo MainTable { get; set; }
        public int RelatedTableId { get; set; }
        public virtual TableInfo RelatedTable { get; set; }

        public string RelationType { get; set; }

    }
}
