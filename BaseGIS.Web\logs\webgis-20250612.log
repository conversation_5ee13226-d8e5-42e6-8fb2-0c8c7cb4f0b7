2025-06-12 10:08:47.378 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-12 10:08:47.478 +03:30 [ERR] An error occurred while seeding the database
System.InvalidOperationException: No service for type 'Microsoft.AspNetCore.Identity.RoleManager`1[Microsoft.AspNetCore.Identity.IdentityRole]' has been registered.
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<Main>$(String[] args) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\Program.cs:line 164
2025-06-12 10:08:48.866 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-12 10:08:48.868 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-12 10:08:48.954 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-12 10:08:48.955 +03:30 [INF] Hosting environment: Development
2025-06-12 10:08:48.956 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-12 10:08:48.960 +03:30 [INF] The application has started
2025-06-12 10:08:58.731 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-12 10:09:20.141 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-12 10:09:20.252 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-12 10:09:20.380 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:09:20.388 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.9009ms.
2025-06-12 10:09:20.425 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-12 10:09:20.668 +03:30 [INF] Executed ViewResult - view Index executed in 266.1648ms.
2025-06-12 10:09:20.675 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 417.9012ms
2025-06-12 10:09:20.678 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-12 10:09:20.685 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 21966.3668ms
2025-06-12 10:09:20.777 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - null null
2025-06-12 10:09:20.777 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - null null
2025-06-12 10:09:20.813 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-12 10:09:20.778 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-12 10:09:20.870 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - null null
2025-06-12 10:09:20.852 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-12 10:09:20.863 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/avatars/male.png - null null
2025-06-12 10:09:21.408 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:09:21.407 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/bootstrap-icons.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\bootstrap-icons.min.css'
2025-06-12 10:09:21.407 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-12 10:09:21.407 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-12 10:09:21.539 +03:30 [INF] Sending file. Request path: '/js/site.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\site.js'
2025-06-12 10:09:21.537 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-12 10:09:20.873 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - null null
2025-06-12 10:09:21.658 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-12 10:09:21.665 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:09:21.663 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - 499 85875 text/css 886.2733ms
2025-06-12 10:09:21.666 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 499 4983 image/jpeg 853.1375ms
2025-06-12 10:09:21.846 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - null null
2025-06-12 10:09:21.668 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 499 2750 image/jpeg 890.324ms
2025-06-12 10:09:21.671 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - 200 3997 text/javascript 807.8173ms
2025-06-12 10:09:21.673 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 499 3398 image/jpeg 820.8756ms
2025-06-12 10:09:21.837 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/avatars/male.png - 499 268 image/png 973.5766ms
2025-06-12 10:09:21.835 +03:30 [INF] Sending file. Request path: '/assets/img/AdminLTELogo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\AdminLTELogo.png'
2025-06-12 10:09:21.851 +03:30 [INF] Sending file. Request path: '/css/site.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site.css'
2025-06-12 10:09:21.854 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 447.8293ms
2025-06-12 10:09:21.856 +03:30 [INF] The file /lib/popper.js/dist/umd/popper.min.js was not modified
2025-06-12 10:09:21.877 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - 499 2637 image/png 1004.6307ms
2025-06-12 10:09:21.879 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - 200 7707 text/css 1102.1207ms
2025-06-12 10:09:21.887 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - 304 null text/javascript 40.4606ms
2025-06-12 10:09:21.907 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-12 10:09:22.060 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-12 10:09:22.124 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-12 10:09:22.276 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-12 10:09:22.186 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 480.436ms
2025-06-12 10:09:22.310 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 499 45276 application/font-woff 402.872ms
2025-06-12 10:09:22.309 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-12 10:09:22.451 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-12 10:09:22.461 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - 499 130396 font/woff2 451.3312ms
2025-06-12 10:09:22.462 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 499 5430 image/x-icon 200.9041ms
2025-06-12 10:10:00.962 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:10:01.023 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:10:01.068 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:10:01.105 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:10:01.350 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 241.2685ms.
2025-06-12 10:10:01.453 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:10:01.478 +03:30 [INF] Executed ViewResult - view Insert executed in 123.2592ms.
2025-06-12 10:10:01.481 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 408.4431ms
2025-06-12 10:10:01.484 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:10:01.486 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 523.9131ms
2025-06-12 10:10:01.560 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - null null
2025-06-12 10:10:01.564 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:10:01.565 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:10:01.569 +03:30 [INF] The file /js/site.js was not modified
2025-06-12 10:10:01.586 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 21.1675ms
2025-06-12 10:10:01.602 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - 304 null text/javascript 41.4023ms
2025-06-12 10:10:01.604 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 40.3142ms
2025-06-12 10:10:02.181 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749710402069 - null null
2025-06-12 10:10:02.197 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:10:02.201 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:10:02.234 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:10:04.162 +03:30 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:10:04.332 +03:30 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:10:04.580 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 2343.7478ms.
2025-06-12 10:10:04.585 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:10:04.776 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 191.8416ms.
2025-06-12 10:10:04.780 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 2575.0361ms
2025-06-12 10:10:04.783 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:10:04.794 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749710402069 - 200 null text/html; charset=utf-8 2613.4417ms
2025-06-12 10:10:09.940 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749710402070 - null null
2025-06-12 10:10:09.944 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:10:09.958 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:10:10.020 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:10:11.979 +03:30 [INF] Executed DbCommand (28ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:10:12.114 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 2090.6141ms.
2025-06-12 10:10:12.120 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:10:15.909 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 3789.5855ms.
2025-06-12 10:10:15.911 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 5949.3058ms
2025-06-12 10:10:15.914 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:10:15.916 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749710402070 - 200 null text/html; charset=utf-8 5976.6879ms
2025-06-12 10:10:23.153 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Database/_FileUpload - multipart/form-data; boundary=----geckoformboundarya6098a76f3fe23fbd8634f50f5fd59ef 2488595
2025-06-12 10:10:23.161 +03:30 [INF] CORS policy execution successful.
2025-06-12 10:10:23.168 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:10:23.176 +03:30 [INF] Route matched with {action = "_FileUpload", controller = "Database"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] _FileUpload(System.String, Microsoft.AspNetCore.Http.IFormFile) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:10:23.332 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:10:23.721 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:10:23.727 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 392.904ms.
2025-06-12 10:10:23.734 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType2`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-12 10:10:23.749 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) in 568.7677ms
2025-06-12 10:10:23.753 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:10:23.756 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Database/_FileUpload - 200 null application/json; charset=utf-8 603.5136ms
2025-06-12 10:10:23.761 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=32733093 - null null
2025-06-12 10:10:23.767 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:10:23.768 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:10:23.791 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:10:26.161 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:10:26.186 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 2393.0466ms.
2025-06-12 10:10:26.189 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:10:26.199 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 10.0257ms.
2025-06-12 10:10:26.201 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 2429.2419ms
2025-06-12 10:10:26.203 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:10:26.205 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=32733093 - 200 null text/html; charset=utf-8 2444.9165ms
2025-06-12 10:11:08.136 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Database/PublishData - application/x-www-form-urlencoded; charset=UTF-8 280
2025-06-12 10:11:08.143 +03:30 [INF] CORS policy execution successful.
2025-06-12 10:11:08.145 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web)'
2025-06-12 10:11:08.149 +03:30 [INF] Route matched with {action = "PublishData", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult PublishData(System.String, System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:11:08.192 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:11:21.292 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:12:14.886 +03:30 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-12 10:12:14.925 +03:30 [INF] Executed DbCommand (10ms) [Parameters=[@__id_0='?' (Size = 11) (DbType = AnsiString)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[AliasName], [s].[CalcPeriod], [s].[DisableRule], [s].[Editable], [s].[FieldIndex], [s].[FieldLength], [s].[FieldType], [s].[IsDisplay], [s].[IsRequired], [s].[IsUnique], [s].[Name], [s].[SQLCalc], [s].[ShpFieldName], [s].[TableInfoId], [s].[UnitName], [s].[Updated], [s].[ValidationRule], [s].[WebService_Period], [s].[WebService_URL], [s].[Id0], [s].[Code], [s].[Domain_ID], [s].[FieldInfoId], [s].[IsMulti], [s].[Name0]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE CONVERT(varchar(11), [t].[Id]) = @__id_0
) AS [t0]
LEFT JOIN (
    SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [d].[Id] AS [Id0], [d].[Code], [d].[Domain_ID], [d].[FieldInfoId], [d].[IsMulti], [d].[Name] AS [Name0]
    FROM [FieldInfos] AS [f]
    LEFT JOIN [DomainInfos] AS [d] ON [f].[Id] = [d].[FieldInfoId]
) AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id], [s].[Id]
2025-06-12 10:12:44.847 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 96651.3802ms.
2025-06-12 10:12:44.852 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType2`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-12 10:12:44.854 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web) in 96702.6746ms
2025-06-12 10:12:44.856 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web)'
2025-06-12 10:12:44.858 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Database/PublishData - 200 null application/json; charset=utf-8 96721.9669ms
2025-06-12 10:13:42.619 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-12 10:13:42.619 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-12 10:13:42.952 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-12 10:13:43.123 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-12 10:13:43.124 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 504.927ms
2025-06-12 10:13:43.126 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 506.491ms
2025-06-12 10:15:15.780 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:15:36.888 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:15:36.892 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:15:36.923 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:15:36.928 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0253ms.
2025-06-12 10:15:36.934 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:15:36.940 +03:30 [INF] Executed ViewResult - view Insert executed in 7.5915ms.
2025-06-12 10:15:36.943 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 48.0148ms
2025-06-12 10:15:36.946 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:15:36.948 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 21167.2772ms
2025-06-12 10:15:37.085 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - null null
2025-06-12 10:15:37.086 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:15:37.086 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:15:37.094 +03:30 [INF] The file /js/site.js was not modified
2025-06-12 10:15:37.123 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 36.6048ms
2025-06-12 10:15:37.134 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 48.1931ms
2025-06-12 10:15:37.135 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - 304 null text/javascript 49.6775ms
2025-06-12 10:15:37.479 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749710737394 - null null
2025-06-12 10:15:37.493 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:15:37.497 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:15:37.529 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:15:37.534 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:15:37.540 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:15:37.546 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 14.2842ms.
2025-06-12 10:15:37.549 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:15:37.552 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 3.1478ms.
2025-06-12 10:15:37.553 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 53.6553ms
2025-06-12 10:15:37.556 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:15:37.558 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749710737394 - 200 null text/html; charset=utf-8 79.7867ms
2025-06-12 10:15:42.191 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749710737395 - null null
2025-06-12 10:15:42.197 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:15:42.200 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:15:42.225 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:15:42.230 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:15:42.237 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 9.3971ms.
2025-06-12 10:15:42.240 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:15:42.265 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 26.4605ms.
2025-06-12 10:15:42.268 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 65.6455ms
2025-06-12 10:15:42.270 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:15:42.271 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749710737395 - 200 null text/html; charset=utf-8 79.4587ms
2025-06-12 10:15:50.261 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Database/_FileUpload - multipart/form-data; boundary=----geckoformboundary4772afd3959aba479c23131e2d03d92f 2488595
2025-06-12 10:15:50.268 +03:30 [INF] CORS policy execution successful.
2025-06-12 10:15:50.270 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:15:50.277 +03:30 [INF] Route matched with {action = "_FileUpload", controller = "Database"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] _FileUpload(System.String, Microsoft.AspNetCore.Http.IFormFile) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:15:50.350 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:15:50.592 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:15:50.596 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 244.7704ms.
2025-06-12 10:15:50.599 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType2`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-12 10:15:50.602 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) in 320.2344ms
2025-06-12 10:15:50.604 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:15:50.606 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Database/_FileUpload - 200 null application/json; charset=utf-8 345.2309ms
2025-06-12 10:15:50.614 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=69727348 - null null
2025-06-12 10:15:50.617 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:15:50.619 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:15:50.647 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:15:50.650 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:15:50.657 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 8.3205ms.
2025-06-12 10:15:50.660 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:15:50.662 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 2.1898ms.
2025-06-12 10:15:50.664 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 42.3962ms
2025-06-12 10:15:50.665 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:15:50.666 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=69727348 - 200 null text/html; charset=utf-8 52.1613ms
2025-06-12 10:16:04.784 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Database/PublishData - application/x-www-form-urlencoded; charset=UTF-8 280
2025-06-12 10:16:04.788 +03:30 [INF] CORS policy execution successful.
2025-06-12 10:16:04.789 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web)'
2025-06-12 10:16:04.793 +03:30 [INF] Route matched with {action = "PublishData", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult PublishData(System.String, System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:16:04.819 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:16:24.356 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:16:24.423 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__id_0='?' (Size = 11) (DbType = AnsiString)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[AliasName], [s].[CalcPeriod], [s].[DisableRule], [s].[Editable], [s].[FieldIndex], [s].[FieldLength], [s].[FieldType], [s].[IsDisplay], [s].[IsRequired], [s].[IsUnique], [s].[Name], [s].[SQLCalc], [s].[ShpFieldName], [s].[TableInfoId], [s].[UnitName], [s].[Updated], [s].[ValidationRule], [s].[WebService_Period], [s].[WebService_URL], [s].[Id0], [s].[Code], [s].[Domain_ID], [s].[FieldInfoId], [s].[IsMulti], [s].[Name0]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE CONVERT(varchar(11), [t].[Id]) = @__id_0
) AS [t0]
LEFT JOIN (
    SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [d].[Id] AS [Id0], [d].[Code], [d].[Domain_ID], [d].[FieldInfoId], [d].[IsMulti], [d].[Name] AS [Name0]
    FROM [FieldInfos] AS [f]
    LEFT JOIN [DomainInfos] AS [d] ON [f].[Id] = [d].[FieldInfoId]
) AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id], [s].[Id]
2025-06-12 10:16:35.052 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 30230.3526ms.
2025-06-12 10:16:35.067 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType2`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-12 10:16:35.069 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web) in 30272.495ms
2025-06-12 10:16:35.071 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web)'
2025-06-12 10:16:35.072 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Database/PublishData - 200 null application/json; charset=utf-8 30288.1068ms
2025-06-12 10:16:37.363 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Database/PublishData - application/x-www-form-urlencoded; charset=UTF-8 280
2025-06-12 10:16:37.369 +03:30 [INF] CORS policy execution successful.
2025-06-12 10:16:37.371 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web)'
2025-06-12 10:16:37.373 +03:30 [INF] Route matched with {action = "PublishData", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult PublishData(System.String, System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:16:37.398 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:16:37.404 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:16:37.441 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__id_0='?' (Size = 11) (DbType = AnsiString)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[AliasName], [s].[CalcPeriod], [s].[DisableRule], [s].[Editable], [s].[FieldIndex], [s].[FieldLength], [s].[FieldType], [s].[IsDisplay], [s].[IsRequired], [s].[IsUnique], [s].[Name], [s].[SQLCalc], [s].[ShpFieldName], [s].[TableInfoId], [s].[UnitName], [s].[Updated], [s].[ValidationRule], [s].[WebService_Period], [s].[WebService_URL], [s].[Id0], [s].[Code], [s].[Domain_ID], [s].[FieldInfoId], [s].[IsMulti], [s].[Name0]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE CONVERT(varchar(11), [t].[Id]) = @__id_0
) AS [t0]
LEFT JOIN (
    SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [d].[Id] AS [Id0], [d].[Code], [d].[Domain_ID], [d].[FieldInfoId], [d].[IsMulti], [d].[Name] AS [Name0]
    FROM [FieldInfos] AS [f]
    LEFT JOIN [DomainInfos] AS [d] ON [f].[Id] = [d].[FieldInfoId]
) AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id], [s].[Id]
2025-06-12 10:17:42.528 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 65126.1302ms.
2025-06-12 10:17:42.566 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType2`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-12 10:17:42.568 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web) in 65193.7521ms
2025-06-12 10:17:42.571 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web)'
2025-06-12 10:17:42.573 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Database/PublishData - 200 null application/json; charset=utf-8 65209.7125ms
2025-06-12 10:17:52.468 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:18:13.568 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:18:13.571 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:18:13.601 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:18:13.604 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0214ms.
2025-06-12 10:18:13.606 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:18:13.609 +03:30 [INF] Executed ViewResult - view Insert executed in 2.6293ms.
2025-06-12 10:18:13.611 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 37.41ms
2025-06-12 10:18:13.613 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:18:13.616 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 21147.6851ms
2025-06-12 10:18:13.669 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - null null
2025-06-12 10:18:13.672 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:18:13.673 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:18:13.675 +03:30 [INF] The file /js/site.js was not modified
2025-06-12 10:18:13.691 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 17.5148ms
2025-06-12 10:18:13.692 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - 304 null text/javascript 23.3292ms
2025-06-12 10:18:13.808 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 135.3534ms
2025-06-12 10:18:15.671 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749710894007 - null null
2025-06-12 10:18:15.675 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:18:15.677 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:18:15.703 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:18:15.709 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:18:15.714 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:18:15.716 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 9.0295ms.
2025-06-12 10:18:15.718 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:18:15.720 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 1.4637ms.
2025-06-12 10:18:15.722 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 40.2512ms
2025-06-12 10:18:15.723 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:18:15.724 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749710894007 - 200 null text/html; charset=utf-8 53.3657ms
2025-06-12 10:18:27.277 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749710894008 - null null
2025-06-12 10:18:27.280 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:18:27.281 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:18:27.306 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:18:28.911 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:18:28.918 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 1608.9763ms.
2025-06-12 10:18:28.921 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:18:28.923 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 2.2704ms.
2025-06-12 10:18:28.926 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 1641.8016ms
2025-06-12 10:18:28.927 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:18:28.929 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749710894008 - 200 null text/html; charset=utf-8 1652.7002ms
2025-06-12 10:18:37.956 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Database/_FileUpload - multipart/form-data; boundary=----geckoformboundary5213ba5d4ebd1bfc8a2c81a06a00f1c5 2488595
2025-06-12 10:18:37.962 +03:30 [INF] CORS policy execution successful.
2025-06-12 10:18:37.963 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:18:37.965 +03:30 [INF] Route matched with {action = "_FileUpload", controller = "Database"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] _FileUpload(System.String, Microsoft.AspNetCore.Http.IFormFile) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:18:38.043 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:18:38.347 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:18:38.351 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 306.5855ms.
2025-06-12 10:18:38.354 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType2`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-12 10:18:38.356 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) in 388.4139ms
2025-06-12 10:18:38.357 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:18:38.359 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Database/_FileUpload - 200 null application/json; charset=utf-8 403.7397ms
2025-06-12 10:18:38.366 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=******** - null null
2025-06-12 10:18:38.371 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:18:38.372 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:18:38.402 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:18:45.561 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:20:05.218 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 86814.1601ms.
2025-06-12 10:20:05.221 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:20:06.420 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 88045.842ms
2025-06-12 10:20:06.546 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:20:06.546 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:20:06.710 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard#1.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard+<ExecuteAsync>d__16..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard#1.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.PartialViewResultExecutor.ExecuteAsync(ActionContext context, PartialViewResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-12 10:20:06.735 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=******** - 500 null text/plain; charset=utf-8 88369.1788ms
2025-06-12 10:20:27.678 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:20:27.683 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:20:27.736 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:20:27.739 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0405ms.
2025-06-12 10:20:27.742 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:20:27.749 +03:30 [INF] Executed ViewResult - view Insert executed in 8.3252ms.
2025-06-12 10:20:27.773 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 86.9526ms
2025-06-12 10:20:27.776 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:20:27.778 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 21232.5399ms
2025-06-12 10:20:27.816 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - null null
2025-06-12 10:20:27.821 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:20:27.822 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:20:27.822 +03:30 [INF] The file /js/site.js was not modified
2025-06-12 10:20:27.832 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 9.9849ms
2025-06-12 10:20:27.879 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - 304 null text/javascript 62.6992ms
2025-06-12 10:20:27.883 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 61.8651ms
2025-06-12 10:20:29.315 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711028277 - null null
2025-06-12 10:20:29.326 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:20:29.330 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:20:29.379 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:20:29.384 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:20:29.388 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:20:29.392 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 9.8417ms.
2025-06-12 10:20:29.394 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:20:29.397 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 3.4356ms.
2025-06-12 10:20:29.399 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 65.6083ms
2025-06-12 10:20:29.403 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:20:29.404 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711028277 - 200 null text/html; charset=utf-8 89.4262ms
2025-06-12 10:20:38.993 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-12 10:20:38.993 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-12 10:20:38.998 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-12 10:20:39.002 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-12 10:20:39.003 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 10.0678ms
2025-06-12 10:20:39.004 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 11.4763ms
2025-06-12 10:20:43.815 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:20:43.871 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:20:43.874 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:20:43.927 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:20:43.930 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0222ms.
2025-06-12 10:20:43.932 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:20:43.934 +03:30 [INF] Executed ViewResult - view Insert executed in 2.2972ms.
2025-06-12 10:20:43.937 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 59.1734ms
2025-06-12 10:20:43.938 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:20:43.939 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 185.232ms
2025-06-12 10:20:44.045 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-12 10:20:44.047 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-12 10:20:44.049 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - null null
2025-06-12 10:20:44.054 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-12 10:20:44.329 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - null null
2025-06-12 10:20:44.273 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-12 10:20:44.329 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - 200 232911 text/css 283.9702ms
2025-06-12 10:20:44.093 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - null null
2025-06-12 10:20:44.053 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - null null
2025-06-12 10:20:44.092 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - null null
2025-06-12 10:20:44.051 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - null null
2025-06-12 10:20:44.582 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:20:44.093 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - null null
2025-06-12 10:20:44.625 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-12 10:20:44.669 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 87.7602ms
2025-06-12 10:20:44.642 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/styles/overlayscrollbars.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\styles\overlayscrollbars.min.css'
2025-06-12 10:20:44.677 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - 200 13202 text/css 584.8057ms
2025-06-12 10:20:44.152 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - null null
2025-06-12 10:20:44.152 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - null null
2025-06-12 10:20:44.237 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - null null
2025-06-12 10:20:44.237 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-12 10:20:44.280 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/avatars/male.png - null null
2025-06-12 10:20:44.281 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-12 10:20:44.546 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - null null
2025-06-12 10:20:44.329 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery/jquery.min.js - null null
2025-06-12 10:20:44.377 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery-validation/dist/jquery.validate.min.js - null null
2025-06-12 10:20:44.378 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js - null null
2025-06-12 10:20:44.422 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - null null
2025-06-12 10:20:44.421 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - null null
2025-06-12 10:20:44.502 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - null null
2025-06-12 10:20:44.501 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.js - null null
2025-06-12 10:20:44.624 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-12 10:20:44.546 +03:30 [INF] Sending file. Request path: '/assets/img/AdminLTELogo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\AdminLTELogo.png'
2025-06-12 10:20:44.563 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js - null null
2025-06-12 10:20:44.581 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - null null
2025-06-12 10:20:44.602 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:20:44.624 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-12 10:20:44.667 +03:30 [INF] Sending file. Request path: '/lib/source-sans-3/index.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\source-sans-3\index.css'
2025-06-12 10:20:44.761 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - 200 2520 text/css 668.2747ms
2025-06-12 10:20:44.669 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - 200 106394 text/css 622.4045ms
2025-06-12 10:20:44.645 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.css'
2025-06-12 10:20:44.682 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\browser\overlayscrollbars.browser.es6.min.js'
2025-06-12 10:20:44.692 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/bootstrap-icons.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\bootstrap-icons.min.css'
2025-06-12 10:20:44.692 +03:30 [INF] Sending file. Request path: '/css/adminlte.rtl.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\adminlte.rtl.css'
2025-06-12 10:20:44.694 +03:30 [INF] Sending file. Request path: '/css/site.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site.css'
2025-06-12 10:20:44.697 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-12 10:20:44.701 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-12 10:20:44.704 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-12 10:20:44.723 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.js'
2025-06-12 10:20:44.791 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - 200 147552 text/javascript 369.2448ms
2025-06-12 10:20:44.735 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 200 4983 image/jpeg 462.3267ms
2025-06-12 10:20:44.739 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - 200 2637 image/png 410.0638ms
2025-06-12 10:20:44.745 +03:30 [INF] Sending file. Request path: '/js/adminlte.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\adminlte.js'
2025-06-12 10:20:44.748 +03:30 [INF] Sending file. Request path: '/lib/popper.js/dist/umd/popper.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\popper.js\dist\umd\popper.min.js'
2025-06-12 10:20:44.750 +03:30 [INF] Sending file. Request path: '/js/site.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\site.js'
2025-06-12 10:20:44.811 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - 200 3997 text/javascript 229.9907ms
2025-06-12 10:20:44.755 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - 200 14806 text/css 706.0116ms
2025-06-12 10:20:44.758 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation/dist/jquery.validate.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js'
2025-06-12 10:20:44.639 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.css'
2025-06-12 10:20:44.765 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js'
2025-06-12 10:20:44.771 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - 200 5267 text/css 720.0465ms
2025-06-12 10:20:44.773 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - 200 29334 text/javascript 680.4248ms
2025-06-12 10:20:44.776 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - 200 85875 text/css 623.8699ms
2025-06-12 10:20:44.778 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.js'
2025-06-12 10:20:44.779 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - 200 358126 text/css 627.2054ms
2025-06-12 10:20:44.781 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/js/bootstrap.bundle.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\js\bootstrap.bundle.min.js'
2025-06-12 10:20:44.781 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - 200 7707 text/css 544.4076ms
2025-06-12 10:20:44.784 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 200 2750 image/jpeg 546.4106ms
2025-06-12 10:20:44.786 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/avatars/male.png - 200 268 image/png 505.5949ms
2025-06-12 10:20:44.789 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 200 3398 image/jpeg 507.8807ms
2025-06-12 10:20:44.730 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.js'
2025-06-12 10:20:44.797 +03:30 [INF] Sending file. Request path: '/lib/jquery/jquery.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery\jquery.min.js'
2025-06-12 10:20:44.806 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js - 200 29455 text/javascript 243.2963ms
2025-06-12 10:20:44.808 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - 200 20122 text/javascript 262.8079ms
2025-06-12 10:20:44.753 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 151.2293ms
2025-06-12 10:20:44.820 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery-validation/dist/jquery.validate.min.js - 200 25308 text/javascript 442.9307ms
2025-06-12 10:20:44.822 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - 200 23932 text/css 769.2933ms
2025-06-12 10:20:44.825 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - 200 5824 text/javascript 403.7457ms
2025-06-12 10:20:44.838 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - 200 46668 text/javascript 336.5614ms
2025-06-12 10:20:44.844 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js - 200 80721 text/javascript 466.0786ms
2025-06-12 10:20:44.862 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.js - 200 67484 text/javascript 360.3684ms
2025-06-12 10:20:44.864 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery/jquery.min.js - 200 87533 text/javascript 535.5005ms
2025-06-12 10:20:44.877 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-12 10:20:44.881 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-12 10:20:44.885 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - null null
2025-06-12 10:20:44.909 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-12 10:20:44.914 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-12 10:20:44.918 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/webfonts/fa-solid-900.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\webfonts\fa-solid-900.woff2'
2025-06-12 10:20:44.918 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 200 45276 application/font-woff 41.3239ms
2025-06-12 10:20:44.921 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - 200 130396 font/woff2 40.1299ms
2025-06-12 10:20:44.923 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - 200 158220 font/woff2 37.8486ms
2025-06-12 10:20:44.955 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-12 10:20:44.958 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-12 10:20:44.960 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 200 5430 image/x-icon 5.4968ms
2025-06-12 10:20:44.969 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-12 10:20:44.971 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-12 10:20:44.973 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-12 10:20:44.978 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-12 10:20:44.978 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 200 45276 application/font-woff 8.4993ms
2025-06-12 10:20:44.980 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - 200 130396 font/woff2 9.8633ms
2025-06-12 10:20:45.033 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711044910 - null null
2025-06-12 10:20:45.036 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:20:45.038 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:20:45.079 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:20:45.083 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:20:45.088 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:20:45.091 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 9.7913ms.
2025-06-12 10:20:45.094 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:20:45.096 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 2.3562ms.
2025-06-12 10:20:45.098 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 57.3012ms
2025-06-12 10:20:45.100 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:20:45.101 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711044910 - 200 null text/html; charset=utf-8 68.1013ms
2025-06-12 10:21:38.708 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:21:38.826 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:21:38.830 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:21:38.887 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:21:38.890 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0389ms.
2025-06-12 10:21:38.892 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:21:38.894 +03:30 [INF] Executed ViewResult - view Insert executed in 2.2519ms.
2025-06-12 10:21:38.896 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 64.043ms
2025-06-12 10:21:38.898 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:21:38.900 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 191.6597ms
2025-06-12 10:21:38.993 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-12 10:21:38.994 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-12 10:21:39.168 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - null null
2025-06-12 10:21:39.215 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-12 10:21:39.303 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery-validation/dist/jquery.validate.min.js - null null
2025-06-12 10:21:39.375 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - null null
2025-06-12 10:21:39.466 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/styles/overlayscrollbars.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\styles\overlayscrollbars.min.css'
2025-06-12 10:21:39.065 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - null null
2025-06-12 10:21:38.997 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - null null
2025-06-12 10:21:39.174 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - null null
2025-06-12 10:21:39.168 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - null null
2025-06-12 10:21:39.168 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - null null
2025-06-12 10:21:39.168 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - null null
2025-06-12 10:21:39.174 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - null null
2025-06-12 10:21:39.196 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - null null
2025-06-12 10:21:39.215 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-12 10:21:39.233 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-12 10:21:39.251 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/avatars/male.png - null null
2025-06-12 10:21:39.270 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - null null
2025-06-12 10:21:39.288 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery/jquery.min.js - null null
2025-06-12 10:21:39.303 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js - null null
2025-06-12 10:21:39.320 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - null null
2025-06-12 10:21:39.338 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - null null
2025-06-12 10:21:39.357 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.js - null null
2025-06-12 10:21:39.375 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - null null
2025-06-12 10:21:39.393 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js - null null
2025-06-12 10:21:39.411 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - null null
2025-06-12 10:21:39.430 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:21:39.447 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:21:39.527 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - 200 13202 text/css 359.2431ms
2025-06-12 10:21:39.525 +03:30 [INF] Sending file. Request path: '/lib/popper.js/dist/umd/popper.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\popper.js\dist\umd\popper.min.js'
2025-06-12 10:21:39.531 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.css'
2025-06-12 10:21:39.534 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-12 10:21:39.542 +03:30 [INF] Sending file. Request path: '/css/adminlte.rtl.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\adminlte.rtl.css'
2025-06-12 10:21:39.541 +03:30 [INF] Sending file. Request path: '/lib/source-sans-3/index.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\source-sans-3\index.css'
2025-06-12 10:21:39.545 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\browser\overlayscrollbars.browser.es6.min.js'
2025-06-12 10:21:39.548 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.css'
2025-06-12 10:21:39.553 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/bootstrap-icons.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\bootstrap-icons.min.css'
2025-06-12 10:21:39.557 +03:30 [INF] Sending file. Request path: '/css/site.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site.css'
2025-06-12 10:21:39.560 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-12 10:21:39.563 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-12 10:21:39.566 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-12 10:21:39.645 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/avatars/male.png - 200 268 image/png 393.5497ms
2025-06-12 10:21:39.576 +03:30 [INF] Sending file. Request path: '/lib/jquery/jquery.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery\jquery.min.js'
2025-06-12 10:21:39.580 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/js/bootstrap.bundle.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\js\bootstrap.bundle.min.js'
2025-06-12 10:21:39.582 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js'
2025-06-12 10:21:39.588 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.js'
2025-06-12 10:21:39.592 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.js'
2025-06-12 10:21:39.594 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.js'
2025-06-12 10:21:39.597 +03:30 [INF] Sending file. Request path: '/js/adminlte.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\adminlte.js'
2025-06-12 10:21:39.668 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js - 200 29455 text/javascript 275.5142ms
2025-06-12 10:21:39.481 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-12 10:21:39.504 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation/dist/jquery.validate.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js'
2025-06-12 10:21:39.527 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-12 10:21:39.526 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-12 10:21:39.607 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 159.7614ms
2025-06-12 10:21:39.610 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 180.5014ms
2025-06-12 10:21:39.613 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - 200 20122 text/javascript 238.2185ms
2025-06-12 10:21:39.615 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - 200 5267 text/css 550.1886ms
2025-06-12 10:21:39.618 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - 200 14806 text/css 621.5209ms
2025-06-12 10:21:39.621 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - 200 358126 text/css 446.6909ms
2025-06-12 10:21:39.623 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - 200 2520 text/css 454.6613ms
2025-06-12 10:21:39.625 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - 200 29334 text/javascript 457.213ms
2025-06-12 10:21:39.628 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - 200 23932 text/css 459.4667ms
2025-06-12 10:21:39.630 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - 200 85875 text/css 455.5797ms
2025-06-12 10:21:39.632 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - 200 7707 text/css 436.1574ms
2025-06-12 10:21:39.636 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 200 2750 image/jpeg 420.4589ms
2025-06-12 10:21:39.638 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 200 3398 image/jpeg 404.6751ms
2025-06-12 10:21:39.569 +03:30 [INF] Sending file. Request path: '/assets/img/AdminLTELogo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\AdminLTELogo.png'
2025-06-12 10:21:39.656 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery/jquery.min.js - 200 87533 text/javascript 368.1528ms
2025-06-12 10:21:39.658 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js - 200 80721 text/javascript 354.5231ms
2025-06-12 10:21:39.660 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - 200 5824 text/javascript 339.6904ms
2025-06-12 10:21:39.663 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - 200 147552 text/javascript 324.2714ms
2025-06-12 10:21:39.664 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.js - 200 67484 text/javascript 307.6642ms
2025-06-12 10:21:39.666 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - 200 46668 text/javascript 291.1329ms
2025-06-12 10:21:39.601 +03:30 [INF] Sending file. Request path: '/js/site.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\site.js'
2025-06-12 10:21:39.679 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 200 4983 image/jpeg 463.6706ms
2025-06-12 10:21:39.681 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery-validation/dist/jquery.validate.min.js - 200 25308 text/javascript 377.5889ms
2025-06-12 10:21:39.683 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - 200 232911 text/css 690.2671ms
2025-06-12 10:21:39.686 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - 200 106394 text/css 691.3757ms
2025-06-12 10:21:39.737 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - 200 2637 image/png 467.102ms
2025-06-12 10:21:39.762 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - 200 3997 text/javascript 350.8685ms
2025-06-12 10:21:39.763 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-12 10:21:39.767 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-12 10:21:39.768 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - null null
2025-06-12 10:21:39.790 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-12 10:21:39.794 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-12 10:21:39.798 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 200 45276 application/font-woff 34.6682ms
2025-06-12 10:21:39.800 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/webfonts/fa-solid-900.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\webfonts\fa-solid-900.woff2'
2025-06-12 10:21:39.800 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - 200 130396 font/woff2 33.2277ms
2025-06-12 10:21:39.807 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - 200 158220 font/woff2 38.1595ms
2025-06-12 10:21:39.829 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-12 10:21:39.831 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-12 10:21:39.833 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-12 10:21:39.838 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-12 10:21:39.839 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 200 45276 application/font-woff 9.4389ms
2025-06-12 10:21:39.841 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - 200 130396 font/woff2 9.8447ms
2025-06-12 10:21:39.847 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-12 10:21:39.853 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-12 10:21:39.855 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 200 5430 image/x-icon 8.4772ms
2025-06-12 10:21:39.911 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711099727 - null null
2025-06-12 10:21:39.914 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:21:39.915 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:21:39.959 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:21:39.962 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:21:39.966 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:21:39.969 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 8.8973ms.
2025-06-12 10:21:39.973 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:21:39.975 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 1.6425ms.
2025-06-12 10:21:39.977 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 58.5502ms
2025-06-12 10:21:39.978 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:21:39.980 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711099727 - 200 null text/html; charset=utf-8 69.0598ms
2025-06-12 10:21:59.229 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:21:59.292 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:21:59.293 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:21:59.366 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:21:59.368 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.035ms.
2025-06-12 10:21:59.371 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:21:59.374 +03:30 [INF] Executed ViewResult - view Insert executed in 3.1761ms.
2025-06-12 10:21:59.376 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 79.83ms
2025-06-12 10:21:59.377 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:21:59.380 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 150.3636ms
2025-06-12 10:21:59.538 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:21:59.539 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:21:59.544 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 4.7824ms
2025-06-12 10:21:59.552 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 14.5126ms
2025-06-12 10:21:59.761 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711119629 - null null
2025-06-12 10:21:59.764 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:21:59.766 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:21:59.809 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:21:59.839 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:21:59.844 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:21:59.850 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 12.6184ms.
2025-06-12 10:21:59.856 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:21:59.858 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 1.676ms.
2025-06-12 10:21:59.859 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 89.7111ms
2025-06-12 10:21:59.861 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:21:59.862 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711119629 - 200 null text/html; charset=utf-8 101.6034ms
2025-06-12 10:22:35.652 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:22:56.758 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:22:56.760 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:22:56.802 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:22:56.805 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0272ms.
2025-06-12 10:22:56.807 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:22:56.811 +03:30 [INF] Executed ViewResult - view Insert executed in 4.1853ms.
2025-06-12 10:22:56.831 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 68.1404ms
2025-06-12 10:22:56.833 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:22:56.835 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 21183.4888ms
2025-06-12 10:22:56.999 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-12 10:22:57.009 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - null null
2025-06-12 10:22:57.003 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - null null
2025-06-12 10:22:57.078 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - null null
2025-06-12 10:22:57.001 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-12 10:22:57.078 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - null null
2025-06-12 10:22:57.083 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - null null
2025-06-12 10:22:57.101 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - null null
2025-06-12 10:22:57.119 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - null null
2025-06-12 10:22:57.137 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - null null
2025-06-12 10:22:57.137 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - null null
2025-06-12 10:22:57.140 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-12 10:22:57.143 +03:30 [INF] Sending file. Request path: '/lib/source-sans-3/index.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\source-sans-3\index.css'
2025-06-12 10:22:57.145 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-12 10:22:57.148 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-12 10:22:57.154 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/avatars/male.png - null null
2025-06-12 10:22:57.158 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - null null
2025-06-12 10:22:57.161 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery/jquery.min.js - null null
2025-06-12 10:22:57.164 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js - null null
2025-06-12 10:22:57.166 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-12 10:22:57.170 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - 200 2520 text/css 91.6693ms
2025-06-12 10:22:57.175 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery-validation/dist/jquery.validate.min.js - null null
2025-06-12 10:22:57.178 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-12 10:22:57.181 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-12 10:22:57.184 +03:30 [INF] Sending file. Request path: '/assets/img/AdminLTELogo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\AdminLTELogo.png'
2025-06-12 10:22:57.187 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - null null
2025-06-12 10:22:57.190 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - null null
2025-06-12 10:22:57.191 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 200 2750 image/jpeg 51.5677ms
2025-06-12 10:22:57.195 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.js - null null
2025-06-12 10:22:57.199 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - null null
2025-06-12 10:22:57.201 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 200 3398 image/jpeg 52.6309ms
2025-06-12 10:22:57.202 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/avatars/male.png - 200 268 image/png 48.1592ms
2025-06-12 10:22:57.204 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - 200 2637 image/png 46.3505ms
2025-06-12 10:22:57.207 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - null null
2025-06-12 10:22:57.210 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js - null null
2025-06-12 10:22:57.213 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - null null
2025-06-12 10:22:57.217 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.css'
2025-06-12 10:22:57.220 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-12 10:22:57.224 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:22:57.227 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:22:57.236 +03:30 [INF] Sending file. Request path: '/css/site.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site.css'
2025-06-12 10:22:57.242 +03:30 [INF] Sending file. Request path: '/js/site.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\site.js'
2025-06-12 10:22:57.244 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - 200 5267 text/css 235.4599ms
2025-06-12 10:22:57.246 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - 200 14806 text/css 243.0524ms
2025-06-12 10:22:57.233 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/styles/overlayscrollbars.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\styles\overlayscrollbars.min.css'
2025-06-12 10:22:57.239 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-12 10:22:57.251 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js'
2025-06-12 10:22:57.308 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - 200 5824 text/javascript 121.7461ms
2025-06-12 10:22:57.258 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - 200 7707 text/css 120.8462ms
2025-06-12 10:22:57.260 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - 200 3997 text/javascript 47.4628ms
2025-06-12 10:22:57.524 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/js/bootstrap.bundle.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\js\bootstrap.bundle.min.js'
2025-06-12 10:22:57.265 +03:30 [INF] Sending file. Request path: '/lib/popper.js/dist/umd/popper.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\popper.js\dist\umd\popper.min.js'
2025-06-12 10:22:57.269 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\browser\overlayscrollbars.browser.es6.min.js'
2025-06-12 10:22:57.370 +03:30 [INF] Sending file. Request path: '/js/adminlte.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\adminlte.js'
2025-06-12 10:22:57.368 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation/dist/jquery.validate.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js'
2025-06-12 10:22:57.256 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.css'
2025-06-12 10:22:57.272 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - 200 13202 text/css 189.3139ms
2025-06-12 10:22:57.274 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 200 4983 image/jpeg 129.0126ms
2025-06-12 10:22:57.369 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 141.5938ms
2025-06-12 10:22:57.421 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.js'
2025-06-12 10:22:57.443 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.js'
2025-06-12 10:22:57.526 +03:30 [INF] Sending file. Request path: '/lib/jquery/jquery.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery\jquery.min.js'
2025-06-12 10:22:57.445 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/bootstrap-icons.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\bootstrap-icons.min.css'
2025-06-12 10:22:57.541 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-12 10:22:57.654 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-12 10:22:57.563 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.js'
2025-06-12 10:22:57.655 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 430.7716ms
2025-06-12 10:22:57.721 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js - 200 80721 text/javascript 557.1309ms
2025-06-12 10:22:57.723 +03:30 [INF] Sending file. Request path: '/css/adminlte.rtl.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\adminlte.rtl.css'
2025-06-12 10:22:57.724 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - 200 20122 text/javascript 516.9098ms
2025-06-12 10:22:57.728 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - 200 29334 text/javascript 626.7479ms
2025-06-12 10:22:57.730 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js - 200 29455 text/javascript 520.098ms
2025-06-12 10:22:57.732 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery-validation/dist/jquery.validate.min.js - 200 25308 text/javascript 556.8376ms
2025-06-12 10:22:57.734 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - 200 23932 text/css 655.6796ms
2025-06-12 10:22:57.751 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - 200 46668 text/javascript 552.1305ms
2025-06-12 10:22:57.754 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.js - 200 67484 text/javascript 558.4668ms
2025-06-12 10:22:57.757 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery/jquery.min.js - 200 87533 text/javascript 596.3654ms
2025-06-12 10:22:57.759 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - 200 85875 text/css 639.8602ms
2025-06-12 10:22:57.761 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - 200 106394 text/css 759.921ms
2025-06-12 10:22:57.764 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - 200 232911 text/css 764.944ms
2025-06-12 10:22:57.766 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - 200 147552 text/javascript 576.6115ms
2025-06-12 10:22:57.777 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - 200 358126 text/css 640.8269ms
2025-06-12 10:22:57.824 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-12 10:22:57.828 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-12 10:22:57.830 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - null null
2025-06-12 10:22:57.836 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-12 10:22:57.842 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-12 10:22:57.845 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/webfonts/fa-solid-900.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\webfonts\fa-solid-900.woff2'
2025-06-12 10:22:57.846 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 200 45276 application/font-woff 21.7429ms
2025-06-12 10:22:57.848 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - 200 130396 font/woff2 19.3742ms
2025-06-12 10:22:57.850 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - 200 158220 font/woff2 19.6828ms
2025-06-12 10:22:57.897 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-12 10:22:57.902 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-12 10:22:57.904 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 200 5430 image/x-icon 6.2747ms
2025-06-12 10:22:57.912 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-12 10:22:57.915 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-12 10:22:57.917 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 200 45276 application/font-woff 5.759ms
2025-06-12 10:22:57.985 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711177848 - null null
2025-06-12 10:22:57.989 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:22:57.991 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:22:58.038 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:22:58.042 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:22:58.048 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:22:58.051 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 11.3102ms.
2025-06-12 10:22:58.053 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:22:58.055 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 1.751ms.
2025-06-12 10:22:58.057 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 63.5788ms
2025-06-12 10:22:58.059 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:22:58.061 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711177848 - 200 null text/html; charset=utf-8 75.9315ms
2025-06-12 10:23:10.050 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=************* - null null
2025-06-12 10:23:10.057 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:23:10.063 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:23:10.095 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:23:12.834 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:23:12.840 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 2740.9772ms.
2025-06-12 10:23:12.843 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:23:13.909 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 3843.4097ms
2025-06-12 10:23:13.909 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:23:14.018 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:23:14.080 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:23:14.237 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard#1.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard+<ExecuteAsync>d__16..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard#1.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.PartialViewResultExecutor.ExecuteAsync(ActionContext context, PartialViewResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-12 10:23:14.240 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:23:14.304 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=************* - 500 null text/plain; charset=utf-8 4253.3044ms
2025-06-12 10:23:14.304 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:23:14.314 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0596ms.
2025-06-12 10:23:14.316 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:23:14.333 +03:30 [INF] Executed ViewResult - view Insert executed in 17.8233ms.
2025-06-12 10:23:14.336 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 80.882ms
2025-06-12 10:23:14.339 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:23:14.342 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 433.5023ms
2025-06-12 10:23:14.449 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:23:14.453 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:23:14.457 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 4.2024ms
2025-06-12 10:23:14.465 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 16.8397ms
2025-06-12 10:23:14.694 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711194610 - null null
2025-06-12 10:23:14.708 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:23:14.711 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:23:14.782 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:23:14.787 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:23:14.791 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:23:14.794 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 9.5993ms.
2025-06-12 10:23:14.797 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:23:14.799 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 2.5029ms.
2025-06-12 10:23:14.800 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 86.4602ms
2025-06-12 10:23:14.801 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:23:14.802 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711194610 - 200 null text/html; charset=utf-8 108.8299ms
2025-06-12 10:23:19.491 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=************* - null null
2025-06-12 10:23:19.497 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:23:19.500 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:23:19.536 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:23:20.900 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:23:20.908 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 1367.7221ms.
2025-06-12 10:23:20.910 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:23:21.744 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 2240.0944ms
2025-06-12 10:23:21.845 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:23:22.012 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard#1.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard+<ExecuteAsync>d__16..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard#1.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.PartialViewResultExecutor.ExecuteAsync(ActionContext context, PartialViewResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-12 10:23:22.032 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=************* - 500 null text/plain; charset=utf-8 2540.5941ms
2025-06-12 10:23:48.208 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=************* - null null
2025-06-12 10:23:48.213 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:23:48.215 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:23:48.249 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:23:50.868 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:23:54.539 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 6287.6388ms.
2025-06-12 10:23:54.564 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:23:55.355 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 7136.0441ms
2025-06-12 10:23:55.469 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:23:55.638 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard#1.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard+<ExecuteAsync>d__16..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard#1.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.PartialViewResultExecutor.ExecuteAsync(ActionContext context, PartialViewResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-12 10:23:55.670 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=************* - 500 null text/plain; charset=utf-8 7462.0644ms
2025-06-12 10:24:12.121 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:24:12.192 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:24:12.195 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:24:12.247 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:24:12.251 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0347ms.
2025-06-12 10:24:12.253 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:24:12.255 +03:30 [INF] Executed ViewResult - view Insert executed in 2.4194ms.
2025-06-12 10:24:12.257 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 58.825ms
2025-06-12 10:24:12.259 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:24:12.261 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 140.4354ms
2025-06-12 10:24:12.371 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:24:12.373 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:24:12.382 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 9.7001ms
2025-06-12 10:24:12.405 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 34.0652ms
2025-06-12 10:24:12.619 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711252527 - null null
2025-06-12 10:24:12.623 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:24:12.624 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:24:12.668 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:24:12.671 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:24:12.678 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:24:12.682 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 12.1816ms.
2025-06-12 10:24:12.684 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:24:12.686 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 1.4492ms.
2025-06-12 10:24:12.688 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 60.4148ms
2025-06-12 10:24:12.690 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:24:12.692 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711252527 - 200 null text/html; charset=utf-8 72.794ms
2025-06-12 10:24:20.466 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=************* - null null
2025-06-12 10:24:20.469 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:24:20.471 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:24:20.504 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:24:22.792 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:24:22.885 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 2377.9051ms.
2025-06-12 10:24:22.890 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:24:24.002 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 3528.9828ms
2025-06-12 10:24:24.226 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:24:24.439 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard#1.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard+<ExecuteAsync>d__16..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard#1.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.PartialViewResultExecutor.ExecuteAsync(ActionContext context, PartialViewResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-12 10:24:24.458 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=************* - 500 null text/plain; charset=utf-8 3992.3537ms
2025-06-12 10:24:43.460 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:24:43.525 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:24:43.529 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:24:43.584 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:24:43.588 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.051ms.
2025-06-12 10:24:43.592 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:24:43.605 +03:30 [INF] Executed ViewResult - view Insert executed in 13.3797ms.
2025-06-12 10:24:43.608 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 76.6183ms
2025-06-12 10:24:43.613 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:24:43.615 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 154.6298ms
2025-06-12 10:24:43.670 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - null null
2025-06-12 10:24:43.675 +03:30 [INF] The file /js/site.js was not modified
2025-06-12 10:24:43.677 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - 304 null text/javascript 7.3253ms
2025-06-12 10:24:43.678 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:24:43.679 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:24:43.699 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 20.5921ms
2025-06-12 10:24:43.703 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 24.7975ms
2025-06-12 10:24:43.796 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711283724 - null null
2025-06-12 10:24:43.813 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:24:43.816 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:24:43.867 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:24:43.870 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:24:43.876 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:24:43.881 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 11.8901ms.
2025-06-12 10:24:43.883 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:24:43.885 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 2.3055ms.
2025-06-12 10:24:43.886 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 65.0177ms
2025-06-12 10:24:43.888 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:24:43.890 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711283724 - 200 null text/html; charset=utf-8 93.1974ms
2025-06-12 10:24:46.576 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:24:46.852 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:24:46.855 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:24:46.908 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:24:46.911 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0183ms.
2025-06-12 10:24:46.913 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:24:46.916 +03:30 [INF] Executed ViewResult - view Insert executed in 2.6208ms.
2025-06-12 10:24:46.918 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 60.1012ms
2025-06-12 10:24:46.921 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:24:46.923 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 347.5808ms
2025-06-12 10:24:46.982 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-12 10:24:46.983 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-12 10:24:46.986 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - null null
2025-06-12 10:24:46.984 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - null null
2025-06-12 10:24:46.983 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - null null
2025-06-12 10:24:46.986 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - null null
2025-06-12 10:24:47.038 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - null null
2025-06-12 10:24:47.038 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - null null
2025-06-12 10:24:47.060 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - null null
2025-06-12 10:24:47.062 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - null null
2025-06-12 10:24:47.065 +03:30 [INF] Sending file. Request path: '/lib/source-sans-3/index.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\source-sans-3\index.css'
2025-06-12 10:24:47.067 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - null null
2025-06-12 10:24:47.069 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-12 10:24:47.073 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-12 10:24:47.075 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-12 10:24:47.079 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/avatars/male.png - null null
2025-06-12 10:24:47.081 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - null null
2025-06-12 10:24:47.083 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery/jquery.min.js - null null
2025-06-12 10:24:47.085 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - 200 2520 text/css 98.3089ms
2025-06-12 10:24:47.089 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js - null null
2025-06-12 10:24:47.093 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-12 10:24:47.097 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery-validation/dist/jquery.validate.min.js - null null
2025-06-12 10:24:47.100 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-12 10:24:47.102 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-12 10:24:47.105 +03:30 [INF] Sending file. Request path: '/assets/img/AdminLTELogo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\AdminLTELogo.png'
2025-06-12 10:24:47.108 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - null null
2025-06-12 10:24:47.111 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - null null
2025-06-12 10:24:47.114 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.js - null null
2025-06-12 10:24:47.115 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 200 2750 image/jpeg 45.9684ms
2025-06-12 10:24:47.119 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - null null
2025-06-12 10:24:47.120 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 200 3398 image/jpeg 45.0047ms
2025-06-12 10:24:47.124 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/avatars/male.png - 200 268 image/png 45.2694ms
2025-06-12 10:24:47.126 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - 200 2637 image/png 44.8207ms
2025-06-12 10:24:47.130 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - null null
2025-06-12 10:24:47.133 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js - null null
2025-06-12 10:24:47.136 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - null null
2025-06-12 10:24:47.142 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:24:47.146 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:24:47.166 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-12 10:24:47.180 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.css'
2025-06-12 10:24:47.170 +03:30 [INF] Sending file. Request path: '/js/site.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\site.js'
2025-06-12 10:24:47.149 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.css'
2025-06-12 10:24:47.153 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-12 10:24:47.160 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/styles/overlayscrollbars.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\styles\overlayscrollbars.min.css'
2025-06-12 10:24:47.176 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js'
2025-06-12 10:24:47.164 +03:30 [INF] Sending file. Request path: '/css/site.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site.css'
2025-06-12 10:24:47.180 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 200 4983 image/jpeg 107.6543ms
2025-06-12 10:24:47.183 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - 200 23932 text/css 196.5263ms
2025-06-12 10:24:47.185 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - 200 3997 text/javascript 48.6648ms
2025-06-12 10:24:47.186 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - 200 5267 text/css 201.955ms
2025-06-12 10:24:47.188 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - 200 14806 text/css 204.8912ms
2025-06-12 10:24:47.191 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - 200 13202 text/css 153.0171ms
2025-06-12 10:24:47.193 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - 200 5824 text/javascript 84.1836ms
2025-06-12 10:24:47.195 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - 200 7707 text/css 127.6117ms
2025-06-12 10:24:47.198 +03:30 [INF] Sending file. Request path: '/lib/popper.js/dist/umd/popper.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\popper.js\dist\umd\popper.min.js'
2025-06-12 10:24:47.200 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\browser\overlayscrollbars.browser.es6.min.js'
2025-06-12 10:24:47.207 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation/dist/jquery.validate.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js'
2025-06-12 10:24:47.211 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 65.0134ms
2025-06-12 10:24:47.217 +03:30 [INF] Sending file. Request path: '/js/adminlte.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\adminlte.js'
2025-06-12 10:24:47.224 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.js'
2025-06-12 10:24:47.232 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.js'
2025-06-12 10:24:47.237 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/bootstrap-icons.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\bootstrap-icons.min.css'
2025-06-12 10:24:47.238 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - 200 20122 text/javascript 108.638ms
2025-06-12 10:24:47.242 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - 200 29334 text/javascript 203.0942ms
2025-06-12 10:24:47.244 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery-validation/dist/jquery.validate.min.js - 200 25308 text/javascript 147.3078ms
2025-06-12 10:24:47.250 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/js/bootstrap.bundle.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\js\bootstrap.bundle.min.js'
2025-06-12 10:24:47.252 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js - 200 29455 text/javascript 118.4069ms
2025-06-12 10:24:47.253 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - 200 46668 text/javascript 134.2769ms
2025-06-12 10:24:47.256 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.js - 200 67484 text/javascript 141.9361ms
2025-06-12 10:24:47.258 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - 200 85875 text/css 198.3038ms
2025-06-12 10:24:47.262 +03:30 [INF] Sending file. Request path: '/lib/jquery/jquery.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery\jquery.min.js'
2025-06-12 10:24:47.268 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-12 10:24:47.276 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.js'
2025-06-12 10:24:47.276 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js - 200 80721 text/javascript 186.8534ms
2025-06-12 10:24:47.282 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-12 10:24:47.285 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 143.324ms
2025-06-12 10:24:47.292 +03:30 [INF] Sending file. Request path: '/css/adminlte.rtl.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\adminlte.rtl.css'
2025-06-12 10:24:47.298 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery/jquery.min.js - 200 87533 text/javascript 214.4726ms
2025-06-12 10:24:47.300 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - 200 106394 text/css 317.5446ms
2025-06-12 10:24:47.303 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - 200 147552 text/javascript 191.1613ms
2025-06-12 10:24:47.310 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - 200 232911 text/css 328.6172ms
2025-06-12 10:24:47.317 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - 200 358126 text/css 255.4751ms
2025-06-12 10:24:47.561 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-12 10:24:47.566 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-12 10:24:47.567 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-12 10:24:47.568 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - null null
2025-06-12 10:24:47.569 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 200 45276 application/font-woff 7.9036ms
2025-06-12 10:24:47.577 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-12 10:24:47.581 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/webfonts/fa-solid-900.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\webfonts\fa-solid-900.woff2'
2025-06-12 10:24:47.590 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - 200 130396 font/woff2 22.7091ms
2025-06-12 10:24:47.592 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - 200 158220 font/woff2 23.2506ms
2025-06-12 10:24:47.654 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-12 10:24:47.659 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-12 10:24:47.662 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 200 5430 image/x-icon 7.2767ms
2025-06-12 10:24:47.708 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711287579 - null null
2025-06-12 10:24:47.712 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:24:47.713 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:24:47.762 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:24:47.766 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:24:47.771 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:24:47.775 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 10.7173ms.
2025-06-12 10:24:47.777 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:24:47.779 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 1.5572ms.
2025-06-12 10:24:47.780 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 64.3504ms
2025-06-12 10:24:47.782 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:24:47.783 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711287579 - 200 null text/html; charset=utf-8 75.3838ms
2025-06-12 10:24:50.320 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=************* - null null
2025-06-12 10:24:50.325 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:24:50.328 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:24:50.376 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:24:52.108 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:24:53.065 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 2686.7663ms.
2025-06-12 10:24:53.069 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:24:54.036 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 3705.4245ms
2025-06-12 10:24:54.183 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:24:54.354 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard#1.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard+<ExecuteAsync>d__16..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard#1.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.PartialViewResultExecutor.ExecuteAsync(ActionContext context, PartialViewResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-12 10:24:54.378 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=************* - 500 null text/plain; charset=utf-8 4058.2849ms
2025-06-12 10:25:02.686 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-12 10:25:02.686 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-12 10:25:02.691 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-12 10:25:02.693 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-12 10:25:02.694 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 8.5015ms
2025-06-12 10:25:02.696 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 10.2618ms
2025-06-12 10:25:06.977 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=************* - null null
2025-06-12 10:25:06.982 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:25:06.983 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:25:07.024 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:25:08.391 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:25:10.675 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 3647.6388ms.
2025-06-12 10:25:10.725 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:25:11.669 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 4682.7355ms
2025-06-12 10:25:11.843 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:25:12.007 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.MethodAccessException: Attempt by method 'AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard#1.ExecuteAsync()' to access method 'AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard+<ExecuteAsync>d__16..ctor()' failed.
   at AspNetCoreGeneratedDocument.Views_Database__Insert_Wizard#1.ExecuteAsync()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageCoreAsync(IRazorPage page, ViewContext context)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderPageAsync(IRazorPage page, ViewContext context, Boolean invokeViewStarts)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.PartialViewResultExecutor.ExecuteAsync(ActionContext context, PartialViewResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-12 10:25:12.025 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=************* - 500 null text/plain; charset=utf-8 5047.5867ms
2025-06-12 10:26:17.339 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-12 10:26:17.415 +03:30 [ERR] An error occurred while seeding the database
System.InvalidOperationException: No service for type 'Microsoft.AspNetCore.Identity.RoleManager`1[Microsoft.AspNetCore.Identity.IdentityRole]' has been registered.
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<Main>$(String[] args) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\Program.cs:line 164
2025-06-12 10:26:17.965 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-12 10:26:17.976 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-12 10:26:18.064 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-12 10:26:18.066 +03:30 [INF] Hosting environment: Development
2025-06-12 10:26:18.068 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-12 10:26:18.070 +03:30 [INF] The application has started
2025-06-12 10:26:32.974 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-12 10:26:54.269 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-12 10:26:54.403 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-12 10:26:54.490 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:26:54.500 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 1.084ms.
2025-06-12 10:26:54.523 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-12 10:26:54.676 +03:30 [INF] Executed ViewResult - view Index executed in 165.0774ms.
2025-06-12 10:26:54.683 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 275.7576ms
2025-06-12 10:26:54.686 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-12 10:26:54.695 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 21726.8357ms
2025-06-12 10:26:54.778 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-12 10:26:54.779 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-12 10:26:54.829 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - null null
2025-06-12 10:26:54.779 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - null null
2025-06-12 10:26:54.876 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - null null
2025-06-12 10:26:54.919 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:26:55.462 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-12 10:26:55.462 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.css'
2025-06-12 10:26:55.462 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-12 10:26:55.462 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-12 10:26:54.920 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - null null
2025-06-12 10:26:54.945 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - null null
2025-06-12 10:26:55.590 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - 499 106394 text/css 811.6245ms
2025-06-12 10:26:55.592 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - 499 5267 text/css 762.7085ms
2025-06-12 10:26:55.593 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - 499 232911 text/css 814.9874ms
2025-06-12 10:26:55.595 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - 499 14806 text/css 816.726ms
2025-06-12 10:26:55.585 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.css'
2025-06-12 10:26:55.591 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:26:55.724 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js'
2025-06-12 10:26:55.730 +03:30 [INF] The file /js/site.js was not modified
2025-06-12 10:26:55.734 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 815.8383ms
2025-06-12 10:26:55.747 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - 499 23932 text/css 915.9857ms
2025-06-12 10:26:55.755 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - 499 5824 text/javascript 836.0174ms
2025-06-12 10:26:55.757 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - 304 null text/javascript 812.6897ms
2025-06-12 10:26:55.802 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 210.5536ms
2025-06-12 10:26:55.806 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-12 10:26:56.000 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-12 10:26:56.003 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 499 5430 image/x-icon 196.4418ms
2025-06-12 10:27:01.178 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:27:01.236 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:27:01.242 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:27:01.281 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:27:01.470 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 185.2959ms.
2025-06-12 10:27:01.475 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:27:01.494 +03:30 [INF] Executed ViewResult - view Insert executed in 20.2694ms.
2025-06-12 10:27:01.497 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 251.4021ms
2025-06-12 10:27:01.500 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:27:01.503 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 325.6507ms
2025-06-12 10:27:01.578 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:27:01.578 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:27:01.595 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 17.232ms
2025-06-12 10:27:01.608 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 29.8355ms
2025-06-12 10:27:02.125 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - null null
2025-06-12 10:27:02.347 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711422138 - null null
2025-06-12 10:27:02.347 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/webfonts/fa-solid-900.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\webfonts\fa-solid-900.woff2'
2025-06-12 10:27:02.352 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - 499 158220 font/woff2 227.1308ms
2025-06-12 10:27:02.366 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:27:02.371 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:27:02.397 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:27:04.020 +03:30 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:27:04.154 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:27:04.356 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 1956.7445ms.
2025-06-12 10:27:04.361 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:27:04.551 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 190.8843ms.
2025-06-12 10:27:04.554 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 2179.9695ms
2025-06-12 10:27:04.557 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:27:04.566 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711422138 - 200 null text/html; charset=utf-8 2262.8997ms
2025-06-12 10:27:08.272 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749711422139 - null null
2025-06-12 10:27:08.276 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:27:08.288 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:27:08.336 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:27:09.937 +03:30 [INF] Executed DbCommand (26ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:27:10.094 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 1754.37ms.
2025-06-12 10:27:10.098 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:27:10.133 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 35.949ms.
2025-06-12 10:27:10.135 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 1843.5032ms
2025-06-12 10:27:10.137 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:27:10.138 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749711422139 - 200 null text/html; charset=utf-8 1866.3804ms
2025-06-12 10:27:18.056 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Database/_FileUpload - multipart/form-data; boundary=----geckoformboundary2947143265a60382753317729a419512 2488595
2025-06-12 10:27:18.065 +03:30 [INF] CORS policy execution successful.
2025-06-12 10:27:18.070 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:27:18.079 +03:30 [INF] Route matched with {action = "_FileUpload", controller = "Database"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] _FileUpload(System.String, Microsoft.AspNetCore.Http.IFormFile) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:27:18.201 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:27:18.668 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:27:18.674 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 470.9755ms.
2025-06-12 10:27:18.680 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType2`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-12 10:27:18.694 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) in 611.6954ms
2025-06-12 10:27:18.695 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:27:18.700 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Database/_FileUpload - 200 null application/json; charset=utf-8 644.0756ms
2025-06-12 10:27:18.704 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=33229981 - null null
2025-06-12 10:27:18.708 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:27:18.709 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:27:18.732 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:27:40.576 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:27:40.609 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 21875.1334ms.
2025-06-12 10:27:40.612 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:27:40.620 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 8.5801ms.
2025-06-12 10:27:40.635 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 21922.8226ms
2025-06-12 10:27:40.637 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:27:40.638 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=33229981 - 200 null text/html; charset=utf-8 21934.3852ms
2025-06-12 10:29:27.627 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:29:48.735 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:29:48.742 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:29:48.772 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:29:48.776 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0486ms.
2025-06-12 10:29:48.779 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:29:48.794 +03:30 [INF] Executed ViewResult - view Insert executed in 16.3568ms.
2025-06-12 10:29:48.802 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 56.6593ms
2025-06-12 10:29:48.804 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:29:48.806 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 21179.809ms
2025-06-12 10:29:48.849 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - null null
2025-06-12 10:29:48.854 +03:30 [INF] The file /js/site.js was not modified
2025-06-12 10:29:48.858 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:29:48.858 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - 304 null text/javascript 8.7109ms
2025-06-12 10:29:48.858 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:29:48.913 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 55.4648ms
2025-06-12 10:29:48.916 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 58.6322ms
2025-06-12 10:29:49.648 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711589568 - null null
2025-06-12 10:29:49.664 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:29:49.669 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:29:49.702 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:29:49.714 +03:30 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:29:49.720 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:29:49.727 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 21.6534ms.
2025-06-12 10:29:49.730 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:29:49.732 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 3.262ms.
2025-06-12 10:29:49.734 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 60.8305ms
2025-06-12 10:29:49.736 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:29:49.737 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711589568 - 200 null text/html; charset=utf-8 89.974ms
2025-06-12 10:29:53.945 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749711589569 - null null
2025-06-12 10:29:53.951 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:29:53.954 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:29:53.982 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:29:53.992 +03:30 [INF] Executed DbCommand (6ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:29:54.008 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 22.8877ms.
2025-06-12 10:29:54.012 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:29:54.035 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 24.2197ms.
2025-06-12 10:29:54.038 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 79.4164ms
2025-06-12 10:29:54.040 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:29:54.042 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749711589569 - 200 null text/html; charset=utf-8 97.4122ms
2025-06-12 10:29:58.201 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Database/_FileUpload - multipart/form-data; boundary=----geckoformboundarya1e7e5cedfce9523c1f3dfc783e0a7e 2488590
2025-06-12 10:29:58.206 +03:30 [INF] CORS policy execution successful.
2025-06-12 10:29:58.209 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:29:58.215 +03:30 [INF] Route matched with {action = "_FileUpload", controller = "Database"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] _FileUpload(System.String, Microsoft.AspNetCore.Http.IFormFile) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:29:58.276 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:29:58.547 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:29:58.554 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 274.9738ms.
2025-06-12 10:29:58.557 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType2`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-12 10:29:58.562 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) in 344.7786ms
2025-06-12 10:29:58.564 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:29:58.566 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Database/_FileUpload - 200 null application/json; charset=utf-8 365.8774ms
2025-06-12 10:29:58.574 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=19424188 - null null
2025-06-12 10:29:58.578 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:29:58.579 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:29:58.616 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:29:58.620 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:29:58.629 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 11.0928ms.
2025-06-12 10:29:58.633 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:29:58.635 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 1.7347ms.
2025-06-12 10:29:58.636 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 54.5082ms
2025-06-12 10:29:58.638 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:29:58.640 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=19424188 - 200 null text/html; charset=utf-8 66.4097ms
2025-06-12 10:31:23.351 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:31:44.444 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:31:44.449 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:31:44.484 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:31:44.488 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.1039ms.
2025-06-12 10:31:44.493 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:31:44.502 +03:30 [INF] Executed ViewResult - view Insert executed in 9.9544ms.
2025-06-12 10:31:44.517 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 65.3524ms
2025-06-12 10:31:44.520 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:31:44.523 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 21171.8696ms
2025-06-12 10:31:44.584 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:31:44.585 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:31:44.590 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 5.7913ms
2025-06-12 10:31:44.605 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 21.6848ms
2025-06-12 10:31:45.560 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711704745 - null null
2025-06-12 10:31:45.572 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:31:45.575 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:31:45.597 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:31:45.606 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:31:45.611 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:31:45.615 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 14.8058ms.
2025-06-12 10:31:45.617 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:31:45.619 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 2.2584ms.
2025-06-12 10:31:45.621 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 44.0243ms
2025-06-12 10:31:45.622 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:31:45.623 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711704745 - 200 null text/html; charset=utf-8 63.64ms
2025-06-12 10:32:56.612 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:33:17.679 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:33:17.683 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:33:17.712 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:33:17.716 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0529ms.
2025-06-12 10:33:17.721 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:33:17.738 +03:30 [INF] Executed ViewResult - view Insert executed in 9.8862ms.
2025-06-12 10:33:17.745 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 59.1901ms
2025-06-12 10:33:17.747 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:33:17.749 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 21137.6576ms
2025-06-12 10:33:17.790 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - null null
2025-06-12 10:33:17.797 +03:30 [INF] The file /js/site.js was not modified
2025-06-12 10:33:17.798 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:33:17.799 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - 304 null text/javascript 8.1582ms
2025-06-12 10:33:17.799 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:33:17.832 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 33.9886ms
2025-06-12 10:33:17.835 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 35.5899ms
2025-06-12 10:33:18.079 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711798002 - null null
2025-06-12 10:33:18.094 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:33:18.098 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:33:18.122 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:33:18.132 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:33:18.140 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:33:18.146 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 20.1008ms.
2025-06-12 10:33:18.149 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:33:18.153 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 3.9945ms.
2025-06-12 10:33:18.155 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 53.3015ms
2025-06-12 10:33:18.158 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:33:18.159 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711798002 - 200 null text/html; charset=utf-8 80.0376ms
2025-06-12 10:33:23.615 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749711798003 - null null
2025-06-12 10:33:23.619 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:33:23.623 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:33:23.649 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:33:23.657 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:33:23.664 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 11.8776ms.
2025-06-12 10:33:23.666 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:33:23.683 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 17.7112ms.
2025-06-12 10:33:23.687 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 60.8771ms
2025-06-12 10:33:23.691 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:33:23.692 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749711798003 - 200 null text/html; charset=utf-8 76.9675ms
2025-06-12 10:33:29.067 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Database/_FileUpload - multipart/form-data; boundary=----geckoformboundarye0a508b7ab1d7eaa137d249301a09ac7 2488595
2025-06-12 10:33:29.073 +03:30 [INF] CORS policy execution successful.
2025-06-12 10:33:29.076 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:33:29.080 +03:30 [INF] Route matched with {action = "_FileUpload", controller = "Database"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] _FileUpload(System.String, Microsoft.AspNetCore.Http.IFormFile) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:33:29.149 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:33:29.437 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:33:29.442 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 291.6258ms.
2025-06-12 10:33:29.445 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType2`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-12 10:33:29.448 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) in 364.9977ms
2025-06-12 10:33:29.450 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:33:29.453 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Database/_FileUpload - 200 null application/json; charset=utf-8 386.3277ms
2025-06-12 10:33:29.462 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=35645716 - null null
2025-06-12 10:33:29.468 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:33:29.469 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:33:29.495 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:33:29.499 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:33:29.509 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 11.1745ms.
2025-06-12 10:33:29.513 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:33:29.515 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 2.0907ms.
2025-06-12 10:33:29.517 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 43.7615ms
2025-06-12 10:33:29.519 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:33:29.521 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=35645716 - 200 null text/html; charset=utf-8 58.2679ms
2025-06-12 10:33:36.099 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Database/PublishData - application/x-www-form-urlencoded; charset=UTF-8 225
2025-06-12 10:33:36.105 +03:30 [INF] CORS policy execution successful.
2025-06-12 10:33:36.107 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web)'
2025-06-12 10:33:36.110 +03:30 [INF] Route matched with {action = "PublishData", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult PublishData(System.String, System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:33:36.150 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:33:36.177 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:33:36.329 +03:30 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-12 10:33:36.375 +03:30 [INF] Executed DbCommand (10ms) [Parameters=[@__id_0='?' (Size = 11) (DbType = AnsiString)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[AliasName], [s].[CalcPeriod], [s].[DisableRule], [s].[Editable], [s].[FieldIndex], [s].[FieldLength], [s].[FieldType], [s].[IsDisplay], [s].[IsRequired], [s].[IsUnique], [s].[Name], [s].[SQLCalc], [s].[ShpFieldName], [s].[TableInfoId], [s].[UnitName], [s].[Updated], [s].[ValidationRule], [s].[WebService_Period], [s].[WebService_URL], [s].[Id0], [s].[Code], [s].[Domain_ID], [s].[FieldInfoId], [s].[IsMulti], [s].[Name0]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE CONVERT(varchar(11), [t].[Id]) = @__id_0
) AS [t0]
LEFT JOIN (
    SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [d].[Id] AS [Id0], [d].[Code], [d].[Domain_ID], [d].[FieldInfoId], [d].[IsMulti], [d].[Name] AS [Name0]
    FROM [FieldInfos] AS [f]
    LEFT JOIN [DomainInfos] AS [d] ON [f].[Id] = [d].[FieldInfoId]
) AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id], [s].[Id]
2025-06-12 10:33:52.644 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:34:51.798 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:34:59.136 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 82983.5323ms.
2025-06-12 10:34:59.140 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType2`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-12 10:34:59.373 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web) in 83260.943ms
2025-06-12 10:34:59.375 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web)'
2025-06-12 10:34:59.377 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Database/PublishData - 499 null application/json; charset=utf-8 83277.432ms
2025-06-12 10:35:12.953 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:35:12.957 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:35:13.008 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:35:13.011 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.026ms.
2025-06-12 10:35:13.015 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:35:13.023 +03:30 [INF] Executed ViewResult - view Insert executed in 9.1285ms.
2025-06-12 10:35:13.026 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 66.5257ms
2025-06-12 10:35:13.028 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:35:13.030 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 21232.0409ms
2025-06-12 10:35:13.095 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:35:13.096 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:35:13.103 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 6.8863ms
2025-06-12 10:35:13.106 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 11.1944ms
2025-06-12 10:35:14.087 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711913252 - null null
2025-06-12 10:35:14.103 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:35:14.110 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:35:14.166 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:35:14.174 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:35:14.178 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:35:14.181 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 12.409ms.
2025-06-12 10:35:14.184 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:35:14.187 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 3.9963ms.
2025-06-12 10:35:14.189 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 75.7872ms
2025-06-12 10:35:14.190 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:35:14.192 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749711913252 - 200 null text/html; charset=utf-8 104.3042ms
2025-06-12 10:36:52.000 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:37:13.110 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:37:13.116 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:37:13.165 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:37:13.169 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0345ms.
2025-06-12 10:37:13.172 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:37:13.180 +03:30 [INF] Executed ViewResult - view Insert executed in 8.5708ms.
2025-06-12 10:37:13.203 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 83.4711ms
2025-06-12 10:37:13.205 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:37:13.207 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 21207.1675ms
2025-06-12 10:37:13.241 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - null null
2025-06-12 10:37:13.248 +03:30 [INF] The file /js/site.js was not modified
2025-06-12 10:37:13.248 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:37:13.249 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:37:13.250 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=qX1iuNG_klHLHpWGScQsLD3N7UgldthbuizBVlq72RI - 304 null text/javascript 8.3468ms
2025-06-12 10:37:13.291 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 41.7627ms
2025-06-12 10:37:13.291 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 43.6236ms
2025-06-12 10:37:14.239 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749712033371 - null null
2025-06-12 10:37:14.250 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:37:14.255 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:37:14.298 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:37:14.305 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:37:14.311 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:37:14.314 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 12.2444ms.
2025-06-12 10:37:14.316 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:37:14.319 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 3.3829ms.
2025-06-12 10:37:14.321 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 62.7569ms
2025-06-12 10:37:14.322 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:37:14.324 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749712033371 - 200 null text/html; charset=utf-8 84.8076ms
2025-06-12 10:37:48.489 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-12 10:37:48.551 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:37:48.554 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:37:48.603 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:37:48.606 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0411ms.
2025-06-12 10:37:48.609 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-12 10:37:48.617 +03:30 [INF] Executed ViewResult - view Insert executed in 8.3599ms.
2025-06-12 10:37:48.645 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 87.8004ms
2025-06-12 10:37:48.648 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-12 10:37:48.649 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 160.5214ms
2025-06-12 10:37:48.678 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-12 10:37:48.680 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-12 10:37:48.685 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 5.1538ms
2025-06-12 10:37:48.708 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 30.0858ms
2025-06-12 10:37:49.689 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749712068713 - null null
2025-06-12 10:37:49.704 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:37:49.707 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:37:49.742 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:37:49.751 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:37:49.757 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-12 10:37:49.761 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 16.0017ms.
2025-06-12 10:37:49.763 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-12 10:37:49.766 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 2.9504ms.
2025-06-12 10:37:49.769 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 57.7907ms
2025-06-12 10:37:49.770 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-12 10:37:49.772 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1749712068713 - 200 null text/html; charset=utf-8 82.1655ms
2025-06-12 10:37:58.214 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749712068714 - null null
2025-06-12 10:37:58.218 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:37:58.220 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:37:58.254 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:37:58.263 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:37:58.269 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 11.774ms.
2025-06-12 10:37:58.272 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:37:58.281 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 9.3568ms.
2025-06-12 10:37:58.284 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 62.176ms
2025-06-12 10:37:58.287 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:37:58.289 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749712068714 - 200 null text/html; charset=utf-8 74.5519ms
2025-06-12 10:38:03.722 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Database/_FileUpload - multipart/form-data; boundary=----geckoformboundaryb07ab7e5491ec991c0bf395452b57fe4 2488595
2025-06-12 10:38:03.729 +03:30 [INF] CORS policy execution successful.
2025-06-12 10:38:03.730 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:38:03.736 +03:30 [INF] Route matched with {action = "_FileUpload", controller = "Database"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] _FileUpload(System.String, Microsoft.AspNetCore.Http.IFormFile) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:38:03.820 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:38:04.052 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:38:04.058 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 236.6378ms.
2025-06-12 10:38:04.061 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType2`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-12 10:38:04.064 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) in 323.9327ms
2025-06-12 10:38:04.066 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:38:04.069 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Database/_FileUpload - 200 null application/json; charset=utf-8 346.2902ms
2025-06-12 10:38:04.078 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=50685074 - null null
2025-06-12 10:38:04.081 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:38:04.082 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:38:04.123 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:38:04.128 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:38:04.134 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 8.882ms.
2025-06-12 10:38:04.137 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:38:04.139 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 1.5279ms.
2025-06-12 10:38:04.140 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 54.6138ms
2025-06-12 10:38:04.142 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:38:04.144 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=50685074 - 200 null text/html; charset=utf-8 66.229ms
2025-06-12 10:38:10.103 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Database/PublishData - application/x-www-form-urlencoded; charset=UTF-8 225
2025-06-12 10:38:10.108 +03:30 [INF] CORS policy execution successful.
2025-06-12 10:38:10.111 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web)'
2025-06-12 10:38:10.114 +03:30 [INF] Route matched with {action = "PublishData", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult PublishData(System.String, System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:38:10.152 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:38:10.164 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:38:10.217 +03:30 [INF] Executed DbCommand (12ms) [Parameters=[@__id_0='?' (Size = 11) (DbType = AnsiString)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[AliasName], [s].[CalcPeriod], [s].[DisableRule], [s].[Editable], [s].[FieldIndex], [s].[FieldLength], [s].[FieldType], [s].[IsDisplay], [s].[IsRequired], [s].[IsUnique], [s].[Name], [s].[SQLCalc], [s].[ShpFieldName], [s].[TableInfoId], [s].[UnitName], [s].[Updated], [s].[ValidationRule], [s].[WebService_Period], [s].[WebService_URL], [s].[Id0], [s].[Code], [s].[Domain_ID], [s].[FieldInfoId], [s].[IsMulti], [s].[Name0]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE CONVERT(varchar(11), [t].[Id]) = @__id_0
) AS [t0]
LEFT JOIN (
    SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [d].[Id] AS [Id0], [d].[Code], [d].[Domain_ID], [d].[FieldInfoId], [d].[IsMulti], [d].[Name] AS [Name0]
    FROM [FieldInfos] AS [f]
    LEFT JOIN [DomainInfos] AS [d] ON [f].[Id] = [d].[FieldInfoId]
) AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id], [s].[Id]
2025-06-12 10:38:12.335 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:38:12.346 +03:30 [INF] ImportShapefileApply: Source coordinate system is already Web Mercator. No transformation needed.
2025-06-12 10:39:52.168 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 102013.9411ms.
2025-06-12 10:39:52.190 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType2`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-12 10:39:52.192 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web) in 102075.2007ms
2025-06-12 10:39:52.195 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web)'
2025-06-12 10:39:52.196 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Database/PublishData - 200 null application/json; charset=utf-8 102092.7984ms
2025-06-12 10:39:55.684 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749712068715 - null null
2025-06-12 10:39:55.691 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:39:55.692 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:39:55.738 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:39:55.745 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:39:55.750 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 9.3171ms.
2025-06-12 10:39:55.751 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:39:55.753 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 1.8165ms.
2025-06-12 10:39:55.755 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 58.4321ms
2025-06-12 10:39:55.757 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:39:55.759 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&_=1749712068715 - 200 null text/html; charset=utf-8 74.8299ms
2025-06-12 10:40:00.570 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Database/_FileUpload - multipart/form-data; boundary=----geckoformboundary1b39f10e12038d4eff626fabaa6146d 2488590
2025-06-12 10:40:00.576 +03:30 [INF] CORS policy execution successful.
2025-06-12 10:40:00.578 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:40:00.580 +03:30 [INF] Route matched with {action = "_FileUpload", controller = "Database"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] _FileUpload(System.String, Microsoft.AspNetCore.Http.IFormFile) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:40:00.697 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:40:00.788 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:40:00.794 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 94.4207ms.
2025-06-12 10:40:00.796 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType2`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-12 10:40:00.798 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web) in 215.7303ms
2025-06-12 10:40:00.800 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._FileUpload (BaseGIS.Web)'
2025-06-12 10:40:00.804 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Database/_FileUpload - 200 null application/json; charset=utf-8 233.6491ms
2025-06-12 10:40:00.811 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=53576530 - null null
2025-06-12 10:40:00.814 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:40:00.815 +03:30 [INF] Route matched with {action = "_Insert_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:40:00.856 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) - Validation state: "Valid"
2025-06-12 10:40:00.861 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:40:00.868 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 9.3337ms.
2025-06-12 10:40:00.871 +03:30 [INF] Executing PartialViewResult, running view _Insert_Wizard.
2025-06-12 10:40:00.874 +03:30 [INF] Executed PartialViewResult - view _Insert_Wizard executed in 3.2411ms.
2025-06-12 10:40:00.878 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web) in 60.3999ms
2025-06-12 10:40:00.880 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_Wizard (BaseGIS.Web)'
2025-06-12 10:40:00.882 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_Wizard?id=6&path=53576530 - 200 null text/html; charset=utf-8 70.6829ms
2025-06-12 10:40:04.810 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Database/PublishData - application/x-www-form-urlencoded; charset=UTF-8 225
2025-06-12 10:40:04.814 +03:30 [INF] CORS policy execution successful.
2025-06-12 10:40:04.816 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web)'
2025-06-12 10:40:04.817 +03:30 [INF] Route matched with {action = "PublishData", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult PublishData(System.String, System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-12 10:40:04.871 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web) - Validation state: "Invalid"
2025-06-12 10:40:04.877 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-12 10:40:04.919 +03:30 [INF] Executed DbCommand (10ms) [Parameters=[@__id_0='?' (Size = 11) (DbType = AnsiString)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[AliasName], [s].[CalcPeriod], [s].[DisableRule], [s].[Editable], [s].[FieldIndex], [s].[FieldLength], [s].[FieldType], [s].[IsDisplay], [s].[IsRequired], [s].[IsUnique], [s].[Name], [s].[SQLCalc], [s].[ShpFieldName], [s].[TableInfoId], [s].[UnitName], [s].[Updated], [s].[ValidationRule], [s].[WebService_Period], [s].[WebService_URL], [s].[Id0], [s].[Code], [s].[Domain_ID], [s].[FieldInfoId], [s].[IsMulti], [s].[Name0]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE CONVERT(varchar(11), [t].[Id]) = @__id_0
) AS [t0]
LEFT JOIN (
    SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [d].[Id] AS [Id0], [d].[Code], [d].[Domain_ID], [d].[FieldInfoId], [d].[IsMulti], [d].[Name] AS [Name0]
    FROM [FieldInfos] AS [f]
    LEFT JOIN [DomainInfos] AS [d] ON [f].[Id] = [d].[FieldInfoId]
) AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id], [s].[Id]
2025-06-12 10:40:17.894 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-12 10:40:34.930 +03:30 [INF] ImportShapefileApply: Source coordinate system is already Web Mercator. No transformation needed.
2025-06-12 10:41:35.188 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 90314.0285ms.
2025-06-12 10:41:35.192 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType2`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-12 10:41:35.194 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web) in 90373.7864ms
2025-06-12 10:41:35.196 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.PublishData (BaseGIS.Web)'
2025-06-12 10:41:35.197 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Database/PublishData - 200 null application/json; charset=utf-8 90387.0224ms
