﻿/* تعریف فونت‌ها */

.appFont {
    font-family: 'AppFont' !important;
    font-weight: normal !important;
}

.appFontBold {
    font-family: 'AppFontBold' !important;
    font-weight: normal !important;
}

@font-face {
    font-family: 'AppFont';
    src: url('../fonts/Shabnam-FD.eot');
    src: url('../fonts/Shabnam-FD.woff') format('woff'), url('../fonts/Shabnam-FD.ttf') format('truetype'), url('../fonts/Shabnam-FD.svg#Shabnam-FD') format('svg'), url('../fonts/Shabnam-FD.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'AppFontBold';
    src: url('../fonts/Shabnam-Bold-FD-FD.eot');
    src: url('../fonts/Shabnam-Bold-FD.woff') format('woff'), url('../fonts/Shabnam-Bold-FD.ttf') format('truetype'), url('../fonts/Shabnam-Bold-FD.svg#Shabnam-Bold-FD') format('svg'), url('../fonts/Shabnam-Bold-FD.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
}


@font-face {
    font-family: 'WYekan';
    src: url('../fonts/YekanWeb-Regular.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'FontAwesome';
    src: url("../fonts/fontawesome-webfont.woff2") format('woff2'), url("../fonts/fontawesome-webfont.woff") format("woff"), url("fontawesome-webfont.ttf") format("truetype");
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-Thin.woff2') format('woff2');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-ExtraLight.woff2') format('woff2');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts//Vazirmatn-Light.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-SemiBold.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-ExtraBold.woff2') format('woff2');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: Vazirmatn;
    src: url('../fonts/Vazirmatn-Black.woff2') format('woff2');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "BYekan";
    src: url("../fonts/BYekan.woff") format("woff");
    font-weight: normal;
    font-style: normal;
}

/* متغیرهای پیش‌فرض */
:root {
    --primary-color: #3b5998;            /* آبی عمیق و حرفه‌ای، مناسب برای دکمه‌ها و عناصر اصلی */
    --primary-hover: #2a4373;
    --secondary-color: #ff6f61;          /* مرجانی گرم، برای ایجاد کنتراست و جلب توجه */
    --secondary-hover: #e65a4e;
    --success-color: #38a169;            /* سبز زمردی ملایم برای پیام‌های موفقیت */
    --success-hover: #2e8555;
    --warning-color: #ed8936;            /* نارنجی ملایم و گرم برای هشدارها */
    --warning-hover: #de7f30;
    --danger-color: #e53e3e;             /* قرمز پررنگ اما متعادل برای خطاها */
    --danger-hover: #c82333;
    --shadow-color: rgba(0, 0, 0, 0.15); /* سایه کمی پررنگ‌تر برای عمق بیشتر */
    --accent-color: #ffffff;             /* سفید خالص برای پس‌زمینه‌های کارت و عناصر برجسته */
    --background-color: #f8fafc;         /* خاکستری بسیار روشن با ته‌رنگ سرد برای پس‌زمینه */
    --text-color: #333355;               /* خاکستری تیره مایل به مشکی برای خوانایی بالا */
    --border-color: #e2e8f0;             /* خاکستری سرد و روشن برای حاشیه‌ها */
    --muted-color: #718096;              /* خاکستری متوسط برای متن‌های کم اهمیت یا غیرفعال */
    --highlight-color: #90cdf4;          /* آبی آسمانی برای حالت‌های هاور یا انتخاب */
    --light-color: 248, 249, 250;
}

/* پالت آبی */
.palette-blue {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --accent-color: #ffffff;
    --background-color: #f0f8ff;
    --border-color: #ced4da;
    --text-color: #212529;
    --muted-color: #6c757d;
    --highlight-color: #17a2b8;
}

/* پالت سبز */
.palette-green {
    --primary-color: #28a745;
    --secondary-color: #17a2b8;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --accent-color: #ffffff;
    --background-color: #f1f8e9;
    --border-color: #ced4da;
    --text-color: #212529;
    --muted-color: #6c757d;
    --highlight-color: #17a2b8;
}

/* پالت قرمز */
.palette-red {
    --primary-color: #dc3545;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --accent-color: #ffffff;
    --background-color: #f8d7da;
    --border-color: #ced4da;
    --text-color: #212529;
    --muted-color: #6c757d;
    --highlight-color: #17a2b8;
}

/* پالت بنفش */
.palette-purple {
    --primary-color: #6f42c1;
    --secondary-color: #17a2b8;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --accent-color: #ffffff;
    --background-color: #f3e5f5;
    --border-color: #ced4da;
    --text-color: #212529;
    --muted-color: #6c757d;
    --highlight-color: #17a2b8;
}

/* پالت تیره (حالت شب) */
.palette-dark {
    --primary-color: #343a40;
    --secondary-color: #495057;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --shadow-color: rgba(0, 0, 0, 0.5);
    --accent-color: #343a40;
    --background-color: #212529;
    --border-color: #495057;
    --text-color: #f8f9fa;
    --muted-color: #adb5bd;
    --highlight-color: #6c757d;
}

/* سایز فونت */
.font-small {
    font-size: 0.875rem;
}

.font-medium {
    font-size: 1rem;
}

.font-large {
    font-size: 1.125rem;
}

/* رنگ فونت */
.font-color-dark {
    --text-color: #444466;
}

.font-color-light {
    --text-color: #ffffff;
}


html, body {
    font-family: 'AppFont';
    font-size: 14px;
}
/* استایل کلی بدنه */
body {
    background-color: var(--background-color);
    color: var(--text-color) !important;
    /*line-height: 1.6;*/
    
}

/* هدر (Navbar) */
#header_main {
    background-color: var(--primary-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    z-index: 1030;
    height: 56px;
}
.header-app .header-item {
    color: var(--accent-color) !important;
    text-decoration: solid;
}
.header-app .header-brand {
    color: var(--accent-color);
    font-family: "AppFontBold";
    font-size: 1.4rem;
}

.header-app .dropdown-item {
    color: var(--primary-color) !important;
}

    .header-app .dropdown-item:hover {
        background-color: var(--secondary-color);
        color: var(--accent-color) !important;
    }

.rounded-circle {
    border: 2px solid var(--accent-color);
}
.header-app .btn-link {
    color: var(--accent-color);
}
/* فاصله‌گذاری بین آیتم‌ها */
.gap-3 {
    gap: 1rem;
}

/* Sidebar */
#sidebar_main {
    position: fixed;
    top: 55px;
    width: 200px;
    height: calc(100% - 60px);
    background-color: var(--primary-color);
    transition: right 0.3s, width 0.3s;
    z-index: 1020;
    font-family: AppFont, BYekan;
}

.sidebar_open #sidebar_main {
    right: 0;
}

.sidebar_mini #sidebar_main {
    width: 80px;
}

.sidebar_mini.sidebar_open #sidebar_main {
    right: 0;
}

.sidebar_content {
    padding: 20px;
}

.sidebar_mini .sidebar_content {
    padding: 10px;
}

.sidebar-nav .nav-item {
    color: #fff;
    position: relative;
    background-color: var(--primary-color);
}

.sidebar-nav .nav-link {
    padding: 10px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    color: #fff;
}

    .sidebar-nav .nav-link:hover, .nav-link.active {
        background-color: var(--secondary-color);
        color: #fff;
    }

.sidebar_mini .submenu {
    display: none;
    position: absolute;
    right: 50px;
    top: 0;
    width: 80px;
    z-index: 1021;
}

.sidebar_mini .nav-item:hover .submenu {
    display: block;
}

.sidebar_mini .nav-link .menu-text {
    display: none;
}

.sidebar_mini .nav-link i {
    margin: 0 auto;
}
/* زیرمنوها */
.sidebar-nav .submenu .nav-link {
    padding: 10px 30px;
    font-size: 1em;
    font-family: AppFont;
}

    .sidebar-nav .submenu .nav-link:hover {
        background-color: #e9ecef;
    }

    .sidebar-nav .submenu .nav-link.active {
        background-color: var(--secondary-color);
        color: var(--accent-color);
    }

/* منوی بازشونده */
.sidebar-nav .collapse {
    background-color: var(--primary-color);
}

.sidebar-nav .nav-link[data-bs-toggle="collapse"] {
    font-family: "AppFontBold";
}

    .sidebar-nav .nav-link[data-bs-toggle="collapse"]::after {
        content: "▼";
        float: right;
        font-size: 0.8em;
        transition: transform 0.3s;
        margin-right: auto;
        margin-left: 5px;
    }

    .sidebar-nav .nav-link[data-bs-toggle="collapse"][aria-expanded="true"]::after {
        transform: rotate(180deg);
    }

/* ریسپانسیو Sidebar */
@media (min-width: 992px) {
    .sidebar {
        position: fixed;
        height: 100vh;
        transform: translateX(0);
    }

        .sidebar.collapsed {
            transform: translateX(-200px);
        }

    main {
        transition: margin-right 0.3s ease;
    }

    .sidebar.collapsed ~ main {
        margin-right: 0;
    }
}

#settings_panel {
    position: fixed;
    top: 55px;
    left: -300px;
    width: 300px;
    height: calc(100% - 60px);
    background-color: #fff;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    transition: left 0.3s;
    z-index: 1020;
}

.settings_open #settings_panel {
    left: 0;
}

.settings_content {
    padding: 1px;
}

#page_content {
    padding: 6px;
    margin-right: 0;
    transition: margin-right 0.3s;
    padding-top:60px;
}

.sidebar_open #page_content {
    margin-right: 200px;
}

.sidebar_mini #page_content {
    margin-right: 80px;
}

/* کارت‌ها (Cards) */
.card {
    margin: 0 auto;
    background-color: var(--accent-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 2px 10px var(--shadow-color);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    animation: fadeIn 0.5s ease-in-out;
}


.card-header {
    color: var(--primary-color);
    border-radius: 8px 8px 0 0;
    padding: 1rem;
    background-color: rgb(from var(--primary-color) r g b/ 5%);
    font-family: AppFontBold, BYekan;
}

.card-body {
    padding: 1.5rem;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* جداول (Tables) */

.table {
    background-color: var(--accent-color);
    border-radius: 8px;
    overflow: hidden;   
}

    .table thead {
        color: var(--accent-color);
    }

        .table thead th {
            color: var(--text-color);
        }

    .table tbody tr {
        transition: background-color 0.2s ease;
    }

        .table tbody tr:hover {
            background-color: var(--highlight-color);
        }

.table-warning {
    background-color: var(--warning-color) !important;
    color: var(--text-color);
}

.table-success {
    background-color: var(--success-color) !important;
    color: var(--accent-color);
}
 
/* دکمه‌ها (Buttons) */
.btn {
    border-radius: 4px;
    padding: 0.4rem 0.6rem;
    transition: all 0.2s ease;
}

/* استایل عمومی دکمه‌ها */
[class*="btn-"] {
    border-radius: 4px;
    padding: 0.4rem 0.6rem;
    box-shadow: 0 2px 5px var(--shadow-color);
}

    [class*="btn-"]:disabled {
        opacity: 0.65;
        cursor: not-allowed;
        transform: none;
    }

/* دکمه‌های اصلی */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--accent-color);
    transition: background-color 0.3s ease, border-color 0.3s ease, transform 0.1s ease;
}

    .btn-primary:hover {
        background-color: var(--primary-hover);
        border-color: var(--primary-hover);
        transform: translateY(-1px);
    }

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--accent-color);
    transition: background-color 0.3s ease, border-color 0.3s ease, transform 0.1s ease;
}

    .btn-secondary:hover {
        background-color: var(--secondary-hover);
        border-color: var(--secondary-hover);
        transform: translateY(-1px);
    }

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: var(--accent-color);
    transition: background-color 0.3s ease, border-color 0.3s ease, transform 0.1s ease;
}

    .btn-success:hover {
        background-color: var(--success-hover);
        border-color: var(--success-hover);
        transform: translateY(-1px);
    }

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: var(--accent-color);
    transition: background-color 0.3s ease, border-color 0.3s ease, transform 0.1s ease;
}

    .btn-danger:hover {
        background-color: var(--danger-hover);
        border-color: var(--danger-hover);
        transform: translateY(-1px);
    }

/* دکمه‌های Outline */
.btn-outline-primary {
    background-color: transparent;
    border-color: var(--primary-color);
    color: var(--primary-color);
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, transform 0.1s ease;
}

    .btn-outline-primary:hover {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: var(--accent-color);
        transform: translateY(-1px);
    }

.btn-outline-secondary {
    background-color: transparent;
    border-color: var(--secondary-color);
    color: var(--secondary-color);
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, transform 0.1s ease;
}

    .btn-outline-secondary:hover {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
        color: var(--accent-color);
        transform: translateY(-1px);
    }

.btn-outline-success {
    background-color: transparent;
    border-color: var(--success-color);
    color: var(--success-color);
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, transform 0.1s ease;
}

    .btn-outline-success:hover {
        background-color: var(--success-color);
        border-color: var(--success-color);
        color: var(--accent-color);
        transform: translateY(-1px);
    }

.btn-outline-danger {
    background-color: transparent;
    border-color: var(--danger-color);
    color: var(--danger-color);
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, transform 0.1s ease;
}

    .btn-outline-danger:hover {
        background-color: var(--danger-color);
        border-color: var(--danger-color);
        color: var(--accent-color);
        transform: translateY(-1px);
    }


/* فرم‌ها (Forms) */
.form-control {
    font-family: "AppFont", sans-serif;
    border-radius: 5px;
    border: 1px solid var(--border-color);
    transition: border-color 0.2s ease;
}

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 5px rgba(44, 62, 80, 0.3);
    }

/* نقشه (Map) */
#inquiryMap {
    border: 1px solid var(--border-color);
    border-radius: 5px;
}

.leaflet-control-layers {
    background-color: var(--accent-color);
    border-radius: 5px;
    box-shadow: 0 2px 5px var(--shadow-color);
    font-family: "AppFont", sans-serif;
}

.leaflet-control-zoom a {
    border-radius: 4px;
    box-shadow: 0 2px 5px var(--shadow-color);
}

.legend {
    background-color: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

    .legend i {
        width: 18px;
        height: 18px;
        float: right;
        margin-left: 8px;
        display: inline-block;
    }

/* Toast */
.toast-container {
    z-index: 1050;
}

.toast {
    max-width: 350px;
    font-family: "AppFont", sans-serif;
    border-radius: 8px;
    box-shadow: 0 4px 10px var(--shadow-color);
    animation: slideIn 0.3s ease;
}

.toast-header {
    border-bottom: 0;
}

.toast-body {
    padding: 15px;
    background-color: var(--accent-color);
    color: var(--text-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
    }

    to {
        transform: translateX(0);
    }
}

/* ریسپانسیو */
@media (max-width: 576px) {
    .btn {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }

    .card {
        margin: 0.5rem;
    }
}

/* استایل Badge */
.badge {
    vertical-align: middle;
    font-size: 0.8em;
    padding: 0.35em 0.65em;
}

.sidebar-nav .nav-link .badge {
    margin-right: 8px;
}

/* استایل‌های اضافی */
.list-inline-item {
    margin-right: 5px;
}

.text-danger {
    font-size: 0.9em;
}

/* استایل‌های تور */
.popover {
    font-family: 'Vazir', sans-serif;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.popover-header {
    background-color: #343a40; /* آبی تیره */
    color: white;
    border-bottom: none;
}

.popover-body {
    background-color: white;
    color: #212529;
}

.swal2-popup {
    font-family: "AppFont", sans-serif;
    direction: rtl;
    text-align: right;
    border-radius: 8px;
    box-shadow: 0 4px 10px var(--shadow-color);
}

.swal2-confirm {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.swal2-cancel {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

/* Dashboard Stats Cards */
.stat-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

    .stat-card:hover {
        transform: translateY(-5px);
    }

.stat-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

    .stat-icon i {
        font-size: 1.5rem;
    }

.stat-label {
    color: var(--muted-color);
    font-size: 0.875rem;
}

.stat-value {
    font-family: AppFont, sans-serif;
    color: var(--text-color);
}

.progress {
    background-color: var(--bs-gray-200);
}

.jsgrid-header-cell {
    color: var(--text-color);
}

.nav-tabs .nav-link {
    color: var(--primary-color);
}
    .nav-tabs .nav-link.active {
        color: var(--secondary-color);
    }

