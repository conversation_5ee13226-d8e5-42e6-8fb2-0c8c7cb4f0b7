using BaseGIS.Core.Entities;
using BaseGIS.Core.Interfaces;
using BaseGIS.Application.DTOs;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BaseGIS.Application.UseCases
{
    public class GetFieldInfoUseCase
    {
        private readonly IFieldInfoRepository _fieldInfoRepository;

        public GetFieldInfoUseCase(IFieldInfoRepository fieldInfoRepository)
        {
            _fieldInfoRepository = fieldInfoRepository;
        }

        public async Task<List<FieldInfoDto>> GetByTableInfoIdAsync(int tableInfoId)
        {
            var fieldInfos = await _fieldInfoRepository.GetByTableInfoIdAsync(tableInfoId);
            return fieldInfos.Select(f => new FieldInfoDto
            {
                Id = f.Id,
                Name = f.Name,
                AliasName = f.AliasName,
                FieldType = f.FieldType,
                FieldLength = f.<PERSON>ength,
                IsRequired = f.IsRequired,
                Editable = f.Editable,
                TableInfoId = f.TableInfoId
            }).ToList();
        }
    }
}