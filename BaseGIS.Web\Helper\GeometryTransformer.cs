﻿namespace BaseGIS.Web.Helper
{

    //public class GeometryTransformer
    //{
    //    public Geometry TransformToUTM(Geometry geometry)
    //    {
    //        if (geometry == null || geometry.IsEmpty)
    //            throw new ArgumentException("هندسه ورودی نمی‌تواند خالی یا null باشد.");

    //        var geometryFactory = geometry.Factory;

    //        // مرحله 1: تشخیص سیستم مختصات هندسه ورودی
    //        ICoordinateSystem sourceCoordSystem;
    //        if (geometry.SRID > 0)
    //        {
    //            // اگر SRID مشخص باشد، سیستم مختصات را از SRID دریافت می‌کنیم
    //            sourceCoordSystem = GetCoordinateSystemFromSRID(geometry.SRID);
    //            if (sourceCoordSystem == null)
    //                throw new InvalidOperationException($"سیستم مختصات با SRID {geometry.SRID} یافت نشد.");
    //        }
    //        else
    //        {
    //            // اگر SRID مشخص نباشد، فرض می‌کنیم WGS84 است
    //            sourceCoordSystem = GeographicCoordinateSystem.WGS84;
    //            geometry.SRID = 4326; // تنظیم SRID به WGS84
    //        }

    //        // مرحله 2: تبدیل به WGS84 (در صورت نیاز)
    //        Geometry wgs84Geometry = geometry;
    //        if (geometry.SRID != 4326) // اگر هندسه در WGS84 نیست
    //        {
    //            var ctf = new CoordinateTransformationFactory();
    //            var transformationToWGS84 = ctf.CreateFromCoordinateSystems(sourceCoordSystem, GeographicCoordinateSystem.WGS84);
    //            wgs84Geometry = TransformGeometry(geometry, transformationToWGS84);
    //            wgs84Geometry.SRID = 4326; // تنظیم SRID به WGS84
    //        }

    //        // مرحله 3: تعیین زون UTM بر اساس مرکز هندسه
    //        var centroid = wgs84Geometry.Centroid;
    //        var longitude = centroid.X;
    //        var zone = (int)Math.Floor((longitude + 180) / 6) + 1;
    //        var isNorthernHemisphere = centroid.Y >= 0;

    //        // تعریف سیستم مختصات UTM
    //        var utmWkt = $"PROJCS[\"WGS 84 / UTM zone {zone}{(isNorthernHemisphere ? "N" : "S")}\"," +
    //                     $"GEOGCS[\"WGS 84\",DATUM[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563]]," +
    //                     $"PRIMEM[\"Greenwich\",0],UNIT[\"degree\",0.0174532925199433]]," +
    //                     $"PROJECTION[\"Transverse_Mercator\"],PARAMETER[\"latitude_of_origin\",0]," +
    //                     $"PARAMETER[\"central_meridian\",{6 * zone - 183}],PARAMETER[\"scale_factor\",0.9996]," +
    //                     $"PARAMETER[\"false_easting\",500000],PARAMETER[\"false_northing\",{(isNorthernHemisphere ? 0 : 10000000)}]," +
    //                     $"UNIT[\"metre\",1]]";

    //        var csFactory = new CoordinateSystemFactory();
    //        var utmCoordSystem = csFactory.CreateFromWkt(utmWkt);

    //        // مرحله 4: تبدیل به UTM
    //        var transformationToUTM = new CoordinateTransformationFactory()
    //            .CreateFromCoordinateSystems(GeographicCoordinateSystem.WGS84, utmCoordSystem);
    //        var utmGeometry = TransformGeometry(wgs84Geometry, transformationToUTM);

    //        // تنظیم SRID برای هندسه خروجی (اختیاری، بسته به نیاز)
    //        utmGeometry.SRID = GetUTMZoneSRID(zone, isNorthernHemisphere);

    //        return utmGeometry;
    //    }

    //    // متد کمکی برای دریافت سیستم مختصات از SRID
    //    private ICoordinateSystem GetCoordinateSystemFromSRID(int srid)
    //    {
    //        // فرض می‌کنیم یک دیکشنری یا سرویس برای نگاشت SRID به WKT وجود دارد
    //        // در اینجا چند نمونه رایج را تعریف می‌کنیم
    //        switch (srid)
    //        {
    //            case 4326: // WGS84
    //                return GeographicCoordinateSystem.WGS84;
    //            case 3857: // Web Mercator
    //                return ProjectedCoordinateSystem.WebMercator;
    //            // زون‌های UTM نمونه
    //            case 32639: // UTM Zone 39N
    //                return CreateUTMCoordinateSystem(39, true);
    //            case 32739: // UTM Zone 39S
    //                return CreateUTMCoordinateSystem(39, false);
    //            default:
    //                // می‌توانید از یک سرویس خارجی یا دیتابیس برای دریافت WKT استفاده کنید
    //                throw new NotSupportedException($"SRID {srid} پشتیبانی نمی‌شود.");
    //        }
    //    }

    //    // متد کمکی برای ایجاد سیستم مختصات UTM
    //    private ICoordinateSystem CreateUTMCoordinateSystem(int zone, bool isNorthernHemisphere)
    //    {
    //        var wkt = $"PROJCS[\"WGS 84 / UTM zone {zone}{(isNorthernHemisphere ? "N" : "S")}\"," +
    //                     $"GEOGCS[\"WGS84\",DATUM\",\"WGS84_Parameters\", \"SPHEROID\",\"WGS84\", \"\",6378136,298.257223563\" \"]," +
    //                     $"PRIMEM[\"Greenwich\",0],UNIT[\"degree\",0.0174532925199433]]," +
    //                     $"PROJECTION[\"Transverse_Mercator\"],PARAMETER[\"latitude_of_origin\",0]," +
    //                     $"PARAMETER[\"central_meridian\",{6 * zone - 183}],PARAMETER[\"scale_factor\",0.9996]," +
    //                     $"PARAMETER[\"false_easting\",500000],PARAMETER[\"false_northing\",{(isNorthernHemisphere ? 0 : 10000000)}]," +
    //                     $"UNIT[\"metre\",1]]";
    //        return new CoordinateSystemFactory().CreateFromWkt(wkt);
    //    }

    //    // متد کمکی برای تبدیل هندسه
    //    private Geometry TransformGeometry(Geometry geometry, ICoordinateTransformation transformation)
    //    {
    //        var mathTransform = transformation.MathTransform;
    //        var clonedGeometry = geometry.Copy();
    //        clonedGeometry.Apply(new MathTransformFilter(mathTransform));
    //        return clonedGeometry;
    //    }

    //    // متد کمکی برای دریافت SRID زون UTM
    //    private int GetUTMZoneSRID(int zone, bool isNorthernHemisphere)
    //    {
    //        // SRIDهای استاندارد EPSG برای زون‌های UTM
    //        // شمالی: EPSG:32601 تا 32660
    //        // جنوبی: EPSG:32701 تا 32760
    //        return (isNorthernHemisphere ? 32601 : 32701) + (zone - 1);
    //    }
    //}
}
