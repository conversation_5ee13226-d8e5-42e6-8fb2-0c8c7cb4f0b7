﻿using NetTopologySuite.Geometries;
using ProjNet.CoordinateSystems;
using ProjNet.CoordinateSystems.Transformations;

namespace BaseGIS.Web.Helper
{
    /// <summary>
    /// کلاس تبدیل هندسه با پشتیبانی از سیستم‌های مختصات مختلف
    /// </summary>
    public class GeometryTransformer
    {
        private readonly ILogger<GeometryTransformer>? _logger;

        public GeometryTransformer(ILogger<GeometryTransformer>? logger = null)
        {
            _logger = logger;
        }

        /// <summary>
        /// متد اصلی برای تبدیل هندسه به UTM - نسخه بهبودیافته
        /// پشتیبانی از سیستم‌های مختصات مختلف
        /// </summary>
        public Geometry TransformToUTM(Geometry geometry, CoordinateSystem sourceCoordSystem)
        {
            if (geometry == null || geometry.IsEmpty)
                throw new ArgumentException("هندسه ورودی نمی‌تواند خالی یا null باشد.");

            try
            {
                // مرحله 1: تشخیص سیستم مختصات هندسه ورودی
                if (sourceCoordSystem == null)
                {
                    sourceCoordSystem = GetSourceCoordinateSystem(geometry);
                }

                // مرحله 2: تبدیل به WGS84 (در صورت نیاز)
                Geometry wgs84Geometry = TransformToWGS84(geometry, sourceCoordSystem);

                // مرحله 3: تعیین زون UTM بر اساس مرکز هندسه
                var centroid = wgs84Geometry.Centroid;
                var longitude = centroid.X;
                var latitude = centroid.Y;

                // بررسی محدوده مختصات جغرافیایی
                if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90)
                {
                    _logger?.LogWarning($"مختصات خارج از محدوده مجاز: Longitude={longitude}, Latitude={latitude}");
                    throw new ArgumentException("مختصات هندسه خارج از محدوده مجاز جغرافیایی است.");
                }

                var zone = (int)Math.Floor((longitude + 180) / 6) + 1;
                var isNorthernHemisphere = latitude >= 0;

                // مرحله 4: ایجاد سیستم مختصات UTM
                var utmCoordSystem = CreateUTMCoordinateSystem(zone, isNorthernHemisphere);

                // مرحله 5: تبدیل به UTM
                var ctf = new CoordinateTransformationFactory();
                var transformation = ctf.CreateFromCoordinateSystems(GeographicCoordinateSystem.WGS84, utmCoordSystem);
                var utmGeometry = TransformGeometry(wgs84Geometry, transformation);

                // تنظیم SRID برای هندسه خروجی
                utmGeometry.SRID = GetUTMZoneSRID(zone, isNorthernHemisphere);

                return utmGeometry;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطا در تبدیل هندسه به UTM");
                throw new InvalidOperationException($"خطا در تبدیل هندسه به UTM: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// متد کمکی برای تبدیل هندسه - نسخه بهبودیافته
        /// </summary>
        private Geometry TransformGeometry(Geometry geometry, ICoordinateTransformation transformation)
        {
            if (geometry == null)
                throw new ArgumentNullException(nameof(geometry));

            if (transformation == null)
                return geometry.Copy();

            try
            {
                var geometryFactory = geometry.Factory;

                return geometry switch
                {
                    Point point => TransformPoint(point, transformation, geometryFactory),
                    LineString lineString => TransformLineString(lineString, transformation, geometryFactory),
                    Polygon polygon => TransformPolygon(polygon, transformation, geometryFactory),
                    MultiPoint multiPoint => TransformMultiPoint(multiPoint, transformation, geometryFactory),
                    MultiLineString multiLineString => TransformMultiLineString(multiLineString, transformation, geometryFactory),
                    MultiPolygon multiPolygon => TransformMultiPolygon(multiPolygon, transformation, geometryFactory),
                    GeometryCollection geometryCollection => TransformGeometryCollection(geometryCollection, transformation, geometryFactory),
                    _ => throw new ArgumentException($"نوع هندسه {geometry.GeometryType} پشتیبانی نمی‌شود.")
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطا در تبدیل هندسه نوع {GeometryType}", geometry.GeometryType);
                throw new InvalidOperationException($"خطا در تبدیل هندسه: {ex.Message}", ex);
            }
        }

        private Point TransformPoint(Point point, ICoordinateTransformation transformation, GeometryFactory factory)
        {
            var coord = TransformCoordinate(point.Coordinate, transformation);
            return factory.CreatePoint(coord);
        }

        private LineString TransformLineString(LineString lineString, ICoordinateTransformation transformation, GeometryFactory factory)
        {
            var coords = lineString.Coordinates.Select(c => TransformCoordinate(c, transformation)).ToArray();
            return factory.CreateLineString(coords);
        }

        private Polygon TransformPolygon(Polygon polygon, ICoordinateTransformation transformation, GeometryFactory factory)
        {
            // تبدیل حلقه خارجی
            var exteriorCoords = polygon.ExteriorRing.Coordinates.Select(c => TransformCoordinate(c, transformation)).ToArray();
            var exteriorRing = factory.CreateLinearRing(exteriorCoords);

            // تبدیل حلقه‌های داخلی (سوراخ‌ها)
            var interiorRings = new LinearRing[polygon.NumInteriorRings];
            for (int i = 0; i < polygon.NumInteriorRings; i++)
            {
                var interiorCoords = polygon.GetInteriorRingN(i).Coordinates.Select(c => TransformCoordinate(c, transformation)).ToArray();
                interiorRings[i] = factory.CreateLinearRing(interiorCoords);
            }

            return factory.CreatePolygon(exteriorRing, interiorRings);
        }

        private MultiPoint TransformMultiPoint(MultiPoint multiPoint, ICoordinateTransformation transformation, GeometryFactory factory)
        {
            var points = new Point[multiPoint.NumGeometries];
            for (int i = 0; i < multiPoint.NumGeometries; i++)
            {
                points[i] = TransformPoint((Point)multiPoint.GetGeometryN(i), transformation, factory);
            }
            return factory.CreateMultiPoint(points);
        }

        private MultiLineString TransformMultiLineString(MultiLineString multiLineString, ICoordinateTransformation transformation, GeometryFactory factory)
        {
            var lineStrings = new LineString[multiLineString.NumGeometries];
            for (int i = 0; i < multiLineString.NumGeometries; i++)
            {
                lineStrings[i] = TransformLineString((LineString)multiLineString.GetGeometryN(i), transformation, factory);
            }
            return factory.CreateMultiLineString(lineStrings);
        }

        private MultiPolygon TransformMultiPolygon(MultiPolygon multiPolygon, ICoordinateTransformation transformation, GeometryFactory factory)
        {
            var polygons = new Polygon[multiPolygon.NumGeometries];
            for (int i = 0; i < multiPolygon.NumGeometries; i++)
            {
                polygons[i] = TransformPolygon((Polygon)multiPolygon.GetGeometryN(i), transformation, factory);
            }
            return factory.CreateMultiPolygon(polygons);
        }

        private GeometryCollection TransformGeometryCollection(GeometryCollection geometryCollection, ICoordinateTransformation transformation, GeometryFactory factory)
        {
            var geometries = new Geometry[geometryCollection.NumGeometries];
            for (int i = 0; i < geometryCollection.NumGeometries; i++)
            {
                geometries[i] = TransformGeometry(geometryCollection.GetGeometryN(i), transformation);
            }
            return factory.CreateGeometryCollection(geometries);
        }

        private Coordinate TransformCoordinate(Coordinate coordinate, ICoordinateTransformation transformation)
        {
            try
            {
                var transformed = transformation.MathTransform.Transform(new[] { coordinate.X, coordinate.Y });
                return new Coordinate(transformed[0], transformed[1]);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطا در تبدیل مختصات ({X}, {Y})", coordinate.X, coordinate.Y);
                // در صورت خطا، مختصات اصلی را برگردان
                return coordinate.Copy();
            }
        }

        // متدهای کمکی برای پشتیبانی از سیستم‌های مختصات مختلف

        /// <summary>
        /// تشخیص سیستم مختصات هندسه ورودی
        /// </summary>
        private CoordinateSystem GetSourceCoordinateSystem(Geometry geometry)
        {
            if (geometry.SRID > 0)
            {
                // اگر SRID مشخص باشد، سیستم مختصات را از SRID دریافت می‌کنیم
                var coordSystem = GetCoordinateSystemFromSRID(geometry.SRID);
                if (coordSystem != null)
                    return coordSystem;

                _logger?.LogWarning($"سیستم مختصات با SRID {geometry.SRID} یافت نشد. از WGS84 استفاده می‌شود.");
            }

            // اگر SRID مشخص نباشد یا یافت نشود، فرض می‌کنیم WGS84 است
            return GeographicCoordinateSystem.WGS84;
        }

        /// <summary>
        /// دریافت سیستم مختصات از SRID
        /// </summary>
        private CoordinateSystem GetCoordinateSystemFromSRID(int srid)
        {
            switch (srid)
            {
                case 4326: // WGS84
                    return GeographicCoordinateSystem.WGS84;
                case 3857: // Web Mercator
                    return ProjectedCoordinateSystem.WebMercator;
                case 4269: // NAD83
                    return CreateNAD83CoordinateSystem();
                case 2154: // RGF93 / Lambert-93 (France)
                    return CreateRGF93CoordinateSystem();
                // زون‌های UTM رایج ایران
                case 32639: // UTM Zone 39N
                    return CreateUTMCoordinateSystem(39, true);
                case 32640: // UTM Zone 40N
                    return CreateUTMCoordinateSystem(40, true);
                case 32641: // UTM Zone 41N
                    return CreateUTMCoordinateSystem(41, true);
                case 32739: // UTM Zone 39S
                    return CreateUTMCoordinateSystem(39, false);
                case 32740: // UTM Zone 40S
                    return CreateUTMCoordinateSystem(40, false);
                default:
                    // برای SRIDهای ناشناخته، تلاش برای ایجاد از WKT استاندارد
                    return TryCreateFromStandardSRID(srid);
            }
        }

        /// <summary>
        /// تبدیل هندسه به WGS84
        /// </summary>
        private Geometry TransformToWGS84(Geometry geometry, CoordinateSystem sourceCoordSystem)
        {
            // اگر هندسه از قبل در WGS84 است
            if (geometry.SRID == 4326 || sourceCoordSystem == GeographicCoordinateSystem.WGS84)
            {
                var result = geometry.Copy();
                result.SRID = 4326;
                return result;
            }

            // تبدیل به WGS84
            var ctf = new CoordinateTransformationFactory();
            var transformation = ctf.CreateFromCoordinateSystems(sourceCoordSystem, GeographicCoordinateSystem.WGS84);
            var wgs84Geometry = TransformGeometry(geometry, transformation);
            wgs84Geometry.SRID = 4326;

            return wgs84Geometry;
        }

        /// <summary>
        /// ایجاد سیستم مختصات UTM
        /// </summary>
        private CoordinateSystem CreateUTMCoordinateSystem(int zone, bool isNorthernHemisphere)
        {
            var utmWkt = $"PROJCS[\"WGS 84 / UTM zone {zone}{(isNorthernHemisphere ? "N" : "S")}\"," +
                         $"GEOGCS[\"WGS 84\",DATUM[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563]]," +
                         $"PRIMEM[\"Greenwich\",0],UNIT[\"degree\",0.0174532925199433]]," +
                         $"PROJECTION[\"Transverse_Mercator\"],PARAMETER[\"latitude_of_origin\",0]," +
                         $"PARAMETER[\"central_meridian\",{6 * zone - 183}],PARAMETER[\"scale_factor\",0.9996]," +
                         $"PARAMETER[\"false_easting\",500000],PARAMETER[\"false_northing\",{(isNorthernHemisphere ? 0 : 10000000)}]," +
                         $"UNIT[\"metre\",1]]";

            var csFactory = new CoordinateSystemFactory();
            return csFactory.CreateFromWkt(utmWkt);
        }

        /// <summary>
        /// دریافت SRID زون UTM
        /// </summary>
        private int GetUTMZoneSRID(int zone, bool isNorthernHemisphere)
        {
            // SRIDهای استاندارد EPSG برای زون‌های UTM
            // شمالی: EPSG:32601 تا 32660
            // جنوبی: EPSG:32701 تا 32760
            return (isNorthernHemisphere ? 32601 : 32701) + (zone - 1);
        }

        /// <summary>
        /// ایجاد سیستم مختصات NAD83
        /// </summary>
        private CoordinateSystem CreateNAD83CoordinateSystem()
        {
            var nad83Wkt = "GEOGCS[\"NAD83\",DATUM[\"North_American_Datum_1983\",SPHEROID[\"GRS 1980\",6378137,298.257222101]]," +
                          "PRIMEM[\"Greenwich\",0],UNIT[\"degree\",0.0174532925199433]]";
            var csFactory = new CoordinateSystemFactory();
            return csFactory.CreateFromWkt(nad83Wkt);
        }

        /// <summary>
        /// ایجاد سیستم مختصات RGF93
        /// </summary>
        private CoordinateSystem CreateRGF93CoordinateSystem()
        {
            var rgf93Wkt = "PROJCS[\"RGF93 / Lambert-93\",GEOGCS[\"RGF93\",DATUM[\"Reseau_Geodesique_Francais_1993\"," +
                          "SPHEROID[\"GRS 1980\",6378137,298.257222101]],PRIMEM[\"Greenwich\",0],UNIT[\"degree\",0.0174532925199433]]," +
                          "PROJECTION[\"Lambert_Conformal_Conic_2SP\"],PARAMETER[\"standard_parallel_1\",49]," +
                          "PARAMETER[\"standard_parallel_2\",44],PARAMETER[\"latitude_of_origin\",46.5]," +
                          "PARAMETER[\"central_meridian\",3],PARAMETER[\"false_easting\",700000]," +
                          "PARAMETER[\"false_northing\",6600000],UNIT[\"metre\",1]]";
            var csFactory = new CoordinateSystemFactory();
            return csFactory.CreateFromWkt(rgf93Wkt);
        }

        /// <summary>
        /// تلاش برای ایجاد سیستم مختصات از SRID استاندارد
        /// </summary>
        private CoordinateSystem TryCreateFromStandardSRID(int srid)
        {
            try
            {
                // اگر SRID در محدوده UTM باشد
                if (srid >= 32601 && srid <= 32660) // UTM North
                {
                    int zone = srid - 32600;
                    return CreateUTMCoordinateSystem(zone, true);
                }
                else if (srid >= 32701 && srid <= 32760) // UTM South
                {
                    int zone = srid - 32700;
                    return CreateUTMCoordinateSystem(zone, false);
                }

                _logger?.LogWarning($"SRID {srid} پشتیبانی نمی‌شود.");
                return null;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"خطا در ایجاد سیستم مختصات برای SRID {srid}");
                return null;
            }
        }
    }
}
