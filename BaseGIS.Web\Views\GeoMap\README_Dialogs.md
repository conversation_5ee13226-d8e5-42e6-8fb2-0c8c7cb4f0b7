# دیالوگ‌های تعاملی نقشه

این سند دیالوگ‌های جدید پیاده‌سازی شده برای ابزارهای اندازه‌گیری و رفتن به مختصات را شرح می‌دهد.

## 🎯 اهداف

1. **دیالوگ اندازه‌گیری** - ابزار کامل اندازه‌گیری نقطه، خط و چندضلعی
2. **دیالوگ GoToXY** - رفتن به مختصات با پشتیبانی از سیستم‌های مختصات مختلف
3. **استفاده از L.Control.Dialog** - دیالوگ‌های تعاملی و قابل جابجایی

## 📋 کتابخانه‌های اضافه شده

### **1. libman.json:**
```json
{
  "provider": "unpkg",
  "library": "leaflet-dialog@1.0.5",
  "destination": "wwwroot/lib/leaflet-dialog/",
  "files": [
    "Leaflet.Dialog.js",
    "Leaflet.Dialog.css"
  ]
},
{
  "provider": "unpkg",
  "library": "leaflet-measure@3.1.0",
  "destination": "wwwroot/lib/leaflet-measure/",
  "files": [
    "leaflet-measure.js",
    "leaflet-measure.css"
  ]
}
```

### **2. Scripts در Index.cshtml:**
```html
<script src="~/lib/leaflet-dialog/Leaflet.Dialog.js"></script>
<script src="~/lib/leaflet-measure/leaflet-measure.js"></script>
```

### **3. Styles در Index.cshtml:**
```html
<link href="~/lib/leaflet-dialog/Leaflet.Dialog.css" rel="stylesheet" />
<link href="~/lib/leaflet-measure/leaflet-measure.css" rel="stylesheet" />
```

## 🔧 دیالوگ اندازه‌گیری

### **ویژگی‌ها:**
- ✅ اندازه‌گیری نقطه (مختصات)
- ✅ اندازه‌گیری خط (طول)
- ✅ اندازه‌گیری چندضلعی (مساحت)
- ✅ انتخاب واحد اندازه‌گیری
- ✅ نمایش نتایج در زمان واقعی
- ✅ پاک کردن اندازه‌گیری‌ها

### **واحدهای پشتیبانی شده:**
| واحد | طول | مساحت |
|------|------|--------|
| **متر** | متر | متر مربع |
| **کیلومتر** | کیلومتر | کیلومتر مربع |
| **فوت** | فوت | فوت مربع |
| **مایل** | مایل | مایل مربع |

### **نحوه استفاده:**
```javascript
// فراخوانی دیالوگ اندازه‌گیری
showMeasurementDialog();

// شروع اندازه‌گیری نقطه
startMeasurement('point');

// شروع اندازه‌گیری خط
startMeasurement('line');

// شروع اندازه‌گیری چندضلعی
startMeasurement('polygon');

// پاک کردن اندازه‌گیری‌ها
clearMeasurements();

// بستن دیالوگ
closeMeasurementDialog();
```

### **مثال کد:**
```javascript
function showMeasurementDialog() {
    var dialogContent = `
        <div class="measurement-dialog">
            <h5 class="mb-3">ابزار اندازه‌گیری</h5>
            <div class="btn-group w-100" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="startMeasurement('point')">
                    <i class="fa fa-circle"></i> نقطه
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="startMeasurement('line')">
                    <i class="fa fa-minus"></i> خط
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="startMeasurement('polygon')">
                    <i class="fa fa-square-o"></i> چندضلعی
                </button>
            </div>
            <!-- سایر المان‌ها -->
        </div>
    `;

    window.measurementDialog = L.control.dialog({
        size: [350, 300],
        anchor: [50, 50],
        position: 'topleft',
        initOpen: true
    })
    .setContent(dialogContent)
    .addTo(map);
}
```

## 🗺️ دیالوگ GoToXY

### **ویژگی‌ها:**
- ✅ پشتیبانی از سیستم‌های مختصات مختلف
- ✅ تبدیل خودکار مختصات
- ✅ انتخاب سطح زوم
- ✅ اضافه کردن نشانگر اختیاری
- ✅ نمایش اطلاعات در popup

### **سیستم‌های مختصات پشتیبانی شده:**
| سیستم | کد | توضیح |
|--------|-----|-------|
| **جغرافیایی** | `geographic` | WGS84 (طول/عرض جغرافیایی) |
| **مرکاتور** | `mercator` | Web Mercator (متر) |
| **UTM 38N** | `utm38` | UTM Zone 38 North |
| **UTM 39N** | `utm39` | UTM Zone 39 North |
| **UTM 40N** | `utm40` | UTM Zone 40 North |
| **UTM 41N** | `utm41` | UTM Zone 41 North |

### **نحوه استفاده:**
```javascript
// فراخوانی دیالوگ GoToXY
togglegotoxy();

// رفتن به مختصات
goToCoordinates();

// تبدیل مختصات
var result = transformCoordinates(x, y, 'utm39');

// تبدیل UTM به WGS84
var wgs84 = transformUTMToWGS84(easting, northing, zone);

// بستن دیالوگ
closeGoToXYDialog();
```

### **مثال‌های تبدیل مختصات:**

#### **1. جغرافیایی (WGS84):**
```javascript
// ورودی: طول=51.3890, عرض=35.6892
var result = transformCoordinates(51.3890, 35.6892, 'geographic');
// خروجی: {lng: 51.3890, lat: 35.6892}
```

#### **2. UTM Zone 39N:**
```javascript
// ورودی: Easting=318499, Northing=3968239
var result = transformCoordinates(318499, 3968239, 'utm39');
// خروجی: {lng: 51.3890, lat: 35.6892} (تقریبی)
```

#### **3. Web Mercator:**
```javascript
// ورودی: X=5718499, Y=4268239
var result = transformCoordinates(5718499, 4268239, 'mercator');
// خروجی: {lng: 51.3890, lat: 35.6892} (تقریبی)
```

### **مثال کد کامل:**
```javascript
function togglegotoxy() {
    var dialogContent = `
        <div class="gotoxy-dialog">
            <h5 class="mb-3">رفتن به مختصات</h5>
            <select class="form-select" id="coordinateSystem">
                <option value="geographic">جغرافیایی (WGS84)</option>
                <option value="mercator">مرکاتور (Web Mercator)</option>
                <option value="utm38">UTM Zone 38N</option>
                <option value="utm39">UTM Zone 39N</option>
                <option value="utm40">UTM Zone 40N</option>
                <option value="utm41">UTM Zone 41N</option>
            </select>
            <input type="number" id="coordinateX" placeholder="51.3890">
            <input type="number" id="coordinateY" placeholder="35.6892">
            <button onclick="goToCoordinates()">رفتن</button>
        </div>
    `;

    window.gotoxyDialog = L.control.dialog({
        size: [400, 350],
        anchor: [100, 50],
        position: 'topleft',
        initOpen: true
    })
    .setContent(dialogContent)
    .addTo(map);
}
```

## 🎨 استایل‌های CSS

### **استایل‌های دیالوگ:**
```css
.measurement-dialog, .gotoxy-dialog {
    font-family: 'Vazir', sans-serif;
    direction: rtl;
    text-align: right;
}

.leaflet-control-dialog {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    overflow: hidden;
}

.leaflet-control-dialog .leaflet-control-dialog-contents {
    padding: 15px;
    background: white;
}
```

### **استایل‌های نشانگر:**
```css
.measurement-marker, .gotoxy-marker {
    background: transparent;
    border: none;
}

.leaflet-div-icon.measurement-marker,
.leaflet-div-icon.gotoxy-marker {
    background: transparent !important;
    border: none !important;
}
```

## 🔧 توابع کلیدی

### **توابع اندازه‌گیری:**
| تابع | عملکرد |
|------|---------|
| `showMeasurementDialog()` | نمایش دیالوگ اندازه‌گیری |
| `startMeasurement(type)` | شروع اندازه‌گیری (point/line/polygon) |
| `startPointMeasurement()` | اندازه‌گیری نقطه |
| `startLineMeasurement(unit)` | اندازه‌گیری خط |
| `startPolygonMeasurement(unit)` | اندازه‌گیری چندضلعی |
| `clearMeasurements()` | پاک کردن اندازه‌گیری‌ها |
| `formatDistance(distance, unit)` | فرمت کردن طول |
| `formatArea(area, unit)` | فرمت کردن مساحت |

### **توابع GoToXY:**
| تابع | عملکرد |
|------|---------|
| `togglegotoxy()` | نمایش دیالوگ GoToXY |
| `updateCoordinateLabels()` | به‌روزرسانی برچسب‌ها |
| `goToCoordinates()` | رفتن به مختصات |
| `transformCoordinates(x, y, system)` | تبدیل مختصات |
| `transformUTMToWGS84(e, n, zone)` | تبدیل UTM به WGS84 |
| `getSystemName(system)` | نام سیستم مختصات |

## 📊 مثال‌های کاربردی

### **1. اندازه‌گیری مساحت ساختمان:**
```javascript
// کاربر روی دکمه اندازه‌گیری کلیک می‌کند
showMeasurementDialog();

// انتخاب چندضلعی و واحد متر مربع
startMeasurement('polygon');

// کاربر نقاط ساختمان را کلیک می‌کند
// نتیجه: "مساحت: 250.75 متر مربع"
```

### **2. رفتن به مختصات UTM:**
```javascript
// کاربر روی دکمه GoToXY کلیک می‌کند
togglegotoxy();

// انتخاب UTM Zone 39N
// ورود Easting: 318499, Northing: 3968239
goToCoordinates();

// نقشه به موقعیت مورد نظر می‌رود و نشانگر نمایش داده می‌شود
```

### **3. اندازه‌گیری مسیر:**
```javascript
// شروع اندازه‌گیری خط
startMeasurement('line');

// کاربر نقاط مسیر را کلیک می‌کند
// نتیجه: "طول کل: 1.25 کیلومتر"
```

## 📝 نکات مهم

### **1. وابستگی‌ها:**
- ✅ Leaflet.Dialog.js باید قبل از استفاده بارگذاری شود
- ✅ proj4.js برای تبدیل مختصات UTM ضروری است
- ✅ FontAwesome برای آیکون‌ها مورد نیاز است

### **2. سازگاری:**
- ✅ سازگار با تمام مرورگرهای مدرن
- ✅ پشتیبانی از دستگاه‌های لمسی
- ✅ Responsive design

### **3. عملکرد:**
- ✅ محاسبات مختصات در سمت کلاینت
- ✅ استفاده از Leaflet native methods
- ✅ بهینه‌سازی برای نقشه‌های بزرگ

### **4. قابلیت توسعه:**
- ✅ امکان اضافه کردن سیستم‌های مختصات جدید
- ✅ قابلیت سفارشی‌سازی ظاهر دیالوگ‌ها
- ✅ پشتیبانی از واحدهای اندازه‌گیری جدید

## 🎉 نتیجه

دیالوگ‌های جدید امکانات زیر را فراهم می‌کنند:
- ✅ ابزار اندازه‌گیری کامل و حرفه‌ای
- ✅ سیستم ناوبری پیشرفته با پشتیبانی از مختصات مختلف
- ✅ رابط کاربری زیبا و کاربرپسند
- ✅ عملکرد بالا و قابلیت اطمینان

این قابلیت‌ها تجربه کاربری را به طور قابل توجهی بهبود می‌بخشند و امکانات حرفه‌ای برای کار با نقشه فراهم می‌کنند.
