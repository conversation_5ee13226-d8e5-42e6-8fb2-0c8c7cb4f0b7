using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BaseGIS.Core.Entities;

namespace BaseGIS.Infrastructure.Persistence.Configurations
{
    public class FeatureVersionConfiguration : IEntityTypeConfiguration<FeatureVersion>
    {
        public void Configure(EntityTypeBuilder<FeatureVersion> builder)
        {
            builder.HasKey(fv => fv.Id);
            builder.Property(fv => fv.GeometryWKT).IsRequired();
            builder.Property(fv => fv.ChangeType).IsRequired();
            builder.Property(fv => fv.DateTime).IsRequired();
        }
    }
} 