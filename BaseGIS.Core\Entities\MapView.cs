﻿using System.ComponentModel.DataAnnotations;

namespace BaseGIS.Core.Entities
{
    public class MapView
    {
        [Key]
        public int ID { get; set; }

        [Required(ErrorMessage = "نام حتما باید وارد شود")]
        [StringLength(100, ErrorMessage = "نام حداکثر می بایست 100 کارکتر باشد")]
        public string Name { get; set; }

        public string Description { get; set; }

        public string Layers { get; set; }

        public string GroupName { get; set; }
        public ApplicationUser User { get; set; }

        public string Date { get; set; }

        public bool Public { get; set; }

        public bool Zoom { get; set; }

        public string BBX { get; set; }

        public string Graphic { get; set; }

        public int? BaseMapID { get; set; }



    }
}
