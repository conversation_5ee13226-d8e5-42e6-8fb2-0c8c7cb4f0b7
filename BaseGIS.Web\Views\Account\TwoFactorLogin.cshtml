﻿@model BaseGIS.Web.ViewModels.TwoFactorLoginViewModel
@{
    ViewData["Title"] = "تأیید دو مرحله‌ای";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}
<div class="container-fluid login-container">
    <div class="row min-vh-100">
        <!-- Left Section -->
        <div class="col-md-6 d-flex align-items-center justify-content-center text-white p-5">
            <div>
                <h4 class="mb-3">
                    <i class="fas fa-cube me-2"></i> LandInquiryApp
                </h4>
                <h1 class="mb-4">تأیید دو مرحله‌ای</h1>
                <p class="lead mb-4">
                    لطفاً کد تأیید ارسال‌شده به ایمیل خود را وارد کنید.
                </p>
                <a href="#" class="text-white text-decoration-none fw-bold">بیشتر بدانید <i class="fas fa-arrow-right ms-2"></i></a>
                <footer class="mt-5 text-white-50 small">
                    1404 © LandInquiryApp
                </footer>
            </div>
        </div>

        <!-- Right Section (2FA Form) -->
        <div class="col-md-6 d-flex align-items-center justify-content-center p-5">
            <div class="card shadow-lg login-card">
                <div class="card-body p-5">
                    <div class="text-end mb-4">
                        <a asp-action="Login" class="text-muted small">بازگشت به ورود</a>
                    </div>
                    <form asp-action="TwoFactorLogin" method="post">
                        <input type="hidden" name="returnUrl" value="@ViewData["ReturnUrl"]" />
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        <div class="mb-4">
                            <label asp-for="TwoFactorCode" class="form-label"></label>
                            <input asp-for="TwoFactorCode" class="form-control" placeholder="کد تأیید 6 رقمی" />
                            <span asp-validation-for="TwoFactorCode" class="text-danger"></span>
                        </div>
                        <div class="mb-4 form-check">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label">مرا به خاطر بسپار</label>
                        </div>
                        <div class="d-flex gap-2">
                            <a asp-action="Login" class="btn btn-secondary">لغو</a>
                            <button type="submit" class="btn btn-primary flex-fill">تأیید کد</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}