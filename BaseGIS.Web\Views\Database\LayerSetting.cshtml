﻿@using BaseGIS.Core.Entities
@{
    string idTbl = Context.Request.Query["id"];
    List<TableInfo> tableInfos = Model;
    var GroupList = tableInfos.Select(x => x.GroupInfo).Distinct().ToList();

    TableInfo tableInfo = new TableInfo();
    if (!string.IsNullOrEmpty(idTbl) && int.TryParse(idTbl, out int id))
    {
        tableInfo = tableInfos.FirstOrDefault(a => a.Id == id) ?? new TableInfo();
    }
}
@section Styles{
    <link href="~/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css" rel="stylesheet">
    <style>
        .fancytree-title a {
            color: #333;
            text-decoration: none;
        }

            .fancytree-title a:hover {
                text-decoration: underline;
            }

        .edit-btn {
            margin-right: 5px;
            color: #007bff;
        }

        .fancytree-node.fancytree-active {
            background-color: cyan;
        }
    </style>

}
<!-- MAIN CONTENT -->
<div >
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6"><h5 class="mb-0">مدیریت ساختار لایه</h5></div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="#">مدیریت داده</a></li>
                        <li class="breadcrumb-item active" aria-current="page">مدیریت ساختار لایه</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->
    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <div class="row">
                <div id="_Layer_List" class="col-sm-12 col-md-12 col-lg-4">
                    <div class="card border-danger mb-3 samanFont" id="wid-id-41">
                        <div class="card-header bg-danger text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <h5 class="card-title samanFont mb-0">فهرست لایه‌ها</h5>
                                    <span class="spinner-border spinner-border-sm text-light d-none ms-2"></span>
                                </div>
                                <div>
                                    <a data-bs-toggle="modal" href="~/database/_layer_managment" data-bs-target="#ModalLayerManagment" class="btn btn-sm btn-outline-light" title="ایجاد لایه جدید"><i class="fa fa-plus"></i></a>
                                    <a data-bs-toggle="modal" href="~/database/_layer_group" data-bs-target="#ModalLayerGroup" class="btn btn-sm btn-outline-light" title="مدیریت گروه‌ها"><i class="fa fa-sitemap"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0" id="layer-content">
                            <div class="p-2 border-bottom">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fa fa-search"></i></span>
                                            <input class="form-control" placeholder="جستجو" type="text" id="FilterTree1">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="custom-scroll tree" id="treeLayer" style="height: 350px; overflow-y: auto;"></div>
                        </div>
                    </div>
                </div>
                <div id="_Layer_Relations" class="col-sm-12 col-md-12 col-lg-8">
                    <!-- محتوای اولیه یا نشانگر لودینگ -->
                    <div class="loading-spinner" style="display: none; text-align: center;">
                        <i class="fa fa-spinner fa-spin"></i> در حال بارگذاری...
                    </div>
                </div>
            </div>
            <div id="_Layer_Fields" class="col-sm-12 col-md-12 col-lg-12">
                <!-- محتوای اولیه یا نشانگر لودینگ -->
                <div class="loading-spinner" style="display: none; text-align: center;">
                    <i class="fa fa-spinner fa-spin"></i> در حال بارگذاری...
                </div>
            </div>
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</div>


<div class="modal samanFont" id="ModalLayerManagment" tabindex="-1" aria-labelledby="ModalLayerManagmentLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content"></div>
    </div>
</div>
<div class="modal samanFont" id="ModalLayerGroup" tabindex="-1" aria-labelledby="ModalLayerGroupLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content"></div>
    </div>
</div>
@section Scripts {

    <script src="~/lib/jquery.fancytree/jquery.fancytree-all.min.js"></script>
    <script type="text/javascript">
                $(document).ready(function () {
                  
                    initializeFancytree();
                    setupModalEvents();

                     // لود اولیه محتوای _Layer_Relations و _Layer_Fields
                    loadLayerRelations('@idTbl');
                    loadLayerFields('@idTbl');

                    // فرض می‌کنیم _Layer_List انتخاب یک لایه را مدیریت می‌کند
                    // این تابع می‌تواند هنگام انتخاب لایه جدید فراخوانی شود
                    window.onLayerSelected = function (layerId) {
                        loadLayerRelations(layerId);
                        loadLayerFields(layerId);
                    };
                });

                function initializeFancytree() {
                    $("#treeLayer").fancytree({
                        extensions: ["filter"],
                        source: [
                            {
                                title: "لایه‌ها",
                                key: "root",
                                folder: true,
                                expanded: true,
                                children: [
        @foreach (var group in GroupList)
        {
            var list = tableInfos.Where(a => a.GroupInfo == group).ToList();
            if (list.Any())
            {
                <text>
                                                            {
                                                                title: "@(group != null ? group.AliasName : "بدون گروه‌بندی")",
                                                                key: "group_@(group?.Id ?? 0)",
                                                                folder: true,
                                                                children: [
                    @foreach (var item in list)
                    {
                        string symol = "fa-table txt-color-yellow";
                        if (item.DatasetType.ToLower() == "point") { symol = "fa-map-marker txt-color-red"; }
                        else if (item.DatasetType.ToLower() == "polyline") { symol = "fa-flash txt-color-green"; }
                        else if (item.DatasetType.ToLower() == "polygon") { symol = "fa-square-o txt-color-blue"; }

                        <text>
                                                                                {
                                                                                    title: "<span><i class='fa @symol'></i> <a href='?id=@item.Id'>@item.AliasName</a></span>" +
                                                                                        "<a data-bs-toggle='modal' href='/database/_layer_managment?id=@item.Id' data-bs-target='#ModalLayerManagment' title='ویرایش' class='edit-btn btn btn-xs'><i class='fa fa-edit'></i></a>",
                                                                                    key: "<EMAIL>",
                                                                                                selected: "@idTbl" === "@item.Id"
                                                                                },
                        </text>
                    }
                                                                ]
                                                            },
                </text>
            }
        }
                                ]
                            }
                        ],
                        filter: {
                            autoApply: true,
                            autoExpand: true,
                            counter: false,
                            fuzzy: false,
                            hideExpandedCounter: true,
                            hideExpanders: false,
                            highlight: true,
                            leavesOnly: false,
                            nodata: "هیچ نتیجه‌ای یافت نشد.",
                            mode: "hide"
                        },
                        escapeTitles: false, // اجازه رندر HTML در عنوان‌ها
                        rtl: true,
                        click: function (event, data) {
                            // جلوگیری از باز شدن گره هنگام کلیک روی لینک ویرایش
                            if ($(event.target).hasClass('edit-btn') || $(event.target).parents('.edit-btn').length) {
                                return false;
                            }
                        }
                    });

                    // جستجو
                    $("#FilterTree1").on("keyup", function () {
                        var filter = $(this).val();
                        $("#treeLayer").fancytree("getTree").filterNodes(filter, { autoExpand: true });
                    });
                }

                function setupModalEvents() {
                    $('a[data-bs-toggle="modal"][data-bs-target="#ModalLayerManagment"]').on('click', function (e) {
                        e.preventDefault();
                        var url = $(this).attr('href');

                        $.ajax({
                            url: url,
                            cache: false,
                            success: function (data) {
                                $("#ModalLayerManagment .modal-content").html(data);
                                var modalElement = document.getElementById('ModalLayerManagment');
                                var modal = new bootstrap.Modal(modalElement);
                                modal.show();

                                modalElement.addEventListener('hidden.bs.modal', function () {
                                    Refresh_Layer_List();
                                    Refresh_Layer_Fields(@(string.IsNullOrEmpty(idTbl) ? "null" : idTbl));
                                    cleanupModalBackdrop();
                                }, { once: true });
                            },
                            error: function (data) {
                                console.error("Error loading modal content:", data);
                            }
                        });
                    });

                    $('a[data-bs-toggle="modal"][data-bs-target="#ModalLayerGroup"]').on('click', function (e) {
                        e.preventDefault();
                        var url = $(this).attr('href');
        debugger;
                        $.ajax({
                            url: url,
                            cache: false,
                            success: function (data) {
                                $("#ModalLayerGroup .modal-content").html(data);
                                var modalElement = document.getElementById('ModalLayerGroup');
                                var modal = new bootstrap.Modal(modalElement);
                                modal.show();

                                modalElement.addEventListener('hidden.bs.modal', function () {
                                    cleanupModalBackdrop();
                                }, { once: true });
                            },
                            error: function (data) {
                                console.error("Error loading modal content:", data);
                            }
                        });
                    });
                }

                function cleanupModalBackdrop() {
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';
                }

                function Refresh_Layer_List() {
                    $("#_Layer_List").load('@Url.Action("_Layer_List", "Database")', function () {
                        initializeFancytree();
                    });
                }
     
       
        function loadLayerRelations(id) {
            if (!id) {
                $("#_Layer_Relations").html("<p>لطفاً یک لایه انتخاب کنید.</p>");
                return;
            }
            $("#_Layer_Relations .loading-spinner").show();
            $.ajax({
                url: '@Url.Action("_Layer_Relations", "DataBase")',
                type: 'GET',
                data: { id: id },
                success: function (data) {
                    $("#_Layer_Relations").html(data);
                },
                error: function (xhr, status, error) {
                    $("#_Layer_Relations").html("<p class='text-danger'>خطا در بارگذاری اطلاعات: " + error + "</p>");
                },
                complete: function () {
                    $("#_Layer_Relations .loading-spinner").hide();
                }
            });
        }

        function loadLayerFields(id) {
            if (!id) {
                $("#_Layer_Fields").html("<p>لطفاً یک لایه انتخاب کنید.</p>");
                return;
            }
            $("#_Layer_Fields .loading-spinner").show();
            $.ajax({
                url: '@Url.Action("_Layer_Fields", "DataBase")',
                type: 'GET',
                data: { id: id },
                success: function (data) {
                    $("#_Layer_Fields").html(data);
                },
                error: function (xhr, status, error) {
                    $("#_Layer_Fields").html("<p class='text-danger'>خطا در بارگذاری اطلاعات: " + error + "</p>");
                },
                complete: function () {
                    $("#_Layer_Fields .loading-spinner").hide();
                }
            });
        }
    </script>
}