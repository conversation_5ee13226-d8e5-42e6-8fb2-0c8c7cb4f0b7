{"version": 3, "file": "overlayscrollbars.mjs", "sources": ["../src/support/cache.ts", "../src/support/compatibility/isBrowser.ts", "../src/support/utils/alias.ts", "../src/support/compatibility/apis.ts", "../src/support/utils/types.ts", "../src/support/utils/animation.ts", "../src/support/utils/array.ts", "../src/support/utils/strings.ts", "../src/support/utils/equal.ts", "../src/support/utils/noop.ts", "../src/support/utils/function.ts", "../src/support/utils/object.ts", "../src/support/utils/math.ts", "../src/support/dom/attribute.ts", "../src/support/dom/class.ts", "../src/support/dom/traversal.ts", "../src/support/dom/manipulation.ts", "../src/trustedTypePolicy.ts", "../src/support/dom/create.ts", "../src/support/dom/style.ts", "../src/support/dom/dimensions.ts", "../src/support/dom/events.ts", "../src/support/dom/scroll.ts", "../src/support/dom/focus.ts", "../src/support/eventListeners.ts", "../src/plugins/plugins.ts", "../src/plugins/optionsValidationPlugin/optionsValidationPlugin.ts", "../src/classnames.ts", "../src/plugins/sizeObserverPlugin/sizeObserverPlugin.ts", "../src/setups/structureSetup/structureSetup.utils.ts", "../src/plugins/scrollbarsHidingPlugin/scrollbarsHidingPlugin.ts", "../src/plugins/clickScrollPlugin/clickScrollPlugin.ts", "../src/options.ts", "../src/nonce.ts", "../src/environment.ts", "../src/observers/domObserver.ts", "../src/observers/sizeObserver.ts", "../src/observers/trinsicObserver.ts", "../src/setups/observersSetup/observersSetup.ts", "../src/initialization.ts", "../src/setups/scrollbarsSetup/scrollbarsSetup.elements.ts", "../src/setups/scrollbarsSetup/scrollbarsSetup.events.ts", "../src/setups/scrollbarsSetup/scrollbarsSetup.ts", "../src/setups/structureSetup/structureSetup.elements.ts", "../src/setups/structureSetup/updateSegments/trinsicUpdateSegment.ts", "../src/setups/structureSetup/updateSegments/paddingUpdateSegment.ts", "../src/setups/structureSetup/updateSegments/overflowUpdateSegment.ts", "../src/setups/structureSetup/structureSetup.ts", "../src/setups/setups.ts", "../src/instances.ts", "../src/overlayscrollbars.ts"], "sourcesContent": ["export interface CacheOptions<Value> {\r\n  // initial value of _value.\r\n  _initialValue: Value;\r\n  // Custom comparison function if shallow compare isn't enough. Returns true if nothing changed.\r\n  _equal?: EqualCachePropFunction<Value>;\r\n  // If true always updates _value and _previous, otherwise they update only when they changed.\r\n  _alwaysUpdateValues?: boolean;\r\n}\r\n\r\nexport type CacheValues<T> = [value: T, changed: boolean, previous?: T];\r\n\r\nexport type EqualCachePropFunction<Value> = (currentVal: Value, newVal: Value) => boolean;\r\n\r\nexport type CacheUpdater<Value> = (current: Value, previous?: Value) => Value;\r\n\r\nexport type UpdateCacheContextual<Value> = (newValue: Value, force?: boolean) => CacheValues<Value>;\r\n\r\nexport type UpdateCache<Value> = (force?: boolean) => CacheValues<Value>;\r\n\r\nexport type GetCurrentCache<Value> = (force?: boolean) => CacheValues<Value>;\r\n\r\nexport type Cache<Value> = [UpdateCache<Value>, GetCurrentCache<Value>];\r\n\r\nexport type CacheContextual<Value> = [UpdateCacheContextual<Value>, GetCurrentCache<Value>];\r\n\r\ntype CreateCache = {\r\n  <Value>(options: CacheOptions<Value>): CacheContextual<Value>;\r\n  <Value>(options: CacheOptions<Value>, update: CacheUpdater<Value>): Cache<Value>;\r\n  <Value>(options: CacheOptions<Value>, update?: CacheUpdater<Value>):\r\n    | CacheContextual<Value>\r\n    | Cache<Value>;\r\n};\r\n\r\nexport const createCache: CreateCache = <Value>(\r\n  options: CacheOptions<Value>,\r\n  update?: CacheUpdater<Value>\r\n): CacheContextual<Value> | Cache<Value> => {\r\n  const { _initialValue, _equal, _alwaysUpdateValues } = options;\r\n  let _value: Value = _initialValue;\r\n  let _previous: Value | undefined;\r\n\r\n  const cacheUpdateContextual: UpdateCacheContextual<Value> = (newValue, force?) => {\r\n    const curr = _value;\r\n\r\n    const newVal = newValue;\r\n    const changed = force || (_equal ? !_equal(curr, newVal) : curr !== newVal);\r\n\r\n    if (changed || _alwaysUpdateValues) {\r\n      _value = newVal;\r\n      _previous = curr;\r\n    }\r\n\r\n    return [_value, changed, _previous];\r\n  };\r\n  const cacheUpdateIsolated: UpdateCache<Value> = (force?) =>\r\n    cacheUpdateContextual(update!(_value, _previous), force);\r\n\r\n  const getCurrentCache: GetCurrentCache<Value> = (force?: boolean) => [\r\n    _value,\r\n    !!force, // changed\r\n    _previous,\r\n  ];\r\n\r\n  return [update ? cacheUpdateIsolated : cacheUpdateContextual, getCurrentCache] as\r\n    | CacheContextual<Value>\r\n    | Cache<Value>;\r\n};\r\n", "export const isBrowser =\r\n  // deno has the global `window` defined\r\n  typeof window !== 'undefined' &&\r\n  // make sure HTML element is available\r\n  typeof HTMLElement !== 'undefined' &&\r\n  // make sure document is defined\r\n  !!window.document;\r\n", "import { isBrowser } from '../compatibility/isBrowser';\r\n\r\nexport const wnd = (isBrowser ? window : {}) as typeof window;\r\nexport const mathMax = Math.max;\r\nexport const mathMin = Math.min;\r\nexport const mathRound = Math.round;\r\nexport const mathFloor = Math.floor;\r\nexport const mathCeil = Math.ceil;\r\nexport const mathAbs = Math.abs;\r\nexport const mathSign = Math.sign;\r\nexport const cAF = wnd.cancelAnimationFrame;\r\nexport const rAF = wnd.requestAnimationFrame;\r\nexport const setT = wnd.setTimeout;\r\nexport const clearT = wnd.clearTimeout;\r\n", "import { wnd } from '../utils/alias';\r\n\r\nconst getApi = <T>(name: string) =>\r\n  (typeof wnd[name as keyof typeof wnd] !== 'undefined'\r\n    ? wnd[name as keyof typeof wnd]\r\n    : undefined) as T;\r\n\r\nexport const MutationObserverConstructor = getApi<typeof MutationObserver>('MutationObserver');\r\nexport const IntersectionObserverConstructor =\r\n  getApi<typeof IntersectionObserver>('IntersectionObserver');\r\nexport const ResizeObserverConstructor = getApi<typeof ResizeObserver>('ResizeObserver');\r\nexport const scrollT = getApi<new (constructor: unknown) => AnimationTimeline>('ScrollTimeline');\r\n", "import type { PlainObject } from '../../typings';\r\n\r\nexport const isUndefined = (obj: any): obj is undefined => obj === undefined;\r\n\r\nexport const isNull = (obj: any): obj is null => obj === null;\r\n\r\nexport const type = (obj: any): string =>\r\n  isUndefined(obj) || isNull(obj)\r\n    ? `${obj}`\r\n    : Object.prototype.toString\r\n        .call(obj)\r\n        .replace(/^\\[object (.+)\\]$/, '$1')\r\n        .toLowerCase();\r\n\r\nexport const isNumber = (obj: any): obj is number => typeof obj === 'number';\r\n\r\nexport const isString = (obj: any): obj is string => typeof obj === 'string';\r\n\r\nexport const isBoolean = (obj: any): obj is boolean => typeof obj === 'boolean';\r\n\r\nexport const isFunction = (obj: any): obj is (...args: any[]) => any => typeof obj === 'function';\r\n\r\nexport const isArray = <T = any>(obj: any): obj is Array<T> => Array.isArray(obj);\r\n\r\nexport const isObject = (obj: any): obj is object =>\r\n  typeof obj === 'object' && !isArray(obj) && !isNull(obj);\r\n\r\n/**\r\n * Returns true if the given object is array like, false otherwise.\r\n * @param obj The Object\r\n */\r\nexport const isArrayLike = <T extends PlainObject = any>(obj: any): obj is ArrayLike<T> => {\r\n  const length = !!obj && obj.length;\r\n  const lengthCorrectFormat = isNumber(length) && length > -1 && length % 1 == 0; // eslint-disable-line eqeqeq\r\n\r\n  return isArray(obj) || (!isFunction(obj) && lengthCorrectFormat)\r\n    ? length > 0 && isObject(obj)\r\n      ? length - 1 in obj\r\n      : true\r\n    : false;\r\n};\r\n\r\n/**\r\n * Returns true if the given object is a \"plain\" (e.g. { key: value }) object, false otherwise.\r\n * @param obj The Object.\r\n */\r\nexport const isPlainObject = <T = any>(obj: any): obj is PlainObject<T> =>\r\n  !!obj && obj.constructor === Object;\r\n\r\n/**\r\n * Checks whether the given object is a HTMLElement.\r\n * @param obj The object which shall be checked.\r\n */\r\nexport const isHTMLElement = (obj: any): obj is HTMLElement => obj instanceof HTMLElement;\r\n\r\n/**\r\n * Checks whether the given object is a Element.\r\n * @param obj The object which shall be checked.\r\n */\r\nexport const isElement = (obj: any): obj is Element => obj instanceof Element;\r\n", "import { mathMax, rAF, cAF } from './alias';\r\nimport { isFunction } from './types';\r\n\r\n/**\r\n * percent: current percent (0 - 1),\r\n * time: current time (duration * percent),\r\n * min: start value\r\n * max: end value\r\n * duration: duration in ms\r\n */\r\nexport type EasingFn = (\r\n  percent: number,\r\n  time: number,\r\n  min: number,\r\n  max: number,\r\n  duration: number\r\n) => number;\r\n\r\nconst animationCurrentTime = () => performance.now();\r\n\r\nexport const animateNumber = (\r\n  from: number,\r\n  to: number,\r\n  duration: number,\r\n  onFrame: (progress: number, percent: number, completed: boolean) => any,\r\n  easing?: EasingFn | false\r\n): ((complete?: boolean) => void) => {\r\n  let animationFrameId = 0;\r\n  const timeStart = animationCurrentTime();\r\n  const finalDuration = mathMax(0, duration);\r\n  const frame = (complete?: boolean) => {\r\n    const timeNow = animationCurrentTime();\r\n    const timeElapsed = timeNow - timeStart;\r\n    const stopAnimation = timeElapsed >= finalDuration;\r\n    const percent = complete\r\n      ? 1\r\n      : 1 - (mathMax(0, timeStart + finalDuration - timeNow) / finalDuration || 0);\r\n    const progress =\r\n      (to - from) *\r\n        (isFunction(easing)\r\n          ? easing(percent, percent * finalDuration, 0, 1, finalDuration)\r\n          : percent) +\r\n      from;\r\n    const animationCompleted = stopAnimation || percent === 1;\r\n\r\n    onFrame && onFrame(progress, percent, animationCompleted);\r\n\r\n    animationFrameId = animationCompleted ? 0 : rAF!(() => frame());\r\n  };\r\n  frame();\r\n  return (complete) => {\r\n    cAF!(animationFrameId);\r\n    complete && frame(complete);\r\n  };\r\n};\r\n", "import type { PlainObject } from '../../typings';\r\nimport { isArray, isArrayLike, isString } from './types';\r\n\r\ntype RunEachItem = ((...args: any) => any | any[]) | false | null | undefined;\r\n\r\nexport function each<T extends Array<unknown> | ReadonlyArray<unknown>>(\r\n  array: T,\r\n  callback: (\r\n    value: T extends Array<infer V> | ReadonlyArray<infer V> ? V : never,\r\n    index: number,\r\n    source: T\r\n  ) => boolean | unknown\r\n): T;\r\nexport function each<T extends ArrayLike<unknown>>(\r\n  arrayLikeObject: T,\r\n  callback: (\r\n    value: T extends ArrayLike<infer V> ? V : never,\r\n    index: number,\r\n    source: T\r\n  ) => boolean | unknown\r\n): T;\r\nexport function each<T extends PlainObject>(\r\n  obj: T,\r\n  callback: (value: any, key: string, source: T) => boolean | unknown\r\n): T;\r\nexport function each(\r\n  source: Array<unknown> | ArrayLike<unknown> | ReadonlyArray<unknown> | PlainObject,\r\n  callback: (value: any, indexOrKey: any, source: any) => boolean | unknown\r\n): Array<unknown> | ArrayLike<unknown> | ReadonlyArray<unknown> | Set<unknown> | PlainObject {\r\n  if (isArrayLike(source)) {\r\n    for (let i = 0; i < source.length; i++) {\r\n      if (callback(source[i], i, source) === false) {\r\n        break;\r\n      }\r\n    }\r\n  } else if (source) {\r\n    // cant use support func keys here due to circular dep\r\n    each(Object.keys(source), (key) => callback(source[key], key, source));\r\n  }\r\n  return source;\r\n}\r\n\r\n/**\r\n * Returns true when the passed item is in the passed array and false otherwise.\r\n * @param arr The array.\r\n * @param item The item.\r\n * @returns Whether the item is in the array.\r\n */\r\nexport const inArray = <T = any>(arr: T[] | readonly T[], item: T): boolean =>\r\n  arr.indexOf(item) >= 0;\r\n\r\n/**\r\n * Concats two arrays and returns an new array without modifying any of the passed arrays.\r\n * @param a Array A.\r\n * @param b Array B.\r\n * @returns A new array which has the entries of both arrays.\r\n */\r\nexport const concat = <T>(a: T[] | ReadonlyArray<T>, b: T[] | ReadonlyArray<T>): T[] => a.concat(b);\r\n\r\n/**\r\n * Pushesh all given items into the given array and returns it.\r\n * @param array The array the items shall be pushed into.\r\n * @param items The items which shall be pushed into the array.\r\n */\r\nexport const push = <T>(array: T[], items: T | ArrayLike<T>, arrayIsSingleItem?: boolean): T[] => {\r\n  !arrayIsSingleItem && !isString(items) && isArrayLike(items)\r\n    ? Array.prototype.push.apply(array, items as T[])\r\n    : array.push(items as T);\r\n  return array;\r\n};\r\n\r\n/**\r\n * Creates a shallow-copied Array instance from an array-like or iterable object.\r\n * @param arr The object from which the array instance shall be created.\r\n */\r\nexport const from = <T = any>(arr?: ArrayLike<T> | Set<T>) => Array.from(arr || []);\r\n\r\n/**\r\n * Creates an array if the passed value is not an array, or returns the value if it is.\r\n * If the passed value is an array like structure and not a string it will be converted into an array.\r\n * @param value The value.\r\n * @returns An array which represents the passed value(s).\r\n */\r\nexport const createOrKeepArray = <T>(value: T | T[] | ArrayLike<T>): T[] => {\r\n  if (isArray(value)) {\r\n    return value;\r\n  }\r\n  return !isString(value) && isArrayLike(value) ? from(value) : [value];\r\n};\r\n\r\n/**\r\n * Check whether the passed array is empty.\r\n * @param array The array which shall be checked.\r\n */\r\nexport const isEmptyArray = (array: any[] | null | undefined): boolean => !!array && !array.length;\r\n\r\n/**\r\n * Deduplicates all items of the array.\r\n * @param array The array to be deduplicated.\r\n * @returns The deduplicated array.\r\n */\r\nexport const deduplicateArray = <T extends any[]>(array: T): T => from(new Set(array)) as T;\r\n\r\n/**\r\n * Calls all functions in the passed array/set of functions.\r\n * @param arr The array filled with function which shall be called.\r\n * @param args The args with which each function is called.\r\n * @param keep True when the Set / array should not be cleared afterwards, false otherwise.\r\n */\r\nexport const runEachAndClear = (arr: RunEachItem[], args?: any[], keep?: boolean): void => {\r\n  // eslint-disable-next-line prefer-spread\r\n  const runFn = (fn: RunEachItem) => (fn ? fn.apply(undefined, args || []) : true); // return true when fn is falsy to not break the loop\r\n  each(arr, runFn);\r\n  !keep && ((arr as any[]).length = 0);\r\n};\r\n", "export const strPaddingTop = 'paddingTop';\r\nexport const strPaddingRight = 'paddingRight';\r\nexport const strPaddingLeft = 'paddingLeft';\r\nexport const strPaddingBottom = 'paddingBottom';\r\nexport const strMarginLeft = 'marginLeft';\r\nexport const strMarginRight = 'marginRight';\r\nexport const strMarginBottom = 'marginBottom';\r\nexport const strOverflowX = 'overflowX';\r\nexport const strOverflowY = 'overflowY';\r\nexport const strWidth = 'width';\r\nexport const strHeight = 'height';\r\nexport const strVisible = 'visible';\r\nexport const strHidden = 'hidden';\r\nexport const strScroll = 'scroll';\r\n\r\nexport const capitalizeFirstLetter = (str: string | number | false | null | undefined): string => {\r\n  const finalStr = String(str || '');\r\n  return finalStr ? finalStr[0].toUpperCase() + finalStr.slice(1) : '';\r\n};\r\n", "import type { WH } from '../dom/dimensions';\r\nimport type { XY } from '../dom/offset';\r\nimport type { TRBL } from '../dom/style';\r\nimport type { PlainObject } from '../../typings';\r\nimport { each } from './array';\r\nimport { mathRound } from './alias';\r\nimport { strHeight, strWidth } from './strings';\r\n\r\n/**\r\n * Compares two objects and returns true if all values of the passed prop names are identical, false otherwise or if one of the two object is falsy.\r\n * @param a Object a.\r\n * @param b Object b.\r\n * @param props The props which shall be compared.\r\n */\r\nexport const equal = <T extends PlainObject>(\r\n  a: T | undefined,\r\n  b: T | undefined,\r\n  props: Array<keyof T> | ReadonlyArray<keyof T>,\r\n  propMutation?: ((value: any) => any) | null | false\r\n): boolean => {\r\n  if (a && b) {\r\n    let result = true;\r\n    each(props, (prop) => {\r\n      const compareA = propMutation ? propMutation(a[prop]) : a[prop];\r\n      const compareB = propMutation ? propMutation(b[prop]) : b[prop];\r\n      if (compareA !== compareB) {\r\n        result = false;\r\n      }\r\n    });\r\n    return result;\r\n  }\r\n  return false;\r\n};\r\n\r\n/**\r\n * Compares object a with object b and returns true if both have the same property values, false otherwise.\r\n * Also returns false if one of the objects is undefined or null.\r\n * @param a Object a.\r\n * @param b Object b.\r\n */\r\nexport const equalWH = <T>(a?: Partial<WH<T>>, b?: Partial<WH<T>>) =>\r\n  equal<Partial<WH<T>>>(a, b, ['w', 'h']);\r\n\r\n/**\r\n * Compares object a with object b and returns true if both have the same property values, false otherwise.\r\n * Also returns false if one of the objects is undefined or null.\r\n * @param a Object a.\r\n * @param b Object b.\r\n */\r\nexport const equalXY = <T>(a?: Partial<XY<T>>, b?: Partial<XY<T>>) =>\r\n  equal<Partial<XY<T>>>(a, b, ['x', 'y']);\r\n\r\n/**\r\n * Compares object a with object b and returns true if both have the same property values, false otherwise.\r\n * Also returns false if one of the objects is undefined or null.\r\n * @param a Object a.\r\n * @param b Object b.\r\n */\r\nexport const equalTRBL = (a?: TRBL, b?: TRBL) => equal<TRBL>(a, b, ['t', 'r', 'b', 'l']);\r\n\r\n/**\r\n * Compares two DOM Rects for their equality of their width and height properties\r\n * Also returns false if one of the DOM Rects is undefined or null.\r\n * @param a DOM Rect a.\r\n * @param b DOM Rect b.\r\n * @param round Whether the values should be rounded.\r\n */\r\nexport const equalBCRWH = (a?: DOMRect, b?: DOMRect, round?: boolean) =>\r\n  equal<DOMRect>(a, b, [strWidth, strHeight], round && ((value) => mathRound(value)));\r\n", "export const noop = () => {}; // eslint-disable-line\r\n", "import { isNumber, isFunction } from './types';\r\nimport { from } from './array';\r\nimport { rAF, cAF, setT, clearT } from './alias';\r\nimport { noop } from './noop';\r\n\r\ntype DebounceTiming = number | false | null | undefined;\r\n\r\nexport interface DebounceOptions<FunctionToDebounce extends (...args: any) => any> {\r\n  /**\r\n   * The timeout for debouncing. If null, no debounce is applied.\r\n   */\r\n  _timeout?: DebounceTiming | (() => DebounceTiming);\r\n  /**\r\n   * A maximum amount of ms. before the function will be called even with debounce.\r\n   */\r\n  _maxDelay?: DebounceTiming | (() => DebounceTiming);\r\n  /**\r\n   * Defines the calling on the leading edge of the timeout.\r\n   */\r\n  _leading?: boolean;\r\n  /**\r\n   * Function which merges parameters for each canceled debounce.\r\n   * If parameters can't be merged the function will return null, otherwise it returns the merged parameters.\r\n   */\r\n  _mergeParams?: (\r\n    prev: Parameters<FunctionToDebounce>,\r\n    curr: Parameters<FunctionToDebounce>\r\n  ) => Parameters<FunctionToDebounce> | false | null | undefined;\r\n}\r\n\r\nexport interface Debounced<FunctionToDebounce extends (...args: any) => any> {\r\n  (...args: Parameters<FunctionToDebounce>): ReturnType<FunctionToDebounce>;\r\n  _flush(): void;\r\n}\r\n\r\nexport const bind = <A extends any[], B extends any[], R>(\r\n  fn: (...args: [...A, ...B]) => R,\r\n  ...args: A\r\n): ((...args: B) => R) => fn.bind(0, ...args);\r\n\r\n/**\r\n * Creates a timeout and cleartimeout tuple. The timeout function always clears the previously created timeout before it runs.\r\n * @param timeout The timeout in ms. If no timeout (or 0) is passed requestAnimationFrame is used instead of setTimeout.\r\n * @returns A tuple with the timeout function as the first value and the clearTimeout function as the second value.\r\n */\r\nexport const selfClearTimeout = (timeout?: number | (() => number)) => {\r\n  let id: number;\r\n  const setTFn = timeout ? setT : rAF!;\r\n  const clearTFn = timeout ? clearT : cAF!;\r\n  return [\r\n    (callback: () => any) => {\r\n      clearTFn(id);\r\n      // @ts-ignore\r\n      id = setTFn(() => callback(), isFunction(timeout) ? timeout() : timeout);\r\n    },\r\n    () => clearTFn(id),\r\n  ] as [timeout: (callback: () => any) => void, clear: () => void];\r\n};\r\n\r\n/**\r\n * Debounces the given function either with a timeout or a animation frame.\r\n * @param functionToDebounce The function which shall be debounced.\r\n * @param options Options for debouncing.\r\n */\r\nexport const debounce = <FunctionToDebounce extends (...args: any) => any>(\r\n  functionToDebounce: FunctionToDebounce,\r\n  options?: DebounceOptions<FunctionToDebounce>\r\n): Debounced<FunctionToDebounce> => {\r\n  const { _timeout, _maxDelay, _leading, _mergeParams } = options || {};\r\n  let maxTimeoutId: number | undefined;\r\n  let prevArguments: Parameters<FunctionToDebounce> | null | undefined;\r\n  let latestArguments: Parameters<FunctionToDebounce> | null | undefined;\r\n  let leadingInvoked: boolean | undefined;\r\n  let clear = noop;\r\n\r\n  const invokeFunctionToDebounce = function (args: Parameters<FunctionToDebounce>) {\r\n    clear();\r\n    clearT(maxTimeoutId);\r\n    leadingInvoked = maxTimeoutId = prevArguments = undefined;\r\n    clear = noop;\r\n    // eslint-disable-next-line\r\n    // @ts-ignore\r\n    functionToDebounce.apply(this, args);\r\n  };\r\n\r\n  const mergeParms = (\r\n    curr: Parameters<FunctionToDebounce>\r\n  ): Parameters<FunctionToDebounce> | false | null | undefined =>\r\n    _mergeParams && prevArguments ? _mergeParams(prevArguments, curr) : curr;\r\n\r\n  const flush = () => {\r\n    /* istanbul ignore next */\r\n    if (clear !== noop) {\r\n      invokeFunctionToDebounce(mergeParms(latestArguments!) || latestArguments!);\r\n    }\r\n  };\r\n\r\n  const debouncedFn = function () {\r\n    // eslint-disable-next-line prefer-rest-params\r\n    const args: Parameters<FunctionToDebounce> = from(arguments) as Parameters<FunctionToDebounce>;\r\n    const finalTimeout = isFunction(_timeout) ? _timeout() : _timeout;\r\n    const hasTimeout = isNumber(finalTimeout) && finalTimeout >= 0;\r\n\r\n    if (hasTimeout) {\r\n      const finalMaxWait = isFunction(_maxDelay) ? _maxDelay() : _maxDelay;\r\n      const hasMaxWait = isNumber(finalMaxWait) && finalMaxWait >= 0;\r\n      const setTimeoutFn = finalTimeout > 0 ? setT : rAF!;\r\n      const clearTimeoutFn = finalTimeout > 0 ? clearT : cAF!;\r\n      const mergeParamsResult = mergeParms(args);\r\n      const invokedArgs = mergeParamsResult || args;\r\n      const boundInvoke = invokeFunctionToDebounce.bind(0, invokedArgs);\r\n      let timeoutId: number | undefined;\r\n\r\n      // if (!mergeParamsResult) {\r\n      //   invokeFunctionToDebounce(prevArguments || args);\r\n      // }\r\n\r\n      clear();\r\n      if (_leading && !leadingInvoked) {\r\n        boundInvoke();\r\n        leadingInvoked = true;\r\n        // @ts-ignore\r\n        timeoutId = setTimeoutFn(() => (leadingInvoked = undefined), finalTimeout);\r\n      } else {\r\n        // @ts-ignore\r\n        timeoutId = setTimeoutFn(boundInvoke, finalTimeout);\r\n\r\n        if (hasMaxWait && !maxTimeoutId) {\r\n          maxTimeoutId = setT(flush, finalMaxWait as number);\r\n        }\r\n      }\r\n\r\n      clear = () => clearTimeoutFn(timeoutId as number);\r\n\r\n      prevArguments = latestArguments = invokedArgs;\r\n    } else {\r\n      invokeFunctionToDebounce(args);\r\n    }\r\n  };\r\n  debouncedFn._flush = flush;\r\n\r\n  return debouncedFn as Debounced<FunctionToDebounce>;\r\n};\r\n", "import type { PlainObject } from '../../typings';\r\nimport { isArray, isFunction, isPlainObject, isNull } from './types';\r\nimport { each } from './array';\r\n\r\n/**\r\n * Determines whether the passed object has a property with the passed name.\r\n * @param obj The object.\r\n * @param prop The name of the property.\r\n */\r\nexport const hasOwnProperty = (obj: any, prop: string | number | symbol): boolean =>\r\n  Object.prototype.hasOwnProperty.call(obj, prop);\r\n\r\n/**\r\n * Returns the names of the enumerable string properties and methods of an object.\r\n * @param obj The object of which the properties shall be returned.\r\n */\r\nexport const keys = (obj: any): Array<string> => (obj ? Object.keys(obj) : []);\r\n\r\ntype AssignDeep = {\r\n  <T, U>(target: T, object1: U): T & U;\r\n  <T, U, V>(target: T, object1: U, object2: V): T & U & V;\r\n  <T, U, V, W>(target: T, object1: U, object2: V, object3: W): T & U & V & W;\r\n  <T, U, V, W, X>(target: T, object1: U, object2: V, object3: W, object4: X): T & U & V & W & X;\r\n  <T, U, V, W, X, Y>(\r\n    target: T,\r\n    object1: U,\r\n    object2: V,\r\n    object3: W,\r\n    object4: X,\r\n    object5: Y\r\n  ): T & U & V & W & X & Y;\r\n  <T, U, V, W, X, Y, Z>(\r\n    target: T,\r\n    object1?: U,\r\n    object2?: V,\r\n    object3?: W,\r\n    object4?: X,\r\n    object5?: Y,\r\n    object6?: Z\r\n  ): T & U & V & W & X & Y & Z;\r\n};\r\n\r\n// https://github.com/jquery/jquery/blob/master/src/core.js#L116\r\nexport const assignDeep: AssignDeep = <T, U, V, W, X, Y, Z>(\r\n  target: T,\r\n  object1?: U,\r\n  object2?: V,\r\n  object3?: W,\r\n  object4?: X,\r\n  object5?: Y,\r\n  object6?: Z\r\n): T & U & V & W & X & Y & Z => {\r\n  const sources: Array<any> = [object1, object2, object3, object4, object5, object6];\r\n\r\n  // Handle case when target is a string or something (possible in deep copy)\r\n  if ((typeof target !== 'object' || isNull(target)) && !isFunction(target)) {\r\n    target = {} as T;\r\n  }\r\n\r\n  each(sources, (source) => {\r\n    // Extend the base object\r\n    each(source, (_, key) => {\r\n      const copy: any = source[key];\r\n\r\n      // Prevent Object.prototype pollution\r\n      // Prevent never-ending loop\r\n      if (target === copy) {\r\n        return true;\r\n      }\r\n\r\n      const copyIsArray = isArray(copy);\r\n\r\n      // Recurse if we're merging plain objects or arrays\r\n      if (copy && isPlainObject(copy)) {\r\n        const src = target[key as keyof T];\r\n        let clone: any = src;\r\n\r\n        // Ensure proper type for the source value\r\n        if (copyIsArray && !isArray(src)) {\r\n          clone = [];\r\n        } else if (!copyIsArray && !isPlainObject(src)) {\r\n          clone = {};\r\n        }\r\n\r\n        // Never move original objects, clone them\r\n        target[key as keyof T] = assignDeep(clone, copy) as any;\r\n      } else {\r\n        target[key as keyof T] = copyIsArray ? copy.slice() : copy;\r\n      }\r\n    });\r\n  });\r\n\r\n  // Return the modified object\r\n  return target as any;\r\n};\r\n\r\nexport const removeUndefinedProperties = <T extends PlainObject>(target: T, deep?: boolean): T =>\r\n  each(assignDeep({}, target), (value, key, copy) => {\r\n    if (value === undefined) {\r\n      delete copy[key];\r\n    } else if (deep && value && isPlainObject(value)) {\r\n      copy[key as keyof typeof copy] = removeUndefinedProperties(value, deep) as any;\r\n    }\r\n  });\r\n\r\n/**\r\n * Returns true if the given object is empty, false otherwise.\r\n * @param obj The Object.\r\n */\r\nexport const isEmptyObject = (obj: any): boolean => !keys(obj).length;\r\n", "import { mathMax, mathMin } from './alias';\r\n\r\n/**\r\n * Caps the passed number between the `min` and `max` bounds.\r\n * @param min The min bound.\r\n * @param max The max bound.\r\n * @param number The number to be capped.\r\n * @returns The capped number between min and max.\r\n */\r\nexport const capNumber = (min: number, max: number, number: number) =>\r\n  mathMax(min, mathMin(max, number));\r\n", "import type { HTMLElementTarget } from './types';\r\nimport { bind, deduplicateArray, each, from, isArray } from '../utils';\r\n\r\nexport type AttributeElementTarget = HTMLElementTarget | Element;\r\n\r\nexport type DomTokens = string | string[] | false | null | undefined | void;\r\n\r\nexport const getDomTokensArray = (tokens: DomTokens) =>\r\n  deduplicateArray((isArray(tokens) ? tokens : (tokens || '').split(' ')).filter((token) => token));\r\n\r\n/**\r\n * Gets a attribute with the given attribute of the given element.\r\n * @param elm The element of which the attribute shall be get.\r\n * @param attrName The attribute name which shall be get.\r\n * @returns The attribute value or `null` when the attribute is not set or `false` if the element is undefined.\r\n */\r\nexport const getAttr = (elm: AttributeElementTarget, attrName: string) =>\r\n  elm && elm.getAttribute(attrName);\r\n\r\n/**\r\n * Returns whether the given attribute exists on the given element.\r\n * @param elm The element.\r\n * @param attrName The attribute.\r\n * @returns A Truthy value indicates a present attrubte.\r\n */\r\nexport const hasAttr = (elm: AttributeElementTarget, attrName: string) =>\r\n  elm && elm.hasAttribute(attrName);\r\n\r\n/**\r\n * Sets the given attributes to the given element.\r\n * @param elm The element of which the attributes shall be removed.\r\n * @param attrName The attribute names separated by a space.\r\n */\r\nexport const setAttrs = (\r\n  elm: AttributeElementTarget,\r\n  attrNames: string | string[],\r\n  value: string | number | false | null | undefined\r\n) => {\r\n  each(getDomTokensArray(attrNames), (attrName) => {\r\n    elm && elm.setAttribute(attrName, String(value || ''));\r\n  });\r\n};\r\n\r\n/**\r\n * Removes the given attributes from the given element.\r\n * @param elm The element of which the attribute shall be removed.\r\n * @param attrName The attribute names separated by a space.\r\n */\r\nexport const removeAttrs = (elm: AttributeElementTarget, attrNames: string | string[]): void => {\r\n  each(getDomTokensArray(attrNames), (attrName) => elm && elm.removeAttribute(attrName));\r\n};\r\n\r\nexport const domTokenListAttr = (elm: AttributeElementTarget, attrName: string) => {\r\n  const initialArr = getDomTokensArray(getAttr(elm, attrName));\r\n  const setElmAttr = bind(setAttrs, elm, attrName);\r\n  const domTokenListOperation = (operationTokens: DomTokens, operation: 'add' | 'delete') => {\r\n    const initialArrSet = new Set(initialArr);\r\n    each(getDomTokensArray(operationTokens), (token) => {\r\n      initialArrSet[operation](token);\r\n    });\r\n    return from(initialArrSet).join(' ');\r\n  };\r\n\r\n  return {\r\n    _remove: (removeTokens: DomTokens) => setElmAttr(domTokenListOperation(removeTokens, 'delete')),\r\n    _add: (addTokens: DomTokens) => setElmAttr(domTokenListOperation(addTokens, 'add')),\r\n    _has: (hasTokens: DomTokens) => {\r\n      const tokenSet = getDomTokensArray(hasTokens);\r\n      return tokenSet.reduce(\r\n        (boolean, token) => boolean && initialArr.includes(token),\r\n        tokenSet.length > 0\r\n      );\r\n    },\r\n  };\r\n};\r\n\r\n/**\r\n * Treats the given attribute like the \"class\" attribute and removes the given value from it.\r\n * @param elm The element.\r\n * @param attrName The attributeName to which the value shall be removed.\r\n * @param value The value which shall be removed.\r\n */\r\nexport const removeAttrClass = (\r\n  elm: AttributeElementTarget,\r\n  attrName: string,\r\n  value: DomTokens\r\n): (() => void) => {\r\n  domTokenListAttr(elm, attrName)._remove(value);\r\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\r\n  return bind(addAttrClass, elm, attrName, value);\r\n};\r\n\r\n/**\r\n * Treats the given attribute like the \"class\" attribute and adds value to it.\r\n * @param elm The element.\r\n * @param attrName The attributeName to which the value shall be added.\r\n * @param value The value which shall be added.\r\n */\r\nexport const addAttrClass = (\r\n  elm: AttributeElementTarget,\r\n  attrName: string,\r\n  value: DomTokens\r\n): (() => void) => {\r\n  domTokenListAttr(elm, attrName)._add(value);\r\n  return bind(removeAttrClass, elm, attrName, value);\r\n};\r\n\r\nexport const addRemoveAttrClass = (\r\n  elm: AttributeElementTarget,\r\n  attrName: string,\r\n  value: DomTokens,\r\n  add?: boolean\r\n) => (add ? addAttrClass : removeAttrClass)(elm, attrName, value);\r\n\r\n/**\r\n * Treats the given attribute like the \"class\" attribute and checks if the given value is in it.\r\n * @param elm The element.\r\n * @param attrName The attributeName from which the content shall be checked.\r\n * @param value The value.\r\n * @returns True if the given attribute has the value in it, false otherwise.\r\n */\r\nexport const hasAttrClass = (\r\n  elm: AttributeElementTarget,\r\n  attrName: string,\r\n  value: DomTokens\r\n): boolean => domTokenListAttr(elm, attrName)._has(value);\r\n", "import type { AttributeElementTarget, DomTokens } from './attribute';\r\nimport { each, from } from '../utils/array';\r\nimport { bind } from '../utils/function';\r\nimport { domTokenListAttr } from './attribute';\r\n\r\nconst createDomTokenListClass = (elm: AttributeElementTarget) => domTokenListAttr(elm, 'class');\r\n\r\n/**\r\n * Check whether the given element has the given class name(s).\r\n * @param elm The element.\r\n * @param className The class name(s).\r\n */\r\nexport const hasClass = (elm: AttributeElementTarget, className: DomTokens): boolean =>\r\n  createDomTokenListClass(elm)._has(className);\r\n\r\n/**\r\n * Removes the given class name(s) from the given element.\r\n * @param elm The element.\r\n * @param className The class name(s) which shall be removed. (separated by spaces)\r\n */\r\nexport const removeClass = (elm: AttributeElementTarget, className: DomTokens): void => {\r\n  createDomTokenListClass(elm)._remove(className);\r\n};\r\n\r\n/**\r\n * Adds the given class name(s) to the given element.\r\n * @param elm The element.\r\n * @param className The class name(s) which shall be added. (separated by spaces)\r\n * @returns A function which removes the added class name(s).\r\n */\r\nexport const addClass = (elm: AttributeElementTarget, className: DomTokens): (() => void) => {\r\n  createDomTokenListClass(elm)._add(className);\r\n  return bind(removeClass, elm, className);\r\n};\r\n\r\n/**\r\n * Takes two className strings, compares them and returns the difference as array.\r\n * @param classNameA ClassName A.\r\n * @param classNameB ClassName B.\r\n */\r\nexport const diffClass = (\r\n  classNameA: string | false | null | undefined,\r\n  classNameB: string | false | null | undefined\r\n) => {\r\n  const set = new Set<string>(classNameA ? classNameA.split(' ') : []);\r\n\r\n  each(classNameB ? classNameB.split(' ') : [], (className) => {\r\n    set.has(className) ? set.delete(className) : set.add(className);\r\n  });\r\n\r\n  return from(set);\r\n};\r\n", "import type { NodeElementTarget } from './types';\r\nimport { isElement } from '../utils/types';\r\nimport { push, from } from '../utils/array';\r\n\r\n/**\r\n * Find all elements with the passed selector, outgoing (and including) the passed element or the document if no element was provided.\r\n * @param selector The selector which has to be searched by.\r\n * @param elm The element from which the search shall be outgoing.\r\n */\r\nexport const find = (selector: string, elm?: NodeElementTarget): Element[] => {\r\n  const rootElm = elm ? isElement(elm) && elm : document;\r\n  return rootElm ? from(rootElm.querySelectorAll(selector)) : [];\r\n};\r\n\r\n/**\r\n * Find the first element with the passed selector, outgoing (and including) the passed element or the document if no element was provided.\r\n * @param selector The selector which has to be searched by.\r\n * @param elm The element from which the search shall be outgoing.\r\n */\r\nexport const findFirst = (selector: string, elm?: NodeElementTarget): NodeElementTarget => {\r\n  const rootElm = elm ? isElement(elm) && elm : document;\r\n  return rootElm && rootElm.querySelector(selector);\r\n};\r\n\r\n/**\r\n * Determines whether the passed element is matching with the passed selector.\r\n * @param elm The element which has to be compared with the passed selector.\r\n * @param selector The selector which has to be compared with the passed element. Additional selectors: ':visible' and ':hidden'.\r\n */\r\nexport const is = (elm: NodeElementTarget, selector: string): boolean =>\r\n  isElement(elm) && elm.matches(selector);\r\n\r\nexport const isBodyElement = (elm: NodeElementTarget) => is(elm, 'body'); // don't do targetElement === ownerDocument.body in case initialization happens in memory\r\n\r\n/**\r\n * Returns the children (no text-nodes or comments) of the passed element which are matching the passed selector. An empty array is returned if the passed element is null.\r\n * @param elm The element of which the children shall be returned.\r\n * @param selector The selector which must match with the children elements.\r\n */\r\nexport const children = (elm: NodeElementTarget, selector?: string): ReadonlyArray<Element> => {\r\n  const childs: Array<Element> = [];\r\n\r\n  return isElement(elm)\r\n    ? push(\r\n        childs,\r\n        from(elm.children).filter((child) => (selector ? is(child, selector) : child))\r\n      )\r\n    : childs;\r\n};\r\n\r\n/**\r\n * Returns the childNodes (incl. text-nodes or comments etc.) of the passed element. An empty array is returned if the passed element is null.\r\n * @param elm The element of which the childNodes shall be returned.\r\n */\r\nexport const contents = (elm: NodeElementTarget): ReadonlyArray<ChildNode> =>\r\n  elm ? from(elm.childNodes) : [];\r\n\r\n/**\r\n * Returns the parent element of the passed element, or null if the passed element is null.\r\n * @param elm The element of which the parent element shall be returned.\r\n */\r\nexport const parent = (elm: NodeElementTarget): NodeElementTarget => elm && elm.parentElement;\r\n\r\n/**\r\n * Returns the closest element to the passed element which matches the given selector.\r\n * @param elm The element.\r\n * @param selector The selector.\r\n * @returns The closest element to the passed element which matches the given selector.\r\n */\r\nexport const closest = (elm: NodeElementTarget, selector: string): NodeElementTarget =>\r\n  isElement(elm) && elm.closest(selector);\r\n\r\n/**\r\n * Gets the focused element of the passed or default document.\r\n * @returns The focused element of the passed document.\r\n */\r\nexport const getFocusedElement = (doc?: Document) => (doc || document).activeElement;\r\n\r\n/**\r\n * Determines whether the given element lies between two selectors in the DOM.\r\n * @param elm The element.\r\n * @param highBoundarySelector The high boundary selector.\r\n * @param deepBoundarySelector The deep boundary selector.\r\n */\r\nexport const liesBetween = (\r\n  elm: NodeElementTarget,\r\n  highBoundarySelector: string,\r\n  deepBoundarySelector: string\r\n): boolean => {\r\n  const closestHighBoundaryElm = closest(elm, highBoundarySelector);\r\n  const closestDeepBoundaryElm = elm && findFirst(deepBoundarySelector, closestHighBoundaryElm);\r\n  const deepBoundaryIsValid =\r\n    closest(closestDeepBoundaryElm, highBoundarySelector) === closestHighBoundaryElm;\r\n\r\n  return closestHighBoundaryElm && closestDeepBoundaryElm\r\n    ? closestHighBoundaryElm === elm ||\r\n        closestDeepBoundaryElm === elm ||\r\n        (deepBoundaryIsValid &&\r\n          closest(closest(elm, deepBoundarySelector), highBoundarySelector) !==\r\n            closestHighBoundaryElm)\r\n    : false;\r\n};\r\n", "import type { NodeElementTarget, NodeElementTargetCollection } from './types';\r\nimport { createOrKeepArray, each } from '../utils/array';\r\nimport { parent } from './traversal';\r\nimport { bind } from '../utils';\r\n\r\n/**\r\n * Removes the given Nodes from their parent.\r\n * @param nodes The Nodes which shall be removed.\r\n */\r\nexport const removeElements = (nodes: NodeElementTargetCollection): void => {\r\n  each(createOrKeepArray(nodes), (node) => {\r\n    const parentElm = parent(node);\r\n    node && parentElm && parentElm.removeChild(node);\r\n  });\r\n};\r\n\r\n/**\r\n * Appends the given children at the end of the given Node.\r\n * @param node The Node to which the children shall be appended.\r\n * @param children The Nodes which shall be appended.\r\n * @returns A function which removes the inserted nodes.\r\n */\r\nexport const appendChildren = (node: NodeElementTarget, children: NodeElementTargetCollection) =>\r\n  bind(\r\n    removeElements,\r\n    node &&\r\n      children &&\r\n      each(createOrKeepArray(children), (child) => {\r\n        child && node.appendChild(child);\r\n      })\r\n  );\r\n", "// at the time of implementation TypeScript doesn't offer any TrustedTypes typescript definitions\r\n// https://github.com/microsoft/TypeScript/issues/30024\r\nlet trustedTypePolicy: unknown | undefined;\r\n\r\nexport const getTrustedTypePolicy = () => trustedTypePolicy;\r\nexport const setTrustedTypePolicy = (newTrustedTypePolicy: unknown | undefined) => {\r\n  trustedTypePolicy = newTrustedTypePolicy;\r\n};\r\n", "import { each } from '../utils/array';\r\nimport { setAttrs } from './attribute';\r\nimport { contents } from './traversal';\r\nimport { removeElements } from './manipulation';\r\nimport { getTrustedTypePolicy } from '../../trustedTypePolicy';\r\n\r\n/**\r\n * Creates a div DOM node.\r\n */\r\nexport const createDiv = (classNames?: string): HTMLDivElement => {\r\n  const div = document.createElement('div');\r\n  setAttrs(div, 'class', classNames);\r\n  return div;\r\n};\r\n\r\n/**\r\n * Creates DOM nodes modeled after the passed html string and returns the root dom nodes as a array.\r\n * @param html The html string after which the DOM nodes shall be created.\r\n */\r\nexport const createDOM = (html: string): ReadonlyArray<Node> => {\r\n  const createdDiv = createDiv();\r\n  const trustedTypesPolicy = getTrustedTypePolicy();\r\n  const trimmedHtml = html.trim();\r\n  createdDiv.innerHTML = trustedTypesPolicy\r\n    ? (trustedTypesPolicy as any).createHTML(trimmedHtml)\r\n    : trimmedHtml;\r\n\r\n  return each(contents(createdDiv), (elm) => removeElements(elm));\r\n};\r\n", "import type { PlainObject, StyleObject, StyleObjectKey, StyleObjectValue } from '../../typings';\r\nimport type { XY } from './offset';\r\nimport type { HTMLElementTarget } from './types';\r\nimport { wnd } from '../utils/alias';\r\nimport { each, from } from '../utils/array';\r\nimport { isString, isNumber, isObject, isNull, isBoolean } from '../utils/types';\r\n\r\nexport interface TRBL {\r\n  t: number;\r\n  r: number;\r\n  b: number;\r\n  l: number;\r\n}\r\n\r\nconst getCSSVal = (computedStyle: CSSStyleDeclaration, prop: StyleObjectKey): string =>\r\n  computedStyle.getPropertyValue(prop) || computedStyle[prop as any] || '';\r\n\r\nconst validFiniteNumber = (number: number) => {\r\n  const notNaN = number || 0;\r\n  return isFinite(notNaN) ? notNaN : 0;\r\n};\r\n\r\nconst parseToZeroOrNumber = (value?: string): number => validFiniteNumber(parseFloat(value || ''));\r\n\r\nexport const roundCssNumber = (value: number) => Math.round(value * 10000) / 10000;\r\n\r\nexport const ratioToCssPercent = (ratio: number) =>\r\n  `${roundCssNumber(validFiniteNumber(ratio) * 100)}%`;\r\n\r\nexport const numberToCssPx = (number: number) => `${roundCssNumber(validFiniteNumber(number))}px`;\r\n\r\nexport function setStyles(\r\n  elm: HTMLElementTarget,\r\n  styles: StyleObject | false | null | undefined\r\n): void {\r\n  elm &&\r\n    styles &&\r\n    each(styles, (rawValue: StyleObjectValue, name) => {\r\n      try {\r\n        const elmStyle = elm.style;\r\n        const value =\r\n          isNull(rawValue) || isBoolean(rawValue)\r\n            ? ''\r\n            : isNumber(rawValue)\r\n              ? numberToCssPx(rawValue)\r\n              : rawValue;\r\n\r\n        if (name.indexOf('--') === 0) {\r\n          elmStyle.setProperty(name, value);\r\n        } else {\r\n          elmStyle[name as any] = value;\r\n        }\r\n      } catch {}\r\n    });\r\n}\r\n\r\nexport function getStyles(\r\n  elm: HTMLElementTarget,\r\n  styles: Array<StyleObjectKey> | ReadonlyArray<StyleObjectKey>,\r\n  pseudoElm?: string | null | undefined\r\n): Partial<Record<StyleObjectKey, string>>;\r\nexport function getStyles(\r\n  elm: HTMLElementTarget,\r\n  styles: StyleObjectKey,\r\n  pseudoElm?: string | null | undefined\r\n): string;\r\nexport function getStyles(\r\n  elm: HTMLElementTarget,\r\n  styles: Array<StyleObjectKey> | ReadonlyArray<StyleObjectKey> | StyleObjectKey,\r\n  pseudoElm?: string | null | undefined\r\n): Partial<Record<StyleObjectKey, string>> | string {\r\n  const getSingleStyle = isString(styles);\r\n  let getStylesResult: string | PlainObject = getSingleStyle ? '' : {};\r\n\r\n  if (elm) {\r\n    const computedStyle = wnd.getComputedStyle(elm, pseudoElm) || elm.style;\r\n    getStylesResult = getSingleStyle\r\n      ? getCSSVal(computedStyle, styles)\r\n      : from(styles).reduce((result, key) => {\r\n          result[key] = getCSSVal(computedStyle, key);\r\n          return result;\r\n        }, getStylesResult as PlainObject);\r\n  }\r\n  return getStylesResult;\r\n}\r\n\r\n/**\r\n * Returns the top right bottom left values of the passed css property.\r\n * @param elm The element of which the values shall be returned.\r\n * @param propertyPrefix The css property prefix. (e.g. \"border\")\r\n * @param propertySuffix The css property suffix. (e.g. \"width\")\r\n */\r\nexport const topRightBottomLeft = (\r\n  elm?: HTMLElementTarget,\r\n  propertyPrefix?: string,\r\n  propertySuffix?: string\r\n): TRBL => {\r\n  const finalPrefix = propertyPrefix ? `${propertyPrefix}-` : '';\r\n  const finalSuffix = propertySuffix ? `-${propertySuffix}` : '';\r\n  const top = `${finalPrefix}top${finalSuffix}` as StyleObjectKey;\r\n  const right = `${finalPrefix}right${finalSuffix}` as StyleObjectKey;\r\n  const bottom = `${finalPrefix}bottom${finalSuffix}` as StyleObjectKey;\r\n  const left = `${finalPrefix}left${finalSuffix}` as StyleObjectKey;\r\n  const result = getStyles(elm, [top, right, bottom, left]);\r\n  return {\r\n    t: parseToZeroOrNumber(result[top]),\r\n    r: parseToZeroOrNumber(result[right]),\r\n    b: parseToZeroOrNumber(result[bottom]),\r\n    l: parseToZeroOrNumber(result[left]),\r\n  };\r\n};\r\n\r\nexport const getTrasformTranslateValue = (\r\n  value: string | number | XY<string | number>,\r\n  isHorizontal?: boolean\r\n) =>\r\n  `translate${\r\n    isObject(value) ? `(${value.x},${value.y})` : `${isHorizontal ? 'X' : 'Y'}(${value})`\r\n  }`;\r\n", "import type { HTMLElementTarget } from './types';\r\nimport { getStyles } from './style';\r\nimport { mathRound, wnd } from '../utils/alias';\r\nimport { bind } from '../utils/function';\r\nimport { strHeight, strWidth } from '../utils/strings';\r\n\r\nexport interface WH<T = number> {\r\n  w: T;\r\n  h: T;\r\n}\r\n\r\nconst elementHasDimensions = (elm: HTMLElement): boolean =>\r\n  !!(elm.offsetWidth || elm.offsetHeight || elm.getClientRects().length);\r\nconst zeroObj: WH = {\r\n  w: 0,\r\n  h: 0,\r\n};\r\n\r\nconst getElmWidthHeightProperty = <E extends HTMLElement | Window>(\r\n  property: E extends HTMLElement ? 'client' | 'offset' | 'scroll' : 'inner',\r\n  elm: E | false | null | undefined\r\n): Readonly<WH> =>\r\n  elm\r\n    ? {\r\n        w: (elm as any)[`${property}Width`],\r\n        h: (elm as any)[`${property}Height`],\r\n      }\r\n    : zeroObj;\r\n\r\n/**\r\n * Returns the window inner- width and height.\r\n */\r\nexport const getWindowSize = (customWnd?: Window): Readonly<WH> =>\r\n  getElmWidthHeightProperty('inner', customWnd || wnd);\r\n\r\n/**\r\n * Returns the scroll- width and height of the passed element. If the element is null the width and height values are 0.\r\n * @param elm The element of which the scroll- width and height shall be returned.\r\n */\r\nexport const getOffsetSize = bind(getElmWidthHeightProperty<HTMLElement>, 'offset') satisfies (\r\n  elm: HTMLElementTarget\r\n) => Readonly<WH>;\r\n\r\n/**\r\n * Returns the client- width and height of the passed element. If the element is null the width and height values are 0.\r\n * @param elm The element of which the client- width and height shall be returned.\r\n */\r\nexport const getClientSize = bind(getElmWidthHeightProperty<HTMLElement>, 'client') satisfies (\r\n  elm: HTMLElementTarget\r\n) => Readonly<WH>;\r\n\r\n/**\r\n * Returns the client- width and height of the passed element. If the element is null the width and height values are 0.\r\n * @param elm The element of which the client- width and height shall be returned.\r\n */\r\nexport const getScrollSize = bind(getElmWidthHeightProperty<HTMLElement>, 'scroll') satisfies (\r\n  elm: HTMLElementTarget\r\n) => Readonly<WH>;\r\n\r\n/**\r\n * Returns the fractional- width and height of the passed element. If the element is null the width and height values are 0.\r\n * @param elm The element of which the fractional- width and height shall be returned.\r\n */\r\nexport const getFractionalSize = (elm: HTMLElementTarget): Readonly<WH> => {\r\n  const cssWidth = parseFloat(getStyles(elm, strWidth)) || 0;\r\n  const cssHeight = parseFloat(getStyles(elm, strHeight)) || 0;\r\n  return {\r\n    w: cssWidth - mathRound(cssWidth),\r\n    h: cssHeight - mathRound(cssHeight),\r\n  };\r\n};\r\n\r\n/**\r\n * Returns the BoundingClientRect of the passed element.\r\n * @param elm The element of which the BoundingClientRect shall be returned.\r\n */\r\nexport const getBoundingClientRect = (elm: HTMLElement): DOMRect => elm.getBoundingClientRect();\r\n\r\n/**\r\n * Determines whether the passed element has any dimensions.\r\n * @param elm The element.\r\n */\r\nexport const hasDimensions = (elm: HTMLElementTarget): boolean =>\r\n  !!elm && elementHasDimensions(elm);\r\n\r\n/**\r\n * Determines whether the passed DOM Rect has any dimensions.\r\n */\r\nexport const domRectHasDimensions = (rect?: DOMRectReadOnly | false | null) =>\r\n  !!(rect && (rect[strHeight] || rect[strWidth]));\r\n\r\n/**\r\n * Determines whether current DOM Rect has appeared according the the previous dom rect..\r\n * @param currContentRect The current DOM Rect.\r\n * @param prevContentRect The previous DOM Rect.\r\n * @returns Whether the dom rect appeared.\r\n */\r\nexport const domRectAppeared = (\r\n  currContentRect: DOMRectReadOnly | false | null | undefined,\r\n  prevContentRect: DOMRectReadOnly | false | null | undefined\r\n) => {\r\n  const rectHasDimensions = domRectHasDimensions(currContentRect);\r\n  const rectHadDimensions = domRectHasDimensions(prevContentRect);\r\n  return !rectHadDimensions && rectHasDimensions;\r\n};\r\n", "import type { DomTokens } from './attribute';\r\nimport { each, runEachAndClear } from '../utils/array';\r\nimport { bind } from '../utils/function';\r\nimport { keys } from '../utils';\r\nimport { getDomTokensArray } from './attribute';\r\n\r\nexport interface EventListenerOptions {\r\n  _capture?: boolean;\r\n  _passive?: boolean;\r\n  _once?: boolean;\r\n}\r\n\r\nexport type EventListenerTarget = EventTarget | false | null | undefined;\r\n\r\nexport type EventListenerMap = {\r\n  [eventNames: string]: ((event: any) => any) | false | null | undefined;\r\n};\r\n\r\n/**\r\n * Removes the passed event listener for the passed event names with the passed options.\r\n * @param target The element from which the listener shall be removed.\r\n * @param eventNames The eventsnames for which the listener shall be removed.\r\n * @param listener The listener which shall be removed.\r\n * @param capture The options of the removed listener.\r\n */\r\nexport const removeEventListener = <T extends Event = Event>(\r\n  target: EventListenerTarget,\r\n  eventNames: DomTokens,\r\n  listener: (event: T) => any,\r\n  capture?: boolean\r\n): void => {\r\n  each(getDomTokensArray(eventNames), (eventName) => {\r\n    target && target.removeEventListener(eventName, listener as EventListener, capture);\r\n  });\r\n};\r\n\r\n/**\r\n * Adds the passed event listener for the passed event names with the passed options.\r\n * @param target The element to which the listener shall be added.\r\n * @param eventNames The eventsnames for which the listener shall be called.\r\n * @param listener The listener which is called on the eventnames.\r\n * @param options The options of the added listener.\r\n */\r\nexport const addEventListener = <T extends Event = Event>(\r\n  target: EventListenerTarget,\r\n  eventNames: DomTokens,\r\n  listener: ((event: T) => any) | false | null | undefined,\r\n  options?: EventListenerOptions\r\n): (() => void) => {\r\n  const passive = (options && options._passive) ?? true;\r\n  const capture = (options && options._capture) || false;\r\n  const once = (options && options._once) || false;\r\n  const nativeOptions: AddEventListenerOptions = {\r\n    passive,\r\n    capture,\r\n  };\r\n\r\n  return bind(\r\n    runEachAndClear,\r\n    getDomTokensArray(eventNames).map((eventName) => {\r\n      const finalListener = (\r\n        once\r\n          ? (evt: T) => {\r\n              removeEventListener(target, eventName, finalListener, capture);\r\n              listener && listener(evt);\r\n            }\r\n          : listener\r\n      ) as EventListener;\r\n\r\n      target && target.addEventListener(eventName, finalListener, nativeOptions);\r\n      return bind(removeEventListener, target, eventName, finalListener, capture);\r\n    })\r\n  );\r\n};\r\n\r\n/**\r\n * Adds the passed event listeners for the passed event names with the passed options.\r\n * @param target The element to which the listener shall be added.\r\n * @param eventListenerMap A map which descirbes the event names and event listeners to be added.\r\n * @param options The options of the added listeners.\r\n */\r\nexport const addEventListeners = (\r\n  target: EventListenerTarget,\r\n  eventListenerMap: EventListenerMap,\r\n  options?: EventListenerOptions\r\n): (() => void) =>\r\n  bind(\r\n    runEachAndClear,\r\n    keys(eventListenerMap).map((eventNames) =>\r\n      addEventListener(target, eventNames, eventListenerMap[eventNames], options)\r\n    )\r\n  );\r\n\r\n/**\r\n * Shorthand for the stopPropagation event Method.\r\n * @param evt The event of which the stopPropagation method shall be called.\r\n */\r\nexport const stopPropagation = (evt: Event): void => evt.stopPropagation();\r\n\r\n/**\r\n * Shorthand for the preventDefault event Method.\r\n * @param evt The event of which the preventDefault method shall be called.\r\n */\r\nexport const preventDefault = (evt: Event): void => evt.preventDefault();\r\n\r\n/**\r\n * Shorthand for the stopPropagation and preventDefault event Method.\r\n * @param evt The event of which the stopPropagation and preventDefault methods shall be called.\r\n */\r\nexport const stopAndPrevent = (evt: Event): void =>\r\n  (stopPropagation(evt) as undefined) || (preventDefault(evt) as undefined);\r\n", "import type { XY } from './offset';\r\nimport type { WH } from './dimensions';\r\nimport { capNumber, isNumber, mathAbs, mathSign } from '../utils';\r\n\r\nexport interface ScrollCoordinates {\r\n  /** The start (origin) scroll coordinates for each axis. */\r\n  _start: XY<number>;\r\n  /** The end scroll coordinates for each axis. */\r\n  _end: XY<number>;\r\n}\r\n\r\n/**\r\n * Scroll the passed element to the passed position.\r\n * @param elm The element to be scrolled.\r\n * @param position The scroll position.\r\n */\r\nexport const scrollElementTo = (\r\n  elm: HTMLElement,\r\n  position: Partial<XY<number | false | null | undefined>> | number | false | null | undefined\r\n): void => {\r\n  const { x, y } = isNumber(position) ? { x: position, y: position } : position || {};\r\n  isNumber(x) && (elm.scrollLeft = x);\r\n  isNumber(y) && (elm.scrollTop = y);\r\n};\r\n\r\n/**\r\n * Scroll the passed element to the passed position.\r\n * @param elm The element to be scrolled.\r\n * @param position The scroll position.\r\n */\r\nexport const getElementScroll = (elm: HTMLElement): Readonly<XY> => ({\r\n  x: elm.scrollLeft,\r\n  y: elm.scrollTop,\r\n});\r\n\r\n/**\r\n * Scroll Coordinates which are 0.\r\n */\r\nexport const getZeroScrollCoordinates = (): ScrollCoordinates => ({\r\n  _start: { x: 0, y: 0 },\r\n  _end: { x: 0, y: 0 },\r\n});\r\n\r\n/**\r\n * Sanatizes raw scroll coordinates.\r\n * The passed `overflowAmount` is used as the \"max\" value for each axis if the sign of the raw max value is not `0`.\r\n * Makes sure that each axis has `0` either in the start or end coordinates.\r\n * @param rawScrollCoordinates The raw scroll coordinates.\r\n * @param overflowAmount The overflow amount.\r\n * @returns\r\n */\r\nexport const sanitizeScrollCoordinates = (\r\n  rawScrollCoordinates: ScrollCoordinates,\r\n  overflowAmount: WH<number>\r\n) => {\r\n  const { _start, _end } = rawScrollCoordinates;\r\n  const { w, h } = overflowAmount;\r\n  const sanitizeAxis = (start: number, end: number, amount: number) => {\r\n    let newStart = mathSign(start) * amount;\r\n    let newEnd = mathSign(end) * amount;\r\n\r\n    if (newStart === newEnd) {\r\n      const startAbs = mathAbs(start);\r\n      const endAbs = mathAbs(end);\r\n\r\n      newEnd = startAbs > endAbs ? 0 : newEnd;\r\n      newStart = startAbs < endAbs ? 0 : newStart;\r\n    }\r\n\r\n    // in doubt set start to 0\r\n    newStart = newStart === newEnd ? 0 : newStart;\r\n\r\n    return [newStart + 0, newEnd + 0] as const; // \"+ 0\" prevents \"-0\" to be in the result\r\n  };\r\n\r\n  const [startX, endX] = sanitizeAxis(_start.x, _end.x, w);\r\n  const [startY, endY] = sanitizeAxis(_start.y, _end.y, h);\r\n\r\n  return {\r\n    _start: {\r\n      x: startX,\r\n      y: startY,\r\n    },\r\n    _end: {\r\n      x: endX,\r\n      y: endY,\r\n    },\r\n  };\r\n};\r\n\r\n/**\r\n * Returns whether the passed scroll coordinates represent the browsers default scroll direction.\r\n * For the default scroll direction the following must be true:\r\n * 1. Start value is `0`.\r\n * 2. End value <= Start value.\r\n * @param scrollCoordinates The scroll coordinates.\r\n */\r\nexport const isDefaultDirectionScrollCoordinates = ({\r\n  _start,\r\n  _end,\r\n}: ScrollCoordinates): XY<boolean> => {\r\n  const getAxis = (start: number, end: number) => start === 0 && start <= end;\r\n\r\n  return {\r\n    x: getAxis(_start.x, _end.x),\r\n    y: getAxis(_start.y, _end.y),\r\n  };\r\n};\r\n\r\n/**\r\n * Gets the current scroll percent between 0..1 for each axis.\r\n * @param scrollCoordinates The scroll coordinates.\r\n * @param currentScroll The current scroll position of the element.\r\n */\r\nexport const getScrollCoordinatesPercent = (\r\n  { _start, _end }: ScrollCoordinates,\r\n  currentScroll: XY<number>\r\n) => {\r\n  const getAxis = (start: number, end: number, current: number) =>\r\n    capNumber(0, 1, (start - current) / (start - end) || 0);\r\n\r\n  return {\r\n    x: getAxis(_start.x, _end.x, currentScroll.x),\r\n    y: getAxis(_start.y, _end.y, currentScroll.y),\r\n  };\r\n};\r\n\r\n/**\r\n * Gets the scroll position of the given percent.\r\n * @param scrollCoordinates The scroll coordinates.\r\n * @param percent The percentage of the scroll.\r\n */\r\nexport const getScrollCoordinatesPosition = (\r\n  { _start, _end }: ScrollCoordinates,\r\n  percent: XY<number>\r\n) => {\r\n  const getAxis = (start: number, end: number, p: number) => start + (end - start) * p;\r\n\r\n  return {\r\n    x: getAxis(_start.x, _end.x, percent.x),\r\n    y: getAxis(_start.y, _end.y, percent.y),\r\n  };\r\n};\r\n", "import type { NodeElementTarget } from './types';\r\n\r\nexport const focusElement = (element: NodeElementTarget) => {\r\n  if (element && (element as HTMLElement).focus) {\r\n    (element as HTMLElement).focus({ preventScroll: true });\r\n  }\r\n};\r\n", "import { isBoolean, isFunction, isString } from './utils/types';\r\nimport { keys } from './utils/object';\r\nimport { each, push, from, isEmptyArray, runEachAndClear, createOrKeepArray } from './utils/array';\r\nimport { bind } from './utils/function';\r\n\r\nexport type EventListener<EventArgs extends Record<string, any[]>, N extends keyof EventArgs> = (\r\n  ...args: EventArgs[N]\r\n) => void;\r\n\r\nexport type EventListeners<EventArgs extends Record<string, any[]>> = {\r\n  [K in keyof EventArgs]?: EventListener<EventArgs, K> | EventListener<EventArgs, K>[] | null;\r\n};\r\n\r\nexport type RemoveEvent<EventArgs extends Record<string, any[]>> = {\r\n  <N extends keyof EventArgs>(name?: N, listener?: EventListener<EventArgs, N>): void;\r\n  <N extends keyof EventArgs>(name?: N, listener?: EventListener<EventArgs, N>[]): void;\r\n  <N extends keyof EventArgs>(\r\n    name?: N,\r\n    listener?: EventListener<EventArgs, N> | EventListener<EventArgs, N>[]\r\n  ): void;\r\n};\r\n\r\nexport type AddEvent<EventArgs extends Record<string, any[]>> = {\r\n  (eventListeners: EventListeners<EventArgs>, pure?: boolean): () => void;\r\n  <N extends keyof EventArgs>(name: N, listener: EventListener<EventArgs, N>): () => void;\r\n  <N extends keyof EventArgs>(name: N, listener: EventListener<EventArgs, N>[]): () => void;\r\n  <N extends keyof EventArgs>(\r\n    nameOrEventListeners: N | EventListeners<EventArgs>,\r\n    listener?: EventListener<EventArgs, N> | EventListener<EventArgs, N>[] | boolean\r\n  ): () => void;\r\n};\r\n\r\nexport type TriggerEvent<EventArgs extends Record<string, any[]>> = {\r\n  <N extends keyof EventArgs>(name: N, args: EventArgs[N]): void;\r\n};\r\n\r\nexport type EventListenerHub<EventArgs extends Record<string, any[]>> = [\r\n  AddEvent<EventArgs>,\r\n  RemoveEvent<EventArgs>,\r\n  TriggerEvent<EventArgs>\r\n];\r\n\r\nconst manageListener = <EventArgs extends Record<string, any[]>, N extends keyof EventArgs>(\r\n  callback: (listener?: EventListener<EventArgs, N>) => void,\r\n  listener?: EventListener<EventArgs, N> | EventListener<EventArgs, N>[]\r\n) => {\r\n  each(createOrKeepArray(listener), callback);\r\n};\r\n\r\nexport const createEventListenerHub = <EventArgs extends Record<string, any[]>>(\r\n  initialEventListeners?: EventListeners<EventArgs>\r\n): EventListenerHub<EventArgs> => {\r\n  const events = new Map<keyof EventArgs, Set<EventListener<EventArgs, keyof EventArgs>>>();\r\n\r\n  const removeEvent: RemoveEvent<EventArgs> = (name, listener) => {\r\n    if (name) {\r\n      const eventSet = events.get(name);\r\n      manageListener((currListener) => {\r\n        if (eventSet) {\r\n          eventSet[currListener ? 'delete' : 'clear'](currListener! as any);\r\n        }\r\n      }, listener);\r\n    } else {\r\n      events.forEach((eventSet) => {\r\n        eventSet.clear();\r\n      });\r\n      events.clear();\r\n    }\r\n  };\r\n\r\n  const addEvent: AddEvent<EventArgs> = (\r\n    nameOrEventListeners: keyof EventArgs | EventListeners<EventArgs>,\r\n    listenerOrPure?:\r\n      | EventListener<EventArgs, keyof EventArgs>\r\n      | EventListener<EventArgs, keyof EventArgs>[]\r\n      | boolean\r\n  ) => {\r\n    if (isString(nameOrEventListeners)) {\r\n      const eventSet = events.get(nameOrEventListeners) || new Set();\r\n      events.set(nameOrEventListeners, eventSet);\r\n\r\n      manageListener((currListener) => {\r\n        isFunction(currListener) && eventSet.add(currListener);\r\n      }, listenerOrPure as Exclude<typeof listenerOrPure, boolean>);\r\n\r\n      return bind(\r\n        removeEvent,\r\n        nameOrEventListeners,\r\n        listenerOrPure as Exclude<typeof listenerOrPure, boolean>\r\n      );\r\n    }\r\n    if (isBoolean(listenerOrPure) && listenerOrPure) {\r\n      removeEvent();\r\n    }\r\n\r\n    const eventListenerKeys = keys(nameOrEventListeners) as (keyof EventListeners<EventArgs>)[];\r\n    const offFns: (() => void)[] = [];\r\n    each(eventListenerKeys, (key) => {\r\n      const eventListener = (nameOrEventListeners as EventListeners<EventArgs>)[key];\r\n      eventListener && push(offFns, addEvent(key, eventListener));\r\n    });\r\n\r\n    return bind(runEachAndClear, offFns);\r\n  };\r\n\r\n  const triggerEvent: TriggerEvent<EventArgs> = (name, args) => {\r\n    each(from(events.get(name)), (event) => {\r\n      if (args && !isEmptyArray(args)) {\r\n        (event as (...eventArgs: EventArgs[keyof EventArgs]) => void).apply(0, args);\r\n      } else {\r\n        (event as () => void)();\r\n      }\r\n    });\r\n  };\r\n\r\n  addEvent(initialEventListeners || {});\r\n\r\n  return [addEvent, removeEvent, triggerEvent];\r\n};\r\n", "import type { OverlayScrollbars, OverlayScrollbarsStatic } from '../overlayscrollbars';\r\nimport type { EventListener, EventListenerArgs, EventListeners } from '../eventListeners';\r\nimport { each, keys } from '../support';\r\n\r\nexport type PluginModuleInstance = Record<string | number | symbol, any>;\r\n\r\nexport type InstancePluginEvent = {\r\n  /**\r\n   * Adds event listeners to the instance.\r\n   * @param eventListeners An object which contains the added listeners.\r\n   * @returns Returns a function which removes the added listeners.\r\n   */\r\n  (eventListeners: EventListeners): () => void;\r\n  /**\r\n   * Adds a single event listener to the instance.\r\n   * @param name The name of the event.\r\n   * @param listener The listener which is invoked on that event.\r\n   * @returns Returns a function which removes the added listeners.\r\n   */\r\n  <N extends keyof EventListenerArgs>(name: N, listener: EventListener<N>): () => void;\r\n  /**\r\n   * Adds multiple event listeners to the instance.\r\n   * @param name The name of the event.\r\n   * @param listener The listeners which are invoked on that event.\r\n   * @returns Returns a function which removes the added listeners.\r\n   */\r\n  <N extends keyof EventListenerArgs>(name: N, listener: EventListener<N>[]): () => void;\r\n};\r\n\r\n/**\r\n * Describes a OverlayScrollbars plugin module.\r\n * Plugin modules must be side-effect free and deterministic. (same input produces same output)\r\n */\r\nexport type PluginModule<\r\n  S extends PluginModuleInstance | void = PluginModuleInstance | void,\r\n  I extends PluginModuleInstance | void = PluginModuleInstance | void,\r\n> = (S extends PluginModuleInstance\r\n  ? {\r\n      /**\r\n       * Creates a plugin which is bound to the static object.\r\n       * The function will be called once with the static object as soon as the plugin is registered.\r\n       * The plugin can add new methods or fields to the passed static object.\r\n       * @param osStatic The static object the plugin is bound to.\r\n       * @returns The plugins instance object or a falsy value if the plugin doesn't need any instance object.\r\n       */\r\n      static: (osStatic: OverlayScrollbarsStatic) => S | void;\r\n    }\r\n  : object) &\r\n  (I extends PluginModuleInstance\r\n    ? {\r\n        /**\r\n         * Creates a A plugin which is bound to an instance.\r\n         * The function will be called each time a new instance is created.\r\n         * The plugin can add new methods or fields to the passed instance object.\r\n         * @param osInstance The instance object the plugin is bound to.\r\n         * @param event A function which adds events to the instance which can't be removed from outside the plugin. (instance events added with the `on` function can be removed with the optional `pure` parameter)\r\n         * @param osStatic The static object the plugin is bound to.\r\n         * @returns The plugins instance object or a falsy value if the plugin doesn't need any instance object.\r\n         */\r\n        instance: (\r\n          osInstance: OverlayScrollbars,\r\n          event: InstancePluginEvent,\r\n          osStatic: OverlayScrollbarsStatic\r\n        ) => I | void;\r\n      }\r\n    : object);\r\n\r\n/**\r\n * Describes a OverlayScrollbar plugin.\r\n */\r\nexport type Plugin<\r\n  Name extends string = string,\r\n  S extends PluginModuleInstance | void = PluginModuleInstance | void,\r\n  I extends PluginModuleInstance | void = PluginModuleInstance | void,\r\n> = {\r\n  /** The field is the plugins name. Plugin names must be globally unique, please choose wisely. */\r\n  [pluginName in Name]: PluginModule<S, I>;\r\n};\r\n\r\n/**\r\n * Describes a OverlayScrollbar plugin which has only a static module.\r\n */\r\nexport type StaticPlugin<\r\n  Name extends string = string,\r\n  T extends PluginModuleInstance = PluginModuleInstance,\r\n> = Plugin<Name, T, void>;\r\n\r\n/**\r\n * Describes a OverlayScrollbar plugin which has only a instance module.\r\n */\r\nexport type InstancePlugin<\r\n  Name extends string = string,\r\n  T extends PluginModuleInstance = PluginModuleInstance,\r\n> = Plugin<Name, void, T>;\r\n\r\n/**\r\n * Infers the type of the static modules instance of the passed plugin.\r\n */\r\nexport type InferStaticPluginModuleInstance<T extends StaticPlugin> =\r\n  T extends StaticPlugin<infer Name>\r\n    ? T[Name]['static'] extends (...args: any[]) => any\r\n      ? ReturnType<T[Name]['static']>\r\n      : void\r\n    : void;\r\n\r\n/**\r\n * Infers the type of the instance modules instance of the passed plugin.\r\n */\r\nexport type InferInstancePluginModuleInstance<T extends InstancePlugin> =\r\n  T extends InstancePlugin<infer Name>\r\n    ? T[Name]['instance'] extends (...args: any[]) => any\r\n      ? ReturnType<T[Name]['instance']>\r\n      : void\r\n    : void;\r\n\r\n/** All registered plugin modules. */\r\nexport const pluginModules: Record<string, PluginModule> = {};\r\n\r\n/** All static plugin module instances. */\r\nexport const staticPluginModuleInstances: Record<string, PluginModuleInstance | void> = {};\r\n\r\n/**\r\n * Adds plugins.\r\n * @param addedPlugin The plugin(s) to add.\r\n * @returns The added plugin modules of the registered plugins.\r\n */\r\nexport const addPlugins = (addedPlugin: Plugin[]) => {\r\n  each(addedPlugin, (plugin) =>\r\n    each(plugin, (_, key) => {\r\n      pluginModules[key] = plugin[key];\r\n    })\r\n  );\r\n};\r\n\r\nexport const registerPluginModuleInstances = (\r\n  plugin: Plugin,\r\n  staticObj: OverlayScrollbarsStatic,\r\n  instanceInfo?: [\r\n    instanceObj: OverlayScrollbars,\r\n    event: InstancePluginEvent,\r\n    instancePluginMap?: Record<string, PluginModuleInstance>,\r\n  ]\r\n): Array<PluginModuleInstance | void> =>\r\n  keys(plugin).map((name) => {\r\n    const { static: osStatic, instance: osInstance } = (\r\n      plugin as Plugin<string, PluginModuleInstance, PluginModuleInstance>\r\n    )[name];\r\n    const [instanceObj, event, instancePluginMap] = instanceInfo || [];\r\n    const ctor = instanceInfo ? osInstance : osStatic;\r\n    if (ctor) {\r\n      const instance = instanceInfo\r\n        ? (\r\n            ctor as Extract<\r\n              typeof ctor,\r\n              (\r\n                osInstance: OverlayScrollbars,\r\n                event: InstancePluginEvent,\r\n                osStatic: OverlayScrollbarsStatic\r\n              ) => PluginModuleInstance | void\r\n            >\r\n          )(instanceObj!, event!, staticObj)\r\n        : (\r\n            ctor as Extract<\r\n              typeof ctor,\r\n              (osStatic: OverlayScrollbarsStatic) => PluginModuleInstance | void\r\n            >\r\n          )(staticObj);\r\n      return ((instancePluginMap || staticPluginModuleInstances)[name] = instance);\r\n    }\r\n  });\r\n\r\nexport const getStaticPluginModuleInstance = <T extends StaticPlugin>(\r\n  pluginModuleName: T extends StaticPlugin<infer N> ? N : never\r\n): InferStaticPluginModuleInstance<T> | undefined =>\r\n  staticPluginModuleInstances[pluginModuleName] as InferStaticPluginModuleInstance<T> | undefined;\r\n", "import type {\r\n  Options,\r\n  PartialOptions,\r\n  OverflowBehavior,\r\n  ScrollbarsVisibilityBehavior,\r\n  ScrollbarsAutoHideBehavior,\r\n  ScrollbarsClickScrollBehavior,\r\n} from '../../options';\r\nimport type { OptionsTemplate, OptionsTemplateValue } from './validation';\r\nimport type { StaticPlugin } from '../plugins';\r\nimport { validateOptions, optionsTemplateTypes as oTypes } from './validation';\r\n\r\nexport const optionsValidationPluginModuleName = '__osOptionsValidationPlugin';\r\n\r\nexport const OptionsValidationPlugin = /* @__PURE__ */ (() => ({\r\n  [optionsValidationPluginModuleName]: {\r\n    static: () => {\r\n      const numberAllowedValues: OptionsTemplateValue<number> = oTypes.number;\r\n      const booleanAllowedValues: OptionsTemplateValue<boolean> = oTypes.boolean;\r\n      const arrayNullValues: OptionsTemplateValue<Array<unknown> | null> = [\r\n        oTypes.array,\r\n        oTypes.null,\r\n      ];\r\n      const overflowAllowedValues: OptionsTemplateValue<OverflowBehavior> =\r\n        'hidden scroll visible visible-hidden';\r\n      const scrollbarsVisibilityAllowedValues: OptionsTemplateValue<ScrollbarsVisibilityBehavior> =\r\n        'visible hidden auto';\r\n      const scrollbarsAutoHideAllowedValues: OptionsTemplateValue<ScrollbarsAutoHideBehavior> =\r\n        'never scroll leavemove';\r\n      const scrollbarsClickScrollAllowedValues: OptionsTemplateValue<ScrollbarsClickScrollBehavior> =\r\n        [booleanAllowedValues, oTypes.string];\r\n\r\n      const optionsTemplate: OptionsTemplate<Options> = {\r\n        paddingAbsolute: booleanAllowedValues, // true || false\r\n        showNativeOverlaidScrollbars: booleanAllowedValues, // true || false\r\n        update: {\r\n          elementEvents: arrayNullValues, // array of tuples || null\r\n          attributes: arrayNullValues,\r\n          debounce: [oTypes.number, oTypes.array, oTypes.null], // number || number array || null\r\n          ignoreMutation: [oTypes.function, oTypes.null], // function || null\r\n        },\r\n        overflow: {\r\n          x: overflowAllowedValues, // visible-hidden  || visible-scroll || hidden || scroll\r\n          y: overflowAllowedValues, // visible-hidden  || visible-scroll || hidden || scroll\r\n        },\r\n        scrollbars: {\r\n          theme: [oTypes.string, oTypes.null], // string || null\r\n          visibility: scrollbarsVisibilityAllowedValues, // visible || hidden || auto\r\n          autoHide: scrollbarsAutoHideAllowedValues, // never || scroll || leave || move ||\r\n          autoHideDelay: numberAllowedValues, // number\r\n          autoHideSuspend: booleanAllowedValues, // true || false\r\n          dragScroll: booleanAllowedValues, // true || false\r\n          clickScroll: scrollbarsClickScrollAllowedValues, // true || false || instant\r\n          pointers: [oTypes.array, oTypes.null], // string array\r\n        },\r\n        /*\r\n        textarea: {\r\n          dynWidth: booleanAllowedValues, // true || false\r\n          dynHeight: booleanAllowedValues, // true || false\r\n          inheritedAttrs: stringArrayNullAllowedValues, // string || array || nul\r\n        },\r\n        */\r\n      };\r\n      return (options: PartialOptions, doWriteErrors?: boolean): PartialOptions => {\r\n        const [validated, foreign] = validateOptions(optionsTemplate, options, doWriteErrors);\r\n        return { ...foreign, ...validated };\r\n      };\r\n    },\r\n  },\r\n}))() satisfies StaticPlugin<typeof optionsValidationPluginModuleName>;\r\n", "import { strOverflowX, strOverflowY } from './support';\r\n\r\nconst dataAttributePrefix = `data-overlayscrollbars`;\r\n\r\n// environment\r\nexport const classNameEnvironment = 'os-environment';\r\nexport const classNameEnvironmentScrollbarHidden = `${classNameEnvironment}-scrollbar-hidden`;\r\n\r\n// initialize\r\nexport const dataAttributeInitialize = `${dataAttributePrefix}-initialize`;\r\n\r\n// shared\r\nexport const dataValueNoClipping = 'noClipping';\r\n\r\n// body\r\nexport const dataAttributeHtmlBody = `${dataAttributePrefix}-body`;\r\n\r\n// host\r\nexport const dataAttributeHost = dataAttributePrefix;\r\nexport const dataValueHostIsHost = 'host';\r\n\r\n// viewport\r\nexport const dataAttributeViewport = `${dataAttributePrefix}-viewport`;\r\nexport const dataValueViewportOverflowXPrefix = strOverflowX;\r\nexport const dataValueViewportOverflowYPrefix = strOverflowY;\r\nexport const dataValueViewportArrange = 'arrange';\r\nexport const dataValueViewportMeasuring = 'measuring';\r\nexport const dataValueViewportScrolling = 'scrolling';\r\nexport const dataValueViewportScrollbarHidden = 'scrollbarHidden';\r\nexport const dataValueViewportNoContent = 'noContent';\r\n\r\n// padding\r\nexport const dataAttributePadding = `${dataAttributePrefix}-padding`;\r\n\r\n// content\r\nexport const dataAttributeContent = `${dataAttributePrefix}-content`;\r\n\r\n// size observer\r\nexport const classNameSizeObserver = 'os-size-observer';\r\nexport const classNameSizeObserverAppear = `${classNameSizeObserver}-appear`;\r\nexport const classNameSizeObserverListener = `${classNameSizeObserver}-listener`;\r\nexport const classNameSizeObserverListenerScroll = `${classNameSizeObserverListener}-scroll`;\r\nexport const classNameSizeObserverListenerItem = `${classNameSizeObserverListener}-item`;\r\nexport const classNameSizeObserverListenerItemFinal = `${classNameSizeObserverListenerItem}-final`;\r\n\r\n// trinsic observer\r\nexport const classNameTrinsicObserver = 'os-trinsic-observer';\r\n\r\n// scrollbars\r\nexport const classNameScrollbarThemeNone = 'os-theme-none';\r\nexport const classNameScrollbar = 'os-scrollbar';\r\nexport const classNameScrollbarRtl = `${classNameScrollbar}-rtl`;\r\nexport const classNameScrollbarHorizontal = `${classNameScrollbar}-horizontal`;\r\nexport const classNameScrollbarVertical = `${classNameScrollbar}-vertical`;\r\nexport const classNameScrollbarTrack = `${classNameScrollbar}-track`;\r\nexport const classNameScrollbarHandle = `${classNameScrollbar}-handle`;\r\nexport const classNameScrollbarVisible = `${classNameScrollbar}-visible`;\r\nexport const classNameScrollbarCornerless = `${classNameScrollbar}-cornerless`;\r\nexport const classNameScrollbarTransitionless = `${classNameScrollbar}-transitionless`;\r\nexport const classNameScrollbarInteraction = `${classNameScrollbar}-interaction`;\r\nexport const classNameScrollbarUnusable = `${classNameScrollbar}-unusable`;\r\nexport const classNameScrollbarAutoHide = `${classNameScrollbar}-auto-hide`;\r\nexport const classNameScrollbarAutoHideHidden = `${classNameScrollbarAutoHide}-hidden`;\r\nexport const classNameScrollbarWheel = `${classNameScrollbar}-wheel`;\r\nexport const classNameScrollbarTrackInteractive = `${classNameScrollbarTrack}-interactive`;\r\nexport const classNameScrollbarHandleInteractive = `${classNameScrollbarHandle}-interactive`;\r\n", "import type { StaticPlugin } from '../plugins';\r\nimport {\r\n  createDOM,\r\n  appendChildren,\r\n  getOffsetSize,\r\n  addEventListener,\r\n  addClass,\r\n  equalWH,\r\n  cAF,\r\n  rAF,\r\n  stopPropagation,\r\n  bind,\r\n  scrollElementTo,\r\n  strWidth,\r\n  strHeight,\r\n  setStyles,\r\n} from '../../support';\r\nimport {\r\n  classNameSizeObserverListenerScroll,\r\n  classNameSizeObserverListenerItem,\r\n  classNameSizeObserverListenerItemFinal,\r\n} from '../../classnames';\r\n\r\nexport const sizeObserverPluginName = '__osSizeObserverPlugin';\r\n\r\nexport const SizeObserverPlugin = /* @__PURE__ */ (() => ({\r\n  [sizeObserverPluginName]: {\r\n    static:\r\n      () =>\r\n      (\r\n        listenerElement: HTMLElement,\r\n        onSizeChangedCallback: (appear: boolean) => any,\r\n        observeAppearChange: boolean | null | undefined\r\n      ): [appearCallback: () => void, offFns: (() => any)[]] => {\r\n        const scrollAmount = 3333333;\r\n        const scrollEventName = 'scroll';\r\n        const observerElementChildren = createDOM(\r\n          `<div class=\"${classNameSizeObserverListenerItem}\" dir=\"ltr\"><div class=\"${classNameSizeObserverListenerItem}\"><div class=\"${classNameSizeObserverListenerItemFinal}\"></div></div><div class=\"${classNameSizeObserverListenerItem}\"><div class=\"${classNameSizeObserverListenerItemFinal}\" style=\"width: 200%; height: 200%\"></div></div></div>`\r\n        );\r\n        const observerElementChildrenRoot = observerElementChildren[0] as HTMLElement;\r\n        const shrinkElement = observerElementChildrenRoot.lastChild as HTMLElement;\r\n        const expandElement = observerElementChildrenRoot.firstChild as HTMLElement;\r\n        const expandElementChild = expandElement?.firstChild as HTMLElement;\r\n\r\n        let cacheSize = getOffsetSize(observerElementChildrenRoot);\r\n        let currSize = cacheSize;\r\n        let isDirty = false;\r\n        let rAFId: number;\r\n\r\n        const reset = () => {\r\n          scrollElementTo(expandElement, scrollAmount);\r\n          scrollElementTo(shrinkElement, scrollAmount);\r\n        };\r\n        const onResized = (appear?: unknown) => {\r\n          rAFId = 0;\r\n          if (isDirty) {\r\n            cacheSize = currSize;\r\n            onSizeChangedCallback(appear === true);\r\n          }\r\n        };\r\n        const onScroll = (scrollEvent?: Event | false) => {\r\n          currSize = getOffsetSize(observerElementChildrenRoot);\r\n          isDirty = !scrollEvent || !equalWH(currSize, cacheSize);\r\n\r\n          if (scrollEvent) {\r\n            stopPropagation(scrollEvent);\r\n\r\n            if (isDirty && !rAFId) {\r\n              cAF!(rAFId);\r\n              rAFId = rAF!(onResized);\r\n            }\r\n          } else {\r\n            onResized(scrollEvent === false);\r\n          }\r\n\r\n          reset();\r\n        };\r\n        const destroyFns = [\r\n          appendChildren(listenerElement, observerElementChildren),\r\n          addEventListener(expandElement, scrollEventName, onScroll),\r\n          addEventListener(shrinkElement, scrollEventName, onScroll),\r\n        ];\r\n\r\n        addClass(listenerElement, classNameSizeObserverListenerScroll);\r\n\r\n        // lets assume that the divs will never be that large and a constant value is enough\r\n        setStyles(expandElementChild, {\r\n          [strWidth]: scrollAmount,\r\n          [strHeight]: scrollAmount,\r\n        });\r\n\r\n        rAF!(reset);\r\n\r\n        return [observeAppearChange ? bind(onScroll, false) : reset, destroyFns];\r\n      },\r\n  },\r\n}))() satisfies StaticPlugin<typeof sizeObserverPluginName>;\r\n", "import type { Env } from '../../environment';\r\nimport type { XY } from '../../support';\r\nimport type { Options, OptionsCheckFn, OverflowBehavior } from '../../options';\r\nimport type { OverflowStyle } from '../../typings';\r\nimport { strHidden, strScroll, strVisible } from '../../support';\r\n\r\nexport interface ViewportOverflowState {\r\n  _overflowScroll: XY<boolean>;\r\n  _overflowStyle: XY<OverflowStyle>;\r\n}\r\n\r\nexport const getShowNativeOverlaidScrollbars = (checkOption: OptionsCheckFn<Options>, env: Env) => {\r\n  const { _nativeScrollbarsOverlaid } = env;\r\n  const [showNativeOverlaidScrollbarsOption, showNativeOverlaidScrollbarsChanged] = checkOption(\r\n    'showNativeOverlaidScrollbars'\r\n  );\r\n\r\n  return [\r\n    showNativeOverlaidScrollbarsOption &&\r\n      _nativeScrollbarsOverlaid.x &&\r\n      _nativeScrollbarsOverlaid.y,\r\n    showNativeOverlaidScrollbarsChanged,\r\n  ] as const;\r\n};\r\n\r\nexport const overflowIsVisible = (overflowBehavior: string) =>\r\n  overflowBehavior.indexOf(strVisible) === 0;\r\n\r\n/**\r\n * Creates a viewport overflow state object.\r\n * @param hasOverflow The information whether an axis has overflow.\r\n * @param overflowBehavior The overflow behavior according to the options.\r\n * @returns A object which represents the newly set overflow state.\r\n */\r\nexport const createViewportOverflowState = (\r\n  hasOverflow: Partial<XY<boolean>>,\r\n  overflowBehavior: XY<OverflowBehavior>\r\n): ViewportOverflowState => {\r\n  const getAxisOverflowStyle = (\r\n    axisBehavior: OverflowBehavior,\r\n    axisHasOverflow: boolean | undefined,\r\n    perpendicularBehavior: OverflowBehavior,\r\n    perpendicularOverflow: boolean | undefined\r\n  ): OverflowStyle => {\r\n    // convert behavior to style:\r\n    // 'visible'        -> 'hidden'\r\n    // 'hidden'         -> 'hidden'\r\n    // 'scroll'         -> 'scroll'\r\n    // 'visible-hidden' -> 'hidden'\r\n    // 'visible-scroll' -> 'scroll'\r\n    const behaviorStyle =\r\n      axisBehavior === strVisible\r\n        ? strHidden\r\n        : (axisBehavior.replace(`${strVisible}-`, '') as OverflowStyle);\r\n    const axisOverflowVisible = overflowIsVisible(axisBehavior);\r\n    const perpendicularOverflowVisible = overflowIsVisible(perpendicularBehavior);\r\n\r\n    // if no axis has overflow set 'hidden'\r\n    if (!axisHasOverflow && !perpendicularOverflow) {\r\n      return strHidden;\r\n    }\r\n\r\n    // if both axis have a visible behavior ('visible', 'visible-hidden', 'visible-scroll') set 'visible'\r\n    if (axisOverflowVisible && perpendicularOverflowVisible) {\r\n      return strVisible;\r\n    }\r\n\r\n    // this this axis has a visible behavior\r\n    if (axisOverflowVisible) {\r\n      const nonPerpendicularOverflow = axisHasOverflow ? strVisible : strHidden;\r\n      return axisHasOverflow && perpendicularOverflow\r\n        ? behaviorStyle // if both axis have an overflow set ('hidden' or 'scroll')\r\n        : nonPerpendicularOverflow; // if only this axis has an overflow set 'visible', if no axis has an overflow set 'hidden'\r\n    }\r\n\r\n    const nonOverflow =\r\n      perpendicularOverflowVisible && perpendicularOverflow ? strVisible : strHidden;\r\n    return axisHasOverflow\r\n      ? behaviorStyle // if this axis has an overflow\r\n      : nonOverflow; // if the perp. axis has a visible behavior and has an overflow set 'visible', otherwise set 'hidden'\r\n  };\r\n\r\n  const _overflowStyle = {\r\n    x: getAxisOverflowStyle(overflowBehavior.x, hasOverflow.x, overflowBehavior.y, hasOverflow.y),\r\n    y: getAxisOverflowStyle(overflowBehavior.y, hasOverflow.y, overflowBehavior.x, hasOverflow.x),\r\n  };\r\n\r\n  return {\r\n    _overflowStyle,\r\n    _overflowScroll: {\r\n      x: _overflowStyle.x === strScroll,\r\n      y: _overflowStyle.y === strScroll,\r\n    },\r\n  };\r\n};\r\n", "import type { ObserversSetupState } from '../../setups';\r\nimport type { Options, OptionsCheckFn } from '../../options';\r\nimport type { StructureSetupElementsObj } from '../../setups/structureSetup/structureSetup.elements';\r\nimport type { ViewportOverflowState } from '../../setups/structureSetup/structureSetup.utils';\r\nimport type { Env } from '../../environment';\r\nimport type { WH } from '../../support';\r\nimport type { OverflowStyle, StyleObject, StyleObjectKey } from '../../typings';\r\nimport type { StructureSetupState } from '../../setups/structureSetup';\r\nimport type { StaticPlugin } from '../plugins';\r\nimport { getShowNativeOverlaidScrollbars } from '../../setups/structureSetup/structureSetup.utils';\r\nimport { dataValueViewportArrange, dataAttributeViewport } from '../../classnames';\r\nimport {\r\n  keys,\r\n  noop,\r\n  each,\r\n  assignDeep,\r\n  strMarginBottom,\r\n  strMarginLeft,\r\n  strMarginRight,\r\n  strPaddingBottom,\r\n  strPaddingLeft,\r\n  strPaddingRight,\r\n  strPaddingTop,\r\n  getStyles,\r\n  setStyles,\r\n  removeAttrClass,\r\n  strWidth,\r\n  strOverflowY,\r\n  strOverflowX,\r\n  strScroll,\r\n} from '../../support';\r\n\r\nexport const scrollbarsHidingPluginName = '__osScrollbarsHidingPlugin';\r\n\r\nexport const ScrollbarsHidingPlugin = /* @__PURE__ */ (() => ({\r\n  [scrollbarsHidingPluginName]: {\r\n    static: () => ({\r\n      _viewportArrangement: (\r\n        structureSetupElements: StructureSetupElementsObj,\r\n        structureSetupState: StructureSetupState,\r\n        observersSetupState: ObserversSetupState,\r\n        env: Env,\r\n        checkOptions: OptionsCheckFn<Options>\r\n      ) => {\r\n        const { _viewportIsTarget, _viewport } = structureSetupElements;\r\n        const { _nativeScrollbarsHiding, _nativeScrollbarsOverlaid, _nativeScrollbarsSize } = env;\r\n        const doViewportArrange =\r\n          !_viewportIsTarget &&\r\n          !_nativeScrollbarsHiding &&\r\n          (_nativeScrollbarsOverlaid.x || _nativeScrollbarsOverlaid.y);\r\n        const [showNativeOverlaidScrollbars] = getShowNativeOverlaidScrollbars(checkOptions, env);\r\n\r\n        /**\r\n         * Gets the current overflow state of the viewport.\r\n         */\r\n        const readViewportOverflowState = (): ViewportOverflowState => {\r\n          const getStatePerAxis = (styleKey: StyleObjectKey) => {\r\n            const overflowStyle = getStyles(_viewport, styleKey) as OverflowStyle;\r\n            const overflowScroll = overflowStyle === strScroll;\r\n\r\n            return [overflowStyle, overflowScroll] as const;\r\n          };\r\n\r\n          const [xOverflowStyle, xOverflowScroll] = getStatePerAxis(strOverflowX);\r\n          const [yOverflowStyle, yOverflowScroll] = getStatePerAxis(strOverflowY);\r\n\r\n          return {\r\n            _overflowStyle: {\r\n              x: xOverflowStyle,\r\n              y: yOverflowStyle,\r\n            },\r\n            _overflowScroll: {\r\n              x: xOverflowScroll,\r\n              y: yOverflowScroll,\r\n            },\r\n          };\r\n        };\r\n\r\n        /**\r\n         * Gets the hide offset matching the passed overflow state.\r\n         * @param viewportOverflowState The overflow state of the viewport\r\n         */\r\n        const _getViewportOverflowHideOffset = (viewportOverflowState: ViewportOverflowState) => {\r\n          const { _overflowScroll } = viewportOverflowState;\r\n          const arrangeHideOffset =\r\n            _nativeScrollbarsHiding || showNativeOverlaidScrollbars ? 0 : 42;\r\n\r\n          const getHideOffsetPerAxis = (\r\n            isOverlaid: boolean,\r\n            overflowScroll: boolean,\r\n            nativeScrollbarSize: number\r\n          ) => {\r\n            const nonScrollbarStylingHideOffset = isOverlaid\r\n              ? arrangeHideOffset\r\n              : nativeScrollbarSize;\r\n            const scrollbarsHideOffset =\r\n              overflowScroll && !_nativeScrollbarsHiding ? nonScrollbarStylingHideOffset : 0;\r\n            const scrollbarsHideOffsetArrange = isOverlaid && !!arrangeHideOffset;\r\n\r\n            return [scrollbarsHideOffset, scrollbarsHideOffsetArrange] as const;\r\n          };\r\n\r\n          const [xScrollbarsHideOffset, xScrollbarsHideOffsetArrange] = getHideOffsetPerAxis(\r\n            _nativeScrollbarsOverlaid.x,\r\n            _overflowScroll.x,\r\n            _nativeScrollbarsSize.x\r\n          );\r\n          const [yScrollbarsHideOffset, yScrollbarsHideOffsetArrange] = getHideOffsetPerAxis(\r\n            _nativeScrollbarsOverlaid.y,\r\n            _overflowScroll.y,\r\n            _nativeScrollbarsSize.y\r\n          );\r\n\r\n          return {\r\n            _scrollbarsHideOffset: {\r\n              x: xScrollbarsHideOffset,\r\n              y: yScrollbarsHideOffset,\r\n            },\r\n            _scrollbarsHideOffsetArrange: {\r\n              x: xScrollbarsHideOffsetArrange,\r\n              y: yScrollbarsHideOffsetArrange,\r\n            },\r\n          };\r\n        };\r\n\r\n        /**\r\n         * Hides the native scrollbars according to the passed parameters.\r\n         * @param viewportOverflowState The viewport overflow state.\r\n         * @param directionIsRTL Whether the direction is RTL or not.\r\n         * @param viewportArrange Whether special styles related to the viewport arrange strategy shall be applied.\r\n         * @param viewportStyleObj The viewport style object to which the needed styles shall be applied.\r\n         */\r\n        const _hideNativeScrollbars = (\r\n          viewportOverflowState: ViewportOverflowState,\r\n          { _directionIsRTL }: ObserversSetupState,\r\n          viewportArrange: boolean\r\n        ): StyleObject | undefined => {\r\n          if (!_viewportIsTarget) {\r\n            const viewportStyleObj: StyleObject = assignDeep(\r\n              {},\r\n              {\r\n                [strMarginRight]: 0,\r\n                [strMarginBottom]: 0,\r\n                [strMarginLeft]: 0,\r\n              }\r\n            );\r\n            const { _scrollbarsHideOffset, _scrollbarsHideOffsetArrange } =\r\n              _getViewportOverflowHideOffset(viewportOverflowState);\r\n            const { x: arrangeX, y: arrangeY } = _scrollbarsHideOffsetArrange;\r\n            const { x: hideOffsetX, y: hideOffsetY } = _scrollbarsHideOffset;\r\n            const { _viewportPaddingStyle } = structureSetupState;\r\n            const horizontalMarginKey: keyof StyleObject = _directionIsRTL\r\n              ? strMarginLeft\r\n              : strMarginRight;\r\n            const viewportHorizontalPaddingKey: keyof StyleObject = _directionIsRTL\r\n              ? strPaddingLeft\r\n              : strPaddingRight;\r\n            const horizontalMarginValue = _viewportPaddingStyle[horizontalMarginKey] as number;\r\n            const verticalMarginValue = _viewportPaddingStyle[strMarginBottom] as number;\r\n            const horizontalPaddingValue = _viewportPaddingStyle[\r\n              viewportHorizontalPaddingKey\r\n            ] as number;\r\n            const verticalPaddingValue = _viewportPaddingStyle[strPaddingBottom] as number;\r\n\r\n            // horizontal\r\n            viewportStyleObj[strWidth] = `calc(100% + ${\r\n              hideOffsetY + horizontalMarginValue * -1\r\n            }px)`;\r\n            viewportStyleObj[horizontalMarginKey] = -hideOffsetY + horizontalMarginValue;\r\n\r\n            // vertical\r\n            viewportStyleObj[strMarginBottom] = -hideOffsetX + verticalMarginValue;\r\n\r\n            // viewport arrange additional styles\r\n            if (viewportArrange) {\r\n              viewportStyleObj[viewportHorizontalPaddingKey] =\r\n                horizontalPaddingValue + (arrangeY ? hideOffsetY : 0);\r\n              viewportStyleObj[strPaddingBottom] =\r\n                verticalPaddingValue + (arrangeX ? hideOffsetX : 0);\r\n            }\r\n\r\n            return viewportStyleObj;\r\n          }\r\n        };\r\n\r\n        /**\r\n         * Sets the styles of the viewport arrange element.\r\n         * @param viewportOverflowState The viewport overflow state according to which the scrollbars shall be hidden.\r\n         * @param viewportScrollSize The content scroll size.\r\n         * @param directionIsRTL Whether the direction is RTL or not.\r\n         * @returns A boolean which indicates whether the viewport arrange element was adjusted.\r\n         */\r\n        const _arrangeViewport = (\r\n          viewportOverflowState: ViewportOverflowState,\r\n          viewportScrollSize: WH<number>,\r\n          sizeFraction: WH<number>\r\n        ) => {\r\n          if (doViewportArrange) {\r\n            const { _viewportPaddingStyle } = structureSetupState;\r\n            const { _scrollbarsHideOffset, _scrollbarsHideOffsetArrange } =\r\n              _getViewportOverflowHideOffset(viewportOverflowState);\r\n            const { x: arrangeX, y: arrangeY } = _scrollbarsHideOffsetArrange;\r\n            const { x: hideOffsetX, y: hideOffsetY } = _scrollbarsHideOffset;\r\n            const { _directionIsRTL } = observersSetupState;\r\n            const viewportArrangeHorizontalPaddingKey: keyof StyleObject = _directionIsRTL\r\n              ? strPaddingRight\r\n              : strPaddingLeft;\r\n            const viewportArrangeHorizontalPaddingValue = _viewportPaddingStyle[\r\n              viewportArrangeHorizontalPaddingKey\r\n            ] as number;\r\n            const viewportArrangeVerticalPaddingValue = _viewportPaddingStyle.paddingTop as number;\r\n            const fractionalContentWidth = viewportScrollSize.w + sizeFraction.w;\r\n            const fractionalContenHeight = viewportScrollSize.h + sizeFraction.h;\r\n            const arrangeSize = {\r\n              w:\r\n                hideOffsetY && arrangeY\r\n                  ? `${\r\n                      hideOffsetY + fractionalContentWidth - viewportArrangeHorizontalPaddingValue\r\n                    }px`\r\n                  : '',\r\n              h:\r\n                hideOffsetX && arrangeX\r\n                  ? `${\r\n                      hideOffsetX + fractionalContenHeight - viewportArrangeVerticalPaddingValue\r\n                    }px`\r\n                  : '',\r\n            };\r\n\r\n            setStyles(_viewport, {\r\n              '--os-vaw': arrangeSize.w,\r\n              '--os-vah': arrangeSize.h,\r\n            });\r\n          }\r\n\r\n          return doViewportArrange;\r\n        };\r\n\r\n        /**\r\n         * Removes all styles applied because of the viewport arrange strategy.\r\n         * @param showNativeOverlaidScrollbars Whether native overlaid scrollbars are shown instead of hidden.\r\n         * @param directionIsRTL Whether the direction is RTL or not.\r\n         * @param viewportOverflowState The currentviewport overflow state or undefined if it has to be determined.\r\n         * @returns A object with a function which applies all the removed styles and the determined viewport vverflow state.\r\n         */\r\n        const _undoViewportArrange = (viewportOverflowState?: ViewportOverflowState) => {\r\n          if (doViewportArrange) {\r\n            const finalViewportOverflowState = viewportOverflowState || readViewportOverflowState();\r\n            const { _viewportPaddingStyle: viewportPaddingStyle } = structureSetupState;\r\n            const { _scrollbarsHideOffsetArrange } = _getViewportOverflowHideOffset(\r\n              finalViewportOverflowState\r\n            );\r\n            const { x: arrangeX, y: arrangeY } = _scrollbarsHideOffsetArrange;\r\n            const finalPaddingStyle: StyleObject = {};\r\n            const assignProps = (props: string[]) =>\r\n              each(props, (prop) => {\r\n                finalPaddingStyle[prop as StyleObjectKey] =\r\n                  viewportPaddingStyle[prop as StyleObjectKey];\r\n              });\r\n\r\n            if (arrangeX) {\r\n              assignProps([strMarginBottom, strPaddingTop, strPaddingBottom]);\r\n            }\r\n\r\n            if (arrangeY) {\r\n              assignProps([strMarginLeft, strMarginRight, strPaddingLeft, strPaddingRight]);\r\n            }\r\n\r\n            const prevStyle = getStyles(_viewport, keys(finalPaddingStyle) as StyleObjectKey[]);\r\n            const addArrange = removeAttrClass(\r\n              _viewport,\r\n              dataAttributeViewport,\r\n              dataValueViewportArrange\r\n            );\r\n\r\n            setStyles(_viewport, finalPaddingStyle);\r\n\r\n            return [\r\n              () => {\r\n                setStyles(\r\n                  _viewport,\r\n                  assignDeep(\r\n                    {},\r\n                    prevStyle,\r\n                    _hideNativeScrollbars(\r\n                      finalViewportOverflowState,\r\n                      observersSetupState,\r\n                      doViewportArrange\r\n                    )\r\n                  )\r\n                );\r\n                addArrange();\r\n              },\r\n              finalViewportOverflowState,\r\n            ] as const;\r\n          }\r\n          return [noop] as const;\r\n        };\r\n\r\n        return {\r\n          _getViewportOverflowHideOffset,\r\n          _arrangeViewport,\r\n          _undoViewportArrange,\r\n          _hideNativeScrollbars,\r\n        };\r\n      },\r\n    }),\r\n  },\r\n}))() satisfies StaticPlugin<typeof scrollbarsHidingPluginName>;\r\n", "import type { StaticPlugin } from '../plugins';\r\nimport { animateNumber, noop, selfClearTimeout } from '../../support';\r\n\r\nexport const clickScrollPluginModuleName = '__osClickScrollPlugin';\r\n\r\nexport const ClickScrollPlugin = /* @__PURE__ */ (() => ({\r\n  [clickScrollPluginModuleName]: {\r\n    static:\r\n      () =>\r\n      (\r\n        moveHandleRelative: (deltaMovement: number) => void,\r\n        targetOffset: number,\r\n        handleLength: number,\r\n        onClickScrollCompleted: (stopped: boolean) => void\r\n      ) => {\r\n        // click scroll animation has 2 main parts:\r\n        // 1. the \"click\" which scrolls 100% of the viewport in a certain amount of time\r\n        // 2. the \"press\" which scrolls to the point where the cursor is located, the \"press\" always waits for the \"click\" to finish\r\n        // The \"click\" should not be canceled by a \"pointerup\" event because very fast clicks or taps would cancel it too fast\r\n        // The \"click\" should only be canceled by a subsequent \"pointerdown\" event because otherwise 2 animations would run\r\n        // The \"press\" should be canceld by the next \"pointerup\" event\r\n\r\n        let stopped = false;\r\n        let stopPressAnimation = noop;\r\n        const linearScrollMs = 133;\r\n        const easedScrollMs = 222;\r\n        const [setPressAnimationTimeout, clearPressAnimationTimeout] =\r\n          selfClearTimeout(linearScrollMs);\r\n        const targetOffsetSign = Math.sign(targetOffset);\r\n        const handleLengthWithTargetSign = handleLength * targetOffsetSign;\r\n        const handleLengthWithTargetSignHalf = handleLengthWithTargetSign / 2;\r\n        const easing = (x: number) => 1 - (1 - x) * (1 - x); // easeOutQuad;\r\n        const easedEndPressAnimation = (from: number, to: number) =>\r\n          animateNumber(from, to, easedScrollMs, moveHandleRelative, easing);\r\n        const linearPressAnimation = (linearFrom: number, msFactor: number) =>\r\n          animateNumber(\r\n            linearFrom,\r\n            targetOffset - handleLengthWithTargetSign,\r\n            linearScrollMs * msFactor,\r\n            (progress, _, completed) => {\r\n              moveHandleRelative(progress);\r\n\r\n              if (completed) {\r\n                stopPressAnimation = easedEndPressAnimation(progress, targetOffset);\r\n              }\r\n            }\r\n          );\r\n        const stopClickAnimation = animateNumber(\r\n          0,\r\n          handleLengthWithTargetSign,\r\n          easedScrollMs,\r\n          (clickAnimationProgress, _, clickAnimationCompleted) => {\r\n            moveHandleRelative(clickAnimationProgress);\r\n\r\n            if (clickAnimationCompleted) {\r\n              onClickScrollCompleted(stopped);\r\n\r\n              if (!stopped) {\r\n                const remainingScrollDistance = targetOffset - clickAnimationProgress;\r\n                const continueWithPress =\r\n                  Math.sign(remainingScrollDistance - handleLengthWithTargetSignHalf) ===\r\n                  targetOffsetSign;\r\n\r\n                continueWithPress &&\r\n                  setPressAnimationTimeout(() => {\r\n                    const remainingLinearScrollDistance =\r\n                      remainingScrollDistance - handleLengthWithTargetSign;\r\n                    const linearBridge =\r\n                      Math.sign(remainingLinearScrollDistance) === targetOffsetSign;\r\n\r\n                    stopPressAnimation = linearBridge\r\n                      ? linearPressAnimation(\r\n                          clickAnimationProgress,\r\n                          Math.abs(remainingLinearScrollDistance) / handleLength\r\n                        )\r\n                      : easedEndPressAnimation(clickAnimationProgress, targetOffset);\r\n                  });\r\n              }\r\n            }\r\n          },\r\n          easing\r\n        );\r\n\r\n        return (stopClick?: boolean) => {\r\n          stopped = true;\r\n\r\n          if (stopClick) {\r\n            stopClickAnimation();\r\n          }\r\n\r\n          clearPressAnimationTimeout();\r\n          stopPressAnimation();\r\n        };\r\n      },\r\n  },\r\n}))() satisfies StaticPlugin<typeof clickScrollPluginModuleName>;\r\n", "import type { DeepPartial, DeepReadonly } from './typings';\r\nimport {\r\n  assignDeep,\r\n  each,\r\n  isObject,\r\n  keys,\r\n  isArray,\r\n  hasOwnProperty,\r\n  isFunction,\r\n  isEmptyObject,\r\n  concat,\r\n} from './support';\r\n\r\nexport type OptionsField = string;\r\n\r\nexport type OptionsPrimitiveValue =\r\n  | boolean\r\n  | number\r\n  | string\r\n  | Array<any>\r\n  | ReadonlyArray<any>\r\n  | [any]\r\n  | [any, ...any[]]\r\n  | ((this: any, ...args: any[]) => any)\r\n  | null;\r\n\r\nexport type OptionsObject = {\r\n  [field: OptionsField]: OptionsPrimitiveValue | OptionsObject;\r\n};\r\n\r\ntype OptionsObjectFieldNameTuples<T> = T extends OptionsPrimitiveValue\r\n  ? []\r\n  : {\r\n      [K in Extract<keyof T, OptionsField>]: [K, ...OptionsObjectFieldNameTuples<T[K]>];\r\n    }[Extract<keyof T, OptionsField>];\r\n\r\ntype JoinOptionsObjectFieldTuples<\r\n  T extends OptionsField[],\r\n  IncompletePath extends boolean = false,\r\n> = T extends [infer F]\r\n  ? F\r\n  : T extends [infer F, ...infer R]\r\n    ? F extends OptionsField\r\n      ?\r\n          | (IncompletePath extends true ? F : never)\r\n          | `${F}.${JoinOptionsObjectFieldTuples<Extract<R, OptionsField[]>>}`\r\n      : never\r\n    : OptionsField;\r\n\r\ntype SplitJoinedOptionsObjectFieldTuples<S extends string> = string extends S\r\n  ? OptionsField[]\r\n  : S extends ''\r\n    ? []\r\n    : S extends `${infer T}.${infer U}`\r\n      ? [T, ...SplitJoinedOptionsObjectFieldTuples<U>]\r\n      : [S];\r\n\r\ntype OptionsObjectFieldTuplesType<O, T extends OptionsField[]> = T extends [infer F]\r\n  ? F extends keyof O\r\n    ? O[F]\r\n    : never\r\n  : T extends [infer F, ...infer R]\r\n    ? F extends keyof O\r\n      ? O[F] extends OptionsPrimitiveValue\r\n        ? O[F]\r\n        : OptionsObjectFieldTuplesType<O[F], Extract<R, OptionsField[]>>\r\n      : never\r\n    : never;\r\n\r\ntype OptionsObjectFieldPath<O extends OptionsObject> = JoinOptionsObjectFieldTuples<\r\n  OptionsObjectFieldNameTuples<O>,\r\n  true\r\n>;\r\n\r\ntype OptionsObjectFieldPathType<\r\n  O extends OptionsObject,\r\n  P extends string,\r\n> = OptionsObjectFieldTuplesType<O, SplitJoinedOptionsObjectFieldTuples<P>>;\r\n\r\nconst opsStringify = (value: any) =>\r\n  JSON.stringify(value, (_, val) => {\r\n    if (isFunction(val)) {\r\n      throw 0;\r\n    }\r\n    return val;\r\n  });\r\n\r\nconst getPropByPath = <T>(obj: any, path: string): T =>\r\n  obj\r\n    ? `${path}`\r\n        .split('.')\r\n        .reduce((o, prop) => (o && hasOwnProperty(o, prop) ? o[prop] : undefined), obj)\r\n    : undefined;\r\n\r\n/**\r\n * The overflow behavior of an axis.\r\n */\r\nexport type OverflowBehavior =\r\n  /** No scrolling is possible and the content is clipped. */\r\n  | 'hidden'\r\n  /** No scrolling is possible and the content isn't clipped. */\r\n  | 'visible'\r\n  /** Scrolling is possible if there is an overflow. */\r\n  | 'scroll'\r\n  /**\r\n   * If the other axis has no overflow the behavior is similar to `visible`.\r\n   * If the other axis has overflow the behavior is similar to `hidden`.\r\n   */\r\n  | 'visible-hidden'\r\n  /**\r\n   * If the other axis has no overflow the behavior is similar to `visible`.\r\n   * If the other axis has overflow the behavior is similar to `scroll`.\r\n   */\r\n  | 'visible-scroll';\r\n\r\n/**\r\n * The scrollbars visibility behavior.\r\n */\r\nexport type ScrollbarsVisibilityBehavior =\r\n  /** The scrollbars are always visible. */\r\n  | 'visible'\r\n  /** The scrollbars are always hidden. */\r\n  | 'hidden'\r\n  /** The scrollbars are only visibile if there is overflow. */\r\n  | 'auto';\r\n\r\n/**\r\n * The scrollbars auto hide behavior\r\n */\r\nexport type ScrollbarsAutoHideBehavior =\r\n  /** The scrollbars are never hidden automatically. */\r\n  | 'never'\r\n  /** The scrollbars are hidden unless the user scrolls. */\r\n  | 'scroll'\r\n  /** The scrollbars are hidden unless the pointer moves in the host element or the user scrolls. */\r\n  | 'move'\r\n  /** The scrollbars are hidden if the pointer leaves the host element or unless the user scrolls. */\r\n  | 'leave';\r\n\r\n/**\r\n * The scrollbar click scroll behavior.\r\n */\r\nexport type ScrollbarsClickScrollBehavior = boolean | 'instant';\r\n\r\n/**\r\n * The options of a OverlayScrollbars instance.\r\n */\r\nexport type Options = {\r\n  /** Whether the padding shall be absolute. */\r\n  paddingAbsolute: boolean;\r\n  /** Whether to show the native scrollbars. Has only an effect it the native scrollbars are overlaid. */\r\n  showNativeOverlaidScrollbars: boolean;\r\n  /** Customizes the automatic update behavior. */\r\n  update: {\r\n    /**\r\n     * The given Event(s) from the elements with the given selector(s) will trigger an update.\r\n     * Useful for everything the MutationObserver and ResizeObserver can't detect\r\n     * e.g.: and Images `load` event or the `transitionend` / `animationend` events.\r\n     */\r\n    elementEvents: Array<[elementSelector: string, eventNames: string]> | null;\r\n    /**\r\n     * The debounce which is used to detect content changes.\r\n     * If a tuple is provided you can customize the `timeout` and the `maxWait` in milliseconds.\r\n     * If a single number customizes only the `timeout`.\r\n     *\r\n     * If the `timeout` is `0`, a debounce still exists. (its executed via `requestAnimationFrame`).\r\n     */\r\n    debounce: [timeout: number, maxWait: number] | number | null;\r\n    /**\r\n     * HTML attributes which will trigger an update if they're changed.\r\n     * Basic attributes like `id`, `class`, `style` etc. are always observed and doesn't have to be added explicitly.\r\n     */\r\n    attributes: string[] | null;\r\n    /**\r\n     * A function which makes it possible to ignore a content mutation or null if nothing shall be ignored.\r\n     * @param mutation The MutationRecord from the MutationObserver.\r\n     * @returns A Truthy value if the mutation shall be ignored, a falsy value otherwise.\r\n     */\r\n    ignoreMutation: ((mutation: MutationRecord) => any) | null;\r\n  };\r\n  /** Customizes the overflow behavior per axis. */\r\n  overflow: {\r\n    /** The overflow behavior of the horizontal (x) axis. */\r\n    x: OverflowBehavior;\r\n    /** The overflow behavior of the vertical (y) axis. */\r\n    y: OverflowBehavior;\r\n  };\r\n  /** Customizes appearance of the scrollbars. */\r\n  scrollbars: {\r\n    /**\r\n     * The scrollbars theme.\r\n     * The theme value will be added as `class` to all `scrollbar` elements of the instance.\r\n     */\r\n    theme: string | null;\r\n    /** The scrollbars visibility behavior. */\r\n    visibility: ScrollbarsVisibilityBehavior;\r\n    /** The scrollbars auto hide behavior. */\r\n    autoHide: ScrollbarsAutoHideBehavior;\r\n    /** The scrollbars auto hide delay in milliseconds. */\r\n    autoHideDelay: number;\r\n    /** Whether the scrollbars auto hide behavior is suspended until a scroll happened. */\r\n    autoHideSuspend: boolean;\r\n    /** Whether it is possible to drag the handle of a scrollbar to scroll the viewport. */\r\n    dragScroll: boolean;\r\n    /** Whether it is possible to click the track of a scrollbar to scroll the viewport. */\r\n    clickScroll: ScrollbarsClickScrollBehavior;\r\n    /**\r\n     * An array of pointer types which shall be supported.\r\n     * Common pointer types are: `mouse`, `pen` and `touch`.\r\n     * https://developer.mozilla.org/en-US/docs/Web/API/PointerEvent/pointerType\r\n     */\r\n    pointers: string[] | null;\r\n  };\r\n};\r\n\r\nexport type ReadonlyOptions = DeepReadonly<Options>;\r\n\r\nexport type PartialOptions = DeepPartial<Options>;\r\n\r\nexport type OptionsCheckFn<O extends OptionsObject> = <P extends OptionsObjectFieldPath<O>>(\r\n  path: P\r\n) => [value: OptionsObjectFieldPathType<O, P>, changed: boolean];\r\n\r\nexport const defaultOptions: ReadonlyOptions = {\r\n  paddingAbsolute: false,\r\n  showNativeOverlaidScrollbars: false,\r\n  update: {\r\n    elementEvents: [['img', 'load']],\r\n    debounce: [0, 33],\r\n    attributes: null,\r\n    ignoreMutation: null,\r\n  },\r\n  overflow: {\r\n    x: 'scroll',\r\n    y: 'scroll',\r\n  },\r\n  scrollbars: {\r\n    theme: 'os-theme-dark',\r\n    visibility: 'auto',\r\n    autoHide: 'never',\r\n    autoHideDelay: 1300,\r\n    autoHideSuspend: false,\r\n    dragScroll: true,\r\n    clickScroll: false,\r\n    pointers: ['mouse', 'touch', 'pen'],\r\n  },\r\n} satisfies OptionsObject & Options;\r\n\r\nexport const getOptionsDiff = <T>(currOptions: T, newOptions: DeepPartial<T>): DeepPartial<T> => {\r\n  const diff: DeepPartial<T> = {};\r\n  const optionsKeys = concat(keys(newOptions), keys(currOptions)) as Array<\r\n    keyof T & keyof DeepPartial<T>\r\n  >;\r\n\r\n  each(optionsKeys, (optionKey) => {\r\n    const currOptionValue = currOptions[optionKey];\r\n    const newOptionValue = newOptions[optionKey];\r\n\r\n    if (isObject(currOptionValue) && isObject(newOptionValue)) {\r\n      assignDeep((diff[optionKey] = {} as any), getOptionsDiff(currOptionValue, newOptionValue));\r\n      // delete empty nested objects\r\n      if (isEmptyObject(diff[optionKey])) {\r\n        delete diff[optionKey];\r\n      }\r\n    } else if (hasOwnProperty(newOptions, optionKey) && newOptionValue !== currOptionValue) {\r\n      let isDiff = true;\r\n\r\n      if (isArray(currOptionValue) || isArray(newOptionValue)) {\r\n        try {\r\n          if (opsStringify(currOptionValue) === opsStringify(newOptionValue)) {\r\n            isDiff = false;\r\n          }\r\n        } catch {}\r\n      }\r\n\r\n      if (isDiff) {\r\n        // @ts-ignore\r\n        diff[optionKey] = newOptionValue;\r\n      }\r\n    }\r\n  });\r\n\r\n  return diff;\r\n};\r\n\r\nexport const createOptionCheck =\r\n  <T extends OptionsObject>(\r\n    options: T,\r\n    changedOptions: DeepPartial<T>,\r\n    force?: boolean\r\n  ): OptionsCheckFn<T> =>\r\n  (path) => [\r\n    getPropByPath(options, path),\r\n    force || getPropByPath(changedOptions, path) !== undefined,\r\n  ];\r\n", "let nonce: string | undefined;\r\n\r\nexport const getNonce = () => nonce;\r\nexport const setNonce = (newNonce: string | undefined) => {\r\n  nonce = newNonce;\r\n};\r\n", "import type { XY, EventListener } from './support';\r\nimport type { Options, PartialOptions } from './options';\r\nimport type { Initialization, PartialInitialization } from './initialization';\r\nimport type { StyleObjectKey } from './typings';\r\nimport { defaultOptions } from './options';\r\nimport { classNameEnvironment, classNameEnvironmentScrollbarHidden } from './classnames';\r\nimport {\r\n  createDOM,\r\n  addClass,\r\n  appendChildren,\r\n  getFractionalSize,\r\n  getClientSize,\r\n  getOffsetSize,\r\n  removeAttrs,\r\n  removeElements,\r\n  assignDeep,\r\n  createCache,\r\n  equalXY,\r\n  createEventListenerHub,\r\n  scrollT,\r\n  bind,\r\n  wnd,\r\n  getStyles,\r\n  isBodyElement,\r\n  isFunction,\r\n  addEventListener,\r\n} from './support';\r\nimport { getNonce } from './nonce';\r\n\r\ntype EnvironmentEventArgs = {\r\n  r: [scrollbarSizeChanged?: boolean];\r\n};\r\n\r\nexport interface Env {\r\n  readonly _nativeScrollbarsSize: XY;\r\n  readonly _nativeScrollbarsOverlaid: XY<boolean>;\r\n  readonly _nativeScrollbarsHiding: boolean;\r\n  readonly _scrollTimeline: boolean;\r\n  readonly _staticDefaultInitialization: Initialization;\r\n  readonly _staticDefaultOptions: Options;\r\n  _addResizeListener(listener: EventListener<EnvironmentEventArgs, 'r'>): () => void;\r\n  _getDefaultInitialization(): Initialization;\r\n  _setDefaultInitialization(newInitialization: PartialInitialization): Initialization;\r\n  _getDefaultOptions(): Options;\r\n  _setDefaultOptions(newDefaultOptions: PartialOptions): Options;\r\n}\r\n\r\nlet environmentInstance: Env;\r\n\r\nconst createEnvironment = (): Env => {\r\n  const getNativeScrollbarSize = (\r\n    measureElm: HTMLElement,\r\n    measureElmChild: HTMLElement,\r\n    clear?: boolean\r\n  ): XY => {\r\n    // fix weird safari issue where getComputedStyle returns all empty styles by appending twice\r\n    appendChildren(document.body, measureElm);\r\n    appendChildren(document.body, measureElm);\r\n\r\n    const cSize = getClientSize(measureElm);\r\n    const oSize = getOffsetSize(measureElm);\r\n    const fSize = getFractionalSize(measureElmChild);\r\n\r\n    clear && removeElements(measureElm);\r\n\r\n    return {\r\n      x: oSize.h - cSize.h + fSize.h,\r\n      y: oSize.w - cSize.w + fSize.w,\r\n    };\r\n  };\r\n\r\n  const getNativeScrollbarsHiding = (testElm: HTMLElement): boolean => {\r\n    let result = false;\r\n    const revertClass = addClass(testElm, classNameEnvironmentScrollbarHidden);\r\n    try {\r\n      result =\r\n        getStyles(testElm, 'scrollbar-width' as StyleObjectKey) === 'none' ||\r\n        getStyles(testElm, 'display', '::-webkit-scrollbar') === 'none';\r\n    } catch {}\r\n    revertClass();\r\n    return result;\r\n  };\r\n\r\n  // changes to this styles need to be reflected in the \"hide native scrollbars\" section of the structure styles\r\n  const envStyle = `.${classNameEnvironment}{scroll-behavior:auto!important;position:fixed;opacity:0;visibility:hidden;overflow:scroll;height:200px;width:200px;z-index:-1}.${classNameEnvironment} div{width:200%;height:200%;margin:10px 0}.${classNameEnvironmentScrollbarHidden}{scrollbar-width:none!important}.${classNameEnvironmentScrollbarHidden}::-webkit-scrollbar,.${classNameEnvironmentScrollbarHidden}::-webkit-scrollbar-corner{appearance:none!important;display:none!important;width:0!important;height:0!important}`;\r\n  const envDOM = createDOM(\r\n    `<div class=\"${classNameEnvironment}\"><div></div><style>${envStyle}</style></div>`\r\n  );\r\n  const envElm = envDOM[0] as HTMLElement;\r\n  const envChildElm = envElm.firstChild as HTMLElement;\r\n  const styleElm = envElm.lastChild as HTMLStyleElement;\r\n  const nonce = getNonce();\r\n\r\n  if (nonce) {\r\n    styleElm.nonce = nonce;\r\n  }\r\n\r\n  const [addEvent, , triggerEvent] = createEventListenerHub<EnvironmentEventArgs>();\r\n  const [updateNativeScrollbarSizeCache, getNativeScrollbarSizeCache] = createCache(\r\n    {\r\n      _initialValue: getNativeScrollbarSize(envElm, envChildElm),\r\n      _equal: equalXY,\r\n    },\r\n    bind(getNativeScrollbarSize, envElm, envChildElm, true)\r\n  );\r\n  const [nativeScrollbarsSize] = getNativeScrollbarSizeCache();\r\n  const nativeScrollbarsHiding = getNativeScrollbarsHiding(envElm);\r\n  const nativeScrollbarsOverlaid = {\r\n    x: nativeScrollbarsSize.x === 0,\r\n    y: nativeScrollbarsSize.y === 0,\r\n  };\r\n  const staticDefaultInitialization: Initialization = {\r\n    elements: {\r\n      host: null,\r\n      padding: !nativeScrollbarsHiding,\r\n      viewport: (target) => nativeScrollbarsHiding && isBodyElement(target) && target,\r\n      content: false,\r\n    },\r\n    scrollbars: {\r\n      slot: true,\r\n    },\r\n    cancel: {\r\n      nativeScrollbarsOverlaid: false,\r\n      body: null,\r\n    },\r\n  };\r\n  const staticDefaultOptions = assignDeep({}, defaultOptions);\r\n  const getDefaultOptions = bind(\r\n    assignDeep as typeof assignDeep<Options, Options>,\r\n    {} as Options,\r\n    staticDefaultOptions\r\n  );\r\n  const getDefaultInitialization = bind(\r\n    assignDeep as typeof assignDeep<Initialization, Initialization>,\r\n    {} as Initialization,\r\n    staticDefaultInitialization\r\n  );\r\n\r\n  const env: Env = {\r\n    _nativeScrollbarsSize: nativeScrollbarsSize,\r\n    _nativeScrollbarsOverlaid: nativeScrollbarsOverlaid,\r\n    _nativeScrollbarsHiding: nativeScrollbarsHiding,\r\n    _scrollTimeline: !!scrollT,\r\n    _addResizeListener: bind(addEvent, 'r'),\r\n    _getDefaultInitialization: getDefaultInitialization,\r\n    _setDefaultInitialization: (newInitializationStrategy) =>\r\n      assignDeep(staticDefaultInitialization, newInitializationStrategy) &&\r\n      getDefaultInitialization(),\r\n    _getDefaultOptions: getDefaultOptions,\r\n    _setDefaultOptions: (newDefaultOptions) =>\r\n      assignDeep(staticDefaultOptions, newDefaultOptions) && getDefaultOptions(),\r\n    _staticDefaultInitialization: assignDeep({}, staticDefaultInitialization),\r\n    _staticDefaultOptions: assignDeep({}, staticDefaultOptions),\r\n  };\r\n\r\n  removeAttrs(envElm, 'style');\r\n  removeElements(envElm);\r\n\r\n  // needed in case content has css viewport units\r\n  addEventListener(wnd, 'resize', () => {\r\n    triggerEvent('r', []);\r\n  });\r\n\r\n  if (\r\n    isFunction(wnd.matchMedia) &&\r\n    !nativeScrollbarsHiding &&\r\n    (!nativeScrollbarsOverlaid.x || !nativeScrollbarsOverlaid.y)\r\n  ) {\r\n    const addZoomListener = (onZoom: () => void) => {\r\n      const media = wnd.matchMedia(`(resolution: ${wnd.devicePixelRatio}dppx)`);\r\n      addEventListener(\r\n        media,\r\n        'change',\r\n        () => {\r\n          onZoom();\r\n          addZoomListener(onZoom);\r\n        },\r\n        {\r\n          _once: true,\r\n        }\r\n      );\r\n    };\r\n    addZoomListener(() => {\r\n      const [updatedNativeScrollbarSize, nativeScrollbarSizeChanged] =\r\n        updateNativeScrollbarSizeCache();\r\n\r\n      assignDeep(env._nativeScrollbarsSize, updatedNativeScrollbarSize); // keep the object and just re-assign!\r\n      triggerEvent('r', [nativeScrollbarSizeChanged]);\r\n    });\r\n  }\r\n\r\n  return env;\r\n};\r\n\r\nexport const getEnvironment = (): Env => {\r\n  if (!environmentInstance) {\r\n    environmentInstance = createEnvironment();\r\n  }\r\n  return environmentInstance;\r\n};\r\n", "import {\r\n  each,\r\n  noop,\r\n  debounce,\r\n  MutationObserverConstructor,\r\n  addEventListener,\r\n  is,\r\n  find,\r\n  push,\r\n  runEachAndClear,\r\n  bind,\r\n  isEmptyArray,\r\n  deduplicateArray,\r\n  inArray,\r\n  concat,\r\n  getAttr,\r\n  isString,\r\n} from '../support';\r\n\r\ntype DOMContentObserverCallback = (contentChangedThroughEvent: boolean) => any;\r\n\r\ntype DOMTargetObserverCallback = (targetChangedAttrs: string[], targetStyleChanged: boolean) => any;\r\n\r\ninterface DOMObserverOptionsBase {\r\n  _attributes?: string[];\r\n  /**\r\n   * A function which can ignore a changed attribute if it returns true.\r\n   * for DOMTargetObserver this applies to the changes to the observed target\r\n   * for DOMContentObserver this applies to changes to nested targets -> nested targets are elements which match the \"_nestedTargetSelector\" selector\r\n   */\r\n  _ignoreTargetChange?: DOMObserverIgnoreTargetChange;\r\n}\r\n\r\ninterface DOMContentObserverOptions extends DOMObserverOptionsBase {\r\n  _eventContentChange?: DOMObserverEventContentChange; // [selector, eventname(s) | function returning eventname(s)] -> eventnames divided by whitespaces\r\n  _nestedTargetSelector?: string;\r\n  _ignoreContentChange?: DOMObserverIgnoreContentChange; // function which will prevent marking certain dom changes as content change if it returns true\r\n}\r\n\r\ninterface DOMTargetObserverOptions extends DOMObserverOptionsBase {\r\n  /**\r\n   * Marks certain attributes as style changing, should be a subset of the _attributes prop.\r\n   * Used to set the \"targetStyleChanged\" param in the DOMTargetObserverCallback.\r\n   */\r\n  _styleChangingAttributes?: string[] | readonly string[];\r\n}\r\n\r\ntype ContentChangeArrayItem = [selector?: string, eventNames?: string] | null | undefined;\r\n\r\nexport type DOMObserverEventContentChange =\r\n  | Array<ContentChangeArrayItem>\r\n  | false\r\n  | null\r\n  | undefined;\r\n\r\nexport type DOMObserverIgnoreContentChange = (\r\n  mutation: MutationRecord,\r\n  isNestedTarget: boolean,\r\n  domObserverTarget: HTMLElement,\r\n  domObserverOptions?: DOMContentObserverOptions\r\n) => boolean;\r\n\r\nexport type DOMObserverIgnoreTargetChange = (\r\n  target: Node,\r\n  attributeName: string,\r\n  oldAttributeValue: string | null,\r\n  newAttributeValue: string | null\r\n) => boolean;\r\n\r\nexport type DOMObserverCallback<ContentObserver extends boolean> = ContentObserver extends true\r\n  ? DOMContentObserverCallback\r\n  : DOMTargetObserverCallback;\r\n\r\nexport type DOMObserverOptions<ContentObserver extends boolean> = ContentObserver extends true\r\n  ? DOMContentObserverOptions\r\n  : DOMTargetObserverOptions;\r\n\r\nexport type DOMObserver<ContentObserver extends boolean> = [\r\n  construct: () => () => void,\r\n  update: () => void | false | Parameters<DOMObserverCallback<ContentObserver>>,\r\n];\r\n\r\ntype EventContentChangeUpdateElement = (\r\n  getElements?: (selector: string) => Node[],\r\n  removed?: boolean\r\n) => void;\r\ntype EventContentChange = [destroy: () => void, updateElements: EventContentChangeUpdateElement];\r\n\r\n/**\r\n * Creates a set of helper functions to observe events of elements inside the target element.\r\n * @param target The target element of which the children elements shall be observed. (not only direct children but also nested ones)\r\n * @param eventContentChange The event content change array. (array of tuples: selector and eventname(s))\r\n * @param callback Callback which is called if one of the elements emits the corresponding event.\r\n * @returns A object which contains a set of helper functions to destroy and update the observation of elements.\r\n */\r\nconst createEventContentChange = (\r\n  target: HTMLElement,\r\n  callback: (...args: any) => any,\r\n  eventContentChange?: DOMObserverEventContentChange\r\n): EventContentChange => {\r\n  let destroyed = false;\r\n  const map = eventContentChange ? new WeakMap<Node, (() => any)[]>() : false; // weak map to prevent memory leak for detached elements\r\n  const destroy = () => {\r\n    destroyed = true;\r\n  };\r\n  const updateElements: EventContentChangeUpdateElement = (getElements) => {\r\n    if (map && eventContentChange) {\r\n      const eventElmList = eventContentChange.map((item) => {\r\n        const [selector, eventNames] = item || [];\r\n        const elements = eventNames && selector ? (getElements || find)(selector, target) : [];\r\n        return [elements, eventNames] as const;\r\n      });\r\n\r\n      each(eventElmList, (item) =>\r\n        each(item[0], (elm) => {\r\n          const eventNames = item[1];\r\n          const entries = map.get(elm) || [];\r\n          const isTargetChild = target.contains(elm);\r\n\r\n          if (isTargetChild && eventNames) {\r\n            const removeListener = addEventListener(elm, eventNames, (event: Event) => {\r\n              if (destroyed) {\r\n                removeListener();\r\n                map.delete(elm);\r\n              } else {\r\n                callback(event);\r\n              }\r\n            });\r\n            map.set(elm, push(entries, removeListener));\r\n          } else {\r\n            runEachAndClear(entries);\r\n            map.delete(elm);\r\n          }\r\n        })\r\n      );\r\n    }\r\n  };\r\n\r\n  updateElements();\r\n\r\n  return [destroy, updateElements];\r\n};\r\n\r\n/**\r\n * Creates a DOM observer which observes DOM changes to either the target element or its children.\r\n * @param target The element which shall be observed.\r\n * @param isContentObserver Whether this observer is just observing the target or just the targets children. (not only direct children but also nested ones)\r\n * @param callback The callback which gets called if a change was detected.\r\n * @param options The options for DOM change detection.\r\n * @returns A object which represents the instance of the DOM observer.\r\n */\r\nexport const createDOMObserver = <ContentObserver extends boolean>(\r\n  target: HTMLElement,\r\n  isContentObserver: ContentObserver,\r\n  callback: DOMObserverCallback<ContentObserver>,\r\n  options?: DOMObserverOptions<ContentObserver>\r\n): DOMObserver<ContentObserver> => {\r\n  let isConnected = false;\r\n  const {\r\n    _attributes,\r\n    _styleChangingAttributes,\r\n    _eventContentChange,\r\n    _nestedTargetSelector,\r\n    _ignoreTargetChange,\r\n    _ignoreContentChange,\r\n  } = (options as DOMContentObserverOptions & DOMTargetObserverOptions) || {};\r\n  const debouncedEventContentChange = debounce(\r\n    () => isConnected && (callback as DOMContentObserverCallback)(true),\r\n    { _timeout: 33, _maxDelay: 99 }\r\n  );\r\n  const [destroyEventContentChange, updateEventContentChangeElements] = createEventContentChange(\r\n    target,\r\n    debouncedEventContentChange,\r\n    _eventContentChange\r\n  );\r\n\r\n  // MutationObserver\r\n  const finalAttributes = _attributes || [];\r\n  const finalStyleChangingAttributes = _styleChangingAttributes || [];\r\n  const observedAttributes = concat(finalAttributes, finalStyleChangingAttributes);\r\n  const observerCallback = (\r\n    fromRecords: boolean,\r\n    mutations: MutationRecord[]\r\n  ): void | Parameters<DOMObserverCallback<ContentObserver>> => {\r\n    if (!isEmptyArray(mutations)) {\r\n      const ignoreTargetChange = _ignoreTargetChange || noop;\r\n      const ignoreContentChange = _ignoreContentChange || noop;\r\n      const totalChangedNodes: Node[] = [];\r\n      const targetChangedAttrs: string[] = [];\r\n      let targetStyleChanged: boolean | '' | null | undefined = false;\r\n      let contentChanged: boolean | '' | null | undefined = false;\r\n      let childListChanged: boolean | '' | null | undefined = false;\r\n\r\n      each(mutations, (mutation) => {\r\n        const {\r\n          attributeName,\r\n          target: mutationTarget,\r\n          type,\r\n          oldValue,\r\n          addedNodes,\r\n          removedNodes,\r\n        } = mutation;\r\n        const isAttributesType = type === 'attributes';\r\n        const isChildListType = type === 'childList';\r\n        const targetIsMutationTarget = target === mutationTarget;\r\n        const isAttrChange = isAttributesType && attributeName;\r\n        const newValue =\r\n          isAttrChange && getAttr(mutationTarget as HTMLElement, attributeName || '');\r\n        // narrow down attributeValue type to `string` or `null` but don't overwrite `<empty string>` with `null`\r\n        const attributeValue = isString(newValue) ? newValue : null;\r\n        const attributeChanged = isAttrChange && oldValue !== attributeValue;\r\n        const styleChangingAttrChanged =\r\n          inArray(finalStyleChangingAttributes, attributeName) && attributeChanged;\r\n\r\n        // if is content observer and something changed in children\r\n        if (isContentObserver && (isChildListType || !targetIsMutationTarget)) {\r\n          const contentAttrChanged = isAttributesType && attributeChanged;\r\n          const isNestedTarget =\r\n            contentAttrChanged &&\r\n            _nestedTargetSelector &&\r\n            is(mutationTarget, _nestedTargetSelector);\r\n          const baseAssertion = isNestedTarget\r\n            ? !ignoreTargetChange(mutationTarget, attributeName, oldValue, attributeValue)\r\n            : !isAttributesType || contentAttrChanged;\r\n          const contentFinalChanged =\r\n            baseAssertion && !ignoreContentChange(mutation, !!isNestedTarget, target, options);\r\n\r\n          each(addedNodes, (node) => push(totalChangedNodes, node));\r\n          each(removedNodes, (node) => push(totalChangedNodes, node));\r\n\r\n          contentChanged = contentChanged || contentFinalChanged;\r\n          childListChanged = childListChanged || isChildListType;\r\n        }\r\n        // if is target observer and target attr changed\r\n        if (\r\n          !isContentObserver &&\r\n          targetIsMutationTarget &&\r\n          attributeChanged &&\r\n          !ignoreTargetChange(mutationTarget, attributeName!, oldValue, attributeValue)\r\n        ) {\r\n          push(targetChangedAttrs, attributeName);\r\n          targetStyleChanged = targetStyleChanged || styleChangingAttrChanged;\r\n        }\r\n      });\r\n\r\n      // adds / removes the new elements from the event content change\r\n      updateEventContentChangeElements((selector: string) =>\r\n        deduplicateArray(totalChangedNodes).reduce<Node[]>((arr, node) => {\r\n          push(arr, find(selector, node));\r\n          return is(node, selector) ? push(arr, node) : arr;\r\n        }, [])\r\n      );\r\n\r\n      if (isContentObserver) {\r\n        !fromRecords && contentChanged && (callback as DOMContentObserverCallback)(false);\r\n        return [false] satisfies Parameters<DOMObserverCallback<true>> as Parameters<\r\n          DOMObserverCallback<ContentObserver>\r\n        >;\r\n      }\r\n\r\n      if (!isEmptyArray(targetChangedAttrs) || targetStyleChanged) {\r\n        const args = [\r\n          deduplicateArray(targetChangedAttrs),\r\n          targetStyleChanged,\r\n        ] satisfies Parameters<DOMTargetObserverCallback> & Parameters<DOMObserverCallback<false>>;\r\n        !fromRecords && (callback as DOMTargetObserverCallback).apply(0, args);\r\n\r\n        return args as Parameters<DOMObserverCallback<ContentObserver>>;\r\n      }\r\n    }\r\n  };\r\n  const mutationObserver: MutationObserver = new MutationObserverConstructor!(\r\n    bind(observerCallback, false)\r\n  );\r\n\r\n  return [\r\n    () => {\r\n      mutationObserver.observe(target, {\r\n        attributes: true,\r\n        attributeOldValue: true,\r\n        attributeFilter: observedAttributes,\r\n        subtree: isContentObserver,\r\n        childList: isContentObserver,\r\n        characterData: isContentObserver,\r\n      });\r\n      isConnected = true;\r\n\r\n      return () => {\r\n        if (isConnected) {\r\n          destroyEventContentChange();\r\n          mutationObserver.disconnect();\r\n          isConnected = false;\r\n        }\r\n      };\r\n    },\r\n    () => {\r\n      if (isConnected) {\r\n        debouncedEventContentChange._flush();\r\n        return observerCallback(true, mutationObserver.takeRecords());\r\n      }\r\n    },\r\n  ];\r\n};\r\n", "import type { SizeObserverPlugin } from '../plugins';\r\nimport {\r\n  createCache,\r\n  createDOM,\r\n  runEachAndClear,\r\n  addEventListener,\r\n  addClass,\r\n  push,\r\n  ResizeObserverConstructor,\r\n  appendChildren,\r\n  domRectHasDimensions,\r\n  bind,\r\n  noop,\r\n  domRectAppeared,\r\n  concat,\r\n} from '../support';\r\nimport {\r\n  classNameSizeObserver,\r\n  classNameSizeObserverAppear,\r\n  classNameSizeObserverListener,\r\n} from '../classnames';\r\nimport { getStaticPluginModuleInstance, sizeObserverPluginName } from '../plugins';\r\n\r\nexport interface SizeObserverOptions {\r\n  /** Whether appearing should be observed. */\r\n  _appear?: boolean;\r\n}\r\n\r\nexport interface SizeObserverCallbackParams {\r\n  _sizeChanged: boolean;\r\n  _appear?: boolean;\r\n}\r\n\r\nexport type SizeObserver = () => () => void;\r\n\r\n/**\r\n * Creates a size observer which observes any size, padding, border, margin and box-sizing changes of the target element. Depending on the options also direction and appear can be observed.\r\n * @param target The target element which shall be observed.\r\n * @param onSizeChangedCallback The callback which gets called after a size change was detected.\r\n * @param options The options for size detection, whether to observe also direction and appear.\r\n * @returns A object which represents the instance of the size observer.\r\n */\r\nexport const createSizeObserver = (\r\n  target: HTMLElement,\r\n  onSizeChangedCallback: (params: SizeObserverCallbackParams) => any,\r\n  options?: SizeObserverOptions\r\n): SizeObserver => {\r\n  const { _appear: observeAppearChange } = options || {};\r\n  const sizeObserverPlugin =\r\n    getStaticPluginModuleInstance<typeof SizeObserverPlugin>(sizeObserverPluginName);\r\n  const [updateResizeObserverContentRectCache] = createCache<DOMRectReadOnly | false>({\r\n    _initialValue: false,\r\n    _alwaysUpdateValues: true,\r\n  });\r\n\r\n  return () => {\r\n    const destroyFns: (() => void)[] = [];\r\n    const baseElements = createDOM(\r\n      `<div class=\"${classNameSizeObserver}\"><div class=\"${classNameSizeObserverListener}\"></div></div>`\r\n    );\r\n    const sizeObserver = baseElements[0] as HTMLElement;\r\n    const listenerElement = sizeObserver.firstChild as HTMLElement;\r\n    const onSizeChangedCallbackProxy = (sizeChangedContext?: ResizeObserverEntry | boolean) => {\r\n      const isResizeObserverCall = sizeChangedContext instanceof ResizeObserverEntry;\r\n\r\n      let skip = false;\r\n      let appear = false;\r\n\r\n      // if triggered from RO.\r\n      if (isResizeObserverCall) {\r\n        const [currRContentRect, , prevContentRect] = updateResizeObserverContentRectCache(\r\n          sizeChangedContext.contentRect\r\n        );\r\n        const hasDimensions = domRectHasDimensions(currRContentRect);\r\n        appear = domRectAppeared(currRContentRect, prevContentRect);\r\n        skip = !appear && !hasDimensions; // skip if display is none or when window resize\r\n      }\r\n      // else if it triggered with appear from polyfill\r\n      else {\r\n        appear = sizeChangedContext === true;\r\n      }\r\n\r\n      if (!skip) {\r\n        onSizeChangedCallback({\r\n          _sizeChanged: true,\r\n          _appear: appear,\r\n        });\r\n      }\r\n    };\r\n\r\n    if (ResizeObserverConstructor) {\r\n      const resizeObserverInstance = new ResizeObserverConstructor((entries) =>\r\n        onSizeChangedCallbackProxy(entries.pop())\r\n      );\r\n      resizeObserverInstance.observe(listenerElement);\r\n      push(destroyFns, () => {\r\n        resizeObserverInstance.disconnect();\r\n      });\r\n    } else if (sizeObserverPlugin) {\r\n      const [pluginAppearCallback, pluginDestroyFns] = sizeObserverPlugin(\r\n        listenerElement,\r\n        onSizeChangedCallbackProxy,\r\n        observeAppearChange\r\n      );\r\n      push(\r\n        destroyFns,\r\n        concat(\r\n          [\r\n            addClass(sizeObserver, classNameSizeObserverAppear),\r\n            addEventListener(sizeObserver, 'animationstart', pluginAppearCallback),\r\n          ],\r\n          pluginDestroyFns\r\n        )\r\n      );\r\n    } else {\r\n      return noop;\r\n    }\r\n\r\n    return bind(runEachAndClear, push(destroyFns, appendChildren(target, sizeObserver)));\r\n  };\r\n};\r\n", "import type { WH, CacheValues } from '../support';\r\nimport { createSizeObserver } from './sizeObserver';\r\nimport { classNameTrinsicObserver } from '../classnames';\r\nimport {\r\n  createDiv,\r\n  getOffsetSize,\r\n  runEachAndClear,\r\n  createCache,\r\n  push,\r\n  IntersectionObserverConstructor,\r\n  appendChildren,\r\n  bind,\r\n} from '../support';\r\n\r\nexport type TrinsicObserverCallback = (heightIntrinsic: CacheValues<boolean>) => any;\r\nexport type TrinsicObserver = [\r\n  construct: () => () => void,\r\n  update: () => void | false | null | undefined | Parameters<TrinsicObserverCallback>,\r\n];\r\n\r\n/**\r\n * Creates a trinsic observer which observes changes to intrinsic or extrinsic sizing for the height of the target element.\r\n * @param target The element which shall be observed.\r\n * @param onTrinsicChangedCallback The callback which gets called after a change was detected.\r\n * @returns A object which represents the instance of the trinsic observer.\r\n */\r\nexport const createTrinsicObserver = (\r\n  target: HTMLElement,\r\n  onTrinsicChangedCallback: TrinsicObserverCallback\r\n): TrinsicObserver => {\r\n  let intersectionObserverInstance: undefined | IntersectionObserver;\r\n  const isHeightIntrinsic = (ioEntryOrSize: IntersectionObserverEntry | WH<number>): boolean =>\r\n    (ioEntryOrSize as WH<number>).h === 0 ||\r\n    (ioEntryOrSize as IntersectionObserverEntry).isIntersecting ||\r\n    (ioEntryOrSize as IntersectionObserverEntry).intersectionRatio > 0;\r\n  const trinsicObserver = createDiv(classNameTrinsicObserver);\r\n  const [updateHeightIntrinsicCache] = createCache({\r\n    _initialValue: false,\r\n  });\r\n  const triggerOnTrinsicChangedCallback = (\r\n    updateValue: IntersectionObserverEntry | WH<number> | undefined,\r\n    fromRecords?: boolean\r\n  ): void | Parameters<TrinsicObserverCallback> => {\r\n    if (updateValue) {\r\n      const heightIntrinsic = updateHeightIntrinsicCache(isHeightIntrinsic(updateValue));\r\n      const [, heightIntrinsicChanged] = heightIntrinsic;\r\n      return (\r\n        heightIntrinsicChanged &&\r\n        !fromRecords &&\r\n        onTrinsicChangedCallback(heightIntrinsic) && [heightIntrinsic]\r\n      );\r\n    }\r\n  };\r\n  const intersectionObserverCallback = (\r\n    fromRecords: boolean,\r\n    entries: IntersectionObserverEntry[]\r\n  ) => triggerOnTrinsicChangedCallback(entries.pop(), fromRecords);\r\n\r\n  return [\r\n    () => {\r\n      const destroyFns: (() => void)[] = [];\r\n\r\n      if (IntersectionObserverConstructor) {\r\n        intersectionObserverInstance = new IntersectionObserverConstructor(\r\n          bind(intersectionObserverCallback, false),\r\n          { root: target }\r\n        );\r\n        intersectionObserverInstance.observe(trinsicObserver);\r\n        push(destroyFns, () => {\r\n          intersectionObserverInstance!.disconnect();\r\n        });\r\n      } else {\r\n        const onSizeChanged = () => {\r\n          const newSize = getOffsetSize(trinsicObserver);\r\n          triggerOnTrinsicChangedCallback(newSize);\r\n        };\r\n        push(destroyFns, createSizeObserver(trinsicObserver, onSizeChanged)());\r\n        onSizeChanged();\r\n      }\r\n\r\n      return bind(runEachAndClear, push(destroyFns, appendChildren(target, trinsicObserver)));\r\n    },\r\n    () =>\r\n      intersectionObserverInstance &&\r\n      intersectionObserverCallback(true, intersectionObserverInstance.takeRecords()),\r\n  ];\r\n};\r\n", "import type { Options, OptionsCheckFn } from '../../options';\r\nimport type { ScrollbarsHidingPlugin } from '../../plugins';\r\nimport type { SizeObserverCallbackParams } from '../../observers';\r\nimport type { StructureSetupElementsObj } from '../structureSetup/structureSetup.elements';\r\nimport type { Setup, SetupUpdateInfo, StructureSetupState } from '../../setups';\r\nimport type { CacheValues, WH } from '../../support';\r\nimport type { PlainObject } from '../../typings';\r\nimport { getStaticPluginModuleInstance, scrollbarsHidingPluginName } from '../../plugins';\r\nimport {\r\n  classNameScrollbar,\r\n  dataAttributeHost,\r\n  dataAttributeViewport,\r\n  dataValueViewportMeasuring,\r\n  dataValueViewportArrange,\r\n  dataValueNoClipping,\r\n} from '../../classnames';\r\nimport { getEnvironment } from '../../environment';\r\nimport { createDOMObserver, createSizeObserver, createTrinsicObserver } from '../../observers';\r\nimport {\r\n  ResizeObserverConstructor,\r\n  assignDeep,\r\n  closest,\r\n  createCache,\r\n  debounce,\r\n  equalWH,\r\n  getFractionalSize,\r\n  isArray,\r\n  isFunction,\r\n  isNumber,\r\n  keys,\r\n  liesBetween,\r\n  getScrollSize,\r\n  getElementScroll,\r\n  scrollElementTo,\r\n  domRectAppeared,\r\n  concat,\r\n  getStyles,\r\n  hasAttrClass,\r\n} from '../../support';\r\n\r\nexport interface ObserversSetupState {\r\n  _heightIntrinsic: boolean;\r\n  _directionIsRTL: boolean;\r\n}\r\n\r\nexport interface ObserversSetupUpdateInfo extends SetupUpdateInfo {\r\n  _takeRecords?: boolean;\r\n}\r\n\r\nexport type ObserversSetupUpdateHints = {\r\n  _sizeChanged?: boolean;\r\n  _directionChanged?: boolean;\r\n  _heightIntrinsicChanged?: boolean;\r\n  _hostMutation?: boolean;\r\n  _contentMutation?: boolean;\r\n  _appear?: boolean;\r\n  _scrollbarSizeChanged?: boolean;\r\n};\r\n\r\nexport type ObserversSetup = Setup<\r\n  ObserversSetupUpdateInfo,\r\n  ObserversSetupState,\r\n  ObserversSetupUpdateHints\r\n>;\r\n\r\nexport const createObserversSetup = (\r\n  structureSetupElements: StructureSetupElementsObj,\r\n  structureSetupState: StructureSetupState,\r\n  getCurrentOption: OptionsCheckFn<Options>,\r\n  onObserversUpdated: (updateHints: ObserversSetupUpdateHints) => void\r\n): ObserversSetup => {\r\n  let debounceTimeout: number | false | undefined;\r\n  let debounceMaxDelay: number | false | undefined;\r\n  let updateContentMutationObserver: (() => void) | undefined;\r\n  let destroyContentMutationObserver: (() => void) | undefined;\r\n  let prevContentRect: DOMRectReadOnly | undefined;\r\n  let prevDirectionIsRTL: boolean | undefined;\r\n  const hostSelector = `[${dataAttributeHost}]`;\r\n\r\n  // TODO: observer textarea attrs if textarea\r\n\r\n  const viewportSelector = `[${dataAttributeViewport}]`;\r\n  const baseStyleChangingAttrs = ['id', 'class', 'style', 'open', 'wrap', 'cols', 'rows'];\r\n  const {\r\n    _target,\r\n    _host,\r\n    _viewport,\r\n    _scrollOffsetElement,\r\n    _content,\r\n    _viewportIsTarget,\r\n    _isBody,\r\n    _viewportHasClass,\r\n    _viewportAddRemoveClass,\r\n    _removeScrollObscuringStyles,\r\n  } = structureSetupElements;\r\n\r\n  const getDirectionIsRTL = (elm: HTMLElement): boolean => getStyles(elm, 'direction') === 'rtl';\r\n\r\n  const state: ObserversSetupState = {\r\n    _heightIntrinsic: false,\r\n    _directionIsRTL: getDirectionIsRTL(_target),\r\n  };\r\n  const env = getEnvironment();\r\n  const scrollbarsHidingPlugin = getStaticPluginModuleInstance<typeof ScrollbarsHidingPlugin>(\r\n    scrollbarsHidingPluginName\r\n  );\r\n\r\n  const [updateContentSizeCache] = createCache<WH<number>>(\r\n    {\r\n      _equal: equalWH,\r\n      _initialValue: { w: 0, h: 0 },\r\n    },\r\n    () => {\r\n      const _undoViewportArrange =\r\n        scrollbarsHidingPlugin &&\r\n        scrollbarsHidingPlugin._viewportArrangement(\r\n          structureSetupElements,\r\n          structureSetupState,\r\n          state,\r\n          env,\r\n          getCurrentOption\r\n        )._undoViewportArrange;\r\n\r\n      const viewportIsTargetBody = _isBody && _viewportIsTarget;\r\n      const noClipping =\r\n        !viewportIsTargetBody && hasAttrClass(_host, dataAttributeHost, dataValueNoClipping);\r\n      const isArranged = !_viewportIsTarget && _viewportHasClass(dataValueViewportArrange);\r\n      const scrollOffset = isArranged && getElementScroll(_scrollOffsetElement);\r\n      const revertScrollObscuringStyles = scrollOffset && _removeScrollObscuringStyles();\r\n\r\n      const revertMeasuring = _viewportAddRemoveClass(dataValueViewportMeasuring, noClipping);\r\n      const redoViewportArrange = isArranged && _undoViewportArrange && _undoViewportArrange()[0];\r\n      const viewportScroll = getScrollSize(_viewport);\r\n      const fractional = getFractionalSize(_viewport);\r\n\r\n      redoViewportArrange && redoViewportArrange();\r\n\r\n      scrollElementTo(_scrollOffsetElement, scrollOffset);\r\n      revertScrollObscuringStyles && revertScrollObscuringStyles();\r\n      noClipping && revertMeasuring();\r\n\r\n      return {\r\n        w: viewportScroll.w + fractional.w,\r\n        h: viewportScroll.h + fractional.h,\r\n      };\r\n    }\r\n  );\r\n\r\n  const onObserversUpdatedDebounced = debounce(onObserversUpdated, {\r\n    _timeout: () => debounceTimeout,\r\n    _maxDelay: () => debounceMaxDelay,\r\n    _mergeParams(prev, curr) {\r\n      const [prevObj] = prev;\r\n      const [currObj] = curr;\r\n      return [\r\n        concat(keys(prevObj), keys(currObj)).reduce((obj, key) => {\r\n          obj[key] = prevObj[key as keyof typeof prevObj] || currObj[key as keyof typeof currObj];\r\n          return obj;\r\n        }, {} as PlainObject),\r\n      ] as [Partial<ObserversSetupUpdateHints>];\r\n    },\r\n  });\r\n\r\n  const setDirection = (updateHints: ObserversSetupUpdateHints) => {\r\n    const newDirectionIsRTL = getDirectionIsRTL(_target);\r\n    assignDeep(updateHints, { _directionChanged: prevDirectionIsRTL !== newDirectionIsRTL });\r\n    assignDeep(state, { _directionIsRTL: newDirectionIsRTL });\r\n    prevDirectionIsRTL = newDirectionIsRTL;\r\n  };\r\n\r\n  const onTrinsicChanged = (\r\n    heightIntrinsicCache: CacheValues<boolean>,\r\n    fromRecords?: true\r\n  ): ObserversSetupUpdateHints => {\r\n    const [heightIntrinsic, heightIntrinsicChanged] = heightIntrinsicCache;\r\n    const updateHints: ObserversSetupUpdateHints = {\r\n      _heightIntrinsicChanged: heightIntrinsicChanged,\r\n    };\r\n\r\n    assignDeep(state, { _heightIntrinsic: heightIntrinsic });\r\n    !fromRecords && onObserversUpdated(updateHints);\r\n\r\n    return updateHints;\r\n  };\r\n\r\n  const onSizeChanged = ({ _sizeChanged, _appear }: SizeObserverCallbackParams) => {\r\n    const exclusiveSizeChange = _sizeChanged && !_appear;\r\n    const updateFn =\r\n      // use debounceed update:\r\n      // if native scrollbars hiding is supported\r\n      // and if the update is more than just a exclusive sizeChange (e.g. size change + appear, or size change + direction)\r\n      !exclusiveSizeChange && env._nativeScrollbarsHiding\r\n        ? onObserversUpdatedDebounced\r\n        : onObserversUpdated;\r\n\r\n    const updateHints: ObserversSetupUpdateHints = {\r\n      _sizeChanged: _sizeChanged || _appear,\r\n      _appear,\r\n    };\r\n\r\n    setDirection(updateHints);\r\n\r\n    updateFn(updateHints);\r\n  };\r\n\r\n  const onContentMutation = (\r\n    contentChangedThroughEvent: boolean,\r\n    fromRecords?: true\r\n  ): ObserversSetupUpdateHints => {\r\n    const [, _contentMutation] = updateContentSizeCache();\r\n    const updateHints: ObserversSetupUpdateHints = {\r\n      _contentMutation,\r\n    };\r\n\r\n    setDirection(updateHints);\r\n\r\n    // if contentChangedThroughEvent is true its already debounced\r\n    const updateFn = contentChangedThroughEvent ? onObserversUpdated : onObserversUpdatedDebounced;\r\n\r\n    _contentMutation && !fromRecords && updateFn(updateHints);\r\n\r\n    return updateHints;\r\n  };\r\n\r\n  const onHostMutation = (\r\n    targetChangedAttrs: string[],\r\n    targetStyleChanged: boolean,\r\n    fromRecords?: true\r\n  ): ObserversSetupUpdateHints => {\r\n    const updateHints: ObserversSetupUpdateHints = {\r\n      _hostMutation: targetStyleChanged,\r\n    };\r\n\r\n    setDirection(updateHints);\r\n\r\n    if (targetStyleChanged && !fromRecords) {\r\n      onObserversUpdatedDebounced(updateHints);\r\n    }\r\n    /*\r\n    else if (!_viewportIsTarget) {\r\n      updateViewportAttrsFromHost(targetChangedAttrs);\r\n    }\r\n    */\r\n\r\n    return updateHints;\r\n  };\r\n\r\n  const [constructTrinsicObserver, updateTrinsicObserver] = _content\r\n    ? createTrinsicObserver(_host, onTrinsicChanged)\r\n    : [];\r\n\r\n  const constructSizeObserver =\r\n    !_viewportIsTarget &&\r\n    createSizeObserver(_host, onSizeChanged, {\r\n      _appear: true,\r\n    });\r\n\r\n  const [constructHostMutationObserver, updateHostMutationObserver] = createDOMObserver(\r\n    _host,\r\n    false,\r\n    onHostMutation,\r\n    {\r\n      _styleChangingAttributes: baseStyleChangingAttrs,\r\n      _attributes: baseStyleChangingAttrs,\r\n    }\r\n  );\r\n\r\n  const viewportIsTargetResizeObserver =\r\n    _viewportIsTarget &&\r\n    ResizeObserverConstructor &&\r\n    new ResizeObserverConstructor((entries) => {\r\n      const currContentRect = entries[entries.length - 1].contentRect;\r\n      onSizeChanged({\r\n        _sizeChanged: true,\r\n        _appear: domRectAppeared(currContentRect, prevContentRect),\r\n      });\r\n      prevContentRect = currContentRect;\r\n    });\r\n  const onWindowResizeDebounced = debounce(\r\n    () => {\r\n      const [, _contentMutation] = updateContentSizeCache();\r\n      onObserversUpdated({ _contentMutation });\r\n    },\r\n    {\r\n      _timeout: 222,\r\n      _leading: true,\r\n    }\r\n  );\r\n\r\n  return [\r\n    () => {\r\n      // order is matter!\r\n      // updateViewportAttrsFromHost();\r\n      viewportIsTargetResizeObserver && viewportIsTargetResizeObserver.observe(_host);\r\n      const destroySizeObserver = constructSizeObserver && constructSizeObserver();\r\n      const destroyTrinsicObserver = constructTrinsicObserver && constructTrinsicObserver();\r\n      const destroyHostMutationObserver = constructHostMutationObserver();\r\n      const removeResizeListener = env._addResizeListener((_scrollbarSizeChanged) => {\r\n        if (_scrollbarSizeChanged) {\r\n          onObserversUpdatedDebounced({ _scrollbarSizeChanged });\r\n        } else {\r\n          onWindowResizeDebounced();\r\n        }\r\n      });\r\n\r\n      return () => {\r\n        viewportIsTargetResizeObserver && viewportIsTargetResizeObserver.disconnect();\r\n        destroySizeObserver && destroySizeObserver();\r\n        destroyTrinsicObserver && destroyTrinsicObserver();\r\n        destroyContentMutationObserver && destroyContentMutationObserver();\r\n        destroyHostMutationObserver();\r\n        removeResizeListener();\r\n      };\r\n    },\r\n    ({ _checkOption, _takeRecords, _force }) => {\r\n      const updateHints: ObserversSetupUpdateHints = {};\r\n\r\n      const [ignoreMutation] = _checkOption('update.ignoreMutation');\r\n      const [attributes, attributesChanged] = _checkOption('update.attributes');\r\n      const [elementEvents, elementEventsChanged] = _checkOption('update.elementEvents');\r\n      const [debounceValue, debounceChanged] = _checkOption('update.debounce');\r\n      const contentMutationObserverChanged = elementEventsChanged || attributesChanged;\r\n      const takeRecords = _takeRecords || _force;\r\n      const ignoreMutationFromOptions = (mutation: MutationRecord) =>\r\n        isFunction(ignoreMutation) && ignoreMutation(mutation);\r\n\r\n      if (contentMutationObserverChanged) {\r\n        updateContentMutationObserver && updateContentMutationObserver();\r\n        destroyContentMutationObserver && destroyContentMutationObserver();\r\n\r\n        const [construct, update] = createDOMObserver(\r\n          _content || _viewport,\r\n          true,\r\n          onContentMutation,\r\n          {\r\n            _attributes: concat(baseStyleChangingAttrs, attributes || []),\r\n            _eventContentChange: elementEvents,\r\n            _nestedTargetSelector: hostSelector,\r\n            _ignoreContentChange: (mutation, isNestedTarget) => {\r\n              const { target: mutationTarget, attributeName } = mutation;\r\n              const ignore =\r\n                !isNestedTarget && attributeName && !_viewportIsTarget\r\n                  ? liesBetween(mutationTarget, hostSelector, viewportSelector)\r\n                  : false;\r\n              return (\r\n                ignore ||\r\n                !!closest(mutationTarget, `.${classNameScrollbar}`) || // ignore explicitely all scrollbar elements\r\n                !!ignoreMutationFromOptions(mutation)\r\n              );\r\n            },\r\n          }\r\n        );\r\n\r\n        destroyContentMutationObserver = construct();\r\n        updateContentMutationObserver = update;\r\n      }\r\n\r\n      if (debounceChanged) {\r\n        onObserversUpdatedDebounced._flush();\r\n        if (isArray(debounceValue)) {\r\n          const timeout = debounceValue[0];\r\n          const maxWait = debounceValue[1];\r\n          debounceTimeout = isNumber(timeout) && timeout;\r\n          debounceMaxDelay = isNumber(maxWait) && maxWait;\r\n        } else if (isNumber(debounceValue)) {\r\n          debounceTimeout = debounceValue;\r\n          debounceMaxDelay = false;\r\n        } else {\r\n          debounceTimeout = false;\r\n          debounceMaxDelay = false;\r\n        }\r\n      }\r\n\r\n      if (takeRecords) {\r\n        const hostUpdateResult = updateHostMutationObserver();\r\n        const trinsicUpdateResult = updateTrinsicObserver && updateTrinsicObserver();\r\n        const contentUpdateResult =\r\n          updateContentMutationObserver && updateContentMutationObserver();\r\n\r\n        hostUpdateResult &&\r\n          assignDeep(\r\n            updateHints,\r\n            onHostMutation(hostUpdateResult[0], hostUpdateResult[1], takeRecords)\r\n          );\r\n\r\n        trinsicUpdateResult &&\r\n          assignDeep(updateHints, onTrinsicChanged(trinsicUpdateResult[0], takeRecords));\r\n\r\n        contentUpdateResult &&\r\n          assignDeep(updateHints, onContentMutation(contentUpdateResult[0], takeRecords));\r\n      }\r\n\r\n      setDirection(updateHints);\r\n\r\n      return updateHints;\r\n    },\r\n    state,\r\n  ];\r\n};\r\n", "import type { DeepPartial } from './typings';\r\nimport { isFunction, isHTMLElement, isNull, isUndefined } from './support';\r\nimport { getEnvironment } from './environment';\r\n\r\ntype FallbackStaticInitializtationElement<Args extends any[]> =\r\n  Extract<StaticInitializationElement<Args>, (...args: Args) => any> extends (\r\n    ...args: infer P\r\n  ) => any\r\n    ? (...args: P) => HTMLElement\r\n    : never;\r\ntype FallbackDynamicInitializtationElement<Args extends any[]> =\r\n  Extract<DynamicInitializationElement<Args>, (...args: Args) => any> extends (\r\n    ...args: infer P\r\n  ) => any\r\n    ? (...args: P) => HTMLElement\r\n    : never;\r\n\r\nexport type StaticInitialization = HTMLElement | false | null;\r\nexport type DynamicInitialization = HTMLElement | boolean | null;\r\n\r\n/**\r\n * Static elements are elements which MUST be present in the final DOM.\r\n * If an `HTMLElement` is passed the passed element will be taken as the repsective element.\r\n * With `false`, `null` or `undefined` an appropriate element is generated automatically.\r\n */\r\nexport type StaticInitializationElement<Args extends any[]> =\r\n  /** A function which returns the the StaticInitialization value. */\r\n  | ((...args: Args) => StaticInitialization)\r\n  /** The StaticInitialization value. */\r\n  | StaticInitialization;\r\n\r\n/**\r\n * Dynamic elements are elements which CAN be present in the final DOM.\r\n * If an `HTMLElement`is passed the passed element will be taken as the repsective element.\r\n * With `true` an appropriate element is generated automatically.\r\n * With `false`, `null` or `undefined` the element won't be in the DOM.\r\n */\r\nexport type DynamicInitializationElement<Args extends any[]> =\r\n  /** A function which returns the the DynamicInitialization value. */\r\n  | ((...args: Args) => DynamicInitialization)\r\n  /** The DynamicInitialization value. */\r\n  | DynamicInitialization;\r\n\r\n/**\r\n * Describes how a OverlayScrollbar instance should initialize.\r\n */\r\nexport type Initialization = {\r\n  /**\r\n   * Customizes which elements are generated and used.\r\n   * If a function is passed to any of the fields, it receives the `target` element as its argument.\r\n   * Any passed function should be a \"pure\" function. (same input produces same output)\r\n   */\r\n  elements: {\r\n    /**\r\n     * Assign a custom element as the host element.\r\n     * Only relevant if the target element is a Textarea.\r\n     */\r\n    host: StaticInitializationElement<[target: InitializationTargetElement]>;\r\n    /** Assign a custom element as the viewport element. */\r\n    viewport: StaticInitializationElement<[target: InitializationTargetElement]>;\r\n    /** Assign a custom element as the padding element or force the element not to be generated. */\r\n    padding: DynamicInitializationElement<[target: InitializationTargetElement]>;\r\n    /** Assign a custom element as the content element or force the element not to be generated. */\r\n    content: DynamicInitializationElement<[target: InitializationTargetElement]>;\r\n  };\r\n  /**\r\n   * Customizes elements related to the scrollbars.\r\n   * If a function is passed, it receives the `target`, `host` and `viewport` element as arguments.\r\n   */\r\n  scrollbars: {\r\n    slot: DynamicInitializationElement<\r\n      [target: InitializationTargetElement, host: HTMLElement, viewport: HTMLElement]\r\n    >;\r\n  };\r\n  /**\r\n   * Customizes the cancelation behavior.\r\n   */\r\n  cancel: {\r\n    /** Whether the initialization shall be canceled if the native scrollbars are overlaid. */\r\n    nativeScrollbarsOverlaid: boolean;\r\n    /**\r\n     * Whether the initialization shall be canceled if its applied to a body element.\r\n     * With `true` an initialization is always canceled, with `false` its never canceled.\r\n     * With `null` the initialization will only be canceled when the initialization would affect the browsers functionality. (window.scrollTo, mobile browser behavior etc.)\r\n     */\r\n    body: boolean | null;\r\n  };\r\n};\r\n\r\nexport type PartialInitialization = DeepPartial<Initialization>;\r\n\r\n/** The initialization target element. */\r\nexport type InitializationTargetElement = HTMLElement; // | HTMLTextAreaElement;\r\n\r\n/**\r\n * The initialization target object.\r\n * OverlayScrollbars({ target: myElement }) is equivalent to OverlayScrollbars(myElement).\r\n */\r\nexport type InitializationTargetObject = PartialInitialization & {\r\n  target: InitializationTargetElement;\r\n};\r\n\r\n/** The initialization target. */\r\nexport type InitializationTarget = InitializationTargetElement | InitializationTargetObject;\r\n\r\nexport const resolveInitialization = <T extends StaticInitialization | DynamicInitialization>(\r\n  args: any,\r\n  value: any\r\n): T => (isFunction(value) ? value.apply(0, args) : value);\r\n\r\nexport const staticInitializationElement = <Args extends any[]>(\r\n  args: Args,\r\n  fallbackStaticInitializationElement: FallbackStaticInitializtationElement<Args>,\r\n  defaultStaticInitializationElement: StaticInitializationElement<Args>,\r\n  staticInitializationElementValue?: StaticInitializationElement<Args>\r\n): HTMLElement => {\r\n  const staticInitialization = isUndefined(staticInitializationElementValue)\r\n    ? defaultStaticInitializationElement\r\n    : staticInitializationElementValue;\r\n  const resolvedInitialization = resolveInitialization<StaticInitialization>(\r\n    args,\r\n    staticInitialization\r\n  );\r\n  return resolvedInitialization || fallbackStaticInitializationElement.apply(0, args);\r\n};\r\n\r\nexport const dynamicInitializationElement = <Args extends any[]>(\r\n  args: Args,\r\n  fallbackDynamicInitializationElement: FallbackDynamicInitializtationElement<Args>,\r\n  defaultDynamicInitializationElement: DynamicInitializationElement<Args>,\r\n  dynamicInitializationElementValue?: DynamicInitializationElement<Args>\r\n): HTMLElement | false => {\r\n  const dynamicInitialization = isUndefined(dynamicInitializationElementValue)\r\n    ? defaultDynamicInitializationElement\r\n    : dynamicInitializationElementValue;\r\n  const resolvedInitialization = resolveInitialization<DynamicInitialization>(\r\n    args,\r\n    dynamicInitialization\r\n  );\r\n  return (\r\n    !!resolvedInitialization &&\r\n    (isHTMLElement(resolvedInitialization)\r\n      ? resolvedInitialization\r\n      : fallbackDynamicInitializationElement.apply(0, args))\r\n  );\r\n};\r\n\r\nexport const cancelInitialization = (\r\n  isBody: boolean,\r\n  cancelInitializationValue?: DeepPartial<Initialization['cancel']> | false | null | undefined\r\n): boolean => {\r\n  const { nativeScrollbarsOverlaid, body } = cancelInitializationValue || {};\r\n  const { _nativeScrollbarsOverlaid, _nativeScrollbarsHiding, _getDefaultInitialization } =\r\n    getEnvironment();\r\n  const { nativeScrollbarsOverlaid: defaultNativeScrollbarsOverlaid, body: defaultbody } =\r\n    _getDefaultInitialization().cancel;\r\n\r\n  const resolvedNativeScrollbarsOverlaid =\r\n    nativeScrollbarsOverlaid ?? defaultNativeScrollbarsOverlaid;\r\n  const resolvedDocumentScrollingElement = isUndefined(body) ? defaultbody : body;\r\n\r\n  const finalNativeScrollbarsOverlaid =\r\n    (_nativeScrollbarsOverlaid.x || _nativeScrollbarsOverlaid.y) &&\r\n    resolvedNativeScrollbarsOverlaid;\r\n  const finalDocumentScrollingElement =\r\n    isBody &&\r\n    (isNull(resolvedDocumentScrollingElement)\r\n      ? !_nativeScrollbarsHiding\r\n      : resolvedDocumentScrollingElement);\r\n\r\n  return !!finalNativeScrollbarsOverlaid || !!finalDocumentScrollingElement;\r\n};\r\n", "import type { XY } from '../../support';\r\nimport type {\r\n  InitializationTarget,\r\n  InitializationTargetElement,\r\n  InitializationTargetObject,\r\n} from '../../initialization';\r\nimport type { StructureSetupElementsObj } from '../structureSetup/structureSetup.elements';\r\nimport type { ScrollbarsSetupEvents } from './scrollbarsSetup.events';\r\nimport type { StyleObject } from '../../typings';\r\nimport type { StructureSetupState } from '../structureSetup';\r\nimport { dynamicInitializationElement as generalDynamicInitializationElement } from '../../initialization';\r\nimport { getEnvironment } from '../../environment';\r\nimport {\r\n  classNameScrollbar,\r\n  classNameScrollbarHorizontal,\r\n  classNameScrollbarVertical,\r\n  classNameScrollbarTrack,\r\n  classNameScrollbarHandle,\r\n} from '../../classnames';\r\nimport {\r\n  addClass,\r\n  appendChildren,\r\n  createDiv,\r\n  each,\r\n  getTrasformTranslateValue,\r\n  isBoolean,\r\n  parent,\r\n  push,\r\n  removeClass,\r\n  removeElements,\r\n  runEachAndClear,\r\n  scrollT,\r\n  bind,\r\n  getElementScroll,\r\n  numberToCssPx,\r\n  setStyles,\r\n  capNumber,\r\n  getScrollCoordinatesPercent,\r\n  isDefaultDirectionScrollCoordinates,\r\n  roundCssNumber,\r\n} from '../../support';\r\n\r\nexport interface ScrollbarStructure {\r\n  _scrollbar: HTMLElement;\r\n  _track: HTMLElement;\r\n  _handle: HTMLElement;\r\n}\r\n\r\nexport interface ScrollbarsSetupElement {\r\n  _scrollbarStructures: ScrollbarStructure[];\r\n  _clone: () => ScrollbarStructure;\r\n  _style: (\r\n    elmStyle: (\r\n      scrollbarStructure: ScrollbarStructure\r\n    ) => [HTMLElement | false | null | undefined, StyleObject]\r\n  ) => void;\r\n}\r\n\r\nexport interface ScrollbarsSetupElementsObj {\r\n  _scrollbarsAddRemoveClass: (\r\n    classNames: string | false | null | undefined,\r\n    add?: boolean,\r\n    isHorizontal?: boolean\r\n  ) => void;\r\n  _refreshScrollbarsHandleLength: () => void;\r\n  _refreshScrollbarsHandleOffset: () => void;\r\n  _refreshScrollbarsScrollbarOffset: () => void;\r\n  _refreshScrollbarsScrollCoordinates: () => void;\r\n  _horizontal: ScrollbarsSetupElement;\r\n  _vertical: ScrollbarsSetupElement;\r\n}\r\n\r\nexport type ScrollbarsSetupElements = [\r\n  elements: ScrollbarsSetupElementsObj,\r\n  appendElements: () => () => void,\r\n];\r\n\r\ntype ScrollbarStyleFn = (\r\n  scrollbarStructure: ScrollbarStructure\r\n) => [HTMLElement | false | null | undefined, StyleObject | false | null | undefined];\r\n\r\nexport const createScrollbarsSetupElements = (\r\n  target: InitializationTarget,\r\n  structureSetupElements: StructureSetupElementsObj,\r\n  structureSetupState: StructureSetupState,\r\n  scrollbarsSetupEvents: ScrollbarsSetupEvents\r\n): ScrollbarsSetupElements => {\r\n  const cssCustomPropViewportPercent = '--os-viewport-percent';\r\n  const cssCustomPropScrollPercent = '--os-scroll-percent';\r\n  const cssCustomPropScrollDirection = '--os-scroll-direction';\r\n  const { _getDefaultInitialization } = getEnvironment();\r\n  const { scrollbars: defaultInitScrollbars } = _getDefaultInitialization();\r\n  const { slot: defaultInitScrollbarsSlot } = defaultInitScrollbars;\r\n  const {\r\n    _target,\r\n    _host,\r\n    _viewport,\r\n    _targetIsElm,\r\n    _scrollOffsetElement,\r\n    _isBody,\r\n    _viewportIsTarget,\r\n  } = structureSetupElements;\r\n  const { scrollbars: scrollbarsInit } = (_targetIsElm ? {} : target) as InitializationTargetObject;\r\n  const { slot: initScrollbarsSlot } = scrollbarsInit || {};\r\n  const destroyFns: (() => void)[] = [];\r\n  const horizontalScrollbars: ScrollbarStructure[] = [];\r\n  const verticalScrollbars: ScrollbarStructure[] = [];\r\n  const evaluatedScrollbarSlot = generalDynamicInitializationElement<\r\n    [InitializationTargetElement, HTMLElement, HTMLElement]\r\n  >(\r\n    [_target, _host, _viewport],\r\n    () => (_viewportIsTarget && _isBody ? _target : _host),\r\n    defaultInitScrollbarsSlot,\r\n    initScrollbarsSlot\r\n  );\r\n\r\n  const initScrollTimeline = (axis: keyof XY<unknown>) => {\r\n    if (scrollT) {\r\n      let currAnimation: Animation | null = null;\r\n      let currAnimationTransform: string[] = [];\r\n      const timeline = new scrollT({\r\n        source: _scrollOffsetElement,\r\n        axis,\r\n      });\r\n      const cancelAnimation = () => {\r\n        currAnimation && currAnimation.cancel();\r\n        currAnimation = null;\r\n      };\r\n      const _setScrollPercentAnimation = (structure: ScrollbarStructure) => {\r\n        const { _scrollCoordinates } = structureSetupState;\r\n        const defaultDirectionScroll =\r\n          isDefaultDirectionScrollCoordinates(_scrollCoordinates)[axis];\r\n        const isHorizontal = axis === 'x';\r\n        const transformArray = [\r\n          getTrasformTranslateValue(0, isHorizontal),\r\n          getTrasformTranslateValue(`calc(100cq${isHorizontal ? 'w' : 'h'} + -100%)`, isHorizontal),\r\n        ];\r\n        const transform = defaultDirectionScroll ? transformArray : transformArray.reverse();\r\n\r\n        if (\r\n          currAnimationTransform[0] === transform[0] &&\r\n          currAnimationTransform[1] === transform[1]\r\n        ) {\r\n          return cancelAnimation;\r\n        }\r\n\r\n        cancelAnimation();\r\n        currAnimationTransform = transform;\r\n        currAnimation = structure._handle.animate(\r\n          {\r\n            // dummy keyframe which fixes bug where the scrollbar handle is reverted to origin position when it should be at its max position\r\n            clear: ['left'],\r\n            // transform is a temporary fix for: https://github.com/KingSora/OverlayScrollbars/issues/705\r\n            // can be reverted to just animate \"cssCustomPropScrollPercent\" when browsers implement an optimization possibility\r\n            transform,\r\n            // [cssCustomPropScrollPercent]: [0, 1],\r\n          },\r\n          {\r\n            timeline,\r\n          }\r\n        );\r\n\r\n        return cancelAnimation;\r\n      };\r\n\r\n      return {\r\n        _setScrollPercentAnimation,\r\n      };\r\n    }\r\n  };\r\n  const scrollTimeline = {\r\n    x: initScrollTimeline('x'),\r\n    y: initScrollTimeline('y'),\r\n  };\r\n  const getViewportPercent = () => {\r\n    const { _overflowAmount, _overflowEdge } = structureSetupState;\r\n    const getAxisValue = (axisViewportSize: number, axisOverflowAmount: number) =>\r\n      capNumber(0, 1, axisViewportSize / (axisViewportSize + axisOverflowAmount) || 0);\r\n\r\n    return {\r\n      x: getAxisValue(_overflowEdge.x, _overflowAmount.x),\r\n      y: getAxisValue(_overflowEdge.y, _overflowAmount.y),\r\n    };\r\n  };\r\n  const scrollbarStructureAddRemoveClass = (\r\n    scrollbarStructures: ScrollbarStructure[],\r\n    classNames: string | false | null | undefined,\r\n    add?: boolean\r\n  ) => {\r\n    const action = add ? addClass : removeClass;\r\n    each(scrollbarStructures, (scrollbarStructure) => {\r\n      action(scrollbarStructure._scrollbar, classNames);\r\n    });\r\n  };\r\n  const scrollbarStyle = (\r\n    scrollbarStructures: ScrollbarStructure[],\r\n    elmStyle: ScrollbarStyleFn\r\n  ) => {\r\n    each(scrollbarStructures, (scrollbarStructure) => {\r\n      const [elm, styles] = elmStyle(scrollbarStructure);\r\n      setStyles(elm, styles);\r\n    });\r\n  };\r\n  const scrollbarsAddRemoveClass = (\r\n    className: string | false | null | undefined,\r\n    add?: boolean,\r\n    onlyHorizontal?: boolean\r\n  ) => {\r\n    const singleAxis = isBoolean(onlyHorizontal);\r\n    const runHorizontal = singleAxis ? onlyHorizontal : true;\r\n    const runVertical = singleAxis ? !onlyHorizontal : true;\r\n    runHorizontal && scrollbarStructureAddRemoveClass(horizontalScrollbars, className, add);\r\n    runVertical && scrollbarStructureAddRemoveClass(verticalScrollbars, className, add);\r\n  };\r\n  const refreshScrollbarsHandleLength = () => {\r\n    const viewportPercent = getViewportPercent();\r\n    const createScrollbarStyleFn =\r\n      (axisViewportPercent: number): ScrollbarStyleFn =>\r\n      (structure: ScrollbarStructure) => [\r\n        structure._scrollbar,\r\n        {\r\n          [cssCustomPropViewportPercent]: roundCssNumber(axisViewportPercent) + '',\r\n        },\r\n      ];\r\n\r\n    scrollbarStyle(horizontalScrollbars, createScrollbarStyleFn(viewportPercent.x));\r\n    scrollbarStyle(verticalScrollbars, createScrollbarStyleFn(viewportPercent.y));\r\n  };\r\n  const refreshScrollbarsHandleOffset = () => {\r\n    if (!scrollT) {\r\n      const { _scrollCoordinates } = structureSetupState;\r\n      const scrollPercent = getScrollCoordinatesPercent(\r\n        _scrollCoordinates,\r\n        getElementScroll(_scrollOffsetElement)\r\n      );\r\n      const createScrollbarStyleFn =\r\n        (axisScrollPercent: number): ScrollbarStyleFn =>\r\n        (structure: ScrollbarStructure) => [\r\n          structure._scrollbar,\r\n          {\r\n            [cssCustomPropScrollPercent]: roundCssNumber(axisScrollPercent) + '',\r\n          },\r\n        ];\r\n\r\n      scrollbarStyle(horizontalScrollbars, createScrollbarStyleFn(scrollPercent.x));\r\n      scrollbarStyle(verticalScrollbars, createScrollbarStyleFn(scrollPercent.y));\r\n    }\r\n  };\r\n  const refreshScrollbarsScrollCoordinates = () => {\r\n    const { _scrollCoordinates } = structureSetupState;\r\n    const defaultDirectionScroll = isDefaultDirectionScrollCoordinates(_scrollCoordinates);\r\n    const createScrollbarStyleFn =\r\n      (axisIsDefaultDirectionScrollCoordinates: boolean): ScrollbarStyleFn =>\r\n      (structure: ScrollbarStructure) => [\r\n        structure._scrollbar,\r\n        {\r\n          [cssCustomPropScrollDirection]: axisIsDefaultDirectionScrollCoordinates ? '0' : '1',\r\n        },\r\n      ];\r\n\r\n    scrollbarStyle(horizontalScrollbars, createScrollbarStyleFn(defaultDirectionScroll.x));\r\n    scrollbarStyle(verticalScrollbars, createScrollbarStyleFn(defaultDirectionScroll.y));\r\n\r\n    // temporary fix for: https://github.com/KingSora/OverlayScrollbars/issues/705\r\n    if (scrollT) {\r\n      horizontalScrollbars.forEach(scrollTimeline.x!._setScrollPercentAnimation);\r\n      verticalScrollbars.forEach(scrollTimeline.y!._setScrollPercentAnimation);\r\n    }\r\n  };\r\n  const refreshScrollbarsScrollbarOffset = () => {\r\n    if (_viewportIsTarget && !_isBody) {\r\n      const { _overflowAmount, _scrollCoordinates } = structureSetupState;\r\n      const isDefaultDirectionScroll = isDefaultDirectionScrollCoordinates(_scrollCoordinates);\r\n      const scrollPercent = getScrollCoordinatesPercent(\r\n        _scrollCoordinates,\r\n        getElementScroll(_scrollOffsetElement)\r\n      );\r\n      const styleScrollbarPosition: ScrollbarStyleFn = (structure: ScrollbarStructure) => {\r\n        const { _scrollbar } = structure;\r\n        const elm = parent(_scrollbar) === _viewport && _scrollbar;\r\n        const getTranslateValue = (\r\n          axisScrollPercent: number,\r\n          axisOverflowAmount: number,\r\n          axisIsDefaultCoordinates: boolean\r\n        ) => {\r\n          const px = axisOverflowAmount * axisScrollPercent;\r\n          return numberToCssPx(axisIsDefaultCoordinates ? px : -px);\r\n        };\r\n\r\n        return [\r\n          elm,\r\n          elm && {\r\n            transform: getTrasformTranslateValue({\r\n              x: getTranslateValue(scrollPercent.x, _overflowAmount.x, isDefaultDirectionScroll.x),\r\n              y: getTranslateValue(scrollPercent.y, _overflowAmount.y, isDefaultDirectionScroll.y),\r\n            }),\r\n          },\r\n        ];\r\n      };\r\n\r\n      scrollbarStyle(horizontalScrollbars, styleScrollbarPosition);\r\n      scrollbarStyle(verticalScrollbars, styleScrollbarPosition);\r\n    }\r\n  };\r\n  const generateScrollbarDOM = (isHorizontal?: boolean): ScrollbarStructure => {\r\n    const xyKey = isHorizontal ? 'x' : 'y';\r\n    const scrollbarClassName = isHorizontal\r\n      ? classNameScrollbarHorizontal\r\n      : classNameScrollbarVertical;\r\n    const scrollbar = createDiv(`${classNameScrollbar} ${scrollbarClassName}`);\r\n    const track = createDiv(classNameScrollbarTrack);\r\n    const handle = createDiv(classNameScrollbarHandle);\r\n    const result = {\r\n      _scrollbar: scrollbar,\r\n      _track: track,\r\n      _handle: handle,\r\n    };\r\n    const timeline = scrollTimeline[xyKey];\r\n\r\n    push(isHorizontal ? horizontalScrollbars : verticalScrollbars, result);\r\n    push(destroyFns, [\r\n      appendChildren(scrollbar, track),\r\n      appendChildren(track, handle),\r\n      bind(removeElements, scrollbar),\r\n      timeline && timeline._setScrollPercentAnimation(result),\r\n      scrollbarsSetupEvents(result, scrollbarsAddRemoveClass, isHorizontal),\r\n    ]);\r\n\r\n    return result;\r\n  };\r\n  const generateHorizontalScrollbarStructure = bind(generateScrollbarDOM, true);\r\n  const generateVerticalScrollbarStructure = bind(generateScrollbarDOM, false);\r\n  const appendElements = () => {\r\n    appendChildren(evaluatedScrollbarSlot, horizontalScrollbars[0]._scrollbar);\r\n    appendChildren(evaluatedScrollbarSlot, verticalScrollbars[0]._scrollbar);\r\n\r\n    return bind(runEachAndClear, destroyFns);\r\n  };\r\n\r\n  generateHorizontalScrollbarStructure();\r\n  generateVerticalScrollbarStructure();\r\n\r\n  return [\r\n    {\r\n      _refreshScrollbarsHandleLength: refreshScrollbarsHandleLength,\r\n      _refreshScrollbarsHandleOffset: refreshScrollbarsHandleOffset,\r\n      _refreshScrollbarsScrollCoordinates: refreshScrollbarsScrollCoordinates,\r\n      _refreshScrollbarsScrollbarOffset: refreshScrollbarsScrollbarOffset,\r\n      _scrollbarsAddRemoveClass: scrollbarsAddRemoveClass,\r\n      _horizontal: {\r\n        _scrollbarStructures: horizontalScrollbars,\r\n        _clone: generateHorizontalScrollbarStructure,\r\n        _style: bind(scrollbarStyle, horizontalScrollbars),\r\n      },\r\n      _vertical: {\r\n        _scrollbarStructures: verticalScrollbars,\r\n        _clone: generateVerticalScrollbarStructure,\r\n        _style: bind(scrollbarStyle, verticalScrollbars),\r\n      },\r\n    },\r\n    appendElements,\r\n  ];\r\n};\r\n", "import type { XY } from '../../support';\r\nimport type { ClickScrollPlugin } from '../../plugins';\r\nimport type { ReadonlyOptions } from '../../options';\r\nimport type { StructureSetupState } from '../../setups';\r\nimport type { ScrollbarsSetupElementsObj, ScrollbarStructure } from './scrollbarsSetup.elements';\r\nimport type { StructureSetupElementsObj } from '../structureSetup/structureSetup.elements';\r\nimport {\r\n  classNameScrollbarHandle,\r\n  classNameScrollbarInteraction,\r\n  classNameScrollbarWheel,\r\n  dataAttributeHost,\r\n  dataAttributeViewport,\r\n} from '../../classnames';\r\nimport { clickScrollPluginModuleName, getStaticPluginModuleInstance } from '../../plugins';\r\nimport {\r\n  getBoundingClientRect,\r\n  getOffsetSize,\r\n  addEventListener,\r\n  preventDefault,\r\n  runEachAndClear,\r\n  selfClearTimeout,\r\n  parent,\r\n  closest,\r\n  push,\r\n  bind,\r\n  mathRound,\r\n  strWidth,\r\n  strHeight,\r\n  getElementScroll,\r\n  scrollElementTo,\r\n  getFocusedElement,\r\n  setT,\r\n  hasAttr,\r\n  stopAndPrevent,\r\n  isFunction,\r\n  mathAbs,\r\n  focusElement,\r\n} from '../../support';\r\n\r\nexport type ScrollbarsSetupEvents = (\r\n  scrollbarStructure: ScrollbarStructure,\r\n  scrollbarsAddRemoveClass: ScrollbarsSetupElementsObj['_scrollbarsAddRemoveClass'],\r\n  isHorizontal?: boolean\r\n) => () => void;\r\n\r\nexport const createScrollbarsSetupEvents = (\r\n  options: ReadonlyOptions,\r\n  structureSetupElements: StructureSetupElementsObj,\r\n  structureSetupState: StructureSetupState,\r\n  scrollbarHandlePointerInteraction: (event: PointerEvent) => void\r\n): ScrollbarsSetupEvents => {\r\n  return (scrollbarStructure, scrollbarsAddRemoveClass, isHorizontal) => {\r\n    const {\r\n      _host,\r\n      _viewport,\r\n      _viewportIsTarget,\r\n      _scrollOffsetElement,\r\n      _documentElm,\r\n      _removeScrollObscuringStyles,\r\n    } = structureSetupElements;\r\n    const { _scrollbar, _track, _handle } = scrollbarStructure;\r\n    const [wheelTimeout, clearWheelTimeout] = selfClearTimeout(333);\r\n    const [scrollSnapScrollTransitionTimeout, clearScrollSnapScrollTransitionTimeout] =\r\n      selfClearTimeout(444);\r\n    const scrollOffsetElementScrollBy = (coordinates: XY<number>) => {\r\n      isFunction(_scrollOffsetElement.scrollBy) &&\r\n        _scrollOffsetElement.scrollBy({\r\n          behavior: 'smooth',\r\n          left: coordinates.x,\r\n          top: coordinates.y,\r\n        });\r\n    };\r\n\r\n    const createInteractiveScrollEvents = () => {\r\n      const releasePointerCaptureEvents = 'pointerup pointercancel lostpointercapture';\r\n      const clientXYKey = `client${isHorizontal ? 'X' : 'Y'}` as 'clientX' | 'clientY';\r\n      const widthHeightKey = isHorizontal ? strWidth : strHeight;\r\n      const leftTopKey = isHorizontal ? 'left' : 'top';\r\n      const whKey = isHorizontal ? 'w' : 'h';\r\n      const xyKey = isHorizontal ? 'x' : 'y';\r\n\r\n      const createRelativeHandleMove =\r\n        (mouseDownScroll: number, invertedScale: number) => (deltaMovement: number) => {\r\n          const { _overflowAmount } = structureSetupState;\r\n          const handleTrackDiff = getOffsetSize(_track)[whKey] - getOffsetSize(_handle)[whKey];\r\n          const scrollDeltaPercent = (invertedScale * deltaMovement) / handleTrackDiff;\r\n          const scrollDelta = scrollDeltaPercent * _overflowAmount[xyKey];\r\n\r\n          scrollElementTo(_scrollOffsetElement, {\r\n            [xyKey]: mouseDownScroll + scrollDelta,\r\n          });\r\n        };\r\n      const pointerdownCleanupFns: Array<() => void> = [];\r\n\r\n      return addEventListener(_track, 'pointerdown', (pointerDownEvent: PointerEvent) => {\r\n        const isDragScroll =\r\n          closest(pointerDownEvent.target as Node, `.${classNameScrollbarHandle}`) === _handle;\r\n        const pointerCaptureElement = isDragScroll ? _handle : _track;\r\n\r\n        const scrollbarOptions = options.scrollbars;\r\n        const dragClickScrollOption = scrollbarOptions[isDragScroll ? 'dragScroll' : 'clickScroll'];\r\n        const { button, isPrimary, pointerType } = pointerDownEvent;\r\n        const { pointers } = scrollbarOptions;\r\n\r\n        const continuePointerDown =\r\n          button === 0 &&\r\n          isPrimary &&\r\n          dragClickScrollOption &&\r\n          (pointers || []).includes(pointerType);\r\n\r\n        if (continuePointerDown) {\r\n          runEachAndClear(pointerdownCleanupFns);\r\n          clearScrollSnapScrollTransitionTimeout();\r\n\r\n          const instantClickScroll =\r\n            !isDragScroll && (pointerDownEvent.shiftKey || dragClickScrollOption === 'instant');\r\n          const getHandleRect = bind(getBoundingClientRect, _handle);\r\n          const getTrackRect = bind(getBoundingClientRect, _track);\r\n          const getHandleOffset = (handleRect?: DOMRect, trackRect?: DOMRect) =>\r\n            (handleRect || getHandleRect())[leftTopKey] - (trackRect || getTrackRect())[leftTopKey];\r\n          const axisScale =\r\n            mathRound(getBoundingClientRect(_scrollOffsetElement)[widthHeightKey]) /\r\n              getOffsetSize(_scrollOffsetElement)[whKey] || 1;\r\n          const moveHandleRelative = createRelativeHandleMove(\r\n            getElementScroll(_scrollOffsetElement)[xyKey],\r\n            1 / axisScale\r\n          );\r\n          const pointerDownOffset = pointerDownEvent[clientXYKey];\r\n          const handleRect = getHandleRect();\r\n          const trackRect = getTrackRect();\r\n          const handleLength = handleRect[widthHeightKey];\r\n          const handleCenter = getHandleOffset(handleRect, trackRect) + handleLength / 2;\r\n          const relativeTrackPointerOffset = pointerDownOffset - trackRect[leftTopKey];\r\n          const startOffset = isDragScroll ? 0 : relativeTrackPointerOffset - handleCenter;\r\n          const releasePointerCapture = (pointerUpEvent: PointerEvent) => {\r\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\r\n            runEachAndClear(pointerupCleanupFns);\r\n            pointerCaptureElement.releasePointerCapture(pointerUpEvent.pointerId);\r\n          };\r\n          const nonAnimatedScroll = isDragScroll || instantClickScroll;\r\n          const revertScrollObscuringStyles = _removeScrollObscuringStyles();\r\n\r\n          const pointerupCleanupFns = [\r\n            addEventListener(_documentElm, releasePointerCaptureEvents, releasePointerCapture),\r\n            addEventListener(_documentElm, 'selectstart', (event: Event) => preventDefault(event), {\r\n              _passive: false,\r\n            }),\r\n            addEventListener(_track, releasePointerCaptureEvents, releasePointerCapture),\r\n            nonAnimatedScroll &&\r\n              addEventListener(_track, 'pointermove', (pointerMoveEvent: PointerEvent) =>\r\n                moveHandleRelative(\r\n                  startOffset + (pointerMoveEvent[clientXYKey] - pointerDownOffset)\r\n                )\r\n              ),\r\n            nonAnimatedScroll &&\r\n              (() => {\r\n                const withoutSnapScrollOffset = getElementScroll(_scrollOffsetElement);\r\n                revertScrollObscuringStyles();\r\n                const withSnapScrollOffset = getElementScroll(_scrollOffsetElement);\r\n                const snapScrollDiff = {\r\n                  x: withSnapScrollOffset.x - withoutSnapScrollOffset.x,\r\n                  y: withSnapScrollOffset.y - withoutSnapScrollOffset.y,\r\n                };\r\n\r\n                if (mathAbs(snapScrollDiff.x) > 3 || mathAbs(snapScrollDiff.y) > 3) {\r\n                  _removeScrollObscuringStyles();\r\n                  scrollElementTo(_scrollOffsetElement, withoutSnapScrollOffset);\r\n                  scrollOffsetElementScrollBy(snapScrollDiff);\r\n                  scrollSnapScrollTransitionTimeout(revertScrollObscuringStyles);\r\n                }\r\n              }),\r\n          ];\r\n\r\n          pointerCaptureElement.setPointerCapture(pointerDownEvent.pointerId);\r\n\r\n          if (instantClickScroll) {\r\n            moveHandleRelative(startOffset);\r\n          } else if (!isDragScroll) {\r\n            const animateClickScroll = getStaticPluginModuleInstance<typeof ClickScrollPlugin>(\r\n              clickScrollPluginModuleName\r\n            );\r\n            if (animateClickScroll) {\r\n              const stopClickScrollAnimation = animateClickScroll(\r\n                moveHandleRelative,\r\n                startOffset,\r\n                handleLength,\r\n                (stopped) => {\r\n                  // if the scroll animation doesn't continue with a press\r\n                  if (stopped) {\r\n                    revertScrollObscuringStyles();\r\n                  } else {\r\n                    push(pointerupCleanupFns, revertScrollObscuringStyles);\r\n                  }\r\n                }\r\n              );\r\n\r\n              push(pointerupCleanupFns, stopClickScrollAnimation);\r\n              push(pointerdownCleanupFns, bind(stopClickScrollAnimation, true));\r\n            }\r\n          }\r\n        }\r\n      });\r\n    };\r\n\r\n    let wheelScrollBy = true;\r\n\r\n    return bind(runEachAndClear, [\r\n      addEventListener(_handle, 'pointermove pointerleave', scrollbarHandlePointerInteraction),\r\n      addEventListener(_scrollbar, 'pointerenter', () => {\r\n        scrollbarsAddRemoveClass(classNameScrollbarInteraction, true);\r\n      }),\r\n      addEventListener(_scrollbar, 'pointerleave pointercancel', () => {\r\n        scrollbarsAddRemoveClass(classNameScrollbarInteraction, false);\r\n      }),\r\n      // focus viewport when clicking on a scrollbar (mouse only)\r\n      !_viewportIsTarget &&\r\n        addEventListener(_scrollbar, 'mousedown', () => {\r\n          const focusedElement = getFocusedElement();\r\n          if (\r\n            hasAttr(focusedElement, dataAttributeViewport) ||\r\n            hasAttr(focusedElement, dataAttributeHost) ||\r\n            focusedElement === document.body\r\n          ) {\r\n            setT(bind(focusElement, _viewport), 25);\r\n          }\r\n        }),\r\n      // propagate wheel events to viewport when mouse is over scrollbar\r\n      addEventListener(\r\n        _scrollbar,\r\n        'wheel',\r\n        (wheelEvent: WheelEvent) => {\r\n          const { deltaX, deltaY, deltaMode } = wheelEvent;\r\n\r\n          // the first wheel event is swallowed, simulate scroll to compensate for it\r\n          if (wheelScrollBy && deltaMode === 0 && parent(_scrollbar) === _host) {\r\n            scrollOffsetElementScrollBy({\r\n              x: deltaX,\r\n              y: deltaY,\r\n            });\r\n          }\r\n\r\n          wheelScrollBy = false;\r\n          scrollbarsAddRemoveClass(classNameScrollbarWheel, true);\r\n          wheelTimeout(() => {\r\n            wheelScrollBy = true;\r\n            scrollbarsAddRemoveClass(classNameScrollbarWheel);\r\n          });\r\n\r\n          preventDefault(wheelEvent);\r\n        },\r\n        { _passive: false, _capture: true }\r\n      ),\r\n      // solve problem of interaction causing click events\r\n      addEventListener(\r\n        _scrollbar,\r\n        'pointerdown',\r\n        // stopPropagation for stopping event propagation (causing click listeners to be invoked)\r\n        // preventDefault to prevent the pointer to cause any actions (e.g. releasing mouse button over an <a> tag causes an navigation)\r\n        bind(addEventListener, _documentElm, 'click', stopAndPrevent, {\r\n          _once: true,\r\n          _capture: true,\r\n          _passive: false,\r\n        }),\r\n        { _capture: true }\r\n      ),\r\n      createInteractiveScrollEvents(),\r\n      clearWheelTimeout,\r\n      clearScrollSnapScrollTransitionTimeout,\r\n    ]);\r\n  };\r\n};\r\n", "import type { OverflowBehavior, ReadonlyOptions } from '../../options';\r\nimport type { ScrollbarsSetupElementsObj } from './scrollbarsSetup.elements';\r\nimport type {\r\n  ObserversSetupState,\r\n  ObserversSetupUpdateHints,\r\n  Setup,\r\n  SetupUpdateInfo,\r\n  StructureSetupState,\r\n  StructureSetupUpdateHints,\r\n} from '../../setups';\r\nimport type { InitializationTarget } from '../../initialization';\r\nimport type { OverflowStyle } from '../../typings';\r\nimport type { StructureSetupElementsObj } from '../structureSetup/structureSetup.elements';\r\nimport {\r\n  classNameScrollbarThemeNone,\r\n  classNameScrollbarVisible,\r\n  classNameScrollbarUnusable,\r\n  classNameScrollbarCornerless,\r\n  classNameScrollbarAutoHideHidden,\r\n  classNameScrollbarHandleInteractive,\r\n  classNameScrollbarTrackInteractive,\r\n  classNameScrollbarRtl,\r\n  classNameScrollbarAutoHide,\r\n} from '../../classnames';\r\nimport { getEnvironment } from '../../environment';\r\nimport {\r\n  bind,\r\n  noop,\r\n  addEventListener,\r\n  push,\r\n  runEachAndClear,\r\n  selfClearTimeout,\r\n  strScroll,\r\n  strVisible,\r\n} from '../../support';\r\nimport { createScrollbarsSetupElements } from './scrollbarsSetup.elements';\r\nimport { createScrollbarsSetupEvents } from './scrollbarsSetup.events';\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\r\nexport interface ScrollbarsSetupState {}\r\n\r\nexport interface ScrollbarsSetupUpdateInfo extends SetupUpdateInfo {\r\n  _observersUpdateHints?: ObserversSetupUpdateHints;\r\n  _structureUpdateHints?: StructureSetupUpdateHints;\r\n}\r\n\r\nexport type ScrollbarsSetup = [\r\n  ...Setup<ScrollbarsSetupUpdateInfo, ScrollbarsSetupState, void>,\r\n  /** The elements created by the scrollbars setup. */\r\n  ScrollbarsSetupElementsObj,\r\n];\r\n\r\nexport const createScrollbarsSetup = (\r\n  target: InitializationTarget,\r\n  options: ReadonlyOptions,\r\n  observersSetupState: ObserversSetupState,\r\n  structureSetupState: StructureSetupState,\r\n  structureSetupElements: StructureSetupElementsObj,\r\n  onScroll: (event: Event) => void\r\n): ScrollbarsSetup => {\r\n  let mouseInHost: boolean | undefined;\r\n  let autoHideIsMove: boolean | undefined;\r\n  let autoHideIsLeave: boolean | undefined;\r\n  let autoHideIsNever: boolean | undefined;\r\n  let prevTheme: string | null | undefined;\r\n  let instanceAutoHideSuspendScrollDestroyFn = noop;\r\n  let instanceAutoHideDelay = 0;\r\n  const hoverablePointerTypes = ['mouse', 'pen'];\r\n\r\n  // needed to not fire unnecessary operations for pointer events on ios safari which will cause side effects: https://github.com/KingSora/OverlayScrollbars/issues/560\r\n  const isHoverablePointerType = (event: PointerEvent) =>\r\n    hoverablePointerTypes.includes(event.pointerType);\r\n\r\n  const [requestScrollAnimationFrame, cancelScrollAnimationFrame] = selfClearTimeout();\r\n  const [autoHideInstantInteractionTimeout, clearAutoHideInstantInteractionTimeout] =\r\n    selfClearTimeout(100);\r\n  const [autoHideSuspendTimeout, clearAutoHideSuspendTimeout] = selfClearTimeout(100);\r\n  const [auotHideTimeout, clearAutoHideTimeout] = selfClearTimeout(() => instanceAutoHideDelay);\r\n  const [elements, appendElements] = createScrollbarsSetupElements(\r\n    target,\r\n    structureSetupElements,\r\n    structureSetupState,\r\n    createScrollbarsSetupEvents(\r\n      options,\r\n      structureSetupElements,\r\n      structureSetupState,\r\n      (event) =>\r\n        isHoverablePointerType(event) &&\r\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\r\n        manageScrollbarsAutoHideInstantInteraction()\r\n    )\r\n  );\r\n  const { _host, _scrollEventElement, _isBody } = structureSetupElements;\r\n  const {\r\n    _scrollbarsAddRemoveClass,\r\n    _refreshScrollbarsHandleLength,\r\n    _refreshScrollbarsHandleOffset,\r\n    _refreshScrollbarsScrollCoordinates,\r\n    _refreshScrollbarsScrollbarOffset,\r\n  } = elements;\r\n  const manageScrollbarsAutoHide = (removeAutoHide: boolean, delayless?: boolean) => {\r\n    clearAutoHideTimeout();\r\n    if (removeAutoHide) {\r\n      _scrollbarsAddRemoveClass(classNameScrollbarAutoHideHidden);\r\n    } else {\r\n      const hide = bind(_scrollbarsAddRemoveClass, classNameScrollbarAutoHideHidden, true);\r\n      if (instanceAutoHideDelay > 0 && !delayless) {\r\n        auotHideTimeout(hide);\r\n      } else {\r\n        hide();\r\n      }\r\n    }\r\n  };\r\n  const manageScrollbarsAutoHideInstantInteraction = () => {\r\n    if (autoHideIsLeave ? !mouseInHost : !autoHideIsNever) {\r\n      manageScrollbarsAutoHide(true);\r\n      autoHideInstantInteractionTimeout(() => {\r\n        manageScrollbarsAutoHide(false);\r\n      });\r\n    }\r\n  };\r\n  const manageAutoHideSuspension = (add: boolean) => {\r\n    _scrollbarsAddRemoveClass(classNameScrollbarAutoHide, add, true);\r\n    _scrollbarsAddRemoveClass(classNameScrollbarAutoHide, add, false);\r\n  };\r\n  const onHostMouseEnter = (event: PointerEvent) => {\r\n    if (isHoverablePointerType(event)) {\r\n      mouseInHost = autoHideIsLeave;\r\n      autoHideIsLeave && manageScrollbarsAutoHide(true);\r\n    }\r\n  };\r\n  const destroyFns: (() => void)[] = [\r\n    clearAutoHideTimeout,\r\n    clearAutoHideInstantInteractionTimeout,\r\n    clearAutoHideSuspendTimeout,\r\n    cancelScrollAnimationFrame,\r\n    () => instanceAutoHideSuspendScrollDestroyFn(),\r\n\r\n    addEventListener(_host, 'pointerover', onHostMouseEnter, { _once: true }),\r\n    addEventListener(_host, 'pointerenter', onHostMouseEnter),\r\n    addEventListener(_host, 'pointerleave', (event: PointerEvent) => {\r\n      if (isHoverablePointerType(event)) {\r\n        mouseInHost = false;\r\n        autoHideIsLeave && manageScrollbarsAutoHide(false);\r\n      }\r\n    }),\r\n    addEventListener(_host, 'pointermove', (event: PointerEvent) => {\r\n      isHoverablePointerType(event) &&\r\n        autoHideIsMove &&\r\n        manageScrollbarsAutoHideInstantInteraction();\r\n    }),\r\n    addEventListener(_scrollEventElement, 'scroll', (event) => {\r\n      requestScrollAnimationFrame(() => {\r\n        _refreshScrollbarsHandleOffset();\r\n        manageScrollbarsAutoHideInstantInteraction();\r\n      });\r\n\r\n      onScroll(event);\r\n\r\n      _refreshScrollbarsScrollbarOffset();\r\n    }),\r\n  ];\r\n\r\n  return [\r\n    () => bind(runEachAndClear, push(destroyFns, appendElements())),\r\n    ({ _checkOption, _force, _observersUpdateHints, _structureUpdateHints }) => {\r\n      const {\r\n        _overflowEdgeChanged,\r\n        _overflowAmountChanged,\r\n        _overflowStyleChanged,\r\n        _scrollCoordinatesChanged,\r\n      } = _structureUpdateHints || {};\r\n      const { _directionChanged, _appear } = _observersUpdateHints || {};\r\n      const { _directionIsRTL } = observersSetupState;\r\n      const { _nativeScrollbarsOverlaid } = getEnvironment();\r\n      const { _overflowStyle, _hasOverflow } = structureSetupState;\r\n      const [showNativeOverlaidScrollbarsOption, showNativeOverlaidScrollbarsChanged] =\r\n        _checkOption('showNativeOverlaidScrollbars');\r\n      const [theme, themeChanged] = _checkOption('scrollbars.theme');\r\n      const [visibility, visibilityChanged] = _checkOption('scrollbars.visibility');\r\n      const [autoHide, autoHideChanged] = _checkOption('scrollbars.autoHide');\r\n      const [autoHideSuspend, autoHideSuspendChanged] = _checkOption('scrollbars.autoHideSuspend');\r\n      const [autoHideDelay] = _checkOption('scrollbars.autoHideDelay');\r\n      const [dragScroll, dragScrollChanged] = _checkOption('scrollbars.dragScroll');\r\n      const [clickScroll, clickScrollChanged] = _checkOption('scrollbars.clickScroll');\r\n      const [overflow, overflowChanged] = _checkOption('overflow');\r\n      const trulyAppeared = _appear && !_force;\r\n      const hasOverflow = _hasOverflow.x || _hasOverflow.y;\r\n      const updateScrollbars =\r\n        _overflowEdgeChanged ||\r\n        _overflowAmountChanged ||\r\n        _scrollCoordinatesChanged ||\r\n        _directionChanged ||\r\n        _force;\r\n      const updateVisibility = _overflowStyleChanged || visibilityChanged || overflowChanged;\r\n      const showNativeOverlaidScrollbars =\r\n        showNativeOverlaidScrollbarsOption &&\r\n        _nativeScrollbarsOverlaid.x &&\r\n        _nativeScrollbarsOverlaid.y;\r\n\r\n      const setScrollbarVisibility = (\r\n        overflowBehavior: OverflowBehavior,\r\n        overflowStyle: OverflowStyle,\r\n        isHorizontal: boolean\r\n      ) => {\r\n        const isVisible =\r\n          overflowBehavior.includes(strScroll) &&\r\n          (visibility === strVisible || (visibility === 'auto' && overflowStyle === strScroll));\r\n\r\n        _scrollbarsAddRemoveClass(classNameScrollbarVisible, isVisible, isHorizontal);\r\n\r\n        return isVisible;\r\n      };\r\n\r\n      instanceAutoHideDelay = autoHideDelay;\r\n\r\n      if (trulyAppeared) {\r\n        if (autoHideSuspend && hasOverflow) {\r\n          manageAutoHideSuspension(false);\r\n          instanceAutoHideSuspendScrollDestroyFn();\r\n          autoHideSuspendTimeout(() => {\r\n            instanceAutoHideSuspendScrollDestroyFn = addEventListener(\r\n              _scrollEventElement,\r\n              'scroll',\r\n              bind(manageAutoHideSuspension, true),\r\n              {\r\n                _once: true,\r\n              }\r\n            );\r\n          });\r\n        } else {\r\n          manageAutoHideSuspension(true);\r\n        }\r\n      }\r\n\r\n      if (showNativeOverlaidScrollbarsChanged) {\r\n        _scrollbarsAddRemoveClass(classNameScrollbarThemeNone, showNativeOverlaidScrollbars);\r\n      }\r\n\r\n      if (themeChanged) {\r\n        _scrollbarsAddRemoveClass(prevTheme);\r\n        _scrollbarsAddRemoveClass(theme, true);\r\n\r\n        prevTheme = theme;\r\n      }\r\n\r\n      if (autoHideSuspendChanged && !autoHideSuspend) {\r\n        manageAutoHideSuspension(true);\r\n      }\r\n\r\n      if (autoHideChanged) {\r\n        autoHideIsMove = autoHide === 'move';\r\n        autoHideIsLeave = autoHide === 'leave';\r\n        autoHideIsNever = autoHide === 'never';\r\n        manageScrollbarsAutoHide(autoHideIsNever, true);\r\n      }\r\n\r\n      if (dragScrollChanged) {\r\n        _scrollbarsAddRemoveClass(classNameScrollbarHandleInteractive, dragScroll);\r\n      }\r\n\r\n      if (clickScrollChanged) {\r\n        _scrollbarsAddRemoveClass(classNameScrollbarTrackInteractive, !!clickScroll);\r\n      }\r\n\r\n      // always update scrollbar visibility before scrollbar size\r\n      // the scrollbar size is influenced whether both or just one scrollbar is visible (because of the corner element)\r\n      if (updateVisibility) {\r\n        const xVisible = setScrollbarVisibility(overflow.x, _overflowStyle.x, true);\r\n        const yVisible = setScrollbarVisibility(overflow.y, _overflowStyle.y, false);\r\n        const hasCorner = xVisible && yVisible;\r\n\r\n        _scrollbarsAddRemoveClass(classNameScrollbarCornerless, !hasCorner);\r\n      }\r\n\r\n      // always update scrollbar sizes after the visibility\r\n      if (updateScrollbars) {\r\n        _refreshScrollbarsHandleOffset();\r\n        _refreshScrollbarsHandleLength();\r\n        _refreshScrollbarsScrollbarOffset();\r\n        _scrollCoordinatesChanged && _refreshScrollbarsScrollCoordinates();\r\n\r\n        _scrollbarsAddRemoveClass(classNameScrollbarUnusable, !_hasOverflow.x, true);\r\n        _scrollbarsAddRemoveClass(classNameScrollbarUnusable, !_hasOverflow.y, false);\r\n        _scrollbarsAddRemoveClass(classNameScrollbarRtl, _directionIsRTL && !_isBody);\r\n      }\r\n    },\r\n    {},\r\n    elements,\r\n  ];\r\n};\r\n", "import type {\r\n  InitializationTarget,\r\n  InitializationTargetElement,\r\n  InitializationTargetObject,\r\n} from '../../initialization';\r\nimport {\r\n  isHTMLElement,\r\n  appendChildren,\r\n  createDiv,\r\n  contents,\r\n  parent,\r\n  removeElements,\r\n  push,\r\n  runEachAndClear,\r\n  removeAttrs,\r\n  hasAttrClass,\r\n  addEventListener,\r\n  bind,\r\n  inArray,\r\n  addAttrClass,\r\n  addRemoveAttrClass,\r\n  setAttrs,\r\n  getAttr,\r\n  isBodyElement,\r\n  getFocusedElement,\r\n  wnd,\r\n  focusElement,\r\n  stopAndPrevent,\r\n  getOffsetSize,\r\n  getScrollSize,\r\n  getStyles,\r\n  strOverflowX,\r\n  strOverflowY,\r\n} from '../../support';\r\nimport {\r\n  dataAttributeHost,\r\n  dataAttributeInitialize,\r\n  dataAttributeViewport,\r\n  dataValueViewportScrollbarHidden,\r\n  dataAttributePadding,\r\n  dataAttributeContent,\r\n  dataAttributeHtmlBody,\r\n  dataValueHostIsHost,\r\n  dataValueViewportScrolling,\r\n} from '../../classnames';\r\nimport { getEnvironment } from '../../environment';\r\nimport {\r\n  staticInitializationElement as generalStaticInitializationElement,\r\n  dynamicInitializationElement as generalDynamicInitializationElement,\r\n} from '../../initialization';\r\nimport { overflowIsVisible } from './structureSetup.utils';\r\n\r\nexport type StructureSetupElements = [\r\n  elements: StructureSetupElementsObj,\r\n  appendElements: () => () => void,\r\n  canceled: () => void,\r\n];\r\n\r\nexport interface StructureSetupElementsObj {\r\n  _target: InitializationTargetElement;\r\n  _host: HTMLElement;\r\n  _viewport: HTMLElement;\r\n  _padding: HTMLElement | false;\r\n  _content: HTMLElement | false;\r\n  _scrollOffsetElement: HTMLElement;\r\n  _scrollEventElement: HTMLElement | Document;\r\n  _originalScrollOffsetElement: HTMLElement;\r\n  // ctx ----\r\n  _isBody: boolean;\r\n  _documentElm: Document;\r\n  _targetIsElm: boolean;\r\n  _viewportIsTarget: boolean;\r\n  _windowElm: () => Window;\r\n  _viewportHasClass: (viewportAttributeClassName: string) => boolean;\r\n  _viewportAddRemoveClass: (viewportAttributeClassName: string, add?: boolean) => () => void;\r\n  _removeScrollObscuringStyles: () => () => void;\r\n}\r\n\r\nexport const createStructureSetupElements = (\r\n  target: InitializationTarget\r\n): StructureSetupElements => {\r\n  const env = getEnvironment();\r\n  const { _getDefaultInitialization, _nativeScrollbarsHiding } = env;\r\n  const { elements: defaultInitElements } = _getDefaultInitialization();\r\n  const {\r\n    padding: defaultPaddingInitialization,\r\n    viewport: defaultViewportInitialization,\r\n    content: defaultContentInitialization,\r\n  } = defaultInitElements;\r\n  const targetIsElm = isHTMLElement(target);\r\n  const targetStructureInitialization = (targetIsElm ? {} : target) as InitializationTargetObject;\r\n  const { elements: initElements } = targetStructureInitialization;\r\n  const {\r\n    padding: paddingInitialization,\r\n    viewport: viewportInitialization,\r\n    content: contentInitialization,\r\n  } = initElements || {};\r\n\r\n  const targetElement = targetIsElm ? target : targetStructureInitialization.target;\r\n  const isBody = isBodyElement(targetElement);\r\n  const ownerDocument = targetElement.ownerDocument;\r\n  const docElement = ownerDocument.documentElement;\r\n  const getDocumentWindow = () => ownerDocument.defaultView || wnd;\r\n  const staticInitializationElement = bind(generalStaticInitializationElement, [targetElement]);\r\n  const dynamicInitializationElement = bind(generalDynamicInitializationElement, [targetElement]);\r\n  const createNewDiv = bind(createDiv, '');\r\n  const generateViewportElement = bind(\r\n    staticInitializationElement,\r\n    createNewDiv,\r\n    defaultViewportInitialization\r\n  );\r\n  const generateContentElement = bind(\r\n    dynamicInitializationElement,\r\n    createNewDiv,\r\n    defaultContentInitialization\r\n  );\r\n  const elementHasOverflow = (elm: HTMLElement) => {\r\n    const offsetSize = getOffsetSize(elm);\r\n    const scrollSize = getScrollSize(elm);\r\n    const overflowX = getStyles(elm, strOverflowX);\r\n    const overflowY = getStyles(elm, strOverflowY);\r\n\r\n    return (\r\n      (scrollSize.w - offsetSize.w > 0 && !overflowIsVisible(overflowX)) ||\r\n      (scrollSize.h - offsetSize.h > 0 && !overflowIsVisible(overflowY))\r\n    );\r\n  };\r\n  const possibleViewportElement = generateViewportElement(viewportInitialization);\r\n  const viewportIsTarget = possibleViewportElement === targetElement;\r\n  const viewportIsTargetBody = viewportIsTarget && isBody;\r\n  const possibleContentElement = !viewportIsTarget && generateContentElement(contentInitialization);\r\n  // edge case if passed viewportElement is contentElement:\r\n  // viewport element has higher priority and content element will not be generated\r\n  // will act the same way as initialization: `{ elements: { viewport, content: false } }`\r\n  const viewportIsContent = !viewportIsTarget && possibleViewportElement === possibleContentElement;\r\n  const viewportElement = viewportIsTargetBody ? docElement : possibleViewportElement;\r\n  const hostElement = viewportIsTargetBody ? viewportElement : targetElement;\r\n  const paddingElement =\r\n    !viewportIsTarget &&\r\n    dynamicInitializationElement(createNewDiv, defaultPaddingInitialization, paddingInitialization);\r\n  const contentElement = !viewportIsContent && possibleContentElement;\r\n  const generatedElements = [contentElement, viewportElement, paddingElement, hostElement].map(\r\n    (elm) => isHTMLElement(elm) && !parent(elm) && elm\r\n  );\r\n  const elementIsGenerated = (elm: HTMLElement | false) => elm && inArray(generatedElements, elm);\r\n  const originalNonBodyScrollOffsetElement =\r\n    !elementIsGenerated(viewportElement) && elementHasOverflow(viewportElement)\r\n      ? viewportElement\r\n      : targetElement;\r\n  const scrollOffsetElement = viewportIsTargetBody ? docElement : viewportElement;\r\n  const scrollEventElement = viewportIsTargetBody ? ownerDocument : viewportElement;\r\n\r\n  const evaluatedTargetObj: StructureSetupElementsObj = {\r\n    _target: targetElement,\r\n    _host: hostElement,\r\n    _viewport: viewportElement,\r\n    _padding: paddingElement,\r\n    _content: contentElement,\r\n    _scrollOffsetElement: scrollOffsetElement,\r\n    _scrollEventElement: scrollEventElement,\r\n    _originalScrollOffsetElement: isBody ? docElement : originalNonBodyScrollOffsetElement,\r\n    _documentElm: ownerDocument,\r\n    _isBody: isBody,\r\n    _targetIsElm: targetIsElm,\r\n    _viewportIsTarget: viewportIsTarget,\r\n    _windowElm: getDocumentWindow,\r\n    _viewportHasClass: (viewportAttributeClassName: string) =>\r\n      hasAttrClass(viewportElement, dataAttributeViewport, viewportAttributeClassName),\r\n    _viewportAddRemoveClass: (viewportAttributeClassName: string, add?: boolean) =>\r\n      addRemoveAttrClass(viewportElement, dataAttributeViewport, viewportAttributeClassName, add),\r\n    _removeScrollObscuringStyles: () =>\r\n      addRemoveAttrClass(\r\n        scrollOffsetElement,\r\n        dataAttributeViewport,\r\n        dataValueViewportScrolling,\r\n        true\r\n      ),\r\n  };\r\n  const { _target, _host, _padding, _viewport, _content } = evaluatedTargetObj;\r\n  const destroyFns: (() => any)[] = [\r\n    () => {\r\n      // always remove dataAttributeHost & dataAttributeInitialize from host and from <html> element if target is body\r\n      removeAttrs(_host, [dataAttributeHost, dataAttributeInitialize]);\r\n      removeAttrs(_target, dataAttributeInitialize);\r\n      if (isBody) {\r\n        removeAttrs(docElement, [dataAttributeInitialize, dataAttributeHost]);\r\n      }\r\n    },\r\n  ];\r\n  let targetContents = contents(\r\n    [_content, _viewport, _padding, _host, _target].find((elm) => elm && !elementIsGenerated(elm))\r\n  );\r\n  const contentSlot = viewportIsTargetBody ? _target : _content || _viewport;\r\n  const destroy = bind(runEachAndClear, destroyFns);\r\n  const appendElements = () => {\r\n    const docWnd = getDocumentWindow();\r\n    const initActiveElm = getFocusedElement();\r\n    const unwrap = (elm: HTMLElement | false | null | undefined) => {\r\n      appendChildren(parent(elm), contents(elm));\r\n      removeElements(elm);\r\n    };\r\n    // wrapping / unwrapping will cause the focused element to blur, this should prevent those events to surface\r\n    const prepareWrapUnwrapFocus = (activeElement: Element | false | null | undefined) =>\r\n      addEventListener(activeElement, 'focusin focusout focus blur', stopAndPrevent, {\r\n        _capture: true,\r\n        _passive: false,\r\n      });\r\n    const tabIndexStr = 'tabindex';\r\n    const originalViewportTabIndex = getAttr(_viewport, tabIndexStr);\r\n    const undoInitWrapUndwrapFocus = prepareWrapUnwrapFocus(initActiveElm);\r\n    setAttrs(_host, dataAttributeHost, viewportIsTarget ? '' : dataValueHostIsHost);\r\n    setAttrs(_padding, dataAttributePadding, '');\r\n    setAttrs(_viewport, dataAttributeViewport, '');\r\n    setAttrs(_content, dataAttributeContent, '');\r\n\r\n    if (!viewportIsTarget) {\r\n      setAttrs(_viewport, tabIndexStr, originalViewportTabIndex || '-1');\r\n      isBody && setAttrs(docElement, dataAttributeHtmlBody, '');\r\n    }\r\n\r\n    appendChildren(contentSlot, targetContents);\r\n    appendChildren(_host, _padding);\r\n    appendChildren(_padding || _host, !viewportIsTarget && _viewport);\r\n    appendChildren(_viewport, _content);\r\n\r\n    push(destroyFns, [\r\n      undoInitWrapUndwrapFocus,\r\n      () => {\r\n        const destroyActiveElm = getFocusedElement();\r\n        const viewportIsGenerated = elementIsGenerated(_viewport);\r\n        // if the focused element is viewport and viewport will be destroyed shift the focus to target\r\n        // otherwise keep the focused element\r\n        const destroyFocusElement =\r\n          viewportIsGenerated && destroyActiveElm === _viewport ? _target : destroyActiveElm;\r\n        const undoDestroyWrapUndwrapFocus = prepareWrapUnwrapFocus(destroyFocusElement);\r\n        removeAttrs(_padding, dataAttributePadding);\r\n        removeAttrs(_content, dataAttributeContent);\r\n        removeAttrs(_viewport, dataAttributeViewport);\r\n        isBody && removeAttrs(docElement, dataAttributeHtmlBody);\r\n        originalViewportTabIndex\r\n          ? setAttrs(_viewport, tabIndexStr, originalViewportTabIndex)\r\n          : removeAttrs(_viewport, tabIndexStr);\r\n\r\n        elementIsGenerated(_content) && unwrap(_content);\r\n        viewportIsGenerated && unwrap(_viewport);\r\n        elementIsGenerated(_padding) && unwrap(_padding);\r\n        focusElement(destroyFocusElement);\r\n        undoDestroyWrapUndwrapFocus();\r\n      },\r\n    ]);\r\n\r\n    if (_nativeScrollbarsHiding && !viewportIsTarget) {\r\n      addAttrClass(_viewport, dataAttributeViewport, dataValueViewportScrollbarHidden);\r\n      push(destroyFns, bind(removeAttrs, _viewport, dataAttributeViewport));\r\n    }\r\n\r\n    // keep the original focused element focused except when\r\n    // the target is body and viewport is not target, then shift the focus to the viewport element\r\n    focusElement(\r\n      !viewportIsTarget && isBody && initActiveElm === _target && docWnd.top === docWnd\r\n        ? _viewport\r\n        : initActiveElm\r\n    );\r\n    undoInitWrapUndwrapFocus();\r\n\r\n    // @ts-ignore\r\n    targetContents = 0;\r\n\r\n    return destroy;\r\n  };\r\n\r\n  return [evaluatedTargetObj, appendElements, destroy];\r\n};\r\n", "import type { CreateStructureUpdateSegment } from '../structureSetup';\r\nimport { setStyles, strHeight } from '../../../support';\r\n\r\n/**\r\n * Lifecycle with the responsibility to adjust the trinsic behavior of the content element.\r\n * @param structureUpdateHub\r\n * @returns\r\n */\r\nexport const createTrinsicUpdateSegment: CreateStructureUpdateSegment =\r\n  ({ _content }) =>\r\n  ({ _observersUpdateHints, _observersState, _force }) => {\r\n    const { _heightIntrinsicChanged } = _observersUpdateHints || {};\r\n    const { _heightIntrinsic } = _observersState;\r\n    const heightIntrinsicChanged = _content && (_heightIntrinsicChanged || _force);\r\n\r\n    if (heightIntrinsicChanged) {\r\n      setStyles(_content, {\r\n        [strHeight]: _heightIntrinsic && '100%',\r\n      });\r\n    }\r\n  };\r\n", "import type { StyleObject } from '../../../typings';\r\nimport type { CreateStructureUpdateSegment } from '../structureSetup';\r\nimport {\r\n  createCache,\r\n  topRightBottomLeft,\r\n  equalTRBL,\r\n  assignDeep,\r\n  bind,\r\n  strMarginBottom,\r\n  strMarginLeft,\r\n  strMarginRight,\r\n  strPaddingBottom,\r\n  strPaddingLeft,\r\n  strPaddingRight,\r\n  strPaddingTop,\r\n  strWidth,\r\n  setStyles,\r\n} from '../../../support';\r\nimport { getEnvironment } from '../../../environment';\r\n\r\n/**\r\n * Lifecycle with the responsibility to adjust the padding styling of the padding and viewport element.\r\n * @param structureUpdateHub\r\n * @returns\r\n */\r\nexport const createPaddingUpdateSegment: CreateStructureUpdateSegment = (\r\n  { _host, _padding, _viewport, _viewportIsTarget },\r\n  state\r\n) => {\r\n  const [updatePaddingCache, currentPaddingCache] = createCache(\r\n    {\r\n      _equal: equalTRBL,\r\n      _initialValue: topRightBottomLeft(),\r\n    },\r\n    bind(topRightBottomLeft, _host, 'padding', '')\r\n  );\r\n\r\n  return ({ _checkOption, _observersUpdateHints, _observersState, _force }) => {\r\n    let [padding, paddingChanged] = currentPaddingCache(_force);\r\n    const { _nativeScrollbarsHiding } = getEnvironment();\r\n    const { _sizeChanged, _contentMutation, _directionChanged } = _observersUpdateHints || {};\r\n    const { _directionIsRTL } = _observersState;\r\n    const [paddingAbsolute, paddingAbsoluteChanged] = _checkOption('paddingAbsolute');\r\n    const contentMutation = _force || _contentMutation;\r\n\r\n    if (_sizeChanged || paddingChanged || contentMutation) {\r\n      [padding, paddingChanged] = updatePaddingCache(_force);\r\n    }\r\n\r\n    const paddingStyleChanged =\r\n      !_viewportIsTarget && (paddingAbsoluteChanged || _directionChanged || paddingChanged);\r\n\r\n    if (paddingStyleChanged) {\r\n      // if there is no padding element and no scrollbar styling, paddingAbsolute isn't supported\r\n      const paddingRelative = !paddingAbsolute || (!_padding && !_nativeScrollbarsHiding);\r\n      const paddingHorizontal = padding.r + padding.l;\r\n      const paddingVertical = padding.t + padding.b;\r\n\r\n      const paddingStyle: StyleObject = {\r\n        [strMarginRight]: paddingRelative && !_directionIsRTL ? -paddingHorizontal : 0,\r\n        [strMarginBottom]: paddingRelative ? -paddingVertical : 0,\r\n        [strMarginLeft]: paddingRelative && _directionIsRTL ? -paddingHorizontal : 0,\r\n        top: paddingRelative ? -padding.t : 0,\r\n        right: paddingRelative ? (_directionIsRTL ? -padding.r : 'auto') : 0,\r\n        left: paddingRelative ? (_directionIsRTL ? 'auto' : -padding.l) : 0,\r\n        [strWidth]: paddingRelative && `calc(100% + ${paddingHorizontal}px)`,\r\n      };\r\n      const viewportStyle: StyleObject = {\r\n        [strPaddingTop]: paddingRelative ? padding.t : 0,\r\n        [strPaddingRight]: paddingRelative ? padding.r : 0,\r\n        [strPaddingBottom]: paddingRelative ? padding.b : 0,\r\n        [strPaddingLeft]: paddingRelative ? padding.l : 0,\r\n      };\r\n\r\n      // if there is no padding element apply the style to the viewport element instead\r\n      setStyles(_padding || _viewport, paddingStyle);\r\n      setStyles(_viewport, viewportStyle);\r\n\r\n      assignDeep(state, {\r\n        _padding: padding,\r\n        _paddingAbsolute: !paddingRelative,\r\n        _viewportPaddingStyle: _padding\r\n          ? viewportStyle\r\n          : assignDeep({}, paddingStyle, viewportStyle),\r\n      });\r\n    }\r\n\r\n    return {\r\n      _paddingStyleChanged: paddingStyleChanged,\r\n    };\r\n  };\r\n};\r\n", "import type { ScrollCoordinates, WH, XY } from '../../../support';\r\nimport type { ScrollbarsHidingPlugin } from '../../../plugins/scrollbarsHidingPlugin';\r\nimport type { OverflowStyle } from '../../../typings';\r\nimport type { CreateStructureUpdateSegment } from '../structureSetup';\r\nimport {\r\n  createCache,\r\n  getScrollSize,\r\n  getFractionalSize,\r\n  equalWH,\r\n  getClientSize,\r\n  equalXY,\r\n  assignDeep,\r\n  bind,\r\n  wnd,\r\n  mathMax,\r\n  getWindowSize,\r\n  addRemoveAttrClass,\r\n  capitalizeFirstLetter,\r\n  setStyles,\r\n  strVisible,\r\n  strHidden,\r\n  keys,\r\n  strScroll,\r\n  scrollElementTo,\r\n  getElementScroll,\r\n  sanitizeScrollCoordinates,\r\n  getStyles,\r\n  equal,\r\n  getZeroScrollCoordinates,\r\n  hasDimensions,\r\n  addEventListener,\r\n  stopPropagation,\r\n  rAF,\r\n  hasAttrClass,\r\n} from '../../../support';\r\nimport { getEnvironment } from '../../../environment';\r\nimport {\r\n  dataAttributeHost,\r\n  dataValueNoClipping,\r\n  dataValueViewportScrollbarHidden,\r\n  dataAttributePadding,\r\n  dataValueViewportOverflowXPrefix,\r\n  dataValueViewportOverflowYPrefix,\r\n  dataValueViewportNoContent,\r\n  dataValueViewportMeasuring,\r\n} from '../../../classnames';\r\nimport { getStaticPluginModuleInstance, scrollbarsHidingPluginName } from '../../../plugins';\r\nimport {\r\n  createViewportOverflowState,\r\n  getShowNativeOverlaidScrollbars,\r\n  overflowIsVisible,\r\n} from '../structureSetup.utils';\r\n\r\ninterface FlowDirectionStyles {\r\n  display?: string;\r\n  direction?: string;\r\n  flexDirection?: string;\r\n  writingMode?: string;\r\n}\r\n\r\n/**\r\n * Lifecycle with the responsibility to set the correct overflow and scrollbar hiding styles of the viewport element.\r\n * @param structureUpdateHub\r\n * @returns\r\n */\r\nexport const createOverflowUpdateSegment: CreateStructureUpdateSegment = (\r\n  structureSetupElements,\r\n  structureSetupState\r\n) => {\r\n  const env = getEnvironment();\r\n  const {\r\n    _host,\r\n    _padding,\r\n    _viewport,\r\n    _viewportIsTarget,\r\n    _scrollEventElement,\r\n    _scrollOffsetElement,\r\n    _isBody,\r\n    _viewportAddRemoveClass,\r\n    _windowElm,\r\n  } = structureSetupElements;\r\n  const { _nativeScrollbarsHiding } = env;\r\n  const viewportIsTargetBody = _isBody && _viewportIsTarget;\r\n  const max0 = bind(mathMax, 0);\r\n  const flowDirectionCanBeNonDefaultMap: Record<\r\n    keyof FlowDirectionStyles,\r\n    (styleValue: string) => boolean\r\n  > = {\r\n    display: () => false,\r\n    direction: (directionStyle) => directionStyle !== 'ltr',\r\n    flexDirection: (flexDirectionStyle) => flexDirectionStyle.endsWith('-reverse'),\r\n    writingMode: (writingModeStyle) => writingModeStyle !== 'horizontal-tb',\r\n  };\r\n  const flowDirectionStyleArr = keys(flowDirectionCanBeNonDefaultMap) as Array<\r\n    keyof FlowDirectionStyles\r\n  >;\r\n  const whCacheOptions = {\r\n    _equal: equalWH,\r\n    _initialValue: { w: 0, h: 0 },\r\n  };\r\n  const partialXYOptions = {\r\n    _equal: equalXY,\r\n    _initialValue: {},\r\n  };\r\n\r\n  const setMeasuringMode = (active: boolean) => {\r\n    // viewportIsTargetBody never needs measuring\r\n    _viewportAddRemoveClass(dataValueViewportMeasuring, !viewportIsTargetBody && active);\r\n  };\r\n\r\n  const getMeasuredScrollCoordinates = (flowDirectionStyles: FlowDirectionStyles) => {\r\n    const flowDirectionCanBeNonDefault = flowDirectionStyleArr.some((styleName) => {\r\n      const styleValue = flowDirectionStyles[styleName];\r\n      return styleValue && flowDirectionCanBeNonDefaultMap[styleName](styleValue);\r\n    });\r\n\r\n    // if the direction can not be non-default return default scroll coordinates (only the sign of the numbers matters)\r\n    if (!flowDirectionCanBeNonDefault) {\r\n      return {\r\n        _start: { x: 0, y: 0 },\r\n        _end: { x: 1, y: 1 },\r\n      };\r\n    }\r\n\r\n    setMeasuringMode(true);\r\n\r\n    const originalScrollOffset = getElementScroll(_scrollOffsetElement);\r\n    const removeNoContent = _viewportAddRemoveClass(dataValueViewportNoContent, true);\r\n    const removeScrollBlock = addEventListener(\r\n      _scrollEventElement,\r\n      strScroll,\r\n      (event) => {\r\n        const scrollEventScrollOffset = getElementScroll(_scrollOffsetElement);\r\n        // if scroll offset didnt change\r\n        if (\r\n          event.isTrusted &&\r\n          scrollEventScrollOffset.x === originalScrollOffset.x &&\r\n          scrollEventScrollOffset.y === originalScrollOffset.y\r\n        ) {\r\n          stopPropagation(event);\r\n        }\r\n      },\r\n      {\r\n        _capture: true,\r\n        _once: true,\r\n      }\r\n    );\r\n\r\n    scrollElementTo(_scrollOffsetElement, {\r\n      x: 0,\r\n      y: 0,\r\n    });\r\n    removeNoContent();\r\n\r\n    const _start = getElementScroll(_scrollOffsetElement);\r\n    const scrollSize = getScrollSize(_scrollOffsetElement);\r\n    scrollElementTo(_scrollOffsetElement, {\r\n      x: scrollSize.w,\r\n      y: scrollSize.h,\r\n    });\r\n\r\n    const tmp = getElementScroll(_scrollOffsetElement);\r\n    scrollElementTo(_scrollOffsetElement, {\r\n      // if tmp is very close start there porbably wasn't any scroll happening so scroll again in different direction\r\n      x: tmp.x - _start.x < 1 && -scrollSize.w,\r\n      y: tmp.y - _start.y < 1 && -scrollSize.h,\r\n    });\r\n\r\n    const _end = getElementScroll(_scrollOffsetElement);\r\n    scrollElementTo(_scrollOffsetElement, originalScrollOffset);\r\n    rAF(() => removeScrollBlock());\r\n\r\n    return {\r\n      _start,\r\n      _end,\r\n    };\r\n  };\r\n  const getOverflowAmount = (\r\n    viewportScrollSize: WH<number>,\r\n    viewportClientSize: WH<number>\r\n  ): WH<number> => {\r\n    const tollerance = wnd.devicePixelRatio % 1 !== 0 ? 1 : 0;\r\n    const amount = {\r\n      w: max0(viewportScrollSize.w - viewportClientSize.w),\r\n      h: max0(viewportScrollSize.h - viewportClientSize.h),\r\n    };\r\n\r\n    return {\r\n      w: amount.w > tollerance ? amount.w : 0,\r\n      h: amount.h > tollerance ? amount.h : 0,\r\n    };\r\n  };\r\n  const [updateSizeFraction, getCurrentSizeFraction] = createCache<WH<number>>(\r\n    whCacheOptions,\r\n    bind(getFractionalSize, _viewport)\r\n  );\r\n  const [updateViewportScrollSizeCache, getCurrentViewportScrollSizeCache] = createCache<\r\n    WH<number>\r\n  >(whCacheOptions, bind(getScrollSize, _viewport));\r\n  const [updateOverflowAmountCache, getCurrentOverflowAmountCache] =\r\n    createCache<WH<number>>(whCacheOptions);\r\n  const [updateHasOverflowCache] = createCache<Partial<XY<boolean>>>(partialXYOptions);\r\n  const [updateOverflowEdge, getCurrentOverflowEdgeCache] = createCache<WH<number>>(whCacheOptions);\r\n  const [updateOverflowStyleCache] = createCache<Partial<XY<OverflowStyle>>>(partialXYOptions);\r\n  const [updateFlowDirectionStyles] = createCache<FlowDirectionStyles>(\r\n    {\r\n      _equal: (currVal, newValu) => equal(currVal, newValu, flowDirectionStyleArr),\r\n      _initialValue: {},\r\n    },\r\n    () => (hasDimensions(_viewport) ? getStyles(_viewport, flowDirectionStyleArr) : {})\r\n  );\r\n  const [updateMeasuredScrollCoordinates, getCurrentMeasuredScrollCoordinates] =\r\n    createCache<ScrollCoordinates>({\r\n      _equal: (currVal, newVal) =>\r\n        equalXY(currVal._start, newVal._start) && equalXY(currVal._end, newVal._end),\r\n      _initialValue: getZeroScrollCoordinates(),\r\n    });\r\n\r\n  const scrollbarsHidingPlugin = getStaticPluginModuleInstance<typeof ScrollbarsHidingPlugin>(\r\n    scrollbarsHidingPluginName\r\n  );\r\n\r\n  const createViewportOverflowStyleClassName = (\r\n    overflowStyle: OverflowStyle,\r\n    isHorizontal?: boolean\r\n  ) => {\r\n    const prefix = isHorizontal\r\n      ? dataValueViewportOverflowXPrefix\r\n      : dataValueViewportOverflowYPrefix;\r\n    return `${prefix}${capitalizeFirstLetter(overflowStyle)}`;\r\n  };\r\n  const setViewportOverflowStyle = (viewportOverflowStyle: XY<OverflowStyle>) => {\r\n    // `createAllOverflowStyleClassNames` and `allOverflowStyleClassNames` could be one scope further up but would increase bundle size\r\n    const createAllOverflowStyleClassNames = (isHorizontal?: boolean) =>\r\n      ([strVisible, strHidden, strScroll] as OverflowStyle[]).map((style) =>\r\n        createViewportOverflowStyleClassName(style, isHorizontal)\r\n      );\r\n    const allOverflowStyleClassNames = createAllOverflowStyleClassNames(true)\r\n      .concat(createAllOverflowStyleClassNames())\r\n      .join(' ');\r\n\r\n    _viewportAddRemoveClass(allOverflowStyleClassNames);\r\n    _viewportAddRemoveClass(\r\n      (keys(viewportOverflowStyle) as Array<keyof typeof viewportOverflowStyle>)\r\n        .map((axis) =>\r\n          createViewportOverflowStyleClassName(viewportOverflowStyle[axis], axis === 'x')\r\n        )\r\n        .join(' '),\r\n      true\r\n    );\r\n  };\r\n\r\n  return (\r\n    { _checkOption, _observersUpdateHints, _observersState, _force },\r\n    { _paddingStyleChanged }\r\n  ) => {\r\n    const { _sizeChanged, _contentMutation, _directionChanged, _appear, _scrollbarSizeChanged } =\r\n      _observersUpdateHints || {};\r\n    const scrollbarsHidingPluginViewportArrangement =\r\n      scrollbarsHidingPlugin &&\r\n      scrollbarsHidingPlugin._viewportArrangement(\r\n        structureSetupElements,\r\n        structureSetupState,\r\n        _observersState,\r\n        env,\r\n        _checkOption\r\n      );\r\n\r\n    const { _arrangeViewport, _undoViewportArrange, _hideNativeScrollbars } =\r\n      scrollbarsHidingPluginViewportArrangement || {};\r\n\r\n    const [showNativeOverlaidScrollbars, showNativeOverlaidScrollbarsChanged] =\r\n      getShowNativeOverlaidScrollbars(_checkOption, env);\r\n    const [overflow, overflowChanged] = _checkOption('overflow');\r\n    const overflowXVisible = overflowIsVisible(overflow.x);\r\n    const overflowYVisible = overflowIsVisible(overflow.y);\r\n\r\n    const viewportChanged =\r\n      true ||\r\n      _sizeChanged ||\r\n      _paddingStyleChanged ||\r\n      _contentMutation ||\r\n      _directionChanged ||\r\n      _scrollbarSizeChanged ||\r\n      showNativeOverlaidScrollbarsChanged;\r\n\r\n    let sizeFractionCache = getCurrentSizeFraction(_force);\r\n    let viewportScrollSizeCache = getCurrentViewportScrollSizeCache(_force);\r\n    let overflowAmuntCache = getCurrentOverflowAmountCache(_force);\r\n    let overflowEdgeCache = getCurrentOverflowEdgeCache(_force);\r\n\r\n    if (showNativeOverlaidScrollbarsChanged && _nativeScrollbarsHiding) {\r\n      _viewportAddRemoveClass(dataValueViewportScrollbarHidden, !showNativeOverlaidScrollbars);\r\n    }\r\n\r\n    if (viewportChanged) {\r\n      if (hasAttrClass(_host, dataAttributeHost, dataValueNoClipping)) {\r\n        setMeasuringMode(true);\r\n      }\r\n\r\n      const [redoViewportArrange] = _undoViewportArrange ? _undoViewportArrange() : [];\r\n\r\n      const [sizeFraction] = (sizeFractionCache = updateSizeFraction(_force));\r\n      const [viewportScrollSize] = (viewportScrollSizeCache =\r\n        updateViewportScrollSizeCache(_force));\r\n      const viewportClientSize = getClientSize(_viewport);\r\n      const windowInnerSize = viewportIsTargetBody && getWindowSize(_windowElm());\r\n      const overflowAmountScrollSize = {\r\n        w: max0(viewportScrollSize.w + sizeFraction.w),\r\n        h: max0(viewportScrollSize.h + sizeFraction.h),\r\n      };\r\n\r\n      const overflowAmountClientSize = {\r\n        w: max0(\r\n          (windowInnerSize\r\n            ? windowInnerSize.w\r\n            : viewportClientSize.w + max0(viewportClientSize.w - viewportScrollSize.w)) +\r\n            sizeFraction.w\r\n        ),\r\n        h: max0(\r\n          (windowInnerSize\r\n            ? windowInnerSize.h\r\n            : viewportClientSize.h + max0(viewportClientSize.h - viewportScrollSize.h)) +\r\n            sizeFraction.h\r\n        ),\r\n      };\r\n\r\n      redoViewportArrange && redoViewportArrange();\r\n\r\n      overflowEdgeCache = updateOverflowEdge(overflowAmountClientSize);\r\n      overflowAmuntCache = updateOverflowAmountCache(\r\n        getOverflowAmount(overflowAmountScrollSize, overflowAmountClientSize),\r\n        _force\r\n      );\r\n    }\r\n\r\n    const [overflowEdge, overflowEdgeChanged] = overflowEdgeCache;\r\n    const [overflowAmount, overflowAmountChanged] = overflowAmuntCache;\r\n    const [viewportScrollSize, viewportScrollSizeChanged] = viewportScrollSizeCache;\r\n    const [sizeFraction, sizeFractionChanged] = sizeFractionCache;\r\n    const [hasOverflow, hasOverflowChanged] = updateHasOverflowCache({\r\n      x: overflowAmount.w > 0,\r\n      y: overflowAmount.h > 0,\r\n    });\r\n    const removeClipping =\r\n      (overflowXVisible && overflowYVisible && (hasOverflow.x || hasOverflow.y)) ||\r\n      (overflowXVisible && hasOverflow.x && !hasOverflow.y) ||\r\n      (overflowYVisible && hasOverflow.y && !hasOverflow.x);\r\n    const adjustViewportStyle =\r\n      _paddingStyleChanged ||\r\n      _directionChanged ||\r\n      _scrollbarSizeChanged ||\r\n      sizeFractionChanged ||\r\n      viewportScrollSizeChanged ||\r\n      overflowEdgeChanged ||\r\n      overflowAmountChanged ||\r\n      overflowChanged ||\r\n      showNativeOverlaidScrollbarsChanged ||\r\n      viewportChanged;\r\n    const viewportOverflowState = createViewportOverflowState(hasOverflow, overflow);\r\n    const [overflowStyle, overflowStyleChanged] = updateOverflowStyleCache(\r\n      viewportOverflowState._overflowStyle\r\n    );\r\n    const [flowDirectionStyles, flowDirectionStylesChanged] = updateFlowDirectionStyles(_force);\r\n\r\n    const adjustMeasuredScrollCoordinates =\r\n      _directionChanged || _appear || flowDirectionStylesChanged || hasOverflowChanged || _force;\r\n    const [scrollCoordinates, scrollCoordinatesChanged] = adjustMeasuredScrollCoordinates\r\n      ? updateMeasuredScrollCoordinates(getMeasuredScrollCoordinates(flowDirectionStyles), _force)\r\n      : getCurrentMeasuredScrollCoordinates();\r\n\r\n    if (adjustViewportStyle) {\r\n      overflowStyleChanged && setViewportOverflowStyle(viewportOverflowState._overflowStyle);\r\n\r\n      if (_hideNativeScrollbars && _arrangeViewport) {\r\n        setStyles(\r\n          _viewport,\r\n          _hideNativeScrollbars(\r\n            viewportOverflowState,\r\n            _observersState,\r\n            _arrangeViewport(viewportOverflowState, viewportScrollSize, sizeFraction)\r\n          )\r\n        );\r\n      }\r\n    }\r\n\r\n    setMeasuringMode(false);\r\n\r\n    addRemoveAttrClass(_host, dataAttributeHost, dataValueNoClipping, removeClipping);\r\n    addRemoveAttrClass(_padding, dataAttributePadding, dataValueNoClipping, removeClipping);\r\n\r\n    assignDeep(structureSetupState, {\r\n      _overflowStyle: overflowStyle,\r\n      _overflowEdge: {\r\n        x: overflowEdge.w,\r\n        y: overflowEdge.h,\r\n      },\r\n      _overflowAmount: {\r\n        x: overflowAmount.w,\r\n        y: overflowAmount.h,\r\n      },\r\n      _hasOverflow: hasOverflow,\r\n      _scrollCoordinates: sanitizeScrollCoordinates(scrollCoordinates, overflowAmount),\r\n    });\r\n\r\n    return {\r\n      _overflowStyleChanged: overflowStyleChanged,\r\n      _overflowEdgeChanged: overflowEdgeChanged,\r\n      _overflowAmountChanged: overflowAmountChanged,\r\n      _scrollCoordinatesChanged: scrollCoordinatesChanged || overflowAmountChanged,\r\n      _scrolled: adjustMeasuredScrollCoordinates,\r\n    };\r\n  };\r\n};\r\n", "import type { TRBL, XY, ScrollCoordinates } from '../../support';\r\nimport type { StructureSetupElementsObj } from './structureSetup.elements';\r\nimport type {\r\n  ObserversSetupState,\r\n  ObserversSetupUpdateHints,\r\n  Setup,\r\n  SetupUpdateInfo,\r\n} from '../../setups';\r\nimport type { InitializationTarget } from '../../initialization';\r\nimport type { StyleObject, OverflowStyle } from '../../typings';\r\nimport {\r\n  assignDeep,\r\n  each,\r\n  getElementScroll,\r\n  getZeroScrollCoordinates,\r\n  scrollElementTo,\r\n  strHidden,\r\n  strMarginBottom,\r\n  strMarginLeft,\r\n  strMarginRight,\r\n  strPaddingBottom,\r\n  strPaddingLeft,\r\n  strPaddingRight,\r\n  strPaddingTop,\r\n} from '../../support';\r\nimport { getEnvironment } from '../../environment';\r\nimport { createStructureSetupElements } from './structureSetup.elements';\r\nimport {\r\n  createOverflowUpdateSegment,\r\n  createPaddingUpdateSegment,\r\n  createTrinsicUpdateSegment,\r\n} from './updateSegments';\r\n\r\nexport interface StructureSetupState {\r\n  _padding: TRBL;\r\n  _paddingAbsolute: boolean;\r\n  _viewportPaddingStyle: StyleObject;\r\n  _overflowEdge: XY<number>;\r\n  _overflowAmount: XY<number>;\r\n  _overflowStyle: XY<OverflowStyle>;\r\n  _hasOverflow: XY<boolean>;\r\n  _scrollCoordinates: ScrollCoordinates;\r\n}\r\n\r\nexport interface StructureSetupUpdateInfo extends SetupUpdateInfo {\r\n  _observersState: ObserversSetupState;\r\n  _observersUpdateHints?: ObserversSetupUpdateHints;\r\n}\r\n\r\nexport type StructureSetupUpdateHints = {\r\n  _overflowEdgeChanged?: boolean;\r\n  _overflowAmountChanged?: boolean;\r\n  _overflowStyleChanged?: boolean;\r\n  _paddingStyleChanged?: boolean;\r\n  _scrollCoordinatesChanged?: boolean;\r\n};\r\n\r\nexport type StructureSetup = [\r\n  ...Setup<StructureSetupUpdateInfo, StructureSetupState, StructureSetupUpdateHints>,\r\n  /** The elements created by the structure setup. */\r\n  StructureSetupElementsObj,\r\n  /** Function to be called when the initialization was canceled. */\r\n  () => void,\r\n];\r\n\r\nexport type StructureUpdateSegment = (\r\n  updateInfo: StructureSetupUpdateInfo,\r\n  updateHints: Readonly<StructureSetupUpdateHints>\r\n) => StructureSetupUpdateHints | void;\r\n\r\nexport type CreateStructureUpdateSegment = (\r\n  structureSetupElements: StructureSetupElementsObj,\r\n  state: StructureSetupState\r\n) => StructureUpdateSegment;\r\n\r\nexport const createStructureSetup = (target: InitializationTarget): StructureSetup => {\r\n  const [elements, appendStructureElements, canceled] = createStructureSetupElements(target);\r\n  const state: StructureSetupState = {\r\n    _padding: {\r\n      t: 0,\r\n      r: 0,\r\n      b: 0,\r\n      l: 0,\r\n    },\r\n    _paddingAbsolute: false,\r\n    _viewportPaddingStyle: {\r\n      [strMarginRight]: 0,\r\n      [strMarginBottom]: 0,\r\n      [strMarginLeft]: 0,\r\n      [strPaddingTop]: 0,\r\n      [strPaddingRight]: 0,\r\n      [strPaddingBottom]: 0,\r\n      [strPaddingLeft]: 0,\r\n    },\r\n    _overflowEdge: { x: 0, y: 0 },\r\n    _overflowAmount: { x: 0, y: 0 },\r\n    _overflowStyle: {\r\n      x: strHidden,\r\n      y: strHidden,\r\n    },\r\n    _hasOverflow: {\r\n      x: false,\r\n      y: false,\r\n    },\r\n    _scrollCoordinates: getZeroScrollCoordinates(),\r\n  };\r\n  const { _target, _scrollOffsetElement, _viewportIsTarget, _removeScrollObscuringStyles } =\r\n    elements;\r\n  const { _nativeScrollbarsHiding, _nativeScrollbarsOverlaid } = getEnvironment();\r\n  const doViewportArrange =\r\n    !_nativeScrollbarsHiding && (_nativeScrollbarsOverlaid.x || _nativeScrollbarsOverlaid.y);\r\n\r\n  const updateSegments: StructureUpdateSegment[] = [\r\n    createTrinsicUpdateSegment(elements, state),\r\n    createPaddingUpdateSegment(elements, state),\r\n    createOverflowUpdateSegment(elements, state),\r\n  ];\r\n\r\n  return [\r\n    appendStructureElements,\r\n    (updateInfo) => {\r\n      const updateHints: StructureSetupUpdateHints = {};\r\n      const adjustScrollOffset = doViewportArrange;\r\n      const scrollOffset = adjustScrollOffset && getElementScroll(_scrollOffsetElement);\r\n      const revertScrollObscuringStyles = scrollOffset && _removeScrollObscuringStyles();\r\n\r\n      each(updateSegments, (updateSegment) => {\r\n        assignDeep(updateHints, updateSegment(updateInfo, updateHints) || {});\r\n      });\r\n\r\n      scrollElementTo(_scrollOffsetElement, scrollOffset);\r\n      revertScrollObscuringStyles && revertScrollObscuringStyles();\r\n      !_viewportIsTarget && scrollElementTo(_target, 0);\r\n\r\n      return updateHints;\r\n    },\r\n    state,\r\n    elements,\r\n    canceled,\r\n  ];\r\n};\r\n", "import type { OptionsCheckFn, Options, PartialOptions, ReadonlyOptions } from '../options';\r\nimport type { DeepReadonly } from '../typings';\r\nimport type { InitializationTarget } from '../initialization';\r\nimport type { ObserversSetupState, ObserversSetupUpdateHints } from './observersSetup';\r\nimport type { StructureSetupState, StructureSetupUpdateHints } from './structureSetup';\r\nimport type { StructureSetupElementsObj } from './structureSetup/structureSetup.elements';\r\nimport type { ScrollbarsSetupElementsObj } from './scrollbarsSetup/scrollbarsSetup.elements';\r\nimport { createOptionCheck } from '../options';\r\nimport {\r\n  assignDeep,\r\n  bind,\r\n  getElementScroll,\r\n  isEmptyObject,\r\n  keys,\r\n  runEachAndClear,\r\n  scrollElementTo,\r\n} from '../support';\r\nimport { createObserversSetup } from './observersSetup';\r\nimport { createScrollbarsSetup } from './scrollbarsSetup';\r\nimport { createStructureSetup } from './structureSetup';\r\n\r\nexport type SetupUpdateHints = Partial<Record<string, boolean>>;\r\n\r\nexport type SetupUpdateInfo = {\r\n  _checkOption: OptionsCheckFn<Options>;\r\n  _changedOptions: PartialOptions;\r\n  _force: boolean;\r\n};\r\n\r\nexport type Setup<\r\n  U extends SetupUpdateInfo,\r\n  S extends Readonly<Record<string, any>>,\r\n  H extends SetupUpdateHints | void,\r\n> = [\r\n  /** The create function which returns the `destroy` function. */\r\n  _create: () => () => void,\r\n  /** Function which updates the setup and returns the update result. */\r\n  _update: (updateInfo: U) => H,\r\n  /** Function which returns the current state. */\r\n  _state: S,\r\n];\r\n\r\nexport interface SetupsUpdateInfo {\r\n  /** The options that changed or `undefined` if none changed. */\r\n  _changedOptions?: PartialOptions;\r\n  /** Whether chache should be ignored. */\r\n  _force?: boolean;\r\n  /** Whether observers should take their records and thus update as well. */\r\n  _takeRecords?: boolean;\r\n  /** Whether one or more scrollbars has been cloned. */\r\n  _cloneScrollbar?: boolean;\r\n}\r\n\r\nexport interface SetupsUpdateHints {\r\n  readonly _observersUpdateHints: DeepReadonly<ObserversSetupUpdateHints>;\r\n  readonly _structureUpdateHints: DeepReadonly<StructureSetupUpdateHints>;\r\n}\r\n\r\nexport interface SetupsState {\r\n  readonly _observersSetupState: DeepReadonly<ObserversSetupState>;\r\n  readonly _structureSetupState: DeepReadonly<StructureSetupState>;\r\n}\r\n\r\nexport interface SetupsElements {\r\n  readonly _structureSetupElements: DeepReadonly<StructureSetupElementsObj>;\r\n  readonly _scrollbarsSetupElements: DeepReadonly<ScrollbarsSetupElementsObj>;\r\n}\r\n\r\nexport type Setups = [\r\n  construct: () => () => void,\r\n  update: (updateInfo: SetupsUpdateInfo) => boolean,\r\n  getState: () => SetupsState,\r\n  elements: SetupsElements,\r\n  canceled: () => void,\r\n];\r\n\r\nexport const createSetups = (\r\n  target: InitializationTarget,\r\n  options: ReadonlyOptions,\r\n  isDestroyed: () => boolean,\r\n  onUpdated: (updateInfo: SetupsUpdateInfo, updateHints: SetupsUpdateHints) => void,\r\n  onScroll: (scrollEvent: Event) => void\r\n): Setups => {\r\n  let cacheAndOptionsInitialized = false;\r\n  const getCurrentOption = createOptionCheck(options, {});\r\n  const [\r\n    structureSetupCreate,\r\n    structureSetupUpdate,\r\n    structureSetupState,\r\n    structureSetupElements,\r\n    structureSetupCanceled,\r\n  ] = createStructureSetup(target);\r\n  const [observersSetupCreate, observersSetupUpdate, observersSetupState] = createObserversSetup(\r\n    structureSetupElements,\r\n    structureSetupState,\r\n    getCurrentOption,\r\n    (observersUpdateHints) => {\r\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\r\n      update({}, observersUpdateHints);\r\n    }\r\n  );\r\n  const [scrollbarsSetupCreate, scrollbarsSetupUpdate, , scrollbarsSetupElements] =\r\n    createScrollbarsSetup(\r\n      target,\r\n      options,\r\n      observersSetupState,\r\n      structureSetupState,\r\n      structureSetupElements,\r\n      onScroll\r\n    );\r\n\r\n  const updateHintsAreTruthy = (hints: SetupUpdateHints) =>\r\n    keys(hints).some((key) => !!hints[key as keyof typeof hints]);\r\n\r\n  const update = (\r\n    updateInfo: SetupsUpdateInfo,\r\n    observerUpdateHints?: ObserversSetupUpdateHints\r\n  ): boolean => {\r\n    if (isDestroyed()) {\r\n      return false;\r\n    }\r\n\r\n    const {\r\n      _changedOptions: rawChangedOptions,\r\n      _force: rawForce,\r\n      _takeRecords,\r\n      _cloneScrollbar,\r\n    } = updateInfo;\r\n\r\n    const _changedOptions = rawChangedOptions || {};\r\n    const _force = !!rawForce || !cacheAndOptionsInitialized;\r\n    const baseUpdateInfoObj: SetupUpdateInfo = {\r\n      _checkOption: createOptionCheck(options, _changedOptions, _force),\r\n      _changedOptions,\r\n      _force,\r\n    };\r\n\r\n    if (_cloneScrollbar) {\r\n      scrollbarsSetupUpdate(baseUpdateInfoObj);\r\n      return false;\r\n    }\r\n\r\n    const observersHints =\r\n      observerUpdateHints ||\r\n      observersSetupUpdate(\r\n        assignDeep({}, baseUpdateInfoObj, {\r\n          _takeRecords,\r\n        })\r\n      );\r\n\r\n    const structureHints = structureSetupUpdate(\r\n      assignDeep({}, baseUpdateInfoObj, {\r\n        _observersState: observersSetupState,\r\n        _observersUpdateHints: observersHints,\r\n      })\r\n    );\r\n\r\n    scrollbarsSetupUpdate(\r\n      assignDeep({}, baseUpdateInfoObj, {\r\n        _observersUpdateHints: observersHints,\r\n        _structureUpdateHints: structureHints,\r\n      })\r\n    );\r\n\r\n    const truthyObserversHints = updateHintsAreTruthy(observersHints);\r\n    const truthyStructureHints = updateHintsAreTruthy(structureHints);\r\n    const changed =\r\n      truthyObserversHints || truthyStructureHints || !isEmptyObject(_changedOptions) || _force;\r\n\r\n    cacheAndOptionsInitialized = true;\r\n\r\n    changed &&\r\n      onUpdated(updateInfo, {\r\n        _observersUpdateHints: observersHints,\r\n        _structureUpdateHints: structureHints,\r\n      });\r\n\r\n    return changed;\r\n  };\r\n\r\n  return [\r\n    () => {\r\n      const { _originalScrollOffsetElement, _scrollOffsetElement, _removeScrollObscuringStyles } =\r\n        structureSetupElements;\r\n      const initialScroll = getElementScroll(_originalScrollOffsetElement);\r\n      const destroyFns = [observersSetupCreate(), structureSetupCreate(), scrollbarsSetupCreate()];\r\n      const revertScrollObscuringStyles = _removeScrollObscuringStyles();\r\n\r\n      scrollElementTo(_scrollOffsetElement, initialScroll);\r\n      revertScrollObscuringStyles();\r\n\r\n      return bind(runEachAndClear, destroyFns);\r\n    },\r\n    update,\r\n    () => ({\r\n      _observersSetupState: observersSetupState,\r\n      _structureSetupState: structureSetupState,\r\n    }),\r\n    {\r\n      _structureSetupElements: structureSetupElements,\r\n      _scrollbarsSetupElements: scrollbarsSetupElements,\r\n    },\r\n    structureSetupCanceled,\r\n  ];\r\n};\r\n", "import type { OverlayScrollbars } from './overlayscrollbars';\r\n\r\nconst targetInstanceMap: WeakMap<Element, OverlayScrollbars> = new WeakMap();\r\n\r\n/**\r\n * Adds the given OverlayScrollbars instance to the given element.\r\n * @param target The element which is the target of the OverlayScrollbars instance.\r\n * @param osInstance The OverlayScrollbars instance.\r\n */\r\nexport const addInstance = (target: Element, osInstance: OverlayScrollbars): void => {\r\n  targetInstanceMap.set(target, osInstance);\r\n};\r\n\r\n/**\r\n * Removes a OverlayScrollbars instance from the given element.\r\n * @param target The element from which its OverlayScrollbars instance shall be removed.\r\n */\r\nexport const removeInstance = (target: Element): void => {\r\n  targetInstanceMap.delete(target);\r\n};\r\n\r\n/**\r\n * Gets the OverlayScrollbars from the given element or undefined if it doesn't have one.\r\n * @param target The element of which its OverlayScrollbars instance shall be get.\r\n */\r\nexport const getInstance = (target: Element): OverlayScrollbars | undefined =>\r\n  targetInstanceMap.get(target);\r\n", "import type { XY, TRBL } from './support';\r\nimport type { Options, PartialOptions, ReadonlyOptions } from './options';\r\nimport type {\r\n  InferInstancePluginModuleInstance,\r\n  InferStaticPluginModuleInstance,\r\n  InstancePlugin,\r\n  OptionsValidationPlugin,\r\n  Plugin,\r\n  PluginModuleInstance,\r\n  StaticPlugin,\r\n} from './plugins';\r\nimport type { Initialization, InitializationTarget, PartialInitialization } from './initialization';\r\nimport type { OverflowStyle } from './typings';\r\nimport type { EventListenerArgs, EventListener, EventListeners } from './eventListeners';\r\nimport type {\r\n  ScrollbarsSetupElement,\r\n  ScrollbarStructure,\r\n} from './setups/scrollbarsSetup/scrollbarsSetup.elements';\r\nimport {\r\n  addPlugins,\r\n  getStaticPluginModuleInstance,\r\n  optionsValidationPluginModuleName,\r\n  pluginModules,\r\n  registerPluginModuleInstances,\r\n} from './plugins';\r\nimport { createSetups } from './setups';\r\nimport { addInstance, getInstance, removeInstance } from './instances';\r\nimport { cancelInitialization } from './initialization';\r\nimport { getEnvironment } from './environment';\r\nimport { getOptionsDiff } from './options';\r\nimport {\r\n  assignDeep,\r\n  isEmptyObject,\r\n  isFunction,\r\n  isHTMLElement,\r\n  createEventListenerHub,\r\n  isPlainObject,\r\n  keys,\r\n  isArray,\r\n  push,\r\n  runEachAndClear,\r\n  bind,\r\n  removeUndefinedProperties,\r\n} from './support';\r\nimport { setNonce } from './nonce';\r\nimport { setTrustedTypePolicy } from './trustedTypePolicy';\r\n\r\n// Notes:\r\n// Height intrinsic detection use \"content: true\" init strategy - or open ticket for custom height intrinsic observer\r\n\r\n/**\r\n * Describes the OverlayScrollbars environment.\r\n */\r\nexport interface Environment {\r\n  /** The native scrollbars size of the browser / system. */\r\n  scrollbarsSize: XY<number>;\r\n  /** Whether the native scrollbars are overlaid. */\r\n  scrollbarsOverlaid: XY<boolean>;\r\n  /** Whether the browser supports native scrollbars hiding. */\r\n  scrollbarsHiding: boolean;\r\n  /** Whether the browser supports the ScrollTimeline API. */\r\n  scrollTimeline: boolean;\r\n  /** The default Initialization to use if nothing else is specified. */\r\n  staticDefaultInitialization: Initialization;\r\n  /** The default Options to use if nothing else is specified. */\r\n  staticDefaultOptions: Options;\r\n\r\n  /** Returns the current default Initialization. */\r\n  getDefaultInitialization(): Initialization;\r\n  /** Returns the current default Options. */\r\n  getDefaultOptions(): Options;\r\n\r\n  /**\r\n   * Sets a new default Initialization.\r\n   * If the new default Initialization is partially filled, its deeply merged with the current default Initialization.\r\n   * @param newDefaultInitialization The new default Initialization.\r\n   * @returns The current default Initialization.\r\n   */\r\n  setDefaultInitialization(newDefaultInitialization: PartialInitialization): Initialization;\r\n  /**\r\n   * Sets new default Options.\r\n   * If the new default Options are partially filled, they're deeply merged with the current default Options.\r\n   * @param newDefaultOptions The new default Options.\r\n   * @returns The current default options.\r\n   */\r\n  setDefaultOptions(newDefaultOptions: PartialOptions): Options;\r\n}\r\n\r\n/**\r\n * The primary entry point to OverlayScrollbars.\r\n */\r\nexport interface OverlayScrollbarsStatic {\r\n  /**\r\n   * Returns the current OverlayScrollbars instance if the target already has an instance.\r\n   * @param target The initialization target to from which the instance shall be returned.\r\n   */\r\n  (target: InitializationTarget): OverlayScrollbars | undefined;\r\n  /**\r\n   * Initializes a new OverlayScrollbars instance to the given target\r\n   * or returns the current OverlayScrollbars instance if the target already has an instance.\r\n   * @param target The target.\r\n   * @param options The options. (Can be just an empty object)\r\n   * @param eventListeners Optional event listeners.\r\n   */\r\n  (\r\n    target: InitializationTarget,\r\n    options: PartialOptions,\r\n    eventListeners?: EventListeners\r\n  ): OverlayScrollbars;\r\n\r\n  /**\r\n   * Checks whether the passed value is a valid and not destroyed overlayscrollbars instance.\r\n   * @param osInstance The value which shall be checked.\r\n   */\r\n  valid(osInstance: any): osInstance is OverlayScrollbars;\r\n  /**\r\n   * Gets the environment.\r\n   */\r\n  env(): Environment;\r\n  /**\r\n   * Sets the nonce attribute for inline styles.\r\n   */\r\n  nonce(newNonce: string | undefined): void;\r\n  /**\r\n   * Sets the trusted type policy used for DOM operations.\r\n   */\r\n  trustedTypePolicy(newTrustedTypePolicy: unknown | undefined): void;\r\n  /**\r\n   * Adds a single plugin.\r\n   * @param plugin The plugin to be added.\r\n   * @returns The plugins static modules instance or `void` if no instance was found.\r\n   */\r\n  plugin<P extends Plugin>(\r\n    plugin: P\r\n  ): P extends StaticPlugin ? InferStaticPluginModuleInstance<P> : void;\r\n  /**\r\n   * Adds multiple plugins.\r\n   * @param plugins The plugins to be added.\r\n   * @returns The plugins static modules instances or `void` if no instance was found.\r\n   */\r\n  plugin<P extends [Plugin, ...Plugin[]]>(\r\n    plugins: P\r\n  ): P extends [Plugin, ...Plugin[]]\r\n    ? {\r\n        [K in keyof P]: P[K] extends StaticPlugin ? InferStaticPluginModuleInstance<P[K]> : void;\r\n      }\r\n    : void;\r\n}\r\n\r\n/**\r\n * Describes a OverlayScrollbars instances state.\r\n */\r\nexport interface State {\r\n  /** Describes the current padding in pixel. */\r\n  padding: TRBL;\r\n  /** Whether the current padding is absolute. */\r\n  paddingAbsolute: boolean;\r\n  /** The client width (x) & height (y) of the viewport in pixel. */\r\n  overflowEdge: XY<number>;\r\n  /** The overflow amount in pixel. */\r\n  overflowAmount: XY<number>;\r\n  /** The css overflow style of the viewport. */\r\n  overflowStyle: XY<OverflowStyle>;\r\n  /** Whether the viewport has an overflow. */\r\n  hasOverflow: XY<boolean>;\r\n  /** The scroll coordinates of the viewport. */\r\n  scrollCoordinates: {\r\n    /** The start (origin) scroll coordinates for each axis. */\r\n    start: XY<number>;\r\n    /** The end scroll coordinates for each axis. */\r\n    end: XY<number>;\r\n  };\r\n  /** Whether the direction is considered rtl. */\r\n  directionRTL: boolean;\r\n  /** Whether the instance is considered destroyed. */\r\n  destroyed: boolean;\r\n}\r\n\r\n/**\r\n * Describes the elements of a scrollbar.\r\n */\r\nexport interface ScrollbarElements {\r\n  /**\r\n   * The root element of the scrollbar.\r\n   * The HTML structure looks like this:\r\n   * <scrollbar>\r\n   *   <track>\r\n   *     <handle />\r\n   *   </track>\r\n   * </scrollbar>\r\n   */\r\n  scrollbar: HTMLElement;\r\n  /** The track element of the scrollbar. */\r\n  track: HTMLElement;\r\n  /** The handle element of the scrollbar. */\r\n  handle: HTMLElement;\r\n}\r\n\r\n/**\r\n * Describes the elements of a scrollbar and provides the possibility to clone them.\r\n */\r\nexport interface CloneableScrollbarElements extends ScrollbarElements {\r\n  /**\r\n   * Clones the current scrollbar and returns the cloned elements.\r\n   * The returned elements aren't added to the DOM.\r\n   */\r\n  clone(): ScrollbarElements;\r\n}\r\n\r\n/**\r\n * Describes the elements of a OverlayScrollbars instance.\r\n */\r\nexport interface Elements {\r\n  /** The element the instance was applied to. */\r\n  target: HTMLElement;\r\n  /** The host element. Its the root of all other elements. */\r\n  host: HTMLElement;\r\n  /**\r\n   * The element which is responsible to apply correct paddings.\r\n   * Depending on the Initialization it can be the same as the viewport element.\r\n   */\r\n  padding: HTMLElement;\r\n  /** The element which is responsible to do any scrolling. */\r\n  viewport: HTMLElement;\r\n  /**\r\n   * The element which is responsible to hold the content.\r\n   * Depending on the Initialization it can be the same as the viewport element.\r\n   */\r\n  content: HTMLElement;\r\n  /**\r\n   * The element through which you can get the current `scrollLeft` or `scrollTop` offset.\r\n   * Depending on the target element it can be the same as the viewport element.\r\n   */\r\n  scrollOffsetElement: HTMLElement;\r\n  /**\r\n   * The element through which you can add `scroll` events.\r\n   * Depending on the target element it can be the same as the viewport element.\r\n   */\r\n  scrollEventElement: HTMLElement | Document;\r\n  /** The horizontal scrollbar elements. */\r\n  scrollbarHorizontal: CloneableScrollbarElements;\r\n  /** The vertical scrollbar elements. */\r\n  scrollbarVertical: CloneableScrollbarElements;\r\n}\r\n\r\n/**\r\n * Describes a OverlayScrollbars instance.\r\n */\r\nexport interface OverlayScrollbars {\r\n  /** Gets the current options of the instance. */\r\n  options(): Options;\r\n  /**\r\n   * Sets the options of the instance.\r\n   * If the new options are partially filled, they're deeply merged with either the current options or the current default options.\r\n   * @param newOptions The new options which should be applied.\r\n   * @param pure Whether the options should be reset before the new options are added.\r\n   * @returns Returns the current options of the instance.\r\n   */\r\n  options(newOptions: PartialOptions, pure?: boolean): Options;\r\n\r\n  /**\r\n   * Adds event listeners to the instance.\r\n   * @param eventListeners An object which contains the added listeners.\r\n   * @param pure Whether all already added event listeners should be removed before the new listeners are added.\r\n   * @returns Returns a function which removes the added listeners.\r\n   */\r\n  on(eventListeners: EventListeners, pure?: boolean): () => void;\r\n  /**\r\n   * Adds a single event listener to the instance.\r\n   * @param name The name of the event.\r\n   * @param listener The listener which is invoked on that event.\r\n   * @returns Returns a function which removes the added listeners.\r\n   */\r\n  on<N extends keyof EventListenerArgs>(name: N, listener: EventListener<N>): () => void;\r\n  /**\r\n   * Adds multiple event listeners to the instance.\r\n   * @param name The name of the event.\r\n   * @param listener The listeners which are invoked on that event.\r\n   * @returns Returns a function which removes the added listeners.\r\n   */\r\n  on<N extends keyof EventListenerArgs>(name: N, listener: EventListener<N>[]): () => void;\r\n\r\n  /**\r\n   * Removes a single event listener from the instance.\r\n   * @param name The name of the event.\r\n   * @param listener The listener which shall be removed.\r\n   */\r\n  off<N extends keyof EventListenerArgs>(name: N, listener: EventListener<N>): void;\r\n  /**\r\n   * Removes multiple event listeners from the instance.\r\n   * @param name The name of the event.\r\n   * @param listener The listeners which shall be removed.\r\n   */\r\n  off<N extends keyof EventListenerArgs>(name: N, listener: EventListener<N>[]): void;\r\n\r\n  /**\r\n   * Updates the instance.\r\n   * @param force Whether the update should force the cache to be invalidated.\r\n   * @returns A boolean which indicates whether the `update` event was triggered through this update.\r\n   * The update event is only triggered if something changed because of this update.\r\n   */\r\n  update(force?: boolean): boolean;\r\n  /** Returns the state of the instance. */\r\n  state(): State;\r\n  /** Returns the elements of the instance. */\r\n  elements(): Elements;\r\n  /** Destroys the instance and removes all added elements. */\r\n  destroy(): void;\r\n  /** Returns the instance of the passed plugin or `undefined` if no instance was found. */\r\n  plugin<P extends InstancePlugin>(osPlugin: P): InferInstancePluginModuleInstance<P> | undefined;\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-redeclare\r\nexport const OverlayScrollbars: OverlayScrollbarsStatic = (\r\n  target: InitializationTarget,\r\n  options?: PartialOptions,\r\n  eventListeners?: EventListeners\r\n) => {\r\n  const { _getDefaultOptions } = getEnvironment();\r\n  const targetIsElement = isHTMLElement(target);\r\n  const instanceTarget = targetIsElement ? target : target.target;\r\n  const potentialInstance = getInstance(instanceTarget);\r\n  if (options && !potentialInstance) {\r\n    let destroyed = false;\r\n    const destroyFns: (() => void)[] = [];\r\n    const instancePluginModuleInstances: Record<string, PluginModuleInstance> = {};\r\n    const validateOptions = (newOptions: PartialOptions) => {\r\n      const newOptionsWithoutUndefined = removeUndefinedProperties(newOptions, true);\r\n      const pluginValidate = getStaticPluginModuleInstance<typeof OptionsValidationPlugin>(\r\n        optionsValidationPluginModuleName\r\n      );\r\n      return pluginValidate\r\n        ? pluginValidate(newOptionsWithoutUndefined, true)\r\n        : newOptionsWithoutUndefined;\r\n    };\r\n    const currentOptions: ReadonlyOptions = assignDeep(\r\n      {},\r\n      _getDefaultOptions(),\r\n      validateOptions(options)\r\n    );\r\n    const [addPluginEvent, removePluginEvents, triggerPluginEvent] =\r\n      createEventListenerHub<EventListenerArgs>();\r\n    const [addInstanceEvent, removeInstanceEvents, triggerInstanceEvent] =\r\n      createEventListenerHub(eventListeners);\r\n    const triggerEvent: typeof triggerPluginEvent = (name, args) => {\r\n      triggerInstanceEvent(name, args);\r\n      triggerPluginEvent(name, args);\r\n    };\r\n    const [setupsConstruct, setupsUpdate, setupsState, setupsElements, setupsCanceled] =\r\n      createSetups(\r\n        target,\r\n        currentOptions,\r\n        () => destroyed,\r\n        ({ _changedOptions, _force }, { _observersUpdateHints, _structureUpdateHints }) => {\r\n          const {\r\n            _sizeChanged,\r\n            _directionChanged,\r\n            _heightIntrinsicChanged,\r\n            _contentMutation,\r\n            _hostMutation,\r\n            _appear,\r\n          } = _observersUpdateHints;\r\n\r\n          const {\r\n            _overflowEdgeChanged,\r\n            _overflowAmountChanged,\r\n            _overflowStyleChanged,\r\n            _scrollCoordinatesChanged,\r\n          } = _structureUpdateHints;\r\n\r\n          triggerEvent('updated', [\r\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\r\n            instance,\r\n            {\r\n              updateHints: {\r\n                sizeChanged: !!_sizeChanged,\r\n                directionChanged: !!_directionChanged,\r\n                heightIntrinsicChanged: !!_heightIntrinsicChanged,\r\n                overflowEdgeChanged: !!_overflowEdgeChanged,\r\n                overflowAmountChanged: !!_overflowAmountChanged,\r\n                overflowStyleChanged: !!_overflowStyleChanged,\r\n                scrollCoordinatesChanged: !!_scrollCoordinatesChanged,\r\n                contentMutation: !!_contentMutation,\r\n                hostMutation: !!_hostMutation,\r\n                appear: !!_appear,\r\n              },\r\n              changedOptions: _changedOptions || {},\r\n              force: !!_force,\r\n            },\r\n          ]);\r\n        },\r\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\r\n        (scrollEvent) => triggerEvent('scroll', [instance, scrollEvent])\r\n      );\r\n\r\n    const destroy = (canceled: boolean) => {\r\n      removeInstance(instanceTarget);\r\n      runEachAndClear(destroyFns);\r\n\r\n      destroyed = true;\r\n\r\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\r\n      triggerEvent('destroyed', [instance, canceled]);\r\n      removePluginEvents();\r\n      removeInstanceEvents();\r\n    };\r\n\r\n    const instance: OverlayScrollbars = {\r\n      options(newOptions?: PartialOptions, pure?: boolean) {\r\n        if (newOptions) {\r\n          const base = pure ? _getDefaultOptions() : {};\r\n          const changedOptions = getOptionsDiff(\r\n            currentOptions,\r\n            assignDeep(base, validateOptions(newOptions))\r\n          );\r\n          if (!isEmptyObject(changedOptions)) {\r\n            assignDeep(currentOptions, changedOptions);\r\n            setupsUpdate({ _changedOptions: changedOptions });\r\n          }\r\n        }\r\n        return assignDeep({}, currentOptions);\r\n      },\r\n      on: addInstanceEvent,\r\n      off: (name, listener) => {\r\n        name && listener && removeInstanceEvents(name, listener);\r\n      },\r\n      state() {\r\n        const { _observersSetupState, _structureSetupState } = setupsState();\r\n        const { _directionIsRTL } = _observersSetupState;\r\n        const {\r\n          _overflowEdge,\r\n          _overflowAmount,\r\n          _overflowStyle,\r\n          _hasOverflow,\r\n          _padding,\r\n          _paddingAbsolute,\r\n          _scrollCoordinates,\r\n        } = _structureSetupState;\r\n        return assignDeep(\r\n          {},\r\n          {\r\n            overflowEdge: _overflowEdge,\r\n            overflowAmount: _overflowAmount,\r\n            overflowStyle: _overflowStyle,\r\n            hasOverflow: _hasOverflow,\r\n            scrollCoordinates: {\r\n              start: _scrollCoordinates._start,\r\n              end: _scrollCoordinates._end,\r\n            },\r\n            padding: _padding,\r\n            paddingAbsolute: _paddingAbsolute,\r\n            directionRTL: _directionIsRTL,\r\n            destroyed,\r\n          }\r\n        );\r\n      },\r\n      elements() {\r\n        const {\r\n          _target,\r\n          _host,\r\n          _padding,\r\n          _viewport,\r\n          _content,\r\n          _scrollOffsetElement,\r\n          _scrollEventElement,\r\n        } = setupsElements._structureSetupElements;\r\n        const { _horizontal, _vertical } = setupsElements._scrollbarsSetupElements;\r\n        const translateScrollbarStructure = (\r\n          scrollbarStructure: ScrollbarStructure\r\n        ): ScrollbarElements => {\r\n          const { _handle, _track, _scrollbar } = scrollbarStructure;\r\n          return {\r\n            scrollbar: _scrollbar,\r\n            track: _track,\r\n            handle: _handle,\r\n          };\r\n        };\r\n        const translateScrollbarsSetupElement = (\r\n          scrollbarsSetupElement: ScrollbarsSetupElement\r\n        ): CloneableScrollbarElements => {\r\n          const { _scrollbarStructures, _clone } = scrollbarsSetupElement;\r\n          const translatedStructure = translateScrollbarStructure(_scrollbarStructures[0]);\r\n\r\n          return assignDeep({}, translatedStructure, {\r\n            clone: () => {\r\n              const result = translateScrollbarStructure(_clone());\r\n              setupsUpdate({ _cloneScrollbar: true });\r\n              return result;\r\n            },\r\n          });\r\n        };\r\n        return assignDeep(\r\n          {},\r\n          {\r\n            target: _target,\r\n            host: _host,\r\n            padding: _padding || _viewport,\r\n            viewport: _viewport,\r\n            content: _content || _viewport,\r\n            scrollOffsetElement: _scrollOffsetElement,\r\n            scrollEventElement: _scrollEventElement,\r\n            scrollbarHorizontal: translateScrollbarsSetupElement(_horizontal),\r\n            scrollbarVertical: translateScrollbarsSetupElement(_vertical),\r\n          }\r\n        );\r\n      },\r\n      update: (_force?: boolean) => setupsUpdate({ _force, _takeRecords: true }),\r\n      destroy: bind(destroy, false),\r\n      plugin: <P extends InstancePlugin>(plugin: P) =>\r\n        instancePluginModuleInstances[keys(plugin)[0]] as\r\n          | InferInstancePluginModuleInstance<P>\r\n          | undefined,\r\n    };\r\n\r\n    push(destroyFns, [setupsCanceled]);\r\n\r\n    // valid inside plugins\r\n    addInstance(instanceTarget, instance);\r\n\r\n    // init plugins\r\n    registerPluginModuleInstances(pluginModules, OverlayScrollbars, [\r\n      instance,\r\n      addPluginEvent,\r\n      instancePluginModuleInstances,\r\n    ]);\r\n\r\n    if (\r\n      cancelInitialization(\r\n        setupsElements._structureSetupElements._isBody,\r\n        !targetIsElement && target.cancel\r\n      )\r\n    ) {\r\n      destroy(true);\r\n      return instance;\r\n    }\r\n\r\n    push(destroyFns, setupsConstruct());\r\n\r\n    triggerEvent('initialized', [instance]);\r\n\r\n    instance.update();\r\n\r\n    return instance;\r\n  }\r\n  return potentialInstance!;\r\n};\r\n\r\nOverlayScrollbars.plugin = (plugins: Plugin | Plugin[]) => {\r\n  const isArr = isArray(plugins);\r\n  const pluginsToAdd: Plugin<string, void | PluginModuleInstance, void | PluginModuleInstance>[] =\r\n    isArr ? plugins : [plugins];\r\n  const result = pluginsToAdd.map(\r\n    (plugin) => registerPluginModuleInstances(plugin, OverlayScrollbars)[0]\r\n  );\r\n  addPlugins(pluginsToAdd);\r\n  return isArr ? result : (result[0] as any);\r\n};\r\nOverlayScrollbars.valid = (osInstance: any): osInstance is OverlayScrollbars => {\r\n  const hasElmsFn = osInstance && (osInstance as OverlayScrollbars).elements;\r\n  const elements = isFunction(hasElmsFn) && hasElmsFn();\r\n  return isPlainObject(elements) && !!getInstance(elements.target);\r\n};\r\nOverlayScrollbars.env = () => {\r\n  const {\r\n    _nativeScrollbarsSize,\r\n    _nativeScrollbarsOverlaid,\r\n    _nativeScrollbarsHiding,\r\n    _scrollTimeline,\r\n    _staticDefaultInitialization,\r\n    _staticDefaultOptions,\r\n    _getDefaultInitialization,\r\n    _setDefaultInitialization,\r\n    _getDefaultOptions,\r\n    _setDefaultOptions,\r\n  } = getEnvironment();\r\n  return assignDeep(\r\n    {},\r\n    {\r\n      scrollbarsSize: _nativeScrollbarsSize,\r\n      scrollbarsOverlaid: _nativeScrollbarsOverlaid,\r\n      scrollbarsHiding: _nativeScrollbarsHiding,\r\n      scrollTimeline: _scrollTimeline,\r\n      staticDefaultInitialization: _staticDefaultInitialization,\r\n      staticDefaultOptions: _staticDefaultOptions,\r\n\r\n      getDefaultInitialization: _getDefaultInitialization,\r\n      setDefaultInitialization: _setDefaultInitialization,\r\n      getDefaultOptions: _getDefaultOptions,\r\n      setDefaultOptions: _setDefaultOptions,\r\n    }\r\n  );\r\n};\r\nOverlayScrollbars.nonce = setNonce;\r\nOverlayScrollbars.trustedTypePolicy = setTrustedTypePolicy;\r\n"], "names": ["createCache", "options", "update", "o", "_initialValue", "i", "_equal", "u", "_alwaysUpdateValues", "_value", "_previous", "cacheUpdateContextual", "newValue", "force", "curr", "newVal", "changed", "cacheUpdateIsolated", "getCurrentCache", "<PERSON><PERSON><PERSON><PERSON>", "window", "HTMLElement", "document", "wnd", "mathMax", "Math", "max", "mathMin", "min", "mathRound", "round", "mathAbs", "abs", "mathSign", "sign", "cAF", "cancelAnimationFrame", "rAF", "requestAnimationFrame", "setT", "setTimeout", "clearT", "clearTimeout", "getApi", "name", "undefined", "MutationObserverConstructor", "IntersectionObserverConstructor", "ResizeObserverConstructor", "scrollT", "isUndefined", "obj", "isNull", "isNumber", "isString", "isBoolean", "isFunction", "isArray", "Array", "isObject", "isArrayLike", "length", "lengthCorrectFormat", "isPlainObject", "constructor", "Object", "isHTMLElement", "isElement", "Element", "animationCurrentTime", "performance", "now", "animateNumber", "from", "to", "duration", "onFrame", "easing", "animationFrameId", "timeStart", "finalDuration", "frame", "complete", "timeNow", "timeElapsed", "stopAnimation", "percent", "progress", "animationCompleted", "each", "source", "callback", "keys", "key", "inArray", "arr", "item", "indexOf", "concat", "a", "b", "push", "array", "items", "arrayIsSingleItem", "prototype", "apply", "createOrKeepArray", "value", "isEmptyArray", "deduplicateArray", "Set", "runEachAndClear", "args", "keep", "runFn", "fn", "strPaddingTop", "strPaddingRight", "strPaddingLeft", "strPaddingBottom", "strMarginLeft", "strMarginRight", "strMarginBottom", "strOverflowX", "strOverflowY", "str<PERSON>idth", "strHeight", "strVisible", "strH<PERSON>den", "strScroll", "capitalizeFirstLetter", "str", "finalStr", "String", "toUpperCase", "slice", "equal", "props", "propMutation", "result", "prop", "compareA", "compareB", "equalWH", "equalXY", "equalTRBL", "noop", "bind", "selfClearTimeout", "timeout", "id", "setTFn", "clearTFn", "debounce", "functionToDebounce", "_", "_timeout", "p", "_max<PERSON><PERSON>y", "v", "_leading", "S", "_mergeParams", "maxTimeoutId", "prevArguments", "latestArguments", "leadingInvoked", "clear", "invokeFunctionToDebounce", "this", "mergeParms", "flush", "debouncedFn", "arguments", "finalTimeout", "hasTimeout", "finalMaxWait", "hasMaxWait", "setTimeoutFn", "clearTimeoutFn", "mergeParamsResult", "invoked<PERSON>rgs", "boundInvoke", "timeoutId", "_flush", "hasOwnProperty", "call", "assignDeep", "target", "object1", "object2", "object3", "object4", "object5", "object6", "sources", "copy", "copyIsArray", "src", "clone", "removeUndefinedProperties", "deep", "isEmptyObject", "capNumber", "number", "getDomTokensArray", "tokens", "split", "filter", "token", "getAttr", "elm", "attrName", "getAttribute", "hasAttr", "hasAttribute", "setAttrs", "attrNames", "setAttribute", "removeAttrs", "removeAttribute", "domTokenListAttr", "initialArr", "setElmAttr", "domTokenListOperation", "operationTokens", "operation", "initialArrSet", "join", "_remove", "removeTokens", "_add", "addTokens", "_has", "hasTokens", "tokenSet", "reduce", "boolean", "includes", "removeAttrClass", "addAttrClass", "addRemoveAttrClass", "add", "hasAttrClass", "createDomTokenListClass", "removeClass", "className", "addClass", "find", "selector", "rootElm", "querySelectorAll", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "is", "matches", "isBodyElement", "contents", "childNodes", "parent", "parentElement", "closest", "getFocusedElement", "doc", "activeElement", "liesBetween", "highBoundarySelector", "deepBoundarySelector", "closestHighBoundaryElm", "closestDeepBoundaryElm", "deepBoundaryIsValid", "removeElements", "nodes", "node", "parentElm", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON><PERSON>", "children", "child", "append<PERSON><PERSON><PERSON>", "trustedTypePolicy", "getTrustedTypePolicy", "setTrustedTypePolicy", "newTrustedTypePolicy", "createDiv", "classNames", "div", "createElement", "createDOM", "html", "createdDiv", "trustedTypesPolicy", "trimmedHtml", "trim", "innerHTML", "createHTML", "getCSSVal", "computedStyle", "getPropertyValue", "validFiniteNumber", "notNaN", "isFinite", "parseToZeroOrNumber", "parseFloat", "roundCssNumber", "numberToCssPx", "setStyles", "styles", "rawValue", "elmStyle", "style", "setProperty", "_unused", "getStyles", "pseudoElm", "getSingleStyle", "getStylesResult", "getComputedStyle", "topRightBottomLeft", "propertyPrefix", "propertySuffix", "finalPrefix", "finalSuffix", "top", "right", "bottom", "left", "t", "r", "l", "getTrasformTranslateValue", "isHorizontal", "x", "y", "elementHasDimensions", "offsetWidth", "offsetHeight", "getClientRects", "zeroObj", "w", "h", "getElmWidthHeightProperty", "property", "getWindowSize", "customWnd", "getOffsetSize", "getClientSize", "getScrollSize", "getFractionalSize", "cssWidth", "cssHeight", "getBoundingClientRect", "hasDimensions", "domRectHasDimensions", "rect", "domRectAppeared", "currContentRect", "prevContentRect", "rectHasDimensions", "rectHadDimensions", "removeEventListener", "eventNames", "listener", "capture", "eventName", "addEventListener", "_ref", "passive", "_passive", "_capture", "once", "_once", "nativeOptions", "map", "finalListener", "evt", "stopPropagation", "preventDefault", "stopAndPrevent", "scrollElementTo", "position", "scrollLeft", "scrollTop", "getElementScroll", "getZeroScrollCoordinates", "_start", "_end", "sanitizeScrollCoordinates", "rawScrollCoordinates", "overflowAmount", "D", "M", "sanitizeAxis", "start", "end", "amount", "newStart", "newEnd", "startAbs", "endAbs", "startX", "endX", "startY", "endY", "isDefaultDirectionScrollCoordinates", "getAxis", "getScrollCoordinatesPercent", "currentScroll", "current", "focusElement", "element", "focus", "preventScroll", "manageListener", "createEventListenerHub", "initialEventListeners", "events", "Map", "removeEvent", "eventSet", "get", "currListener", "for<PERSON>ach", "addEvent", "nameOrEventListeners", "listenerOrPure", "set", "eventListenerKeys", "offFns", "eventListener", "triggerEvent", "event", "pluginModules", "staticPluginModuleInstances", "addPlugins", "addedPlugin", "plugin", "registerPluginModuleInstances", "staticObj", "instanceInfo", "static", "os<PERSON>tat<PERSON>", "instance", "osInstance", "instanceObj", "instancePluginMap", "ctor", "getStaticPluginModuleInstance", "pluginModuleName", "optionsValidationPluginModuleName", "dataAttributePrefix", "classNameEnvironment", "classNameEnvironmentScrollbarHidden", "dataAttributeInitialize", "dataValueNoClipping", "dataAttributeHtmlBody", "dataAttributeHost", "dataValueHostIsHost", "dataAttributeViewport", "dataValueViewportOverflowXPrefix", "dataValueViewportOverflowYPrefix", "dataValueViewportArrange", "dataValueViewportMeasuring", "dataValueViewportScrolling", "dataValueViewportScrollbarHidden", "dataValueViewportNoContent", "dataAttributePadding", "dataAttributeContent", "classNameSizeObserver", "classNameSizeObserverAppear", "classNameSizeObserverListener", "classNameSizeObserverListenerScroll", "classNameSizeObserverListenerItem", "classNameSizeObserverListenerItemFinal", "classNameTrinsicObserver", "classNameScrollbarThemeNone", "classNameScrollbar", "classNameScrollbarRtl", "classNameScrollbarHorizontal", "classNameScrollbarVertical", "classNameScrollbarTrack", "classNameScrollbarHandle", "classNameScrollbarVisible", "classNameScrollbarCornerless", "classNameScrollbarInteraction", "classNameScrollbarUnusable", "classNameScrollbarAutoHide", "classNameScrollbarAutoHideHidden", "classNameScrollbarWheel", "classNameScrollbarTrackInteractive", "classNameScrollbarHandleInteractive", "sizeObserverPluginName", "SizeObserverPlugin", "listenerElement", "onSizeChangedCallback", "observeAppearChange", "scrollAmount", "scrollEventName", "observer<PERSON>lement<PERSON><PERSON><PERSON><PERSON>", "observerElementChildrenRoot", "shrinkElement", "<PERSON><PERSON><PERSON><PERSON>", "expandElement", "<PERSON><PERSON><PERSON><PERSON>", "expandElementChild", "cacheSize", "currSize", "isDirty", "rAFId", "reset", "onResized", "appear", "onScroll", "scrollEvent", "destroyFns", "getShowNativeOverlaidScrollbars", "checkOption", "env", "T", "_nativeScrollbarsOverlaid", "showNativeOverlaidScrollbarsOption", "showNativeOverlaidScrollbarsChanged", "overflowIsVisible", "overflowBehavior", "createViewportOverflowState", "hasOverflow", "getAxisOverflowStyle", "axisBehavior", "axisHasOverflow", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perpendicularOverflow", "behaviorStyle", "replace", "axisOverflowVisible", "perpendicularOverflowVisible", "nonPerpendicularOverflow", "nonOverflow", "_overflowStyle", "_overflowScroll", "scrollbarsHidingPluginName", "ScrollbarsHidingPlugin", "_viewportArrangement", "structureSetupElements", "structureSetupState", "observersSetupState", "checkOptions", "L", "_viewportIsTarget", "U", "_viewport", "P", "_nativeScrollbarsHiding", "N", "_nativeScrollbarsSize", "doViewportArrange", "showNativeOverlaidScrollbars", "readViewportOverflowState", "getStatePerAxis", "styleKey", "overflowStyle", "overflowScroll", "xOverflowStyle", "xOverflowScroll", "yOverflowStyle", "yOverflowScroll", "_getViewportOverflowHideOffset", "viewportOverflowState", "R", "arrangeHideOffset", "getHideOffsetPerAxis", "isOverlaid", "nativeScrollbarSize", "nonScrollbarStylingHideOffset", "scrollbarsHideOffset", "scrollbarsHideOffsetArrange", "xScrollbarsHideOffset", "xScrollbarsHideOffsetArrange", "yScrollbarsHideOffset", "yScrollbarsHideOffsetArrange", "_scrollbarsHideOffset", "_scrollbarsHideOffsetArrange", "_hideNativeScrollbars", "_directionIsRTL", "viewportArrange", "viewportStyleObj", "q", "B", "arrangeX", "arrangeY", "hideOffsetX", "hideOffsetY", "j", "_viewportPaddingStyle", "horizontalMarginKey", "viewportHorizontalPaddingKey", "horizontalMarginValue", "verticalMarginValue", "horizontalPaddingValue", "verticalPaddingValue", "_arrangeViewport", "viewportScrollSize", "sizeFraction", "F", "viewportArrangeHorizontalPaddingKey", "viewportArrangeHorizontalPaddingValue", "viewportArrangeVerticalPaddingValue", "paddingTop", "fractionalContentWidth", "fractionalContenHeight", "arrangeSize", "_undoViewportArrange", "finalViewportOverflowState", "viewportPaddingStyle", "finalPaddingStyle", "assignProps", "prevStyle", "add<PERSON><PERSON><PERSON>", "clickScrollPluginModuleName", "ClickScrollPlugin", "moveHandleRelative", "targetOffset", "handleLength", "onClickScrollCompleted", "stopped", "stopPressAnimation", "linearScrollMs", "easedScrollMs", "setPressAnimationTimeout", "clearPressAnimationTimeout", "targetOffsetSign", "handleLengthWithTargetSign", "handleLengthWithTargetSignHalf", "easedEndPressAnimation", "linearPressAnimation", "linearFrom", "msFactor", "completed", "stopClickAnimation", "clickAnimationProgress", "clickAnimationCompleted", "remainingScrollDistance", "continueWithPress", "remainingLinearScrollDistance", "linearBridge", "stopClick", "opsStringify", "JSON", "stringify", "val", "getPropByPath", "path", "defaultOptions", "paddingAbsolute", "elementEvents", "attributes", "ignoreMutation", "overflow", "scrollbars", "theme", "visibility", "autoHide", "autoHideDelay", "autoHideSuspend", "dragScroll", "clickScroll", "pointers", "getOptionsDiff", "currOptions", "newOptions", "diff", "optionsKeys", "optionKey", "currOptionValue", "newOptionValue", "isDiff", "createOptionCheck", "changedOptions", "nonce", "getNonce", "setNonce", "newNonce", "environmentInstance", "createEnvironment", "getNativeScrollbarSize", "measureElm", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "body", "cSize", "oSize", "fSize", "getNativeScrollbarsHiding", "testElm", "revertClass", "envStyle", "envDOM", "envElm", "env<PERSON><PERSON><PERSON><PERSON><PERSON>", "styleElm", "updateNativeScrollbarSizeCache", "getNativeScrollbarSizeCache", "nativeScrollbarsSize", "nativeScrollbarsHiding", "nativeScrollbarsOverlaid", "staticDefaultInitialization", "elements", "host", "padding", "viewport", "content", "slot", "cancel", "staticDefaultOptions", "getDefaultOptions", "getDefaultInitialization", "_scrollTimeline", "_addResizeListener", "_getDefaultInitialization", "_setDefaultInitialization", "newInitializationStrategy", "_getDefaultOptions", "_setDefaultOptions", "newDefaultOptions", "_staticDefaultInitialization", "_staticDefaultOptions", "matchMedia", "addZoomListener", "onZoom", "media", "devicePixelRatio", "updatedNativeScrollbarSize", "nativeScrollbarSizeChanged", "getEnvironment", "createEventContentChange", "eventContentChange", "destroyed", "WeakMap", "destroy", "updateElements", "getElements", "eventElmList", "entries", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contains", "removeListener", "delete", "createDOMObserver", "isContentObserver", "isConnected", "ct", "_attributes", "rt", "_styleChangingAttributes", "lt", "_eventContentChange", "it", "_nestedTargetSelector", "ut", "_ignoreTarget<PERSON><PERSON>e", "_t", "_ignoreContentChange", "debouncedEventContentChange", "destroyEventContentChange", "updateEventContentChangeElements", "finalAttributes", "finalStyleChangingAttributes", "observedAttributes", "observerCallback", "fromRecords", "mutations", "ignoreTargetChange", "ignoreContentChange", "totalChangedNodes", "targetChangedAttrs", "targetStyleChanged", "contentChanged", "mutation", "attributeName", "<PERSON><PERSON><PERSON><PERSON>", "type", "oldValue", "addedNodes", "removedNodes", "isAttributesType", "isChildListType", "targetIsMutationTarget", "isAttrChange", "attributeValue", "attributeChanged", "styleChangingAttrChanged", "contentAttrChanged", "isNestedTarget", "baseAssertion", "contentFinalChanged", "mutationObserver", "observe", "attributeOldValue", "attributeFilter", "subtree", "childList", "characterData", "disconnect", "takeRecords", "createSizeObserver", "_appear", "sizeObserverPlugin", "updateResizeObserverContentRectCache", "baseElements", "sizeObserver", "onSizeChangedCallbackProxy", "sizeChangedContext", "isResizeObserverCall", "ResizeObserverEntry", "skip", "currRContentRect", "contentRect", "_sizeChanged", "resizeObserverInstance", "pop", "pluginAppearCallback", "pluginDestroyFns", "createTrinsicObserver", "onTrinsicChangedCallback", "intersectionObserverInstance", "isHeightIntrinsic", "ioEntryOrSize", "isIntersecting", "intersectionRatio", "trinsicObserver", "updateHeightIntrinsicCache", "triggerOnTrinsicChangedCallback", "updateValue", "heightIntrinsic", "heightIntrinsicChanged", "intersectionObserverCallback", "root", "onSizeChanged", "newSize", "createObserversSetup", "getCurrentOption", "onObserversUpdated", "debounceTimeout", "debounceMaxDelay", "updateContentMutationObserver", "destroyContentMutationObserver", "prevDirectionIsRTL", "hostSelector", "viewportSelector", "baseStyleChangingAttrs", "vt", "_target", "ht", "_host", "gt", "_scrollOffsetElement", "bt", "_content", "wt", "_isBody", "yt", "_viewportHasClass", "St", "_viewportAddRemoveClass", "<PERSON>t", "_removeScrollObscuringStyles", "getDirectionIsRTL", "state", "_heightIntrinsic", "scrollbarsHidingPlugin", "updateContentSizeCache", "viewportIsTargetBody", "noClipping", "isArranged", "scrollOffset", "revertScrollObscuringStyles", "revertMeasuring", "redoViewportArrange", "viewportScroll", "fractional", "onObserversUpdatedDebounced", "prev", "prevObj", "currObj", "setDirection", "updateHints", "newDirectionIsRTL", "_directionChanged", "onTrinsicChanged", "heightIntrinsicCache", "_heightIntrinsicChanged", "exclusiveSizeChange", "updateFn", "onContentMutation", "contentChangedThroughEvent", "_contentMutation", "onHostMutation", "_hostMutation", "constructTrinsicObserver", "updateTrinsicObserver", "constructSizeObserver", "constructHostMutationObserver", "updateHostMutationObserver", "viewportIsTargetResizeObserver", "onWindowResizeDebounced", "destroySizeObserver", "destroyTrinsicObserver", "destroyHostMutationObserver", "removeResizeListener", "_scrollbarSizeChanged", "_checkOption", "_takeRecords", "_force", "attributesChanged", "elementEventsChanged", "debounceValue", "debounce<PERSON><PERSON>ed", "contentMutationObserverChanged", "ignoreMutationFromOptions", "construct", "ignore", "max<PERSON><PERSON>", "hostUpdateResult", "trinsicUpdateResult", "contentUpdateResult", "resolveInitialization", "staticInitializationElement", "fallbackStaticInitializationElement", "defaultStaticInitializationElement", "staticInitializationElementValue", "staticInitialization", "resolvedInitialization", "dynamicInitializationElement", "fallbackDynamicInitializationElement", "defaultDynamicInitializationElement", "dynamicInitializationElementValue", "dynamicInitialization", "cancelInitialization", "isBody", "cancelInitializationValue", "Z", "defaultNativeScrollbarsOverlaid", "defaultbody", "resolvedNativeScrollbarsOverlaid", "resolvedDocumentScrollingElement", "finalNativeScrollbarsOverlaid", "finalDocumentScrollingElement", "createScrollbarsSetupElements", "scrollbarsSetupEvents", "cssCustomPropViewportPercent", "cssCustomPropScrollPercent", "cssCustomPropScrollDirection", "defaultInitScrollbars", "defaultInitScrollbarsSlot", "Mt", "_targetIsElm", "scrollbarsInit", "initScrollbarsSlot", "horizontalScrollbars", "verticalScrollbars", "evaluatedScrollbarSlot", "generalDynamicInitializationElement", "initScrollTimeline", "axis", "currAnimation", "currAnimationTransform", "timeline", "cancelAnimation", "_setScrollPercentAnimation", "structure", "Tt", "_scrollCoordinates", "defaultDirectionScroll", "transformArray", "transform", "reverse", "_handle", "animate", "scrollTimeline", "getViewportPercent", "Vt", "_overflowAmount", "Lt", "_overflowEdge", "getAxisValue", "axisViewportSize", "axisOverflowAmount", "scrollbarStructureAddRemoveClass", "scrollbarStructures", "action", "scrollbarStructure", "_scrollbar", "scrollbarStyle", "scrollbarsAddRemoveClass", "onlyHorizontal", "singleAxis", "runHorizontal", "runVertical", "refreshScrollbarsHandleLength", "viewportPercent", "createScrollbarStyleFn", "axisViewportPercent", "refreshScrollbarsHandleOffset", "scrollPercent", "axisScrollPercent", "refreshScrollbarsScrollCoordinates", "axisIsDefaultDirectionScrollCoordinates", "refreshScrollbarsScrollbarOffset", "isDefaultDirectionScroll", "styleScrollbarPosition", "Ut", "getTranslateValue", "axisIsDefaultCoordinates", "px", "generateScrollbarDOM", "xyKey", "scrollbarClassName", "scrollbar", "track", "handle", "_track", "generateHorizontalScrollbarStructure", "generateVerticalScrollbarStructure", "appendElements", "_refreshScrollbarsHandleLength", "_refreshScrollbarsHandleOffset", "_refreshScrollbarsScrollCoordinates", "_refreshScrollbarsScrollbarOffset", "_scrollbarsAddRemoveClass", "_horizontal", "_scrollbarStructures", "_clone", "_style", "_vertical", "createScrollbarsSetupEvents", "scrollbarHandlePointerInteraction", "Kt", "_documentElm", "Pt", "kt", "wheelTimeout", "clearWheelTimeout", "scrollSnapScrollTransitionTimeout", "clearScrollSnapScrollTransitionTimeout", "scrollOffsetElementScrollBy", "coordinates", "scrollBy", "behavior", "createInteractiveScrollEvents", "releasePointerCaptureEvents", "clientXYKey", "widthHeightKey", "leftTopKey", "wh<PERSON>ey", "createRelativeHandleMove", "mouseDownScroll", "invertedScale", "deltaMovement", "handleTrackDiff", "scrollDeltaPercent", "scrollDelta", "pointerdownCleanupFns", "pointerDownEvent", "isDragScroll", "pointerCaptureElement", "scrollbarOptions", "dragClickScrollOption", "button", "isPrimary", "pointerType", "continuePointerDown", "instantClickScroll", "shift<PERSON>ey", "getHandleRect", "getTrackRect", "getHandleOffset", "handleRect", "trackRect", "axisScale", "pointerDownOffset", "handleCenter", "relativeTrackPointerOffset", "startOffset", "releasePointerCapture", "pointerUpEvent", "pointerupCleanupFns", "pointerId", "nonAnimatedScroll", "pointerMoveEvent", "withoutSnapScrollOffset", "withSnapScrollOffset", "snapScrollDiff", "setPointerCapture", "animateClickScroll", "stopClickScrollAnimation", "wheelScrollBy", "focusedElement", "wheelEvent", "deltaX", "deltaY", "deltaMode", "createScrollbarsSetup", "mouseInHost", "autoHideIsMove", "autoHideIsLeave", "autoHideIsNever", "prevTheme", "instanceAutoHideSuspendScrollDestroyFn", "instanceAutoHideDelay", "hoverablePointerTypes", "isHoverablePointerType", "requestScrollAnimationFrame", "cancelScrollAnimationFrame", "autoHideInstantInteractionTimeout", "clearAutoHideInstantInteractionTimeout", "autoHideSuspendTimeout", "clearAutoHideSuspendTimeout", "auotHideTimeout", "clearAutoHideTimeout", "manageScrollbarsAutoHideInstantInteraction", "Qt", "_scrollEventElement", "jt", "Nt", "qt", "Bt", "Ft", "manageScrollbarsAutoHide", "removeAutoHide", "delayless", "hide", "manageAutoHideSuspension", "onHostMouseEnter", "_observersUpdateHints", "_structureUpdateHints", "nn", "_overflowEdgeChanged", "sn", "_overflowAmountChanged", "en", "_overflowStyleChanged", "cn", "_scrollCoordinatesChanged", "Ct", "dt", "k", "rn", "_hasOverflow", "themeChanged", "visibilityChanged", "autoHideChanged", "autoHideSuspendChanged", "dragScrollChanged", "clickScrollChanged", "overflowChanged", "trulyAppeared", "updateScrollbars", "updateVisibility", "setScrollbarVisibility", "isVisible", "xVisible", "yVisible", "<PERSON><PERSON><PERSON><PERSON>", "createStructureSetupElements", "defaultInitElements", "defaultPaddingInitialization", "defaultViewportInitialization", "defaultContentInitialization", "targetIsElm", "targetStructureInitialization", "initElements", "paddingInitialization", "viewportInitialization", "contentInitialization", "targetElement", "ownerDocument", "doc<PERSON><PERSON>", "documentElement", "getDocumentWindow", "defaultView", "generalStaticInitializationElement", "createNewDiv", "generateViewportElement", "generateContentElement", "elementHasOverflow", "offsetSize", "scrollSize", "overflowX", "overflowY", "possibleViewportElement", "viewportIsTarget", "possibleContentElement", "viewportIsContent", "viewportElement", "hostElement", "paddingElement", "contentElement", "generatedElements", "elementIsGenerated", "originalNonBodyScrollOffsetElement", "scrollOffsetElement", "scrollEventElement", "evaluatedTargetObj", "_padding", "_originalScrollOffsetElement", "_windowElm", "viewportAttributeClassName", "ln", "targetContents", "contentSlot", "docWnd", "initActiveElm", "unwrap", "prepareWrapUnwrapFocus", "tabIndexStr", "originalViewportTabIndex", "undoInitWrapUndwrapFocus", "destroyActiveElm", "viewportIsGenerated", "destroyFocusElement", "undoDestroyWrapUndwrapFocus", "createTrinsicUpdateSegment", "_observersState", "xt", "$t", "createPaddingUpdateSegment", "updatePaddingCache", "currentPaddingCache", "paddingChanged", "ft", "Ht", "paddingAbsoluteChanged", "contentMutation", "paddingStyleChanged", "paddingRelative", "paddingHorizontal", "paddingVertical", "paddingStyle", "viewportStyle", "_paddingAbsolute", "_paddingStyleChanged", "createOverflowUpdateSegment", "un", "max0", "flowDirectionCanBeNonDefaultMap", "display", "direction", "directionStyle", "flexDirection", "flexDirectionStyle", "endsWith", "writingMode", "writingModeStyle", "flowDirectionStyleArr", "whCacheOptions", "partialXYOptions", "setMeasuringMode", "active", "getMeasuredScrollCoordinates", "flowDirectionStyles", "flowDirectionCanBeNonDefault", "some", "styleName", "styleValue", "originalScrollOffset", "remove<PERSON><PERSON><PERSON><PERSON>nt", "removeScrollBlock", "scrollEventScrollOffset", "isTrusted", "tmp", "getOverflowAmount", "viewportClientSize", "tollerance", "updateSizeFraction", "getCurrentSizeFraction", "updateViewportScrollSizeCache", "getCurrentViewportScrollSizeCache", "updateOverflowAmountCache", "getCurrentOverflowAmountCache", "updateHasOverflowCache", "updateOverflowEdge", "getCurrentOverflowEdgeCache", "updateOverflowStyleCache", "updateFlowDirectionStyles", "currVal", "newValu", "updateMeasuredScrollCoordinates", "getCurrentMeasuredScrollCoordinates", "createViewportOverflowStyleClassName", "prefix", "setViewportOverflowStyle", "viewportOverflowStyle", "createAllOverflowStyleClassNames", "allOverflowStyleClassNames", "zt", "scrollbarsHidingPluginViewportArrangement", "Y", "W", "J", "overflowXVisible", "overflowYVisible", "viewportChanged", "sizeFractionCache", "viewportScrollSizeCache", "overflowAmuntCache", "overflowEdgeCache", "windowInnerSize", "overflowAmountScrollSize", "overflowAmountClientSize", "overflowEdge", "overflowEdgeChanged", "overflowAmountChanged", "viewportScrollSizeChanged", "sizeFractionChanged", "hasOverflowChanged", "removeClipping", "adjustViewportStyle", "overflowStyleChanged", "flowDirectionStylesChanged", "adjustMeasuredScrollCoordinates", "scrollCoordinates", "scrollCoordinatesChanged", "_scrolled", "createStructureSetup", "appendStructureElements", "canceled", "updateSegments", "updateInfo", "adjustScrollOffset", "updateSegment", "createSetups", "isDestroyed", "onUpdated", "cacheAndOptionsInitialized", "structureSetupCreate", "structureSetupUpdate", "structureSetupCanceled", "observersSetupCreate", "observersSetupUpdate", "observersUpdateHints", "scrollbarsSetupCreate", "scrollbarsSetupUpdate", "scrollbarsSetupElements", "updateHintsAreTruthy", "hints", "observerUpdateHints", "_changedOptions", "rawChangedOptions", "rawForce", "At", "hn", "_cloneScrollbar", "baseUpdateInfoObj", "observersHints", "structureHints", "truthyObserversHints", "truthyStructureHints", "an", "initialScroll", "_observersSetupState", "_structureSetupState", "_structureSetupElements", "_scrollbarsSetupElements", "targetInstanceMap", "addInstance", "removeInstance", "getInstance", "OverlayScrollbars", "eventListeners", "nt", "targetIsElement", "instanceTarget", "potentialInstance", "instancePluginModuleInstances", "validateOptions", "newOptionsWithoutUndefined", "pluginValidate", "currentOptions", "addPluginEvent", "removePluginEvents", "triggerPluginEvent", "addInstanceEvent", "removeInstanceEvents", "triggerInstanceEvent", "setupsConstruct", "setupsUpdate", "setupsState", "setupsElements", "setupsCanceled", "Et", "sizeChanged", "directionChanged", "hostMutation", "pure", "base", "on", "off", "gn", "bn", "dn", "directionRTL", "Xt", "Gt", "translateScrollbarStructure", "translateScrollbarsSetupElement", "scrollbarsSetupElement", "Yt", "Wt", "translatedStructure", "scrollbarHorizontal", "scrollbarVertical", "plugins", "isArr", "pluginsToAdd", "valid", "hasElmsFn", "G", "st", "et", "tt", "ot", "scrollbarsSize", "scrollbarsOverlaid", "scrollbarsHiding", "setDefaultInitialization", "setDefaultOptions"], "mappings": ";;;;;;;;;AAiCO,MAAMA,cAA2BA,CACtCC,GACAC;EAEA,OAAMC,GAAEC,GAAaC,GAAEC,GAAMC,GAAEC,KAAwBP;EACvD,IAAIQ,IAAgBL;EACpB,IAAIM;EAEJ,MAAMC,wBAAsDA,CAACC,GAAUC;IACrE,MAAMC,IAAOL;IAEb,MAAMM,IAASH;IACf,MAAMI,IAAUH,MAAUP,KAAUA,EAAOQ,GAAMC,KAAUD,MAASC;IAEpE,IAAIC,KAAWR,GAAqB;MAClCC,IAASM;MACTL,IAAYI;AACd;IAEA,OAAO,EAACL,GAAQO,GAASN;AAAU;EAErC,MAAMO,sBAA2CJ,KAC/CF,sBAAsBT,EAAQO,GAAQC,IAAYG;EAEpD,MAAMK,kBAA2CL,KAAoB,EACnEJ,KACEI,GACFH;EAGF,OAAO,EAACR,IAASe,sBAAsBN,uBAAuBO;AAE9C;;ACjEX,MAAMC,WAEJC,WAAW,sBAEXC,gBAAgB,iBAErBD,OAAOE;;ACJJ,MAAMC,IAAOJ,IAAYC,SAAS;;AAClC,MAAMI,IAAUC,KAAKC;;AACrB,MAAMC,IAAUF,KAAKG;;AACrB,MAAMC,IAAYJ,KAAKK;;AAGvB,MAAMC,IAAUN,KAAKO;;AACrB,MAAMC,IAAWR,KAAKS;;AACtB,MAAMC,IAAMZ,EAAIa;;AAChB,MAAMC,IAAMd,EAAIe;;AAChB,MAAMC,IAAOhB,EAAIiB;;AACjB,MAAMC,IAASlB,EAAImB;;ACX1B,MAAMC,SAAaC,YACTrB,EAAIqB,OAA8B,cACtCrB,EAAIqB,UACJC;;AAEC,MAAMC,IAA8BH,OAAgC;;AACpE,MAAMI,IACXJ,OAAoC;;AAC/B,MAAMK,IAA4BL,OAA8B;;AAChE,MAAMM,IAAUN,OAAwD;;ACTxE,MAAMO,cAAeC,KAA+BA,WAAQN;;AAE5D,MAAMO,SAAUD,KAA0BA,MAAQ;;AAUlD,MAAME,WAAYF,YAAmCA,MAAQ;;AAE7D,MAAMG,WAAYH,YAAmCA,MAAQ;;AAE7D,MAAMI,YAAaJ,YAAoCA,MAAQ;;AAE/D,MAAMK,aAAcL,YAAoDA,MAAQ;;AAEhF,MAAMM,UAAoBN,KAA8BO,MAAMD,QAAQN;;AAEtE,MAAMQ,WAAYR,YAChBA,MAAQ,aAAaM,QAAQN,OAASC,OAAOD;;AAM/C,MAAMS,cAA4CT;EACvD,MAAMU,MAAWV,KAAOA,EAAIU;EAC5B,MAAMC,IAAsBT,SAASQ,MAAWA,KAAU,KAAKA,IAAS,KAAK;EAE7E,OAAOJ,QAAQN,OAAUK,WAAWL,MAAQW,IACxCD,IAAS,KAAKF,SAASR,KACrBU,IAAS,KAAKV,IACd,OACF;AAAK;;AAOJ,MAAMY,gBAA0BZ,OACnCA,KAAOA,EAAIa,gBAAgBC;;AAMxB,MAAMC,gBAAiBf,KAAiCA,aAAe9B;;AAMvE,MAAM8C,YAAahB,KAA6BA,aAAeiB;;ACzCtE,MAAMC,uBAAuBA,MAAMC,YAAYC;;AAExC,MAAMC,gBAAgBA,CAC3BC,GACAC,GACAC,GACAC,GACAC;EAEA,IAAIC,IAAmB;EACvB,MAAMC,IAAYV;EAClB,MAAMW,IAAgBxD,EAAQ,GAAGmD;EACjC,MAAMM,QAASC;IACb,MAAMC,IAAUd;IAChB,MAAMe,IAAcD,IAAUJ;IAC9B,MAAMM,IAAgBD,KAAeJ;IACrC,MAAMM,IAAUJ,IACZ,IACA,KAAK1D,EAAQ,GAAGuD,IAAYC,IAAgBG,KAAWH,KAAiB;IAC5E,MAAMO,KACHb,IAAKD,MACHjB,WAAWqB,KACRA,EAAOS,GAASA,IAAUN,GAAe,GAAG,GAAGA,KAC/CM,KACNb;IACF,MAAMe,IAAqBH,KAAiBC,MAAY;IAExDV,KAAWA,EAAQW,GAAUD,GAASE;IAEtCV,IAAmBU,IAAqB,IAAInD,GAAK,MAAM4C;AAAQ;EAEjEA;EACA,OAAQC;IACN/C,EAAK2C;IACLI,KAAYD,MAAMC;AAAS;AAC5B;;AC5Ba,SAAAO,KACdC,GACAC;EAEA,IAAI/B,YAAY8B;IACd,KAAK,IAAIrF,IAAI,GAAGA,IAAIqF,EAAO7B,QAAQxD;MACjC,IAAIsF,EAASD,EAAOrF,IAAIA,GAAGqF,OAAY;QACrC;;;SAGC,IAAIA;IAETD,KAAKxB,OAAO2B,KAAKF,KAAUG,KAAQF,EAASD,EAAOG,IAAMA,GAAKH;;EAEhE,OAAOA;AACT;;AAQO,MAAMI,UAAUA,CAAUC,GAAyBC,MACxDD,EAAIE,QAAQD,MAAS;;AAQhB,MAAME,SAASA,CAAIC,GAA2BC,MAAmCD,EAAED,OAAOE;;AAO1F,MAAMC,OAAOA,CAAIC,GAAYC,GAAyBC;GACpClD,SAASiD,MAAU3C,YAAY2C,KAClD7C,MAAM+C,UAAUJ,KAAKK,MAAMJ,GAAOC,KAClCD,EAAMD,KAAKE;EACf,OAAOD;AAAK;;AAOP,MAAM7B,OAAiBsB,KAAgCrC,MAAMe,KAAKsB,KAAO;;AAQzE,MAAMY,oBAAwBC;EACnC,IAAInD,QAAQmD;IACV,OAAOA;;EAET,QAAQtD,SAASsD,MAAUhD,YAAYgD,KAASnC,KAAKmC,KAAS,EAACA;AAAM;;AAOhE,MAAMC,eAAgBP,OAA+CA,MAAUA,EAAMzC;;AAOrF,MAAMiD,mBAAqCR,KAAgB7B,KAAK,IAAIsC,IAAIT;;AAQxE,MAAMU,kBAAkBA,CAACjB,GAAoBkB,GAAcC;EAEhE,MAAMC,QAASC,KAAqBA,IAAKA,EAAGV,WAAM7D,GAAWoE,KAAQ,MAAM;EAC3ExB,KAAKM,GAAKoB;GACTD,MAAUnB,EAAclC,SAAS;AAAE;;ACjH/B,MAAMwD,IAAgB;;AACtB,MAAMC,IAAkB;;AACxB,MAAMC,IAAiB;;AACvB,MAAMC,IAAmB;;AACzB,MAAMC,IAAgB;;AACtB,MAAMC,IAAiB;;AACvB,MAAMC,IAAkB;;AACxB,MAAMC,IAAe;;AACrB,MAAMC,IAAe;;AACrB,MAAMC,IAAW;;AACjB,MAAMC,IAAY;;AAClB,MAAMC,IAAa;;AACnB,MAAMC,IAAY;;AAClB,MAAMC,IAAY;;AAElB,MAAMC,wBAAyBC;EACpC,MAAMC,IAAWC,OAAOF,KAAO;EAC/B,OAAOC,IAAWA,EAAS,GAAGE,gBAAgBF,EAASG,MAAM,KAAK;AAAE;;ACH/D,MAAMC,QAAQA,CACnBtC,GACAC,GACAsC,GACAC;EAEA,IAAIxC,KAAKC,GAAG;IACV,IAAIwC,IAAS;IACbnD,KAAKiD,IAAQG;MACX,MAAMC,IAAkD3C,EAAE0C;MAC1D,MAAME,IAAkD3C,EAAEyC;MAC1D,IAAIC,MAAaC;QACfH,IAAS;;AACX;IAEF,OAAOA;AACT;EACA,OAAO;AAAK;;AASP,MAAMI,UAAUA,CAAI7C,GAAoBC,MAC7CqC,MAAsBtC,GAAGC,GAAG,EAAC,KAAK;;AAQ7B,MAAM6C,UAAUA,CAAI9C,GAAoBC,MAC7CqC,MAAsBtC,GAAGC,GAAG,EAAC,KAAK;;AAQ7B,MAAM8C,YAAYA,CAAC/C,GAAUC,MAAaqC,MAAYtC,GAAGC,GAAG,EAAC,KAAK,KAAK,KAAK;;AC1D5E,MAAM+C,OAAOA;;ACmCb,MAAMC,OAAOA,CAClBhC,MACGH,MACqBG,EAAGgC,KAAK,MAAMnC;;AAOjC,MAAMoC,mBAAoBC;EAC/B,IAAIC;EACJ,MAAMC,IAASF,IAAU/G,IAAOF;EAChC,MAAMoH,IAAWH,IAAU7G,IAASN;EACpC,OAAO,EACJwD;IACC8D,EAASF;IAETA,IAAKC,GAAO,MAAM7D,MAAYnC,WAAW8F,KAAWA,MAAYA;AAAQ,KAE1E,MAAMG,EAASF;AAC+C;;AAQ3D,MAAMG,WAAWA,CACtBC,GACA1J;EAEA,OAAM2J,GAAEC,GAAQC,GAAEC,GAASC,GAAEC,GAAQC,GAAEC,KAAiBlK,KAAW,CAAA;EACnE,IAAImK;EACJ,IAAIC;EACJ,IAAIC;EACJ,IAAIC;EACJ,IAAIC,IAAQrB;EAEZ,MAAMsB,IAA2B,SAA3BA,yBAAqCxD;IACzCuD;IACA/H,EAAO2H;IACPG,IAAiBH,IAAeC,SAAgBxH;IAChD2H,IAAQrB;IAGRQ,EAAmBjD,MAAMgE,MAAMzD;;EAGjC,MAAM0D,aACJ7J,KAEAqJ,KAAgBE,IAAgBF,EAAaE,GAAevJ,KAAQA;EAEtE,MAAM8J,QAAQA;IAEZ,IAAIJ,MAAUrB;MACZsB,EAAyBE,WAAWL,MAAqBA;;AAC3D;EAGF,MAAMO,IAAc,SAAdA;IAEJ,MAAM5D,IAAuCxC,KAAKqG;IAClD,MAAMC,IAAevH,WAAWqG,KAAYA,MAAaA;IACzD,MAAMmB,IAAa3H,SAAS0H,MAAiBA,KAAgB;IAE7D,IAAIC,GAAY;MACd,MAAMC,IAAezH,WAAWuG,KAAaA,MAAcA;MAC3D,MAAMmB,IAAa7H,SAAS4H,MAAiBA,KAAgB;MAC7D,MAAME,IAAeJ,IAAe,IAAIxI,IAAOF;MAC/C,MAAM+I,IAAiBL,IAAe,IAAItI,IAASN;MACnD,MAAMkJ,IAAoBV,WAAW1D;MACrC,MAAMqE,IAAcD,KAAqBpE;MACzC,MAAMsE,IAAcd,EAAyBrB,KAAK,GAAGkC;MACrD,IAAIE;MAMJhB;MACA,IAAIP,MAAaM,GAAgB;QAC/BgB;QACAhB,IAAiB;QAEjBiB,IAAYL,GAAa,MAAOZ,SAAiB1H,IAAYkI;AAC/D,aAAO;QAELS,IAAYL,EAAaI,GAAaR;QAEtC,IAAIG,MAAed;UACjBA,IAAe7H,EAAKqI,OAAOK;;AAE/B;MAEAT,IAAQA,MAAMY,EAAeI;MAE7BnB,IAAgBC,IAAkBgB;AACpC;MACEb,EAAyBxD;;;EAG7B4D,EAAYY,IAASb;EAErB,OAAOC;AAA4C;;ACpI9C,MAAMa,iBAAiBA,CAACvI,GAAU0F,MACvC5E,OAAOwC,UAAUiF,eAAeC,KAAKxI,GAAK0F;;AAMrC,MAAMjD,OAAQzC,KAA6BA,IAAMc,OAAO2B,KAAKzC,KAAO;;AA2BpE,MAAMyI,aAAyBA,CACpCC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC;EAEA,MAAMC,IAAsB,EAACN,GAASC,GAASC,GAASC,GAASC,GAASC;EAG1E,YAAYN,MAAW,YAAYzI,OAAOyI,QAAarI,WAAWqI;IAChEA,IAAS,CAAA;;EAGXpG,KAAK2G,IAAU1G;IAEbD,KAAKC,IAAQ,CAACkE,GAAG/D;MACf,MAAMwG,IAAY3G,EAAOG;MAIzB,IAAIgG,MAAWQ;QACb,OAAO;;MAGT,MAAMC,IAAc7I,QAAQ4I;MAG5B,IAAIA,KAAQtI,cAAcsI,IAAO;QAC/B,MAAME,IAAMV,EAAOhG;QACnB,IAAI2G,IAAaD;QAGjB,IAAID,MAAgB7I,QAAQ8I;UAC1BC,IAAQ;eACH,KAAKF,MAAgBvI,cAAcwI;UACxCC,IAAQ,CAAA;;QAIVX,EAAOhG,KAAkB+F,WAAWY,GAAOH;AAC7C;QACER,EAAOhG,KAAkByG,IAAcD,EAAK7D,UAAU6D;;AACxD;AACA;EAIJ,OAAOR;AAAa;;AAGf,MAAMY,4BAA4BA,CAAwBZ,GAAWa,MAC1EjH,KAAKmG,WAAW,IAAIC,KAAS,CAACjF,GAAOf,GAAKwG;EACxC,IAAIzF,WAAU/D;WACLwJ,EAAKxG;SACP,IAAYe,KAAS7C,cAAc6C;IACxCyF,EAAKxG,KAA4B4G,0BAA0B7F;;AAC7D;;AAOG,MAAM+F,gBAAiBxJ,MAAuByC,KAAKzC,GAAKU;;ACpGxD,MAAM+I,YAAYA,CAAChL,GAAaF,GAAamL,MAClDrL,EAAQI,GAAKD,EAAQD,GAAKmL;;ACHrB,MAAMC,oBAAqBC,KAChCjG,kBAAkBrD,QAAQsJ,KAAUA,KAAUA,KAAU,IAAIC,MAAM,MAAMC,QAAQC,KAAUA;;AAQrF,MAAMC,UAAUA,CAACC,GAA6BC,MACnDD,KAAOA,EAAIE,aAAaD;;AAQnB,MAAME,UAAUA,CAACH,GAA6BC,MACnDD,KAAOA,EAAII,aAAaH;;AAOnB,MAAMI,WAAWA,CACtBL,GACAM,GACA9G;EAEAnB,KAAKqH,kBAAkBY,KAAaL;IAClCD,KAAOA,EAAIO,aAAaN,GAAU/E,OAAO1B,KAAS;AAAI;AACtD;;AAQG,MAAMgH,cAAcA,CAACR,GAA6BM;EACvDjI,KAAKqH,kBAAkBY,KAAaL,KAAaD,KAAOA,EAAIS,gBAAgBR;AAAU;;AAGjF,MAAMS,mBAAmBA,CAACV,GAA6BC;EAC5D,MAAMU,IAAajB,kBAAkBK,QAAQC,GAAKC;EAClD,MAAMW,IAAa5E,KAAKqE,UAAUL,GAAKC;EACvC,MAAMY,wBAAwBA,CAACC,GAA4BC;IACzD,MAAMC,IAAgB,IAAIrH,IAAIgH;IAC9BtI,KAAKqH,kBAAkBoB,KAAmBhB;MACxCkB,EAAcD,GAAWjB;AAAM;IAEjC,OAAOzI,KAAK2J,GAAeC,KAAK;AAAI;EAGtC,OAAO;IACLC,GAAUC,KAA4BP,EAAWC,sBAAsBM,GAAc;IACrFC,GAAOC,KAAyBT,EAAWC,sBAAsBQ,GAAW;IAC5EC,GAAOC;MACL,MAAMC,IAAW9B,kBAAkB6B;MACnC,OAAOC,EAASC,QACd,CAACC,GAAS5B,MAAU4B,KAAWf,EAAWgB,SAAS7B,KACnD0B,EAAS/K,SAAS;AACnB;;AAEJ;;AASI,MAAMmL,kBAAkBA,CAC7B5B,GACAC,GACAzG;EAEAkH,iBAAiBV,GAAKC,GAAUiB,EAAQ1H;EAExC,OAAOwC,KAAK6F,cAAc7B,GAAKC,GAAUzG;AAAM;;AAS1C,MAAMqI,eAAeA,CAC1B7B,GACAC,GACAzG;EAEAkH,iBAAiBV,GAAKC,GAAUmB,EAAK5H;EACrC,OAAOwC,KAAK4F,iBAAiB5B,GAAKC,GAAUzG;AAAM;;AAG7C,MAAMsI,qBAAqBA,CAChC9B,GACAC,GACAzG,GACAuI,OACIA,IAAMF,eAAeD,iBAAiB5B,GAAKC,GAAUzG;;AASpD,MAAMwI,eAAeA,CAC1BhC,GACAC,GACAzG,MACYkH,iBAAiBV,GAAKC,GAAUqB,EAAK9H;;ACxHnD,MAAMyI,0BAA2BjC,KAAgCU,iBAAiBV,GAAK;;AAehF,MAAMkC,cAAcA,CAAClC,GAA6BmC;EACvDF,wBAAwBjC,GAAKkB,EAAQiB;AAAU;;AAS1C,MAAMC,WAAWA,CAACpC,GAA6BmC;EACpDF,wBAAwBjC,GAAKoB,EAAKe;EAClC,OAAOnG,KAAKkG,aAAalC,GAAKmC;AAAU;;ACvBnC,MAAME,OAAOA,CAACC,GAAkBtC;EACrC,MAAMuC,IAAUvC,IAAMjJ,UAAUiJ,MAAQA,IAAM9L;EAC9C,OAAOqO,IAAUlL,KAAKkL,EAAQC,iBAAiBF,MAAa;AAAE;;AAQzD,MAAMG,YAAYA,CAACH,GAAkBtC;EAC1C,MAAMuC,IAAUvC,IAAMjJ,UAAUiJ,MAAQA,IAAM9L;EAC9C,OAAOqO,KAAWA,EAAQG,cAAcJ;AAAS;;AAQ5C,MAAMK,KAAKA,CAAC3C,GAAwBsC,MACzCvL,UAAUiJ,MAAQA,EAAI4C,QAAQN;;AAEzB,MAAMO,gBAAiB7C,KAA2B2C,GAAG3C,GAAK;;AAsB1D,MAAM8C,WAAY9C,KACvBA,IAAM3I,KAAK2I,EAAI+C,cAAc;;AAMxB,MAAMC,SAAUhD,KAA8CA,KAAOA,EAAIiD;;AAQzE,MAAMC,UAAUA,CAAClD,GAAwBsC,MAC9CvL,UAAUiJ,MAAQA,EAAIkD,QAAQZ;;AAMzB,MAAMa,oBAAqBC,KAAmB,SAAkBC;;AAQhE,MAAMC,cAAcA,CACzBtD,GACAuD,GACAC;EAEA,MAAMC,IAAyBP,QAAQlD,GAAKuD;EAC5C,MAAMG,IAAyB1D,KAAOyC,UAAUe,GAAsBC;EACtE,MAAME,IACJT,QAAQQ,GAAwBH,OAA0BE;EAE5D,OAAOA,KAA0BC,IAC7BD,MAA2BzD,KACzB0D,MAA2B1D,KAC1B2D,KACCT,QAAQA,QAAQlD,GAAKwD,IAAuBD,OAC1CE,IACN;AAAK;;AC3FJ,MAAMG,iBAAkBC;EAC7BxL,KAAKkB,kBAAkBsK,KAASC;IAC9B,MAAMC,IAAYf,OAAOc;IACzBA,KAAQC,KAAaA,EAAUC,YAAYF;AAAK;AAChD;;AASG,MAAMG,iBAAiBA,CAACH,GAAyBI,MACtDlI,KACE4H,gBACAE,KACEI,KACA7L,KAAKkB,kBAAkB2K,KAAYC;EACjCA,KAASL,EAAKM,YAAYD;AAAM;;AC1BxC,IAAIE;;AAEG,MAAMC,uBAAuBA,MAAMD;;AACnC,MAAME,uBAAwBC;EACnCH,IAAoBG;AAAoB;;ACGnC,MAAMC,YAAaC;EACxB,MAAMC,IAAMzQ,SAAS0Q,cAAc;EACnCvE,SAASsE,GAAK,SAASD;EACvB,OAAOC;AAAG;;AAOL,MAAME,YAAaC;EACxB,MAAMC,IAAaN;EACnB,MAAMO,IAAqBV;EAC3B,MAAMW,IAAcH,EAAKI;EACzBH,EAAWI,YAAYH,IAClBA,EAA2BI,WAAWH,KACvCA;EAEJ,OAAO5M,KAAKyK,SAASiC,KAAc/E,KAAQ4D,eAAe5D;AAAK;;ACbjE,MAAMqF,YAAYA,CAACC,GAAoC7J,MACrD6J,EAAcC,iBAAiB9J,MAAS6J,EAAc7J,MAAgB;;AAExE,MAAM+J,oBAAqB/F;EACzB,MAAMgG,IAAShG,KAAU;EACzB,OAAOiG,SAASD,KAAUA,IAAS;AAAC;;AAGtC,MAAME,sBAAuBnM,KAA2BgM,kBAAkBI,WAAWpM,KAAS;;AAEvF,MAAMqM,iBAAkBrM,KAAkBnF,KAAKK,MAAM8E,IAAQ,OAAS;;AAKtE,MAAMsM,gBAAiBrG,KAAmB,GAAGoG,eAAeL,kBAAkB/F;;AAErE,SAAAsG,UACd/F,GACAgG;EAEAhG,KACEgG,KACA3N,KAAK2N,IAAQ,CAACC,GAA4BzQ;IACxC;MACE,MAAM0Q,IAAWlG,EAAImG;MACrB,MAAM3M,IACJxD,OAAOiQ,MAAa9P,UAAU8P,KAC1B,KACAhQ,SAASgQ,KACPH,cAAcG,KACdA;MAER,IAAIzQ,EAAKqD,QAAQ,UAAU;QACzBqN,EAASE,YAAY5Q,GAAMgE;;QAE3B0M,EAAS1Q,KAAegE;;AAE5B,MAAE,OAAA6M,IAAO;AAAA;AAEf;;SAYgBC,UACdtG,GACAgG,GACAO;EAEA,MAAMC,IAAiBtQ,SAAS8P;EAChC,IAAIS,IAAwCD,IAAiB,KAAK;EAElE,IAAIxG,GAAK;IACP,MAAMsF,IAAgBnR,EAAIuS,iBAAiB1G,GAAKuG,MAAcvG,EAAImG;IAClEM,IAAkBD,IACdnB,UAAUC,GAAeU,KACzB3O,KAAK2O,GAAQvE,QAAO,CAACjG,GAAQ/C;MAC3B+C,EAAO/C,KAAO4M,UAAUC,GAAe7M;MACvC,OAAO+C;AAAM,QACZiL;AACT;EACA,OAAOA;AACT;;AAQO,MAAME,qBAAqBA,CAChC3G,GACA4G,GACAC;EAEA,MAAMC,IAAcF,IAAiB,GAAGA,OAAoB;EAC5D,MAAMG,IAAcF,IAAiB,IAAIA,MAAmB;EAC5D,MAAMG,IAAM,GAAGF,OAAiBC;EAChC,MAAME,IAAQ,GAAGH,SAAmBC;EACpC,MAAMG,IAAS,GAAGJ,UAAoBC;EACtC,MAAMI,IAAO,GAAGL,QAAkBC;EAClC,MAAMvL,IAAS8K,UAAUtG,GAAK,EAACgH,GAAKC,GAAOC,GAAQC;EACnD,OAAO;IACLC,GAAGzB,oBAAoBnK,EAAOwL;IAC9BK,GAAG1B,oBAAoBnK,EAAOyL;IAC9BjO,GAAG2M,oBAAoBnK,EAAO0L;IAC9BI,GAAG3B,oBAAoBnK,EAAO2L;;AAC/B;;AAGI,MAAMI,4BAA4BA,CACvC/N,GACAgO,MAEA,YACEjR,SAASiD,KAAS,IAAIA,EAAMiO,KAAKjO,EAAMkO,OAAO,GAAGF,IAAe,MAAM,OAAOhO;;AC1GjF,MAAMmO,uBAAwB3H,QACzBA,EAAI4H,eAAe5H,EAAI6H,gBAAgB7H,EAAI8H,iBAAiBrR;;AACjE,MAAMsR,IAAc;EAClBC,GAAG;EACHC,GAAG;;;AAGL,MAAMC,4BAA4BA,CAChCC,GACAnI,MAEAA,IACI;EACEgI,GAAIhI,EAAY,GAAGmI;EACnBF,GAAIjI,EAAY,GAAGmI;IAErBJ;;AAKC,MAAMK,gBAAiBC,KAC5BH,0BAA0B,SAASG,KAAalU;;AAM3C,MAAMmU,IAAgBtM,KAAKkM,2BAAwC;;AAQnE,MAAMK,IAAgBvM,KAAKkM,2BAAwC;;AAQnE,MAAMM,IAAgBxM,KAAKkM,2BAAwC;;AAQnE,MAAMO,oBAAqBzI;EAChC,MAAM0I,IAAW9C,WAAWU,UAAUtG,GAAKtF,OAAc;EACzD,MAAMiO,IAAY/C,WAAWU,UAAUtG,GAAKrF,OAAe;EAC3D,OAAO;IACLqN,GAAGU,IAAWjU,EAAUiU;IACxBT,GAAGU,IAAYlU,EAAUkU;;AAC1B;;AAOI,MAAMC,wBAAyB5I,KAA8BA,EAAI4I;;AAMjE,MAAMC,gBAAiB7I,OAC1BA,KAAO2H,qBAAqB3H;;AAKzB,MAAM8I,uBAAwBC,QAChCA,MAASA,EAAKpO,MAAcoO,EAAKrO;;AAQ/B,MAAMsO,kBAAkBA,CAC7BC,GACAC;EAEA,MAAMC,IAAoBL,qBAAqBG;EAC/C,MAAMG,IAAoBN,qBAAqBI;EAC/C,QAAQE,KAAqBD;AAAiB;;AC9EzC,MAAME,sBAAsBA,CACjC5K,GACA6K,GACAC,GACAC;EAEAnR,KAAKqH,kBAAkB4J,KAAcG;IACnChL,KAAUA,EAAO4K,oBAAoBI,GAAWF,GAA2BC;AAAQ;AACnF;;AAUG,MAAME,mBAAmBA,CAC9BjL,GACA6K,GACAC,GACA1W;EACgB,IAAA8W;EAChB,MAAMC,KAAOD,IAAI9W,KAAWA,EAAQgX,MAAQ,OAAAF,IAAK;EACjD,MAAMH,IAAW3W,KAAWA,EAAQiX,KAAa;EACjD,MAAMC,IAAQlX,KAAWA,EAAQmX,KAAU;EAC3C,MAAMC,IAAyC;IAC7CL;IACAJ;;EAGF,OAAOxN,KACLpC,iBACA8F,kBAAkB4J,GAAYY,KAAKT;IACjC,MAAMU,IACJJ,IACKK;MACCf,oBAAoB5K,GAAQgL,GAAWU,GAAeX;MACtDD,KAAYA,EAASa;AAAI,QAE3Bb;IAGN9K,KAAUA,EAAOiL,iBAAiBD,GAAWU,GAAeF;IAC5D,OAAOjO,KAAKqN,qBAAqB5K,GAAQgL,GAAWU,GAAeX;AAAQ;AAE9E;;AAyBI,MAAMa,kBAAmBD,KAAqBA,EAAIC;;AAMlD,MAAMC,iBAAkBF,KAAqBA,EAAIE;;AAMjD,MAAMC,iBAAkBH,KAC5BC,gBAAgBD,MAAuBE,eAAeF;;AC9FlD,MAAMI,kBAAkBA,CAC7BxK,GACAyK;EAEA,OAAMhD,GAAEA,GAACC,GAAEA,KAAMzR,SAASwU,KAAY;IAAEhD,GAAGgD;IAAU/C,GAAG+C;MAAaA,KAAY,CAAA;EACjFxU,SAASwR,OAAOzH,EAAI0K,aAAajD;EACjCxR,SAASyR,OAAO1H,EAAI2K,YAAYjD;AAAE;;AAQ7B,MAAMkD,mBAAoB5K,MAAoC;EACnEyH,GAAGzH,EAAI0K;EACPhD,GAAG1H,EAAI2K;;;AAMF,MAAME,2BAA2BA,OAA0B;EAChEC,GAAQ;IAAErD,GAAG;IAAGC,GAAG;;EACnBqD,GAAM;IAAEtD,GAAG;IAAGC,GAAG;;;;AAWZ,MAAMsD,4BAA4BA,CACvCC,GACAC;EAEA,OAAMC,GAAEL,GAAMM,GAAEL,KAASE;EACzB,OAAMjD,GAAEA,GAACC,GAAEA,KAAMiD;EACjB,MAAMG,eAAeA,CAACC,GAAeC,GAAaC;IAChD,IAAIC,IAAW5W,EAASyW,KAASE;IACjC,IAAIE,IAAS7W,EAAS0W,KAAOC;IAE7B,IAAIC,MAAaC,GAAQ;MACvB,MAAMC,IAAWhX,EAAQ2W;MACzB,MAAMM,IAASjX,EAAQ4W;MAEvBG,IAASC,IAAWC,IAAS,IAAIF;MACjCD,IAAWE,IAAWC,IAAS,IAAIH;AACrC;IAGAA,IAAWA,MAAaC,IAAS,IAAID;IAErC,OAAO,EAACA,IAAW,GAAGC,IAAS;AAAW;EAG5C,OAAOG,GAAQC,KAAQT,aAAaP,EAAOrD,GAAGsD,EAAKtD,GAAGO;EACtD,OAAO+D,GAAQC,KAAQX,aAAaP,EAAOpD,GAAGqD,EAAKrD,GAAGO;EAEtD,OAAO;IACL6C,GAAQ;MACNrD,GAAGoE;MACHnE,GAAGqE;;IAELhB,GAAM;MACJtD,GAAGqE;MACHpE,GAAGsE;;;AAEN;;AAUI,MAAMC,sCAAsCA,EACjDnB,MACAC;EAEA,MAAMmB,UAAUA,CAACZ,GAAeC,MAAgBD,MAAU,KAAKA,KAASC;EAExE,OAAO;IACL9D,GAAGyE,QAAQpB,EAAOrD,GAAGsD,EAAKtD;IAC1BC,GAAGwE,QAAQpB,EAAOpD,GAAGqD,EAAKrD;;AAC3B;;AAQI,MAAMyE,8BAA8BA,EACvCrB,MAAQC,OACVqB;EAEA,MAAMF,UAAUA,CAACZ,GAAeC,GAAac,MAC3C7M,UAAU,GAAG,IAAI8L,IAAQe,MAAYf,IAAQC,MAAQ;EAEvD,OAAO;IACL9D,GAAGyE,QAAQpB,EAAOrD,GAAGsD,EAAKtD,GAAG2E,EAAc3E;IAC3CC,GAAGwE,QAAQpB,EAAOpD,GAAGqD,EAAKrD,GAAG0E,EAAc1E;;AAC5C;;AC1HI,MAAM4E,eAAgBC;EAC3B,IAAIA,KAAYA,EAAwBC;IACrCD,EAAwBC,MAAM;MAAEC,eAAe;;;AAClD;;ACqCF,MAAMC,iBAAiBA,CACrBnU,GACAgR;EAEAlR,KAAKkB,kBAAkBgQ,IAAWhR;AAAS;;AAGtC,MAAMoU,yBACXC;EAEA,MAAMC,IAAS,IAAIC;EAEnB,MAAMC,cAAsCA,CAACvX,GAAM+T;IACjD,IAAI/T,GAAM;MACR,MAAMwX,IAAWH,EAAOI,IAAIzX;MAC5BkX,gBAAgBQ;QACd,IAAIF;UACFA,EAASE,IAAe,WAAW,SAASA;;AAC9C,UACC3D;AACL,WAAO;MACLsD,EAAOM,SAASH;QACdA,EAAS5P;AAAO;MAElByP,EAAOzP;AACT;AAAA;EAGF,MAAMgQ,WAAgCA,CACpCC,GACAC;IAKA,IAAIpX,SAASmX,IAAuB;MAClC,MAAML,IAAWH,EAAOI,IAAII,MAAyB,IAAI1T;MACzDkT,EAAOU,IAAIF,GAAsBL;MAEjCN,gBAAgBQ;QACd9W,WAAW8W,MAAiBF,EAASjL,IAAImL;AAAa,UACrDI;MAEH,OAAOtR,KACL+Q,aACAM,GACAC;AAEJ;IACA,IAAInX,UAAUmX,MAAmBA;MAC/BP;;IAGF,MAAMS,IAAoBhV,KAAK6U;IAC/B,MAAMI,IAAyB;IAC/BpV,KAAKmV,IAAoB/U;MACvB,MAAMiV,IAAiBL,EAAmD5U;MAC1EiV,KAAiBzU,KAAKwU,GAAQL,SAAS3U,GAAKiV;AAAe;IAG7D,OAAO1R,KAAKpC,iBAAiB6T;AAAO;EAGtC,MAAME,eAAwCA,CAACnY,GAAMqE;IACnDxB,KAAKhB,KAAKwV,EAAOI,IAAIzX,MAASoY;MAC5B,IAAI/T,MAASJ,aAAaI;QACvB+T,EAA6DtU,MAAM,GAAGO;;QAEtE+T;;AACH;AACA;EAGJR,SAASR,KAAyB,CAAA;EAElC,OAAO,EAACQ,UAAUL,aAAaY;AAAa;;ACDvC,MAAME,IAA8C,CAAA;;AAGpD,MAAMC,IAA2E,CAAA;;AAOjF,MAAMC,aAAcC;EACzB3V,KAAK2V,IAAcC,KACjB5V,KAAK4V,IAAQ,CAACzR,GAAG/D;IACfoV,EAAcpV,KAAOwV,EAAOxV;AAAI;AAEnC;;AAGI,MAAMyV,gCAAgCA,CAC3CD,GACAE,GACAC,MAMA5V,KAAKyV,GAAQ/D,KAAK1U;EAChB,OAAQ6Y,QAAQC,GAAUC,UAAUC,KAClCP,EACAzY;EACF,OAAOiZ,GAAab,GAAOc,KAAqBN,KAAgB;EAChE,MAAMO,IAAOP,IAAeI,IAAaF;EACzC,IAAIK,GAAM;IACR,MAAMJ,IAAWH,IAEXO,EAQAF,GAAcb,GAAQO,KAEtBQ,EAIAR;IACN,QAASO,KAAqBZ,GAA6BtY,KAAQ+Y;AACrE;AAAA;;AAGG,MAAMK,gCACXC,KAEAf,EAA4Be;;AClKvB,MAAMC,IAAoC;;ACVjD,MAAMC,IAAsB;;AAGrB,MAAMC,IAAuB;;AAC7B,MAAMC,IAAsC,GAAGD;;AAG/C,MAAME,IAA0B,GAAGH;;AAGnC,MAAMI,IAAsB;;AAG5B,MAAMC,IAAwB,GAAGL;;AAGjC,MAAMM,IAAoBN;;AAC1B,MAAMO,IAAsB;;AAG5B,MAAMC,IAAwB,GAAGR;;AACjC,MAAMS,IAAmChV;;AACzC,MAAMiV,IAAmChV;;AACzC,MAAMiV,IAA2B;;AACjC,MAAMC,IAA6B;;AACnC,MAAMC,IAA6B;;AACnC,MAAMC,IAAmC;;AACzC,MAAMC,IAA6B;;AAGnC,MAAMC,IAAuB,GAAGhB;;AAGhC,MAAMiB,KAAuB,GAAGjB;;AAGhC,MAAMkB,KAAwB;;AAC9B,MAAMC,KAA8B,GAAGD;;AACvC,MAAME,KAAgC,GAAGF;;AACzC,MAAMG,KAAsC,GAAGD;;AAC/C,MAAME,KAAoC,GAAGF;;AAC7C,MAAMG,KAAyC,GAAGD;;AAGlD,MAAME,KAA2B;;AAGjC,MAAMC,KAA8B;;AACpC,MAAMC,KAAqB;;AAC3B,MAAMC,KAAwB,GAAGD;;AACjC,MAAME,KAA+B,GAAGF;;AACxC,MAAMG,KAA6B,GAAGH;;AACtC,MAAMI,KAA0B,GAAGJ;;AACnC,MAAMK,KAA2B,GAAGL;;AACpC,MAAMM,KAA4B,GAAGN;;AACrC,MAAMO,KAA+B,GAAGP;;AAExC,MAAMQ,KAAgC,GAAGR;;AACzC,MAAMS,KAA6B,GAAGT;;AACtC,MAAMU,KAA6B,GAAGV;;AACtC,MAAMW,KAAmC,GAAGD;;AAC5C,MAAME,KAA0B,GAAGZ;;AACnC,MAAMa,KAAqC,GAAGT;;AAC9C,MAAMU,KAAsC,GAAGT;;AC1C/C,MAAMU,KAAyB;;AAEzBC,MAAAA,qBAAqC,QAAQ;EACxDD,CAACA,KAAyB;IACxBnD,QACEA,MACA,CACEqD,GACAC,GACAC;MAEA,MAAMC,IAAe;MACrB,MAAMC,IAAkB;MACxB,MAAMC,IAA0BlN,UAC9B,eAAewL,6BAA4DA,mBAAkDC,+BAAmED,mBAAkDC;MAEpP,MAAM0B,IAA8BD,EAAwB;MAC5D,MAAME,IAAgBD,EAA4BE;MAClD,MAAMC,IAAgBH,EAA4BI;MAClD,MAAMC,IAAqBF,KAAAA,YAAAA,IAAAA,EAAeC;MAE1C,IAAIE,IAAYhK,EAAc0J;MAC9B,IAAIO,IAAWD;MACf,IAAIE,IAAU;MACd,IAAIC;MAEJ,MAAMC,QAAQA;QACZlI,gBAAgB2H,GAAeN;QAC/BrH,gBAAgByH,GAAeJ;AAAa;MAE9C,MAAMc,YAAaC;QACjBH,IAAQ;QACR,IAAID,GAAS;UACXF,IAAYC;UACZZ,EAAsBiB,MAAW;AACnC;AAAA;MAEF,MAAMC,WAAYC;QAChBP,IAAWjK,EAAc0J;QACzBQ,KAAWM,MAAgBlX,QAAQ2W,GAAUD;QAE7C,IAAIQ,GAAa;UACfzI,gBAAgByI;UAEhB,IAAIN,MAAYC,GAAO;YACrB1d,EAAK0d;YACLA,IAAQxd,EAAK0d;AACf;AACF;UACEA,UAAUG,MAAgB;;QAG5BJ;AAAO;MAET,MAAMK,IAAa,EACjB9O,eAAeyN,GAAiBK,IAChCrI,iBAAiByI,GAAeL,GAAiBe,WACjDnJ,iBAAiBuI,GAAeH,GAAiBe;MAGnDzQ,SAASsP,GAAiBtB;MAG1BrK,UAAUsM,GAAoB;QAC5B3X,CAACA,IAAWmX;QACZlX,CAACA,IAAYkX;;MAGf5c,EAAKyd;MAEL,OAAO,EAACd,IAAsB5V,KAAK6W,UAAU,SAASH,OAAOK;AAAW;;GApE9B;;ACd3C,MAAMC,kCAAkCA,CAACC,GAAsCC;EACpF,OAAMC,GAAEC,KAA8BF;EACtC,OAAOG,GAAoCC,KAAuCL,EAChF;EAGF,OAAO,EACLI,KACED,EAA0B3L,KAC1B2L,EAA0B1L,GAC5B4L;AACQ;;AAGL,MAAMC,oBAAqBC,KAChCA,EAAiB3a,QAAQ+B,OAAgB;;AAQpC,MAAM6Y,8BAA8BA,CACzCC,GACAF;EAEA,MAAMG,uBAAuBA,CAC3BC,GACAC,GACAC,GACAC;IAQA,MAAMC,IACJJ,MAAiBhZ,IACbC,IACC+Y,EAAaK,QAAQ,GAAGrZ,MAAe;IAC9C,MAAMsZ,IAAsBX,kBAAkBK;IAC9C,MAAMO,IAA+BZ,kBAAkBO;IAGvD,KAAKD,MAAoBE;MACvB,OAAOlZ;;IAIT,IAAIqZ,KAAuBC;MACzB,OAAOvZ;;IAIT,IAAIsZ,GAAqB;MACvB,MAAME,IAA2BP,IAAkBjZ,IAAaC;MAChE,OAAOgZ,KAAmBE,IACtBC,IACAI;AACN;IAEA,MAAMC,IACJF,KAAgCJ,IAAwBnZ,IAAaC;IACvE,OAAOgZ,IACHG,IACAK;AAAW;EAGjB,MAAMC,IAAiB;IACrB7M,GAAGkM,qBAAqBH,EAAiB/L,GAAGiM,EAAYjM,GAAG+L,EAAiB9L,GAAGgM,EAAYhM;IAC3FA,GAAGiM,qBAAqBH,EAAiB9L,GAAGgM,EAAYhM,GAAG8L,EAAiB/L,GAAGiM,EAAYjM;;EAG7F,OAAO;IACL6M;IACAC,GAAiB;MACf9M,GAAG6M,EAAe7M,MAAM3M;MACxB4M,GAAG4M,EAAe5M,MAAM5M;;;AAE3B;;AC7DI,MAAM0Z,KAA6B;;AAE7BC,MAAAA,qBAAyC,QAAQ;EAC5DD,CAACA,KAA6B;IAC5BnG,QAAQA,OAAO;MACbqG,GAAsBA,CACpBC,GACAC,GACAC,GACA3B,GACA4B;QAEA,OAAMC,GAAEC,GAAiBC,GAAEC,KAAcP;QACzC,OAAMQ,GAAEC,GAAuBjC,GAAEC,GAAyBiC,GAAEC,KAA0BpC;QACtF,MAAMqC,KACHP,MACAI,MACAhC,EAA0B3L,KAAK2L,EAA0B1L;QAC5D,OAAO8N,KAAgCxC,gCAAgC8B,GAAc5B;QAKrF,MAAMuC,4BAA4BA;UAChC,MAAMC,kBAAmBC;YACvB,MAAMC,IAAgBtP,UAAU4O,GAAWS;YAC3C,MAAME,IAAiBD,MAAkB9a;YAEzC,OAAO,EAAC8a,GAAeC;AAAwB;UAGjD,OAAOC,GAAgBC,KAAmBL,gBAAgBlb;UAC1D,OAAOwb,GAAgBC,KAAmBP,gBAAgBjb;UAE1D,OAAO;YACL6Z,GAAgB;cACd7M,GAAGqO;cACHpO,GAAGsO;;YAELzB,GAAiB;cACf9M,GAAGsO;cACHrO,GAAGuO;;;AAEN;QAOH,MAAMC,iCAAkCC;UACtC,OAAMC,GAAE7B,KAAoB4B;UAC5B,MAAME,IACJjB,KAA2BI,IAA+B,IAAI;UAEhE,MAAMc,uBAAuBA,CAC3BC,GACAV,GACAW;YAEA,MAAMC,IAAgCF,IAClCF,IACAG;YACJ,MAAME,IACJb,MAAmBT,IAA0BqB,IAAgC;YAC/E,MAAME,IAA8BJ,OAAgBF;YAEpD,OAAO,EAACK,GAAsBC;AAAqC;UAGrE,OAAOC,GAAuBC,KAAgCP,qBAC5DlD,EAA0B3L,GAC1B8M,EAAgB9M,GAChB6N,EAAsB7N;UAExB,OAAOqP,GAAuBC,KAAgCT,qBAC5DlD,EAA0B1L,GAC1B6M,EAAgB7M,GAChB4N,EAAsB5N;UAGxB,OAAO;YACLsP,GAAuB;cACrBvP,GAAGmP;cACHlP,GAAGoP;;YAELG,GAA8B;cAC5BxP,GAAGoP;cACHnP,GAAGqP;;;AAEN;QAUH,MAAMG,wBAAwBA,CAC5Bf,IACEgB,OACFC;UAEA,KAAKpC,GAAmB;YACtB,MAAMqC,IAAgC7Y,WACpC,IACA;cACElE,CAACA,IAAiB;cAClBC,CAACA,IAAkB;cACnBF,CAACA,IAAgB;;YAGrB,OAAMid,GAAEN,GAAqBO,GAAEN,KAC7Bf,+BAA+BC;YACjC,OAAQ1O,GAAG+P,GAAU9P,GAAG+P,KAAaR;YACrC,OAAQxP,GAAGiQ,GAAahQ,GAAGiQ,KAAgBX;YAC3C,OAAMY,GAAEC,KAA0BjD;YAClC,MAAMkD,IAAyCX,IAC3C9c,IACAC;YACJ,MAAMyd,IAAkDZ,IACpDhd,IACAD;YACJ,MAAM8d,IAAwBH,EAAsBC;YACpD,MAAMG,IAAsBJ,EAAsBtd;YAClD,MAAM2d,IAAyBL,EAC7BE;YAEF,MAAMI,IAAuBN,EAAsBzd;YAGnDid,EAAiB3c,KAAY,eAC3Bid,IAAcK,KAAyB;YAEzCX,EAAiBS,MAAwBH,IAAcK;YAGvDX,EAAiB9c,MAAoBmd,IAAcO;YAGnD,IAAIb,GAAiB;cACnBC,EAAiBU,KACfG,KAA0BT,IAAWE,IAAc;cACrDN,EAAiBjd,KACf+d,KAAwBX,IAAWE,IAAc;AACrD;YAEA,OAAOL;AACT;AAAA;QAUF,MAAMe,mBAAmBA,CACvBjC,GACAkC,GACAC;UAEA,IAAI/C,GAAmB;YACrB,OAAMqC,GAAEC,KAA0BjD;YAClC,OAAM0C,GAAEN,GAAqBO,GAAEN,KAC7Bf,+BAA+BC;YACjC,OAAQ1O,GAAG+P,GAAU9P,GAAG+P,KAAaR;YACrC,OAAQxP,GAAGiQ,GAAahQ,GAAGiQ,KAAgBX;YAC3C,OAAMuB,GAAEpB,KAAoBtC;YAC5B,MAAM2D,IAAyDrB,IAC3Djd,IACAC;YACJ,MAAMse,IAAwCZ,EAC5CW;YAEF,MAAME,IAAsCb,EAAsBc;YAClE,MAAMC,IAAyBP,EAAmBrQ,IAAIsQ,EAAatQ;YACnE,MAAM6Q,IAAyBR,EAAmBpQ,IAAIqQ,EAAarQ;YACnE,MAAM6Q,IAAc;cAClB9Q,GACE2P,KAAeF,IACX,GACEE,IAAciB,IAAyBH,QAEzC;cACNxQ,GACEyP,KAAeF,IACX,GACEE,IAAcmB,IAAyBH,QAEzC;;YAGR3S,UAAUmP,GAAW;cACnB,YAAY4D,EAAY9Q;cACxB,YAAY8Q,EAAY7Q;;AAE5B;UAEA,OAAOsN;AAAiB;QAU1B,MAAMwD,uBAAwB5C;UAC5B,IAAIZ,GAAmB;YACrB,MAAMyD,IAA6B7C,KAAyBV;YAC5D,OAAQoC,GAAuBoB,KAAyBrE;YACxD,OAAM2C,GAAEN,KAAiCf,+BACvC8C;YAEF,OAAQvR,GAAG+P,GAAU9P,GAAG+P,KAAaR;YACrC,MAAMiC,IAAiC,CAAA;YACvC,MAAMC,cAAe7d,KACnBjD,KAAKiD,IAAQG;cACXyd,EAAkBzd,KAChBwd,EAAqBxd;AAAuB;YAGlD,IAAI+b;cACF2B,YAAY,EAAC5e,GAAiBN,GAAeG;;YAG/C,IAAIqd;cACF0B,YAAY,EAAC9e,GAAeC,GAAgBH,GAAgBD;;YAG9D,MAAMkf,IAAY9S,UAAU4O,GAAW1c,KAAK0gB;YAC5C,MAAMG,IAAazX,gBACjBsT,GACA3F,GACAG;YAGF3J,UAAUmP,GAAWgE;YAErB,OAAO,EACL;cACEnT,UACEmP,GACA1W,WACE,CAAA,GACA4a,GACAlC,sBACE8B,GACAnE,GACAU;cAIN8D;AAAY,eAEdL;AAEJ;UACA,OAAO,EAACjd;AAAc;QAGxB,OAAO;UACLma;UACAkC;UACAW;UACA7B;;AACD;;;GA7Q6C;;AC/B/C,MAAMoC,KAA8B;;AAE9BC,MAAAA,qBAAoC,QAAQ;EACvDD,CAACA,KAA8B;IAC7BjL,QACEA,MACA,CACEmL,GACAC,GACAC,GACAC;MASA,IAAIC,IAAU;MACd,IAAIC,IAAqB9d;MACzB,MAAM+d,IAAiB;MACvB,MAAMC,IAAgB;MACtB,OAAOC,GAA0BC,KAC/Bhe,iBAAiB6d;MACnB,MAAMI,IAAmB7lB,KAAKS,KAAK2kB;MACnC,MAAMU,IAA6BT,IAAeQ;MAClD,MAAME,IAAiCD,IAA6B;MACpE,MAAM1iB,SAAUgQ,KAAc,KAAK,IAAIA,MAAM,IAAIA;MACjD,MAAM4S,yBAAyBA,CAAChjB,GAAcC,MAC5CF,cAAcC,GAAMC,GAAIyiB,GAAeP,GAAoB/hB;MAC7D,MAAM6iB,uBAAuBA,CAACC,GAAoBC,MAChDpjB,cACEmjB,GACAd,IAAeU,GACfL,IAAiBU,IACjB,CAACriB,GAAUqE,GAAGie;QACZjB,EAAmBrhB;QAEnB,IAAIsiB;UACFZ,IAAqBQ,uBAAuBliB,GAAUshB;;AACxD;MAGN,MAAMiB,IAAqBtjB,cACzB,GACA+iB,GACAJ,IACA,CAACY,GAAwBne,GAAGoe;QAC1BpB,EAAmBmB;QAEnB,IAAIC,GAAyB;UAC3BjB,EAAuBC;UAEvB,KAAKA,GAAS;YACZ,MAAMiB,IAA0BpB,IAAekB;YAC/C,MAAMG,IACJzmB,KAAKS,KAAK+lB,IAA0BT,OACpCF;YAEFY,KACEd,GAAyB;cACvB,MAAMe,IACJF,IAA0BV;cAC5B,MAAMa,IACJ3mB,KAAKS,KAAKimB,OAAmCb;cAE/CL,IAAqBmB,IACjBV,qBACEK,GACAtmB,KAAKO,IAAImmB,KAAiCrB,KAE5CW,uBAAuBM,GAAwBlB;AAAa;AAEtE;AACF;AAAA,UAEFhiB;MAGF,OAAQwjB;QACNrB,IAAU;QAEV,IAAIqB;UACFP;;QAGFT;QACAJ;AAAoB;AACrB;;GAvFwC;;AC0EjD,MAAMqB,eAAgB1hB,KACpB2hB,KAAKC,UAAU5hB,IAAO,CAACgD,GAAG6e;EACxB,IAAIjlB,WAAWilB;IACb,MAAM;;EAER,OAAOA;AAAG;;AAGd,MAAMC,gBAAgBA,CAAIvlB,GAAUwlB,MAClCxlB,IACI,GAAGwlB,IACA3b,MAAM,KACN6B,QAAO,CAAC1O,GAAG0I,MAAU1I,KAAKuL,eAAevL,GAAG0I,KAAQ1I,EAAE0I,UAAQhG,IAAYM,UAC7EN;;AAmIC,MAAM+lB,KAAkC;EAC7CC,iBAAiB;EACjBjG,8BAA8B;EAC9B1iB,QAAQ;IACN4oB,eAAe,EAAC,EAAC,OAAO;IACxBpf,UAAU,EAAC,GAAG;IACdqf,YAAY;IACZC,gBAAgB;;EAElBC,UAAU;IACRpU,GAAG;IACHC,GAAG;;EAELoU,YAAY;IACVC,OAAO;IACPC,YAAY;IACZC,UAAU;IACVC,eAAe;IACfC,iBAAiB;IACjBC,YAAY;IACZC,aAAa;IACbC,UAAU,EAAC,SAAS,SAAS;;;;AAI1B,MAAMC,iBAAiBA,CAAIC,GAAgBC;EAChD,MAAMC,IAAuB,CAAA;EAC7B,MAAMC,IAAc7jB,OAAON,KAAKikB,IAAajkB,KAAKgkB;EAIlDnkB,KAAKskB,IAAcC;IACjB,MAAMC,IAAkBL,EAAYI;IACpC,MAAME,IAAiBL,EAAWG;IAElC,IAAIrmB,SAASsmB,MAAoBtmB,SAASumB,IAAiB;MACzDte,WAAYke,EAAKE,KAAa,CAAA,GAAYL,eAAeM,GAAiBC;MAE1E,IAAIvd,cAAcmd,EAAKE;eACdF,EAAKE;;AAEhB,WAAO,IAAIte,eAAeme,GAAYG,MAAcE,MAAmBD,GAAiB;MACtF,IAAIE,IAAS;MAEb,IAAI1mB,QAAQwmB,MAAoBxmB,QAAQymB;QACtC;UACE,IAAI5B,aAAa2B,OAAqB3B,aAAa4B;YACjDC,IAAS;;AAEb,UAAE,OAAA1W,IAAO;;MAGX,IAAI0W;QAEFL,EAAKE,KAAaE;;AAEtB;AAAA;EAGF,OAAOJ;AAAI;;AAGN,MAAMM,oBACXA,CACEnqB,GACAoqB,GACAxpB,MAED8nB,KAAS,EACRD,cAAczoB,GAAS0oB,IACvB9nB,KAAS6nB,cAAc2B,GAAgB1B,YAAU9lB;;ACrSrD,IAAIynB;;AAEG,MAAMC,WAAWA,MAAMD;;AACvB,MAAME,WAAYC;EACvBH,KAAQG;AAAQ;;AC2ClB,IAAIC;;AAEJ,MAAMC,oBAAoBA;EACxB,MAAMC,yBAAyBA,CAC7BC,GACAC,GACAtgB;IAGA6G,eAAe/P,SAASypB,MAAMF;IAC9BxZ,eAAe/P,SAASypB,MAAMF;IAE9B,MAAMG,IAAQrV,EAAckV;IAC5B,MAAMI,IAAQvV,EAAcmV;IAC5B,MAAMK,IAAQrV,kBAAkBiV;IAEhCtgB,KAASwG,eAAe6Z;IAExB,OAAO;MACLhW,GAAGoW,EAAM5V,IAAI2V,EAAM3V,IAAI6V,EAAM7V;MAC7BP,GAAGmW,EAAM7V,IAAI4V,EAAM5V,IAAI8V,EAAM9V;;AAC9B;EAGH,MAAM+V,4BAA6BC;IACjC,IAAIxiB,IAAS;IACb,MAAMyiB,IAAc7b,SAAS4b,GAAS/O;IACtC;MACEzT,IACE8K,UAAU0X,GAAS,uBAAyC,UAC5D1X,UAAU0X,GAAS,WAAW,2BAA2B;AAC7D,MAAE,OAAA3X,IAAO;IACT4X;IACA,OAAOziB;AAAM;EAIf,MAAM0iB,IAAW,IAAIlP,oIAAuJA,+CAAkEC,qCAAuEA,yBAA2DA;EAChX,MAAMkP,IAAStZ,UACb,eAAemK,wBAA2CkP;EAE5D,MAAME,IAASD,EAAO;EACtB,MAAME,IAAcD,EAAOhM;EAC3B,MAAMkM,IAAWF,EAAOlM;EACxB,MAAMgL,IAAQC;EAEd,IAAID;IACFoB,EAASpB,QAAQA;;EAGnB,OAAO9P,GAAQ,EAAIO,KAAgBhB;EACnC,OAAO4R,GAAgCC,KAA+B5rB,YACpE;IACEI,GAAewqB,uBAAuBY,GAAQC;IAC9CnrB,GAAQ2I;KAEVG,KAAKwhB,wBAAwBY,GAAQC,GAAa;EAEpD,OAAOI,KAAwBD;EAC/B,MAAME,IAAyBX,0BAA0BK;EACzD,MAAMO,IAA2B;IAC/BlX,GAAGgX,EAAqBhX,MAAM;IAC9BC,GAAG+W,EAAqB/W,MAAM;;EAEhC,MAAMkX,IAA8C;IAClDC,UAAU;MACRC,MAAM;MACNC,UAAUL;MACVM,UAAWvgB,KAAWigB,KAA0B7b,cAAcpE,MAAWA;MACzEwgB,SAAS;;IAEXnD,YAAY;MACVoD,MAAM;;IAERC,QAAQ;MACNR,0BAA0B;MAC1BhB,MAAM;;;EAGV,MAAMyB,IAAuB5gB,WAAW,CAAE,GAAEgd;EAC5C,MAAM6D,IAAoBrjB,KACxBwC,YACA,CAAa,GACb4gB;EAEF,MAAME,IAA2BtjB,KAC/BwC,YACA,CAAoB,GACpBogB;EAGF,MAAM1L,IAAW;IACfoC,GAAuBmJ;IACvBrL,GAA2BuL;IAC3BvJ,GAAyBsJ;IACzBa,KAAmB1pB;IACnB2pB,GAAoBxjB,KAAKoR,GAAU;IACnCqS,GAA2BH;IAC3BI,IAA4BC,KAC1BnhB,WAAWogB,GAA6Be,MACxCL;IACFM,IAAoBP;IACpBQ,IAAqBC,KACnBthB,WAAW4gB,GAAsBU,MAAsBT;IACzDU,IAA8BvhB,WAAW,CAAE,GAAEogB;IAC7CoB,IAAuBxhB,WAAW,CAAE,GAAE4gB;;EAGxC5e,YAAY4d,GAAQ;EACpBxa,eAAewa;EAGf1U,iBAAiBvV,GAAK,WAAU;IAC9BwZ,EAAa,KAAK;AAAG;EAGvB,IACEvX,WAAWjC,EAAI8rB,gBACdvB,OACCC,EAAyBlX,MAAMkX,EAAyBjX,IAC1D;IACA,MAAMwY,kBAAmBC;MACvB,MAAMC,IAAQjsB,EAAI8rB,WAAW,gBAAgB9rB,EAAIksB;MACjD3W,iBACE0W,GACA,WACA;QACED;QACAD,gBAAgBC;AAAO,UAEzB;QACEnW,GAAO;;AAEV;IAEHkW,iBAAgB;MACd,OAAOI,GAA4BC,KACjChC;MAEF/f,WAAW0U,EAAIoC,GAAuBgL;MACtC3S,EAAa,KAAK,EAAC4S;AAA4B;AAEnD;EAEA,OAAOrN;AAAG;;AAGL,MAAMsN,iBAAiBA;EAC5B,KAAKlD;IACHA,KAAsBC;;EAExB,OAAOD;AAAmB;;ACvG5B,MAAMmD,2BAA2BA,CAC/BhiB,GACAlG,GACAmoB;EAEA,IAAIC,IAAY;EAChB,MAAMzW,IAAMwW,IAAqB,IAAIE,UAAiC;EACtE,MAAMC,UAAUA;IACdF,IAAY;AAAI;EAElB,MAAMG,iBAAmDC;IACvD,IAAI7W,KAAOwW,GAAoB;MAC7B,MAAMM,IAAeN,EAAmBxW,KAAKtR;QAC3C,OAAO0J,GAAUgH,KAAc1Q,KAAQ;QACvC,MAAMimB,IAAWvV,KAAchH,KAAYye,KAAe1e,MAAMC,GAAU7D,KAAU;QACpF,OAAO,EAACogB,GAAUvV;AAAoB;MAGxCjR,KAAK2oB,IAAepoB,KAClBP,KAAKO,EAAK,KAAKoH;QACb,MAAMsJ,IAAa1Q,EAAK;QACxB,MAAMqoB,IAAU/W,EAAI+C,IAAIjN,MAAQ;QAChC,MAAMkhB,IAAgBziB,EAAO0iB,SAASnhB;QAEtC,IAAIkhB,KAAiB5X,GAAY;UAC/B,MAAM8X,IAAiB1X,iBAAiB1J,GAAKsJ,IAAasE;YACxD,IAAI+S,GAAW;cACbS;cACAlX,EAAImX,OAAOrhB;AACb;cACEzH,EAASqV;;AACX;UAEF1D,EAAIqD,IAAIvN,GAAK/G,KAAKgoB,GAASG;AAC7B,eAAO;UACLxnB,gBAAgBqnB;UAChB/W,EAAImX,OAAOrhB;AACb;AAAA;AAGN;AAAA;EAGF8gB;EAEA,OAAO,EAACD,SAASC;AAAe;;AAW3B,MAAMQ,oBAAoBA,CAC/B7iB,GACA8iB,GACAhpB,GACA1F;EAEA,IAAI2uB,IAAc;EAClB,OAAMC,IACJC,GAAWC,IACXC,GAAwBC,IACxBC,GAAmBC,IACnBC,GAAqBC,IACrBC,GAAmBC,IACnBC,KACGvvB,KAAoE,CAAA;EACzE,MAAMwvB,IAA8B/lB,UAClC,MAAMklB,KAAgBjpB,EAAwC,QAC9D;IAAEkE,GAAU;IAAIE,GAAW;;EAE7B,OAAO2lB,GAA2BC,KAAoC9B,yBACpEhiB,GACA4jB,GACAP;EAIF,MAAMU,IAAkBd,KAAe;EACvC,MAAMe,IAA+Bb,KAA4B;EACjE,MAAMc,IAAqB5pB,OAAO0pB,GAAiBC;EACnD,MAAME,mBAAmBA,CACvBC,GACAC;IAEA,KAAKppB,aAAaopB,IAAY;MAC5B,MAAMC,IAAqBZ,KAAuBnmB;MAClD,MAAMgnB,IAAsBX,KAAwBrmB;MACpD,MAAMinB,IAA4B;MAClC,MAAMC,IAA+B;MACrC,IAAIC,IAAsD;MAC1D,IAAIC,IAAkD;MAGtD9qB,KAAKwqB,IAAYO;QACf,OAAMC,eACJA,GACA5kB,QAAQ6kB,GAAcC,MACtBA,GAAIC,UACJA,GAAQC,YACRA,GAAUC,cACVA,KACEN;QACJ,MAAMO,IAAmBJ,MAAS;QAClC,MAAMK,IAAkBL,MAAS;QACjC,MAAMM,IAAyBplB,MAAW6kB;QAC1C,MAAMQ,IAAeH,KAAoBN;QACzC,MAAM7vB,IACJswB,KAAgB/jB,QAAQujB,GAA+BD,KAAiB;QAE1E,MAAMU,IAAiB7tB,SAAS1C,KAAYA,IAAW;QACvD,MAAMwwB,IAAmBF,KAAgBN,MAAaO;QACtD,MAAME,IACJvrB,QAAQ+pB,GAA8BY,MAAkBW;QAG1D,IAAIzC,MAAsBqC,MAAoBC,IAAyB;UACrE,MAAMK,IAAqBP,KAAoBK;UAC/C,MAAMG,IACJD,KACAlC,KACArf,GAAG2gB,GAAgBtB;UACrB,MAAMoC,IAAgBD,KACjBrB,EAAmBQ,GAAgBD,GAAeG,GAAUO,MAC5DJ,KAAoBO;UACzB,MAAMG,IACJD,MAAkBrB,EAAoBK,KAAYe,GAAgB1lB,GAAQ5L;UAE5EwF,KAAKorB,IAAa3f,KAAS7K,KAAK+pB,GAAmBlf;UACnDzL,KAAKqrB,IAAe5f,KAAS7K,KAAK+pB,GAAmBlf;UAErDqf,IAAiBA,KAAkBkB;AAErC;QAEA,KACG9C,KACDsC,KACAG,MACClB,EAAmBQ,GAAgBD,GAAgBG,GAAUO,IAC9D;UACA9qB,KAAKgqB,GAAoBI;UACzBH,IAAqBA,KAAsBe;AAC7C;AAAA;MAIF1B,GAAkCjgB,KAChC5I,iBAAiBspB,GAAmBvhB,QAAe,CAAC9I,GAAKmL;QACvD7K,KAAKN,GAAK0J,KAAKC,GAAUwB;QACzB,OAAOnB,GAAGmB,GAAMxB,KAAYrJ,KAAKN,GAAKmL,KAAQnL;AAAG,UAChD;MAGL,IAAI4oB,GAAmB;SACpBqB,KAAeO,KAAmB5qB,EAAwC;QAC3E,OAAO,EAAC;AAGV;MAEA,KAAKkB,aAAawpB,MAAuBC,GAAoB;QAC3D,MAAMrpB,IAAO,EACXH,iBAAiBupB,IACjBC;SAEDN,KAAgBrqB,EAAuCe,MAAM,GAAGO;QAEjE,OAAOA;AACT;AACF;AAAA;EAEF,MAAMyqB,IAAqC,IAAI5uB,EAC7CsG,KAAK2mB,kBAAkB;EAGzB,OAAO,EACL;IACE2B,EAAiBC,QAAQ9lB,GAAQ;MAC/Bkd,YAAY;MACZ6I,mBAAmB;MACnBC,iBAAiB/B;MACjBgC,SAASnD;MACToD,WAAWpD;MACXqD,eAAerD;;IAEjBC,IAAc;IAEd,OAAO;MACL,IAAIA,GAAa;QACfc;QACAgC,EAAiBO;QACjBrD,IAAc;AAChB;AAAA;AACD,KAEH;IACE,IAAIA,GAAa;MACfa,EAA4BhkB;MAC5B,OAAOskB,iBAAiB,MAAM2B,EAAiBQ;AACjD;AAAA;AAEH;;ACnQI,MAAMC,qBAAqBA,CAChCtmB,GACAkT,GACA9e;EAEA,OAAQmyB,IAASpT,KAAwB/e,KAAW,CAAA;EACpD,MAAMoyB,IACJrW,8BAAyD4C;EAC3D,OAAO0T,KAAwCtyB,YAAqC;IAClFI,GAAe;IACfI,GAAqB;;EAGvB,OAAO;IACL,MAAM2f,IAA6B;IACnC,MAAMoS,IAAetgB,UACnB,eAAeoL,mBAAsCE;IAEvD,MAAMiV,IAAeD,EAAa;IAClC,MAAMzT,IAAkB0T,EAAahT;IACrC,MAAMiT,6BAA8BC;MAClC,MAAMC,IAAuBD,aAA8BE;MAE3D,IAAIC,IAAO;MACX,IAAI7S,IAAS;MAGb,IAAI2S,GAAsB;QACxB,OAAOG,GAAgB,EAAIxc,KAAmBgc,EAC5CI,EAAmBK;QAErB,MAAM9c,IAAgBC,qBAAqB4c;QAC3C9S,IAAS5J,gBAAgB0c,GAAkBxc;QAC3Cuc,KAAQ7S,MAAW/J;AACrB;QAGE+J,IAAS0S,MAAuB;;MAGlC,KAAKG;QACH9T,EAAsB;UACpBiU,IAAc;UACdZ,IAASpS;;;AAEb;IAGF,IAAIhd,GAA2B;MAC7B,MAAMiwB,IAAyB,IAAIjwB,GAA2BqrB,KAC5DoE,2BAA2BpE,EAAQ6E;MAErCD,EAAuBtB,QAAQ7S;MAC/BzY,KAAK8Z,IAAY;QACf8S,EAAuBhB;AAAY;AAEtC,WAAM,IAAII,GAAoB;MAC7B,OAAOc,GAAsBC,KAAoBf,EAC/CvT,GACA2T,4BACAzT;MAEF3Y,KACE8Z,GACAja,OACE,EACEsJ,SAASgjB,GAAclV,KACvBxG,iBAAiB0b,GAAc,kBAAkBW,MAEnDC;AAGN;MACE,OAAOjqB;;IAGT,OAAOC,KAAKpC,iBAAiBX,KAAK8Z,GAAY9O,eAAexF,GAAQ2mB;AAAe;AACrF;;AC7FI,MAAMa,wBAAwBA,CACnCxnB,GACAynB;EAEA,IAAIC;EACJ,MAAMC,oBAAqBC,KACxBA,EAA6Bpe,MAAM,KACnCoe,EAA4CC,kBAC5CD,EAA4CE,oBAAoB;EACnE,MAAMC,IAAkB/hB,UAAU8L;EAClC,OAAOkW,KAA8B7zB,YAAY;IAC/CI,GAAe;;EAEjB,MAAM0zB,kCAAkCA,CACtCC,GACA/D;IAEA,IAAI+D,GAAa;MACf,MAAMC,IAAkBH,EAA2BL,kBAAkBO;MACrE,OAASE,EAAAA,KAA0BD;MACnC,OACEC,MACCjE,KACDsD,EAAyBU,MAAoB,EAACA;AAElD;AAAA;EAEF,MAAME,+BAA+BA,CACnClE,GACA3B,MACGyF,gCAAgCzF,EAAQ6E,OAAOlD;EAEpD,OAAO,EACL;IACE,MAAM7P,IAA6B;IAEnC,IAAIpd,GAAiC;MACnCwwB,IAA+B,IAAIxwB,EACjCqG,KAAK8qB,8BAA8B,QACnC;QAAEC,MAAMtoB;;MAEV0nB,EAA6B5B,QAAQiC;MACrCvtB,KAAK8Z,IAAY;QACfoT,EAA8BtB;AAAY;AAE9C,WAAO;MACL,MAAMmC,gBAAgBA;QACpB,MAAMC,IAAU3e,EAAcke;QAC9BE,gCAAgCO;AAAQ;MAE1ChuB,KAAK8Z,GAAYgS,mBAAmByB,GAAiBQ,cAApCjC;MACjBiC;AACF;IAEA,OAAOhrB,KAAKpC,iBAAiBX,KAAK8Z,GAAY9O,eAAexF,GAAQ+nB;AAAkB,KAEzF,MACEL,KACAW,6BAA6B,MAAMX,EAA6BrB;AACnE;;ACpBI,MAAMoC,uBAAuBA,CAClCvS,GACAC,GACAuS,GACAC;EAEA,IAAIC;EACJ,IAAIC;EACJ,IAAIC;EACJ,IAAIC;EACJ,IAAIte;EACJ,IAAIue;EACJ,MAAMC,IAAe,IAAIrY;EAIzB,MAAMsY,IAAmB,IAAIpY;EAC7B,MAAMqY,IAAyB,EAAC,MAAM,SAAS,SAAS,QAAQ,QAAQ,QAAQ;EAChF,OAAMC,IACJC,GAAOC,IACPC,GAAK/S,GACLC,GAAS+S,IACTC,GAAoBC,IACpBC,GAAQrT,GACRC,GAAiBqT,IACjBC,GAAOC,IACPC,GAAiBC,IACjBC,GAAuBC,IACvBC,KACEjU;EAEJ,MAAMkU,oBAAqB7oB,KAA8BsG,UAAUtG,GAAK,iBAAiB;EAEzF,MAAM8oB,IAA6B;IACjCC,IAAkB;IAClB5R,GAAiB0R,kBAAkBf;;EAErC,MAAM5U,IAAMsN;EACZ,MAAMwI,IAAyBpa,8BAC7B4F;EAGF,OAAOyU,KAA0Br2B,YAC/B;IACEM,GAAQ0I;IACR5I,GAAe;MAAEgV,GAAG;MAAGC,GAAG;;MAE5B;IACE,MAAM8Q,IACJiQ,KACAA,EAAuBtU,EACrBC,GACAC,GACAkU,GACA5V,GACAiU,GACApO;IAEJ,MAAMmQ,IAAuBZ,KAAWtT;IACxC,MAAMmU,KACHD,KAAwBlnB,aAAagmB,GAAO3Y,GAAmBF;IAClE,MAAMia,KAAcpU,KAAqBwT,EAAkB9Y;IAC3D,MAAM2Z,IAAeD,KAAcxe,iBAAiBsd;IACpD,MAAMoB,IAA8BD,KAAgBT;IAEpD,MAAMW,IAAkBb,EAAwB/Y,GAA4BwZ;IAC5E,MAAMK,IAAsBJ,KAAcrQ,KAAwBA,IAAuB;IACzF,MAAM0Q,IAAiBjhB,EAAc0M;IACrC,MAAMwU,IAAajhB,kBAAkByM;IAErCsU,KAAuBA;IAEvBhf,gBAAgB0d,GAAsBmB;IACtCC,KAA+BA;IAC/BH,KAAcI;IAEd,OAAO;MACLvhB,GAAGyhB,EAAezhB,IAAI0hB,EAAW1hB;MACjCC,GAAGwhB,EAAexhB,IAAIyhB,EAAWzhB;;AAClC;EAIL,MAAM0hB,IAA8BrtB,SAAS8qB,GAAoB;IAC/D3qB,GAAUA,MAAM4qB;IAChB1qB,GAAWA,MAAM2qB;IACjBvqB,CAAAA,CAAa6sB,GAAMl2B;MACjB,OAAOm2B,KAAWD;MAClB,OAAOE,KAAWp2B;MAClB,OAAO,EACLoF,OAAON,KAAKqxB,IAAUrxB,KAAKsxB,IAAUroB,QAAO,CAAC1L,GAAK0C;QAChD1C,EAAI0C,KAAOoxB,EAAQpxB,MAAgCqxB,EAAQrxB;QAC3D,OAAO1C;AAAG,UACT,CAAA;AAEP;;EAGF,MAAMg0B,eAAgBC;IACpB,MAAMC,IAAoBpB,kBAAkBf;IAC5CtpB,WAAWwrB,GAAa;MAAEE,IAAmBzC,MAAuBwC;;IACpEzrB,WAAWsqB,GAAO;MAAE3R,GAAiB8S;;IACrCxC,IAAqBwC;AAAiB;EAGxC,MAAME,mBAAmBA,CACvBC,GACAxH;IAEA,OAAOgE,GAAiBC,KAA0BuD;IAClD,MAAMJ,IAAyC;MAC7CK,IAAyBxD;;IAG3BroB,WAAWsqB,GAAO;MAAEC,IAAkBnC;;KACrChE,KAAewE,EAAmB4C;IAEnC,OAAOA;AAAW;EAGpB,MAAMhD,gBAAgBA,EAAGpB,OAAcZ;IACrC,MAAMsF,IAAsB1E,MAAiBZ;IAC7C,MAAMuF,KAIHD,KAAuBpX,EAAIkC,IACxBuU,IACAvC;IAEN,MAAM4C,IAAyC;MAC7CpE,IAAcA,KAAgBZ;MAC9BA;;IAGF+E,aAAaC;IAEbO,EAASP;AAAY;EAGvB,MAAMQ,oBAAoBA,CACxBC,GACA7H;IAEA,OAAS8H,EAAAA,KAAoBzB;IAC7B,MAAMe,IAAyC;MAC7CU;;IAGFX,aAAaC;IAGb,MAAMO,IAAWE,IAA6BrD,IAAqBuC;IAEnEe,MAAqB9H,KAAe2H,EAASP;IAE7C,OAAOA;AAAW;EAGpB,MAAMW,iBAAiBA,CACrB1H,GACAC,GACAN;IAEA,MAAMoH,IAAyC;MAC7CY,IAAe1H;;IAGjB6G,aAAaC;IAEb,IAAI9G,MAAuBN;MACzB+G,EAA4BK;;IAQ9B,OAAOA;AAAW;EAGpB,OAAOa,GAA0BC,KAAyB1C,IACtDnC,sBAAsB+B,GAAOmC,oBAC7B;EAEJ,MAAMY,KACH/V,KACD+P,mBAAmBiD,GAAOhB,eAAe;IACvChC,IAAS;;EAGb,OAAOgG,GAA+BC,KAA8B3J,kBAClE0G,GACA,OACA2C,gBACA;IACE/I,IAA0BgG;IAC1BlG,IAAakG;;EAIjB,MAAMsD,IACJlW,KACApf,KACA,IAAIA,GAA2BqrB;IAC7B,MAAMhY,IAAkBgY,EAAQA,EAAQxqB,SAAS,GAAGkvB;IACpDqB,cAAc;MACZpB,IAAc;MACdZ,IAAShc,gBAAgBC,GAAiBC;;IAE5CA,IAAkBD;AAAe;EAErC,MAAMkiB,IAA0B7uB,UAC9B;IACE,OAASouB,EAAAA,KAAoBzB;IAC7B7B,EAAmB;MAAEsD;;AAAmB,MAE1C;IACEjuB,GAAU;IACVI,GAAU;;EAId,OAAO,EACL;IAGEquB,KAAkCA,EAA+B3G,QAAQyD;IACzE,MAAMoD,IAAsBL,KAAyBA;IACrD,MAAMM,IAAyBR,KAA4BA;IAC3D,MAAMS,IAA8BN;IACpC,MAAMO,IAAuBrY,EAAIsM,GAAoBgM;MACnD,IAAIA;QACF7B,EAA4B;UAAE6B;;;QAE9BL;;AACF;IAGF,OAAO;MACLD,KAAkCA,EAA+BrG;MACjEuG,KAAuBA;MACvBC,KAA0BA;MAC1B7D,KAAkCA;MAClC8D;MACAC;AAAsB;AACvB,KAEH,EAAGE,OAAcC,OAAcC;IAC7B,MAAM3B,IAAyC,CAAA;IAE/C,OAAOpO,KAAkB6P,EAAa;IACtC,OAAO9P,GAAYiQ,KAAqBH,EAAa;IACrD,OAAO/P,GAAemQ,KAAwBJ,EAAa;IAC3D,OAAOK,GAAeC,KAAmBN,EAAa;IACtD,MAAMO,IAAiCH,KAAwBD;IAC/D,MAAM9G,IAAc4G,KAAgBC;IACpC,MAAMM,4BAA6B7I,KACjChtB,WAAWwlB,MAAmBA,EAAewH;IAE/C,IAAI4I,GAAgC;MAClCzE,KAAiCA;MACjCC,KAAkCA;MAElC,OAAO0E,GAAWp5B,KAAUwuB,kBAC1B8G,KAAYlT,GACZ,MACAsV,mBACA;QACE9I,IAAa5oB,OAAO8uB,GAAwBjM,KAAc;QAC1DmG,IAAqBpG;QACrBsG,IAAuB0F;QACvBtF,IAAsBA,CAACgB,GAAUe;UAC/B,OAAQ1lB,QAAQ6kB,GAAcD,eAAEA,KAAkBD;UAClD,MAAM+I,KACHhI,KAAkBd,MAAkBrO,IACjC1R,YAAYggB,GAAgBoE,GAAcC,KAC1C;UACN,OACEwE,OACEjpB,QAAQogB,GAAgB,IAAI7S,WAC5Bwb,0BAA0B7I;AAAS;;MAM7CoE,IAAiC0E;MACjC3E,IAAgCz0B;AAClC;IAEA,IAAIi5B,GAAiB;MACnBpC,EAA4BtrB;MAC5B,IAAIhI,QAAQy1B,IAAgB;QAC1B,MAAM5vB,IAAU4vB,EAAc;QAC9B,MAAMM,IAAUN,EAAc;QAC9BzE,IAAkBpxB,SAASiG,MAAYA;QACvCorB,IAAmBrxB,SAASm2B,MAAYA;AAC1C,aAAO,IAAIn2B,SAAS61B,IAAgB;QAClCzE,IAAkByE;QAClBxE,IAAmB;AACrB,aAAO;QACLD,IAAkB;QAClBC,IAAmB;AACrB;AACF;IAEA,IAAIxC,GAAa;MACf,MAAMuH,IAAmBpB;MACzB,MAAMqB,IAAsBxB,KAAyBA;MACrD,MAAMyB,IACJhF,KAAiCA;MAEnC8E,KACE7tB,WACEwrB,GACAW,eAAe0B,EAAiB,IAAIA,EAAiB,IAAIvH;MAG7DwH,KACE9tB,WAAWwrB,GAAaG,iBAAiBmC,EAAoB,IAAIxH;MAEnEyH,KACE/tB,WAAWwrB,GAAaQ,kBAAkB+B,EAAoB,IAAIzH;AACtE;IAEAiF,aAAaC;IAEb,OAAOA;AAAW,KAEpBlB;AACD;;ACpSI,MAAM0D,wBAAwBA,CACnC3yB,GACAL,MACOpD,WAAWoD,KAASA,EAAMF,MAAM,GAAGO,KAAQL;;AAE7C,MAAMizB,8BAA8BA,CACzC5yB,GACA6yB,GACAC,GACAC;EAEA,MAAMC,IAAuB/2B,YAAY82B,KACrCD,IACAC;EACJ,MAAME,IAAyBN,sBAC7B3yB,GACAgzB;EAEF,OAAOC,KAA0BJ,EAAoCpzB,MAAM,GAAGO;AAAK;;AAG9E,MAAMkzB,+BAA+BA,CAC1ClzB,GACAmzB,GACAC,GACAC;EAEA,MAAMC,IAAwBr3B,YAAYo3B,KACtCD,IACAC;EACJ,MAAMJ,IAAyBN,sBAC7B3yB,GACAszB;EAEF,SACIL,MACDh2B,cAAcg2B,KACXA,IACAE,EAAqC1zB,MAAM,GAAGO;AAAM;;AAIrD,MAAMuzB,uBAAuBA,CAClCC,GACAC;EAEA,OAAM3O,0BAAEA,GAAwBhB,MAAEA,KAAS2P,KAA6B,CAAA;EACxE,OAAMna,GAAEC,GAAyB+B,GAAEC,GAAuBmY,GAAE9N,KAC1De;EACF,OAAQ7B,0BAA0B6O,GAAiC7P,MAAM8P,KACvEhO,IAA4BN;EAE9B,MAAMuO,IACJ/O,KAAAA,OAAAA,IAA4B6O;EAC9B,MAAMG,IAAmC73B,YAAY6nB,KAAQ8P,IAAc9P;EAE3E,MAAMiQ,KACHxa,EAA0B3L,KAAK2L,EAA0B1L,MAC1DgmB;EACF,MAAMG,IACJR,MACCr3B,OAAO23B,MACHvY,IACDuY;EAEN,SAASC,OAAmCC;AAA6B;;ACzFpE,MAAMC,gCAAgCA,CAC3CrvB,GACAkW,GACAC,GACAmZ;EAEA,MAAMC,IAA+B;EACrC,MAAMC,IAA6B;EACnC,MAAMC,IAA+B;EACrC,OAAMX,GAAE9N,KAA8Be;EACtC,OAAQ1E,YAAYqS,KAA0B1O;EAC9C,OAAQP,MAAMkP,KAA8BD;EAC5C,OAAMtG,IACJC,GAAOC,IACPC,GAAK/S,GACLC,GAASmZ,IACTC,GAAYrG,IACZC,GAAoBG,IACpBC,GAAOvT,GACPC,KACEL;EACJ,OAAQmH,YAAYyS,KAAoBD,IAAe,CAAE,IAAG7vB;EAC5D,OAAQygB,MAAMsP,KAAuBD,KAAkB,CAAA;EACvD,MAAMxb,IAA6B;EACnC,MAAM0b,IAA6C;EACnD,MAAMC,IAA2C;EACjD,MAAMC,IAAyBC,6BAG7B,EAAC9G,GAASE,GAAO9S,MACjB,MAAOF,KAAqBsT,IAAUR,IAAUE,IAChDoG,GACAI;EAGF,MAAMK,qBAAsBC;IAC1B,IAAIj5B,GAAS;MACX,IAAIk5B,IAAkC;MACtC,IAAIC,IAAmC;MACvC,MAAMC,IAAW,IAAIp5B,EAAQ;QAC3ByC,QAAQ4vB;QACR4G;;MAEF,MAAMI,kBAAkBA;QACtBH,KAAiBA,EAAc5P;QAC/B4P,IAAgB;AAAI;MAEtB,MAAMI,6BAA8BC;QAClC,OAAMC,IAAEC,KAAuB1a;QAC/B,MAAM2a,IACJtjB,oCAAoCqjB,GAAoBR;QAC1D,MAAMtnB,IAAesnB,MAAS;QAC9B,MAAMU,IAAiB,EACrBjoB,0BAA0B,GAAGC,IAC7BD,0BAA0B,aAAaC,IAAe,MAAM,gBAAgBA;QAE9E,MAAMioB,IAAYF,IAAyBC,IAAiBA,EAAeE;QAE3E,IACEV,EAAuB,OAAOS,EAAU,MACxCT,EAAuB,OAAOS,EAAU;UAExC,OAAOP;;QAGTA;QACAF,IAAyBS;QACzBV,IAAgBK,EAAUO,GAAQC,QAChC;UAEExyB,OAAO,EAAC;UAGRqyB;WAGF;UACER;;QAIJ,OAAOC;AAAe;MAGxB,OAAO;QACLC;;AAEJ;AAAA;EAEF,MAAMU,IAAiB;IACrBpoB,GAAGonB,mBAAmB;IACtBnnB,GAAGmnB,mBAAmB;;EAExB,MAAMiB,qBAAqBA;IACzB,OAAMC,IAAEC,GAAeC,IAAEC,KAAkBtb;IAC3C,MAAMub,eAAeA,CAACC,GAA0BC,MAC9C7wB,UAAU,GAAG,GAAG4wB,KAAoBA,IAAmBC,MAAuB;IAEhF,OAAO;MACL5oB,GAAG0oB,aAAaD,EAAczoB,GAAGuoB,EAAgBvoB;MACjDC,GAAGyoB,aAAaD,EAAcxoB,GAAGsoB,EAAgBtoB;;AAClD;EAEH,MAAM4oB,mCAAmCA,CACvCC,GACA7rB,GACA3C;IAEA,MAAMyuB,IAASzuB,IAAMK,WAAWF;IAChC7J,KAAKk4B,IAAsBE;MACzBD,EAAOC,EAAmBC,IAAYhsB;AAAW;AACjD;EAEJ,MAAMisB,iBAAiBA,CACrBJ,GACArqB;IAEA7N,KAAKk4B,IAAsBE;MACzB,OAAOzwB,GAAKgG,KAAUE,EAASuqB;MAC/B1qB,UAAU/F,GAAKgG;AAAO;AACtB;EAEJ,MAAM4qB,2BAA2BA,CAC/BzuB,GACAJ,GACA8uB;IAEA,MAAMC,IAAa36B,UAAU06B;IAC7B,MAAME,IAAgBD,IAAaD,IAAiB;IACpD,MAAMG,IAAcF,KAAcD,IAAiB;IACnDE,KAAiBT,iCAAiC7B,GAAsBtsB,GAAWJ;IACnFivB,KAAeV,iCAAiC5B,GAAoBvsB,GAAWJ;AAAI;EAErF,MAAMkvB,gCAAgCA;IACpC,MAAMC,IAAkBpB;IACxB,MAAMqB,yBACHC,KACAhC,KAAkC,EACjCA,EAAUsB,IACV;MACE1C,CAACA,IAA+BnoB,eAAeurB,KAAuB;;IAI5ET,eAAelC,GAAsB0C,uBAAuBD,EAAgBzpB;IAC5EkpB,eAAejC,GAAoByC,uBAAuBD,EAAgBxpB;AAAG;EAE/E,MAAM2pB,gCAAgCA;IACpC,KAAKx7B,GAAS;MACZ,OAAMw5B,IAAEC,KAAuB1a;MAC/B,MAAM0c,IAAgBnlB,4BACpBmjB,GACA1kB,iBAAiBsd;MAEnB,MAAMiJ,yBACHI,KACAnC,KAAkC,EACjCA,EAAUsB,IACV;QACEzC,CAACA,IAA6BpoB,eAAe0rB,KAAqB;;MAIxEZ,eAAelC,GAAsB0C,uBAAuBG,EAAc7pB;MAC1EkpB,eAAejC,GAAoByC,uBAAuBG,EAAc5pB;AAC1E;AAAA;EAEF,MAAM8pB,qCAAqCA;IACzC,OAAMnC,IAAEC,KAAuB1a;IAC/B,MAAM2a,IAAyBtjB,oCAAoCqjB;IACnE,MAAM6B,yBACHM,KACArC,KAAkC,EACjCA,EAAUsB,IACV;MACExC,CAACA,IAA+BuD,IAA0C,MAAM;;IAItFd,eAAelC,GAAsB0C,uBAAuB5B,EAAuB9nB;IACnFkpB,eAAejC,GAAoByC,uBAAuB5B,EAAuB7nB;IAGjF,IAAI7R,GAAS;MACX44B,EAAqBthB,QAAQ0iB,EAAepoB,EAAG0nB;MAC/CT,EAAmBvhB,QAAQ0iB,EAAenoB,EAAGynB;AAC/C;AAAA;EAEF,MAAMuC,mCAAmCA;IACvC,IAAI1c,MAAsBsT,GAAS;MACjC,OAAMyH,IAAEC,GAAeX,IAAEC,KAAuB1a;MAChD,MAAM+c,IAA2B1lB,oCAAoCqjB;MACrE,MAAMgC,IAAgBnlB,4BACpBmjB,GACA1kB,iBAAiBsd;MAEnB,MAAM0J,yBAA4CxC;QAChD,OAAMyC,IAAEnB,KAAetB;QACvB,MAAMpvB,IAAMgD,OAAO0tB,OAAgBxb,KAAawb;QAChD,MAAMoB,oBAAoBA,CACxBP,GACAlB,GACA0B;UAEA,MAAMC,IAAK3B,IAAqBkB;UAChC,OAAOzrB,cAAcisB,IAA2BC,KAAMA;AAAG;QAG3D,OAAO,EACLhyB,GACAA,KAAO;UACLyvB,WAAWloB,0BAA0B;YACnCE,GAAGqqB,kBAAkBR,EAAc7pB,GAAGuoB,EAAgBvoB,GAAGkqB,EAAyBlqB;YAClFC,GAAGoqB,kBAAkBR,EAAc5pB,GAAGsoB,EAAgBtoB,GAAGiqB,EAAyBjqB;;;AAGvF;MAGHipB,eAAelC,GAAsBmD;MACrCjB,eAAejC,GAAoBkD;AACrC;AAAA;EAEF,MAAMK,uBAAwBzqB;IAC5B,MAAM0qB,IAAQ1qB,IAAe,MAAM;IACnC,MAAM2qB,IAAqB3qB,IACvBmJ,KACAC;IACJ,MAAMwhB,IAAY3tB,UAAU,GAAGgM,MAAsB0hB;IACrD,MAAME,IAAQ5tB,UAAUoM;IACxB,MAAMyhB,IAAS7tB,UAAUqM;IACzB,MAAMtV,IAAS;MACbk1B,IAAY0B;MACZG,IAAQF;MACR1C,IAAS2C;;IAEX,MAAMrD,IAAWY,EAAeqC;IAEhCj5B,KAAKuO,IAAeinB,IAAuBC,GAAoBlzB;IAC/DvC,KAAK8Z,GAAY,EACf9O,eAAemuB,GAAWC,IAC1BpuB,eAAeouB,GAAOC,IACtBt2B,KAAK4H,gBAAgBwuB,IACrBnD,KAAYA,EAASE,GAA2B3zB,IAChDuyB,EAAsBvyB,GAAQo1B,0BAA0BppB;IAG1D,OAAOhM;AAAM;EAEf,MAAMg3B,IAAuCx2B,KAAKi2B,sBAAsB;EACxE,MAAMQ,IAAqCz2B,KAAKi2B,sBAAsB;EACtE,MAAMS,iBAAiBA;IACrBzuB,eAAe0qB,GAAwBF,EAAqB,GAAGiC;IAC/DzsB,eAAe0qB,GAAwBD,EAAmB,GAAGgC;IAE7D,OAAO10B,KAAKpC,iBAAiBmZ;AAAW;EAG1Cyf;EACAC;EAEA,OAAO,EACL;IACEE,IAAgC1B;IAChC2B,IAAgCvB;IAChCwB,IAAqCrB;IACrCsB,IAAmCpB;IACnCqB,IAA2BnC;IAC3BoC,IAAa;MACXC,IAAsBxE;MACtByE,IAAQV;MACRW,IAAQn3B,KAAK20B,gBAAgBlC;;IAE/B2E,IAAW;MACTH,IAAsBvE;MACtBwE,IAAQT;MACRU,IAAQn3B,KAAK20B,gBAAgBjC;;KAGjCgE;AACD;;AC5TI,MAAMW,8BAA8BA,CACzCxgC,GACA8hB,GACAC,GACA0e,MAEO,CAAC7C,GAAoBG,GAA0BppB;EACpD,OAAMugB,IACJC,GAAK/S,GACLC,GAASH,GACTC,GAAiBiT,IACjBC,GAAoBqL,IACpBC,GAAY7K,IACZC,KACEjU;EACJ,OAAMkd,IAAEnB,GAAU+C,IAAElB,GAAMmB,IAAE/D,KAAYc;EACxC,OAAOkD,GAAcC,KAAqB33B,iBAAiB;EAC3D,OAAO43B,GAAmCC,KACxC73B,iBAAiB;EACnB,MAAM83B,8BAA+BC;IACnC59B,WAAW8xB,EAAqB+L,aAC9B/L,EAAqB+L,SAAS;MAC5BC,UAAU;MACV/sB,MAAM6sB,EAAYvsB;MAClBT,KAAKgtB,EAAYtsB;;AACjB;EAGN,MAAMysB,gCAAgCA;IACpC,MAAMC,IAA8B;IACpC,MAAMC,IAAc,SAAS7sB,IAAe,MAAM;IAClD,MAAM8sB,IAAiB9sB,IAAe9M,IAAWC;IACjD,MAAM45B,IAAa/sB,IAAe,SAAS;IAC3C,MAAMgtB,IAAQhtB,IAAe,MAAM;IACnC,MAAM0qB,IAAQ1qB,IAAe,MAAM;IAEnC,MAAMitB,2BACJA,CAACC,GAAyBC,MAA2BC;MACnD,OAAM7E,IAAEC,KAAoBpb;MAC5B,MAAMigB,IAAkBvsB,EAAciqB,GAAQiC,KAASlsB,EAAcqnB,GAAS6E;MAC9E,MAAMM,IAAsBH,IAAgBC,IAAiBC;MAC7D,MAAME,IAAcD,IAAqB9E,EAAgBkC;MAEzD1nB,gBAAgB0d,GAAsB;QACpCgK,CAACA,IAAQwC,IAAkBK;;AAC3B;IAEN,MAAMC,IAA2C;IAEjD,OAAOtrB,iBAAiB6oB,GAAQ,gBAAgB0C;MAC9C,MAAMC,IACJhyB,QAAQ+xB,EAAiBx2B,QAAgB,IAAIqS,UAAgC6e;MAC/E,MAAMwF,IAAwBD,IAAevF,IAAU4C;MAEvD,MAAM6C,IAAmBviC,EAAQipB;MACjC,MAAMuZ,IAAwBD,EAAiBF,IAAe,eAAe;MAC7E,OAAMI,QAAEA,GAAMC,WAAEA,GAASC,aAAEA,KAAgBP;MAC3C,OAAM3Y,UAAEA,KAAa8Y;MAErB,MAAMK,IACJH,MAAW,KACXC,KACAF,MACC/Y,KAAY,IAAI3a,SAAS6zB;MAE5B,IAAIC,GAAqB;QACvB77B,gBAAgBo7B;QAChBlB;QAEA,MAAM4B,KACHR,MAAiBD,EAAiBU,YAAYN,MAA0B;QAC3E,MAAMO,IAAgB55B,KAAK4M,uBAAuB+mB;QAClD,MAAMkG,IAAe75B,KAAK4M,uBAAuB2pB;QACjD,MAAMuD,kBAAkBA,CAACC,GAAsBC,OAC5CD,KAAcH,KAAiBrB,MAAeyB,KAAaH,KAAgBtB;QAC9E,MAAM0B,IACJxhC,EAAUmU,sBAAsBsf,GAAsBoM,MACpDhsB,EAAc4f,GAAsBsM,MAAU;QAClD,MAAMhb,IAAqBib,yBACzB7pB,iBAAiBsd,GAAsBgK,IACvC,IAAI+D;QAEN,MAAMC,IAAoBjB,EAAiBZ;QAC3C,MAAM0B,IAAaH;QACnB,MAAMI,IAAYH;QAClB,MAAMnc,IAAeqc,EAAWzB;QAChC,MAAM6B,IAAeL,gBAAgBC,GAAYC,KAAatc,IAAe;QAC7E,MAAM0c,IAA6BF,IAAoBF,EAAUzB;QACjE,MAAM8B,IAAcnB,IAAe,IAAIkB,IAA6BD;QACpE,MAAMG,wBAAyBC;UAE7B38B,gBAAgB48B;UAChBrB,EAAsBmB,sBAAsBC,EAAeE;AAAU;QAEvE,MAAMC,IAAoBxB,KAAgBQ;QAC1C,MAAMpM,IAA8BV;QAEpC,MAAM4N,IAAsB,EAC1B9sB,iBAAiB8pB,GAAcY,GAA6BkC,wBAC5D5sB,iBAAiB8pB,GAAc,gBAAgB5lB,KAAiBtD,eAAesD,KAAQ;UACrF/D,GAAU;YAEZH,iBAAiB6oB,GAAQ6B,GAA6BkC,wBACtDI,KACEhtB,iBAAiB6oB,GAAQ,gBAAgBoE,KACvCnd,EACE6c,KAAeM,EAAiBtC,KAAe6B,OAGrDQ;UAEI,MAAME,IAA0BhsB,iBAAiBsd;UACjDoB;UACA,MAAMuN,IAAuBjsB,iBAAiBsd;UAC9C,MAAM4O,IAAiB;YACrBrvB,GAAGovB,EAAqBpvB,IAAImvB,EAAwBnvB;YACpDC,GAAGmvB,EAAqBnvB,IAAIkvB,EAAwBlvB;;UAGtD,IAAI/S,EAAQmiC,EAAervB,KAAK,KAAK9S,EAAQmiC,EAAepvB,KAAK,GAAG;YAClEkhB;YACApe,gBAAgB0d,GAAsB0O;YACtC7C,4BAA4B+C;YAC5BjD,EAAkCvK;AACpC;AACD;QAGL6L,EAAsB4B,kBAAkB9B,EAAiBwB;QAEzD,IAAIf;UACFlc,EAAmB6c;eACd,KAAKnB,GAAc;UACxB,MAAM8B,IAAqBpoB,8BACzB0K;UAEF,IAAI0d,GAAoB;YACtB,MAAMC,IAA2BD,EAC/Bxd,GACA6c,GACA3c,IACCE;cAEC,IAAIA;gBACF0P;;gBAEArwB,KAAKu9B,GAAqBlN;;AAC5B;YAIJrwB,KAAKu9B,GAAqBS;YAC1Bh+B,KAAK+7B,GAAuBh5B,KAAKi7B,GAA0B;AAC7D;AACF;AACF;AAAA;AACA;EAGJ,IAAIC,IAAgB;EAEpB,OAAOl7B,KAAKpC,iBAAiB,EAC3B8P,iBAAiBimB,GAAS,4BAA4B2D,IACtD5pB,iBAAiBgnB,GAAY,iBAAgB;IAC3CE,EAAyB3f,IAA+B;AAAK,OAE/DvH,iBAAiBgnB,GAAY,+BAA8B;IACzDE,EAAyB3f,IAA+B;AAAM,QAG/D+D,KACCtL,iBAAiBgnB,GAAY,cAAa;IACxC,MAAMyG,IAAiBh0B;IACvB,IACEhD,QAAQg3B,GAAgB5nB,MACxBpP,QAAQg3B,GAAgB9nB,MACxB8nB,MAAmBjjC,SAASypB;MAE5BxoB,EAAK6G,KAAKsQ,cAAc4I,IAAY;;AACtC,OAGJxL,iBACEgnB,GACA,UACC0G;IACC,OAAMC,QAAEA,GAAMC,QAAEA,GAAMC,WAAEA,KAAcH;IAGtC,IAAIF,KAAiBK,MAAc,KAAKv0B,OAAO0tB,OAAgB1I;MAC7D+L,4BAA4B;QAC1BtsB,GAAG4vB;QACH3vB,GAAG4vB;;;IAIPJ,IAAgB;IAChBtG,EAAyBvf,IAAyB;IAClDsiB,GAAa;MACXuD,IAAgB;MAChBtG,EAAyBvf;AAAwB;IAGnD/G,eAAe8sB;AAAW,MAE5B;IAAEvtB,GAAU;IAAOC,GAAU;MAG/BJ,iBACEgnB,GACA,eAGA10B,KAAK0N,kBAAkB8pB,GAAc,SAASjpB,gBAAgB;IAC5DP,GAAO;IACPF,GAAU;IACVD,GAAU;MAEZ;IAAEC,GAAU;MAEdqqB,iCACAP,GACAE;AACA;;ACxNC,MAAM0D,wBAAwBA,CACnC/4B,GACA5L,GACAgiB,GACAD,GACAD,GACA9B;EAEA,IAAI4kB;EACJ,IAAIC;EACJ,IAAIC;EACJ,IAAIC;EACJ,IAAIC;EACJ,IAAIC,IAAyC/7B;EAC7C,IAAIg8B,IAAwB;EAC5B,MAAMC,IAAwB,EAAC,SAAS;EAGxC,MAAMC,yBAA0BrqB,KAC9BoqB,EAAsBr2B,SAASiM,EAAM4nB;EAEvC,OAAO0C,GAA6BC,KAA8Bl8B;EAClE,OAAOm8B,GAAmCC,KACxCp8B,iBAAiB;EACnB,OAAOq8B,GAAwBC,KAA+Bt8B,iBAAiB;EAC/E,OAAOu8B,GAAiBC,KAAwBx8B,kBAAiB,MAAM87B;EACvE,OAAOlZ,GAAU6T,KAAkB5E,8BACjCrvB,GACAkW,GACAC,GACAye,4BACExgC,GACA8hB,GACAC,IACChH,KACCqqB,uBAAuBrqB,MAEvB8qB;EAGN,OAAM3Q,IAAEC,GAAK2Q,IAAEC,GAAmBvQ,IAAEC,KAAY3T;EAChD,OAAMkkB,IACJ9F,GAAyB+F,IACzBnG,GAA8BoG,IAC9BnG,GAA8BoG,IAC9BnG,GAAmCoG,IACnCnG,KACEjU;EACJ,MAAMqa,2BAA2BA,CAACC,GAAyBC;IACzDX;IACA,IAAIU;MACFpG,EAA0B3hB;WACrB;MACL,MAAMioB,IAAOr9B,KAAK+2B,GAA2B3hB,IAAkC;MAC/E,IAAI2mB,IAAwB,MAAMqB;QAChCZ,EAAgBa;;QAEhBA;;AAEJ;AAAA;EAEF,MAAMX,6CAA6CA;IACjD,IAAIf,KAAmBF,KAAeG,GAAiB;MACrDsB,yBAAyB;MACzBd,GAAkC;QAChCc,yBAAyB;AAAM;AAEnC;AAAA;EAEF,MAAMI,2BAA4Bv3B;IAChCgxB,EAA0B5hB,IAA4BpP,GAAK;IAC3DgxB,EAA0B5hB,IAA4BpP,GAAK;AAAM;EAEnE,MAAMw3B,mBAAoB3rB;IACxB,IAAIqqB,uBAAuBrqB,IAAQ;MACjC6pB,IAAcE;MACdA,KAAmBuB,yBAAyB;AAC9C;AAAA;EAEF,MAAMnmB,IAA6B,EACjC0lB,GACAJ,GACAE,GACAJ,GACA,MAAML,KAENpuB,iBAAiBse,GAAO,eAAeuR,kBAAkB;IAAEvvB,GAAO;MAClEN,iBAAiBse,GAAO,gBAAgBuR,mBACxC7vB,iBAAiBse,GAAO,iBAAiBpa;IACvC,IAAIqqB,uBAAuBrqB,IAAQ;MACjC6pB,IAAc;MACdE,KAAmBuB,yBAAyB;AAC9C;AAAA,OAEFxvB,iBAAiBse,GAAO,gBAAgBpa;IACtCqqB,uBAAuBrqB,MACrB8pB,KACAgB;AAA4C,OAEhDhvB,iBAAiBkvB,GAAqB,WAAWhrB;IAC/CsqB,GAA4B;MAC1BtF;MACA8F;AAA4C;IAG9C7lB,EAASjF;IAETklB;AAAmC;EAIvC,OAAO,EACL,MAAM92B,KAAKpC,iBAAiBX,KAAK8Z,GAAY2f,OAC7C,EAAGjH,OAAcE,OAAQ6N,OAAuBC;IAC9C,OAAMC,IACJC,GAAoBC,IACpBC,GAAsBC,IACtBC,GAAqBC,IACrBC,KACER,KAAyB,CAAA;IAC7B,OAAMS,IAAEhQ,GAAiBiQ,IAAEnV,KAAYwU,KAAyB,CAAA;IAChE,OAAMjhB,GAAEpB,KAAoBtC;IAC5B,OAAM1B,GAAEC,KAA8BoN;IACtC,OAAM4Z,GAAE9lB,GAAc+lB,IAAEC,KAAiB1lB;IACzC,OAAOvB,GAAoCC,KACzCmY,EAAa;IACf,OAAO1P,GAAOwe,KAAgB9O,EAAa;IAC3C,OAAOzP,GAAYwe,KAAqB/O,EAAa;IACrD,OAAOxP,GAAUwe,KAAmBhP,EAAa;IACjD,OAAOtP,GAAiBue,KAA0BjP,EAAa;IAC/D,OAAOvP,KAAiBuP,EAAa;IACrC,OAAOrP,GAAYue,KAAqBlP,EAAa;IACrD,OAAOpP,GAAaue,KAAsBnP,EAAa;IACvD,OAAO5P,GAAUgf,KAAmBpP,EAAa;IACjD,MAAMqP,IAAgB9V,MAAY2G;IAClC,MAAMjY,IAAc4mB,EAAa7yB,KAAK6yB,EAAa5yB;IACnD,MAAMqzB,IACJpB,KACAE,KACAI,KACA/P,KACAyB;IACF,MAAMqP,IAAmBjB,KAAyBS,KAAqBK;IACvE,MAAMrlB,IACJnC,KACAD,EAA0B3L,KAC1B2L,EAA0B1L;IAE5B,MAAMuzB,yBAAyBA,CAC7BznB,GACAoC,GACApO;MAEA,MAAM0zB,IACJ1nB,EAAiB7R,SAAS7G,OACzBkhB,MAAephB,KAAeohB,MAAe,UAAUpG,MAAkB9a;MAE5Ei4B,EAA0BhiB,IAA2BmqB,GAAW1zB;MAEhE,OAAO0zB;AAAS;IAGlBnD,IAAwB7b;IAExB,IAAI4e;MACF,IAAI3e,KAAmBzI,GAAa;QAClC4lB,yBAAyB;QACzBxB;QACAQ,GAAuB;UACrBR,IAAyCpuB,iBACvCkvB,GACA,UACA58B,KAAKs9B,0BAA0B,OAC/B;YACEtvB,GAAO;;AAEV;AAEL;QACEsvB,yBAAyB;;;IAI7B,IAAIhmB;MACFyf,EAA0BviB,IAA6BgF;;IAGzD,IAAI+kB,GAAc;MAChBxH,EAA0B8E;MAC1B9E,EAA0BhX,GAAO;MAEjC8b,IAAY9b;AACd;IAEA,IAAI2e,MAA2Bve;MAC7Bmd,yBAAyB;;IAG3B,IAAImB,GAAiB;MACnB/C,IAAiBzb,MAAa;MAC9B0b,IAAkB1b,MAAa;MAC/B2b,IAAkB3b,MAAa;MAC/Bid,yBAAyBtB,GAAiB;AAC5C;IAEA,IAAI+C;MACF5H,EAA0BxhB,IAAqC6K;;IAGjE,IAAIwe;MACF7H,EAA0BzhB,MAAsC+K;;IAKlE,IAAI2e,GAAkB;MACpB,MAAMG,IAAWF,uBAAuBpf,EAASpU,GAAG6M,EAAe7M,GAAG;MACtE,MAAM2zB,IAAWH,uBAAuBpf,EAASnU,GAAG4M,EAAe5M,GAAG;MACtE,MAAM2zB,IAAYF,KAAYC;MAE9BrI,EAA0B/hB,KAA+BqqB;AAC3D;IAGA,IAAIN,GAAkB;MACpBnI;MACAD;MACAG;MACAmH,KAA6BpH;MAE7BE,EAA0B7hB,KAA6BopB,EAAa7yB,GAAG;MACvEsrB,EAA0B7hB,KAA6BopB,EAAa5yB,GAAG;MACvEqrB,EAA0BriB,IAAuByG,MAAoBmR;AACvE;AAAA,KAEF,CAAA,GACAzJ;AACD;;ACnNI,MAAMyc,+BACX78B;EAEA,MAAMyU,IAAMsN;EACZ,OAAM+M,GAAE9N,GAAyBtK,GAAEC,KAA4BlC;EAC/D,OAAQ2L,UAAU0c,KAAwB9b;EAC1C,OACEV,SAASyc,GACTxc,UAAUyc,GACVxc,SAASyc,KACPH;EACJ,MAAMI,IAAc7kC,cAAc2H;EAClC,MAAMm9B,IAAiCD,IAAc,CAAE,IAAGl9B;EAC1D,OAAQogB,UAAUgd,KAAiBD;EACnC,OACE7c,SAAS+c,GACT9c,UAAU+c,GACV9c,SAAS+c,KACPH,KAAgB,CAAA;EAEpB,MAAMI,IAAgBN,IAAcl9B,IAASm9B,EAA8Bn9B;EAC3E,MAAM4uB,IAASxqB,cAAco5B;EAC7B,MAAMC,IAAgBD,EAAcC;EACpC,MAAMC,IAAaD,EAAcE;EACjC,MAAMC,oBAAoBA,MAAMH,EAAcI,eAAenoC;EAC7D,MAAMs4B,IAA8BzwB,KAAKugC,6BAAoC,EAACN;EAC9E,MAAMlP,IAA+B/wB,KAAK4yB,8BAAqC,EAACqN;EAChF,MAAMO,IAAexgC,KAAKyI,WAAW;EACrC,MAAMg4B,IAA0BzgC,KAC9BywB,GACA+P,GACAf;EAEF,MAAMiB,IAAyB1gC,KAC7B+wB,GACAyP,GACAd;EAEF,MAAMiB,qBAAsB38B;IAC1B,MAAM48B,IAAat0B,EAActI;IACjC,MAAM68B,IAAar0B,EAAcxI;IACjC,MAAM88B,IAAYx2B,UAAUtG,GAAKxF;IACjC,MAAMuiC,IAAYz2B,UAAUtG,GAAKvF;IAEjC,OACGoiC,EAAW70B,IAAI40B,EAAW50B,IAAI,MAAMuL,kBAAkBupB,MACtDD,EAAW50B,IAAI20B,EAAW30B,IAAI,MAAMsL,kBAAkBwpB;AAAW;EAGtE,MAAMC,IAA0BP,EAAwBV;EACxD,MAAMkB,IAAmBD,MAA4Bf;EACrD,MAAM/S,IAAuB+T,KAAoB5P;EACjD,MAAM6P,KAA0BD,KAAoBP,EAAuBV;EAI3E,MAAMmB,KAAqBF,KAAoBD,MAA4BE;EAC3E,MAAME,IAAkBlU,IAAuBiT,IAAaa;EAC5D,MAAMK,IAAcnU,IAAuBkU,IAAkBnB;EAC7D,MAAMqB,KACHL,KACDlQ,EAA6ByP,GAAchB,GAA8BM;EAC3E,MAAMyB,KAAkBJ,KAAqBD;EAC7C,MAAMM,IAAoB,EAACD,GAAgBH,GAAiBE,GAAgBD,IAAanzB,KACtFlK,KAAQlJ,cAAckJ,OAASgD,OAAOhD,MAAQA;EAEjD,MAAMy9B,qBAAsBz9B,KAA6BA,KAAOtH,QAAQ8kC,GAAmBx9B;EAC3F,MAAM09B,KACHD,mBAAmBL,MAAoBT,mBAAmBS,KACvDA,IACAnB;EACN,MAAM0B,IAAsBzU,IAAuBiT,IAAaiB;EAChE,MAAMQ,IAAqB1U,IAAuBgT,IAAgBkB;EAElE,MAAMS,IAAgD;IACpD/V,IAASmU;IACTjU,IAAOqV;IACPnoB,GAAWkoB;IACXU,IAAUR;IACVlV,IAAUmV;IACVrV,IAAsByV;IACtB/E,IAAqBgF;IACrBG,IAA8B1Q,IAAS8O,IAAauB;IACpDlK,IAAc0I;IACd5T,IAAS+E;IACTiB,IAAcqN;IACd3mB,GAAmBioB;IACnBe,IAAY3B;IACZ7T,IAAoByV,KAClBj8B,aAAao7B,GAAiB7tB,GAAuB0uB;IACvDvV,IAAyBA,CAACuV,GAAoCl8B,MAC5DD,mBAAmBs7B,GAAiB7tB,GAAuB0uB,GAA4Bl8B;IACzF6mB,IAA8BA,MAC5B9mB,mBACE67B,GACApuB,GACAK,GACA;;EAGN,OAAMiY,IAAEC,GAAOC,IAAEC,GAAKkW,IAAEJ,GAAQ7oB,GAAEC,GAASiT,IAAEC,MAAayV;EAC1D,MAAM9qB,KAA4B,EAChC;IAEEvS,YAAYwnB,GAAO,EAAC3Y,GAAmBH;IACvC1O,YAAYsnB,GAAS5Y;IACrB,IAAIme;MACF7sB,YAAY27B,GAAY,EAACjtB,GAAyBG;;AACpD;EAGJ,IAAI8uB,KAAiBr7B,SACnB,EAACslB,IAAUlT,GAAW4oB,GAAU9V,GAAOF,IAASzlB,MAAMrC,KAAQA,MAAQy9B,mBAAmBz9B;EAE3F,MAAMo+B,KAAclV,IAAuBpB,IAAUM,MAAYlT;EACjE,MAAM2L,KAAU7kB,KAAKpC,iBAAiBmZ;EACtC,MAAM2f,iBAAiBA;IACrB,MAAM2L,IAAShC;IACf,MAAMiC,IAAgBn7B;IACtB,MAAMo7B,SAAUv+B;MACdiE,eAAejB,OAAOhD,IAAM8C,SAAS9C;MACrC4D,eAAe5D;AAAI;IAGrB,MAAMw+B,yBAA0Bn7B,KAC9BqG,iBAAiBrG,GAAe,+BAA+BkH,gBAAgB;MAC7ET,GAAU;MACVD,GAAU;;IAEd,MAAM40B,IAAc;IACpB,MAAMC,IAA2B3+B,QAAQmV,GAAWupB;IACpD,MAAME,IAA2BH,uBAAuBF;IACxDj+B,SAAS2nB,GAAO3Y,GAAmB4tB,IAAmB,KAAK3tB;IAC3DjP,SAASy9B,GAAU/tB,GAAsB;IACzC1P,SAAS6U,GAAW3F,GAAuB;IAC3ClP,SAAS+nB,IAAUpY,IAAsB;IAEzC,KAAKitB,GAAkB;MACrB58B,SAAS6U,GAAWupB,GAAaC,KAA4B;MAC7DrR,KAAUhtB,SAAS87B,GAAY/sB,GAAuB;AACxD;IAEAnL,eAAem6B,IAAaD;IAC5Bl6B,eAAe+jB,GAAO8V;IACtB75B,eAAe65B,KAAY9V,IAAQiV,KAAoB/nB;IACvDjR,eAAeiR,GAAWkT;IAE1BnvB,KAAK8Z,IAAY,EACf4rB,GACA;MACE,MAAMC,IAAmBz7B;MACzB,MAAM07B,IAAsBpB,mBAAmBvoB;MAG/C,MAAM4pB,IACJD,KAAuBD,MAAqB1pB,IAAY4S,IAAU8W;MACpE,MAAMG,IAA8BP,uBAAuBM;MAC3Dt+B,YAAYs9B,GAAU/tB;MACtBvP,YAAY4nB,IAAUpY;MACtBxP,YAAY0U,GAAW3F;MACvB8d,KAAU7sB,YAAY27B,GAAY/sB;MAClCsvB,IACIr+B,SAAS6U,GAAWupB,GAAaC,KACjCl+B,YAAY0U,GAAWupB;MAE3BhB,mBAAmBrV,OAAamW,OAAOnW;MACvCyW,KAAuBN,OAAOrpB;MAC9BuoB,mBAAmBK,MAAaS,OAAOT;MACvCxxB,aAAawyB;MACbC;AAA6B;IAIjC,IAAI3pB,MAA4B6nB,GAAkB;MAChDp7B,aAAaqT,GAAW3F,GAAuBM;MAC/C5W,KAAK8Z,IAAY/W,KAAKwE,aAAa0U,GAAW3F;AAChD;IAIAjD,cACG2wB,KAAoB5P,KAAUiR,MAAkBxW,KAAWuW,EAAOr3B,QAAQq3B,IACvEnpB,IACAopB;IAENK;IAGAR,KAAiB;IAEjB,OAAOtd;AAAO;EAGhB,OAAO,EAACgd,GAAoBnL,gBAAgB7R;AAAQ;;ACvQ/C,MAAMme,6BACXA,EAAG5W,WACH,EAAGoR,OAAuByF,OAAiBtT;EACzC,OAAMuT,IAAE7U,KAA4BmP,KAAyB,CAAA;EAC7D,OAAM2F,IAAEpW,KAAqBkW;EAC7B,MAAMpY,IAAyBuB,MAAaiC,KAA2BsB;EAEvE,IAAI9E;IACF9gB,UAAUqiB,GAAU;MAClBztB,CAACA,IAAYouB,KAAoB;;;AAErC;;ACMG,MAAMqW,6BAA2DA,EACpEpX,OAAO8V,OAAU5oB,MAAWF,OAC9B8T;EAEA,OAAOuW,GAAoBC,KAAuB1sC,YAChD;IACEM,GAAQ4I;IACR9I,GAAe2T;KAEjB3K,KAAK2K,oBAAoBqhB,GAAO,WAAW;EAG7C,OAAO,EAAGyD,OAAc+N,OAAuByF,OAAiBtT;IAC9D,KAAK5M,GAASwgB,KAAkBD,EAAoB3T;IACpD,OAAMxW,GAAEC,KAA4BoL;IACpC,OAAMgf,IAAE5Z,GAAY6Z,IAAE/U,GAAgBwP,IAAEhQ,KAAsBsP,KAAyB,CAAA;IACvF,OAAMjhB,GAAEpB,KAAoB8nB;IAC5B,OAAOxjB,GAAiBikB,KAA0BjU,EAAa;IAC/D,MAAMkU,IAAkBhU,KAAUjB;IAElC,IAAI9E,KAAgB2Z,KAAkBI;OACnC5gB,GAASwgB,KAAkBF,EAAmB1T;;IAGjD,MAAMiU,KACH5qB,MAAsB0qB,KAA0BxV,KAAqBqV;IAExE,IAAIK,GAAqB;MAEvB,MAAMC,KAAmBpkB,MAAqBqiB,MAAa1oB;MAC3D,MAAM0qB,IAAoB/gB,EAAQ1X,IAAI0X,EAAQzX;MAC9C,MAAMy4B,IAAkBhhB,EAAQ3X,IAAI2X,EAAQ/lB;MAE5C,MAAMgnC,IAA4B;QAChC1lC,CAACA,IAAiBulC,MAAoB1oB,KAAmB2oB,IAAoB;QAC7EvlC,CAACA,IAAkBslC,KAAmBE,IAAkB;QACxD1lC,CAACA,IAAgBwlC,KAAmB1oB,KAAmB2oB,IAAoB;QAC3E94B,KAAK64B,KAAmB9gB,EAAQ3X,IAAI;QACpCH,OAAO44B,IAAmB1oB,KAAmB4H,EAAQ1X,IAAI,SAAU;QACnEF,MAAM04B,IAAmB1oB,IAAkB,UAAU4H,EAAQzX,IAAK;QAClE5M,CAACA,IAAWmlC,KAAmB,eAAeC;;MAEhD,MAAMG,IAA6B;QACjChmC,CAACA,IAAgB4lC,IAAkB9gB,EAAQ3X,IAAI;QAC/ClN,CAACA,IAAkB2lC,IAAkB9gB,EAAQ1X,IAAI;QACjDjN,CAACA,IAAmBylC,IAAkB9gB,EAAQ/lB,IAAI;QAClDmB,CAACA,IAAiB0lC,IAAkB9gB,EAAQzX,IAAI;;MAIlDvB,UAAU+3B,KAAY5oB,GAAW8qB;MACjCj6B,UAAUmP,GAAW+qB;MAErBzhC,WAAWsqB,GAAO;QAChBgV,IAAU/e;QACVmhB,KAAmBL;QACnBhoB,GAAuBimB,IACnBmC,IACAzhC,WAAW,CAAE,GAAEwhC,GAAcC;;AAErC;IAEA,OAAO;MACLE,IAAsBP;;AACvB;AACF;;ACzBI,MAAMQ,8BAA4DA,CACvEzrB,GACAC;EAEA,MAAM1B,IAAMsN;EACZ,OAAMuH,IACJC,GAAKkW,IACLJ,GAAQ7oB,GACRC,GAASH,GACTC,GAAiB2jB,IACjBC,GAAmB3Q,IACnBC,GAAoBG,IACpBC,GAAOG,IACPC,GAAuB2X,IACvBrC,KACErpB;EACJ,OAAMQ,GAAEC,KAA4BlC;EACpC,MAAMgW,IAAuBZ,KAAWtT;EACxC,MAAMsrB,IAAOtkC,KAAK5H,GAAS;EAC3B,MAAMmsC,IAGF;IACFC,SAASA,MAAM;IACfC,WAAYC,KAAmBA,MAAmB;IAClDC,eAAgBC,KAAuBA,EAAmBC,SAAS;IACnEC,aAAcC,KAAqBA,MAAqB;;EAE1D,MAAMC,IAAwBxoC,KAAK+nC;EAGnC,MAAMU,IAAiB;IACrB/tC,GAAQ0I;IACR5I,GAAe;MAAEgV,GAAG;MAAGC,GAAG;;;EAE5B,MAAMi5B,IAAmB;IACvBhuC,GAAQ2I;IACR7I,GAAe,CAAE;;EAGnB,MAAMmuC,mBAAoBC;IAExB1Y,EAAwB/Y,IAA6BuZ,KAAwBkY;AAAO;EAGtF,MAAMC,+BAAgCC;IACpC,MAAMC,IAA+BP,EAAsBQ,MAAMC;MAC/D,MAAMC,IAAaJ,EAAoBG;MACvC,OAAOC,KAAcnB,EAAgCkB,GAAWC;AAAW;IAI7E,KAAKH;MACH,OAAO;QACLz2B,GAAQ;UAAErD,GAAG;UAAGC,GAAG;;QACnBqD,GAAM;UAAEtD,GAAG;UAAGC,GAAG;;;;IAIrBy5B,iBAAiB;IAEjB,MAAMQ,IAAuB/2B,iBAAiBsd;IAC9C,MAAM0Z,IAAkBlZ,EAAwB5Y,GAA4B;IAC5E,MAAM+xB,IAAoBn4B,iBACxBkvB,GACA99B,IACC8S;MACC,MAAMk0B,IAA0Bl3B,iBAAiBsd;MAEjD,IACEta,EAAMm0B,aACND,EAAwBr6B,MAAMk6B,EAAqBl6B,KACnDq6B,EAAwBp6B,MAAMi6B,EAAqBj6B;QAEnD2C,gBAAgBuD;;AAClB,QAEF;MACE9D,GAAU;MACVE,GAAO;;IAIXQ,gBAAgB0d,GAAsB;MACpCzgB,GAAG;MACHC,GAAG;;IAELk6B;IAEA,MAAM92B,IAASF,iBAAiBsd;IAChC,MAAM2U,IAAar0B,EAAc0f;IACjC1d,gBAAgB0d,GAAsB;MACpCzgB,GAAGo1B,EAAW70B;MACdN,GAAGm1B,EAAW50B;;IAGhB,MAAM+5B,IAAMp3B,iBAAiBsd;IAC7B1d,gBAAgB0d,GAAsB;MAEpCzgB,GAAGu6B,EAAIv6B,IAAIqD,EAAOrD,IAAI,MAAMo1B,EAAW70B;MACvCN,GAAGs6B,EAAIt6B,IAAIoD,EAAOpD,IAAI,MAAMm1B,EAAW50B;;IAGzC,MAAM8C,IAAOH,iBAAiBsd;IAC9B1d,gBAAgB0d,GAAsByZ;IACtC1sC,GAAI,MAAM4sC;IAEV,OAAO;MACL/2B;MACAC;;AACD;EAEH,MAAMk3B,oBAAoBA,CACxB5pB,GACA6pB;IAEA,MAAMC,IAAahuC,EAAIksB,mBAAmB,MAAM,IAAI,IAAI;IACxD,MAAM7U,IAAS;MACbxD,GAAGs4B,EAAKjoB,EAAmBrQ,IAAIk6B,EAAmBl6B;MAClDC,GAAGq4B,EAAKjoB,EAAmBpQ,IAAIi6B,EAAmBj6B;;IAGpD,OAAO;MACLD,GAAGwD,EAAOxD,IAAIm6B,IAAa32B,EAAOxD,IAAI;MACtCC,GAAGuD,EAAOvD,IAAIk6B,IAAa32B,EAAOvD,IAAI;;AACvC;EAEH,OAAOm6B,GAAoBC,KAA0BzvC,YACnDquC,GACAjlC,KAAKyM,mBAAmByM;EAE1B,OAAOotB,GAA+BC,KAAqC3vC,YAEzEquC,GAAgBjlC,KAAKwM,GAAe0M;EACtC,OAAOstB,GAA2BC,KAChC7vC,YAAwBquC;EAC1B,OAAOyB,KAA0B9vC,YAAkCsuC;EACnE,OAAOyB,GAAoBC,KAA+BhwC,YAAwBquC;EAClF,OAAO4B,KAA4BjwC,YAAwCsuC;EAC3E,OAAO4B,KAA6BlwC,YAClC;IACEM,GAAQA,CAAC6vC,GAASC,MAAY3nC,MAAM0nC,GAASC,GAAShC;IACtDhuC,GAAe,CAAE;MAEnB,MAAO6V,cAAcqM,KAAa5O,UAAU4O,GAAW8rB,KAAyB,CAAA;EAElF,OAAOiC,GAAiCC,KACtCtwC,YAA+B;IAC7BM,GAAQA,CAAC6vC,GAASpvC,MAChBkI,QAAQknC,EAAQj4B,GAAQnX,EAAOmX,MAAWjP,QAAQknC,EAAQh4B,GAAMpX,EAAOoX;IACzE/X,GAAe6X;;EAGnB,MAAMme,IAAyBpa,8BAC7B4F;EAGF,MAAM2uB,uCAAuCA,CAC3CvtB,GACApO;IAEA,MAAM47B,IAAS57B,IACXgI,IACAC;IACJ,OAAO,GAAG2zB,IAASroC,sBAAsB6a;AAAgB;EAE3D,MAAMytB,2BAA4BC;IAEhC,MAAMC,mCAAoC/7B,KACvC,EAAC5M,GAAYC,GAAWC,IAA+BoP,KAAK/D,KAC3Dg9B,qCAAqCh9B,GAAOqB;IAEhD,MAAMg8B,IAA6BD,iCAAiC,MACjEzqC,OAAOyqC,oCACPtiC,KAAK;IAERynB,EAAwB8a;IACxB9a,EACGlwB,KAAK8qC,GACHp5B,KAAK4kB,KACJqU,qCAAqCG,EAAsBxU,IAAOA,MAAS,OAE5E7tB,KAAK,MACR;AACD;EAGH,OAAO,EACHwqB,OAAc+N,OAAuByF,OAAiBtT,SACtDwU;IAEF,OAAMX,IAAE5Z,GAAY6Z,IAAE/U,GAAgBwP,IAAEhQ,GAAiBiQ,IAAEnV,GAAOye,IAAEjY,KAClEgO,KAAyB,CAAA;IAC3B,MAAMkK,IACJ1a,KACAA,EAAuBtU,EACrBC,GACAC,GACAqqB,GACA/rB,GACAuY;IAGJ,OAAMkY,GAAEvrB,GAAgBwrB,GAAE7qB,GAAoB8qB,GAAE3sB,KAC9CwsB,KAA6C,CAAA;IAE/C,OAAOluB,GAA8BlC,KACnCN,gCAAgCyY,GAAcvY;IAChD,OAAO2I,GAAUgf,KAAmBpP,EAAa;IACjD,MAAMqY,IAAmBvwB,kBAAkBsI,EAASpU;IACpD,MAAMs8B,IAAmBxwB,kBAAkBsI,EAASnU;IAEpD,MAAMs8B,IACJ;IAQF,IAAIC,IAAoB5B,EAAuB1W;IAC/C,IAAIuY,IAA0B3B,EAAkC5W;IAChE,IAAIwY,IAAqB1B,EAA8B9W;IACvD,IAAIyY,KAAoBxB,EAA4BjX;IAEpD,IAAIrY,KAAuC8B;MACzCsT,EAAwB7Y,IAAmC2F;;IAGxC;MACnB,IAAIxT,aAAagmB,GAAO3Y,GAAmBF;QACzCgyB,iBAAiB;;MAGnB,OAAO3X,KAAuBzQ,IAAuBA,MAAyB;MAE9E,OAAOT,KAAiB2rB,IAAoB7B,EAAmBzW;MAC/D,OAAOtT,KAAuB6rB,IAC5B5B,EAA8B3W;MAChC,MAAMuW,IAAqB35B,EAAc2M;MACzC,MAAMmvB,IAAkBnb,KAAwB9gB,cAAc41B;MAC9D,MAAMsG,IAA2B;QAC/Bt8B,GAAGs4B,EAAKjoB,EAAmBrQ,IAAIsQ,EAAatQ;QAC5CC,GAAGq4B,EAAKjoB,EAAmBpQ,IAAIqQ,EAAarQ;;MAG9C,MAAMs8B,IAA2B;QAC/Bv8B,GAAGs4B,GACA+D,IACGA,EAAgBr8B,IAChBk6B,EAAmBl6B,IAAIs4B,EAAK4B,EAAmBl6B,IAAIqQ,EAAmBrQ,MACxEsQ,EAAatQ;QAEjBC,GAAGq4B,GACA+D,IACGA,EAAgBp8B,IAChBi6B,EAAmBj6B,IAAIq4B,EAAK4B,EAAmBj6B,IAAIoQ,EAAmBpQ,MACxEqQ,EAAarQ;;MAInBuhB,KAAuBA;MAEvB4a,KAAoBzB,EAAmB4B;MACvCJ,IAAqB3B,EACnBP,kBAAkBqC,GAA0BC,IAC5C5Y;AAEJ;IAEA,OAAO6Y,IAAcC,MAAuBL;IAC5C,OAAOl5B,IAAgBw5B,MAAyBP;IAChD,OAAO9rB,IAAoBssB,MAA6BT;IACxD,OAAO5rB,IAAcssB,MAAuBX;IAC5C,OAAOvwB,IAAamxB,MAAsBnC,EAAuB;MAC/Dj7B,GAAGyD,GAAelD,IAAI;MACtBN,GAAGwD,GAAejD,IAAI;;IAExB,MAAM68B,KACHhB,KAAoBC,MAAqBrwB,GAAYjM,KAAKiM,GAAYhM,MACtEo8B,KAAoBpwB,GAAYjM,MAAMiM,GAAYhM,KAClDq8B,KAAoBrwB,GAAYhM,MAAMgM,GAAYjM;IACrD,MAAMs9B,KACJ5E,KACAjW,KACAsB,KACAoZ,MACAD,MACAF,MACAC,MACA7J,KACAvnB,KACA0wB;IACF,MAAM7tB,KAAwB1C,4BAA4BC,IAAamI;IACvE,OAAOjG,IAAeovB,MAAwBnC,EAC5C1sB,GAAsB7B;IAExB,OAAOgtB,IAAqB2D,MAA8BnC,EAA0BnX;IAEpF,MAAMuZ,KACJhb,KAAqBlF,KAAWigB,MAA8BJ,MAAsBlZ;IACtF,OAAOwZ,IAAmBC,MAA4BF,KAClDjC,EAAgC5B,6BAA6BC,KAAsB3V,KACnFuX;IAEJ,IAAI6B,IAAqB;MACvBC,MAAwB3B,yBAAyBltB,GAAsB7B;MAEvE,IAAI4C,KAAyBkB;QAC3BrS,UACEmP,GACAgC,EACEf,IACA8oB,GACA7mB,EAAiBjC,IAAuBkC,IAAoBC;;AAIpE;IAEA6oB,iBAAiB;IAEjBr/B,mBAAmBkmB,GAAO3Y,GAAmBF,GAAqB21B;IAClEhjC,mBAAmBg8B,GAAU/tB,GAAsBZ,GAAqB21B;IAExEtmC,WAAWoW,GAAqB;MAC9BN,GAAgBsB;MAChBsa,IAAe;QACbzoB,GAAG+8B,GAAax8B;QAChBN,GAAG88B,GAAav8B;;MAElB+nB,IAAiB;QACfvoB,GAAGyD,GAAelD;QAClBN,GAAGwD,GAAejD;;MAEpBqyB,IAAc5mB;MACd4b,IAAoBtkB,0BAA0Bm6B,IAAmBj6B;;IAGnE,OAAO;MACL6uB,IAAuBiL;MACvBrL,IAAsB8K;MACtB5K,IAAwB6K;MACxBzK,IAA2BmL,MAA4BV;MACvDW,IAAWH;;AACZ;AACF;;ACjVI,MAAMI,uBAAwB7mC;EACnC,OAAOogB,GAAU0mB,GAAyBC,KAAYlK,6BAA6B78B;EACnF,MAAMqqB,IAA6B;IACjCgV,IAAU;MACR12B,GAAG;MACHC,GAAG;MACHrO,GAAG;MACHsO,GAAG;;IAEL44B,IAAkB;IAClBroB,GAAuB;MACrBvd,CAACA,IAAiB;MAClBC,CAACA,IAAkB;MACnBF,CAACA,IAAgB;MACjBJ,CAACA,IAAgB;MACjBC,CAACA,IAAkB;MACnBE,CAACA,IAAmB;MACpBD,CAACA,IAAiB;;IAEpB+1B,IAAe;MAAEzoB,GAAG;MAAGC,GAAG;;IAC1BsoB,IAAiB;MAAEvoB,GAAG;MAAGC,GAAG;;IAC5B4M,GAAgB;MACd7M,GAAG5M;MACH6M,GAAG7M;;IAELy/B,IAAc;MACZ7yB,GAAG;MACHC,GAAG;;IAEL4nB,IAAoBzkB;;EAEtB,OAAMgd,IAAEC,GAAOG,IAAEC,GAAoBnT,GAAEC,GAAiB2T,IAAEC,KACxD/J;EACF,OAAM1J,GAAEC,GAAuBjC,GAAEC,KAA8BoN;EAC/D,MAAMjL,KACHH,MAA4BhC,EAA0B3L,KAAK2L,EAA0B1L;EAExF,MAAM+9B,IAA2C,EAC/CzG,2BAA2BngB,IAC3BugB,2BAA2BvgB,GAAUiK,IACrCsX,4BAA4BvhB,GAAUiK;EAGxC,OAAO,EACLyc,GACCG;IACC,MAAM1b,IAAyC,CAAA;IAC/C,MAAM2b,IAAqBpwB;IAC3B,MAAM8T,IAAesc,KAAsB/6B,iBAAiBsd;IAC5D,MAAMoB,IAA8BD,KAAgBT;IAEpDvwB,KAAKotC,IAAiBG;MACpBpnC,WAAWwrB,GAAa4b,EAAcF,GAAY1b,MAAgB,CAAA;AAAG;IAGvExf,gBAAgB0d,GAAsBmB;IACtCC,KAA+BA;KAC9BtU,KAAqBxK,gBAAgBsd,GAAS;IAE/C,OAAOkC;AAAW,KAEpBlB,GACAjK,GACA2mB;AACD;;AC/DI,MAAMK,eAAeA,CAC1BpnC,GACA5L,GACAizC,GACAC,GACAlzB;EAEA,IAAImzB,IAA6B;EACjC,MAAM7e,IAAmBnK,kBAAkBnqB,GAAS,CAAE;EACtD,OACEozC,GACAC,GACAtxB,GACAD,GACAwxB,KACEb,qBAAqB7mC;EACzB,OAAO2nC,GAAsBC,GAAsBxxB,KAAuBqS,qBACxEvS,GACAC,GACAuS,IACCmf;IAECxzC,OAAO,CAAA,GAAIwzC;AAAqB;EAGpC,OAAOC,GAAuBC,GAAyBC,EAAAA,KACrDjP,sBACE/4B,GACA5L,GACAgiB,GACAD,GACAD,GACA9B;EAGJ,MAAM6zB,uBAAwBC,KAC5BnuC,KAAKmuC,GAAOnF,MAAM/oC,OAAUkuC,EAAMluC;EAEpC,MAAM3F,SAASA,CACb4yC,GACAkB;IAEA,IAAId;MACF,OAAO;;IAGT,OACEe,IAAiBC,GACjBnb,IAAQob,GAAQC,IAChBtb,GAAYub,IACZC,KACExB;IAEJ,MAAMmB,IAAkBC,KAAqB;IAC7C,MAAMnb,MAAWob,MAAaf;IAC9B,MAAMmB,IAAqC;MACzC1b,IAAczO,kBAAkBnqB,GAASg0C,GAAiBlb;MAC1Dkb;MACAlb;;IAGF,IAAIub,GAAiB;MACnBV,EAAsBW;MACtB,OAAO;AACT;IAEA,MAAMC,IACJR,KACAP,EACE7nC,WAAW,CAAA,GAAI2oC,GAAmB;MAChCzb;;IAIN,MAAM2b,IAAiBnB,EACrB1nC,WAAW,CAAA,GAAI2oC,GAAmB;MAChClI,IAAiBpqB;MACjB2kB,IAAuB4N;;IAI3BZ,EACEhoC,WAAW,CAAE,GAAE2oC,GAAmB;MAChC3N,IAAuB4N;MACvB3N,IAAuB4N;;IAI3B,MAAMC,IAAuBZ,qBAAqBU;IAClD,MAAMG,IAAuBb,qBAAqBW;IAClD,MAAMzzC,IACJ0zC,KAAwBC,MAAyBhoC,cAAcsnC,MAAoBlb;IAErFqa,IAA6B;IAE7BpyC,KACEmyC,EAAUL,GAAY;MACpBlM,IAAuB4N;MACvB3N,IAAuB4N;;IAG3B,OAAOzzC;AAAO;EAGhB,OAAO,EACL;IACE,OAAM4zC,IAAEzJ,GAA4B9V,IAAEC,GAAoBS,IAAEC,KAC1DjU;IACF,MAAM8yB,IAAgB78B,iBAAiBmzB;IACvC,MAAMhrB,IAAa,EAACqzB,KAAwBH,KAAwBM;IACpE,MAAMjd,IAA8BV;IAEpCpe,gBAAgB0d,GAAsBuf;IACtCne;IAEA,OAAOttB,KAAKpC,iBAAiBmZ;AAAW,KAE1CjgB,QACA,OAAO;IACL40C,IAAsB7yB;IACtB8yB,IAAsB/yB;MAExB;IACEgzB,IAAyBjzB;IACzBkzB,IAA0BpB;KAE5BN;AACD;;ACzMH,MAAM2B,KAAyD,IAAIlnB;;AAO5D,MAAMmnB,cAAcA,CAACtpC,GAAiB+P;EAC3Cs5B,GAAkBv6B,IAAI9O,GAAQ+P;AAAW;;AAOpC,MAAMw5B,iBAAkBvpC;EAC7BqpC,GAAkBzmB,OAAO5iB;AAAO;;AAO3B,MAAMwpC,cAAexpC,KAC1BqpC,GAAkB76B,IAAIxO;;AC+RjB,MAAMypC,oBAA6CA,CACxDzpC,GACA5L,GACAs1C;EAEA,OAAMC,IAAExoB,KAAuBY;EAC/B,MAAM6nB,IAAkBvxC,cAAc2H;EACtC,MAAM6pC,IAAiBD,IAAkB5pC,IAASA,EAAOA;EACzD,MAAM8pC,IAAoBN,YAAYK;EACtC,IAAIz1C,MAAY01C,GAAmB;IACjC,IAAI5nB,IAAY;IAChB,MAAM5N,IAA6B;IACnC,MAAMy1B,IAAsE,CAAA;IAC5E,MAAMC,kBAAmBhsB;MACvB,MAAMisB,IAA6BrpC,0BAA0Bod;MAC7D,MAAMksB,IAAiB/5B,8BACrBE;MAEF,OAAO65B,IACHA,EAAeD,GAA4B,QAC3CA;AAA0B;IAEhC,MAAME,IAAkCpqC,WACtC,CAAE,GACFohB,KACA6oB,gBAAgB51C;IAElB,OAAOg2C,GAAgBC,GAAoBC,KACzCp8B;IACF,OAAOq8B,GAAkBC,GAAsBC,KAC7Cv8B,uBAAuBw7B;IACzB,MAAMx6B,eAA0CA,CAACnY,GAAMqE;MACrDqvC,EAAqB1zC,GAAMqE;MAC3BkvC,EAAmBvzC,GAAMqE;AAAK;IAEhC,OAAOsvC,GAAiBC,GAAcC,GAAaC,GAAgBC,KACjE1D,aACEpnC,GACAmqC,IACA,MAAMjoB,KACN,EAAGkmB,OAAiBlb,SAAY6N,OAAuBC;MACrD,OAAM+F,IACJ5Z,GAAYsU,IACZhQ,GAAiBgV,IACjB7U,GAAuBoV,IACvB/U,GAAgB8e,IAChB5e,GAAauP,IACbnV,KACEwU;MAEJ,OAAME,IACJC,GAAoBC,IACpBC,GAAsBC,IACtBC,GAAqBC,IACrBC,KACER;MAEJ9rB,aAAa,WAAW,EAEtBY,GACA;QACEyb,aAAa;UACXyf,eAAe7jB;UACf8jB,oBAAoBxf;UACpBrD,0BAA0BwD;UAC1Boa,uBAAuB9K;UACvB+K,yBAAyB7K;UACzBmL,wBAAwBjL;UACxBqL,4BAA4BnL;UAC5B0F,mBAAmBjV;UACnBif,gBAAgB/e;UAChBhY,UAAUoS;;QAEZ/H,gBAAgB4pB,KAAmB,CAAE;QACrCpzC,SAASk4B;;AAEX,SAGH7Y,KAAgBnF,aAAa,UAAU,EAACY,GAAUuE;IAGvD,MAAM+N,UAAW2kB;MACfwC,eAAeM;MACf1uC,gBAAgBmZ;MAEhB4N,IAAY;MAGZhT,aAAa,aAAa,EAACY,GAAUi3B;MACrCsD;MACAG;AAAsB;IAGxB,MAAM16B,IAA8B;MAClC1b,OAAAA,CAAQ4pB,GAA6BmtB;QACnC,IAAIntB,GAAY;UACd,MAAMotB,IAAOD,IAAOhqB,MAAuB,CAAA;UAC3C,MAAM3C,IAAiBV,eACrBqsB,GACApqC,WAAWqrC,GAAMpB,gBAAgBhsB;UAEnC,KAAKld,cAAc0d,IAAiB;YAClCze,WAAWoqC,GAAgB3rB;YAC3BmsB,EAAa;cAAEvC,IAAiB5pB;;AAClC;AACF;QACA,OAAOze,WAAW,IAAIoqC;AACvB;MACDkB,IAAId;MACJe,KAAKA,CAACv0C,GAAM+T;QACV/T,KAAQ+T,KAAY0/B,EAAqBzzC,GAAM+T;AAAS;MAE1Duf,KAAAA;QACE,OAAMkhB,IAAEtC,GAAoBuC,IAAEtC,KAAyB0B;QACvD,OAAM9wB,GAAEpB,KAAoBuwB;QAC5B,OAAMzX,IACJC,GAAaH,IACbC,GAAeoK,GACf9lB,GAAc+lB,IACdC,GAAY4D,IACZJ,GAAQoM,IACRhK,GAAgB7Q,IAChBC,KACEqY;QACJ,OAAOnpC,WACL,CAAA,GACA;UACEgmC,cAActU;UACdhlB,gBAAgB8kB;UAChBpa,eAAetB;UACfZ,aAAa4mB;UACb6K,mBAAmB;YACjB75B,OAAOgkB,EAAmBxkB;YAC1BS,KAAK+jB,EAAmBvkB;;UAE1BgU,SAAS+e;UACTriB,iBAAiBykB;UACjBiK,cAAchzB;UACdwJ;;AAGL;MACD9B,QAAAA;QACE,OAAMgJ,IACJC,GAAOC,IACPC,GAAKkW,IACLJ,GAAQ7oB,GACRC,GAASiT,IACTC,GAAQH,IACRC,GAAoByQ,IACpBC,KACE0Q,EAAe1B;QACnB,OAAMwC,IAAEpX,GAAWqX,IAAEjX,KAAckW,EAAezB;QAClD,MAAMyC,8BACJ7Z;UAEA,OAAMiD,IAAE/D,GAAO8D,IAAElB,GAAMV,IAAEnB,KAAeD;UACxC,OAAO;YACL2B,WAAW1B;YACX2B,OAAOE;YACPD,QAAQ3C;;AACT;QAEH,MAAM4a,kCACJC;UAEA,OAAMC,IAAExX,GAAoByX,IAAExX,KAAWsX;UACzC,MAAMG,IAAsBL,4BAA4BrX,EAAqB;UAE7E,OAAOz0B,WAAW,CAAE,GAAEmsC,GAAqB;YACzCvrC,OAAOA;cACL,MAAM5D,IAAS8uC,4BAA4BpX;cAC3CkW,EAAa;gBAAElC,IAAiB;;cAChC,OAAO1rC;AAAM;;AAEf;QAEJ,OAAOgD,WACL,CAAA,GACA;UACEC,QAAQqpB;UACRhJ,MAAMkJ;UACNjJ,SAAS+e,KAAY5oB;UACrB8J,UAAU9J;UACV+J,SAASmJ,KAAYlT;UACrByoB,qBAAqBzV;UACrB0V,oBAAoBhF;UACpBgS,qBAAqBL,gCAAgCvX;UACrD6X,mBAAmBN,gCAAgCnX;;AAGxD;MACDtgC,QAAS64B,KAAqByd,EAAa;QAAEzd;QAAQD,IAAc;;MACnE7K,SAAS7kB,KAAK6kB,SAAS;MACvB5S,QAAmCA,KACjCu6B,EAA8BhwC,KAAKyV,GAAQ;;IAK/ChV,KAAK8Z,GAAY,EAACw2B;IAGlBxB,YAAYO,GAAgB/5B;IAG5BL,8BAA8BL,GAAeq6B,mBAAmB,EAC9D35B,GACAs6B,GACAL;IAGF,IACEpb,qBACEkc,EAAe1B,GAAwBtf,KACtC+f,KAAmB5pC,EAAO0gB,SAE7B;MACA0B,QAAQ;MACR,OAAOtS;AACT;IAEAtV,KAAK8Z,GAAYo2B;IAEjBx7B,aAAa,eAAe,EAACY;IAE7BA,EAASzb;IAET,OAAOyb;AACT;EACA,OAAOg6B;AAAkB;;AAG3BL,kBAAkBj6B,SAAU68B;EAC1B,MAAMC,IAAQ10C,QAAQy0C;EACtB,MAAME,IACJD,IAAQD,IAAU,EAACA;EACrB,MAAMtvC,IAASwvC,EAAa9gC,KACzB+D,KAAWC,8BAA8BD,GAAQi6B,mBAAmB;EAEvEn6B,WAAWi9B;EACX,OAAOD,IAAQvvC,IAAUA,EAAO;AAAU;;AAE5C0sC,kBAAkB+C,QAASz8B;EACzB,MAAM08B,IAAY18B,KAAeA,EAAiCqQ;EAClE,MAAMA,IAAWzoB,WAAW80C,MAAcA;EAC1C,OAAOv0C,cAAckoB,QAAeopB,YAAYppB,EAASpgB;AAAO;;AAElEypC,kBAAkBh1B,MAAM;EACtB,OAAMmC,GACJC,GAAqBnC,GACrBC,GAAyB+B,GACzBC,GAAuB+1B,GACvB5rB,GAAe6rB,IACfrrB,GAA4BsrB,IAC5BrrB,GAAqBuN,GACrB9N,GAAyB6rB,IACzB5rB,GAAyB0oB,IACzBxoB,GAAkB2rB,IAClB1rB,KACEW;EACJ,OAAOhiB,WACL,CAAA,GACA;IACEgtC,gBAAgBl2B;IAChBm2B,oBAAoBr4B;IACpBs4B,kBAAkBt2B;IAClBya,gBAAgBtQ;IAChBX,6BAA6BmB;IAC7BX,sBAAsBY;IAEtBV,0BAA0BG;IAC1BksB,0BAA0BjsB;IAC1BL,mBAAmBO;IACnBgsB,mBAAmB/rB;;AAEtB;;AAEHqoB,kBAAkBhrB,QAAQE;;AAC1B8qB,kBAAkB7jC,oBAAoBE;;"}