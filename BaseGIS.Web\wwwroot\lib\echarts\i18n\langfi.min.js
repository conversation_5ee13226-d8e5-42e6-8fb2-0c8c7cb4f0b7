!function(a,t){"function"==typeof define&&define.amd?define(["exports","echarts"],t):"object"==typeof exports&&"string"!=typeof exports.nodeName?t(0,require("echarts/lib/echarts")):t(0,a.echarts)}(this,function(a,t){t.registerLocale("FI",{time:{month:["tammikuuta","helmikuuta","maaliskuuta","huhtikuuta","toukokuuta","kesäkuuta","heinäkuuta","elokuuta","syyskuuta","lokakuuta","marraskuuta","joulukuuta"],monthAbbr:["tammik","helmik","maalisk","huhtik","toukok","kesäk","heinäk","elok","syysk","lokak","marrask","jouluk"],dayOfWeek:["sunnunta<PERSON>","maana<PERSON><PERSON>","tii<PERSON><PERSON>","keski<PERSON><PERSON><PERSON>a","torstaina","perjantaina","lauantaina"],dayOfWeekAbbr:["su","ma","ti","ke","to","pe","la"]},legend:{selector:{all:"Kaikki",inverse:"Käänteinen"}},toolbox:{brush:{title:{rect:"Laatikko valinta",polygon:"Lasso valinta",lineX:"Vaakataso valinta",lineY:"Pysty valinta",keep:"Pidä valinta",clear:"Poista valinta"}},dataView:{title:"Data näkymä",lang:["Data näkymä","Sulje","Päivitä"]},dataZoom:{title:{zoom:"Zoomaa",back:"Zoomin nollaus"}},magicType:{title:{line:"Vaihda Viivakaavioon",bar:"Vaihda palkkikaavioon",stack:"Pinoa",tiled:"Erottele"}},restore:{title:"Palauta"},saveAsImage:{title:"Tallenna kuvana",lang:["Paina oikeaa hiirennappia tallentaaksesi kuva"]}}})});