﻿using BaseGIS.Core.Entities;
using BaseGIS.Infrastructure.Persistence;
using BaseGIS.Web.Hubs;
using BaseGIS.Web.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;
using NetTopologySuite.IO;
using System.Text.Json;

namespace BaseGIS.Web.Controllers
{

    public class FeatureController : Controller
    {
        private readonly ApplicationDbContext _db;
        private readonly TileGenerationBackgroundService _tileGenService;
        private readonly IHubContext<FeaturesHub> _hubContext;
        private readonly ILogger<FeatureController> _logger;

        public FeatureController(ApplicationDbContext db, TileGenerationBackgroundService tileGenService, IHubContext<FeaturesHub> hubContext, ILogger<FeatureController> logger)
        {
            _db = db;
            _tileGenService = tileGenService;
            _hubContext = hubContext;
            _logger = logger;
        }

        // محاسبه تایل‌های تحت تأثیر
        private List<(int z, int x, int y)> GetAffectedTiles(Geometry geometry, int minZoom = 10, int maxZoom = 18)
        {
            var tiles = new List<(int z, int x, int y)>();
            var envelope = geometry.EnvelopeInternal;

            for (int z = minZoom; z <= maxZoom; z++)
            {
                var (minX, minY, maxX, maxY) = GetTileBounds(envelope, z);
                for (int x = minX; x <= maxX; x++)
                {
                    for (int y = minY; y <= maxY; y++)
                    {
                        tiles.Add((z, x, y));
                    }
                }
            }

            return tiles;
        }

        // تبدیل محدوده به مختصات تایل
        private (int minX, int minY, int maxX, int maxY) GetTileBounds(Envelope envelope, int zoom)
        {
            var minTile = LatLonToTile(envelope.MinY, envelope.MinX, zoom);
            var maxTile = LatLonToTile(envelope.MaxY, envelope.MaxX, zoom);
            return (
                Math.Min(minTile.x, maxTile.x),
                Math.Min(minTile.y, maxTile.y),
                Math.Max(minTile.x, maxTile.x),
                Math.Max(minTile.y, maxTile.y)
            );
        }

        // تبدیل مختصات جغرافیایی به مختصات تایل
        private (int x, int y) LatLonToTile(double lat, double lon, int zoom)
        {
            int n = 1 << zoom;
            double latRad = lat * Math.PI / 180.0;
            double x = (lon + 180.0) / 360.0 * n;
            double y = (1.0 - Math.Log(Math.Tan(latRad) + 1.0 / Math.Cos(latRad)) / Math.PI) / 2.0 * n;
            return ((int)x, (int)y);
        }

        // GET: Feature/GetFeatures/{tableInfoId}
        [HttpGet]
        public async Task<IActionResult> GetFeatures(int tableInfoId)
        {
            try
            {
                return Json(new { success = true, data = new { } });
                var tableInfo = await _db.TableInfos.FindAsync(tableInfoId);
                if (tableInfo == null)
                {
                    _logger.LogWarning("GetFeatures: TableInfo with ID {TableInfoId} not found.", tableInfoId);
                    return Json(new { success = false, message = "TableInfo not found." });
                }

                var sql = $"SELECT Id, [geom].STAsText() as geom, Properties FROM [{tableInfo.Name}]";
                var features = new List<object>();
                using (var cmd = _db.Database.GetDbConnection().CreateCommand())
                {
                    cmd.CommandText = sql;
                    _db.Database.OpenConnection();
                    using (var reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var properties = reader["Properties"] != DBNull.Value
                                ? JsonSerializer.Deserialize<Dictionary<string, object>>(reader["Properties"].ToString())
                                : new Dictionary<string, object>();
                            features.Add(new
                            {
                                Id = reader.GetInt32(reader.GetOrdinal("Id")),
                                Geometry = reader.GetString(reader.GetOrdinal("geom")),
                                Properties = properties
                            });
                        }
                    }
                    _db.Database.CloseConnection();
                }

                _logger.LogInformation("Successfully retrieved features for TableInfo ID {TableInfoId}.", tableInfoId);
                return Json(new { success = true, data = features });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving features for TableInfo ID {TableInfoId}.", tableInfoId);
                return Json(new { success = false, message = "An error occurred while retrieving features." });
            }
        }

        // GET: Feature/GetFeature/{tableInfoId}/{featureId}
        [HttpGet]
        public async Task<IActionResult> GetFeature(int tableInfoId, int featureId)
        {
            try
            {
                var tableInfo = await _db.TableInfos.FindAsync(tableInfoId);
                if (tableInfo == null)
                {
                    _logger.LogWarning("GetFeature: TableInfo with ID {TableInfoId} not found.", tableInfoId);
                    return Json(new { success = false, message = "TableInfo not found." });
                }

                var sql = $"SELECT Id, [geom].STAsText() as geom, Properties FROM [{tableInfo.Name}] WHERE Id = @id";
                using (var cmd = _db.Database.GetDbConnection().CreateCommand())
                {
                    cmd.CommandText = sql;
                    var param = cmd.CreateParameter();
                    param.ParameterName = "@id";
                    param.Value = featureId;
                    cmd.Parameters.Add(param);
                    _db.Database.OpenConnection();
                    using (var reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var properties = reader["Properties"] != DBNull.Value
                                ? JsonSerializer.Deserialize<Dictionary<string, object>>(reader["Properties"].ToString())
                                : new Dictionary<string, object>();
                            var feature = new
                            {
                                Id = reader.GetInt32(reader.GetOrdinal("Id")),
                                Geometry = reader.GetString(reader.GetOrdinal("geom")),
                                Properties = properties
                            };
                            _logger.LogInformation("Successfully retrieved feature {FeatureId} for TableInfo ID {TableInfoId}.", featureId, tableInfoId);
                            return Json(new { success = true, data = feature });
                        }
                    }
                    _db.Database.CloseConnection();
                }

                _logger.LogWarning("GetFeature: Feature with ID {FeatureId} not found in TableInfo ID {TableInfoId}.", featureId, tableInfoId);
                return Json(new { success = false, message = "Feature not found." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving feature {FeatureId} for TableInfo ID {TableInfoId}.", featureId, tableInfoId);
                return Json(new { success = false, message = "An error occurred while retrieving the feature." });
            }
        }

        // POST: Feature/AddFeature/{tableInfoId}
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddFeature(int tableInfoId, [FromBody] FeatureDto featureDto)
        {
            try
            {
                var tableInfo = await _db.TableInfos.FindAsync(tableInfoId);
                if (tableInfo == null)
                {
                    _logger.LogWarning("AddFeature: TableInfo with ID {TableInfoId} not found.", tableInfoId);
                    return Json(new { success = false, message = "TableInfo not found." });
                }

                // تبدیل GeoJSON به WKT و Geometry
                var geoJsonReader = new GeoJsonReader();
                var geometry = geoJsonReader.Read<Geometry>(featureDto.Geometry);
                var wktWriter = new WKTWriter();
                var wkt = wktWriter.Write(geometry);

                // درج فیچر
                var sql = $"INSERT INTO [{tableInfo.Name}] ([geom], [Properties]) VALUES (geometry::STGeomFromText(@wkt, 3857), @properties); SELECT SCOPE_IDENTITY();";
                int newId = 0;
                using (var cmd = _db.Database.GetDbConnection().CreateCommand())
                {
                    cmd.CommandText = sql;
                    var param1 = cmd.CreateParameter();
                    param1.ParameterName = "@wkt";
                    param1.Value = wkt;
                    cmd.Parameters.Add(param1);
                    var param2 = cmd.CreateParameter();
                    param2.ParameterName = "@properties";
                    param2.Value = JsonSerializer.Serialize(featureDto.Properties);
                    cmd.Parameters.Add(param2);
                    _db.Database.OpenConnection();
                    var result = cmd.ExecuteScalar();
                    if (result != null) newId = int.Parse(result.ToString());
                    _db.Database.CloseConnection();
                }

                // نسخه‌بندی
                int lastVersion = await _db.FeatureVersions
                    .Where(fv => fv.TableInfoId == tableInfoId && fv.FeatureId == newId)
                    .MaxAsync(fv => (int?)fv.VersionNumber) ?? 0;
                var fv = new FeatureVersion
                {
                    TableInfoId = tableInfoId,
                    FeatureId = newId,
                    GeometryWKT = wkt,
                    VersionNumber = lastVersion + 1,
                    ChangeType = "Add",
                    DateTime = DateTime.UtcNow
                };
                _db.FeatureVersions.Add(fv);
                await _db.SaveChangesAsync();

                // محاسبه تایل‌های تحت تأثیر
                var affectedTiles = GetAffectedTiles(geometry);
                foreach (var tile in affectedTiles)
                {
                    _tileGenService.Enqueue(new TileGenerationJob
                    {
                        TableInfoId = tableInfoId,
                        WKT = wkt,
                        Zoom = tile.z,
                        X = tile.x,
                        Y = tile.y
                    });
                }

                // اطلاع‌رسانی با SignalR
                await _hubContext.Clients.All.SendAsync("FeatureAdded", tableInfoId);

                _logger.LogInformation("Successfully added feature with ID {FeatureId} to TableInfo ID {TableInfoId}.", newId, tableInfoId);
                return Json(new { success = true, featureId = newId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding feature to TableInfo ID {TableInfoId}.", tableInfoId);
                return Json(new { success = false, message = "An error occurred while adding the feature." });
            }
        }

        // POST: Feature/UpdateFeature/{tableInfoId}/{featureId}
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateFeature(int tableInfoId, int featureId, [FromBody] FeatureDto featureDto)
        {
            try
            {
                var tableInfo = await _db.TableInfos.FindAsync(tableInfoId);
                if (tableInfo == null)
                {
                    _logger.LogWarning("UpdateFeature: TableInfo with ID {TableInfoId} not found.", tableInfoId);
                    return Json(new { success = false, message = "TableInfo not found." });
                }

                // دریافت هندسه قدیمی برای محاسبه تایل‌ها
                string? oldWkt = null;
                var getSql = $"SELECT [geom].STAsText() as geom FROM [{tableInfo.Name}] WHERE Id = @id";
                using (var getCmd = _db.Database.GetDbConnection().CreateCommand())
                {
                    getCmd.CommandText = getSql;
                    var param = getCmd.CreateParameter();
                    param.ParameterName = "@id";
                    param.Value = featureId;
                    getCmd.Parameters.Add(param);
                    _db.Database.OpenConnection();
                    using (var reader = getCmd.ExecuteReader())
                    {
                        if (reader.Read())
                            oldWkt = reader["geom"]?.ToString();
                    }
                    _db.Database.CloseConnection();
                }

                // تبدیل GeoJSON به WKT و Geometry
                var geoJsonReader = new GeoJsonReader();
                var newGeometry = geoJsonReader.Read<Geometry>(featureDto.Geometry);
                var wktWriter = new WKTWriter();
                var newWkt = wktWriter.Write(newGeometry);

                // آپدیت فیچر
                var sql = $"UPDATE [{tableInfo.Name}] SET [geom] = geometry::STGeomFromText(@wkt, 3857), [Properties] = @properties WHERE Id = @id";
                using (var cmd = _db.Database.GetDbConnection().CreateCommand())
                {
                    cmd.CommandText = sql;
                    var param1 = cmd.CreateParameter();
                    param1.ParameterName = "@wkt";
                    param1.Value = newWkt;
                    cmd.Parameters.Add(param1);
                    var param2 = cmd.CreateParameter();
                    param2.ParameterName = "@properties";
                    param2.Value = JsonSerializer.Serialize(featureDto.Properties);
                    cmd.Parameters.Add(param2);
                    var param3 = cmd.CreateParameter();
                    param3.ParameterName = "@id";
                    param3.Value = featureId;
                    cmd.Parameters.Add(param3);
                    _db.Database.OpenConnection();
                    var rows = cmd.ExecuteNonQuery();
                    _db.Database.CloseConnection();

                    if (rows > 0)
                    {
                        // نسخه‌بندی
                        int lastVersion = await _db.FeatureVersions
                            .Where(fv => fv.TableInfoId == tableInfoId && fv.FeatureId == featureId)
                            .MaxAsync(fv => (int?)fv.VersionNumber) ?? 0;
                        var fv = new FeatureVersion
                        {
                            TableInfoId = tableInfoId,
                            FeatureId = featureId,
                            GeometryWKT = newWkt,
                            VersionNumber = lastVersion + 1,
                            ChangeType = "Update",
                            DateTime = DateTime.UtcNow
                        };
                        _db.FeatureVersions.Add(fv);
                        await _db.SaveChangesAsync();

                        // محاسبه تایل‌های تحت تأثیر (قدیمی و جدید)
                        var affectedTiles = new List<(int z, int x, int y)>();
                        if (!string.IsNullOrEmpty(oldWkt))
                        {
                            var wktReader = new WKTReader();
                            var oldGeometry = wktReader.Read(oldWkt);
                            affectedTiles.AddRange(GetAffectedTiles(oldGeometry));
                        }
                        affectedTiles.AddRange(GetAffectedTiles(newGeometry));
                        affectedTiles = affectedTiles.Distinct().ToList();

                        foreach (var tile in affectedTiles)
                        {
                            _tileGenService.Enqueue(new TileGenerationJob
                            {
                                TableInfoId = tableInfoId,
                                WKT = newWkt,
                                Zoom = tile.z,
                                X = tile.x,
                                Y = tile.y
                            });
                        }

                        // اطلاع‌رسانی با SignalR
                        await _hubContext.Clients.All.SendAsync("FeatureUpdated", tableInfoId, featureId);

                        _logger.LogInformation("Successfully updated feature {FeatureId} in TableInfo ID {TableInfoId}.", featureId, tableInfoId);
                        return Json(new { success = true });
                    }
                }

                _logger.LogWarning("UpdateFeature: Feature with ID {FeatureId} not found in TableInfo ID {TableInfoId}.", featureId, tableInfoId);
                return Json(new { success = false, message = "Feature not found." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating feature {FeatureId} in TableInfo ID {TableInfoId}.", featureId, tableInfoId);
                return Json(new { success = false, message = "An error occurred while updating the feature." });
            }
        }

        // POST: Feature/DeleteFeature/{tableInfoId}/{featureId}
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteFeature(int tableInfoId, int featureId)
        {
            try
            {
                var tableInfo = await _db.TableInfos.FindAsync(tableInfoId);
                if (tableInfo == null)
                {
                    _logger.LogWarning("DeleteFeature: TableInfo with ID {TableInfoId} not found.", tableInfoId);
                    return Json(new { success = false, message = "TableInfo not found." });
                }

                // دریافت هندسه برای نسخه‌بندی و تایل‌ها
                string? lastWkt = null;
                var getSql = $"SELECT [geom].STAsText() as geom FROM [{tableInfo.Name}] WHERE Id = @id";
                using (var getCmd = _db.Database.GetDbConnection().CreateCommand())
                {
                    getCmd.CommandText = getSql;
                    var param = getCmd.CreateParameter();
                    param.ParameterName = "@id";
                    param.Value = featureId;
                    getCmd.Parameters.Add(param);
                    _db.Database.OpenConnection();
                    using (var reader = getCmd.ExecuteReader())
                    {
                        if (reader.Read())
                            lastWkt = reader["geom"]?.ToString();
                    }
                    _db.Database.CloseConnection();
                }

                // حذف فیچر
                var sql = $"DELETE FROM [{tableInfo.Name}] WHERE Id = @id";
                using (var cmd = _db.Database.GetDbConnection().CreateCommand())
                {
                    cmd.CommandText = sql;
                    var param = cmd.CreateParameter();
                    param.ParameterName = "@id";
                    param.Value = featureId;
                    cmd.Parameters.Add(param);
                    _db.Database.OpenConnection();
                    var rows = cmd.ExecuteNonQuery();
                    _db.Database.CloseConnection();

                    if (rows > 0)
                    {
                        // نسخه‌بندی
                        int lastVersion = await _db.FeatureVersions
                            .Where(fv => fv.TableInfoId == tableInfoId && fv.FeatureId == featureId)
                            .MaxAsync(fv => (int?)fv.VersionNumber) ?? 0;
                        var fv = new FeatureVersion
                        {
                            TableInfoId = tableInfoId,
                            FeatureId = featureId,
                            GeometryWKT = lastWkt ?? string.Empty,
                            VersionNumber = lastVersion + 1,
                            ChangeType = "Delete",
                            DateTime = DateTime.UtcNow
                        };
                        _db.FeatureVersions.Add(fv);
                        await _db.SaveChangesAsync();

                        // محاسبه تایل‌های تحت تأثیر
                        var affectedTiles = new List<(int z, int x, int y)>();
                        if (!string.IsNullOrEmpty(lastWkt))
                        {
                            var wktReader = new WKTReader();
                            var geometry = wktReader.Read(lastWkt);
                            affectedTiles = GetAffectedTiles(geometry);
                            foreach (var tile in affectedTiles)
                            {
                                _tileGenService.Enqueue(new TileGenerationJob
                                {
                                    TableInfoId = tableInfoId,
                                    WKT = lastWkt,
                                    Zoom = tile.z,
                                    X = tile.x,
                                    Y = tile.y
                                });
                            }
                        }

                        // اطلاع‌رسانی با SignalR
                        await _hubContext.Clients.All.SendAsync("FeatureDeleted", tableInfoId, featureId);

                        _logger.LogInformation("Successfully deleted feature {FeatureId} from TableInfo ID {TableInfoId}.", featureId, tableInfoId);
                        return Json(new { success = true });
                    }
                }

                _logger.LogWarning("DeleteFeature: Feature with ID {FeatureId} not found in TableInfo ID {TableInfoId}.", featureId, tableInfoId);
                return Json(new { success = false, message = "Feature not found." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting feature {FeatureId} from TableInfo ID {TableInfoId}.", featureId, tableInfoId);
                return Json(new { success = false, message = "An error occurred while deleting the feature." });
            }
        }

        // GET: Feature/Versions/{tableInfoId}/{featureId}
        [HttpGet]
        public async Task<IActionResult> Versions(int tableInfoId, int featureId)
        {
            try
            {
                var versions = await _db.FeatureVersions
                    .Where(fv => fv.TableInfoId == tableInfoId && fv.FeatureId == featureId)
                    .OrderBy(fv => fv.VersionNumber)
                    .Select(fv => new
                    {
                        fv.Id,
                        fv.VersionNumber,
                        fv.ChangeType,
                        fv.DateTime,
                        fv.GeometryWKT
                    })
                    .ToListAsync();

                _logger.LogInformation("Successfully retrieved feature versions for Feature ID {FeatureId} in TableInfo ID {TableInfoId}.", featureId, tableInfoId);
                return Json(new { success = true, data = versions });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving feature versions for Feature ID {FeatureId} in TableInfo ID {TableInfoId}.", featureId, tableInfoId);
                return Json(new { success = false, message = "An error occurred while retrieving feature versions." });
            }
        }

        // GET: Feature/Diff/{tableInfoId}/{featureId}
        [HttpGet]
        public async Task<IActionResult> Diff(int tableInfoId, int featureId, [FromQuery] int fromVersion, [FromQuery] int toVersion)
        {
            try
            {
                var from = await _db.FeatureVersions
                    .FirstOrDefaultAsync(fv => fv.TableInfoId == tableInfoId && fv.FeatureId == featureId && fv.VersionNumber == fromVersion);
                var to = await _db.FeatureVersions
                    .FirstOrDefaultAsync(fv => fv.TableInfoId == tableInfoId && fv.FeatureId == featureId && fv.VersionNumber == toVersion);
                if (from == null || to == null)
                {
                    _logger.LogWarning("Diff: One or both versions not found for Feature ID {FeatureId} in TableInfo ID {TableInfoId} (fromVersion: {FromVersion}, toVersion: {ToVersion}).", featureId, tableInfoId, fromVersion, toVersion);
                    return Json(new { success = false, message = "One or both versions not found." });
                }

                var reader = new WKTReader();
                Geometry? geomFrom = null, geomTo = null;
                try
                {
                    geomFrom = reader.Read(from.GeometryWKT);
                    geomTo = reader.Read(to.GeometryWKT);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Diff: Invalid WKT format in one of the versions for Feature ID {FeatureId} in TableInfo ID {TableInfoId}.", featureId, tableInfoId);
                    return Json(new { success = false, message = "Invalid WKT format in one of the versions." });
                }

                var diff = geomFrom.SymmetricDifference(geomTo);
                var writer = new WKTWriter();
                var diffWkt = writer.Write(diff);

                _logger.LogInformation("Successfully calculated feature diff for Feature ID {FeatureId} in TableInfo ID {TableInfoId} (fromVersion: {FromVersion}, toVersion: {ToVersion}).", featureId, tableInfoId, fromVersion, toVersion);
                return Json(new { success = true, data = new { DiffWkt = diffWkt } });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating feature diff for Feature ID {FeatureId} in TableInfo ID {TableInfoId}.", featureId, tableInfoId);
                return Json(new { success = false, message = "An error occurred while calculating the feature diff." });
            }
        }

        // POST: Feature/Patch/{tableInfoId}/{featureId}
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Patch(int tableInfoId, int featureId, [FromQuery] int baseVersion, [FromBody] FeatureDto featureDto)
        {
            try
            {
                // چک کردن نسخه پایه
                var baseVer = await _db.FeatureVersions
                    .FirstOrDefaultAsync(fv => fv.TableInfoId == tableInfoId && fv.FeatureId == featureId && fv.VersionNumber == baseVersion);
                if (baseVer == null)
                {
                    _logger.LogWarning("Patch: Base version {BaseVersion} not found for Feature ID {FeatureId} in TableInfo ID {TableInfoId}.", baseVersion, featureId, tableInfoId);
                    return Json(new { success = false, message = "Base version not found." });
                }

                var tableInfo = await _db.TableInfos.FindAsync(tableInfoId);
                if (tableInfo == null)
                {
                    _logger.LogWarning("Patch: TableInfo with ID {TableInfoId} not found.", tableInfoId);
                    return Json(new { success = false, message = "TableInfo not found." });
                }

                // دریافت هندسه قدیمی برای محاسبه تایل‌ها
                string? oldWkt = null;
                var getSql = $"SELECT [geom].STAsText() as geom FROM [{tableInfo.Name}] WHERE Id = @id";
                using (var getCmd = _db.Database.GetDbConnection().CreateCommand())
                {
                    getCmd.CommandText = getSql;
                    var param = getCmd.CreateParameter();
                    param.ParameterName = "@id";
                    param.Value = featureId;
                    getCmd.Parameters.Add(param);
                    _db.Database.OpenConnection();
                    using (var reader = getCmd.ExecuteReader())
                    {
                        if (reader.Read())
                            oldWkt = reader["geom"]?.ToString();
                    }
                    _db.Database.CloseConnection();
                }

                // تبدیل GeoJSON به WKT و Geometry
                var geoJsonReader = new GeoJsonReader();
                var newGeometry = geoJsonReader.Read<Geometry>(featureDto.Geometry);
                var wktWriter = new WKTWriter();
                var newWkt = wktWriter.Write(newGeometry);

                // آپدیت فیچر
                var sql = $"UPDATE [{tableInfo.Name}] SET [geom] = geometry::STGeomFromText(@wkt, 3857), [Properties] = @properties WHERE Id = @id";
                using (var cmd = _db.Database.GetDbConnection().CreateCommand())
                {
                    cmd.CommandText = sql;
                    var param1 = cmd.CreateParameter();
                    param1.ParameterName = "@wkt";
                    param1.Value = newWkt;
                    cmd.Parameters.Add(param1);
                    var param2 = cmd.CreateParameter();
                    param2.ParameterName = "@properties";
                    param2.Value = JsonSerializer.Serialize(featureDto.Properties);
                    cmd.Parameters.Add(param2);
                    var param3 = cmd.CreateParameter();
                    param3.ParameterName = "@id";
                    param3.Value = featureId;
                    cmd.Parameters.Add(param3);
                    _db.Database.OpenConnection();
                    var rows = cmd.ExecuteNonQuery();
                    _db.Database.CloseConnection();

                    if (rows > 0)
                    {
                        // نسخه‌بندی
                        int lastVersion = await _db.FeatureVersions
                            .Where(fv => fv.TableInfoId == tableInfoId && fv.FeatureId == featureId)
                            .MaxAsync(fv => (int?)fv.VersionNumber) ?? 0;
                        var fv = new FeatureVersion
                        {
                            TableInfoId = tableInfoId,
                            FeatureId = featureId,
                            GeometryWKT = newWkt,
                            VersionNumber = lastVersion + 1,
                            ChangeType = "Patch",
                            DateTime = DateTime.UtcNow
                        };
                        _db.FeatureVersions.Add(fv);
                        await _db.SaveChangesAsync();

                        // محاسبه تایل‌های تحت تأثیر (قدیمی و جدید)
                        var affectedTiles = new List<(int z, int x, int y)>();
                        if (!string.IsNullOrEmpty(oldWkt))
                        {
                            var wktReader = new WKTReader();
                            var oldGeometry = wktReader.Read(oldWkt);
                            affectedTiles.AddRange(GetAffectedTiles(oldGeometry));
                        }
                        affectedTiles.AddRange(GetAffectedTiles(newGeometry));
                        affectedTiles = affectedTiles.Distinct().ToList();

                        foreach (var tile in affectedTiles)
                        {
                            _tileGenService.Enqueue(new TileGenerationJob
                            {
                                TableInfoId = tableInfoId,
                                WKT = newWkt,
                                Zoom = tile.z,
                                X = tile.x,
                                Y = tile.y
                            });
                        }

                        // اطلاع‌رسانی با SignalR
                        await _hubContext.Clients.All.SendAsync("FeatureUpdated", tableInfoId, featureId);

                        _logger.LogInformation("Successfully patched feature {FeatureId} in TableInfo ID {TableInfoId} based on version {BaseVersion}.", featureId, tableInfoId, baseVersion);
                        return Json(new { success = true });
                    }
                }

                _logger.LogWarning("Patch: Feature with ID {FeatureId} not found in TableInfo ID {TableInfoId}.", featureId, tableInfoId);
                return Json(new { success = false, message = "Feature not found." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error patching feature {FeatureId} in TableInfo ID {TableInfoId}.", featureId, tableInfoId);
                return Json(new { success = false, message = "An error occurred while patching the feature." });
            }
        }


        [HttpGet]
        public async Task<IActionResult> GetSymbologies(int tableId)
        {
            try
            {
                var tableInfo = await _db.TableInfos.FindAsync(tableId);
                if (tableInfo == null)
                {
                    _logger.LogWarning("GetSymbologies: TableInfo with ID {TableInfoId} not found.", tableId);
                    return Json(new { success = false, message = "TableInfo not found." });
                }

                var symbologies = await _db.SymbologyInfos
                    .Where(s => s.TableInfoId == tableId)
                    .Select(s => new
                    {
                        s.Id,
                        s.Name,
                        s.Type,
                        s.IsDefault,
                        s.Json,
                        s.FieldName,
                        s.FieldAlias
                    })
                    .ToListAsync();

                _logger.LogInformation("Successfully retrieved symbologies for TableInfo ID {TableInfoId}.", tableId);
                return Json(new { success = true, data = symbologies });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving symbologies for TableInfo ID {TableInfoId}.", tableId);
                return Json(new { success = false, message = "An error occurred while retrieving symbologies." });
            }
        }

        public async Task<IActionResult> GetPropertyFields(int tableInfoId)
        {
            var tableInfo = await _db.TableInfos.FindAsync(tableInfoId);
            if (tableInfo == null) return Json(new { success = false });
            var sql = $"SELECT TOP 1 Properties FROM [{tableInfo.Name}] WHERE Properties IS NOT NULL";
            var properties = new List<string>();
            using (var cmd = _db.Database.GetDbConnection().CreateCommand())
            {
                cmd.CommandText = sql;
                _db.Database.OpenConnection();
                using (var reader = cmd.ExecuteReader())
                {
                    if (reader.Read() && reader["Properties"] != DBNull.Value)
                    {
                        var props = JsonSerializer.Deserialize<Dictionary<string, object>>(reader["Properties"].ToString());
                        properties = props.Keys.ToList();
                    }
                }
                _db.Database.CloseConnection();
            }
            return Json(new { success = true, data = properties });
        }

        [HttpGet]
        public async Task<IActionResult> GetGeometryType(int tableId)
        {
            try
            {
                var tableInfo = await _db.TableInfos.FirstOrDefaultAsync(t => t.Id == tableId);
                if (tableInfo == null)
                    return Json(new { success = false, message = "جدول یافت نشد." });

                // فرض می‌کنیم اولین فیچر نشان‌دهنده نوع هندسه جدول است
                using (var connection = new SqlConnection(_db.Database.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    using (var cmd = connection.CreateCommand())
                    {
                        cmd.CommandText = $@"SELECT TOP 1 [Shape].STGeometryType() as GeometryType FROM [{tableInfo.Name}]";
                        var geometryType = await cmd.ExecuteScalarAsync();
                        if (geometryType == null)
                            return Json(new { success = false, message = "هیچ داده‌ای در جدول یافت نشد." });

                        return Json(new { success = true, geometryType = geometryType.ToString() });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetGeometryType: Error retrieving geometry type for TableInfo ID {TableId}.", tableId);
                return Json(new { success = false, message = "خطا در دریافت نوع هندسه." });
            }
        }
    }

    public class FeatureDto
    {
        public string Geometry { get; set; } // GeoJSON
        public Dictionary<string, object> Properties { get; set; }
    }
}