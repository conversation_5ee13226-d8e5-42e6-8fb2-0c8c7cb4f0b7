using BaseGIS.Core.Entities;
using BaseGIS.Core.Interfaces;
using BaseGIS.Application.DTOs;

namespace BaseGIS.Application.UseCases
{
    public class CreateSymbologyInfoUseCase
    {
        private readonly ISymbologyInfoRepository _symbologyInfoRepository;
        private readonly ITableInfoRepository _tableInfoRepository;

        public CreateSymbologyInfoUseCase(ISymbologyInfoRepository symbologyInfoRepository, ITableInfoRepository tableInfoRepository)
        {
            _symbologyInfoRepository = symbologyInfoRepository;
            _tableInfoRepository = tableInfoRepository;
        }

        public async Task ExecuteAsync(SymbologyInfoDto dto)
        {
            // بررسی وجود TableInfo
            var tableInfo = await _tableInfoRepository.GetByIdAsync(dto.TableInfoId);
            if (tableInfo == null)
            {
                throw new InvalidOperationException("TableInfo یافت نشد.");
            }

            var symbologyInfo = new SymbologyInfo
            {
                Type = Enum.Parse<SymbologyType>(dto.Type),
                TableInfoId = dto.TableInfoId,
                Name = dto.Name,
                IsDefault = dto.IsDefault,
                Json = dto.Json,
                FieldAlias = dto.FieldAlias,
                FieldName = dto.FieldName
            };

            await _symbologyInfoRepository.AddAsync(symbologyInfo);
        }
    }
}