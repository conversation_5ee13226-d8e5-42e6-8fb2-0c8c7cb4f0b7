/**
 * Base Component Class
 * کلاس پایه برای تمام کامپوننت‌ها
 */

class BaseComponent {
    constructor(element, options = {}) {
        this.element = element;
        this.options = { ...this.getDefaultOptions(), ...options };
        this.isInitialized = false;
        this.eventListeners = new Map();
        
        this.init();
    }

    /**
     * تنظیمات پیش‌فرض
     */
    getDefaultOptions() {
        return {
            autoInit: true,
            destroyOnRemove: true,
            debug: false
        };
    }

    /**
     * مقداردهی اولیه
     */
    init() {
        if (this.isInitialized) return;
        
        try {
            this.beforeInit();
            this.render();
            this.bindEvents();
            this.afterInit();
            
            this.isInitialized = true;
            this.trigger('initialized');
            
            if (this.options.debug) {
                console.log(`Component ${this.constructor.name} initialized`, this);
            }
        } catch (error) {
            console.error(`Error initializing component ${this.constructor.name}:`, error);
            this.trigger('error', { error });
        }
    }

    /**
     * قبل از مقداردهی
     */
    beforeInit() {
        // Override in child classes
    }

    /**
     * رندر کامپوننت
     */
    render() {
        // Override in child classes
    }

    /**
     * اتصال رویدادها
     */
    bindEvents() {
        // Override in child classes
    }

    /**
     * بعد از مقداردهی
     */
    afterInit() {
        // Override in child classes
    }

    /**
     * اضافه کردن Event Listener
     */
    on(event, handler) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(handler);
        return this;
    }

    /**
     * حذف Event Listener
     */
    off(event, handler) {
        if (!this.eventListeners.has(event)) return this;
        
        const handlers = this.eventListeners.get(event);
        const index = handlers.indexOf(handler);
        if (index > -1) {
            handlers.splice(index, 1);
        }
        
        return this;
    }

    /**
     * اجرای Event
     */
    trigger(event, data = {}) {
        if (!this.eventListeners.has(event)) return this;
        
        const handlers = this.eventListeners.get(event);
        handlers.forEach(handler => {
            try {
                handler.call(this, data);
            } catch (error) {
                console.error(`Error in event handler for ${event}:`, error);
            }
        });
        
        return this;
    }

    /**
     * بروزرسانی تنظیمات
     */
    updateOptions(newOptions) {
        this.options = { ...this.options, ...newOptions };
        this.trigger('optionsUpdated', { options: this.options });
        return this;
    }

    /**
     * دریافت تنظیمات
     */
    getOption(key) {
        return this.options[key];
    }

    /**
     * تنظیم گزینه
     */
    setOption(key, value) {
        this.options[key] = value;
        this.trigger('optionChanged', { key, value });
        return this;
    }

    /**
     * نمایش کامپوننت
     */
    show() {
        if (this.element) {
            this.element.style.display = '';
            this.trigger('shown');
        }
        return this;
    }

    /**
     * مخفی کردن کامپوننت
     */
    hide() {
        if (this.element) {
            this.element.style.display = 'none';
            this.trigger('hidden');
        }
        return this;
    }

    /**
     * فعال کردن کامپوننت
     */
    enable() {
        if (this.element) {
            this.element.removeAttribute('disabled');
            this.element.classList.remove('disabled');
            this.trigger('enabled');
        }
        return this;
    }

    /**
     * غیرفعال کردن کامپوننت
     */
    disable() {
        if (this.element) {
            this.element.setAttribute('disabled', 'disabled');
            this.element.classList.add('disabled');
            this.trigger('disabled');
        }
        return this;
    }

    /**
     * بروزرسانی کامپوننت
     */
    refresh() {
        this.trigger('beforeRefresh');
        this.render();
        this.trigger('refreshed');
        return this;
    }

    /**
     * نابودی کامپوننت
     */
    destroy() {
        try {
            this.trigger('beforeDestroy');
            
            // Remove all event listeners
            this.eventListeners.clear();
            
            // Remove DOM event listeners
            this.unbindEvents();
            
            // Clean up element
            if (this.element) {
                this.element.removeAttribute('data-component');
                this.element = null;
            }
            
            this.isInitialized = false;
            this.trigger('destroyed');
            
            if (this.options.debug) {
                console.log(`Component ${this.constructor.name} destroyed`);
            }
        } catch (error) {
            console.error(`Error destroying component ${this.constructor.name}:`, error);
        }
    }

    /**
     * حذف Event Listener های DOM
     */
    unbindEvents() {
        // Override in child classes
    }

    /**
     * یافتن عنصر در کامپوننت
     */
    find(selector) {
        return this.element ? this.element.querySelector(selector) : null;
    }

    /**
     * یافتن تمام عناصر در کامپوننت
     */
    findAll(selector) {
        return this.element ? this.element.querySelectorAll(selector) : [];
    }

    /**
     * اضافه کردن کلاس
     */
    addClass(className) {
        if (this.element) {
            this.element.classList.add(className);
        }
        return this;
    }

    /**
     * حذف کلاس
     */
    removeClass(className) {
        if (this.element) {
            this.element.classList.remove(className);
        }
        return this;
    }

    /**
     * تغییر وضعیت کلاس
     */
    toggleClass(className) {
        if (this.element) {
            this.element.classList.toggle(className);
        }
        return this;
    }

    /**
     * بررسی وجود کلاس
     */
    hasClass(className) {
        return this.element ? this.element.classList.contains(className) : false;
    }

    /**
     * تنظیم محتوای HTML
     */
    setHtml(html) {
        if (this.element) {
            this.element.innerHTML = html;
        }
        return this;
    }

    /**
     * دریافت محتوای HTML
     */
    getHtml() {
        return this.element ? this.element.innerHTML : '';
    }

    /**
     * تنظیم متن
     */
    setText(text) {
        if (this.element) {
            this.element.textContent = text;
        }
        return this;
    }

    /**
     * دریافت متن
     */
    getText() {
        return this.element ? this.element.textContent : '';
    }

    /**
     * تنظیم attribute
     */
    setAttribute(name, value) {
        if (this.element) {
            this.element.setAttribute(name, value);
        }
        return this;
    }

    /**
     * دریافت attribute
     */
    getAttribute(name) {
        return this.element ? this.element.getAttribute(name) : null;
    }

    /**
     * حذف attribute
     */
    removeAttribute(name) {
        if (this.element) {
            this.element.removeAttribute(name);
        }
        return this;
    }

    /**
     * تنظیم data attribute
     */
    setData(key, value) {
        return this.setAttribute(`data-${key}`, value);
    }

    /**
     * دریافت data attribute
     */
    getData(key) {
        return this.getAttribute(`data-${key}`);
    }

    /**
     * بررسی معتبر بودن کامپوننت
     */
    isValid() {
        return this.isInitialized && this.element !== null;
    }

    /**
     * تبدیل به JSON
     */
    toJSON() {
        return {
            type: this.constructor.name,
            options: this.options,
            isInitialized: this.isInitialized
        };
    }
}

/**
 * Component Factory
 * کارخانه تولید کامپوننت‌ها
 */
class ComponentFactory {
    constructor() {
        this.components = new Map();
        this.instances = new WeakMap();
    }

    /**
     * ثبت نوع کامپوننت
     */
    register(name, componentClass) {
        this.components.set(name, componentClass);
        return this;
    }

    /**
     * ایجاد کامپوننت
     */
    create(name, element, options = {}) {
        const ComponentClass = this.components.get(name);
        if (!ComponentClass) {
            throw new Error(`Component type '${name}' not registered`);
        }

        const instance = new ComponentClass(element, options);
        this.instances.set(element, instance);
        
        // Mark element as having a component
        element.setAttribute('data-component', name);
        
        return instance;
    }

    /**
     * دریافت کامپوننت از عنصر
     */
    getInstance(element) {
        return this.instances.get(element);
    }

    /**
     * نابودی کامپوننت
     */
    destroy(element) {
        const instance = this.instances.get(element);
        if (instance) {
            instance.destroy();
            this.instances.delete(element);
        }
    }

    /**
     * مقداردهی خودکار کامپوننت‌ها
     */
    autoInit(container = document) {
        const elements = container.querySelectorAll('[data-component]');
        elements.forEach(element => {
            const componentType = element.getAttribute('data-component');
            if (componentType && !this.instances.has(element)) {
                try {
                    this.create(componentType, element);
                } catch (error) {
                    console.error(`Error auto-initializing component ${componentType}:`, error);
                }
            }
        });
    }
}

// Global component factory instance
window.ComponentFactory = new ComponentFactory();

// Auto-initialize components when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.ComponentFactory.autoInit();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { BaseComponent, ComponentFactory };
}
