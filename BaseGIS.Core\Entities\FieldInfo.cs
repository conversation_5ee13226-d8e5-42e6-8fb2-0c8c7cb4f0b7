namespace BaseGIS.Core.Entities
{
    public class FieldInfo
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string AliasName { get; set; }
        public string? UnitName { get; set; }
        public string FieldType { get; set; }
        public int FieldLength { get; set; }
        public bool IsUnique { get; set; }
        public bool IsRequired { get; set; }
        public bool Editable { get; set; }
        public bool IsDisplay { get; set; }
        public string? ValidationRule { get; set; }
        public string? DisableRule { get; set; }
        public string? WebService_URL { get; set; }
        public int WebService_Period { get; set; }
        public string? SQLCalc { get; set; }
        public int CalcPeriod { get; set; }
        public string? ShpFieldName { get; set; }
        public DateTime Updated { get; set; }
        public int TableInfoId { get; set; }
        public TableInfo TableInfo { get; set; }
        public List<DomainInfo> DomainInfos { get; set; }
        public int FieldIndex { get; set; }
    }
}