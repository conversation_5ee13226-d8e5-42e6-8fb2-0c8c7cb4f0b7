﻿@model BaseGIS.Web.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "ورود به سیستم";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}
<div class="container-fluid login-container">
    <div class="row min-vh-100">
        <!-- Left Section -->
        <div class="col-md-6 d-flex align-items-center justify-content-center text-white p-5">
            <div>
                <h2 class="mb-3">
                    <i class="fas fa-cube me-2"></i> کمان
                </h2>
                <h3 class="mb-4">کاوش مکانی استعلامات نقشه‌ای</h3>
                <p class="lead mb-4">
                    سرویس اعلام استعلام با هدف دریافت محدوده‌ای سطحی و ارزیابی این محدوده در راستای پاسخگویی به میزان همپوشانی این محدوده با املاک و اراضی در اختیار سازمان طراحی گردیده است. پس از دریافت استعلام از طریق این سرویس، پاسخ یا به صورت خودکار (از طریق تحلیل همپوشانی در بانک اطلاعات GIS سازمان) یا به صورت فرایندی (پس از بررسی کارشناسان) به پنجره واحد مدیریت زمین ارسال می‌گردد.
                </p>
                <a href="#" class="text-white text-decoration-none fw-bold">بیشتر بدانید <i class="fas fa-arrow-right ms-2"></i></a>
                <div class="mt-5">
                     
                </div>
                <footer class="mt-5 text-white-50 small">
                    1404 © LandInquiryApp
                </footer>
            </div>
        </div>

        <!-- Right Section (Login Form) -->
        <div class="col-md-6 d-flex align-items-center justify-content-center p-5">
            <div class="card shadow-lg login-card">
                <div class="card-body p-5">
                    <div class="text-end mb-4">
                        <a href="#" class="text-muted small">ایجاد حساب کاربری</a>
                    </div>
                    <form asp-action="Login" method="post">
                        <input type="hidden" name="returnUrl" value="@ViewData["ReturnUrl"]" />
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        <div class="mb-4">
                            <label asp-for="Username" class="form-label"></label>
                            <input asp-for="Username" class="form-control " placeholder="نام کاربری " />
                            <span asp-validation-for="Username" class="text-danger"></span>
                        </div>
                        <div class="mb-4">
                            <label asp-for="Password" class="form-label"></label>
                            <input asp-for="Password" class="form-control" placeholder="رمز عبور " />
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>
                        <div class="mb-4 form-check">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label"></label>
                        </div>
                        <div class="d-flex gap-2">
                            <a asp-action="ForgotPassword" class="btn btn-secondary">فراموشی رمز عبور</a>
                            <button type="submit" class="btn btn-primary flex-fill">ورود</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}