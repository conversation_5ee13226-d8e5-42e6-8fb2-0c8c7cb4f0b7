/**
 * Drawing Tools Component
 * کامپوننت ابزارهای ترسیم
 */

class DrawingTools extends BaseComponent {
    getDefaultOptions() {
        return {
            ...super.getDefaultOptions(),
            drawingModes: ['point', 'line', 'polygon', 'circle', 'rectangle', 'freehand'],
            defaultDrawingMode: 'point',
            pointStyle: {
                color: '#ff0000',
                fillColor: '#ff0000',
                fillOpacity: 0.8,
                radius: 8,
                weight: 2
            },
            lineStyle: {
                color: '#0000ff',
                weight: 3,
                opacity: 0.8,
                dashArray: null
            },
            polygonStyle: {
                color: '#00ff00',
                weight: 2,
                opacity: 0.8,
                fillColor: '#00ff00',
                fillOpacity: 0.3
            },
            circleStyle: {
                color: '#ff8800',
                weight: 2,
                opacity: 0.8,
                fillColor: '#ff8800',
                fillOpacity: 0.3
            },
            rectangleStyle: {
                color: '#8800ff',
                weight: 2,
                opacity: 0.8,
                fillColor: '#8800ff',
                fillOpacity: 0.3
            },
            enableSnapping: true,
            snapDistance: 10,
            showTooltips: true,
            allowEdit: true,
            onDrawStart: null,
            onDrawEnd: null,
            onDrawCancel: null,
            onFeatureEdit: null,
            onFeatureDelete: null
        };
    }

    beforeInit() {
        this.currentDrawingMode = this.options.defaultDrawingMode;
        this.map = window.map;
        this.editableLayers = window.editableLayers;
        this.isDrawing = false;
        this.currentDrawnLayer = null;
        this.drawControl = null;
        this.editControl = null;
        this.drawnItems = new L.FeatureGroup();
        
        // Add drawn items to map
        if (this.map) {
            this.map.addLayer(this.drawnItems);
        }
    }

    render() {
        this.renderToolbar();
        this.renderStyleDialog();
        this.renderLayerDialog();
    }

    renderToolbar() {
        const toolbar = this.createToolbar();
        this.element.appendChild(toolbar);
    }

    createToolbar() {
        const toolbar = document.createElement('div');
        toolbar.className = 'drawing-tools-toolbar';
        toolbar.innerHTML = `
            <div class="drawing-modes">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary drawing-btn" 
                            data-mode="point" title="ترسیم نقطه">
                        <i class="fas fa-map-marker-alt"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary drawing-btn" 
                            data-mode="line" title="ترسیم خط">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary drawing-btn" 
                            data-mode="polygon" title="ترسیم چندضلعی">
                        <i class="fas fa-draw-polygon"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary drawing-btn" 
                            data-mode="circle" title="ترسیم دایره">
                        <i class="fas fa-circle"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary drawing-btn" 
                            data-mode="rectangle" title="ترسیم مستطیل">
                        <i class="fas fa-square"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary drawing-btn" 
                            data-mode="freehand" title="ترسیم آزاد">
                        <i class="fas fa-pencil-alt"></i>
                    </button>
                </div>
            </div>
            
            <div class="drawing-actions">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-info edit-btn" 
                            title="ویرایش اشکال">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-outline-warning style-btn" 
                            title="تنظیمات استایل">
                        <i class="fas fa-palette"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary layers-btn" 
                            title="مدیریت لایه‌ها">
                        <i class="fas fa-layer-group"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger clear-btn" 
                            title="پاک کردن همه">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            
            <div class="drawing-status">
                <span class="status-text">آماده ترسیم</span>
                <div class="drawing-options">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="enableSnapping" 
                               ${this.options.enableSnapping ? 'checked' : ''}>
                        <label class="form-check-label" for="enableSnapping">
                            چسبندگی
                        </label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="showTooltips" 
                               ${this.options.showTooltips ? 'checked' : ''}>
                        <label class="form-check-label" for="showTooltips">
                            راهنما
                        </label>
                    </div>
                </div>
            </div>
        `;
        return toolbar;
    }

    renderStyleDialog() {
        const dialog = this.createStyleDialog();
        document.body.appendChild(dialog);
    }

    createStyleDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'drawing-tools-dialog';
        dialog.id = 'styleDialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-header">
                    <h5>تنظیمات استایل</h5>
                    <button type="button" class="btn-close" data-action="close"></button>
                </div>
                <div class="dialog-body">
                    <div class="style-controls">
                        <div class="mb-3">
                            <label for="shapeType" class="form-label">نوع شکل:</label>
                            <select class="form-select" id="shapeType">
                                <option value="point">نقطه</option>
                                <option value="line">خط</option>
                                <option value="polygon">چندضلعی</option>
                                <option value="circle">دایره</option>
                                <option value="rectangle">مستطیل</option>
                            </select>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <label for="strokeColor" class="form-label">رنگ خط:</label>
                                <input type="color" class="form-control form-control-color" 
                                       id="strokeColor" value="#0000ff">
                            </div>
                            <div class="col-md-6">
                                <label for="fillColor" class="form-label">رنگ پر:</label>
                                <input type="color" class="form-control form-control-color" 
                                       id="fillColor" value="#0000ff">
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label for="strokeWidth" class="form-label">ضخامت خط:</label>
                                <input type="range" class="form-range" id="strokeWidth" 
                                       min="1" max="10" value="2">
                                <div class="d-flex justify-content-between">
                                    <small>1</small>
                                    <small id="strokeWidthValue">2</small>
                                    <small>10</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="fillOpacity" class="form-label">شفافیت پر:</label>
                                <input type="range" class="form-range" id="fillOpacity" 
                                       min="0" max="100" value="30">
                                <div class="d-flex justify-content-between">
                                    <small>0%</small>
                                    <small id="fillOpacityValue">30%</small>
                                    <small>100%</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="strokeOpacity" class="form-label">شفافیت خط:</label>
                            <input type="range" class="form-range" id="strokeOpacity" 
                                   min="0" max="100" value="80">
                            <div class="d-flex justify-content-between">
                                <small>0%</small>
                                <small id="strokeOpacityValue">80%</small>
                                <small>100%</small>
                            </div>
                        </div>
                        
                        <div class="mb-3" id="pointSizeContainer">
                            <label for="pointSize" class="form-label">اندازه نقطه:</label>
                            <input type="range" class="form-range" id="pointSize" 
                                   min="3" max="20" value="8">
                            <div class="d-flex justify-content-between">
                                <small>3</small>
                                <small id="pointSizeValue">8</small>
                                <small>20</small>
                            </div>
                        </div>
                        
                        <div class="mb-3" id="dashArrayContainer">
                            <label for="dashArray" class="form-label">الگوی خط:</label>
                            <select class="form-select" id="dashArray">
                                <option value="">پیوسته</option>
                                <option value="5,5">خط چین کوتاه</option>
                                <option value="10,10">خط چین بلند</option>
                                <option value="5,10,5">نقطه خط</option>
                                <option value="2,5,10,5">نقطه خط چین</option>
                            </select>
                        </div>
                        
                        <div class="style-preview">
                            <h6>پیش‌نمایش:</h6>
                            <div class="preview-container" id="stylePreview">
                                <!-- Preview will be rendered here -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button type="button" class="btn btn-secondary" data-action="close">انصراف</button>
                    <button type="button" class="btn btn-success" data-action="apply-to-all">اعمال به همه</button>
                    <button type="button" class="btn btn-primary" data-action="apply">اعمال</button>
                </div>
            </div>
        `;
        return dialog;
    }

    renderLayerDialog() {
        const dialog = this.createLayerDialog();
        document.body.appendChild(dialog);
    }

    createLayerDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'drawing-tools-dialog';
        dialog.id = 'layerDialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-header">
                    <h5>مدیریت لایه‌های ترسیم</h5>
                    <button type="button" class="btn-close" data-action="close"></button>
                </div>
                <div class="dialog-body">
                    <div class="layer-controls">
                        <div class="mb-3">
                            <button type="button" class="btn btn-primary btn-sm w-100" 
                                    data-action="export-geojson">
                                <i class="fas fa-download"></i> دانلود GeoJSON
                            </button>
                        </div>
                        
                        <div class="mb-3">
                            <label for="importFile" class="form-label">بارگذاری فایل:</label>
                            <input type="file" class="form-control" id="importFile" 
                                   accept=".geojson,.json,.kml,.gpx">
                        </div>
                        
                        <div class="layer-list" id="layerList">
                            <div class="no-layers text-center text-muted">
                                <i class="fas fa-layer-group fa-2x mb-2"></i>
                                <p>هیچ لایه‌ای ترسیم نشده است</p>
                            </div>
                        </div>
                        
                        <div class="layer-statistics" id="layerStats">
                            <h6>آمار لایه‌ها:</h6>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <span class="stat-label">نقاط:</span>
                                    <span class="stat-value" id="pointCount">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">خطوط:</span>
                                    <span class="stat-value" id="lineCount">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">چندضلعی‌ها:</span>
                                    <span class="stat-value" id="polygonCount">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">کل:</span>
                                    <span class="stat-value" id="totalCount">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button type="button" class="btn btn-secondary" data-action="close">بستن</button>
                    <button type="button" class="btn btn-danger" data-action="clear-all">
                        <i class="fas fa-trash"></i> پاک کردن همه
                    </button>
                </div>
            </div>
        `;
        return dialog;
    }

    bindEvents() {
        super.bindEvents();

        // Toolbar events
        this.bindToolbarEvents();

        // Dialog events
        this.bindDialogEvents();

        // Map events
        this.bindMapEvents();
    }

    bindToolbarEvents() {
        // Drawing mode buttons
        this.element.querySelectorAll('.drawing-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const mode = e.currentTarget.dataset.mode;
                this.setDrawingMode(mode);
            });
        });

        // Action buttons
        this.find('.edit-btn')?.addEventListener('click', () => {
            this.toggleEditMode();
        });

        this.find('.style-btn')?.addEventListener('click', () => {
            this.showStyleDialog();
        });

        this.find('.layers-btn')?.addEventListener('click', () => {
            this.showLayerDialog();
        });

        this.find('.clear-btn')?.addEventListener('click', () => {
            this.clearAllDrawings();
        });

        // Options checkboxes
        this.find('#enableSnapping')?.addEventListener('change', (e) => {
            this.options.enableSnapping = e.target.checked;
        });

        this.find('#showTooltips')?.addEventListener('change', (e) => {
            this.options.showTooltips = e.target.checked;
        });
    }

    bindDialogEvents() {
        // Style dialog events
        const styleDialog = document.getElementById('styleDialog');
        if (styleDialog) {
            this.bindStyleDialogEvents(styleDialog);
        }

        // Layer dialog events
        const layerDialog = document.getElementById('layerDialog');
        if (layerDialog) {
            this.bindLayerDialogEvents(layerDialog);
        }
    }

    bindStyleDialogEvents(dialog) {
        // Shape type change
        const shapeTypeSelect = dialog.querySelector('#shapeType');
        if (shapeTypeSelect) {
            shapeTypeSelect.addEventListener('change', (e) => {
                this.updateStyleControls(e.target.value);
                this.updateStylePreview();
            });
        }

        // Color inputs
        ['strokeColor', 'fillColor'].forEach(id => {
            const input = dialog.querySelector(`#${id}`);
            if (input) {
                input.addEventListener('change', () => {
                    this.updateStylePreview();
                });
            }
        });

        // Range inputs
        ['strokeWidth', 'fillOpacity', 'strokeOpacity', 'pointSize'].forEach(id => {
            const input = dialog.querySelector(`#${id}`);
            const valueDisplay = dialog.querySelector(`#${id}Value`);
            if (input && valueDisplay) {
                input.addEventListener('input', (e) => {
                    const value = e.target.value;
                    if (id.includes('Opacity')) {
                        valueDisplay.textContent = `${value}%`;
                    } else {
                        valueDisplay.textContent = value;
                    }
                    this.updateStylePreview();
                });
            }
        });

        // Dash array select
        const dashArraySelect = dialog.querySelector('#dashArray');
        if (dashArraySelect) {
            dashArraySelect.addEventListener('change', () => {
                this.updateStylePreview();
            });
        }

        // Action buttons
        dialog.querySelectorAll('[data-action]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.handleStyleAction(action);
            });
        });
    }

    bindLayerDialogEvents(dialog) {
        // Import file
        const importFile = dialog.querySelector('#importFile');
        if (importFile) {
            importFile.addEventListener('change', (e) => {
                this.importFile(e.target.files[0]);
            });
        }

        // Action buttons
        dialog.querySelectorAll('[data-action]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.handleLayerAction(action);
            });
        });
    }

    bindMapEvents() {
        if (!this.map) return;

        // Drawing events
        this.map.on('draw:created', (e) => {
            this.onDrawCreated(e);
        });

        this.map.on('draw:edited', (e) => {
            this.onDrawEdited(e);
        });

        this.map.on('draw:deleted', (e) => {
            this.onDrawDeleted(e);
        });

        this.map.on('draw:drawstart', (e) => {
            this.onDrawStart(e);
        });

        this.map.on('draw:drawstop', (e) => {
            this.onDrawStop(e);
        });
    }

    // ========================================
    // Drawing Operations
    // ========================================

    setDrawingMode(mode) {
        if (!this.options.drawingModes.includes(mode)) {
            console.warn(`Drawing mode '${mode}' is not supported`);
            return;
        }

        // Update current mode
        this.currentDrawingMode = mode;

        // Update UI
        this.updateDrawingModeUI(mode);

        // Initialize drawing control
        this.initializeDrawingControl(mode);

        // Update status
        this.updateStatus(`حالت ترسیم: ${this.getModeName(mode)}`);

        this.trigger('drawingModeChanged', { mode });
    }

    initializeDrawingControl(mode) {
        // Remove existing draw control
        if (this.drawControl) {
            this.map.removeControl(this.drawControl);
            this.drawControl = null;
        }

        if (!this.map || !window.L || !window.L.Control || !window.L.Control.Draw) {
            console.warn('Leaflet.draw is not available');
            return;
        }

        const drawOptions = this.getDrawOptions(mode);

        this.drawControl = new L.Control.Draw({
            position: 'topright',
            draw: drawOptions,
            edit: {
                featureGroup: this.drawnItems,
                remove: true
            }
        });

        this.map.addControl(this.drawControl);
    }

    getDrawOptions(mode) {
        const options = {
            polyline: false,
            polygon: false,
            circle: false,
            rectangle: false,
            marker: false,
            circlemarker: false
        };

        const style = this.getCurrentStyle(mode);

        switch (mode) {
            case 'point':
                options.marker = {
                    icon: this.createCustomIcon(style)
                };
                break;
            case 'line':
                options.polyline = {
                    shapeOptions: style
                };
                break;
            case 'polygon':
                options.polygon = {
                    shapeOptions: style,
                    allowIntersection: false,
                    showArea: true
                };
                break;
            case 'circle':
                options.circle = {
                    shapeOptions: style
                };
                break;
            case 'rectangle':
                options.rectangle = {
                    shapeOptions: style
                };
                break;
            case 'freehand':
                options.polyline = {
                    shapeOptions: style
                };
                break;
        }

        return options;
    }

    getCurrentStyle(mode) {
        const styleKey = `${mode}Style`;
        return this.options[styleKey] || this.options.polygonStyle;
    }

    createCustomIcon(style) {
        return L.divIcon({
            className: 'custom-drawing-marker',
            html: `<div style="
                background-color: ${style.fillColor || style.color};
                border: ${style.weight || 2}px solid ${style.color};
                border-radius: 50%;
                width: ${(style.radius || 8) * 2}px;
                height: ${(style.radius || 8) * 2}px;
                opacity: ${style.opacity || 0.8};
            "></div>`,
            iconSize: [(style.radius || 8) * 2, (style.radius || 8) * 2],
            iconAnchor: [style.radius || 8, style.radius || 8]
        });
    }

    toggleEditMode() {
        if (this.editControl) {
            this.map.removeControl(this.editControl);
            this.editControl = null;
            this.updateStatus('حالت ویرایش غیرفعال شد');
        } else {
            this.editControl = new L.Control.Draw({
                position: 'topright',
                draw: false,
                edit: {
                    featureGroup: this.drawnItems,
                    remove: true
                }
            });
            this.map.addControl(this.editControl);
            this.updateStatus('حالت ویرایش فعال شد');
        }
    }

    clearAllDrawings() {
        if (confirm('آیا از پاک کردن همه ترسیم‌ها اطمینان دارید؟')) {
            this.drawnItems.clearLayers();
            if (this.editableLayers) {
                this.editableLayers.clearLayers();
            }
            this.updateLayerList();
            this.updateLayerStatistics();
            this.updateStatus('همه ترسیم‌ها پاک شدند');
            this.showToast('همه ترسیم‌ها پاک شدند', 'success');
        }
    }

    // ========================================
    // Event Handlers
    // ========================================

    onDrawCreated(e) {
        const layer = e.layer;
        const type = e.layerType;

        // Add to drawn items
        this.drawnItems.addLayer(layer);

        // Add to editable layers if available
        if (this.editableLayers) {
            this.editableLayers.addLayer(layer);
            layer.tool = "selecttool";
            layer.type = this.getLayerType(type);
        }

        // Add properties
        layer.drawingType = type;
        layer.drawingMode = this.currentDrawingMode;
        layer.createdAt = new Date().toISOString();
        layer.id = this.generateLayerId();

        // Add popup
        layer.bindPopup(this.createFeaturePopup(layer));

        // Update UI
        this.updateLayerList();
        this.updateLayerStatistics();
        this.updateStatus(`${this.getModeName(type)} ترسیم شد`);

        // Trigger events
        if (this.options.onDrawEnd) {
            this.options.onDrawEnd(layer, type);
        }

        this.trigger('drawEnd', { layer, type });
        this.showToast(`${this.getModeName(type)} ترسیم شد`, 'success');
    }

    onDrawEdited(e) {
        const layers = e.layers;

        layers.eachLayer((layer) => {
            layer.editedAt = new Date().toISOString();
        });

        this.updateLayerList();
        this.updateStatus(`${layers.getLayers().length} شکل ویرایش شد`);

        if (this.options.onFeatureEdit) {
            this.options.onFeatureEdit(layers);
        }

        this.trigger('featureEdit', { layers });
        this.showToast(`${layers.getLayers().length} شکل ویرایش شد`, 'success');
    }

    onDrawDeleted(e) {
        const layers = e.layers;

        this.updateLayerList();
        this.updateLayerStatistics();
        this.updateStatus(`${layers.getLayers().length} شکل حذف شد`);

        if (this.options.onFeatureDelete) {
            this.options.onFeatureDelete(layers);
        }

        this.trigger('featureDelete', { layers });
        this.showToast(`${layers.getLayers().length} شکل حذف شد`, 'success');
    }

    onDrawStart(e) {
        this.isDrawing = true;
        this.updateStatus(`در حال ترسیم ${this.getModeName(e.layerType)}...`);

        if (this.options.onDrawStart) {
            this.options.onDrawStart(e.layerType);
        }

        this.trigger('drawStart', { type: e.layerType });
    }

    onDrawStop(e) {
        this.isDrawing = false;
        this.updateStatus('آماده ترسیم');
    }

    // ========================================
    // Style Management
    // ========================================

    handleStyleAction(action) {
        switch (action) {
            case 'close':
                this.hideStyleDialog();
                break;
            case 'apply':
                this.applyCurrentStyle();
                break;
            case 'apply-to-all':
                this.applyStyleToAll();
                break;
        }
    }

    applyCurrentStyle() {
        const style = this.getStyleFromDialog();
        const shapeType = document.getElementById('shapeType')?.value;

        if (shapeType) {
            this.options[`${shapeType}Style`] = style;
            this.updateStylePreview();
            this.showToast('استایل اعمال شد', 'success');
        }
    }

    applyStyleToAll() {
        const style = this.getStyleFromDialog();

        this.drawnItems.eachLayer((layer) => {
            if (layer.setStyle) {
                layer.setStyle(style);
            }
        });

        this.showToast('استایل به همه اشکال اعمال شد', 'success');
    }

    getStyleFromDialog() {
        const strokeColor = document.getElementById('strokeColor')?.value || '#0000ff';
        const fillColor = document.getElementById('fillColor')?.value || '#0000ff';
        const strokeWidth = parseInt(document.getElementById('strokeWidth')?.value) || 2;
        const fillOpacity = parseInt(document.getElementById('fillOpacity')?.value) / 100 || 0.3;
        const strokeOpacity = parseInt(document.getElementById('strokeOpacity')?.value) / 100 || 0.8;
        const pointSize = parseInt(document.getElementById('pointSize')?.value) || 8;
        const dashArray = document.getElementById('dashArray')?.value || null;

        return {
            color: strokeColor,
            fillColor: fillColor,
            weight: strokeWidth,
            fillOpacity: fillOpacity,
            opacity: strokeOpacity,
            radius: pointSize,
            dashArray: dashArray || undefined
        };
    }

    updateStyleControls(shapeType) {
        const pointSizeContainer = document.getElementById('pointSizeContainer');
        const dashArrayContainer = document.getElementById('dashArrayContainer');

        if (pointSizeContainer) {
            pointSizeContainer.style.display = shapeType === 'point' ? 'block' : 'none';
        }

        if (dashArrayContainer) {
            dashArrayContainer.style.display = ['line', 'polygon'].includes(shapeType) ? 'block' : 'none';
        }

        // Load current style for shape type
        const currentStyle = this.options[`${shapeType}Style`];
        if (currentStyle) {
            this.loadStyleToDialog(currentStyle);
        }
    }

    loadStyleToDialog(style) {
        const strokeColor = document.getElementById('strokeColor');
        const fillColor = document.getElementById('fillColor');
        const strokeWidth = document.getElementById('strokeWidth');
        const fillOpacity = document.getElementById('fillOpacity');
        const strokeOpacity = document.getElementById('strokeOpacity');
        const pointSize = document.getElementById('pointSize');

        if (strokeColor) strokeColor.value = style.color || '#0000ff';
        if (fillColor) fillColor.value = style.fillColor || '#0000ff';
        if (strokeWidth) strokeWidth.value = style.weight || 2;
        if (fillOpacity) fillOpacity.value = (style.fillOpacity || 0.3) * 100;
        if (strokeOpacity) strokeOpacity.value = (style.opacity || 0.8) * 100;
        if (pointSize) pointSize.value = style.radius || 8;

        // Update value displays
        document.getElementById('strokeWidthValue').textContent = style.weight || 2;
        document.getElementById('fillOpacityValue').textContent = `${(style.fillOpacity || 0.3) * 100}%`;
        document.getElementById('strokeOpacityValue').textContent = `${(style.opacity || 0.8) * 100}%`;
        document.getElementById('pointSizeValue').textContent = style.radius || 8;
    }

    updateStylePreview() {
        const preview = document.getElementById('stylePreview');
        const shapeType = document.getElementById('shapeType')?.value;
        const style = this.getStyleFromDialog();

        if (!preview || !shapeType) return;

        let previewHTML = '';

        switch (shapeType) {
            case 'point':
                previewHTML = `
                    <div style="
                        width: ${style.radius * 2}px;
                        height: ${style.radius * 2}px;
                        background-color: ${style.fillColor};
                        border: ${style.weight}px solid ${style.color};
                        border-radius: 50%;
                        opacity: ${style.opacity};
                        margin: 10px auto;
                    "></div>
                `;
                break;
            case 'line':
                previewHTML = `
                    <div style="
                        width: 100px;
                        height: ${style.weight}px;
                        background-color: ${style.color};
                        opacity: ${style.opacity};
                        margin: 20px auto;
                        ${style.dashArray ? `background-image: repeating-linear-gradient(90deg, ${style.color} 0px, ${style.color} 5px, transparent 5px, transparent 10px);` : ''}
                    "></div>
                `;
                break;
            case 'polygon':
            case 'rectangle':
                previewHTML = `
                    <div style="
                        width: 80px;
                        height: 60px;
                        background-color: ${style.fillColor};
                        border: ${style.weight}px solid ${style.color};
                        opacity: ${style.fillOpacity};
                        margin: 10px auto;
                    "></div>
                `;
                break;
            case 'circle':
                previewHTML = `
                    <div style="
                        width: 60px;
                        height: 60px;
                        background-color: ${style.fillColor};
                        border: ${style.weight}px solid ${style.color};
                        border-radius: 50%;
                        opacity: ${style.fillOpacity};
                        margin: 10px auto;
                    "></div>
                `;
                break;
        }

        preview.innerHTML = previewHTML;
    }

    // ========================================
    // Layer Management
    // ========================================

    handleLayerAction(action) {
        switch (action) {
            case 'close':
                this.hideLayerDialog();
                break;
            case 'export-geojson':
                this.exportGeoJSON();
                break;
            case 'clear-all':
                this.clearAllDrawings();
                break;
        }
    }

    exportGeoJSON() {
        const geojson = this.drawnItems.toGeoJSON();

        // Add metadata
        geojson.metadata = {
            exportedAt: new Date().toISOString(),
            totalFeatures: geojson.features.length,
            exportedBy: 'BaseGIS Drawing Tools'
        };

        const dataStr = JSON.stringify(geojson, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `drawings_${new Date().toISOString().split('T')[0]}.geojson`;
        link.click();

        this.showToast('فایل GeoJSON دانلود شد', 'success');
    }

    importFile(file) {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                this.loadGeoJSON(data);
            } catch (error) {
                console.error('Error parsing file:', error);
                this.showToast('خطا در خواندن فایل', 'danger');
            }
        };
        reader.readAsText(file);
    }

    loadGeoJSON(geojson) {
        try {
            const layer = L.geoJSON(geojson, {
                style: (feature) => {
                    return this.getCurrentStyle(feature.geometry.type.toLowerCase());
                },
                pointToLayer: (feature, latlng) => {
                    const style = this.getCurrentStyle('point');
                    return L.circleMarker(latlng, style);
                }
            });

            layer.eachLayer((subLayer) => {
                this.drawnItems.addLayer(subLayer);
                if (this.editableLayers) {
                    this.editableLayers.addLayer(subLayer);
                    subLayer.tool = "selecttool";
                }
                subLayer.bindPopup(this.createFeaturePopup(subLayer));
            });

            this.updateLayerList();
            this.updateLayerStatistics();
            this.showToast('فایل با موفقیت بارگذاری شد', 'success');

        } catch (error) {
            console.error('Error loading GeoJSON:', error);
            this.showToast('خطا در بارگذاری فایل', 'danger');
        }
    }

    updateLayerList() {
        const container = document.getElementById('layerList');
        if (!container) return;

        const layers = [];
        this.drawnItems.eachLayer((layer) => {
            layers.push(layer);
        });

        if (layers.length === 0) {
            container.innerHTML = `
                <div class="no-layers text-center text-muted">
                    <i class="fas fa-layer-group fa-2x mb-2"></i>
                    <p>هیچ لایه‌ای ترسیم نشده است</p>
                </div>
            `;
            return;
        }

        container.innerHTML = layers.map((layer, index) => `
            <div class="layer-item" data-layer-id="${layer.id || index}">
                <div class="layer-header">
                    <span class="layer-type">
                        <i class="fas ${this.getLayerIcon(layer)}"></i>
                        ${this.getModeName(layer.drawingType || 'unknown')}
                    </span>
                    <div class="layer-actions">
                        <button type="button" class="btn btn-sm btn-outline-primary"
                                onclick="window.drawingTools.zoomToLayer('${layer.id || index}')">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger"
                                onclick="window.drawingTools.deleteLayer('${layer.id || index}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="layer-details">
                    <small class="text-muted">
                        ایجاد: ${layer.createdAt ? new Date(layer.createdAt).toLocaleDateString('fa-IR') : 'نامشخص'}
                        ${layer.editedAt ? `<br>ویرایش: ${new Date(layer.editedAt).toLocaleDateString('fa-IR')}` : ''}
                    </small>
                </div>
            </div>
        `).join('');
    }

    updateLayerStatistics() {
        const stats = {
            points: 0,
            lines: 0,
            polygons: 0,
            total: 0
        };

        this.drawnItems.eachLayer((layer) => {
            stats.total++;

            if (layer instanceof L.Marker || layer instanceof L.CircleMarker) {
                stats.points++;
            } else if (layer instanceof L.Polyline && !(layer instanceof L.Polygon)) {
                stats.lines++;
            } else if (layer instanceof L.Polygon || layer instanceof L.Circle || layer instanceof L.Rectangle) {
                stats.polygons++;
            }
        });

        // Update UI
        const pointCount = document.getElementById('pointCount');
        const lineCount = document.getElementById('lineCount');
        const polygonCount = document.getElementById('polygonCount');
        const totalCount = document.getElementById('totalCount');

        if (pointCount) pointCount.textContent = stats.points;
        if (lineCount) lineCount.textContent = stats.lines;
        if (polygonCount) polygonCount.textContent = stats.polygons;
        if (totalCount) totalCount.textContent = stats.total;
    }

    // ========================================
    // Utility Methods
    // ========================================

    updateDrawingModeUI(mode) {
        // Update button states
        this.element.querySelectorAll('.drawing-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.mode === mode) {
                btn.classList.add('active');
            }
        });
    }

    updateStatus(message) {
        const statusText = this.find('.status-text');
        if (statusText) {
            statusText.textContent = message;
        }
    }

    getModeName(mode) {
        const names = {
            point: 'نقطه',
            line: 'خط',
            polyline: 'خط',
            polygon: 'چندضلعی',
            circle: 'دایره',
            rectangle: 'مستطیل',
            freehand: 'ترسیم آزاد',
            marker: 'نقطه'
        };
        return names[mode] || mode;
    }

    getLayerType(drawingType) {
        const types = {
            marker: 'point',
            polyline: 'line',
            polygon: 'polygon',
            circle: 'polygon',
            rectangle: 'polygon'
        };
        return types[drawingType] || 'polygon';
    }

    getLayerIcon(layer) {
        if (layer instanceof L.Marker || layer instanceof L.CircleMarker) {
            return 'fa-map-marker-alt';
        } else if (layer instanceof L.Polyline && !(layer instanceof L.Polygon)) {
            return 'fa-minus';
        } else if (layer instanceof L.Circle) {
            return 'fa-circle';
        } else if (layer instanceof L.Rectangle) {
            return 'fa-square';
        } else if (layer instanceof L.Polygon) {
            return 'fa-draw-polygon';
        }
        return 'fa-question';
    }

    generateLayerId() {
        return `layer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    createFeaturePopup(layer) {
        const layerId = layer.id || 'unknown';
        return `
            <div class="feature-popup">
                <strong>${this.getModeName(layer.drawingType || 'unknown')}</strong><br>
                <div class="popup-actions mt-2">
                    <button type="button" class="btn btn-sm btn-outline-primary"
                            onclick="window.drawingTools.editLayer('${layerId}')">
                        <i class="fas fa-edit"></i> ویرایش
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger"
                            onclick="window.drawingTools.deleteLayer('${layerId}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `;
    }

    zoomToLayer(layerId) {
        this.drawnItems.eachLayer((layer) => {
            if (layer.id === layerId) {
                if (layer.getBounds) {
                    this.map.fitBounds(layer.getBounds());
                } else if (layer.getLatLng) {
                    this.map.setView(layer.getLatLng(), 15);
                }
            }
        });
    }

    deleteLayer(layerId) {
        if (confirm('آیا از حذف این لایه اطمینان دارید؟')) {
            this.drawnItems.eachLayer((layer) => {
                if (layer.id === layerId) {
                    this.drawnItems.removeLayer(layer);
                    if (this.editableLayers && this.editableLayers.hasLayer(layer)) {
                        this.editableLayers.removeLayer(layer);
                    }
                }
            });
            this.updateLayerList();
            this.updateLayerStatistics();
            this.showToast('لایه حذف شد', 'success');
        }
    }

    editLayer(layerId) {
        // Enable edit mode for specific layer
        this.toggleEditMode();
        this.showToast('حالت ویرایش فعال شد', 'info');
    }

    showToast(message, type = 'info', duration = 3000) {
        // Use existing toast system or create simple alert
        if (window.showToast) {
            window.showToast(message, type, duration);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    // ========================================
    // Dialog Management
    // ========================================

    showStyleDialog() {
        const dialog = document.getElementById('styleDialog');
        if (dialog) {
            dialog.style.display = 'block';
            this.updateStyleControls(this.currentDrawingMode);
            this.updateStylePreview();
        }
    }

    hideStyleDialog() {
        const dialog = document.getElementById('styleDialog');
        if (dialog) {
            dialog.style.display = 'none';
        }
    }

    showLayerDialog() {
        const dialog = document.getElementById('layerDialog');
        if (dialog) {
            dialog.style.display = 'block';
            this.updateLayerList();
            this.updateLayerStatistics();
        }
    }

    hideLayerDialog() {
        const dialog = document.getElementById('layerDialog');
        if (dialog) {
            dialog.style.display = 'none';
        }
    }

    // ========================================
    // Public API
    // ========================================

    setDrawingStyle(mode, style) {
        if (this.options.drawingModes.includes(mode)) {
            this.options[`${mode}Style`] = { ...this.options[`${mode}Style`], ...style };
        }
    }

    getDrawingStyle(mode) {
        return this.options[`${mode}Style`] || {};
    }

    getDrawnLayers() {
        const layers = [];
        this.drawnItems.eachLayer((layer) => {
            layers.push(layer);
        });
        return layers;
    }

    isInDrawingMode() {
        return this.isDrawing;
    }

    getCurrentDrawingMode() {
        return this.currentDrawingMode;
    }

    // ========================================
    // Cleanup
    // ========================================

    destroy() {
        // Remove controls
        if (this.drawControl && this.map) {
            this.map.removeControl(this.drawControl);
        }

        if (this.editControl && this.map) {
            this.map.removeControl(this.editControl);
        }

        // Remove drawn items
        if (this.drawnItems && this.map) {
            this.map.removeLayer(this.drawnItems);
        }

        // Remove dialogs
        const styleDialog = document.getElementById('styleDialog');
        if (styleDialog) {
            styleDialog.remove();
        }

        const layerDialog = document.getElementById('layerDialog');
        if (layerDialog) {
            layerDialog.remove();
        }

        super.destroy();
    }
}

// Register component
window.ComponentFactory.register('drawing-tools', DrawingTools);
