<!--begin::Statistical Report Content-->
<div class="container-fluid h-100">
    <div class="row h-100">
        <div class="col-12">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fa fa-pie-chart"></i> گزارش آماری
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- تنظیمات گزارش -->
                        <div class="col-md-3">
                            <div class="report-settings">
                                <div class="mb-4">
                                    <h6 class="border-bottom pb-2">انتخاب لایه</h6>
                                    <select class="form-select" id="reportLayer" onchange="loadReportFields()">
                                        <option value="">لایه را انتخاب کنید...</option>
                                        <option value="buildings">ساختمان‌ها</option>
                                        <option value="roads">جاده‌ها</option>
                                        <option value="parks">پارک‌ها</option>
                                        <option value="water">منابع آبی</option>
                                    </select>
                                </div>

                                <div class="mb-4" id="fieldsSection" style="display: none;">
                                    <h6 class="border-bottom pb-2">فیلدهای آماری</h6>
                                    <div id="statisticalFields">
                                        <!-- فیلدها اینجا لود می‌شوند -->
                                    </div>
                                </div>

                                <div class="mb-4" id="groupingSection" style="display: none;">
                                    <h6 class="border-bottom pb-2">گروه‌بندی</h6>
                                    <select class="form-select" id="groupByField">
                                        <option value="">بدون گروه‌بندی</option>
                                    </select>
                                </div>

                                <div class="mb-4" id="chartSection" style="display: none;">
                                    <h6 class="border-bottom pb-2">نوع نمودار</h6>
                                    <div class="btn-group-vertical w-100" role="group">
                                        <input type="radio" class="btn-check" name="chartType" id="pieChart" value="pie" checked>
                                        <label class="btn btn-outline-primary" for="pieChart">
                                            <i class="fa fa-pie-chart"></i> دایره‌ای
                                        </label>
                                        
                                        <input type="radio" class="btn-check" name="chartType" id="barChart" value="bar">
                                        <label class="btn btn-outline-primary" for="barChart">
                                            <i class="fa fa-bar-chart"></i> ستونی
                                        </label>
                                        
                                        <input type="radio" class="btn-check" name="chartType" id="lineChart" value="line">
                                        <label class="btn btn-outline-primary" for="lineChart">
                                            <i class="fa fa-line-chart"></i> خطی
                                        </label>
                                    </div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-success" onclick="generateReport()" disabled id="generateReportBtn">
                                        <i class="fa fa-chart-bar"></i> تولید گزارش
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="clearReport()">
                                        <i class="fa fa-eraser"></i> پاک کردن
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- نمایش گزارش -->
                        <div class="col-md-9">
                            <div class="report-display">
                                <!-- تب‌های گزارش -->
                                <ul class="nav nav-tabs" id="reportTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                                            <i class="fa fa-eye"></i> نمای کلی
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="charts-tab" data-bs-toggle="tab" data-bs-target="#charts" type="button" role="tab">
                                            <i class="fa fa-chart-pie"></i> نمودارها
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="tables-tab" data-bs-toggle="tab" data-bs-target="#tables" type="button" role="tab">
                                            <i class="fa fa-table"></i> جداول
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="export-tab" data-bs-toggle="tab" data-bs-target="#export" type="button" role="tab">
                                            <i class="fa fa-download"></i> خروجی
                                        </button>
                                    </li>
                                </ul>

                                <div class="tab-content" id="reportTabContent">
                                    <!-- تب نمای کلی -->
                                    <div class="tab-pane fade show active" id="overview" role="tabpanel">
                                        <div class="p-3">
                                            <div id="overviewContent">
                                                <div class="text-center text-muted">
                                                    <i class="fa fa-chart-bar fa-4x mb-3"></i>
                                                    <h5>گزارش آماری</h5>
                                                    <p>برای شروع، لایه مورد نظر را انتخاب کرده و فیلدهای آماری را تعریف کنید.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- تب نمودارها -->
                                    <div class="tab-pane fade" id="charts" role="tabpanel">
                                        <div class="p-3">
                                            <div id="chartsContent" style="height: 500px;">
                                                <div class="text-center text-muted">
                                                    <i class="fa fa-chart-pie fa-4x mb-3"></i>
                                                    <p>نمودارهای آماری اینجا نمایش داده می‌شوند.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- تب جداول -->
                                    <div class="tab-pane fade" id="tables" role="tabpanel">
                                        <div class="p-3">
                                            <div id="tablesContent" style="height: 500px; overflow: auto;">
                                                <div class="text-center text-muted">
                                                    <i class="fa fa-table fa-4x mb-3"></i>
                                                    <p>جداول آماری اینجا نمایش داده می‌شوند.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- تب خروجی -->
                                    <div class="tab-pane fade" id="export" role="tabpanel">
                                        <div class="p-3">
                                            <div id="exportContent">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="card">
                                                            <div class="card-header">
                                                                <h6 class="mb-0">خروجی فایل</h6>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="d-grid gap-2">
                                                                    <button class="btn btn-outline-success" onclick="exportToExcel()">
                                                                        <i class="fa fa-file-excel"></i> خروجی Excel
                                                                    </button>
                                                                    <button class="btn btn-outline-danger" onclick="exportToPDF()">
                                                                        <i class="fa fa-file-pdf"></i> خروجی PDF
                                                                    </button>
                                                                    <button class="btn btn-outline-info" onclick="exportToCSV()">
                                                                        <i class="fa fa-file-csv"></i> خروجی CSV
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="card">
                                                            <div class="card-header">
                                                                <h6 class="mb-0">اشتراک‌گذاری</h6>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="d-grid gap-2">
                                                                    <button class="btn btn-outline-primary" onclick="shareReport()">
                                                                        <i class="fa fa-share"></i> اشتراک‌گذاری
                                                                    </button>
                                                                    <button class="btn btn-outline-warning" onclick="emailReport()">
                                                                        <i class="fa fa-envelope"></i> ارسال ایمیل
                                                                    </button>
                                                                    <button class="btn btn-outline-secondary" onclick="printReport()">
                                                                        <i class="fa fa-print"></i> چاپ
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // متغیرهای گزارش آماری
    let reportData = {};
    let currentChart = null;

    // فیلدهای شبیه‌سازی شده
    const reportFields = {
        'buildings': [
            { name: 'Type', label: 'نوع کاربری', type: 'categorical' },
            { name: 'Floors', label: 'تعداد طبقات', type: 'numerical' },
            { name: 'Area', label: 'مساحت', type: 'numerical' },
            { name: 'BuildYear', label: 'سال ساخت', type: 'numerical' },
            { name: 'Status', label: 'وضعیت', type: 'categorical' }
        ],
        'roads': [
            { name: 'Type', label: 'نوع جاده', type: 'categorical' },
            { name: 'Width', label: 'عرض', type: 'numerical' },
            { name: 'Length', label: 'طول', type: 'numerical' },
            { name: 'Surface', label: 'نوع روکش', type: 'categorical' }
        ],
        'parks': [
            { name: 'Type', label: 'نوع پارک', type: 'categorical' },
            { name: 'Area', label: 'مساحت', type: 'numerical' },
            { name: 'Facilities', label: 'امکانات', type: 'categorical' }
        ]
    };

    /**
     * بارگذاری فیلدهای گزارش
     */
    function loadReportFields() {
        const layerId = document.getElementById('reportLayer').value;
        const fieldsSection = document.getElementById('fieldsSection');
        const groupingSection = document.getElementById('groupingSection');
        const chartSection = document.getElementById('chartSection');
        const generateBtn = document.getElementById('generateReportBtn');

        if (!layerId) {
            fieldsSection.style.display = 'none';
            groupingSection.style.display = 'none';
            chartSection.style.display = 'none';
            generateBtn.disabled = true;
            return;
        }

        const fields = reportFields[layerId] || [];
        
        // نمایش فیلدهای آماری
        const statisticalFields = document.getElementById('statisticalFields');
        let fieldsHtml = '';
        fields.forEach(field => {
            fieldsHtml += `
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="field_${field.name}" value="${field.name}">
                    <label class="form-check-label" for="field_${field.name}">
                        ${field.label} <small class="text-muted">(${field.type === 'numerical' ? 'عددی' : 'دسته‌ای'})</small>
                    </label>
                </div>
            `;
        });
        statisticalFields.innerHTML = fieldsHtml;

        // نمایش فیلدهای گروه‌بندی
        const groupByField = document.getElementById('groupByField');
        groupByField.innerHTML = '<option value="">بدون گروه‌بندی</option>';
        fields.filter(f => f.type === 'categorical').forEach(field => {
            groupByField.innerHTML += `<option value="${field.name}">${field.label}</option>`;
        });

        fieldsSection.style.display = 'block';
        groupingSection.style.display = 'block';
        chartSection.style.display = 'block';
        generateBtn.disabled = false;
    }

    /**
     * تولید گزارش
     */
    function generateReport() {
        const layerId = document.getElementById('reportLayer').value;
        const selectedFields = getSelectedFields();
        const groupByField = document.getElementById('groupByField').value;
        const chartType = document.querySelector('input[name="chartType"]:checked').value;

        if (!layerId || selectedFields.length === 0) {
            alert('لطفاً لایه و حداقل یک فیلد را انتخاب کنید.');
            return;
        }

        // نمایش loading
        showReportLoading();

        // شبیه‌سازی تولید گزارش
        setTimeout(() => {
            simulateReportGeneration(layerId, selectedFields, groupByField, chartType);
        }, 2000);
    }

    /**
     * دریافت فیلدهای انتخاب شده
     */
    function getSelectedFields() {
        const fields = [];
        document.querySelectorAll('#statisticalFields input[type="checkbox"]:checked').forEach(checkbox => {
            fields.push(checkbox.value);
        });
        return fields;
    }

    /**
     * نمایش loading
     */
    function showReportLoading() {
        const overviewContent = document.getElementById('overviewContent');
        overviewContent.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-success" role="status">
                    <span class="visually-hidden">در حال تولید گزارش...</span>
                </div>
                <p class="mt-2">در حال تولید گزارش آماری...</p>
            </div>
        `;
    }

    /**
     * شبیه‌سازی تولید گزارش
     */
    function simulateReportGeneration(layerId, fields, groupBy, chartType) {
        // داده‌های شبیه‌سازی شده
        reportData = {
            layer: layerId,
            totalRecords: 1250,
            fields: fields,
            groupBy: groupBy,
            chartType: chartType,
            statistics: {
                'Type': {
                    'اداری': 450,
                    'مسکونی': 600,
                    'تجاری': 200
                },
                'Floors': {
                    min: 1,
                    max: 25,
                    avg: 8.5,
                    sum: 10625
                },
                'Area': {
                    min: 50,
                    max: 5000,
                    avg: 850,
                    sum: 1062500
                }
            }
        };

        displayReport();
    }

    /**
     * نمایش گزارش
     */
    function displayReport() {
        displayOverview();
        displayCharts();
        displayTables();
    }

    /**
     * نمایش نمای کلی
     */
    function displayOverview() {
        const overviewContent = document.getElementById('overviewContent');
        const layerName = getLayerDisplayName(reportData.layer);

        let html = `
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="alert alert-success">
                        <h5><i class="fa fa-check-circle"></i> گزارش آماری ${layerName}</h5>
                        <p class="mb-0">گزارش با موفقیت تولید شد. کل رکوردها: <strong>${reportData.totalRecords.toLocaleString()}</strong></p>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fa fa-database fa-3x text-primary mb-3"></i>
                            <h4>${reportData.totalRecords.toLocaleString()}</h4>
                            <p class="text-muted">کل رکوردها</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fa fa-list fa-3x text-success mb-3"></i>
                            <h4>${reportData.fields.length}</h4>
                            <p class="text-muted">فیلدهای تحلیل شده</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fa fa-chart-pie fa-3x text-warning mb-3"></i>
                            <h4>${Object.keys(reportData.statistics).length}</h4>
                            <p class="text-muted">آمار تولید شده</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">خلاصه آمار</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
        `;

        // نمایش آمار هر فیلد
        Object.entries(reportData.statistics).forEach(([field, stats]) => {
            if (typeof stats === 'object' && stats.min !== undefined) {
                // فیلد عددی
                html += `
                    <div class="col-md-6 mb-3">
                        <h6>${getFieldDisplayName(field)}</h6>
                        <ul class="list-unstyled">
                            <li><strong>حداقل:</strong> ${stats.min.toLocaleString()}</li>
                            <li><strong>حداکثر:</strong> ${stats.max.toLocaleString()}</li>
                            <li><strong>میانگین:</strong> ${stats.avg.toLocaleString()}</li>
                            <li><strong>مجموع:</strong> ${stats.sum.toLocaleString()}</li>
                        </ul>
                    </div>
                `;
            } else {
                // فیلد دسته‌ای
                html += `
                    <div class="col-md-6 mb-3">
                        <h6>${getFieldDisplayName(field)}</h6>
                        <ul class="list-unstyled">
                `;
                Object.entries(stats).forEach(([category, count]) => {
                    const percentage = ((count / reportData.totalRecords) * 100).toFixed(1);
                    html += `<li><strong>${category}:</strong> ${count.toLocaleString()} (${percentage}%)</li>`;
                });
                html += '</ul></div>';
            }
        });

        html += `
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        overviewContent.innerHTML = html;
    }

    /**
     * نمایش نمودارها
     */
    function displayCharts() {
        const chartsContent = document.getElementById('chartsContent');
        
        chartsContent.innerHTML = `
            <div class="row h-100">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <h6 class="mb-0">توزیع نوع کاربری</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="typeChart" width="300" height="200"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <h6 class="mb-0">توزیع مساحت</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="areaChart" width="300" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // شبیه‌سازی نمودار (در پیاده‌سازی واقعی از Chart.js استفاده کنید)
        setTimeout(() => {
            const typeCanvas = document.getElementById('typeChart');
            const areaCanvas = document.getElementById('areaChart');
            
            if (typeCanvas) {
                const ctx = typeCanvas.getContext('2d');
                ctx.fillStyle = '#007bff';
                ctx.fillRect(50, 50, 100, 100);
                ctx.fillStyle = '#28a745';
                ctx.fillRect(170, 30, 100, 120);
                ctx.fillStyle = '#ffc107';
                ctx.fillRect(290, 70, 100, 80);
                
                ctx.fillStyle = '#000';
                ctx.font = '12px Arial';
                ctx.fillText('اداری', 70, 170);
                ctx.fillText('مسکونی', 180, 170);
                ctx.fillText('تجاری', 310, 170);
            }
            
            if (areaCanvas) {
                const ctx = areaCanvas.getContext('2d');
                ctx.strokeStyle = '#dc3545';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(50, 150);
                ctx.lineTo(100, 100);
                ctx.lineTo(150, 120);
                ctx.lineTo(200, 80);
                ctx.lineTo(250, 90);
                ctx.lineTo(300, 60);
                ctx.stroke();
            }
        }, 100);
    }

    /**
     * نمایش جداول
     */
    function displayTables() {
        const tablesContent = document.getElementById('tablesContent');
        
        let html = `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>فیلد</th>
                            <th>نوع</th>
                            <th>تعداد</th>
                            <th>درصد</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        Object.entries(reportData.statistics).forEach(([field, stats]) => {
            if (typeof stats === 'object' && stats.min === undefined) {
                Object.entries(stats).forEach(([category, count]) => {
                    const percentage = ((count / reportData.totalRecords) * 100).toFixed(1);
                    html += `
                        <tr>
                            <td>${getFieldDisplayName(field)}</td>
                            <td>${category}</td>
                            <td>${count.toLocaleString()}</td>
                            <td>${percentage}%</td>
                        </tr>
                    `;
                });
            }
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        tablesContent.innerHTML = html;
    }

    /**
     * پاک کردن گزارش
     */
    function clearReport() {
        document.getElementById('reportLayer').value = '';
        document.getElementById('fieldsSection').style.display = 'none';
        document.getElementById('groupingSection').style.display = 'none';
        document.getElementById('chartSection').style.display = 'none';
        document.getElementById('generateReportBtn').disabled = true;

        // پاک کردن محتوا
        document.getElementById('overviewContent').innerHTML = `
            <div class="text-center text-muted">
                <i class="fa fa-chart-bar fa-4x mb-3"></i>
                <h5>گزارش آماری</h5>
                <p>برای شروع، لایه مورد نظر را انتخاب کرده و فیلدهای آماری را تعریف کنید.</p>
            </div>
        `;

        document.getElementById('chartsContent').innerHTML = `
            <div class="text-center text-muted">
                <i class="fa fa-chart-pie fa-4x mb-3"></i>
                <p>نمودارهای آماری اینجا نمایش داده می‌شوند.</p>
            </div>
        `;

        document.getElementById('tablesContent').innerHTML = `
            <div class="text-center text-muted">
                <i class="fa fa-table fa-4x mb-3"></i>
                <p>جداول آماری اینجا نمایش داده می‌شوند.</p>
            </div>
        `;

        reportData = {};
    }

    /**
     * دریافت نام نمایشی لایه
     */
    function getLayerDisplayName(layerId) {
        const names = {
            'buildings': 'ساختمان‌ها',
            'roads': 'جاده‌ها',
            'parks': 'پارک‌ها',
            'water': 'منابع آبی'
        };
        return names[layerId] || layerId;
    }

    /**
     * دریافت نام نمایشی فیلد
     */
    function getFieldDisplayName(fieldName) {
        const names = {
            'Type': 'نوع',
            'Floors': 'تعداد طبقات',
            'Area': 'مساحت',
            'BuildYear': 'سال ساخت',
            'Status': 'وضعیت'
        };
        return names[fieldName] || fieldName;
    }

    // توابع خروجی
    function exportToExcel() { alert('خروجی Excel'); }
    function exportToPDF() { alert('خروجی PDF'); }
    function exportToCSV() { alert('خروجی CSV'); }
    function shareReport() { alert('اشتراک‌گذاری گزارش'); }
    function emailReport() { alert('ارسال ایمیل'); }
    function printReport() { alert('چاپ گزارش'); }
</script>
<!--end::Statistical Report Content-->
