2025-06-02 12:44:58.116 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 12:45:00.711 +03:30 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-02 12:45:00.839 +03:30 [INF] Executed DbCommand (93ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-02 12:45:00.928 +03:30 [INF] Starting database seeding
2025-06-02 12:45:01.565 +03:30 [INF] Executed DbCommand (196ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 12:45:01.648 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 12:45:01.729 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedEmail_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0
2025-06-02 12:45:01.794 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedUserName] = @__normalizedUserName_0
2025-06-02 12:45:01.853 +03:30 [INF] Finished database seeding
2025-06-02 12:45:01.910 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [AppSettings] AS [a]
        WHERE [a].[Name] = N'CompanyName') THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-02 12:45:01.920 +03:30 [INF] Database seeding completed
2025-06-02 12:45:06.542 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-02 12:45:06.544 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-02 12:45:06.967 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 12:45:06.968 +03:30 [INF] Hosting environment: Development
2025-06-02 12:45:06.969 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-02 12:45:06.974 +03:30 [INF] The application has started
2025-06-02 12:45:22.340 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 12:45:23.245 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-02 12:45:23.324 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__user_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ClaimType], [a].[ClaimValue], [a].[UserId]
FROM [AspNetUserClaims] AS [a]
WHERE [a].[UserId] = @__user_Id_0
2025-06-02 12:45:23.358 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-02 12:45:23.370 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 12:45:23.392 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__role_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0
2025-06-02 12:45:23.409 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:45:23.426 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 12:45:23.645 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 12:45:23.654 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.9601ms.
2025-06-02 12:45:23.768 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 12:45:23.823 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-02 12:45:23.995 +03:30 [INF] Executed ViewResult - view Index executed in 327.2663ms.
2025-06-02 12:45:24.005 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 573.3551ms
2025-06-02 12:45:24.007 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:45:24.017 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 1688.5529ms
2025-06-02 12:45:24.086 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/css/adminlte.rtl.css - null null
2025-06-02 12:45:24.101 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/assets/img/user1-128x128.jpg - null null
2025-06-02 12:45:24.086 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-02 12:45:24.332 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/assets/img/user1-128x128.jpg - 404 0 null 230.7813ms
2025-06-02 12:45:24.331 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/css/adminlte.rtl.css - 404 0 null 245.2577ms
2025-06-02 12:45:24.471 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/assets/img/user1-128x128.jpg, Response status code: 404
2025-06-02 12:45:24.086 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-02 12:45:24.111 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/assets/img/user8-128x128.jpg - null null
2025-06-02 12:45:24.677 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-02 12:45:24.673 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/css/adminlte.rtl.css, Response status code: 404
2025-06-02 12:45:24.148 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/assets/img/user3-128x128.jpg - null null
2025-06-02 12:45:24.158 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/assets/img/AdminLTELogo.png - null null
2025-06-02 12:45:24.158 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/assets/img/user2-160x160.jpg - null null
2025-06-02 12:45:24.168 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/logo.png - null null
2025-06-02 12:45:24.178 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/avatars/male.png - null null
2025-06-02 12:45:24.897 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - 499 106394 text/css 810.8788ms
2025-06-02 12:45:24.892 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-02 12:45:25.036 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/assets/img/user8-128x128.jpg - 404 0 null 925.8913ms
2025-06-02 12:45:25.036 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/assets/img/user3-128x128.jpg - 404 0 null 888.6175ms
2025-06-02 12:45:25.036 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/assets/img/AdminLTELogo.png - 404 0 null 878.334ms
2025-06-02 12:45:25.036 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/assets/img/user2-160x160.jpg - 404 0 null 878.3238ms
2025-06-02 12:45:25.034 +03:30 [INF] Sending file. Request path: '/img/logo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\logo.png'
2025-06-02 12:45:24.219 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - null null
2025-06-02 12:45:24.310 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/js/adminlte.js - null null
2025-06-02 12:45:24.310 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 12:45:24.329 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site.css?v=4jrNIwWPb6Z8RQZpNZvbUCyYT_QaP73cm0C8RK9XkLM - null null
2025-06-02 12:45:24.332 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=cJHDvzXLb3gXlNMKuPnwt6yOLtwhwS9DoLLFneXi3yk - null null
2025-06-02 12:45:24.341 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 12:45:25.172 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - 499 232911 text/css 1085.425ms
2025-06-02 12:45:25.176 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/assets/img/user8-128x128.jpg, Response status code: 404
2025-06-02 12:45:25.180 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/assets/img/user3-128x128.jpg, Response status code: 404
2025-06-02 12:45:25.183 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/assets/img/AdminLTELogo.png, Response status code: 404
2025-06-02 12:45:25.186 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/assets/img/user2-160x160.jpg, Response status code: 404
2025-06-02 12:45:25.189 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/logo.png - 499 830 image/png 1020.2306ms
2025-06-02 12:45:25.164 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-02 12:45:25.193 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - 404 0 null 974.1326ms
2025-06-02 12:45:25.197 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/js/adminlte.js - 404 0 null 886.8808ms
2025-06-02 12:45:25.204 +03:30 [INF] The file /css/site.css was not modified
2025-06-02 12:45:24.396 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 12:45:25.204 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 893.9863ms
2025-06-02 12:45:25.338 +03:30 [INF] Sending file. Request path: '/js/site.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\site.js'
2025-06-02 12:45:25.342 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 1000.2415ms
2025-06-02 12:45:25.363 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/avatars/male.png - 499 268 image/png 1184.6519ms
2025-06-02 12:45:25.367 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css, Response status code: 404
2025-06-02 12:45:25.370 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/js/adminlte.js, Response status code: 404
2025-06-02 12:45:25.372 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site.css?v=4jrNIwWPb6Z8RQZpNZvbUCyYT_QaP73cm0C8RK9XkLM - 304 null text/css 1049.2313ms
2025-06-02 12:45:25.381 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=cJHDvzXLb3gXlNMKuPnwt6yOLtwhwS9DoLLFneXi3yk - 499 2636 text/javascript 1049.9205ms
2025-06-02 12:45:25.393 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-02 12:45:25.396 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 12:45:25.595 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-02 12:45:25.598 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 499 45276 application/font-woff 205.3237ms
2025-06-02 12:45:25.614 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 1217.8525ms
2025-06-02 12:45:25.633 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/js/adminlte.js - null null
2025-06-02 12:45:25.637 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/js/adminlte.js - 404 0 null 4.5232ms
2025-06-02 12:45:25.641 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/js/adminlte.js, Response status code: 404
2025-06-02 12:45:25.714 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 12:45:25.719 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 5.0973ms
2025-06-02 12:45:25.723 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 12:46:29.918 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 12:46:29.964 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:46:29.969 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 12:46:30.008 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 12:46:30.012 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0316ms.
2025-06-02 12:46:30.016 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 12:46:30.040 +03:30 [INF] Executed ViewResult - view Index executed in 25.7637ms.
2025-06-02 12:46:30.043 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 71.6625ms
2025-06-02 12:46:30.044 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:46:30.045 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 127.4329ms
2025-06-02 12:46:30.066 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/css/adminlte.rtl.css - null null
2025-06-02 12:46:30.067 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/assets/img/user8-128x128.jpg - null null
2025-06-02 12:46:30.110 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/assets/img/user3-128x128.jpg - null null
2025-06-02 12:46:30.190 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/css/adminlte.rtl.css - 404 0 null 124.6031ms
2025-06-02 12:46:30.066 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/assets/img/user1-128x128.jpg - null null
2025-06-02 12:46:30.144 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/assets/img/AdminLTELogo.png - null null
2025-06-02 12:46:30.144 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/assets/img/user2-160x160.jpg - null null
2025-06-02 12:46:30.156 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/js/adminlte.js - null null
2025-06-02 12:46:30.191 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 12:46:30.207 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 12:46:30.249 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/assets/img/user8-128x128.jpg - 404 0 null 182.1774ms
2025-06-02 12:46:30.253 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/assets/img/user3-128x128.jpg - 404 0 null 184.1476ms
2025-06-02 12:46:30.257 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/css/adminlte.rtl.css, Response status code: 404
2025-06-02 12:46:30.261 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/assets/img/user1-128x128.jpg - 404 0 null 194.7237ms
2025-06-02 12:46:30.265 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/assets/img/AdminLTELogo.png - 404 0 null 121.3633ms
2025-06-02 12:46:30.269 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/assets/img/user2-160x160.jpg - 404 0 null 124.7281ms
2025-06-02 12:46:30.271 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/js/adminlte.js - 404 0 null 115.6653ms
2025-06-02 12:46:30.279 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 78.2971ms
2025-06-02 12:46:30.281 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 90.8976ms
2025-06-02 12:46:30.284 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/assets/img/user8-128x128.jpg, Response status code: 404
2025-06-02 12:46:30.287 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/assets/img/user3-128x128.jpg, Response status code: 404
2025-06-02 12:46:30.294 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/assets/img/user1-128x128.jpg, Response status code: 404
2025-06-02 12:46:30.298 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/assets/img/AdminLTELogo.png, Response status code: 404
2025-06-02 12:46:30.301 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/assets/img/user2-160x160.jpg, Response status code: 404
2025-06-02 12:46:30.306 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/js/adminlte.js, Response status code: 404
2025-06-02 12:46:30.448 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/dist/js/adminlte.js - null null
2025-06-02 12:46:30.453 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/dist/js/adminlte.js - 404 0 null 4.5514ms
2025-06-02 12:46:30.458 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/dist/js/adminlte.js, Response status code: 404
2025-06-02 12:50:03.909 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 12:50:04.975 +03:30 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-02 12:50:05.039 +03:30 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-02 12:50:05.115 +03:30 [INF] Starting database seeding
2025-06-02 12:50:05.462 +03:30 [INF] Executed DbCommand (21ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 12:50:05.529 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 12:50:05.563 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedEmail_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0
2025-06-02 12:50:05.645 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedUserName] = @__normalizedUserName_0
2025-06-02 12:50:05.692 +03:30 [INF] Finished database seeding
2025-06-02 12:50:05.722 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [AppSettings] AS [a]
        WHERE [a].[Name] = N'CompanyName') THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-02 12:50:05.732 +03:30 [INF] Database seeding completed
2025-06-02 12:50:06.217 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-02 12:50:06.220 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-02 12:50:06.285 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 12:50:06.288 +03:30 [INF] Hosting environment: Development
2025-06-02 12:50:06.289 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-02 12:50:06.292 +03:30 [INF] The application has started
2025-06-02 12:50:08.131 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 12:50:08.513 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:50:08.548 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 12:50:08.725 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 12:50:08.734 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 1.2685ms.
2025-06-02 12:50:08.760 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 12:50:08.807 +03:30 [INF] Executed ViewResult - view Index executed in 53.4375ms.
2025-06-02 12:50:08.811 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 229.2029ms
2025-06-02 12:50:08.813 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:50:08.823 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 0 text/html; charset=utf-8 699.5758ms
2025-06-02 12:50:08.907 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-02 12:50:09.128 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-02 12:50:09.131 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 499 5430 image/x-icon 224.4171ms
2025-06-02 12:50:23.455 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 12:50:23.519 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:50:23.524 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 12:50:23.553 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 12:50:23.557 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0463ms.
2025-06-02 12:50:23.560 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 12:50:23.563 +03:30 [INF] Executed ViewResult - view Index executed in 3.9205ms.
2025-06-02 12:50:23.565 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 38.2274ms
2025-06-02 12:50:23.567 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:50:23.570 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 0 text/html; charset=utf-8 114.5586ms
2025-06-02 12:50:32.312 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/home - null null
2025-06-02 12:50:32.368 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:50:32.372 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 12:50:32.404 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 12:50:32.408 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0252ms.
2025-06-02 12:50:32.410 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 12:50:32.412 +03:30 [INF] Executed ViewResult - view Index executed in 1.084ms.
2025-06-02 12:50:32.413 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 37.7285ms
2025-06-02 12:50:32.414 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:50:32.415 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/home - 200 0 text/html; charset=utf-8 103.0893ms
2025-06-02 12:50:39.286 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/home/<USER>
2025-06-02 12:50:39.329 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:50:39.332 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 12:50:39.366 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 12:50:39.370 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0239ms.
2025-06-02 12:50:39.372 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 12:50:39.374 +03:30 [INF] Executed ViewResult - view Index executed in 1.6492ms.
2025-06-02 12:50:39.375 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 38.2444ms
2025-06-02 12:50:39.376 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:50:39.378 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/home/<USER>/html; charset=utf-8 91.5991ms
2025-06-02 12:51:15.734 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/home/<USER>
2025-06-02 12:51:15.780 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:51:15.783 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 12:51:15.810 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 12:51:15.813 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0333ms.
2025-06-02 12:51:15.817 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 12:51:15.819 +03:30 [INF] Executed ViewResult - view Index executed in 2.6518ms.
2025-06-02 12:51:15.821 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 34.9876ms
2025-06-02 12:51:15.823 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:51:15.825 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/home/<USER>/html; charset=utf-8 90.8656ms
2025-06-02 12:51:32.820 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/home/<USER>
2025-06-02 12:51:32.862 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:51:32.867 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 12:51:32.896 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 12:51:32.899 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0326ms.
2025-06-02 12:51:32.902 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 12:51:36.520 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 3651.1229ms
2025-06-02 12:51:36.623 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:51:36.753 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: RenderBody has not been called for the page at '/Views/Shared/Layout2.cshtml'. To ignore call IgnoreBody().
   at Microsoft.AspNetCore.Mvc.Razor.RazorPage.EnsureRenderedBodyOrSections()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 12:51:36.893 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/home/<USER>/html; charset=utf-8 4073.2568ms
2025-06-02 12:51:36.947 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 12:51:36.948 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 12:51:36.960 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 12.4434ms
2025-06-02 12:51:37.005 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 57.0989ms
2025-06-02 12:52:22.327 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/home/<USER>
2025-06-02 12:52:22.393 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:52:22.397 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 12:52:22.428 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 12:52:22.433 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0224ms.
2025-06-02 12:52:22.435 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 12:52:23.150 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 748.2878ms
2025-06-02 12:52:23.255 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:52:23.387 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: RenderBody has not been called for the page at '/Views/Shared/Layout2.cshtml'. To ignore call IgnoreBody().
   at Microsoft.AspNetCore.Mvc.Razor.RazorPage.EnsureRenderedBodyOrSections()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 12:52:23.420 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/home/<USER>/html; charset=utf-8 1088.7199ms
2025-06-02 12:52:23.465 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 12:52:23.470 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 12:52:23.484 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 14.0896ms
2025-06-02 12:52:23.507 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.3085ms
2025-06-02 12:52:38.998 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/home/<USER>
2025-06-02 12:52:39.040 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:52:39.043 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 12:52:39.080 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 12:52:39.084 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0218ms.
2025-06-02 12:52:39.086 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 12:52:39.842 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 795.648ms
2025-06-02 12:52:39.940 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:52:40.083 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: RenderBody has not been called for the page at '/Views/Shared/Layout2.cshtml'. To ignore call IgnoreBody().
   at Microsoft.AspNetCore.Mvc.Razor.RazorPage.EnsureRenderedBodyOrSections()
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 12:52:40.104 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/home/<USER>/html; charset=utf-8 1105.8322ms
2025-06-02 12:52:40.159 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 12:52:40.161 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 12:52:40.169 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 7.5933ms
2025-06-02 12:52:40.183 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 24.5798ms
2025-06-02 12:53:22.968 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 12:53:24.175 +03:30 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-02 12:53:24.251 +03:30 [INF] Executed DbCommand (49ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-02 12:53:24.281 +03:30 [INF] Starting database seeding
2025-06-02 12:53:24.694 +03:30 [INF] Executed DbCommand (24ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 12:53:24.768 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 12:53:24.801 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedEmail_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0
2025-06-02 12:53:24.869 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedUserName] = @__normalizedUserName_0
2025-06-02 12:53:24.914 +03:30 [INF] Finished database seeding
2025-06-02 12:53:24.947 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [AppSettings] AS [a]
        WHERE [a].[Name] = N'CompanyName') THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-02 12:53:24.959 +03:30 [INF] Database seeding completed
2025-06-02 12:53:25.417 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-02 12:53:25.419 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-02 12:53:25.483 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 12:53:25.486 +03:30 [INF] Hosting environment: Development
2025-06-02 12:53:25.487 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-02 12:53:25.490 +03:30 [INF] The application has started
2025-06-02 12:53:27.394 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 12:53:27.810 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:53:27.840 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 12:53:27.976 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 12:53:27.985 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 1.0228ms.
2025-06-02 12:53:28.008 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 12:53:28.915 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 1065.4012ms
2025-06-02 12:53:29.010 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:53:29.152 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The layout view '~/Views/Shared/Layout2.cshtml' could not be located. The following locations were searched:
~/Views/Shared/Layout2.cshtml
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.GetLayoutPage(ViewContext context, String executingFilePath, String layoutPath)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 12:53:29.228 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 500 null text/html; charset=utf-8 1844.6084ms
2025-06-02 12:53:29.253 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 12:53:29.253 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 12:53:29.262 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 9.2451ms
2025-06-02 12:53:29.302 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 49.4513ms
2025-06-02 12:53:29.308 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-02 12:53:29.472 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-02 12:53:29.476 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 499 5430 image/x-icon 168.0504ms
2025-06-02 12:53:50.100 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 12:53:50.174 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:53:50.177 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 12:53:50.207 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 12:53:50.210 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0452ms.
2025-06-02 12:53:50.213 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 12:53:50.899 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 719.6535ms
2025-06-02 12:53:51.015 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:53:51.131 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The layout view '~/Views/Shared/Layout2.cshtml' could not be located. The following locations were searched:
~/Views/Shared/Layout2.cshtml
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.GetLayoutPage(ViewContext context, String executingFilePath, String layoutPath)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderLayoutAsync(ViewContext context, ViewBufferTextWriter bodyWriter)
   at Microsoft.AspNetCore.Mvc.Razor.RazorView.RenderAsync(ViewContext context)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ViewContext viewContext, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewExecutor.ExecuteAsync(ActionContext actionContext, IView view, ViewDataDictionary viewData, ITempDataDictionary tempData, String contentType, Nullable`1 statusCode)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-02 12:53:51.170 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 500 null text/html; charset=utf-8 1069.737ms
2025-06-02 12:53:51.188 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 12:53:51.188 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 12:53:51.214 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 25.9885ms
2025-06-02 12:53:51.227 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.8401ms
2025-06-02 12:53:58.860 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 12:53:58.915 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:53:58.920 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 12:53:58.947 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 12:53:58.950 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0246ms.
2025-06-02 12:53:58.953 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 12:53:59.002 +03:30 [INF] Executed ViewResult - view Index executed in 49.4123ms.
2025-06-02 12:53:59.009 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 87.3565ms
2025-06-02 12:53:59.013 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:53:59.018 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 157.7394ms
2025-06-02 12:53:59.037 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - null null
2025-06-02 12:53:59.041 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-02 12:53:59.078 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user2-160x160.jpg - null null
2025-06-02 12:53:59.040 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-02 12:53:59.039 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-02 12:53:59.079 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - null null
2025-06-02 12:53:59.097 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js - null null
2025-06-02 12:53:59.100 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 12:53:59.108 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 12:53:59.115 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-02 12:53:59.115 +03:30 [INF] Sending file. Request path: '/assets/img/user2-160x160.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user2-160x160.jpg'
2025-06-02 12:53:59.121 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-02 12:53:59.116 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-02 12:53:59.119 +03:30 [INF] Sending file. Request path: '/assets/img/AdminLTELogo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\AdminLTELogo.png'
2025-06-02 12:53:59.130 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 21.9225ms
2025-06-02 12:53:59.130 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 200 3398 image/jpeg 89.8069ms
2025-06-02 12:53:59.132 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user2-160x160.jpg - 200 6905 image/jpeg 54.1731ms
2025-06-02 12:53:59.135 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 200 4983 image/jpeg 95.0319ms
2025-06-02 12:53:59.137 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 200 2750 image/jpeg 98.3323ms
2025-06-02 12:53:59.139 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - 200 2637 image/png 60.2518ms
2025-06-02 12:53:59.146 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 46.0067ms
2025-06-02 12:53:59.148 +03:30 [INF] Sending file. Request path: '/css/adminlte.rtl.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\adminlte.rtl.css'
2025-06-02 12:53:59.148 +03:30 [INF] Sending file. Request path: '/js/adminlte.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\adminlte.js'
2025-06-02 12:53:59.176 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - 200 358126 text/css 138.9875ms
2025-06-02 12:53:59.179 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js - 200 29455 text/javascript 89.1767ms
2025-06-02 12:54:24.558 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/index.html - null null
2025-06-02 12:54:24.604 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/index.html - 404 0 null 45.7731ms
2025-06-02 12:54:24.633 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/index.html, Response status code: 404
2025-06-02 12:54:26.225 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 12:54:26.242 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 20.7082ms
2025-06-02 12:54:42.105 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-02 12:54:42.145 +03:30 [INF] Sending file. Request path: '/css/adminlte.rtl.css.map'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\adminlte.rtl.css.map'
2025-06-02 12:54:42.147 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 200 853328 text/plain 42.3453ms
2025-06-02 12:57:57.950 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 12:57:58.009 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:57:58.013 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 12:57:58.046 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 12:57:58.048 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0344ms.
2025-06-02 12:57:58.051 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 12:57:58.083 +03:30 [INF] Executed ViewResult - view Index executed in 32.9007ms.
2025-06-02 12:57:58.098 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 81.9104ms
2025-06-02 12:57:58.100 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 12:57:58.102 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 152.3591ms
2025-06-02 12:57:58.117 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 12:57:58.117 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 12:57:58.125 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 7.9614ms
2025-06-02 12:57:58.126 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 8.9602ms
2025-06-02 12:58:29.918 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/docs/javascript/treeview.html - null null
2025-06-02 12:58:29.980 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/docs/javascript/treeview.html - 404 0 null 61.7923ms
2025-06-02 12:58:29.987 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/docs/javascript/treeview.html, Response status code: 404
2025-06-02 12:58:53.918 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 12:58:53.925 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 6.7888ms
2025-06-02 12:59:16.854 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-02 12:59:16.912 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-02 12:59:16.914 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 59.6605ms
2025-06-02 13:00:28.028 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 13:00:28.082 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 13:00:28.086 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 13:00:28.116 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 13:00:28.119 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.033ms.
2025-06-02 13:00:28.122 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 13:00:28.151 +03:30 [INF] Executed ViewResult - view Index executed in 29.9166ms.
2025-06-02 13:00:28.155 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 66.2616ms
2025-06-02 13:00:28.157 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 13:00:28.159 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 131.0259ms
2025-06-02 13:00:28.319 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 13:00:28.325 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 13:00:28.348 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 22.803ms
2025-06-02 13:00:28.349 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 29.4392ms
2025-06-02 13:00:28.947 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-02 13:00:28.950 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-02 13:00:28.952 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 5.6888ms
2025-06-02 13:01:04.408 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-02 13:01:04.411 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-02 13:01:04.412 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 4.7163ms
2025-06-02 13:11:08.773 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 13:11:08.823 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 13:11:08.826 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 13:11:08.859 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 13:11:08.861 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0141ms.
2025-06-02 13:11:08.863 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 13:11:08.905 +03:30 [INF] Executed ViewResult - view Index executed in 42.0753ms.
2025-06-02 13:11:08.917 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 87.2203ms
2025-06-02 13:11:08.920 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 13:11:08.921 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 148.0174ms
2025-06-02 13:11:08.943 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 13:11:08.943 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 13:11:08.951 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 7.5034ms
2025-06-02 13:11:08.954 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 10.5174ms
2025-06-02 13:27:07.610 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 13:27:07.818 +03:30 [INF] Executed DbCommand (8ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-02 13:27:07.881 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__user_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ClaimType], [a].[ClaimValue], [a].[UserId]
FROM [AspNetUserClaims] AS [a]
WHERE [a].[UserId] = @__user_Id_0
2025-06-02 13:27:07.918 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-02 13:27:07.926 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 13:27:07.940 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__role_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0
2025-06-02 13:27:07.951 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 13:27:07.955 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 13:27:08.062 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 13:27:08.066 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.024ms.
2025-06-02 13:27:08.070 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 13:27:08.109 +03:30 [INF] Executed ViewResult - view Index executed in 40.1055ms.
2025-06-02 13:27:08.114 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 155.0993ms
2025-06-02 13:27:08.117 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 13:27:08.124 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 513.4662ms
2025-06-02 13:27:08.168 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 13:27:08.169 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 13:27:08.176 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 6.4536ms
2025-06-02 13:27:08.227 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 59.3192ms
2025-06-02 13:30:39.629 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 13:30:39.666 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 13:30:39.668 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 13:30:39.706 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 13:30:39.708 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0165ms.
2025-06-02 13:30:39.711 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 13:30:39.742 +03:30 [INF] Executed ViewResult - view Index executed in 32.1395ms.
2025-06-02 13:30:39.744 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 73.6943ms
2025-06-02 13:30:39.757 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 13:30:39.758 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 129.4011ms
2025-06-02 13:30:39.795 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 13:30:39.797 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 13:30:39.829 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 32.0338ms
2025-06-02 13:30:39.833 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 38.1298ms
2025-06-02 13:30:47.062 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/index2.html - null null
2025-06-02 13:30:47.095 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/index2.html - 404 0 null 32.8825ms
2025-06-02 13:30:47.101 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/index2.html, Response status code: 404
2025-06-02 13:30:50.211 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 13:30:50.217 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 5.454ms
2025-06-02 16:51:38.615 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 16:51:40.678 +03:30 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-02 16:51:40.813 +03:30 [INF] Executed DbCommand (77ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-02 16:51:40.864 +03:30 [INF] Starting database seeding
2025-06-02 16:51:41.591 +03:30 [INF] Executed DbCommand (86ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 16:51:41.729 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 16:51:41.814 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[@__normalizedEmail_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0
2025-06-02 16:51:41.895 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedUserName] = @__normalizedUserName_0
2025-06-02 16:51:41.946 +03:30 [INF] Finished database seeding
2025-06-02 16:51:41.985 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [AppSettings] AS [a]
        WHERE [a].[Name] = N'CompanyName') THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-02 16:51:41.994 +03:30 [INF] Database seeding completed
2025-06-02 16:51:42.793 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-02 16:51:42.795 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-02 16:51:43.099 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 16:51:43.101 +03:30 [INF] Hosting environment: Development
2025-06-02 16:51:43.102 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-02 16:51:43.107 +03:30 [INF] The application has started
2025-06-02 16:51:44.797 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 16:51:45.558 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-02 16:51:45.639 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__user_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ClaimType], [a].[ClaimValue], [a].[UserId]
FROM [AspNetUserClaims] AS [a]
WHERE [a].[UserId] = @__user_Id_0
2025-06-02 16:51:45.751 +03:30 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-02 16:51:45.762 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 16:51:45.777 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__role_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0
2025-06-02 16:51:45.789 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 16:51:45.808 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 16:51:45.965 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 16:51:45.971 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.8995ms.
2025-06-02 16:51:46.012 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 16:51:46.109 +03:30 [INF] Executed ViewResult - view Index executed in 125.8149ms.
2025-06-02 16:51:46.116 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 304.9702ms
2025-06-02 16:51:46.119 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 16:51:46.137 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 1351.1146ms
2025-06-02 16:51:46.154 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-02 16:51:46.154 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-02 16:51:46.155 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user2-160x160.jpg - null null
2025-06-02 16:51:46.155 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-02 16:51:46.212 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 16:51:46.212 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 16:51:46.782 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-02 16:51:46.782 +03:30 [INF] Sending file. Request path: '/assets/img/user2-160x160.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user2-160x160.jpg'
2025-06-02 16:51:46.821 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 499 4983 image/jpeg 666.6689ms
2025-06-02 16:51:46.782 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-02 16:51:46.782 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-02 16:51:46.818 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 606.143ms
2025-06-02 16:51:46.860 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user2-160x160.jpg - 499 6905 image/jpeg 668.5789ms
2025-06-02 16:51:46.869 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 499 2750 image/jpeg 714.7436ms
2025-06-02 16:51:46.873 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 499 3398 image/jpeg 717.8039ms
2025-06-02 16:51:46.903 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 691.1331ms
2025-06-02 16:51:46.931 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-02 16:51:47.098 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-02 16:51:47.101 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 499 5430 image/x-icon 169.5092ms
2025-06-02 16:53:34.919 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 16:53:34.986 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 16:53:34.988 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 16:53:35.036 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 16:53:35.037 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0408ms.
2025-06-02 16:53:35.042 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 16:53:35.060 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-02 16:53:35.067 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-02 16:53:35.098 +03:30 [INF] Executed ViewResult - view Index executed in 57.5343ms.
2025-06-02 16:53:35.101 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 109.7984ms
2025-06-02 16:53:35.102 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 16:53:35.105 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 186.4678ms
2025-06-02 16:53:35.130 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 16:53:35.131 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 16:53:35.139 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 8.1756ms
2025-06-02 16:53:35.162 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 31.5697ms
2025-06-02 16:57:57.594 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 16:57:57.667 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 16:57:57.670 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 16:57:57.698 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 16:57:57.703 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.018ms.
2025-06-02 16:57:57.705 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 16:57:57.720 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-02 16:57:57.729 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-02 16:57:57.798 +03:30 [INF] Executed ViewResult - view Index executed in 93.7187ms.
2025-06-02 16:57:57.805 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 133.3239ms
2025-06-02 16:57:57.808 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 16:57:57.811 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 217.2408ms
2025-06-02 16:57:57.848 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-02 16:57:57.850 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-02 16:57:58.098 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-02 16:57:58.011 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - null null
2025-06-02 16:57:57.851 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - null null
2025-06-02 16:57:58.012 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 16:57:58.226 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - 499 232911 text/css 377.9215ms
2025-06-02 16:57:58.420 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - 404 0 null 464.5805ms
2025-06-02 16:57:58.420 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-02 16:57:58.223 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-02 16:57:58.046 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 16:57:58.099 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 16:57:58.437 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 426.12ms
2025-06-02 16:57:58.441 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css, Response status code: 404
2025-06-02 16:57:58.443 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-02 16:57:58.443 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - 499 14806 text/css 591.8841ms
2025-06-02 16:57:58.446 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - 499 106394 text/css 596.0678ms
2025-06-02 16:57:58.517 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 16:57:58.459 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 360.0027ms
2025-06-02 16:57:58.452 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 406.5824ms
2025-06-02 16:57:58.583 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-02 16:57:58.592 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 89.4349ms
2025-06-02 16:57:58.596 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 16:57:58.601 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 499 45276 application/font-woff 158.6674ms
2025-06-02 16:57:58.604 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 16:58:19.054 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-02 16:58:19.054 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-02 16:58:19.069 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-02 16:58:19.070 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-02 16:58:19.071 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 17.4169ms
2025-06-02 16:58:19.072 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 18.7323ms
2025-06-02 16:59:40.575 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - null null
2025-06-02 16:59:40.580 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - 404 0 null 5.4247ms
2025-06-02 16:59:40.586 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css, Response status code: 404
2025-06-02 17:00:36.471 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-02 17:00:36.476 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-02 17:00:36.478 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 7.5196ms
2025-06-02 17:00:36.479 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-02 17:00:36.501 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-02 17:00:36.503 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 24.6577ms
2025-06-02 17:01:05.335 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 17:01:05.387 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 17:01:05.389 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 17:01:05.422 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 17:01:05.425 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0192ms.
2025-06-02 17:01:05.427 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 17:01:05.434 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-02 17:01:05.442 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-02 17:01:05.519 +03:30 [INF] Executed ViewResult - view Index executed in 92.4431ms.
2025-06-02 17:01:05.522 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 131.0653ms
2025-06-02 17:01:05.523 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 17:01:05.524 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 188.6035ms
2025-06-02 17:01:05.638 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - null null
2025-06-02 17:01:05.640 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site.css?v=KzpTWK5esQ_0WPLhbthK717l6bPmsVdZ1zTMJ4n_mWo - null null
2025-06-02 17:01:05.643 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - 404 0 null 4.7981ms
2025-06-02 17:01:05.663 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 17:01:05.679 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 17:01:05.664 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css, Response status code: 404
2025-06-02 17:01:05.679 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 17:01:05.698 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 34.9453ms
2025-06-02 17:01:05.701 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 22.0776ms
2025-06-02 17:01:05.699 +03:30 [INF] Sending file. Request path: '/css/site.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site.css'
2025-06-02 17:01:05.710 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 17:01:05.711 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 31.8148ms
2025-06-02 17:01:05.716 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site.css?v=KzpTWK5esQ_0WPLhbthK717l6bPmsVdZ1zTMJ4n_mWo - 200 20791 text/css 76.4132ms
2025-06-02 17:01:05.807 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 17:01:05.811 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 3.4162ms
2025-06-02 17:01:05.815 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 17:02:45.327 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 17:02:45.379 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 17:02:45.382 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 17:02:45.412 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 17:02:45.414 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0201ms.
2025-06-02 17:02:45.418 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 17:02:45.433 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-02 17:02:45.442 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-02 17:02:45.493 +03:30 [INF] Executed ViewResult - view Index executed in 76.6257ms.
2025-06-02 17:02:45.509 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 124.7803ms
2025-06-02 17:02:45.511 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 17:02:45.512 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 185.6857ms
2025-06-02 17:02:45.547 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - null null
2025-06-02 17:02:45.551 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - 404 0 null 4.8334ms
2025-06-02 17:02:45.552 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 17:02:45.557 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css, Response status code: 404
2025-06-02 17:02:45.561 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 17:02:45.574 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 22.4928ms
2025-06-02 17:02:45.598 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 17:02:45.571 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 17:02:45.600 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 39.529ms
2025-06-02 17:02:45.608 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 44.9431ms
2025-06-02 17:02:45.619 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 17:02:45.622 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 3.3864ms
2025-06-02 17:02:45.626 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 17:03:02.051 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/index.html - null null
2025-06-02 17:03:02.092 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/index.html - 404 0 null 41.832ms
2025-06-02 17:03:02.100 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/index.html, Response status code: 404
2025-06-02 17:03:03.709 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 17:03:03.715 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 5.7939ms
2025-06-02 17:04:11.426 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 17:04:11.474 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 17:04:11.476 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 17:04:11.507 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 17:04:11.509 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0174ms.
2025-06-02 17:04:11.511 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 17:04:11.526 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-02 17:04:11.535 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-02 17:04:11.595 +03:30 [INF] Executed ViewResult - view Index executed in 84.4589ms.
2025-06-02 17:04:11.598 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 119.7633ms
2025-06-02 17:04:11.602 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 17:04:11.604 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 177.7807ms
2025-06-02 17:04:11.626 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - null null
2025-06-02 17:04:11.632 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - 404 0 null 6.6441ms
2025-06-02 17:04:11.642 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css, Response status code: 404
2025-06-02 17:04:11.656 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 17:04:11.662 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 6.0687ms
2025-06-02 17:04:11.663 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 17:04:11.664 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 17:04:11.672 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - null null
2025-06-02 17:04:11.687 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 22.6561ms
2025-06-02 17:04:11.667 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 17:04:11.700 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 36.7996ms
2025-06-02 17:04:11.701 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - 404 0 null 29.6504ms
2025-06-02 17:04:11.716 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css, Response status code: 404
2025-06-02 17:04:11.753 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 17:04:11.757 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 3.737ms
2025-06-02 17:04:11.760 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 17:04:15.044 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 17:04:15.086 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 17:04:15.090 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 17:04:15.120 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 17:04:15.124 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.034ms.
2025-06-02 17:04:15.125 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 17:04:15.129 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-02 17:04:15.132 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-02 17:04:15.137 +03:30 [INF] Executed ViewResult - view Index executed in 11.9324ms.
2025-06-02 17:04:15.140 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 48.4021ms
2025-06-02 17:04:15.142 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 17:04:15.145 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 100.3873ms
2025-06-02 17:04:15.168 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - null null
2025-06-02 17:04:15.173 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - 404 0 null 5.1507ms
2025-06-02 17:04:15.178 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css, Response status code: 404
2025-06-02 17:04:15.184 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 17:04:15.190 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 17:04:15.190 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 17:04:15.190 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 6.1831ms
2025-06-02 17:04:15.210 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 20.6417ms
2025-06-02 17:04:15.202 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 11.3488ms
2025-06-02 17:04:15.219 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 17:04:15.235 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 17:04:15.239 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 4.1091ms
2025-06-02 17:04:15.245 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 17:04:41.952 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/clear-browser-cache - null null
2025-06-02 17:04:41.957 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/clear-browser-cache - 200 0 null 5.5199ms
2025-06-02 17:04:41.972 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css?nonce=1748871281962 - null null
2025-06-02 17:04:41.975 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css?nonce=1748871281963 - null null
2025-06-02 17:04:42.010 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css?nonce=1748871281963 - null null
2025-06-02 17:04:42.047 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css?nonce=1748871281964 - null null
2025-06-02 17:04:42.020 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css?nonce=1748871281963 - null null
2025-06-02 17:04:42.033 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css?nonce=1748871281963 - null null
2025-06-02 17:04:42.128 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-02 17:04:42.128 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-02 17:04:42.129 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-02 17:04:42.136 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css?nonce=1748871281963 - 404 0 null 116.2332ms
2025-06-02 17:04:42.137 +03:30 [INF] Sending file. Request path: '/css/adminlte.rtl.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\adminlte.rtl.css'
2025-06-02 17:04:42.139 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.css'
2025-06-02 17:04:42.140 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css?nonce=1748871281963 - 200 106394 text/css 165.5331ms
2025-06-02 17:04:42.142 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css?nonce=1748871281962 - 200 232911 text/css 170.0453ms
2025-06-02 17:04:42.145 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css?nonce=1748871281963 - 200 14806 text/css 134.4358ms
2025-06-02 17:04:42.149 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css, Response status code: 404
2025-06-02 17:04:42.151 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css?nonce=1748871281964 - 200 358126 text/css 104.1177ms
2025-06-02 17:04:42.154 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css?nonce=1748871281963 - 200 23932 text/css 120.8238ms
2025-06-02 17:05:45.638 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 17:05:45.683 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 17:05:45.687 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 17:05:45.725 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 17:05:45.727 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0322ms.
2025-06-02 17:05:45.730 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 17:05:45.745 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-02 17:05:45.753 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-02 17:05:45.883 +03:30 [INF] Executed ViewResult - view Index executed in 153.756ms.
2025-06-02 17:05:45.887 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 197.7203ms
2025-06-02 17:05:45.889 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 17:05:45.891 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 252.4112ms
2025-06-02 17:05:45.944 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - null null
2025-06-02 17:05:45.948 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery/jquery.min.js - null null
2025-06-02 17:05:45.949 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js - null null
2025-06-02 17:05:45.944 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - null null
2025-06-02 17:05:46.009 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - 404 0 null 65.1331ms
2025-06-02 17:05:45.969 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery-validation/dist/jquery.validate.min.js - null null
2025-06-02 17:05:45.995 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - null null
2025-06-02 17:05:46.008 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - null null
2025-06-02 17:05:46.021 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 17:05:46.029 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - null null
2025-06-02 17:05:46.032 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css, Response status code: 404
2025-06-02 17:05:46.046 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 25.0278ms
2025-06-02 17:05:46.061 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 17:05:46.062 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=cJHDvzXLb3gXlNMKuPnwt6yOLtwhwS9DoLLFneXi3yk - null null
2025-06-02 17:05:46.057 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js - null null
2025-06-02 17:05:46.065 +03:30 [INF] Sending file. Request path: '/js/site.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\site.js'
2025-06-02 17:05:46.075 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 17:05:46.076 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=cJHDvzXLb3gXlNMKuPnwt6yOLtwhwS9DoLLFneXi3yk - 200 2636 text/javascript 14.1861ms
2025-06-02 17:05:46.079 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 17:05:46.080 +03:30 [INF] Sending file. Request path: '/css/site.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site.css'
2025-06-02 17:05:46.084 +03:30 [INF] Sending file. Request path: '/js/adminlte.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\adminlte.js'
2025-06-02 17:05:46.085 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation/dist/jquery.validate.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js'
2025-06-02 17:05:46.088 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js'
2025-06-02 17:05:46.089 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - 200 7707 text/css 144.8006ms
2025-06-02 17:05:46.091 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js - 200 29455 text/javascript 34.3356ms
2025-06-02 17:05:46.093 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery-validation/dist/jquery.validate.min.js - 200 25308 text/javascript 124.3026ms
2025-06-02 17:05:46.095 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - 200 5824 text/javascript 100.512ms
2025-06-02 17:05:46.095 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 16.4729ms
2025-06-02 17:05:46.101 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/js/bootstrap.bundle.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\js\bootstrap.bundle.min.js'
2025-06-02 17:05:46.104 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.js'
2025-06-02 17:05:46.106 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 31.0648ms
2025-06-02 17:05:46.114 +03:30 [INF] Sending file. Request path: '/lib/jquery/jquery.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery\jquery.min.js'
2025-06-02 17:05:46.119 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js - 200 80721 text/javascript 169.6579ms
2025-06-02 17:05:46.121 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - 200 46668 text/javascript 91.8651ms
2025-06-02 17:05:46.125 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-02 17:05:46.127 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery/jquery.min.js - 200 87533 text/javascript 181.0562ms
2025-06-02 17:05:46.145 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.js'
2025-06-02 17:05:46.147 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-02 17:05:46.147 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - 200 147552 text/javascript 139.1998ms
2025-06-02 17:05:46.149 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 200 45276 application/font-woff 24.5293ms
2025-06-02 17:05:46.183 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 17:05:46.188 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 5.8572ms
2025-06-02 17:05:46.192 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 17:05:46.227 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-02 17:05:46.236 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-02 17:05:46.238 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 200 5430 image/x-icon 10.9029ms
2025-06-02 17:35:48.864 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 17:35:49.671 +03:30 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-02 17:35:49.763 +03:30 [INF] Executed DbCommand (67ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-02 17:35:49.777 +03:30 [INF] Starting database seeding
2025-06-02 17:35:50.104 +03:30 [INF] Executed DbCommand (13ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 17:35:50.174 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 17:35:50.203 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedEmail_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0
2025-06-02 17:35:50.266 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedUserName] = @__normalizedUserName_0
2025-06-02 17:35:50.302 +03:30 [INF] Finished database seeding
2025-06-02 17:35:50.333 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [AppSettings] AS [a]
        WHERE [a].[Name] = N'CompanyName') THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-02 17:35:50.343 +03:30 [INF] Database seeding completed
2025-06-02 17:35:50.525 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-02 17:35:50.527 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 17:35:50.530 +03:30 [INF] Hosting environment: Development
2025-06-02 17:35:50.531 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\basegis.web
2025-06-02 17:35:50.534 +03:30 [INF] The application has started
2025-06-02 17:35:55.386 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/ - null null
2025-06-02 17:35:55.400 +03:30 [WRN] Failed to determine the https port for redirect.
2025-06-02 17:35:55.495 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 17:35:55.541 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 17:35:55.584 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 17:35:55.696 +03:30 [INF] Executed ViewResult - view Index executed in 124.9039ms.
2025-06-02 17:35:55.704 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 153.1534ms
2025-06-02 17:35:55.706 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 17:35:55.714 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/ - 200 null text/html; charset=utf-8 333.182ms
2025-06-02 17:35:55.720 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/leaflet-draw/leaflet.draw.css - null null
2025-06-02 17:35:55.720 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/font-awesome/css/all.css - null null
2025-06-02 17:35:55.720 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/leaflet/leaflet.css - null null
2025-06-02 17:35:55.720 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/sweetalert2/sweetalert2.min.css - null null
2025-06-02 17:35:55.720 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-02 17:35:55.721 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/source-sans-3/index.css - null null
2025-06-02 17:35:55.737 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/leaflet-draw/leaflet.draw.css - 404 0 null 15.8407ms
2025-06-02 17:35:55.751 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-02 17:35:55.751 +03:30 [INF] Sending file. Request path: '/lib/source-sans-3/index.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\source-sans-3\index.css'
2025-06-02 17:35:55.751 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.css'
2025-06-02 17:35:55.751 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-02 17:35:55.754 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5200/lib/leaflet-draw/leaflet.draw.css, Response status code: 404
2025-06-02 17:35:55.754 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-02 17:35:55.755 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/font-awesome/css/all.css - 200 106394 text/css 34.2005ms
2025-06-02 17:35:55.757 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/source-sans-3/index.css - 200 2520 text/css 36.036ms
2025-06-02 17:35:55.760 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/sweetalert2/sweetalert2.min.css - 200 23932 text/css 39.2747ms
2025-06-02 17:35:55.761 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/leaflet/leaflet.css - 200 14806 text/css 40.935ms
2025-06-02 17:35:55.764 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/overlayscrollbars/styles/overlayscrollbars.min.css - null null
2025-06-02 17:35:55.766 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/bootstrap/css/bootstrap.rtl.min.css - 200 232911 text/css 45.1902ms
2025-06-02 17:35:55.769 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - null null
2025-06-02 17:35:55.773 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/css/adminlte.rtl.css - null null
2025-06-02 17:35:55.777 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - null null
2025-06-02 17:35:55.780 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/bootstrap-icons/font/bootstrap-icons.min.css - null null
2025-06-02 17:35:55.784 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/styles/overlayscrollbars.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\styles\overlayscrollbars.min.css'
2025-06-02 17:35:55.790 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\browser\overlayscrollbars.browser.es6.min.js'
2025-06-02 17:35:55.798 +03:30 [INF] Sending file. Request path: '/css/adminlte.rtl.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\adminlte.rtl.css'
2025-06-02 17:35:55.800 +03:30 [INF] Sending file. Request path: '/css/site.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site.css'
2025-06-02 17:35:55.804 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/bootstrap-icons.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\bootstrap-icons.min.css'
2025-06-02 17:35:55.805 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/overlayscrollbars/styles/overlayscrollbars.min.css - 200 13202 text/css 40.8939ms
2025-06-02 17:35:55.807 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - 200 29334 text/javascript 37.32ms
2025-06-02 17:35:55.809 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/css/adminlte.rtl.css - 200 358126 text/css 36.0118ms
2025-06-02 17:35:55.810 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - 200 7707 text/css 33.6573ms
2025-06-02 17:35:55.813 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/bootstrap-icons/font/bootstrap-icons.min.css - 200 85875 text/css 32.4242ms
2025-06-02 17:35:55.815 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/bootstrap/js/bootstrap.bundle.min.js - null null
2025-06-02 17:35:55.817 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/jquery/jquery.min.js - null null
2025-06-02 17:35:55.820 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/assets/img/user8-128x128.jpg - null null
2025-06-02 17:35:55.824 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/assets/img/user1-128x128.jpg - null null
2025-06-02 17:35:55.828 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/img/avatars/male.png - null null
2025-06-02 17:35:55.832 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/assets/img/user3-128x128.jpg - null null
2025-06-02 17:35:55.836 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/js/bootstrap.bundle.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\js\bootstrap.bundle.min.js'
2025-06-02 17:35:55.840 +03:30 [INF] Sending file. Request path: '/lib/jquery/jquery.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery\jquery.min.js'
2025-06-02 17:35:55.844 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-02 17:35:55.847 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-02 17:35:55.850 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-02 17:35:55.853 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-02 17:35:55.854 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/bootstrap/js/bootstrap.bundle.min.js - 200 80721 text/javascript 39.1556ms
2025-06-02 17:35:55.856 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/jquery/jquery.min.js - 200 87533 text/javascript 38.8032ms
2025-06-02 17:35:55.858 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/assets/img/user8-128x128.jpg - 200 4983 image/jpeg 37.5126ms
2025-06-02 17:35:55.860 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/assets/img/user1-128x128.jpg - 200 2750 image/jpeg 36.0071ms
2025-06-02 17:35:55.862 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/img/avatars/male.png - 200 268 image/png 33.7829ms
2025-06-02 17:35:55.864 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/assets/img/user3-128x128.jpg - 200 3398 image/jpeg 31.8157ms
2025-06-02 17:35:55.868 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/jquery-validation/dist/jquery.validate.min.js - null null
2025-06-02 17:35:55.871 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - null null
2025-06-02 17:35:55.875 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/leaflet/leaflet.js - null null
2025-06-02 17:35:55.879 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 17:35:55.883 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/sweetalert2/sweetalert2.min.js - null null
2025-06-02 17:35:55.887 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/popper.js/umd/popper.min.js - null null
2025-06-02 17:35:55.890 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation/dist/jquery.validate.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js'
2025-06-02 17:35:55.893 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js'
2025-06-02 17:35:55.897 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.js'
2025-06-02 17:35:55.902 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.js'
2025-06-02 17:35:55.905 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/leaflet-draw/leaflet.draw.js - 404 0 null 25.1571ms
2025-06-02 17:35:55.906 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/popper.js/umd/popper.min.js - 404 0 null 19.7554ms
2025-06-02 17:35:55.909 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/jquery-validation/dist/jquery.validate.min.js - 200 25308 text/javascript 41.2144ms
2025-06-02 17:35:55.911 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - 200 5824 text/javascript 39.8727ms
2025-06-02 17:35:55.913 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/leaflet/leaflet.js - 200 147552 text/javascript 38.0199ms
2025-06-02 17:35:55.915 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/sweetalert2/sweetalert2.min.js - 200 46668 text/javascript 31.8459ms
2025-06-02 17:35:55.919 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5200/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 17:35:55.930 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5200/lib/popper.js/umd/popper.min.js, Response status code: 404
2025-06-02 17:35:55.933 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/js/adminlte.js - null null
2025-06-02 17:35:55.939 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/js/site.js?v=cJHDvzXLb3gXlNMKuPnwt6yOLtwhwS9DoLLFneXi3yk - null null
2025-06-02 17:35:55.943 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/assets/img/AdminLTELogo.png - null null
2025-06-02 17:35:55.951 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-02 17:35:55.954 +03:30 [INF] Sending file. Request path: '/js/adminlte.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\adminlte.js'
2025-06-02 17:35:55.960 +03:30 [INF] Sending file. Request path: '/js/site.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\site.js'
2025-06-02 17:35:55.964 +03:30 [INF] Sending file. Request path: '/assets/img/AdminLTELogo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\AdminLTELogo.png'
2025-06-02 17:35:55.967 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-02 17:35:55.968 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/js/adminlte.js - 200 29455 text/javascript 34.3026ms
2025-06-02 17:35:55.969 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/js/site.js?v=cJHDvzXLb3gXlNMKuPnwt6yOLtwhwS9DoLLFneXi3yk - 200 2636 text/javascript 30.9275ms
2025-06-02 17:35:55.971 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/assets/img/AdminLTELogo.png - 200 2637 image/png 28.4178ms
2025-06-02 17:35:55.973 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - 200 130396 font/woff2 22.4024ms
2025-06-02 17:35:55.978 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/fonts/Shabnam-FD.woff - null null
2025-06-02 17:35:55.993 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-02 17:35:55.995 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/fonts/Shabnam-FD.woff - 200 45276 application/font-woff 17.0232ms
2025-06-02 17:35:56.095 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/favicon.ico - null null
2025-06-02 17:35:56.099 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-02 17:35:56.101 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/favicon.ico - 200 5430 image/x-icon 6.7054ms
2025-06-02 17:36:05.422 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Map - null null
2025-06-02 17:36:05.427 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.MapController.Index (BaseGIS.Web)'
2025-06-02 17:36:05.436 +03:30 [INF] Route matched with {action = "Index", controller = "Map"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.MapController (BaseGIS.Web).
2025-06-02 17:36:05.752 +03:30 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-02 17:36:05.845 +03:30 [INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [g].[Id], [g].[AliasName], [g].[Name], [s].[Id], [s].[AliasName], [s].[DatasetType], [s].[GroupInfoId], [s].[MaxLabelScale], [s].[MaxScale], [s].[MinLabelScale], [s].[MinScale], [s].[Name], [s].[ShortName], [s].[SimplifyFactor], [s].[ValidationRule], [s].[Id0], [s].[AliasName0], [s].[CalcPeriod], [s].[DisableRule], [s].[Editable], [s].[FieldIndex], [s].[FieldLength], [s].[FieldType], [s].[IsDisplay], [s].[IsRequired], [s].[IsUnique], [s].[Name0], [s].[SQLCalc], [s].[ShpFieldName], [s].[TableInfoId], [s].[UnitName], [s].[Updated], [s].[ValidationRule0], [s].[WebService_Period], [s].[WebService_URL]
FROM [GroupInfos] AS [g]
LEFT JOIN (
    SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [f0].[Id] AS [Id0], [f0].[AliasName] AS [AliasName0], [f0].[CalcPeriod], [f0].[DisableRule], [f0].[Editable], [f0].[FieldIndex], [f0].[FieldLength], [f0].[FieldType], [f0].[IsDisplay], [f0].[IsRequired], [f0].[IsUnique], [f0].[Name] AS [Name0], [f0].[SQLCalc], [f0].[ShpFieldName], [f0].[TableInfoId], [f0].[UnitName], [f0].[Updated], [f0].[ValidationRule] AS [ValidationRule0], [f0].[WebService_Period], [f0].[WebService_URL]
    FROM [TableInfos] AS [t0]
    LEFT JOIN [FieldInfos] AS [f0] ON [t0].[Id] = [f0].[TableInfoId]
) AS [s] ON [g].[Id] = [s].[GroupInfoId]
WHERE EXISTS (
    SELECT 1
    FROM [TableInfos] AS [t]
    WHERE [g].[Id] = [t].[GroupInfoId] AND EXISTS (
        SELECT 1
        FROM [FieldInfos] AS [f]
        WHERE [t].[Id] = [f].[TableInfoId] AND LOWER([f].[FieldType]) LIKE N'%geometry%'))
ORDER BY [g].[Id], [s].[Id]
2025-06-02 17:36:05.943 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 17:36:06.004 +03:30 [INF] Executed ViewResult - view Index executed in 62.2221ms.
2025-06-02 17:36:06.010 +03:30 [INF] Executed action BaseGIS.Web.Controllers.MapController.Index (BaseGIS.Web) in 568.9284ms
2025-06-02 17:36:06.014 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.MapController.Index (BaseGIS.Web)'
2025-06-02 17:36:06.021 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Map - 200 null text/html; charset=utf-8 599.4943ms
2025-06-02 17:36:06.036 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/mapbox-gl/dist/mapbox-gl.css - null null
2025-06-02 17:36:06.037 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/leaflet-draw/leaflet.draw.css - null null
2025-06-02 17:36:06.038 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/mapbox-gl-draw/dist/mapbox-gl-draw.css - null null
2025-06-02 17:36:06.041 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/img/logo.png - null null
2025-06-02 17:36:06.044 +03:30 [INF] Sending file. Request path: '/lib/mapbox-gl/dist/mapbox-gl.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\mapbox-gl\dist\mapbox-gl.css'
2025-06-02 17:36:06.046 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/leaflet-draw/leaflet.draw.css - 404 0 null 8.3503ms
2025-06-02 17:36:06.050 +03:30 [INF] Sending file. Request path: '/lib/mapbox-gl-draw/dist/mapbox-gl-draw.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\mapbox-gl-draw\dist\mapbox-gl-draw.css'
2025-06-02 17:36:06.054 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/mapbox-gl-draw/dist/mapbox-gl-draw.js - null null
2025-06-02 17:36:06.055 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/wellknown/wellknown.js - null null
2025-06-02 17:36:06.057 +03:30 [INF] Sending file. Request path: '/img/logo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\logo.png'
2025-06-02 17:36:06.058 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/mapbox-gl/dist/mapbox-gl.css - 200 35658 text/css 22.1078ms
2025-06-02 17:36:06.063 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5200/lib/leaflet-draw/leaflet.draw.css, Response status code: 404
2025-06-02 17:36:06.065 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/mapbox-gl-draw/dist/mapbox-gl-draw.css - 200 4492 text/css 27.29ms
2025-06-02 17:36:06.070 +03:30 [INF] Sending file. Request path: '/lib/mapbox-gl-draw/dist/mapbox-gl-draw.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\mapbox-gl-draw\dist\mapbox-gl-draw.js'
2025-06-02 17:36:06.073 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/wellknown/wellknown.js - 404 0 null 17.7875ms
2025-06-02 17:36:06.075 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/img/logo.png - 200 830 image/png 33.8172ms
2025-06-02 17:36:06.081 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 17:36:06.084 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/mapbox-gl/dist/mapbox-gl.js - null null
2025-06-02 17:36:06.086 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/signalr/dist/browser/signalr.min.js - null null
2025-06-02 17:36:06.088 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/mapbox-gl-draw/dist/mapbox-gl-draw.js - 200 79151 text/javascript 33.82ms
2025-06-02 17:36:06.092 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5200/lib/wellknown/wellknown.js, Response status code: 404
2025-06-02 17:36:06.095 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/lib/bootstrap/dist/js/bootstrap.bundle.min.js - null null
2025-06-02 17:36:06.097 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/leaflet-draw/leaflet.draw.js - 404 0 null 16.318ms
2025-06-02 17:36:06.103 +03:30 [INF] Sending file. Request path: '/lib/signalr/dist/browser/signalr.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\signalr\dist\browser\signalr.min.js'
2025-06-02 17:36:06.104 +03:30 [INF] Sending file. Request path: '/lib/mapbox-gl/dist/mapbox-gl.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\mapbox-gl\dist\mapbox-gl.js'
2025-06-02 17:36:06.120 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/dist/js/bootstrap.bundle.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js'
2025-06-02 17:36:06.124 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5200/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 17:36:06.127 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/signalr/dist/browser/signalr.min.js - 200 42189 text/javascript 40.6152ms
2025-06-02 17:36:06.129 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/mapbox-gl/dist/mapbox-gl.js - 200 985978 text/javascript 45.6344ms
2025-06-02 17:36:06.131 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/lib/bootstrap/dist/js/bootstrap.bundle.min.js - 200 80721 text/javascript 36.0839ms
2025-06-02 17:36:06.775 +03:30 [INF] Request starting HTTP/1.1 POST http://localhost:5200/featuresHub/negotiate?negotiateVersion=1 - text/plain;charset=UTF-8 0
2025-06-02 17:36:06.780 +03:30 [INF] CORS policy execution successful.
2025-06-02 17:36:06.783 +03:30 [INF] Executing endpoint '/featuresHub/negotiate'
2025-06-02 17:36:06.803 +03:30 [INF] Executed endpoint '/featuresHub/negotiate'
2025-06-02 17:36:06.806 +03:30 [INF] Request finished HTTP/1.1 POST http://localhost:5200/featuresHub/negotiate?negotiateVersion=1 - 200 316 application/json 30.4229ms
2025-06-02 17:36:06.829 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1316/806.png - null null
2025-06-02 17:36:06.830 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1315/806.png - null null
2025-06-02 17:36:06.830 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1316/805.png - null null
2025-06-02 17:36:06.853 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:06.831 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1317/806.png - null null
2025-06-02 17:36:06.843 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1315/805.png - null null
2025-06-02 17:36:06.845 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:06.850 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:06.831 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1316/807.png - null null
2025-06-02 17:36:06.868 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/featuresHub?id=7gWsJew1JQ3U5Ioo9q-WDQ - null null
2025-06-02 17:36:06.869 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:06.875 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:06.881 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:06.883 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:06.883 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:06.883 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:06.884 +03:30 [INF] CORS policy execution successful.
2025-06-02 17:36:06.885 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:06.886 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:06.887 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:06.898 +03:30 [INF] Executing endpoint '/featuresHub'
2025-06-02 17:36:07.279 +03:30 [INF] Successfully proxied OSM tile z=11, x=1315, y=805 from https://c.tile.openstreetmap.org/11/1315/805.png
2025-06-02 17:36:07.284 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:07.290 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 386.3638ms
2025-06-02 17:36:07.292 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.294 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1315/805.png - 200 17556 image/png 451.3818ms
2025-06-02 17:36:07.299 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1315/807.png - null null
2025-06-02 17:36:07.302 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.303 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:07.313 +03:30 [INF] Successfully proxied OSM tile z=11, x=1317, y=806 from https://a.tile.openstreetmap.org/11/1317/806.png
2025-06-02 17:36:07.318 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:07.319 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 419.4429ms
2025-06-02 17:36:07.320 +03:30 [INF] Successfully proxied OSM tile z=11, x=1316, y=805 from https://c.tile.openstreetmap.org/11/1316/805.png
2025-06-02 17:36:07.324 +03:30 [INF] Successfully proxied OSM tile z=11, x=1315, y=806 from https://a.tile.openstreetmap.org/11/1315/806.png
2025-06-02 17:36:07.325 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.328 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:07.329 +03:30 [INF] Successfully proxied OSM tile z=11, x=1316, y=807 from https://c.tile.openstreetmap.org/11/1316/807.png
2025-06-02 17:36:07.330 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:07.331 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1317/806.png - 200 21233 image/png 500.3843ms
2025-06-02 17:36:07.333 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 436.7419ms
2025-06-02 17:36:07.336 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:07.337 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 445.9033ms
2025-06-02 17:36:07.338 +03:30 [INF] Successfully proxied OSM tile z=11, x=1316, y=806 from https://c.tile.openstreetmap.org/11/1316/806.png
2025-06-02 17:36:07.343 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1317/805.png - null null
2025-06-02 17:36:07.345 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.347 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 440.8246ms
2025-06-02 17:36:07.357 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.351 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:07.353 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.354 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1316/805.png - 200 26238 image/png 523.7942ms
2025-06-02 17:36:07.348 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.358 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1316/807.png - 200 27185 image/png 527.5485ms
2025-06-02 17:36:07.361 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 466.5915ms
2025-06-02 17:36:07.364 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:07.368 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1317/807.png - null null
2025-06-02 17:36:07.370 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1315/806.png - 200 35868 image/png 539.6605ms
2025-06-02 17:36:07.374 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1314/805.png - null null
2025-06-02 17:36:07.376 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.382 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.385 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1314/806.png - null null
2025-06-02 17:36:07.387 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.388 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1316/806.png - 200 45985 image/png 559.6823ms
2025-06-02 17:36:07.390 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:07.394 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.395 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:07.398 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1314/807.png - null null
2025-06-02 17:36:07.401 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:07.406 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.410 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:07.495 +03:30 [INF] Successfully proxied OSM tile z=11, x=1315, y=807 from https://a.tile.openstreetmap.org/11/1315/807.png
2025-06-02 17:36:07.501 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:07.508 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 199.6252ms
2025-06-02 17:36:07.512 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.515 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1315/807.png - 200 31063 image/png 215.5864ms
2025-06-02 17:36:07.519 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1318/806.png - null null
2025-06-02 17:36:07.523 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.524 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:07.587 +03:30 [INF] Successfully proxied OSM tile z=11, x=1317, y=807 from https://a.tile.openstreetmap.org/11/1317/807.png
2025-06-02 17:36:07.591 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:07.592 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 192.5178ms
2025-06-02 17:36:07.595 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.597 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1317/807.png - 200 21217 image/png 228.2591ms
2025-06-02 17:36:07.600 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1318/805.png - null null
2025-06-02 17:36:07.602 +03:30 [INF] Successfully proxied OSM tile z=11, x=1317, y=805 from https://b.tile.openstreetmap.org/11/1317/805.png
2025-06-02 17:36:07.604 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.606 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:07.607 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:07.609 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 229.9793ms
2025-06-02 17:36:07.610 +03:30 [INF] Successfully proxied OSM tile z=11, x=1314, y=805 from https://c.tile.openstreetmap.org/11/1314/805.png
2025-06-02 17:36:07.616 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.619 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:07.620 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1317/805.png - 200 19947 image/png 277.2786ms
2025-06-02 17:36:07.621 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 218.2854ms
2025-06-02 17:36:07.629 +03:30 [INF] Successfully proxied OSM tile z=11, x=1314, y=806 from https://b.tile.openstreetmap.org/11/1314/806.png
2025-06-02 17:36:07.627 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1318/807.png - null null
2025-06-02 17:36:07.629 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.632 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:07.635 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.636 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1314/805.png - 200 23497 image/png 262.0137ms
2025-06-02 17:36:07.637 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 228.5439ms
2025-06-02 17:36:07.639 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:07.647 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.651 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1314/806.png - 200 36169 image/png 265.6771ms
2025-06-02 17:36:07.653 +03:30 [INF] Successfully proxied OSM tile z=11, x=1314, y=807 from https://b.tile.openstreetmap.org/11/1314/807.png
2025-06-02 17:36:07.657 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:07.661 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 248.1802ms
2025-06-02 17:36:07.662 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.664 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1314/807.png - 200 29275 image/png 266.2841ms
2025-06-02 17:36:07.737 +03:30 [INF] Successfully proxied OSM tile z=11, x=1318, y=806 from https://a.tile.openstreetmap.org/11/1318/806.png
2025-06-02 17:36:07.740 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:07.742 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 214.3336ms
2025-06-02 17:36:07.745 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.746 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1318/806.png - 200 18286 image/png 226.9162ms
2025-06-02 17:36:07.773 +03:30 [INF] Successfully proxied OSM tile z=11, x=1318, y=805 from https://a.tile.openstreetmap.org/11/1318/805.png
2025-06-02 17:36:07.776 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:07.777 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 162.6864ms
2025-06-02 17:36:07.779 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.780 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1318/805.png - 200 16038 image/png 180.3059ms
2025-06-02 17:36:07.801 +03:30 [INF] Successfully proxied OSM tile z=11, x=1318, y=807 from https://b.tile.openstreetmap.org/11/1318/807.png
2025-06-02 17:36:07.804 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:07.806 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 156.4614ms
2025-06-02 17:36:07.808 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:07.810 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1318/807.png - 200 15536 image/png 182.2573ms
2025-06-02 17:36:17.010 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetFeatures/5 - null null
2025-06-02 17:36:17.010 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetGeometryType?tableId=5&_=1748873166565 - null null
2025-06-02 17:36:17.014 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web)'
2025-06-02 17:36:17.020 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web)'
2025-06-02 17:36:17.025 +03:30 [INF] Route matched with {action = "GetFeatures", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeatures(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:17.027 +03:30 [INF] Route matched with {action = "GetGeometryType", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetGeometryType(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:17.035 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType18`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType19, BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 17:36:17.099 +03:30 [INF] Executed DbCommand (37ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-02 17:36:17.151 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web) in 122.1505ms
2025-06-02 17:36:17.157 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web)'
2025-06-02 17:36:17.161 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetFeatures/5 - 200 null application/json; charset=utf-8 150.6931ms
2025-06-02 17:36:17.324 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType25`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-02 17:36:17.329 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web) in 297.9997ms
2025-06-02 17:36:17.333 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web)'
2025-06-02 17:36:17.335 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetGeometryType?tableId=5&_=1748873166565 - 200 null application/json; charset=utf-8 324.8275ms
2025-06-02 17:36:17.339 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetSymbologies?tableId=5&_=1748873166566 - null null
2025-06-02 17:36:17.342 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web)'
2025-06-02 17:36:17.344 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/5/10/658/403?t=1748873177006 - null null
2025-06-02 17:36:17.345 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/5/10/657/403?t=1748873177006 - null null
2025-06-02 17:36:17.346 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/5/10/657/402?t=1748873177006 - null null
2025-06-02 17:36:17.346 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/5/10/658/402?t=1748873177006 - null null
2025-06-02 17:36:17.346 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/5/10/659/402?t=1748873177006 - null null
2025-06-02 17:36:17.349 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:17.349 +03:30 [INF] Route matched with {action = "GetSymbologies", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSymbologies(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:17.351 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:17.354 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:17.358 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:17.363 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:17.366 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:17.367 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:17.368 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:17.369 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:17.371 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:17.377 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\5\10\658\403.mvt
2025-06-02 17:36:17.379 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\5\10\657\403.mvt
2025-06-02 17:36:17.388 +03:30 [WRN] Tile not found at: wwwroot/tiles\5\10\657\403.mvt
2025-06-02 17:36:17.383 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\5\10\658\402.mvt
2025-06-02 17:36:17.385 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\5\10\659\402.mvt
2025-06-02 17:36:17.386 +03:30 [WRN] Tile not found at: wwwroot/tiles\5\10\658\403.mvt
2025-06-02 17:36:17.381 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\5\10\657\402.mvt
2025-06-02 17:36:17.392 +03:30 [WRN] Tile not found at: wwwroot/tiles\5\10\658\402.mvt
2025-06-02 17:36:17.393 +03:30 [WRN] Tile not found at: wwwroot/tiles\5\10\659\402.mvt
2025-06-02 17:36:17.393 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:17.395 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:17.396 +03:30 [WRN] Tile not found at: wwwroot/tiles\5\10\657\402.mvt
2025-06-02 17:36:17.397 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:17.398 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:17.399 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 21.0646ms
2025-06-02 17:36:17.400 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 24.2276ms
2025-06-02 17:36:17.406 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:17.401 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:17.402 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 19.1319ms
2025-06-02 17:36:17.403 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 17.8323ms
2025-06-02 17:36:17.404 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:17.401 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-02 17:36:17.409 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/5/10/658/403?t=1748873177006 - 404 0 null 65.1036ms
2025-06-02 17:36:17.411 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 30.6076ms
2025-06-02 17:36:17.413 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:17.415 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:17.416 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/5/10/657/403?t=1748873177006 - 404 0 null 71.1177ms
2025-06-02 17:36:17.422 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/5/10/659/403?t=1748873177006 - null null
2025-06-02 17:36:17.424 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:17.426 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/5/10/658/402?t=1748873177006 - 404 0 null 80.2526ms
2025-06-02 17:36:17.428 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/5/10/659/402?t=1748873177006 - 404 0 null 81.863ms
2025-06-02 17:36:17.449 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:17.451 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/5/10/657/402?t=1748873177006 - 404 0 null 105.097ms
2025-06-02 17:36:17.459 +03:30 [INF] Executed DbCommand (7ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Name], [s].[Type], [s].[IsDefault], [s].[Json], [s].[FieldName], [s].[FieldAlias]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableId_0
2025-06-02 17:36:17.463 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:17.469 +03:30 [INF] Successfully retrieved symbologies for TableInfo ID 5.
2025-06-02 17:36:17.470 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\5\10\659\403.mvt
2025-06-02 17:36:17.471 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType18`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`7[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[BaseGIS.Core.Entities.SymbologyType, BaseGIS.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-02 17:36:17.473 +03:30 [WRN] Tile not found at: wwwroot/tiles\5\10\659\403.mvt
2025-06-02 17:36:17.477 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:17.478 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 8.3153ms
2025-06-02 17:36:17.479 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:17.480 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/5/10/659/403?t=1748873177006 - 404 0 null 58.7849ms
2025-06-02 17:36:17.501 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web) in 135.0252ms
2025-06-02 17:36:17.503 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web)'
2025-06-02 17:36:17.506 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetSymbologies?tableId=5&_=1748873166566 - 200 null application/json; charset=utf-8 166.6312ms
2025-06-02 17:36:18.272 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetGeometryType?tableId=12&_=1748873166567 - null null
2025-06-02 17:36:18.272 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetFeatures/12 - null null
2025-06-02 17:36:18.277 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web)'
2025-06-02 17:36:18.283 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web)'
2025-06-02 17:36:18.286 +03:30 [INF] Route matched with {action = "GetGeometryType", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetGeometryType(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:18.287 +03:30 [INF] Route matched with {action = "GetFeatures", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeatures(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:18.293 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-02 17:36:18.294 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType18`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType19, BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 17:36:18.298 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web) in 4.2987ms
2025-06-02 17:36:18.299 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web)'
2025-06-02 17:36:18.301 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetFeatures/12 - 200 null application/json; charset=utf-8 28.9417ms
2025-06-02 17:36:18.303 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType25`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-02 17:36:18.308 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web) in 17.432ms
2025-06-02 17:36:18.313 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web)'
2025-06-02 17:36:18.314 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetGeometryType?tableId=12&_=1748873166567 - 200 null application/json; charset=utf-8 42.763ms
2025-06-02 17:36:18.319 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetSymbologies?tableId=12&_=1748873166568 - null null
2025-06-02 17:36:18.323 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web)'
2025-06-02 17:36:18.325 +03:30 [INF] Route matched with {action = "GetSymbologies", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSymbologies(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:18.327 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/657/403?t=1748873178269 - null null
2025-06-02 17:36:18.327 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/658/403?t=1748873178269 - null null
2025-06-02 17:36:18.327 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/657/402?t=1748873178269 - null null
2025-06-02 17:36:18.328 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/658/402?t=1748873178269 - null null
2025-06-02 17:36:18.328 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/659/403?t=1748873178269 - null null
2025-06-02 17:36:18.331 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-02 17:36:18.332 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:18.335 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:18.339 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:18.343 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:18.346 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:18.350 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:18.350 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Name], [s].[Type], [s].[IsDefault], [s].[Json], [s].[FieldName], [s].[FieldAlias]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableId_0
2025-06-02 17:36:18.351 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:18.352 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:18.353 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:18.355 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:18.359 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\657\403.mvt
2025-06-02 17:36:18.361 +03:30 [INF] Successfully retrieved symbologies for TableInfo ID 12.
2025-06-02 17:36:18.363 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\658\403.mvt
2025-06-02 17:36:18.365 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\657\402.mvt
2025-06-02 17:36:18.367 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\658\402.mvt
2025-06-02 17:36:18.368 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\659\403.mvt
2025-06-02 17:36:18.369 +03:30 [INF] Successfully read tile z=10, x=657, y=403 from wwwroot/tiles\12\10\657\403.mvt
2025-06-02 17:36:18.370 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType18`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`7[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[BaseGIS.Core.Entities.SymbologyType, BaseGIS.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-02 17:36:18.371 +03:30 [INF] Successfully read tile z=10, x=658, y=403 from wwwroot/tiles\12\10\658\403.mvt
2025-06-02 17:36:18.374 +03:30 [INF] Successfully read tile z=10, x=657, y=402 from wwwroot/tiles\12\10\657\402.mvt
2025-06-02 17:36:18.376 +03:30 [INF] Successfully read tile z=10, x=658, y=402 from wwwroot/tiles\12\10\658\402.mvt
2025-06-02 17:36:18.378 +03:30 [INF] Successfully read tile z=10, x=659, y=403 from wwwroot/tiles\12\10\659\403.mvt
2025-06-02 17:36:18.380 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:18.382 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web) in 54.1509ms
2025-06-02 17:36:18.385 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:18.386 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:18.389 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:18.391 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:18.400 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 32.3923ms
2025-06-02 17:36:18.394 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web)'
2025-06-02 17:36:18.395 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 32.2313ms
2025-06-02 17:36:18.397 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 32.205ms
2025-06-02 17:36:18.399 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 32.3511ms
2025-06-02 17:36:18.392 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 33.512ms
2025-06-02 17:36:18.403 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:18.408 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetSymbologies?tableId=12&_=1748873166568 - 200 null application/json; charset=utf-8 88.9891ms
2025-06-02 17:36:18.410 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:18.412 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:18.414 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:18.417 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:18.419 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/659/403?t=1748873178269 - 200 3841 application/vnd.mapbox-vector-tile 90.7871ms
2025-06-02 17:36:18.424 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/658/403?t=1748873178269 - 200 5070 application/vnd.mapbox-vector-tile 96.6858ms
2025-06-02 17:36:18.426 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/657/402?t=1748873178269 - 200 4131 application/vnd.mapbox-vector-tile 98.3817ms
2025-06-02 17:36:18.428 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/658/402?t=1748873178269 - 200 3515 application/vnd.mapbox-vector-tile 99.8339ms
2025-06-02 17:36:18.429 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/657/403?t=1748873178269 - 200 8251 application/vnd.mapbox-vector-tile 102.0706ms
2025-06-02 17:36:18.450 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/659/402?t=1748873178269 - null null
2025-06-02 17:36:18.453 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:18.455 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:18.458 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\659\402.mvt
2025-06-02 17:36:18.460 +03:30 [INF] Successfully read tile z=10, x=659, y=402 from wwwroot/tiles\12\10\659\402.mvt
2025-06-02 17:36:18.461 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:18.464 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 6.0507ms
2025-06-02 17:36:18.466 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:18.468 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/659/402?t=1748873178269 - 200 2721 application/vnd.mapbox-vector-tile 17.6152ms
2025-06-02 17:36:21.261 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetGeometryType?tableId=12&_=1748873166569 - null null
2025-06-02 17:36:21.262 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetFeatures/12 - null null
2025-06-02 17:36:21.265 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web)'
2025-06-02 17:36:21.268 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web)'
2025-06-02 17:36:21.270 +03:30 [INF] Route matched with {action = "GetGeometryType", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetGeometryType(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:21.272 +03:30 [INF] Route matched with {action = "GetFeatures", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeatures(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:21.277 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-02 17:36:21.280 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType18`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType19, BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 17:36:21.284 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType25`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-02 17:36:21.284 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web) in 5.3662ms
2025-06-02 17:36:21.286 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web) in 10.647ms
2025-06-02 17:36:21.287 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web)'
2025-06-02 17:36:21.289 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web)'
2025-06-02 17:36:21.291 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetFeatures/12 - 200 null application/json; charset=utf-8 29.3094ms
2025-06-02 17:36:21.293 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetGeometryType?tableId=12&_=1748873166569 - 200 null application/json; charset=utf-8 31.4972ms
2025-06-02 17:36:21.302 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetSymbologies?tableId=12&_=1748873166570 - null null
2025-06-02 17:36:21.307 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web)'
2025-06-02 17:36:21.310 +03:30 [INF] Route matched with {action = "GetSymbologies", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSymbologies(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:21.311 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/658/403?t=1748873181259 - null null
2025-06-02 17:36:21.311 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/657/403?t=1748873181259 - null null
2025-06-02 17:36:21.311 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/658/402?t=1748873181259 - null null
2025-06-02 17:36:21.312 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/657/402?t=1748873181259 - null null
2025-06-02 17:36:21.312 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/659/402?t=1748873181259 - null null
2025-06-02 17:36:21.317 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-02 17:36:21.318 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:21.321 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:21.325 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:21.330 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:21.332 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:21.336 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Name], [s].[Type], [s].[IsDefault], [s].[Json], [s].[FieldName], [s].[FieldAlias]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableId_0
2025-06-02 17:36:21.336 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:21.338 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:21.340 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:21.342 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:21.343 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:21.346 +03:30 [INF] Successfully retrieved symbologies for TableInfo ID 12.
2025-06-02 17:36:21.348 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\658\403.mvt
2025-06-02 17:36:21.350 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\657\403.mvt
2025-06-02 17:36:21.352 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\658\402.mvt
2025-06-02 17:36:21.357 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\657\402.mvt
2025-06-02 17:36:21.360 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\659\402.mvt
2025-06-02 17:36:21.361 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType18`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`7[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[BaseGIS.Core.Entities.SymbologyType, BaseGIS.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-02 17:36:21.362 +03:30 [INF] Successfully read tile z=10, x=658, y=403 from wwwroot/tiles\12\10\658\403.mvt
2025-06-02 17:36:21.363 +03:30 [INF] Successfully read tile z=10, x=657, y=403 from wwwroot/tiles\12\10\657\403.mvt
2025-06-02 17:36:21.364 +03:30 [INF] Successfully read tile z=10, x=658, y=402 from wwwroot/tiles\12\10\658\402.mvt
2025-06-02 17:36:21.365 +03:30 [INF] Successfully read tile z=10, x=657, y=402 from wwwroot/tiles\12\10\657\402.mvt
2025-06-02 17:36:21.366 +03:30 [INF] Successfully read tile z=10, x=659, y=402 from wwwroot/tiles\12\10\659\402.mvt
2025-06-02 17:36:21.368 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web) in 53.6629ms
2025-06-02 17:36:21.371 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:21.374 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:21.377 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:21.379 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:21.380 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:21.382 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web)'
2025-06-02 17:36:21.383 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 35.9232ms
2025-06-02 17:36:21.385 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 34.9471ms
2025-06-02 17:36:21.386 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 34.3024ms
2025-06-02 17:36:21.393 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 37.6568ms
2025-06-02 17:36:21.396 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 35.6474ms
2025-06-02 17:36:21.397 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetSymbologies?tableId=12&_=1748873166570 - 200 null application/json; charset=utf-8 95.04ms
2025-06-02 17:36:21.399 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:21.400 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:21.402 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:21.405 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:21.408 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:21.413 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/658/403?t=1748873181259 - 200 5070 application/vnd.mapbox-vector-tile 102.2256ms
2025-06-02 17:36:21.415 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/657/403?t=1748873181259 - 200 8251 application/vnd.mapbox-vector-tile 103.7622ms
2025-06-02 17:36:21.416 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/658/402?t=1748873181259 - 200 3515 application/vnd.mapbox-vector-tile 104.2747ms
2025-06-02 17:36:21.417 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/657/402?t=1748873181259 - 200 4131 application/vnd.mapbox-vector-tile 105.0321ms
2025-06-02 17:36:21.418 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/659/402?t=1748873181259 - 200 2721 application/vnd.mapbox-vector-tile 105.7791ms
2025-06-02 17:36:21.422 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/659/403?t=1748873181259 - null null
2025-06-02 17:36:21.438 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:21.440 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:21.443 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\659\403.mvt
2025-06-02 17:36:21.445 +03:30 [INF] Successfully read tile z=10, x=659, y=403 from wwwroot/tiles\12\10\659\403.mvt
2025-06-02 17:36:21.446 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:21.449 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 5.7644ms
2025-06-02 17:36:21.451 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:21.452 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/659/403?t=1748873181259 - 200 3841 application/vnd.mapbox-vector-tile 30.736ms
2025-06-02 17:36:23.510 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetGeometryType?tableId=12&_=1748873166571 - null null
2025-06-02 17:36:23.510 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetFeatures/12 - null null
2025-06-02 17:36:23.514 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web)'
2025-06-02 17:36:23.516 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web)'
2025-06-02 17:36:23.518 +03:30 [INF] Route matched with {action = "GetGeometryType", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetGeometryType(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:23.519 +03:30 [INF] Route matched with {action = "GetFeatures", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeatures(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:23.524 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-02 17:36:23.526 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType18`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType19, BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 17:36:23.531 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType25`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-02 17:36:23.531 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web) in 5.58ms
2025-06-02 17:36:23.534 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web) in 12.2516ms
2025-06-02 17:36:23.536 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web)'
2025-06-02 17:36:23.537 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web)'
2025-06-02 17:36:23.539 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetFeatures/12 - 200 null application/json; charset=utf-8 28.6808ms
2025-06-02 17:36:23.540 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetGeometryType?tableId=12&_=1748873166571 - 200 null application/json; charset=utf-8 30.8239ms
2025-06-02 17:36:23.548 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetSymbologies?tableId=12&_=1748873166572 - null null
2025-06-02 17:36:23.550 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web)'
2025-06-02 17:36:23.551 +03:30 [INF] Route matched with {action = "GetSymbologies", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSymbologies(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:23.558 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/658/403?t=1748873183507 - null null
2025-06-02 17:36:23.558 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/657/403?t=1748873183507 - null null
2025-06-02 17:36:23.558 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-02 17:36:23.559 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/658/402?t=1748873183507 - null null
2025-06-02 17:36:23.559 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/657/402?t=1748873183507 - null null
2025-06-02 17:36:23.559 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/659/402?t=1748873183507 - null null
2025-06-02 17:36:23.564 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:23.566 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:23.571 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Name], [s].[Type], [s].[IsDefault], [s].[Json], [s].[FieldName], [s].[FieldAlias]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableId_0
2025-06-02 17:36:23.572 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:23.576 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:23.578 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:23.579 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:23.580 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:23.583 +03:30 [INF] Successfully retrieved symbologies for TableInfo ID 12.
2025-06-02 17:36:23.584 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:23.585 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:23.586 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:23.589 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\658\403.mvt
2025-06-02 17:36:23.592 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\657\403.mvt
2025-06-02 17:36:23.594 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType18`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`7[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[BaseGIS.Core.Entities.SymbologyType, BaseGIS.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-02 17:36:23.597 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\658\402.mvt
2025-06-02 17:36:23.600 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\657\402.mvt
2025-06-02 17:36:23.602 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\659\402.mvt
2025-06-02 17:36:23.603 +03:30 [INF] Successfully read tile z=10, x=658, y=403 from wwwroot/tiles\12\10\658\403.mvt
2025-06-02 17:36:23.604 +03:30 [INF] Successfully read tile z=10, x=657, y=403 from wwwroot/tiles\12\10\657\403.mvt
2025-06-02 17:36:23.608 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web) in 53.2478ms
2025-06-02 17:36:23.610 +03:30 [INF] Successfully read tile z=10, x=658, y=402 from wwwroot/tiles\12\10\658\402.mvt
2025-06-02 17:36:23.612 +03:30 [INF] Successfully read tile z=10, x=657, y=402 from wwwroot/tiles\12\10\657\402.mvt
2025-06-02 17:36:23.613 +03:30 [INF] Successfully read tile z=10, x=659, y=402 from wwwroot/tiles\12\10\659\402.mvt
2025-06-02 17:36:23.615 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:23.616 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:23.618 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web)'
2025-06-02 17:36:23.620 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:23.622 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:23.624 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:23.625 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 36.5881ms
2025-06-02 17:36:23.627 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 34.7694ms
2025-06-02 17:36:23.628 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetSymbologies?tableId=12&_=1748873166572 - 200 null application/json; charset=utf-8 80.3065ms
2025-06-02 17:36:23.630 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 32.7048ms
2025-06-02 17:36:23.632 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 32.3496ms
2025-06-02 17:36:23.634 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 31.8526ms
2025-06-02 17:36:23.636 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:23.638 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:23.646 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:23.648 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:23.649 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:23.650 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/658/403?t=1748873183507 - 200 5070 application/vnd.mapbox-vector-tile 92.7805ms
2025-06-02 17:36:23.651 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/657/403?t=1748873183507 - 200 8251 application/vnd.mapbox-vector-tile 93.0988ms
2025-06-02 17:36:23.652 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/658/402?t=1748873183507 - 200 3515 application/vnd.mapbox-vector-tile 93.2009ms
2025-06-02 17:36:23.654 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/657/402?t=1748873183507 - 200 4131 application/vnd.mapbox-vector-tile 94.1926ms
2025-06-02 17:36:23.655 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/659/402?t=1748873183507 - 200 2721 application/vnd.mapbox-vector-tile 95.5038ms
2025-06-02 17:36:23.660 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/659/403?t=1748873183507 - null null
2025-06-02 17:36:23.676 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:23.677 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:23.680 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\10\659\403.mvt
2025-06-02 17:36:23.680 +03:30 [INF] Successfully read tile z=10, x=659, y=403 from wwwroot/tiles\12\10\659\403.mvt
2025-06-02 17:36:23.682 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:23.683 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 4.0815ms
2025-06-02 17:36:23.686 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:23.687 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/10/659/403?t=1748873183507 - 200 3841 application/vnd.mapbox-vector-tile 27.6812ms
2025-06-02 17:36:24.966 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetGeometryType?tableId=13&_=1748873166573 - null null
2025-06-02 17:36:24.967 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetFeatures/13 - null null
2025-06-02 17:36:24.970 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web)'
2025-06-02 17:36:24.974 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web)'
2025-06-02 17:36:24.977 +03:30 [INF] Route matched with {action = "GetGeometryType", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetGeometryType(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:24.978 +03:30 [INF] Route matched with {action = "GetFeatures", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeatures(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:24.983 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-02 17:36:24.984 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType18`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType19, BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 17:36:24.990 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web) in 6.2131ms
2025-06-02 17:36:24.992 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web)'
2025-06-02 17:36:24.994 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetFeatures/13 - 200 null application/json; charset=utf-8 27.8799ms
2025-06-02 17:36:24.996 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType25`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-02 17:36:25.001 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web) in 20.174ms
2025-06-02 17:36:25.005 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web)'
2025-06-02 17:36:25.007 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetGeometryType?tableId=13&_=1748873166573 - 200 null application/json; charset=utf-8 40.9436ms
2025-06-02 17:36:25.014 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetSymbologies?tableId=13&_=1748873166574 - null null
2025-06-02 17:36:25.017 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web)'
2025-06-02 17:36:25.018 +03:30 [INF] Route matched with {action = "GetSymbologies", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSymbologies(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:25.025 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-02 17:36:25.025 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/657/403?t=1748873184964 - null null
2025-06-02 17:36:25.026 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/658/403?t=1748873184964 - null null
2025-06-02 17:36:25.026 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/657/402?t=1748873184964 - null null
2025-06-02 17:36:25.027 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/659/402?t=1748873184964 - null null
2025-06-02 17:36:25.027 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/658/402?t=1748873184964 - null null
2025-06-02 17:36:25.031 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Name], [s].[Type], [s].[IsDefault], [s].[Json], [s].[FieldName], [s].[FieldAlias]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableId_0
2025-06-02 17:36:25.033 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:25.035 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:25.038 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:25.043 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:25.046 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:25.050 +03:30 [INF] Successfully retrieved symbologies for TableInfo ID 13.
2025-06-02 17:36:25.051 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:25.052 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:25.054 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:25.056 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:25.058 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:25.060 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType18`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`7[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[BaseGIS.Core.Entities.SymbologyType, BaseGIS.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-02 17:36:25.062 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\10\657\403.mvt
2025-06-02 17:36:25.064 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\10\658\403.mvt
2025-06-02 17:36:25.066 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\10\657\402.mvt
2025-06-02 17:36:25.068 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\10\659\402.mvt
2025-06-02 17:36:25.072 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\10\658\402.mvt
2025-06-02 17:36:25.075 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web) in 54.0017ms
2025-06-02 17:36:25.077 +03:30 [WRN] Tile not found at: wwwroot/tiles\13\10\657\403.mvt
2025-06-02 17:36:25.079 +03:30 [WRN] Tile not found at: wwwroot/tiles\13\10\658\403.mvt
2025-06-02 17:36:25.080 +03:30 [WRN] Tile not found at: wwwroot/tiles\13\10\657\402.mvt
2025-06-02 17:36:25.086 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:25.082 +03:30 [WRN] Tile not found at: wwwroot/tiles\13\10\658\402.mvt
2025-06-02 17:36:25.083 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web)'
2025-06-02 17:36:25.084 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:25.085 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:25.081 +03:30 [WRN] Tile not found at: wwwroot/tiles\13\10\659\402.mvt
2025-06-02 17:36:25.087 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 21.3257ms
2025-06-02 17:36:25.089 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:25.091 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetSymbologies?tableId=13&_=1748873166574 - 200 null application/json; charset=utf-8 76.7635ms
2025-06-02 17:36:25.092 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 29.9393ms
2025-06-02 17:36:25.094 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 29.539ms
2025-06-02 17:36:25.103 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:25.097 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:25.098 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 25.8631ms
2025-06-02 17:36:25.100 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/659/403?t=1748873184964 - null null
2025-06-02 17:36:25.102 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:25.095 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:25.106 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/658/403?t=1748873184964 - 404 0 null 79.4795ms
2025-06-02 17:36:25.107 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/657/402?t=1748873184964 - 404 0 null 80.7856ms
2025-06-02 17:36:25.109 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:25.112 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:25.114 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/657/403?t=1748873184964 - 404 0 null 88.7428ms
2025-06-02 17:36:25.115 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 46.8008ms
2025-06-02 17:36:25.124 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/658/402?t=1748873184964 - 404 0 null 97.1757ms
2025-06-02 17:36:25.125 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:25.129 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:25.134 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\10\659\403.mvt
2025-06-02 17:36:25.135 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/659/402?t=1748873184964 - 404 0 null 108.3176ms
2025-06-02 17:36:25.138 +03:30 [WRN] Tile not found at: wwwroot/tiles\13\10\659\403.mvt
2025-06-02 17:36:25.143 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:25.143 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 9.1454ms
2025-06-02 17:36:25.144 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:25.145 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/659/403?t=1748873184964 - 404 0 null 45.1485ms
2025-06-02 17:36:26.222 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetGeometryType?tableId=13&_=1748873166575 - null null
2025-06-02 17:36:26.223 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetFeatures/13 - null null
2025-06-02 17:36:26.227 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web)'
2025-06-02 17:36:26.230 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web)'
2025-06-02 17:36:26.231 +03:30 [INF] Route matched with {action = "GetGeometryType", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetGeometryType(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:26.233 +03:30 [INF] Route matched with {action = "GetFeatures", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetFeatures(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:26.237 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableId_0
2025-06-02 17:36:26.238 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType18`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType19, BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-02 17:36:26.244 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType25`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-02 17:36:26.245 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web) in 6.9449ms
2025-06-02 17:36:26.246 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web) in 11.3298ms
2025-06-02 17:36:26.248 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetFeatures (BaseGIS.Web)'
2025-06-02 17:36:26.249 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetGeometryType (BaseGIS.Web)'
2025-06-02 17:36:26.250 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetFeatures/13 - 200 null application/json; charset=utf-8 27.3779ms
2025-06-02 17:36:26.251 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetGeometryType?tableId=13&_=1748873166575 - 200 null application/json; charset=utf-8 29.0064ms
2025-06-02 17:36:26.258 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/658/403?t=1748873186220 - null null
2025-06-02 17:36:26.260 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/657/403?t=1748873186220 - null null
2025-06-02 17:36:26.260 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/657/402?t=1748873186220 - null null
2025-06-02 17:36:26.261 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/658/402?t=1748873186220 - null null
2025-06-02 17:36:26.261 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/659/403?t=1748873186220 - null null
2025-06-02 17:36:26.264 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/Feature/GetSymbologies?tableId=13&_=1748873166576 - null null
2025-06-02 17:36:26.267 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:26.271 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:26.274 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:26.277 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:26.280 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:26.282 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web)'
2025-06-02 17:36:26.284 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:26.286 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:26.287 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:26.290 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:26.293 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:26.295 +03:30 [INF] Route matched with {action = "GetSymbologies", controller = "Feature"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSymbologies(Int32) on controller BaseGIS.Web.Controllers.FeatureController (BaseGIS.Web).
2025-06-02 17:36:26.298 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\10\658\403.mvt
2025-06-02 17:36:26.300 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\10\657\403.mvt
2025-06-02 17:36:26.302 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\10\657\402.mvt
2025-06-02 17:36:26.304 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\10\658\402.mvt
2025-06-02 17:36:26.308 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\10\659\403.mvt
2025-06-02 17:36:26.311 +03:30 [WRN] Tile not found at: wwwroot/tiles\13\10\658\403.mvt
2025-06-02 17:36:26.312 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-02 17:36:26.312 +03:30 [WRN] Tile not found at: wwwroot/tiles\13\10\657\403.mvt
2025-06-02 17:36:26.314 +03:30 [WRN] Tile not found at: wwwroot/tiles\13\10\657\402.mvt
2025-06-02 17:36:26.314 +03:30 [WRN] Tile not found at: wwwroot/tiles\13\10\658\402.mvt
2025-06-02 17:36:26.315 +03:30 [WRN] Tile not found at: wwwroot/tiles\13\10\659\403.mvt
2025-06-02 17:36:26.316 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:26.319 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:26.319 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[Name], [s].[Type], [s].[IsDefault], [s].[Json], [s].[FieldName], [s].[FieldAlias]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableId_0
2025-06-02 17:36:26.320 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:26.322 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:26.324 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:26.326 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 28.7467ms
2025-06-02 17:36:26.327 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 27.673ms
2025-06-02 17:36:26.330 +03:30 [INF] Successfully retrieved symbologies for TableInfo ID 13.
2025-06-02 17:36:26.331 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 29.7657ms
2025-06-02 17:36:26.332 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 28.0133ms
2025-06-02 17:36:26.333 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 25.2707ms
2025-06-02 17:36:26.334 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:26.335 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:26.337 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType18`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType24`7[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[BaseGIS.Core.Entities.SymbologyType, BaseGIS.Core, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-02 17:36:26.339 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:26.342 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:26.344 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:26.345 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/658/403?t=1748873186220 - 404 0 null 86.787ms
2025-06-02 17:36:26.346 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/657/403?t=1748873186220 - 404 0 null 86.4452ms
2025-06-02 17:36:26.350 +03:30 [INF] Executed action BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web) in 39.292ms
2025-06-02 17:36:26.351 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/657/402?t=1748873186220 - 404 0 null 91.1457ms
2025-06-02 17:36:26.354 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/658/402?t=1748873186220 - 404 0 null 93.4382ms
2025-06-02 17:36:26.357 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/659/403?t=1748873186220 - 404 0 null 95.9971ms
2025-06-02 17:36:26.362 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/659/402?t=1748873186220 - null null
2025-06-02 17:36:26.366 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.FeatureController.GetSymbologies (BaseGIS.Web)'
2025-06-02 17:36:26.381 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:26.382 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/Feature/GetSymbologies?tableId=13&_=1748873166576 - 200 null application/json; charset=utf-8 118.2906ms
2025-06-02 17:36:26.383 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:26.392 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\10\659\402.mvt
2025-06-02 17:36:26.393 +03:30 [WRN] Tile not found at: wwwroot/tiles\13\10\659\402.mvt
2025-06-02 17:36:26.394 +03:30 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-02 17:36:26.395 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 3.6547ms
2025-06-02 17:36:26.397 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:26.398 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/10/659/402?t=1748873186220 - 404 0 null 36.1433ms
2025-06-02 17:36:28.452 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/9/329/201?t=1748873183507 - null null
2025-06-02 17:36:28.453 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/9/328/201?t=1748873183507 - null null
2025-06-02 17:36:28.455 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/9/329/201?t=1748873186220 - null null
2025-06-02 17:36:28.455 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/9/328/201?t=1748873186220 - null null
2025-06-02 17:36:28.461 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:28.467 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:28.469 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:28.474 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:28.477 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.478 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.479 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.480 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.483 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\9\329\201.mvt
2025-06-02 17:36:28.485 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\9\328\201.mvt
2025-06-02 17:36:28.489 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\9\329\201.mvt
2025-06-02 17:36:28.494 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\9\328\201.mvt
2025-06-02 17:36:28.496 +03:30 [INF] Successfully read tile z=9, x=329, y=201 from wwwroot/tiles\12\9\329\201.mvt
2025-06-02 17:36:28.497 +03:30 [INF] Successfully read tile z=9, x=328, y=201 from wwwroot/tiles\12\9\328\201.mvt
2025-06-02 17:36:28.498 +03:30 [INF] Successfully read tile z=9, x=329, y=201 from wwwroot/tiles\13\9\329\201.mvt
2025-06-02 17:36:28.499 +03:30 [INF] Successfully read tile z=9, x=328, y=201 from wwwroot/tiles\13\9\328\201.mvt
2025-06-02 17:36:28.501 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.503 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.506 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.513 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 24.1904ms
2025-06-02 17:36:28.509 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 26.4243ms
2025-06-02 17:36:28.511 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 26.0587ms
2025-06-02 17:36:28.508 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.524 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 30.0551ms
2025-06-02 17:36:28.543 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:28.521 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:28.517 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:28.519 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:28.554 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/9/329/201?t=1748873183507 - 200 15185 application/vnd.mapbox-vector-tile 102.9128ms
2025-06-02 17:36:28.546 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1313/805.png - null null
2025-06-02 17:36:28.547 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/9/328/201?t=1748873186220 - 200 15235 application/vnd.mapbox-vector-tile 91.9754ms
2025-06-02 17:36:28.551 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/9/328/201?t=1748873183507 - 200 23930 application/vnd.mapbox-vector-tile 97.7802ms
2025-06-02 17:36:28.553 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/9/329/201?t=1748873186220 - 200 11691 application/vnd.mapbox-vector-tile 98.063ms
2025-06-02 17:36:28.546 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1319/807.png - null null
2025-06-02 17:36:28.560 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1319/805.png - null null
2025-06-02 17:36:28.563 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.568 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1313/807.png - null null
2025-06-02 17:36:28.572 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1313/806.png - null null
2025-06-02 17:36:28.576 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1319/806.png - null null
2025-06-02 17:36:28.578 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.581 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.582 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.584 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.597 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.591 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.604 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.594 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.588 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.593 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.611 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.613 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/10/658/403.png - null null
2025-06-02 17:36:28.614 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/10/658/402.png - null null
2025-06-02 17:36:28.614 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/10/657/403.png - null null
2025-06-02 17:36:28.614 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/10/659/403.png - null null
2025-06-02 17:36:28.614 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/10/657/402.png - null null
2025-06-02 17:36:28.614 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/10/659/402.png - null null
2025-06-02 17:36:28.619 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.624 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.626 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.629 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.631 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.634 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.634 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.636 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.638 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.639 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.640 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.642 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.783 +03:30 [INF] Successfully proxied OSM tile z=11, x=1313, y=807 from https://c.tile.openstreetmap.org/11/1313/807.png
2025-06-02 17:36:28.791 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.801 +03:30 [INF] Connection id "0HND1RQBO7F33", Request id "0HND1RQBO7F33:00000014": the application aborted the connection.
2025-06-02 17:36:28.805 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 204.9983ms
2025-06-02 17:36:28.808 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.811 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1313/807.png - 499 14790 image/png 242.9395ms
2025-06-02 17:36:28.838 +03:30 [INF] Successfully proxied OSM tile z=11, x=1319, y=805 from https://a.tile.openstreetmap.org/11/1319/805.png
2025-06-02 17:36:28.839 +03:30 [INF] Successfully proxied OSM tile z=11, x=1319, y=806 from https://a.tile.openstreetmap.org/11/1319/806.png
2025-06-02 17:36:28.841 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.843 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.845 +03:30 [INF] Connection id "0HND1RQBO7F37", Request id "0HND1RQBO7F37:00000017": the application aborted the connection.
2025-06-02 17:36:28.846 +03:30 [INF] Connection id "0HND1RQBO7F35", Request id "0HND1RQBO7F35:00000011": the application aborted the connection.
2025-06-02 17:36:28.847 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 237.4182ms
2025-06-02 17:36:28.849 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 242.0011ms
2025-06-02 17:36:28.850 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.851 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.853 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1319/805.png - 499 11656 image/png 292.9087ms
2025-06-02 17:36:28.853 +03:30 [INF] Successfully proxied OSM tile z=11, x=1313, y=805 from https://b.tile.openstreetmap.org/11/1313/805.png
2025-06-02 17:36:28.854 +03:30 [INF] Successfully proxied OSM tile z=11, x=1313, y=806 from https://c.tile.openstreetmap.org/11/1313/806.png
2025-06-02 17:36:28.854 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1319/806.png - 499 17773 image/png 278.3003ms
2025-06-02 17:36:28.861 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.862 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.867 +03:30 [INF] Connection id "0HND1RQBO7F38", Request id "0HND1RQBO7F38:00000010": the application aborted the connection.
2025-06-02 17:36:28.867 +03:30 [INF] Successfully proxied OSM tile z=11, x=1319, y=807 from https://a.tile.openstreetmap.org/11/1319/807.png
2025-06-02 17:36:28.868 +03:30 [INF] Connection id "0HND1RQBO7F34", Request id "0HND1RQBO7F34:0000001C": the application aborted the connection.
2025-06-02 17:36:28.870 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 273.6788ms
2025-06-02 17:36:28.872 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.873 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 257.4421ms
2025-06-02 17:36:28.875 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.876 +03:30 [INF] Connection id "0HND1RQBO7F36", Request id "0HND1RQBO7F36:00000011": the application aborted the connection.
2025-06-02 17:36:28.878 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.879 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1313/805.png - 499 34604 image/png 332.7818ms
2025-06-02 17:36:28.880 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 266.9749ms
2025-06-02 17:36:28.882 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1313/806.png - 499 28255 image/png 309.947ms
2025-06-02 17:36:28.886 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.891 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1319/807.png - 499 8312 image/png 345.2081ms
2025-06-02 17:36:28.913 +03:30 [INF] Successfully proxied OSM tile z=10, x=658, y=402 from https://c.tile.openstreetmap.org/10/658/402.png
2025-06-02 17:36:28.915 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.916 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 270.2465ms
2025-06-02 17:36:28.917 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.918 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/10/658/402.png - 200 23162 image/png 304.9142ms
2025-06-02 17:36:28.923 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/10/658/404.png - null null
2025-06-02 17:36:28.926 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.927 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.928 +03:30 [INF] Successfully proxied OSM tile z=10, x=658, y=403 from https://b.tile.openstreetmap.org/10/658/403.png
2025-06-02 17:36:28.931 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.933 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 289.0434ms
2025-06-02 17:36:28.934 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.936 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/10/658/403.png - 200 36220 image/png 322.9929ms
2025-06-02 17:36:28.940 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/10/657/404.png - null null
2025-06-02 17:36:28.941 +03:30 [INF] Successfully proxied OSM tile z=10, x=659, y=403 from https://a.tile.openstreetmap.org/10/659/403.png
2025-06-02 17:36:28.943 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.945 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.946 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.947 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 297.4086ms
2025-06-02 17:36:28.952 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.953 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/10/659/403.png - 200 21279 image/png 339.0241ms
2025-06-02 17:36:28.957 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/10/659/404.png - null null
2025-06-02 17:36:28.960 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.962 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.968 +03:30 [INF] Successfully proxied OSM tile z=10, x=659, y=402 from https://c.tile.openstreetmap.org/10/659/402.png
2025-06-02 17:36:28.971 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.972 +03:30 [INF] Successfully proxied OSM tile z=10, x=657, y=402 from https://b.tile.openstreetmap.org/10/657/402.png
2025-06-02 17:36:28.972 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 317.0183ms
2025-06-02 17:36:28.975 +03:30 [INF] Successfully proxied OSM tile z=10, x=657, y=403 from https://b.tile.openstreetmap.org/10/657/403.png
2025-06-02 17:36:28.975 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.976 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.981 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/10/659/402.png - 200 17479 image/png 366.1213ms
2025-06-02 17:36:28.979 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 327.2206ms
2025-06-02 17:36:28.978 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:28.985 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/10/656/403.png - null null
2025-06-02 17:36:28.986 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.988 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 340.1136ms
2025-06-02 17:36:28.990 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.992 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/10/657/402.png - 200 22041 image/png 377.9928ms
2025-06-02 17:36:28.994 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:28.995 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:28.998 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/10/656/402.png - null null
2025-06-02 17:36:28.999 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/10/657/403.png - 200 42425 image/png 385.4229ms
2025-06-02 17:36:29.005 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.008 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/10/660/403.png - null null
2025-06-02 17:36:29.010 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.013 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.016 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.092 +03:30 [INF] Successfully proxied OSM tile z=10, x=658, y=404 from https://a.tile.openstreetmap.org/10/658/404.png
2025-06-02 17:36:29.094 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.096 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 167.001ms
2025-06-02 17:36:29.098 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.099 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/10/658/404.png - 200 19932 image/png 176.4835ms
2025-06-02 17:36:29.103 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/10/660/402.png - null null
2025-06-02 17:36:29.103 +03:30 [INF] Successfully proxied OSM tile z=10, x=657, y=404 from https://b.tile.openstreetmap.org/10/657/404.png
2025-06-02 17:36:29.108 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.110 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.111 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.112 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 162.2328ms
2025-06-02 17:36:29.117 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.118 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/10/657/404.png - 200 15366 image/png 177.8784ms
2025-06-02 17:36:29.123 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/10/656/404.png - null null
2025-06-02 17:36:29.121 +03:30 [INF] Successfully proxied OSM tile z=10, x=659, y=404 from https://b.tile.openstreetmap.org/10/659/404.png
2025-06-02 17:36:29.126 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.129 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.130 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.132 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 168.3132ms
2025-06-02 17:36:29.137 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.140 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/10/659/404.png - 200 20791 image/png 183.2485ms
2025-06-02 17:36:29.147 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/10/660/404.png - null null
2025-06-02 17:36:29.150 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.153 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.169 +03:30 [INF] Successfully proxied OSM tile z=10, x=656, y=403 from https://b.tile.openstreetmap.org/10/656/403.png
2025-06-02 17:36:29.172 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.174 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 172.9055ms
2025-06-02 17:36:29.176 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.177 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/10/656/403.png - 200 20408 image/png 192.2104ms
2025-06-02 17:36:29.180 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/9/328/202?t=1748873183507 - null null
2025-06-02 17:36:29.183 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.184 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.188 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\9\328\202.mvt
2025-06-02 17:36:29.189 +03:30 [INF] Successfully read tile z=9, x=328, y=202 from wwwroot/tiles\12\9\328\202.mvt
2025-06-02 17:36:29.191 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.192 +03:30 [INF] Successfully proxied OSM tile z=10, x=660, y=403 from https://b.tile.openstreetmap.org/10/660/403.png
2025-06-02 17:36:29.193 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 5.4376ms
2025-06-02 17:36:29.195 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.196 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.197 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 179.2479ms
2025-06-02 17:36:29.198 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/9/328/202?t=1748873183507 - 200 5571 application/vnd.mapbox-vector-tile 17.8861ms
2025-06-02 17:36:29.204 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/9/329/202?t=1748873183507 - null null
2025-06-02 17:36:29.246 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.247 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.203 +03:30 [INF] Successfully proxied OSM tile z=10, x=656, y=402 from https://c.tile.openstreetmap.org/10/656/402.png
2025-06-02 17:36:29.252 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.249 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\9\329\202.mvt
2025-06-02 17:36:29.200 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.279 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 263.9272ms
2025-06-02 17:36:29.280 +03:30 [INF] Successfully read tile z=9, x=329, y=202 from wwwroot/tiles\12\9\329\202.mvt
2025-06-02 17:36:29.281 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/10/660/403.png - 200 15282 image/png 272.2557ms
2025-06-02 17:36:29.282 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.284 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.287 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/9/330/201?t=1748873183507 - null null
2025-06-02 17:36:29.290 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/10/656/402.png - 200 34219 image/png 292.1254ms
2025-06-02 17:36:29.292 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 43.2741ms
2025-06-02 17:36:29.295 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.296 +03:30 [INF] Successfully proxied OSM tile z=10, x=660, y=402 from https://a.tile.openstreetmap.org/10/660/402.png
2025-06-02 17:36:29.299 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/9/330/202?t=1748873183507 - null null
2025-06-02 17:36:29.300 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.302 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.313 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\9\330\201.mvt
2025-06-02 17:36:29.317 +03:30 [INF] Successfully read tile z=9, x=330, y=201 from wwwroot/tiles\12\9\330\201.mvt
2025-06-02 17:36:29.307 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.309 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/9/329/202?t=1748873183507 - 200 13145 application/vnd.mapbox-vector-tile 104.5737ms
2025-06-02 17:36:29.304 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.323 +03:30 [INF] Successfully proxied OSM tile z=10, x=656, y=404 from https://a.tile.openstreetmap.org/10/656/404.png
2025-06-02 17:36:29.326 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.328 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.329 +03:30 [INF] Successfully proxied OSM tile z=10, x=660, y=404 from https://c.tile.openstreetmap.org/10/660/404.png
2025-06-02 17:36:29.331 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/9/329/202?t=1748873186220 - null null
2025-06-02 17:36:29.332 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 217.2968ms
2025-06-02 17:36:29.334 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.336 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 22.8313ms
2025-06-02 17:36:29.339 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\12\9\330\202.mvt
2025-06-02 17:36:29.342 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.344 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.346 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.347 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 212.8203ms
2025-06-02 17:36:29.349 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.350 +03:30 [INF] Successfully read tile z=9, x=330, y=202 from wwwroot/tiles\12\9\330\202.mvt
2025-06-02 17:36:29.351 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 195.1574ms
2025-06-02 17:36:29.353 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.354 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/10/660/402.png - 200 11944 image/png 250.9804ms
2025-06-02 17:36:29.357 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.359 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/9/330/201?t=1748873183507 - 200 10526 application/vnd.mapbox-vector-tile 71.8053ms
2025-06-02 17:36:29.373 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/9/330/202?t=1748873186220 - null null
2025-06-02 17:36:29.376 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.365 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\9\329\202.mvt
2025-06-02 17:36:29.367 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/9/328/202?t=1748873186220 - null null
2025-06-02 17:36:29.381 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.361 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.362 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.377 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.378 +03:30 [INF] Successfully read tile z=9, x=329, y=202 from wwwroot/tiles\13\9\329\202.mvt
2025-06-02 17:36:29.369 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/10/656/404.png - 200 10058 image/png 246.0674ms
2025-06-02 17:36:29.383 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.384 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1317/804.png - null null
2025-06-02 17:36:29.384 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1315/804.png - null null
2025-06-02 17:36:29.385 +03:30 [INF] Connection id "0HND1RQBO7F3E", Request id "0HND1RQBO7F3E:00000003": the application aborted the connection.
2025-06-02 17:36:29.385 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1318/804.png - null null
2025-06-02 17:36:29.385 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1319/806.png - null null
2025-06-02 17:36:29.385 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1314/804.png - null null
2025-06-02 17:36:29.386 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/10/660/404.png - 200 11099 image/png 238.4245ms
2025-06-02 17:36:29.388 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\9\330\202.mvt
2025-06-02 17:36:29.391 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.394 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/9/330/201?t=1748873186220 - null null
2025-06-02 17:36:29.396 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\9\328\202.mvt
2025-06-02 17:36:29.398 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.401 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.402 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 63.454ms
2025-06-02 17:36:29.405 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.408 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.411 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.414 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1316/804.png - null null
2025-06-02 17:36:29.415 +03:30 [INF] Successfully read tile z=9, x=330, y=202 from wwwroot/tiles\13\9\330\202.mvt
2025-06-02 17:36:29.416 +03:30 [INF] Connection id "0HND1RQBO7F3F", Request id "0HND1RQBO7F3F:00000005": the application aborted the connection.
2025-06-02 17:36:29.419 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.421 +03:30 [INF] Successfully read tile z=9, x=328, y=202 from wwwroot/tiles\13\9\328\202.mvt
2025-06-02 17:36:29.423 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.422 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.425 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.426 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.428 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.429 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.431 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.433 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.434 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 69.988ms
2025-06-02 17:36:29.436 +03:30 [INF] Route matched with {action = "GetTile", controller = "Tile"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetTile(Int32, Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.438 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.444 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/12/9/330/202?t=1748873183507 - 499 12024 application/vnd.mapbox-vector-tile 144.5863ms
2025-06-02 17:36:29.450 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1319/805.png - null null
2025-06-02 17:36:29.451 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1319/807.png - null null
2025-06-02 17:36:29.451 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.453 +03:30 [INF] Connection id "0HND1RQBO7F3C", Request id "0HND1RQBO7F3C:00000004": the application aborted the connection.
2025-06-02 17:36:29.454 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.458 +03:30 [INF] Attempting to read tile from: wwwroot/tiles\13\9\330\201.mvt
2025-06-02 17:36:29.459 +03:30 [INF] Connection id "0HND1RQBO7F3B", Request id "0HND1RQBO7F3B:00000004": the application aborted the connection.
2025-06-02 17:36:29.462 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1313/806.png - null null
2025-06-02 17:36:29.464 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.467 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.470 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1313/805.png - null null
2025-06-02 17:36:29.470 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 82.3175ms
2025-06-02 17:36:29.472 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/9/329/202?t=1748873186220 - 499 21995 application/vnd.mapbox-vector-tile 140.8126ms
2025-06-02 17:36:29.474 +03:30 [INF] Successfully read tile z=9, x=330, y=201 from wwwroot/tiles\13\9\330\201.mvt
2025-06-02 17:36:29.476 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 79.8013ms
2025-06-02 17:36:29.478 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.479 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.480 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.483 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.484 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.488 +03:30 [INF] Request starting HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1313/807.png - null null
2025-06-02 17:36:29.489 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.491 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.492 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.498 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.499 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/9/330/202?t=1748873186220 - 499 23962 application/vnd.mapbox-vector-tile 126.0932ms
2025-06-02 17:36:29.501 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.503 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web) in 45.3599ms
2025-06-02 17:36:29.504 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/9/328/202?t=1748873186220 - 499 15035 application/vnd.mapbox-vector-tile 136.5904ms
2025-06-02 17:36:29.513 +03:30 [INF] Route matched with {action = "ProxyOsmTile", controller = "Tile"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ProxyOsmTile(Int32, Int32, Int32) on controller BaseGIS.Web.Controllers.TileController (BaseGIS.Web).
2025-06-02 17:36:29.514 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.GetTile (BaseGIS.Web)'
2025-06-02 17:36:29.521 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/api/Tile/tile/13/9/330/201?t=1748873186220 - 200 9310 application/vnd.mapbox-vector-tile 127.1879ms
2025-06-02 17:36:29.653 +03:30 [INF] Successfully proxied OSM tile z=11, x=1319, y=806 from https://b.tile.openstreetmap.org/11/1319/806.png
2025-06-02 17:36:29.657 +03:30 [INF] Successfully proxied OSM tile z=11, x=1315, y=804 from https://b.tile.openstreetmap.org/11/1315/804.png
2025-06-02 17:36:29.659 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.661 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.663 +03:30 [INF] Connection id "0HND1RQBO7F3J", Request id "0HND1RQBO7F3J:00000001": the application aborted the connection.
2025-06-02 17:36:29.664 +03:30 [INF] Connection id "0HND1RQBO7F3H", Request id "0HND1RQBO7F3H:00000001": the application aborted the connection.
2025-06-02 17:36:29.666 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 218.3714ms
2025-06-02 17:36:29.668 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 227.5161ms
2025-06-02 17:36:29.669 +03:30 [INF] Successfully proxied OSM tile z=11, x=1318, y=804 from https://c.tile.openstreetmap.org/11/1318/804.png
2025-06-02 17:36:29.669 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.672 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.673 +03:30 [INF] Successfully proxied OSM tile z=11, x=1317, y=804 from https://c.tile.openstreetmap.org/11/1317/804.png
2025-06-02 17:36:29.675 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.675 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1319/806.png - 499 17773 image/png 290.5956ms
2025-06-02 17:36:29.677 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1315/804.png - 499 10645 image/png 292.2558ms
2025-06-02 17:36:29.679 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.680 +03:30 [INF] Connection id "0HND1RQBO7F3I", Request id "0HND1RQBO7F3I:00000001": the application aborted the connection.
2025-06-02 17:36:29.683 +03:30 [INF] Successfully proxied OSM tile z=11, x=1314, y=804 from https://c.tile.openstreetmap.org/11/1314/804.png
2025-06-02 17:36:29.691 +03:30 [INF] Connection id "0HND1RQBO7F3G", Request id "0HND1RQBO7F3G:00000001": the application aborted the connection.
2025-06-02 17:36:29.691 +03:30 [INF] Successfully proxied OSM tile z=11, x=1316, y=804 from https://b.tile.openstreetmap.org/11/1316/804.png
2025-06-02 17:36:29.693 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 247.2034ms
2025-06-02 17:36:29.695 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.696 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 253.4608ms
2025-06-02 17:36:29.702 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.699 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.699 +03:30 [INF] Successfully proxied OSM tile z=11, x=1319, y=805 from https://a.tile.openstreetmap.org/11/1319/805.png
2025-06-02 17:36:29.701 +03:30 [INF] Connection id "0HND1RQBO7F3K", Request id "0HND1RQBO7F3K:00000001": the application aborted the connection.
2025-06-02 17:36:29.698 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.704 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1317/804.png - 499 9690 image/png 319.7806ms
2025-06-02 17:36:29.705 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1318/804.png - 499 11430 image/png 320.3689ms
2025-06-02 17:36:29.706 +03:30 [INF] Successfully proxied OSM tile z=11, x=1319, y=807 from https://a.tile.openstreetmap.org/11/1319/807.png
2025-06-02 17:36:29.708 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.709 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 259.0889ms
2025-06-02 17:36:29.710 +03:30 [INF] Connection id "0HND1RQBO7F3D", Request id "0HND1RQBO7F3D:00000004": the application aborted the connection.
2025-06-02 17:36:29.720 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.721 +03:30 [INF] Connection id "0HND1RQBO7F3L", Request id "0HND1RQBO7F3L:00000001": the application aborted the connection.
2025-06-02 17:36:29.723 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.725 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 256.4109ms
2025-06-02 17:36:29.727 +03:30 [INF] Connection id "0HND1RQBO7F3M", Request id "0HND1RQBO7F3M:00000001": the application aborted the connection.
2025-06-02 17:36:29.728 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 233.8696ms
2025-06-02 17:36:29.729 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1314/804.png - 499 12700 image/png 343.7516ms
2025-06-02 17:36:29.731 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.732 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 235.8292ms
2025-06-02 17:36:29.733 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.738 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1316/804.png - 499 10642 image/png 323.9304ms
2025-06-02 17:36:29.740 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.741 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1319/805.png - 499 11656 image/png 290.743ms
2025-06-02 17:36:29.743 +03:30 [INF] Successfully proxied OSM tile z=11, x=1313, y=807 from https://c.tile.openstreetmap.org/11/1313/807.png
2025-06-02 17:36:29.745 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1319/807.png - 499 8312 image/png 294.462ms
2025-06-02 17:36:29.751 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.756 +03:30 [INF] Connection id "0HND1RQBO7F3P", Request id "0HND1RQBO7F3P:00000001": the application aborted the connection.
2025-06-02 17:36:29.758 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 238.1226ms
2025-06-02 17:36:29.759 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.761 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1313/807.png - 499 14790 image/png 272.646ms
2025-06-02 17:36:29.761 +03:30 [INF] Successfully proxied OSM tile z=11, x=1313, y=805 from https://c.tile.openstreetmap.org/11/1313/805.png
2025-06-02 17:36:29.767 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.768 +03:30 [INF] Connection id "0HND1RQBO7F3O", Request id "0HND1RQBO7F3O:00000001": the application aborted the connection.
2025-06-02 17:36:29.771 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 262.7779ms
2025-06-02 17:36:29.774 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.775 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1313/805.png - 499 34604 image/png 305.5071ms
2025-06-02 17:36:29.808 +03:30 [INF] Successfully proxied OSM tile z=11, x=1313, y=806 from https://c.tile.openstreetmap.org/11/1313/806.png
2025-06-02 17:36:29.811 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-02 17:36:29.813 +03:30 [INF] Connection id "0HND1RQBO7F3N", Request id "0HND1RQBO7F3N:00000001": the application aborted the connection.
2025-06-02 17:36:29.814 +03:30 [INF] Executed action BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web) in 308.3058ms
2025-06-02 17:36:29.816 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.TileController.ProxyOsmTile (BaseGIS.Web)'
2025-06-02 17:36:29.817 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/proxy/osm/11/1313/806.png - 499 28255 image/png 355.1371ms
2025-06-02 17:36:48.359 +03:30 [INF] Executed endpoint '/featuresHub'
2025-06-02 17:36:48.361 +03:30 [INF] Request finished HTTP/1.1 GET http://localhost:5200/featuresHub?id=7gWsJew1JQ3U5Ioo9q-WDQ - 101 null null 41493.3695ms
2025-06-02 18:52:37.340 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-02 18:52:38.653 +03:30 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-02 18:52:38.745 +03:30 [INF] Executed DbCommand (63ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-02 18:52:38.816 +03:30 [INF] Starting database seeding
2025-06-02 18:52:39.246 +03:30 [INF] Executed DbCommand (30ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 18:52:39.325 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 18:52:39.363 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedEmail_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(2) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedEmail] = @__normalizedEmail_0
2025-06-02 18:52:39.436 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedUserName] = @__normalizedUserName_0
2025-06-02 18:52:39.476 +03:30 [INF] Finished database seeding
2025-06-02 18:52:39.505 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [AppSettings] AS [a]
        WHERE [a].[Name] = N'CompanyName') THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-02 18:52:39.516 +03:30 [INF] Database seeding completed
2025-06-02 18:52:40.239 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-02 18:52:40.240 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-02 18:52:40.321 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-02 18:52:40.324 +03:30 [INF] Hosting environment: Development
2025-06-02 18:52:40.325 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-02 18:52:40.328 +03:30 [INF] The application has started
2025-06-02 18:52:48.998 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-02 18:53:10.797 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-02 18:53:10.910 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__user_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ClaimType], [a].[ClaimValue], [a].[UserId]
FROM [AspNetUserClaims] AS [a]
WHERE [a].[UserId] = @__user_Id_0
2025-06-02 18:53:11.020 +03:30 [INF] Executed DbCommand (14ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-02 18:53:11.027 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Name], [a].[NormalizedName]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-02 18:53:11.045 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__role_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0
2025-06-02 18:53:11.058 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 18:53:11.078 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-02 18:53:11.313 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-02 18:53:11.320 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.8582ms.
2025-06-02 18:53:11.473 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-02 18:53:11.541 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-02 18:53:11.672 +03:30 [INF] Executed ViewResult - view Index executed in 335.8441ms.
2025-06-02 18:53:11.680 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 599.063ms
2025-06-02 18:53:11.684 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-02 18:53:11.695 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 22709.5059ms
2025-06-02 18:53:11.761 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-02 18:53:11.761 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-02 18:53:11.774 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - null null
2025-06-02 18:53:11.761 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - null null
2025-06-02 18:53:11.786 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - null null
2025-06-02 18:53:11.795 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - null null
2025-06-02 18:53:11.807 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - null null
2025-06-02 18:53:11.817 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - null null
2025-06-02 18:53:11.847 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - null null
2025-06-02 18:53:11.847 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - null null
2025-06-02 18:53:11.847 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-02 18:53:11.847 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-02 18:53:11.849 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-02 18:53:11.850 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css - 404 0 null 32.727ms
2025-06-02 18:53:11.856 +03:30 [INF] Sending file. Request path: '/lib/source-sans-3/index.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\source-sans-3\index.css'
2025-06-02 18:53:11.861 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/avatars/male.png - null null
2025-06-02 18:53:11.866 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - null null
2025-06-02 18:53:11.866 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - null null
2025-06-02 18:53:11.867 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-02 18:53:11.870 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.css, Response status code: 404
2025-06-02 18:53:11.873 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-02 18:53:11.883 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-02 18:53:11.872 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - 200 2520 text/css 86.3106ms
2025-06-02 18:53:11.885 +03:30 [INF] Sending file. Request path: '/assets/img/AdminLTELogo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\AdminLTELogo.png'
2025-06-02 18:53:11.885 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 200 2750 image/jpeg 37.176ms
2025-06-02 18:53:11.883 +03:30 [INF] The file /css/site.css was not modified
2025-06-02 18:53:11.899 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-02 18:53:11.899 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/umd/popper.min.js - null null
2025-06-02 18:53:11.900 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 200 3398 image/jpeg 51.5533ms
2025-06-02 18:53:11.904 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/avatars/male.png - 200 268 image/png 43.2487ms
2025-06-02 18:53:11.909 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-02 18:53:11.910 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - 200 2637 image/png 44.5174ms
2025-06-02 18:53:11.914 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - null null
2025-06-02 18:53:11.916 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - 304 null text/css 49.2212ms
2025-06-02 18:53:11.925 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - null null
2025-06-02 18:53:11.933 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js - null null
2025-06-02 18:53:11.937 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=cJHDvzXLb3gXlNMKuPnwt6yOLtwhwS9DoLLFneXi3yk - null null
2025-06-02 18:53:12.206 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.js'
2025-06-02 18:53:12.330 +03:30 [INF] Sending file. Request path: '/js/adminlte.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\adminlte.js'
2025-06-02 18:53:11.937 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-02 18:53:12.074 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.js'
2025-06-02 18:53:12.081 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/styles/overlayscrollbars.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\styles\overlayscrollbars.min.css'
2025-06-02 18:53:11.988 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/umd/popper.min.js - 404 0 null 89.2486ms
2025-06-02 18:53:12.461 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - 499 46668 text/javascript 535.1343ms
2025-06-02 18:53:12.463 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js - 499 29455 text/javascript 529.8348ms
2025-06-02 18:53:12.458 +03:30 [INF] Sending file. Request path: '/js/site.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\site.js'
2025-06-02 18:53:12.469 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - 499 147552 text/javascript 554.5306ms
2025-06-02 18:53:12.471 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - 200 13202 text/css 675.729ms
2025-06-02 18:53:11.916 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 18:53:11.918 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - 200 14806 text/css 157.4285ms
2025-06-02 18:53:12.483 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/popper.js/umd/popper.min.js, Response status code: 404
2025-06-02 18:53:12.484 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-02 18:53:12.493 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.css'
2025-06-02 18:53:12.496 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=cJHDvzXLb3gXlNMKuPnwt6yOLtwhwS9DoLLFneXi3yk - 499 2636 text/javascript 559.4725ms
2025-06-02 18:53:12.506 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 596.5303ms
2025-06-02 18:53:12.508 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\browser\overlayscrollbars.browser.es6.min.js'
2025-06-02 18:53:12.514 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 598.2548ms
2025-06-02 18:53:12.519 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/bootstrap-icons.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\bootstrap-icons.min.css'
2025-06-02 18:53:12.521 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-02 18:53:12.527 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 200 4983 image/jpeg 680.0111ms
2025-06-02 18:53:12.530 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - 200 23932 text/css 759.3572ms
2025-06-02 18:53:12.541 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-02 18:53:12.545 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - 200 29334 text/javascript 738.2048ms
2025-06-02 18:53:12.548 +03:30 [INF] Sending file. Request path: '/css/adminlte.rtl.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\adminlte.rtl.css'
2025-06-02 18:53:12.552 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 18:53:12.552 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 615.3802ms
2025-06-02 18:53:12.554 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - 200 85875 text/css 706.5053ms
2025-06-02 18:53:12.557 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - 200 106394 text/css 796.9061ms
2025-06-02 18:53:12.566 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - 200 232911 text/css 806.0942ms
2025-06-02 18:53:12.574 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - 200 358126 text/css 726.6814ms
2025-06-02 18:53:12.608 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-02 18:53:12.668 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-02 18:53:12.746 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - null null
2025-06-02 18:53:12.845 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-02 18:53:12.854 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js - 404 0 null 160.0108ms
2025-06-02 18:53:12.855 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 499 45276 application/font-woff 247.176ms
2025-06-02 18:53:12.861 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-02 18:53:12.862 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-draw/leaflet.draw.js, Response status code: 404
2025-06-02 18:53:12.863 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/umd/popper.min.js - null null
2025-06-02 18:53:12.870 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - 200 130396 font/woff2 222.3692ms
2025-06-02 18:53:12.882 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/umd/popper.min.js - 404 0 null 19.3655ms
2025-06-02 18:53:12.891 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/popper.js/umd/popper.min.js, Response status code: 404
2025-06-02 18:53:50.090 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/layout/layout-rtl.html - null null
2025-06-02 18:53:50.157 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/layout/layout-rtl.html - 404 0 null 66.6975ms
2025-06-02 18:53:50.175 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/layout/layout-rtl.html, Response status code: 404
