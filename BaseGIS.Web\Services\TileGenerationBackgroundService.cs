﻿using BaseGIS.Infrastructure.Persistence;
using System.Collections.Concurrent;

namespace BaseGIS.Web.Services
{
    public class TileGenerationJob
    {
        public int TableInfoId { get; set; }
        public string WKT { get; set; }
        public int Zoom { get; set; }
        public int X { get; set; }
        public int Y { get; set; }
    }

    public class TileGenerationBackgroundService : BackgroundService
    {
        private readonly ConcurrentQueue<TileGenerationJob> _queue = new ConcurrentQueue<TileGenerationJob>();
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TileGenerationBackgroundService> _logger;

        public TileGenerationBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<TileGenerationBackgroundService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public void Enqueue(TileGenerationJob job)
        {
            _queue.Enqueue(job);
            _logger.LogInformation("Enqueued tile generation job for TableInfo ID {TableInfoId}, z={Zoom}, x={X}, y={Y}.",
                job.TableInfoId, job.Zoom, job.X, job.Y);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                if (_queue.TryDequeue(out var job))
                {
                    try
                    {
                        using (var scope = _serviceProvider.CreateScope())
                        {
                            var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                            await GenerateTile(dbContext, job);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing tile generation job for TableInfo ID {TableInfoId}, z={Zoom}, x={X}, y={Y}.",
                            job.TableInfoId, job.Zoom, job.X, job.Y);
                    }
                }
                else
                {
                    await Task.Delay(1000, stoppingToken); // منتظر جاب جدید
                }
            }
        }

        private async Task GenerateTile(ApplicationDbContext dbContext, TileGenerationJob job)
        {
            var tableInfo = await dbContext.TableInfos.FindAsync(job.TableInfoId);
            if (tableInfo == null)
            {
                _logger.LogWarning("TableInfo ID {TableInfoId} not found for tile generation.", job.TableInfoId);
                return;
            }

            // تولید تایل (مشابه منطق GenerateAndSaveTile در پاسخ‌های قبلی)
            // این بخش باید با متدهای موجود هماهنگ شود
            _logger.LogInformation("Generating tile for TableInfo ID {TableInfoId}, z={Zoom}, x={X}, y={Y}.",
                job.TableInfoId, job.Zoom, job.X, job.Y);

            // فرض می‌کنیم از MapboxTileWriter2 و NetTopologySuite استفاده می‌کنیم
            // این کد باید با متد SaveTile هماهنگ شود
        }
    }
}