!function(e){"function"==typeof define&&define.amd?define(["exports"],e):"object"==typeof exports&&"string"!=typeof exports.nodeName?e(exports):e({})}(function(e){var a,t={time:{month:["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","Červen","Červenec","Sr<PERSON>","<PERSON><PERSON>ř<PERSON>","Říjen","Listopad","Prosinec"],monthAbbr:["Led","Úno","<PERSON><PERSON>e","<PERSON>","<PERSON>v<PERSON>","Čvn","Čvc","Srp","<PERSON><PERSON><PERSON>","Říj","Lis","Pro"],dayOfWeek:["<PERSON><PERSON><PERSON>","<PERSON>ělí","Úterý","Středa","Čtvrtek","Pátek","Sobota"],dayOfWeekAbbr:["Ne","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","So"]},legend:{selector:{all:"Vše",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Obdélníkový výběr",polygon:"<PERSON><PERSON> výběr",lineX:"Horizontální výběr",lineY:"Vertikální výběr",keep:"Ponechat výběr",clear:"Zrušit výběr"}},dataView:{title:"Data",lang:["Data","Zavřít","Obnovit"]},dataZoom:{title:{zoom:"Přiblížit",back:"Oddálit"}},magicType:{title:{line:"Změnit na Spojnicový graf",bar:"Změnit na Sloupcový graf",stack:"Plošný",tiled:"Tile"}},restore:{title:"Obnovit"},saveAsImage:{title:"Uložit jako obrázek",lang:["Obrázek uložte pravým kliknutím"]}},series:{typeNames:{pie:"Výsečový graf",bar:"Sloupcový graf",line:"Spojnicový graf",scatter:"XY bodový graf",effectScatter:"Effect XY bodový graf",radar:"Paprskový graf",tree:"Strom",treemap:"Stromová mapa",boxplot:"Krabicový graf",candlestick:"Burzovní graf",k:"K spojnicový graf",heatmap:"Teplotní mapa",map:"Mapa",parallel:"Rovnoběžné souřadnice",lines:"Spojnicový graf",graph:"Graf vztahů",sankey:"Sankeyův diagram",funnel:"Trychtýř (Funnel)",gauge:"Indikátor",pictorialBar:"Obrázkový sloupcový graf",themeRiver:"Theme River Map",sunburst:"Vícevrstvý prstencový graf"}},aria:{general:{withTitle:'Toto je graf o "{title}"',withoutTitle:"Toto je graf"},series:{single:{prefix:"",withName:"{seriesName} s typem {seriesType}.",withoutName:" s typem {seriesType}."},multiple:{prefix:". Obsahuje {seriesCount} řad.",withName:" Řada {seriesId} je typu {seriesType} repreyentující {seriesName}.",withoutName:" Řada {seriesId} je typu {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"Všechna data jsou: ",partialData:"První {displayCnt} položky jsou: ",withName:"data pro {name} jsou {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}};for(a in t)t.hasOwnProperty(a)&&(e[a]=t[a])});