import { BaseComponent } from '../components/base-component.js';

class IdentifyPanelComponent extends BaseComponent {
    getDefaultOptions() {
        return {
            ...super.getDefaultOptions(),
            features: [], // featureCollection
            onFeatureSelect: null, // (feature, index) => {}
            onClose: null, // () => {}
            onZoom: null, // (feature, index) => {}
            onFlash: null, // (feature, index) => {}
            onTabChange: null // (feature, tab, index) => {}
        };
    }

    init() {
        this.currentPage = 1;
        this.totalPages = 0;
        this.features = [];
        this.visible = false;
        this.render();
    }

    show(features) {
        this.features = features || [];
        this.totalPages = this.features.length;
        this.currentPage = 1;
        this.visible = true;
        this.render();
    }

    hide() {
        this.visible = false;
        this.render();
        if (typeof this.options.onClose === 'function') this.options.onClose();
    }

    render() {
        this.element.style.display = this.visible ? 'block' : 'none';
        if (!this.visible || !this.features || this.features.length === 0) {
            this.element.innerHTML = this.visible ? '<div class="alert alert-info m-3">هیچ عارضه‌ای یافت نشد</div>' : '';
            return;
        }
        const feature = this.features[this.currentPage - 1];
        let html = `<div class="card m-0">
            <div class="card-header d-flex justify-content-between align-items-center p-2">
                <h6 class="mb-0">نتایج شناسایی</h6>
                <button type="button" class="btn btn-sm btn-outline-secondary" data-action="close"><i class="fa fa-times"></i></button>
            </div>
            <div class="card m-0">
                <div class="card-header d-flex justify-content-between align-items-center p-1">
                    <small>عارضه ${this.currentPage} از ${this.totalPages}</small>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-primary" data-action="zoom"><i class="fa fa-search-plus"></i></button>
                        <button type="button" class="btn btn-sm btn-outline-primary" data-action="flash"><i class="fa fa-bolt"></i></button>
                    </div>
                </div>
                <div class="card-body p-1">
                    <ul class="nav nav-tabs" role="tablist">
                        <li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#info-tab" data-tab="info" role="tab">اطلاعات</a></li>
                        <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#related-tab" data-tab="related" role="tab">رکوردهای مرتبط</a></li>
                        <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#docs-tab" data-tab="docs" role="tab">مستندات</a></li>
                    </ul>
                    <div class="tab-content mt-1">
                        <div class="tab-pane fade show active" id="info-tab" role="tabpanel">
                            ${this.renderInfoTab(feature)}
                        </div>
                        <div class="tab-pane fade" id="related-tab" role="tabpanel">
                            <div class="text-center p-3"><i class="fa fa-spinner fa-spin"></i> در حال بارگذاری...</div>
                        </div>
                        <div class="tab-pane fade" id="docs-tab" role="tabpanel">
                            <div class="text-center p-3"><i class="fa fa-spinner fa-spin"></i> در حال بارگذاری...</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="d-flex justify-content-center mt-1">
                <nav aria-label="Page navigation">
                    <ul class="pagination mb-0">
                        <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}"><a class="page-link" href="#" data-action="prev">قبلی</a></li>
                        <li class="page-item ${this.currentPage === this.totalPages ? 'disabled' : ''}"><a class="page-link" href="#" data-action="next">بعدی</a></li>
                    </ul>
                </nav>
            </div>
        </div>`;
        this.element.innerHTML = html;
        this.bindEvents();
    }

    renderInfoTab(feature) {
        if (!feature || !feature.properties) return '<div class="alert alert-warning">اطلاعاتی موجود نیست</div>';
        let html = '';
        for (const prop in feature.properties) {
            if (feature.properties[prop] !== null && feature.properties[prop] !== '') {
                html += `<div class="row mb-1"><div class="col-5"><small><strong>${prop}:</strong></small></div><div class="col-7"><small>${feature.properties[prop]}</small></div></div>`;
            }
        }
        return html;
    }

    bindEvents() {
        this.element.querySelector('[data-action="close"]')?.addEventListener('click', () => this.hide());
        this.element.querySelector('[data-action="zoom"]')?.addEventListener('click', () => {
            if (typeof this.options.onZoom === 'function') this.options.onZoom(this.features[this.currentPage - 1], this.currentPage - 1);
        });
        this.element.querySelector('[data-action="flash"]')?.addEventListener('click', () => {
            if (typeof this.options.onFlash === 'function') this.options.onFlash(this.features[this.currentPage - 1], this.currentPage - 1);
        });
        this.element.querySelector('[data-action="prev"]')?.addEventListener('click', e => {
            e.preventDefault();
            if (this.currentPage > 1) {
                this.currentPage--;
                this.render();
                if (typeof this.options.onFeatureSelect === 'function') this.options.onFeatureSelect(this.features[this.currentPage - 1], this.currentPage - 1);
            }
        });
        this.element.querySelector('[data-action="next"]')?.addEventListener('click', e => {
            e.preventDefault();
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
                this.render();
                if (typeof this.options.onFeatureSelect === 'function') this.options.onFeatureSelect(this.features[this.currentPage - 1], this.currentPage - 1);
            }
        });
        // تب‌ها
        this.element.querySelectorAll('.nav-link[data-tab]').forEach(tab => {
            tab.addEventListener('click', e => {
                const tabType = tab.getAttribute('data-tab');
                if (typeof this.options.onTabChange === 'function') this.options.onTabChange(this.features[this.currentPage - 1], tabType, this.currentPage - 1);
            });
        });
    }
}

window.ComponentFactory.register('identify-panel', IdentifyPanelComponent); 