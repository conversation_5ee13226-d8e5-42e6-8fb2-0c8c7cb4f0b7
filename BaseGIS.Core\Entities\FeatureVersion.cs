using System;

namespace BaseGIS.Core.Entities
{
    public class FeatureVersion
    {
        public int Id { get; set; }
        public int TableInfoId { get; set; }
        public int FeatureId { get; set; }
        public string GeometryWKT { get; set; } = string.Empty;
        public int VersionNumber { get; set; }
        public string ChangeType { get; set; } = string.Empty; // Add, Update, Delete
        public DateTime DateTime { get; set; }
        public string? UserId { get; set; }
    }
} 