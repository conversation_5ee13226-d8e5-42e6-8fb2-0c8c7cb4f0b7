/**
 * Spatial Analysis Tools Component Styles
 * استایل‌های کامپوننت ابزارهای تحلیل مکانی
 */

/* ========================================
   Spatial Analysis Toolbar
======================================== */
.spatial-analysis-toolbar {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.spatial-analysis-toolbar .btn-group {
    display: flex;
    gap: 2px;
}

.spatial-analysis-toolbar .btn {
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 4px;
    transition: all 0.2s ease;
    border: 1px solid #dee2e6;
    background: #fff;
    color: #495057;
}

.spatial-analysis-toolbar .btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.spatial-analysis-toolbar .btn.btn-primary {
    background: #007bff;
    border-color: #007bff;
    color: #fff;
}

/* ========================================
   Spatial Analysis Dialog
======================================== */
.spatial-analysis-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1050;
    display: none;
    width: 450px;
    max-width: 90vw;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid #dee2e6;
    animation: fadeInScale 0.3s ease-out;
}

.spatial-analysis-dialog .dialog-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.spatial-analysis-dialog .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.spatial-analysis-dialog .dialog-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.spatial-analysis-dialog .dialog-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
    max-height: 60vh;
}

.spatial-analysis-dialog .dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* ========================================
   Buffer Controls
======================================== */
.buffer-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.buffer-options {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
}

.buffer-options h6 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

.buffer-options .btn-group {
    display: flex;
    gap: 5px;
}

.buffer-options .btn {
    flex: 1;
    font-size: 12px;
    padding: 8px 12px;
}

/* ========================================
   Batch Operations
======================================== */
.batch-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.operation-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.operation-buttons .btn {
    text-align: left;
    padding: 10px 15px;
    font-size: 13px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.operation-buttons .btn i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

.operation-buttons .btn:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* ========================================
   Feature Popup
======================================== */
.feature-popup {
    display: flex;
    gap: 5px;
    align-items: center;
}

.feature-popup .btn {
    padding: 4px 8px;
    font-size: 11px;
    line-height: 1;
}

/* ========================================
   Form Controls
======================================== */
.spatial-analysis-dialog .form-label {
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 5px;
}

.spatial-analysis-dialog .form-control,
.spatial-analysis-dialog .form-select {
    font-size: 13px;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.spatial-analysis-dialog .form-control:focus,
.spatial-analysis-dialog .form-select:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.spatial-analysis-dialog .form-control-color {
    width: 50px;
    height: 38px;
    padding: 2px;
}

.spatial-analysis-dialog .input-group .form-select {
    border-left: none;
}

.spatial-analysis-dialog .form-check {
    display: flex;
    align-items: center;
    gap: 8px;
}

.spatial-analysis-dialog .form-check-input {
    margin: 0;
}

.spatial-analysis-dialog .form-check-label {
    font-size: 13px;
    color: #495057;
    margin: 0;
}

/* ========================================
   Responsive Design
======================================== */
@media (max-width: 768px) {
    .spatial-analysis-dialog {
        width: 95vw;
        max-height: 90vh;
    }
    
    .spatial-analysis-toolbar {
        flex-wrap: wrap;
        gap: 3px;
    }
    
    .spatial-analysis-toolbar .btn {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .buffer-options .btn-group {
        flex-direction: column;
    }
    
    .operation-buttons .btn {
        text-align: center;
    }
    
    .operation-buttons .btn:hover {
        transform: none;
    }
}

/* ========================================
   Animation Effects
======================================== */
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.spatial-analysis-toolbar {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ========================================
   Dark Theme Support
======================================== */
@media (prefers-color-scheme: dark) {
    .spatial-analysis-toolbar {
        background: rgba(33, 37, 41, 0.9);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .spatial-analysis-dialog {
        background: #212529;
        border-color: #495057;
        color: #fff;
    }
    
    .spatial-analysis-dialog .dialog-header,
    .spatial-analysis-dialog .dialog-footer {
        background: #343a40;
        border-color: #495057;
    }
    
    .spatial-analysis-dialog .dialog-header h5 {
        color: #fff;
    }
    
    .spatial-analysis-dialog .form-control,
    .spatial-analysis-dialog .form-select {
        background: #495057;
        border-color: #6c757d;
        color: #fff;
    }
    
    .spatial-analysis-dialog .form-control:focus,
    .spatial-analysis-dialog .form-select:focus {
        background: #495057;
        border-color: #007bff;
        color: #fff;
    }
}

/* ========================================
   Status Indicators
======================================== */
.buffer-status {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

.buffer-status.active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.buffer-status.processing {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.buffer-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* ========================================
   Progress Indicators
======================================== */
.analysis-progress {
    display: none;
    margin-top: 10px;
}

.analysis-progress.show {
    display: block;
}

.analysis-progress .progress {
    height: 6px;
    border-radius: 3px;
    background: #e9ecef;
}

.analysis-progress .progress-bar {
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.analysis-progress .progress-text {
    font-size: 11px;
    color: #6c757d;
    margin-top: 5px;
    text-align: center;
}
