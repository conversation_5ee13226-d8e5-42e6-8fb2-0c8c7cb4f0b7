﻿@using BaseGIS.Core.Entities
@{
    string tblid = Context.Request.Query["tblid"];
    string id = Context.Request.Query["id"];
    RelationInfo relationInfo = Model;
    List< TableInfo> tableInfos = ViewBag.ListLayers; 
}
<form action="/Database/_Layer_Relation" method="post" id="formLayerRelation">
    @Html.AntiForgeryToken()
    <input id="ID" name="ID" value="@id" type="hidden" />
    <input id="TBLID" name="TBLID" value="@tblid" type="hidden" />

    <div class="modal-header bg-light">
        <h5 class="modal-title samanFont">اطلاعات ارتباط</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>
    
    <div class="modal-body">
        <div class="d-flex justify-content-end mb-3">
            <div class="btn-group">
                <button type="button" class="btn btn-outline-success" title="جدید" id="newForm">
                    <i class="fa fa-file-o"></i>
                </button>
                <button type="button" class="btn btn-outline-danger" title="حذف">
                    <i class="fa fa-times"></i>
                </button>
            </div>
        </div>

        <div class="mb-3">
            <label for="AliasName" class="form-label">نام</label>
            <div class="input-group">
                <input id="AliasName" name="AliasName" class="form-control" type="text" 
                       placeholder="نام فارسی" value="@relationInfo.AliasName">
                <span class="input-group-text"><i class="fa fa-database"></i></span>
            </div>
        </div>

        <div class="mb-3">
            <label for="MainTable" class="form-label">نام جدول اصلی:</label>
            <select class="form-select" id="MainTable" name="MainTable" @(id != null ? "disabled" : "")>
                <option value="">@(id != null ? relationInfo.MainTable.AliasName : "")</option>
                @foreach (var item in tableInfos)
                {
                    <option value="@item.Name">@item.AliasName</option>
                }
            </select>
        </div>

        <div class="mb-3">
            <label for="RelatedTable" class="form-label">نام جدول خارجی:</label>
            <select class="form-select" id="RelatedTable" name="RelatedTable" @(id != null ? "disabled" : "")>
                <option value="">@(id != null ? relationInfo.RelatedTable.AliasName : "")</option>
                @foreach (var item in tableInfos)
                {
                    <option value="@item.Name">@item.AliasName</option>
                }
            </select>
        </div>

        <div class="mb-3">
            <label for="RelationType" class="form-label">نوع ارتباط:</label>
            <select class="form-select" id="RelationType" name="RelationType" @(id != null ? "disabled" : "")>
                <option value="">@(id != null ? relationInfo.RelationType : "")</option>
                <option value="1">1 به n</option>
                <option value="2">n به m</option>
            </select>
        </div>
    </div>
    
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            انصراف
        </button>
        <button id="submitLayerRelation" type="button" class="btn btn-success">
            ذخیره
        </button>
    </div>
</form>

<script>
    $(document).ready(function() {
        
        
        $("#submitLayerRelation").click(function () {
            saveForm();
        });
        
        $("#newForm").click(function () {
            $("#ID").val("");
            $("#AliasName").val("");
            $("#MainTable").val("").trigger('change');
            $("#RelatedTable").val("").trigger('change');
            $("#RelationType").val("").trigger('change');
        });
    });

    function saveForm() {
        var form = $("#formLayerRelation").serialize();
        $.ajax({
            type: 'POST',
            url: "../Database/_Layer_Relation",
            data: form,
            dataType: 'json',
            success: function (data) {
                if (data.success)
                    $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "green", iconSmall: "fa fa-thumbs-up bounce animated", timeout: 4000 });
                else
                    $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
            },
            error: function (data) {
                $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
            }
        });
    }
     
</script>









