using BaseGIS.Core.Entities;
using BaseGIS.Infrastructure.Persistence;
using BaseGIS.Web.Helper;
using BaseGIS.Web.Utilities;
using BaseGIS.Web.ViewModels;
using Mapsui;
using Mapsui.Layers;
using Mapsui.Rendering.Skia;
using Mapsui.Styles;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;
using NetTopologySuite.IO;
using NetTopologySuite.Features;
using ProjNet.CoordinateSystems.Transformations;
using SkiaSharp;
using System.Text.Json;
using Color = Mapsui.Styles.Color;
using TableInfo = BaseGIS.Core.Entities.TableInfo;

namespace BaseGIS.Web.Controllers
{
    /// <summary>
    /// نتیجه انتخاب لایه
    /// </summary>
    public class LayerSelectionResult
    {
        public int LayerId { get; set; }
        public string LayerName { get; set; } = string.Empty;
        public int FeatureCount { get; set; }
        public object? Features { get; set; }
    }

    [Route("rest")]
    public partial class RESTController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<RESTController> _logger;
        private readonly ILoggerFactory _loggerFactory;

        private const int DatabaseSRID = 3857;

        public RESTController(ApplicationDbContext context, IConfiguration configuration, ILogger<RESTController> logger, ILoggerFactory loggerFactory)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
            _loggerFactory = loggerFactory;
        }

        /// <summary>
        /// Handles requests for the map service description, typically for L.esri.dynamicMapLayer's initial handshake.
        /// URL: /rest/services/map/mapserver?f=json
        /// </summary>
        [HttpGet("services/{Name}/{Types}")] // Matches /rest/services/map/mapserver
        public async Task<IActionResult> ServiceDescription(string Name, string Types, [FromQuery] string f)
        {
            if (Name.ToLower() == "map" && Types.ToLower() == "mapserver" && f?.ToLower() == "json")
            {
                // اینجا اگر ShortName رو هم میخواین به کلاینت بفرستید، باید به Select اضافه کنید.
                // در حال حاضر L.esri.dynamicMapLayer فقط به name و id نیاز داره،
                // ولی اگر UI شما بخواد ShortName رو نمایش بده، باید اینجا اضافه بشه.
                var layers = _context.TableInfos.Select(t => new
                {
                    id = t.Id, // از ID واقعی (عددی) TableInfo استفاده کنید. این برای L.esri مهم است.
                    name = t.Name, // این نام در L.esri استفاده می‌شود
                    shortName = t.ShortName, // اضافه کردن ShortName اگر در TableInfo هست
                    defaultVisibility = true,
                    parentLayerId = -1,
                    subLayerIds = new int[] { },
                    minScale = 0,
                    maxScale = 0
                }).ToList();
                var serviceInfo = new
                {
                    currentVersion = 10.81, // Or whatever version you want to mimic
                    serviceDescription = "GIS Map Service",
                    mapName = "Layers",
                    description = "Dynamic map service for BaseGIS layers.",
                    spatialReference = new { wkid = DatabaseSRID, latestWkid = DatabaseSRID },
                    singleFusedMapCache = false,
                    capabilities = "Map,Query,Data",
                    layers = layers,
                    supportedImageFormatTypes = "PNG32,PNG24,PNG,JPG,DIB,TIFF,GIF",
                    supportsDynamicLayers = true,
                };

                return Ok(serviceInfo);
            }

            return NotFound($"Service description not found for path: /{Name}/{Types}. Expected /services/map/mapserver?f=json");
        }

        /// <summary>
        /// This action serves as the main entry point for L.esri.dynamicMapLayer image requests.
        /// URL structure: /rest/services/{Name}/{Types}/{Layer} (where Layer is "export")
        /// </summary>
        [HttpGet("services/{Name}/{Types}/{Layer}")]
        [HttpPost("services/{Name}/{Types}/{Layer}")]
        public async Task<IActionResult> Services(string Name, string Types, string Layer)
        {
            if (Name.ToLower() == "map" && Types.ToLower() == "mapserver")
            {
                var routeValues = new RouteValueDictionary();

                // Handle GET parameters
                foreach (var queryParam in HttpContext.Request.Query)
                {
                    routeValues.Add(queryParam.Key, queryParam.Value);
                }

                // Handle POST parameters
                if (HttpContext.Request.Method == "POST")
                {
                    var contentType = HttpContext.Request.ContentType?.ToLower();
                    if (contentType != null)
                    {
                        if (contentType.Contains("application/json"))
                        {
                            // Read JSON body
                            using var reader = new StreamReader(HttpContext.Request.Body);
                            var jsonBody = await reader.ReadToEndAsync();
                            if (!string.IsNullOrEmpty(jsonBody))
                            {
                                var jsonData = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(jsonBody);
                                foreach (var item in jsonData)
                                {
                                    switch (item.Value.ValueKind)
                                    {
                                        case JsonValueKind.String:
                                            routeValues[item.Key] = item.Value.GetString();
                                            break;
                                        case JsonValueKind.Number:
                                            routeValues[item.Key] = item.Value.GetDecimal().ToString();
                                            break;
                                        case JsonValueKind.True:
                                        case JsonValueKind.False:
                                            routeValues[item.Key] = item.Value.GetBoolean().ToString().ToLower();
                                            break;
                                        case JsonValueKind.Null:
                                            routeValues[item.Key] = null;
                                            break;
                                        default:
                                            routeValues[item.Key] = item.Value.ToString();
                                            break;
                                    }
                                }
                            }
                        }
                        else if (contentType.Contains("application/x-www-form-urlencoded") ||
                                contentType.Contains("multipart/form-data"))
                        {
                            foreach (var formParam in HttpContext.Request.Form)
                            {
                                routeValues[formParam.Key] = formParam.Value;
                            }
                        }
                    }
                }

                // Remove route parameters
                routeValues.Remove("Name");
                routeValues.Remove("Types");
                routeValues.Remove("Layer");

                if (Layer.ToLower() == "export")
                {
                    return await ExportMapImage(routeValues);
                }
                else if (Layer.Equals("zoom", StringComparison.OrdinalIgnoreCase))
                {
                    // اطمینان از وجود پارامترهای ضروری
                    if (!routeValues.ContainsKey("layers"))
                    {
                        return BadRequest(new { success = false, message = "پارامتر 'layers' الزامی است." });
                    }

                    return await ZoomToLayer(routeValues);
                }
                else if (Layer.ToLower() == "identify")
                {
                    return GetMapIdentify(routeValues);
                }
                else if (Layer.ToLower() == "selectbygeometry")
                {
                    return await SelectByGeometry(routeValues);
                }
                return NotFound($"Service not found for path: /{Name}/{Types}/{Layer}. Expected /services/map/mapserver/layer");
            }
            else
            {
                return NotFound($"Service not found for path: /{Name}/{Types}/{Layer}. Expected /services/map/mapserver");
            }
        }

        /// <summary>
        /// Renders a map image based on provided parameters.
        /// This method can now be called directly by clients at /rest/export,
        /// or redirected to from the 'Services' action.
        /// </summary>
        /// <param name="layers">Comma-separated list of layer IDs/names to display.</param>
        /// <param name="bbox">Bounding box (minX,minY,maxX,maxY) for map extent.</param>
        /// <param name="size">Desired dimensions of the output image (width,height).</param>
        /// <param name="symbologyName">Optional: Name of symbology to apply.</param>
        /// <param name="layerDefs">Optional: JSON string of layer definitions for custom queries.</param>
        /// <param name="format">Output image format (e.g., "png", "jpg").</param>
        /// <param name="dpi">DPI for rendering.</param>
        /// <param name="transparent">Whether background should be transparent.</param>
        private async Task<IActionResult> ExportMapImage(RouteValueDictionary routeValues)
        {
            string layers = routeValues.GetValueOrDefault("layers", "").ToString();
            string bbox = routeValues.GetValueOrDefault("bbox", "").ToString();
            string size = routeValues.GetValueOrDefault("size", "").ToString();
            string layerDefs = routeValues.GetValueOrDefault("layerDefs", "{}").ToString();
            string format = routeValues.GetValueOrDefault("format", "png").ToString();
            string dpi = routeValues.GetValueOrDefault("dpi", "96").ToString();
            string transparent = routeValues.GetValueOrDefault("transparent", "true").ToString();

            // 1. تجزیه پارامتر size به width و height
            int width = 0, height = 0;
            if (!string.IsNullOrEmpty(size))
            {
                var sizeParts = size.Split(',');
                if (sizeParts.Length == 2 && int.TryParse(sizeParts[0], out width) && int.TryParse(sizeParts[1], out height))
                {
                }
                else
                {
                    return BadRequest("فرمت پارامتر 'size' نامعتبر است. انتظار 'width,height' می‌رود.");
                }
            }
            else
            {
                return BadRequest("پارامتر 'size' الزامی است.");
            }

            // 2. بررسی پارامترهای ضروری
            if (string.IsNullOrEmpty(layers) || string.IsNullOrEmpty(bbox) || width <= 0 || height <= 0)
            {
                return BadRequest($"پارامترهای 'layers', 'bbox', 'width', 'height' الزامی هستند. " +
                                  $"Current: layers='{layers}', bbox='{bbox}', width={width}, height={height}");
            }

            // 3. تجزیه پارامتر layers
            var layerIdentifiers = layers.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                        .Select(s => s.Trim())
                                        .Where(s => !string.IsNullOrEmpty(s))
                                        .Select(id => id.StartsWith("show:", StringComparison.OrdinalIgnoreCase) ? id.Substring(5) :
                                                      id.StartsWith("hide:", StringComparison.OrdinalIgnoreCase) ? null : id)
                                        .Where(id => id != null)
                                        .ToList();

            if (!layerIdentifiers.Any())
            {
                Console.WriteLine("No layers specified. Returning transparent image.");
                return File(GenerateTransparentPng(width, height), "image/png");
            }

            try
            {
                // Parse BBOX
                var (bbxs, bboxError) = ParseBbox(bbox);
                if (bboxError != null) return BadRequest(bboxError);
                var extent = new MRect(bbxs[0], bbxs[1], bbxs[2], bbxs[3]);

                // اعتبارسنجی extent
                if (extent.Width <= 0 || extent.Height <= 0 || double.IsNaN(extent.MinX) || double.IsNaN(extent.MinY))
                {
                    Console.WriteLine($"Invalid extent: MinX={extent.MinX}, MinY={extent.MinY}, MaxX={extent.MaxX}, MaxY={extent.MaxY}");
                    return BadRequest("محدوده bbox نامعتبر است.");
                }

                // اعتبارسنجی ابعاد تصویر
                if (width <= 0 || height <= 0 || width > 4096 || height > 4096)
                    return BadRequest("ابعاد تصویر نامعتبر است. عرض و ارتفاع باید بین 1 و 4096 باشند.");

                Dictionary<string, string> layerDefsDict;
                try
                {
                    // حذف : از انتهای رشته اگر وجود داشت
                    if (layerDefs.EndsWith(":"))
                    {
                        layerDefs = layerDefs.Substring(0, layerDefs.Length - 1);
                    }

                    layerDefsDict = string.IsNullOrWhiteSpace(layerDefs) || layerDefs == "{}"
                        ? new Dictionary<string, string>()
                        : JsonSerializer.Deserialize<Dictionary<string, string>>(layerDefs);
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, "خطا در تجزیه layerDefs");
                    return BadRequest(new { success = false, message = $"فرمت layerDefs نامعتبر است: {ex.Message}" });
                }

                float scale = float.TryParse(dpi, out var parsedDpi) ? parsedDpi / 96f : 1f;

                using (var map = new Map
                {
                    CRS = $"EPSG:{DatabaseSRID}",
                    BackColor = transparent.Equals("true", StringComparison.OrdinalIgnoreCase) ? Color.Transparent : Color.White
                })
                {
                    bool hasVisibleFeatures = false;
                    MRect featuresExtent = null;

                    foreach (var identifier in layerIdentifiers)
                    {
                        string layerId = "";
                        string lbl = "";
                        string opacity = "";
                        string symbology = "";

                        if (checkLayerParams(identifier, out layerId, out lbl, out opacity, out symbology))
                        {
                            //string tableId = layerId[^4..];
                            string tableId = layerId.Substring(layerId.Length - 4);
                            int tableIdInt = int.Parse(tableId);

                            TableInfo tableInfo = await _context.TableInfos.FirstOrDefaultAsync(t => t.Id == tableIdInt);
                            if (tableInfo == null)
                            {
                                Console.WriteLine($"TableInfo with ShortName '{layerId}' not found. Skipping layer.");
                                continue;
                            }
                            SymbologyInfo symbologyInfo = null;
                            if (!string.IsNullOrEmpty(symbology))
                            {
                                int symbologyId = int.Parse(symbology);
                                symbologyInfo = await _context.SymbologyInfos.FirstOrDefaultAsync(s => s.TableInfoId == tableInfo.Id && s.Id == symbologyId);
                            }
                            else
                            {
                                symbologyInfo = await _context.SymbologyInfos.FirstOrDefaultAsync(s => s.TableInfoId == tableInfo.Id && s.IsDefault);
                            }

                            var features = await FetchFeaturesAsync(tableInfo.Name, extent, layerDefsDict);
                            if (features.Any())
                            {
                                // ****** تغییر ۱: همیشه از StyleCollection استفاده کنید ******
                                var styleCollection = new StyleCollection();

                                // تبدیل و افزودن استایل اصلی
                                Mapsui.Styles.IStyle mapsuiStyle = MapsuiSymbologyConverter.CreateMapsuiStyleFromDotSpatialSymbologyJson(
                                    symbologyInfo?.Json,
                                    tableInfo.DatasetType,
                                    symbologyInfo?.FieldName
                                );

                                if (mapsuiStyle == null)
                                {
                                    Console.WriteLine($"Symbology conversion returned null. Using default style.");
                                    mapsuiStyle = MapsuiSymbologyConverter.CreateDefaultMapsuiStyle(tableInfo.DatasetType);
                                }
                                styleCollection.Styles.Add(mapsuiStyle); // افزودن استایل اصلی به کالکشن

                                // افزودن برچسب‌گذاری اگر lbl مشخص شده باشد
                                if (!string.IsNullOrEmpty(lbl))
                                {
                                    var sampleFeature = features.FirstOrDefault();
                                    if (sampleFeature != null && sampleFeature.Fields.Contains(lbl))
                                    {
                                        // ****** تغییر ۲: استفاده از LabelColumn برای تعیین فیلد برچسب ******
                                        var labelStyle = new LabelStyle
                                        {
                                            LabelColumn = lbl, // <--- اصلاح کلیدی
                                            ForeColor = Color.Black,
                                            BackColor = new Brush(Color.FromArgb(128, 255, 255, 255)),
                                            Font = new Font { FontFamily = "Arial", Size = 12 },
                                            Halo = new Pen(Color.White, 2),
                                            Offset = new Offset(0, -10),
                                            CollisionDetection = true
                                        };
                                        styleCollection.Styles.Add(labelStyle); // افزودن استایل برچسب به همان کالکشن
                                        Console.WriteLine($"Label style added for layer '{tableInfo.Name}' using field '{lbl}'.");
                                    }
                                    else
                                    {
                                        Console.WriteLine($"Field '{lbl}' not found. Skipping labeling.");
                                    }
                                }

                                // تنظیم شفافیت لایه
                                double layerOpacity = 1.0;
                                if (!string.IsNullOrEmpty(opacity) && double.TryParse(opacity, out double parsedOpacity))
                                {
                                    layerOpacity = Math.Clamp(parsedOpacity / 100.0, 0.0, 1.0);
                                }

                                // ایجاد لایه با استفاده از StyleCollection
                                var mapLayer = new MemoryLayer(tableInfo.Name)
                                {
                                    Features = features,
                                    Style = styleCollection, // <--- همیشه از کالکشن استفاده کنید
                                    IsMapInfoLayer = true,
                                    Enabled = true,
                                    Opacity = layerOpacity
                                };

                                map.Layers.Add(mapLayer);
                                Console.WriteLine($"Layer '{tableInfo.Name}' added.");
                                hasVisibleFeatures = true;
                            }
                            else
                            {
                                Console.WriteLine($"No features found for layer '{tableInfo.Name}'.");
                            }
                        }
                    }

                    if (!map.Layers.Any() || !hasVisibleFeatures)
                    {
                        Console.WriteLine("No layers with visible features were added to the map. Returning transparent image.");
                        return File(GenerateTransparentPng(width, height), "image/png");
                    }

                    // تنظیم Viewport
                    var desiredResolution = Math.Max(extent.Width / width, extent.Height / height);// 1000; // رزولوشن مشابه TestMapImage
                    map.Navigator.CenterOnAndZoomTo(new MPoint(extent.Centroid.X, extent.Centroid.Y), desiredResolution);
                    var viewport = new Viewport
                    {
                        Width = width,
                        Height = height,
                        Resolution = desiredResolution,
                        CenterX = extent.Centroid.X,
                        CenterY = extent.Centroid.Y
                    };

                    Console.WriteLine($"Viewport: Width={viewport.Width}, Height={viewport.Height}, Resolution={viewport.Resolution}, CenterX={viewport.CenterX}, CenterY={viewport.CenterY}");
                    Console.WriteLine($"Extent: MinX={extent.MinX}, MinY={extent.MinY}, MaxX={extent.MaxX}, MaxY={extent.MaxY}");

                    // رندر تصویر
                    var renderer = new MapRenderer();
                    using var skBitmapStream = renderer.RenderToBitmapStream(viewport, map.Layers, map.BackColor, scale);

                    if (skBitmapStream == null || skBitmapStream.Length == 0)
                    {
                        Console.WriteLine("RenderToBitmapStream returned null or empty stream.");
                        return File(GenerateTransparentPng(width, height), "image/png");
                    }

                    Console.WriteLine($"Rendered stream size: {skBitmapStream.Length} bytes");

                    // تبدیل به فرمت خروجی
                    byte[] imageBytes;
                    string contentType;

                    if (format.Equals("jpg", StringComparison.OrdinalIgnoreCase) || format.Equals("jpeg", StringComparison.OrdinalIgnoreCase))
                    {
                        try
                        {
                            skBitmapStream.Position = 0;
                            using var skImage = SKImage.FromEncodedData(skBitmapStream);
                            if (skImage == null)
                            {
                                Console.WriteLine("Failed to create SKImage from stream.");
                                return File(GenerateTransparentPng(width, height), "image/png");
                            }
                            using var skData = skImage.Encode(SKEncodedImageFormat.Jpeg, 80);
                            imageBytes = skData.ToArray();
                            contentType = "image/jpeg";
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error converting to JPEG: {ex.Message}");
                            return File(GenerateTransparentPng(width, height), "image/png");
                        }
                    }
                    else
                    {
                        skBitmapStream.Position = 0;
                        imageBytes = skBitmapStream.ToArray();
                        contentType = "image/png";
                    }

                    Console.WriteLine($"Output image size: {imageBytes.Length} bytes, Format: {contentType}");
                    return File(imageBytes, contentType);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ExportMapImage: {ex.Message}");
                return File(GenerateTransparentPng(width, height), "image/png");
            }
        }

        /// <summary>
        /// انتخاب عارضه‌ها بر اساس geometry ارسال شده
        /// URL: /rest/services/map/mapserver/selectbygeometry
        /// </summary>
        /// <param name="routeValues">پارامترهای درخواست شامل geometry، layers، و سایر تنظیمات</param>
        /// <returns>نتیجه انتخاب شامل تعداد عارضه‌های انتخاب شده و اطلاعات آن‌ها</returns>
        private async Task<IActionResult> SelectByGeometry(RouteValueDictionary routeValues)
        {
            try
            {
                // دریافت پارامترها
                string geometryParam = routeValues.GetValueOrDefault("geometry", "")?.ToString() ?? "";
                string geometryType = routeValues.GetValueOrDefault("geometryType", "esriGeometryPolygon")?.ToString() ?? "esriGeometryPolygon";
                string layers = routeValues.GetValueOrDefault("layers", "")?.ToString() ?? "";
                string spatialRel = routeValues.GetValueOrDefault("spatialRel", "esriSpatialRelIntersects")?.ToString() ?? "esriSpatialRelIntersects";
                string returnGeometry = routeValues.GetValueOrDefault("returnGeometry", "false")?.ToString() ?? "false";
                string returnIdsOnly = routeValues.GetValueOrDefault("returnIdsOnly", "false")?.ToString() ?? "false";
                string maxRecordCount = routeValues.GetValueOrDefault("maxRecordCount", "1000")?.ToString() ?? "1000";

                _logger.LogInformation("SelectByGeometry called with geometry: {Geometry}, layers: {Layers}",
                    geometryParam.Length > 100 ? geometryParam.Substring(0, 100) + "..." : geometryParam, layers);

                // اعتبارسنجی پارامترهای ضروری
                if (string.IsNullOrEmpty(geometryParam))
                {
                    return BadRequest(new {
                        success = false,
                        message = "پارامتر 'geometry' الزامی است."
                    });
                }

                if (string.IsNullOrEmpty(layers))
                {
                    return BadRequest(new {
                        success = false,
                        message = "پارامتر 'layers' الزامی است."
                    });
                }

                // تجزیه geometry
                NetTopologySuite.Geometries.Geometry? queryGeometry = null;
                try
                {
                    queryGeometry = ParseGeometryFromRequest(geometryParam, geometryType);
                    if (queryGeometry is null)
                    {
                        return BadRequest(new {
                            success = false,
                            message = "فرمت geometry نامعتبر است."
                        });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "خطا در تجزیه geometry");
                    return BadRequest(new {
                        success = false,
                        message = $"خطا در تجزیه geometry: {ex.Message}"
                    });
                }

                // تجزیه لایه‌ها
                var layerIdentifiers = layers.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                            .Select(s => s.Trim())
                                            .Where(s => !string.IsNullOrEmpty(s))
                                            .ToList();

                if (!layerIdentifiers.Any())
                {
                    return BadRequest(new {
                        success = false,
                        message = "هیچ لایه معتبری مشخص نشده است."
                    });
                }

                // تنظیم حداکثر تعداد رکورد
                int maxRecords = 1000;
                if (!int.TryParse(maxRecordCount, out maxRecords) || maxRecords <= 0)
                {
                    maxRecords = 1000;
                }

                var results = new List<object>();
                int totalCount = 0;

                // پردازش هر لایه
                foreach (var identifier in layerIdentifiers)
                {
                    try
                    {
                        var layerResult = await ProcessLayerSelection(identifier, queryGeometry, spatialRel,
                            returnGeometry.Equals("true", StringComparison.OrdinalIgnoreCase),
                            returnIdsOnly.Equals("true", StringComparison.OrdinalIgnoreCase),
                            maxRecords - totalCount);

                        if (layerResult != null)
                        {
                            results.Add(layerResult);
                            totalCount += layerResult.FeatureCount;

                            if (totalCount >= maxRecords)
                            {
                                break;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطا در پردازش لایه {LayerId}", identifier);
                        // ادامه پردازش سایر لایه‌ها
                    }
                }

                // بازگشت نتیجه
                var response = new
                {
                    success = true,
                    totalCount = totalCount,
                    layers = results,
                    spatialReference = new { wkid = DatabaseSRID, latestWkid = DatabaseSRID }
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطا در SelectByGeometry");
                return StatusCode(500, new {
                    success = false,
                    message = $"خطای سرور: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// تجزیه geometry از درخواست
        /// </summary>
        private NetTopologySuite.Geometries.Geometry? ParseGeometryFromRequest(string geometryParam, string geometryType)
        {
            try
            {
                var reader = new WKTReader();

                // اگر geometry به صورت WKT ارسال شده باشد
                if (geometryParam.StartsWith("POINT") || geometryParam.StartsWith("POLYGON") ||
                    geometryParam.StartsWith("LINESTRING") || geometryParam.StartsWith("MULTIPOINT") ||
                    geometryParam.StartsWith("MULTIPOLYGON") || geometryParam.StartsWith("MULTILINESTRING"))
                {
                    return reader.Read(geometryParam);
                }

                // اگر geometry به صورت JSON ارسال شده باشد
                if (geometryParam.StartsWith("{"))
                {
                    var geometryJson = JsonSerializer.Deserialize<JsonElement>(geometryParam);
                    return ParseEsriGeometryJson(geometryJson, geometryType);
                }

                // اگر geometry به صورت coordinates ساده ارسال شده باشد (مثل bbox)
                if (geometryParam.Contains(","))
                {
                    var coords = geometryParam.Split(',').Select(double.Parse).ToArray();
                    if (coords.Length == 4) // bbox: minX,minY,maxX,maxY
                    {
                        var factory = new NetTopologySuite.Geometries.GeometryFactory();
                        var envelope = new Envelope(coords[0], coords[2], coords[1], coords[3]);
                        return factory.ToGeometry(envelope);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطا در تجزیه geometry: {Geometry}", geometryParam);
                return null;
            }
        }

        /// <summary>
        /// تجزیه geometry از فرمت JSON ESRI
        /// </summary>
        private NetTopologySuite.Geometries.Geometry? ParseEsriGeometryJson(JsonElement geometryJson, string geometryType)
        {
            try
            {
                var factory = new NetTopologySuite.Geometries.GeometryFactory();

                switch (geometryType.ToLower())
                {
                    case "esrigeometrypoint":
                        if (geometryJson.TryGetProperty("x", out var x) && geometryJson.TryGetProperty("y", out var y))
                        {
                            return factory.CreatePoint(new Coordinate(x.GetDouble(), y.GetDouble()));
                        }
                        break;

                    case "esrigeometrypolygon":
                        if (geometryJson.TryGetProperty("rings", out var rings))
                        {
                            var ringsList = new List<LinearRing>();
                            foreach (var ring in rings.EnumerateArray())
                            {
                                var coordinates = new List<Coordinate>();
                                foreach (var point in ring.EnumerateArray())
                                {
                                    var pointArray = point.EnumerateArray().ToArray();
                                    if (pointArray.Length >= 2)
                                    {
                                        coordinates.Add(new Coordinate(pointArray[0].GetDouble(), pointArray[1].GetDouble()));
                                    }
                                }
                                if (coordinates.Count >= 4) // حداقل 4 نقطه برای یک ring
                                {
                                    ringsList.Add(factory.CreateLinearRing(coordinates.ToArray()));
                                }
                            }
                            if (ringsList.Any())
                            {
                                var shell = ringsList.First();
                                var holes = ringsList.Skip(1).ToArray();
                                return factory.CreatePolygon(shell, holes);
                            }
                        }
                        break;

                    case "esrigeometryenvelope":
                        if (geometryJson.TryGetProperty("xmin", out var xmin) &&
                            geometryJson.TryGetProperty("ymin", out var ymin) &&
                            geometryJson.TryGetProperty("xmax", out var xmax) &&
                            geometryJson.TryGetProperty("ymax", out var ymax))
                        {
                            var envelope = new Envelope(xmin.GetDouble(), xmax.GetDouble(), ymin.GetDouble(), ymax.GetDouble());
                            return factory.ToGeometry(envelope);
                        }
                        break;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطا در تجزیه ESRI geometry JSON");
                return null;
            }
        }

        /// <summary>
        /// پردازش انتخاب برای یک لایه خاص
        /// </summary>
        private async Task<LayerSelectionResult?> ProcessLayerSelection(
            string identifier,
            NetTopologySuite.Geometries.Geometry queryGeometry,
            string spatialRel,
            bool returnGeometry,
            bool returnIdsOnly,
            int maxRecords)
        {
            try
            {
                // تجزیه شناسه لایه
                if (!checkLayerParams(identifier, out string layerId, out string lbl, out string opacity, out string symbology))
                {
                    return null;
                }

                string tableId = layerId.Substring(layerId.Length - 4);
                int tableIdInt = int.Parse(tableId);

                var tableInfo = await _context.TableInfos.FirstOrDefaultAsync(t => t.Id == tableIdInt);
                if (tableInfo == null)
                {
                    _logger.LogWarning("TableInfo با ID {TableId} یافت نشد", tableIdInt);
                    return null;
                }

                // انجام کوئری مکانی
                var selectedFeatures = await PerformSpatialQuery(tableInfo.Name, queryGeometry, spatialRel, maxRecords);

                var result = new LayerSelectionResult
                {
                    LayerId = tableIdInt,
                    LayerName = tableInfo.Name,
                    FeatureCount = selectedFeatures.Count(),
                    Features = returnIdsOnly ? null : selectedFeatures.Select(f => new
                    {
                        attributes = f.Attributes,
                        geometry = returnGeometry ? f.Geometry?.AsText() : null
                    }).ToList()
                };

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطا در پردازش انتخاب لایه {Identifier}", identifier);
                return null;
            }
        }

        /// <summary>
        /// انجام کوئری مکانی روی جدول
        /// </summary>
        private async Task<IEnumerable<NetTopologySuite.Features.IFeature>> PerformSpatialQuery(
            string tableName,
            NetTopologySuite.Geometries.Geometry queryGeometry,
            string spatialRel,
            int maxRecords)
        {
            try
            {
                var features = new List<NetTopologySuite.Features.IFeature>();
                var connectionString = _configuration.GetConnectionString("DefaultConnection");

                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                // تبدیل geometry به فرمت SQL Server
                var geometryWkt = queryGeometry.AsText();
                var geometryParam = $"geometry::STGeomFromText('{geometryWkt}', {DatabaseSRID})";

                // تعیین نوع رابطه مکانی
                string spatialFunction = spatialRel.ToLower() switch
                {
                    "esrispatialrelintersects" => "STIntersects",
                    "esrispatialrelcontains" => "STContains",
                    "esrispatialrelwithin" => "STWithin",
                    "esrispatialreltouches" => "STTouches",
                    "esrispatialreloverlaps" => "STOverlaps",
                    _ => "STIntersects"
                };

                // ساخت کوئری SQL
                var sql = $@"
                    SELECT TOP ({maxRecords}) *
                    FROM [{tableName}]
                    WHERE [Geometry].{spatialFunction}({geometryParam}) = 1";

                _logger.LogInformation("Executing spatial query: {Sql}", sql);

                using var command = new SqlCommand(sql, connection);
                command.CommandTimeout = 30;

                using var reader = await command.ExecuteReaderAsync();
                var wktReader = new WKTReader();

                while (await reader.ReadAsync())
                {
                    var attributes = new NetTopologySuite.Features.AttributesTable();
                    NetTopologySuite.Geometries.Geometry? featureGeometry = null;

                    // خواندن تمام ستون‌ها
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        var fieldName = reader.GetName(i);
                        var fieldValue = reader.IsDBNull(i) ? null : reader.GetValue(i);

                        if (fieldName.Equals("Geometry", StringComparison.OrdinalIgnoreCase))
                        {
                            // خواندن geometry
                            if (fieldValue != null)
                            {
                                try
                                {
                                    var geometryBytes = (byte[])fieldValue;
                                    var sqlGeometry = Microsoft.SqlServer.Types.SqlGeometry.STGeomFromWKB(
                                        new Microsoft.SqlServer.Types.SqlBytes(geometryBytes), DatabaseSRID);
                                    var wkt = sqlGeometry.STAsText().Value;
                                    featureGeometry = wktReader.Read(wkt);
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogWarning(ex, "خطا در خواندن geometry برای feature");
                                }
                            }
                        }
                        else
                        {
                            // خواندن attribute
                            attributes.Add(fieldName, fieldValue);
                        }
                    }

                    // ایجاد feature
                    var feature = new NetTopologySuite.Features.Feature(featureGeometry, attributes);
                    features.Add(feature);
                }

                _logger.LogInformation("Found {Count} features in spatial query", features.Count);
                return features;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطا در انجام کوئری مکانی برای جدول {TableName}", tableName);
                return new List<IFeature>();
            }
        }

        private async Task<IActionResult> ZoomToLayer(
            RouteValueDictionary routeValues)
        {
            try
            {
                string layers = routeValues.GetValueOrDefault("layers", "").ToString();
                string selected = routeValues.GetValueOrDefault("selected", "false").ToString();
                string layerDefs = routeValues.GetValueOrDefault("layerDefs", "{}").ToString();

                if (string.IsNullOrEmpty(layers))
                {
                    return BadRequest(new { success = false, message = "پارامتر 'layers' الزامی است." });
                }

                _logger.LogInformation("ZoomToLayer called with layers: {Layers}, selected: {Selected}",
                    layers, selected);

                // Parse layer identifiers
                var layerIdentifiers = layers.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                .Select(s => s.Trim())
                                .Where(s => !string.IsNullOrEmpty(s))
                                .Select(id => id.StartsWith("show:", StringComparison.OrdinalIgnoreCase) ? id.Substring(5) :
                                              id.StartsWith("hide:", StringComparison.OrdinalIgnoreCase) ? null : id)
                                .Where(id => id != null)
                                .ToList();

                if (!layerIdentifiers.Any())
                {
                    return BadRequest(new { success = false, message = "هیچ لایه‌ای برای بزرگنمایی مشخص نشده است." });
                }

                // Parse layer definitions
                Dictionary<string, string> layerDefsDict;
                try
                {
                    layerDefsDict = string.IsNullOrWhiteSpace(layerDefs) || layerDefs == "{}"
                                    ? new Dictionary<string, string>()
                                    : JsonSerializer.Deserialize<Dictionary<string, string>>(layerDefs);
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, "خطا در تجزیه layerDefs");
                    return BadRequest(new { success = false, message = $"فرمت layerDefs نامعتبر است: {ex.Message}" });
                }

                Extent resultExtent = null;
                foreach (var identifier in layerIdentifiers)
                {
                    string layerId = "";
                    string lbl = "";
                    string opacity = "";
                    string symbology = "";

                    if (!checkLayerParams(identifier, out layerId, out lbl, out opacity, out symbology))
                    {
                        _logger.LogWarning("Invalid layer parameters for identifier: {Identifier}", identifier);
                        continue;
                    }

                    // Extract table ID from layer ID
                    if (!int.TryParse(layerId.Substring(layerId.Length - 4), out int tableIdInt))
                    {
                        _logger.LogWarning("Invalid table ID format in layer ID: {LayerId}", layerId);
                        continue;
                    }

                    // Get table info
                    var tableInfo = await _context.TableInfos.FirstOrDefaultAsync(t => t.Id == tableIdInt);
                    if (tableInfo == null)
                    {
                        _logger.LogWarning("TableInfo not found for ID: {TableId}", tableIdInt);
                        continue;
                    }

                    try
                    {
                        using var db = new DBManagement(_configuration);
                        var filters = new List<string>();

                        // Handle selected features
                        if (selected?.ToLower() == "true")
                        {
                            var userName = User.Identity?.Name ?? string.Empty;
                            var query = "select OIDs FROM UserSelects WHERE (UserID = @userName AND [Table] = @layerId)";
                            var parameters = new[]
                            {
                                new Microsoft.Data.SqlClient.SqlParameter("@userName", userName),
                                new SqlParameter("@layerId", layerId)
                            };

                            var dtOIDs = db.SelectTableSQL(query, parameters);
                            if (dtOIDs.Rows.Count > 0)
                            {
                                string oids = dtOIDs.Rows[0]["OIDs"].ToString();
                                if (!string.IsNullOrEmpty(oids))
                                {
                                    filters.Add($"ObjectID In ({oids})");
                                }
                            }
                        }
                        else if (layerDefsDict.TryGetValue(layerId, out var layerDef))
                        {
                            if (!string.IsNullOrEmpty(layerDef))
                            {
                                var arrayCLDef = layerDef.Split(';');
                                if (arrayCLDef.Length > 1)
                                {
                                    var arrayFSDefs = arrayCLDef[0].Split(',');
                                    if (arrayFSDefs[0].ToLower() != "select")
                                    {
                                        filters.Add($" {arrayCLDef[0]} ");
                                    }
                                }
                                else
                                {
                                    filters.Add($" {layerDef} ");
                                }
                            }
                        }

                        // Build SQL query to get extent
                        var sql = $@"
                            SELECT 
                                MIN([Shape].STEnvelope().STPointN(1).STX) as XMin,
                                MIN([Shape].STEnvelope().STPointN(1).STY) as YMin,
                                MAX([Shape].STEnvelope().STPointN(3).STX) as XMax,
                                MAX([Shape].STEnvelope().STPointN(3).STY) as YMax 
                            FROM [{tableInfo.Name}]";

                        if (filters.Count > 0)
                        {
                            string filterExpression = $" WHERE {string.Join(" AND ", filters)}";
                            if (SQLExtensions.ValidateSQLWhere(filterExpression))
                            {
                                sql += filterExpression;
                            }
                            else
                            {
                                _logger.LogWarning("Invalid SQL filter expression: {FilterExpression}", filterExpression);
                                continue;
                            }
                        }

                        var dt = db.SelectTableSQL(sql);
                        if (dt.Rows.Count > 0)
                        {
                            resultExtent = new Extent
                            {
                                XMax = Convert.ToDouble(dt.Rows[0]["XMax"]),
                                YMax = Convert.ToDouble(dt.Rows[0]["YMax"]),
                                XMin = Convert.ToDouble(dt.Rows[0]["XMin"]),
                                YMin = Convert.ToDouble(dt.Rows[0]["YMin"])
                            };
                            break; // Use the first valid extent
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error calculating extent for layer {LayerId}", layerId);
                    }
                }

                if (resultExtent == null)
                {
                    return NotFound(new { success = false, message = "هیچ محدوده‌ای برای بزرگنمایی یافت نشد." });
                }

                return Ok(new { success = true, data = resultExtent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in ZoomToLayer");
                return StatusCode(500, new { success = false, message = "خطای غیرمنتظره در بزرگنمایی" });
            }
        }

        private IActionResult GetMapIdentify(RouteValueDictionary routeValues)
        {
            try
            {
                // بررسی پارامترهای اجباری
                if (!routeValues.ContainsKey("geometry") || !routeValues.ContainsKey("geometryType") ||
                    !routeValues.ContainsKey("mapExtent") || !routeValues.ContainsKey("layers"))
                {
                    return BadRequest(new { success = false, message = "پارامترهای geometry، geometryType، mapExtent و layers الزامی هستند." });
                }

                // خواندن پارامترها با مقادیر پیش‌فرض
                string f = routeValues.GetValueOrDefault("f", "json").ToString();
                string tolerance = routeValues.GetValueOrDefault("tolerance", "3").ToString();
                string returnGeometry = routeValues.GetValueOrDefault("returnGeometry", "true").ToString();
                string imageDisplay = routeValues.GetValueOrDefault("imageDisplay", "").ToString();
                string geometry = routeValues["geometry"].ToString();
                string geometryType = routeValues["geometryType"].ToString();
                string sr = routeValues.GetValueOrDefault("sr", DatabaseSRID.ToString()).ToString();
                string mapExtent = routeValues["mapExtent"].ToString();
                string layers = routeValues["layers"].ToString();
                string layerDefs = routeValues.GetValueOrDefault("layerDefs", "{}").ToString();

                _logger.LogInformation("Identify request received with parameters: {Parameters}",
                    string.Join(", ", new[] { f, tolerance, returnGeometry, imageDisplay, geometry,
                        geometryType, sr, mapExtent, layers, layerDefs }));

                // 1. تجزیه mapExtent برای محاسبه tolerance در مختصات واقعی
                var (extent, extentError) = ParseBbox(mapExtent);
                if (extentError != null)
                {
                    return BadRequest(new { success = false, message = extentError });
                }


                double toleranceInMeters = 0;
                if (double.TryParse(tolerance, out double toleranceInPixels))
                {
                    // تبدیل tolerance از پیکسل به متر
                    double mapWidth = extent[2] - extent[0];
                    double mapHeight = extent[3] - extent[1];
                    double pixelSize = Math.Max(mapWidth, mapHeight) / 1000; // فرض می‌کنیم عرض نقشه 1000 پیکسل است
                    toleranceInMeters = toleranceInPixels * pixelSize;
                }

                // 2. تجزیه geometry و تبدیل به مختصات
                double clickPointX = 0;
                double clickPointY = 0;

                if (geometry.Contains("{"))
                {
                    var geometryObj = JsonSerializer.Deserialize<JsonElement>(geometry);
                    clickPointX = geometryObj.GetProperty("x").GetDouble();
                    clickPointY = geometryObj.GetProperty("y").GetDouble();
                }
                else
                {
                    var coords = geometry.Split(',');
                    clickPointX = double.Parse(coords[0]);
                    clickPointY = double.Parse(coords[1]);
                }

                Extent identifyExtentGeo = new Extent
                {
                    XMin = clickPointX - toleranceInMeters,
                    YMin = clickPointY - toleranceInMeters,
                    XMax = clickPointX + toleranceInMeters,
                    YMax = clickPointY + toleranceInMeters
                };

                var identifyExtent = identifyExtentGeo;

                if (sr != "102100")
                {
                    var transformer = new Helper.GeometryTransformer(_loggerFactory.CreateLogger<Helper.GeometryTransformer>());
                    identifyExtent = transformer.TransformExtent(identifyExtentGeo, "3857");
                }

                // 3. تجزیه layers و اعمال layerDefs
                var layerIdentifiers = layers.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                          .Select(s => s.Trim())
                                          .Where(s => !string.IsNullOrEmpty(s))
                                          .Select(id => id.StartsWith("show:", StringComparison.OrdinalIgnoreCase) ? id.Substring(5) :
                                                        id.StartsWith("hide:", StringComparison.OrdinalIgnoreCase) ? null : id)
                                          .Where(id => id != null)
                                          .ToList();

                Dictionary<string, string> layerDefsDict;
                try
                {
                    // حذف : از انتهای رشته اگر وجود داشت
                    if (layerDefs.EndsWith(":"))
                    {
                        layerDefs = layerDefs.Substring(0, layerDefs.Length - 1);
                    }

                    layerDefsDict = string.IsNullOrWhiteSpace(layerDefs) || layerDefs == "{}" || layerDefs == "false"
                        ? new Dictionary<string, string>()
                        : JsonSerializer.Deserialize<Dictionary<string, string>>(layerDefs);
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, "خطا در تجزیه layerDefs");
                    return BadRequest(new { success = false, message = $"فرمت layerDefs نامعتبر است: {ex.Message}" });
                }

                // 4. اجرای کوئری برای یافتن لایه‌های متقاطع
                var results = new List<object>();
                foreach (var identifier in layerIdentifiers)
                {
                    string layerId = "";
                    string lbl = "";
                    string opacity = "";
                    string symbology = "";

                    if (!checkLayerParams(identifier, out layerId, out lbl, out opacity, out symbology))
                    {
                        _logger.LogWarning("Invalid layer parameters for identifier: {Identifier}", identifier);
                        continue;
                    }

                    // استخراج tableId از layerId
                    if (!int.TryParse(layerId.Substring(layerId.Length - 4), out int tableIdInt))
                    {
                        _logger.LogWarning("Invalid table ID format in layer ID: {LayerId}", layerId);
                        continue;
                    }

                    // دریافت اطلاعات جدول
                    var tableInfo = _context.TableInfos
                        .Include(t => t.FieldInfos)
                        .FirstOrDefault(t => t.Id == tableIdInt);
                    if (tableInfo == null)
                    {
                        _logger.LogWarning("TableInfo not found for ID: {TableId}", tableIdInt);
                        continue;
                    }

                    try
                    {
                        using var db = new DBManagement(_configuration);
                        var filters = new List<string>();

                        // اعمال layerDefs
                        if (layerDefsDict.TryGetValue(layerId, out var layerDef))
                        {
                            if (!string.IsNullOrEmpty(layerDef))
                            {
                                var arrayCLDef = layerDef.Split(';');
                                if (arrayCLDef.Length > 1)
                                {
                                    var arrayFSDefs = arrayCLDef[0].Split(',');
                                    if (arrayFSDefs[0].ToLower() != "select")
                                    {
                                        filters.Add($" {arrayCLDef[0]} ");
                                    }
                                }
                                else
                                {
                                    filters.Add($" {layerDef} ");
                                }
                            }
                        }

                        // ساخت کوئری برای یافتن لایه‌های متقاطع
                        var selectColumns = string.Join(",", tableInfo.FieldInfos
                            .Where(fi => fi.IsDisplay)
                            .OrderBy(fi => fi.FieldIndex)
                            .Select(fi => fi.Name));

                        //var polygon = "'POLYGON((" + identifyExtent.XMin + ' ' + identifyExtent.YMin + ", " + identifyExtent.XMax + ' ' + identifyExtent.YMin + ", " + identifyExtent.XMax + ' ' + identifyExtent.YMax + ", " + identifyExtent.XMin + ' ' + identifyExtent.YMax + ", " + identifyExtent.XMin + ' ' + identifyExtent.YMin + "))'";
                        var polygon = "geometry::STGeomFromText('POLYGON((" + identifyExtent.XMin + ' ' + identifyExtent.YMin + ", " + identifyExtent.XMax + ' ' + identifyExtent.YMin + ", " + identifyExtent.XMax + ' ' + identifyExtent.YMax + ", " + identifyExtent.XMin + ' ' + identifyExtent.YMax + ", " + identifyExtent.XMin + ' ' + identifyExtent.YMin + "))', " + +DatabaseSRID + ")";
                        string whereGeom = " Where Shape.Filter(" + polygon + ") =1 ";

                        var sql = $@"
                            SELECT TOP 10 {selectColumns}, Shape.STAsBinary() as ShapeBinary
                            FROM [{tableInfo.Name}]
                            {whereGeom}
                        ";

                        if (filters.Count > 0)
                        {
                            string filterExpression = $" AND {string.Join(" AND ", filters)}";
                            if (SQLExtensions.ValidateSQLWhere(filterExpression))
                            {
                                sql += filterExpression;
                            }
                            else
                            {
                                _logger.LogWarning("Invalid SQL filter expression: {FilterExpression}", filterExpression);
                                continue;
                            }
                        }

                        var dt = db.SelectTableSQL(sql);
                        if (dt.Rows.Count > 0)
                        {
                            foreach (System.Data.DataRow row in dt.Rows)
                            {
                                var attributes = new Dictionary<string, object>();
                                foreach (System.Data.DataColumn column in dt.Columns)
                                {
                                    if (column.ColumnName != "ShapeBinary") // نادیده گرفتن ستون Shape
                                    {
                                        attributes[column.ColumnName] = row[column];
                                    }
                                }


                                ViewModels.Geometry geometryObject = new BaseGIS.Web.ViewModels.Geometry();
                                if (returnGeometry.ToLower() == "true" && row["ShapeBinary"] != DBNull.Value)
                                {
                                    var geometryBytes = (byte[])row["ShapeBinary"];
                                    var geometryReader = new WKBReader();
                                    var ntsGeometry = geometryReader.Read(geometryBytes);

                                    // Create transformer for coordinate system conversion
                                    var transformer = new Helper.GeometryTransformer(_loggerFactory.CreateLogger<Helper.GeometryTransformer>());

                                    // Simplify geometry if needed
                                    if (tolerance != "0" && ntsGeometry.NumPoints > 5000)
                                    {
                                        var simplifier = new NetTopologySuite.Simplify.DouglasPeuckerSimplifier(ntsGeometry);
                                        simplifier.DistanceTolerance = 0.001;
                                        ntsGeometry = simplifier.GetResultGeometry();
                                    }

                                    ProjNet.CoordinateSystems.CoordinateSystem sourseCoordSystem = ProjNet.CoordinateSystems.ProjectedCoordinateSystem.WebMercator;
                                    ProjNet.CoordinateSystems.CoordinateSystem targetCoordSystem = ProjNet.CoordinateSystems.GeographicCoordinateSystem.WGS84;
                                    var ctf = new CoordinateTransformationFactory();
                                    var transformation = ctf.CreateFromCoordinateSystems(sourseCoordSystem, targetCoordSystem);

                                    // Convert to appropriate geometry type
                                    var viewModelGeometry = new ViewModels.Geometry();
                                    switch (ntsGeometry.GeometryType)
                                    {
                                        case "Polygon":
                                            var ntsPolygon = (Polygon)ntsGeometry;
                                            viewModelGeometry.rings = new List<List<List<double>>>();
                                            List<List<double>> exteriorCoords = new List<List<double>>();
                                            foreach (Coordinate coordinate in ntsPolygon.ExteriorRing.Coordinates)
                                            {
                                                if (!(sr == "102100" || sr == "3857"))
                                                {
                                                    var coord = TransformCoordinate(coordinate, transformation);
                                                    exteriorCoords.Add(new List<double>() { coord.X, coord.Y });
                                                }
                                                else
                                                {
                                                    exteriorCoords.Add(new List<double>() { coordinate.X, coordinate.Y });
                                                }
                                            }
                                            ((List<List<List<double>>>)viewModelGeometry.rings).Add(exteriorCoords);

                                            for (int i = 0; i < ntsPolygon.NumInteriorRings; i++)
                                            {
                                                List<List<double>> holeCoords = new List<List<double>>();
                                                foreach (Coordinate coordinate in ntsPolygon.GetInteriorRingN(i).Coordinates)
                                                {
                                                    if (!(sr == "102100" || sr == "3857"))
                                                    {
                                                        var coord = TransformCoordinate(coordinate, transformation);
                                                        holeCoords.Add(new List<double>() { coord.X, coord.Y });
                                                    }
                                                    else
                                                    {
                                                        holeCoords.Add(new List<double>() { coordinate.X, coordinate.Y });
                                                    }
                                                }
                                                ((List<List<List<double>>>)viewModelGeometry.rings).Add(holeCoords);
                                            }
                                            break;

                                        case "MultiPolygon":
                                            var ntsMultiPolygon = (MultiPolygon)ntsGeometry;
                                            viewModelGeometry.rings = new List<List<List<double>>>();

                                            for (int i = 0; i < ntsMultiPolygon.NumGeometries; i++)
                                            {
                                                var ntsPoly = (Polygon)ntsMultiPolygon.GetGeometryN(i);
                                                List<List<double>> exteriorCoords1 = new List<List<double>>();
                                                foreach (Coordinate coordinate in ntsPoly.ExteriorRing.Coordinates)
                                                {
                                                    if (!(sr == "102100" || sr == "3857"))
                                                    {
                                                        var coord = TransformCoordinate(coordinate, transformation);
                                                        exteriorCoords1.Add(new List<double>() { coord.X, coord.Y });
                                                    }
                                                    else
                                                    {
                                                        exteriorCoords1.Add(new List<double>() { coordinate.X, coordinate.Y });
                                                    }
                                                }
                                                 ((List<List<List<double>>>)viewModelGeometry.rings).Add(exteriorCoords1);

                                                for (int j = 0; j < ntsPoly.NumInteriorRings; j++)
                                                {
                                                    List<List<double>> holeCoords = new List<List<double>>();
                                                    foreach (Coordinate coordinate in ntsPoly.GetInteriorRingN(j).Coordinates)
                                                    {
                                                        if (!(sr == "102100" || sr == "3857"))
                                                        {
                                                            var coord = TransformCoordinate(coordinate, transformation);
                                                            holeCoords.Add(new List<double>() { coord.X, coord.Y });
                                                        }
                                                        else
                                                        {
                                                            holeCoords.Add(new List<double>() { coordinate.X, coordinate.Y });
                                                        }
                                                    }
                                                    ((List<List<List<double>>>)viewModelGeometry.rings).Add(holeCoords);
                                                }
                                            }
                                            break;

                                        case "LineString":
                                            var ntsLineString = (LineString)ntsGeometry;
                                            viewModelGeometry.paths = new List<List<List<double>>>();
                                            List<List<double>> coords = new List<List<double>>();
                                            foreach (Coordinate coordinate in ntsLineString.Coordinates)
                                            {
                                                if (!(sr == "102100" || sr == "3857"))
                                                {
                                                    var coord = TransformCoordinate(coordinate, transformation);
                                                    coords.Add(new List<double>() { coord.X, coord.Y });
                                                }
                                                else
                                                {
                                                    coords.Add(new List<double>() { coordinate.X, coordinate.Y });
                                                }
                                            }
                                            viewModelGeometry.paths.Add(coords);
                                            break;

                                        case "Point":
                                            var ntsPoint = (Point)ntsGeometry;
                                            var pCoordinate = ntsGeometry.Coordinate;
                                            if (!(sr == "102100" || sr == "3857"))
                                            {
                                                pCoordinate = TransformCoordinate(pCoordinate, transformation);

                                            }
                                            viewModelGeometry.x = pCoordinate.X;
                                            viewModelGeometry.y = pCoordinate.Y;
                                            break;

                                        case "MultiPoint":
                                            var ntsMultiPoint = (MultiPoint)ntsGeometry;
                                            viewModelGeometry.points = new List<List<double>>();
                                            foreach (Coordinate coordinate in ntsMultiPoint.Coordinates)
                                            {
                                                if (!(sr == "102100" || sr == "3857"))
                                                {
                                                    var coord1 = TransformCoordinate(coordinate, transformation);
                                                    viewModelGeometry.points.Add(new List<double>() { coord1.X, coord1.Y });
                                                }
                                                else
                                                {
                                                    viewModelGeometry.points.Add(new List<double>() { coordinate.X, coordinate.Y });
                                                }
                                            }
                                            break;
                                    }
                                    geometryObject = viewModelGeometry;
                                }
                                IdentifyResult identifyResult = new IdentifyResult();
                                identifyResult.layerId = tableIdInt;
                                identifyResult.layerName = tableInfo.Name;
                                identifyResult.value = attributes.ContainsKey(lbl) ? attributes[lbl].ToString() : "";
                                identifyResult.displayFieldName = "OBJECTID";
                                identifyResult.attributes = attributes;
                                identifyResult.geometry = geometryObject;
                                identifyResult.geometryType = "esriGeometry" + tableInfo.DatasetType;

                                results.Add(identifyResult);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error querying layer {LayerId}", layerId);
                    }
                }

                return Json(new
                {
                    success = true,
                    results = results
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetMapIdentify");
                return StatusCode(500, new { success = false, message = "خطا در پردازش درخواست identify" });
            }
        }
        private Coordinate TransformCoordinate(Coordinate coordinate, ICoordinateTransformation transformation)
        {
            try
            {
                var transformed = transformation.MathTransform.Transform(new[] { coordinate.X, coordinate.Y });
                return new Coordinate(transformed[0], transformed[1]);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطا در تبدیل مختصات ({X}, {Y})", coordinate.X, coordinate.Y);
                // در صورت خطا، مختصات اصلی را برگردان
                return coordinate.Copy();
            }
        }

        [HttpPost("legend")]
        public ActionResult Legend(string layerId, int symbologyId)
        {
            try
            {
                // 1. اعتبارسنجی ورودی‌ها
                if (string.IsNullOrEmpty(layerId) || symbologyId <= 0)
                {
                    return BadRequest("LayerId and SymbologyId are required.");
                }

                // 2. استخراج tableId
                string tableId = layerId.Substring(layerId.Length - 4);
                if (!int.TryParse(tableId, out int tableIdInt))
                {
                    return BadRequest("Invalid LayerId format.");
                }

                // 3. اطلاعات سیمبولوژی و لایه را از دیتابیس واکشی کنید
                var symbologyInfo = _context.SymbologyInfos.Find(symbologyId);
                if (symbologyInfo == null)
                {
                    return NotFound($"Symbology with ID {symbologyId} not found.");
                }

                var tableInfo = _context.TableInfos.Find(tableIdInt);
                if (tableInfo == null)
                {
                    return NotFound($"TableInfo with ID {tableIdInt} not found.");
                }

                // 4. تولید آیتم‌های لجند
                var legendItems = MapsuiLegendGenerator.GenerateLegend(symbologyInfo.Json, tableInfo, symbologyInfo.FieldName);

                // 5. تبدیل به فرمت NodeTree
                var list = new List<NodeTree>();
                foreach (var item in legendItems)
                {
                    list.Add(new NodeTree
                    {
                        title = item.Label,
                        key = "L_" + Guid.NewGuid().ToString(), // کلید منحصر به فرد
                        icon = item.ImageData,
                        iconHeight = item.ImageHeight,
                        iconWidth = item.ImageWidth
                    });
                }

                // 6. بازگشت JSON
                return Json(list);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating legend: {ex.Message}");
                return StatusCode(500, "An error occurred while generating the legend.");
            }
        }

        public IActionResult TestMapImage(
            [FromQuery] string size = "500,500",
            [FromQuery] string format = "png",
            [FromQuery] string transparent = "false")
        {
            int width = 0, height = 0;
            try
            {
                // 1. تجزیه پارامتر size به width و height

                var sizeParts = size.Split(',');
                if (sizeParts.Length != 2 || !int.TryParse(sizeParts[0], out width) || !int.TryParse(sizeParts[1], out height))
                {
                    return BadRequest("فرمت پارامتر 'size' نامعتبر است. انتظار 'width,height' می‌رود.");
                }

                if (width <= 0 || height <= 0 || width > 4096 || height > 4096)
                {
                    return BadRequest("ابعاد تصویر نامعتبر است. عرض و ارتفاع باید بین 1 و 4096 باشند.");
                }

                // 2. ایجاد نقشه
                using (var map = new Map
                {
                    CRS = "EPSG:3857",
                    BackColor = transparent.Equals("true", StringComparison.OrdinalIgnoreCase) ? Color.Transparent : Color.White
                })
                {
                    // 3. ایجاد نقطه تستی (مختصات تهران در EPSG:3857)
                    var point = new MPoint(5907474, 3968250);
                    var feature = new PointFeature(point);
                    var mapLayer = new MemoryLayer
                    {
                        Name = "TestLayer",
                        Features = new List<IFeature> { feature },
                        Style = new SymbolStyle
                        {
                            Fill = new Brush(Color.Green),
                            SymbolScale = 1.0,
                            SymbolType = Mapsui.Styles.SymbolType.Ellipse,
                            Outline = new Pen(Color.Black, 2),
                            Opacity = 1.0f
                        },
                        IsMapInfoLayer = true,
                        Enabled = true,
                        Opacity = 1.0
                    };
                    map.Layers.Add(mapLayer);
                    Console.WriteLine($"Test layer added with 1 feature at ({point.X}, {point.Y}). Style: SymbolStyle, Scale: 1.0, Color: Green");

                    // 4. تنظیم محدوده نمایش
                    var extent = new MRect(point.X - 1000, point.Y - 1000, point.X + 1000, point.Y + 1000);
                    Console.WriteLine($"Extent: MinX={extent.MinX}, MinY={extent.MinY}, MaxX={extent.MaxX}, MaxY={extent.MaxY}");

                    // 5. تنظیم Viewport
                    var desiredResolution = 100.0; // رزولوشن ثابت برای زوم مناسب
                    map.Navigator.CenterOnAndZoomTo(new MPoint(point.X, point.Y), desiredResolution);
                    var viewport = new Viewport
                    {
                        Width = width,
                        Height = height,
                        Resolution = desiredResolution,
                        CenterX = point.X,
                        CenterY = point.Y
                    };

                    Console.WriteLine($"Viewport: Width={viewport.Width}, Height={viewport.Height}, Resolution={viewport.Resolution}, CenterX={viewport.CenterX}, CenterY={viewport.CenterY}");

                    // 6. رندر نقشه به تصویر
                    var renderer = new MapRenderer();
                    using var skBitmapStream = renderer.RenderToBitmapStream(viewport, map.Layers, map.BackColor, 1.0f);

                    if (skBitmapStream == null || skBitmapStream.Length == 0)
                    {
                        Console.WriteLine("RenderToBitmapStream returned null or empty stream.");
                        return File(GenerateTransparentPng(width, height), "image/png");
                    }

                    Console.WriteLine($"Rendered stream size: {skBitmapStream.Length} bytes");

                    // 7. تبدیل به فرمت خروجی
                    byte[] imageBytes;
                    string contentType;

                    if (format.Equals("jpg", StringComparison.OrdinalIgnoreCase) || format.Equals("jpeg", StringComparison.OrdinalIgnoreCase))
                    {
                        try
                        {
                            skBitmapStream.Position = 0;
                            using var skImage = SKImage.FromEncodedData(skBitmapStream);
                            if (skImage == null)
                            {
                                Console.WriteLine("Failed to create SKImage from stream.");
                                return File(GenerateTransparentPng(width, height), "image/png");
                            }
                            using var skData = skImage.Encode(SKEncodedImageFormat.Jpeg, 90);
                            imageBytes = skData.ToArray();
                            contentType = "image/jpeg";
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error converting to JPEG: {ex.Message}");
                            return File(GenerateTransparentPng(width, height), "image/png");
                        }
                    }
                    else
                    {
                        skBitmapStream.Position = 0;
                        imageBytes = skBitmapStream.ToArray();
                        contentType = "image/png";
                    }

                    Console.WriteLine($"Output image size: {imageBytes.Length} bytes, Format: {contentType}");
                    return File(imageBytes, contentType);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in TestMapImage: {ex.Message}");
                return File(GenerateTransparentPng(width, height), "image/png");
            }
        }
        public static bool checkLayerParams(string layer, out string layerId, out string lbl, out string opacity, out string symbology)
        {
            opacity = "";
            lbl = "";
            symbology = "";
            layerId = layer.Split(';')[0];
            if (layer.Split(';').Length > 1)
                lbl = layer.Split(';')[1];

            if (layer.Split(';').Length > 2)
                opacity = layer.Split(';')[2];

            if (layer.Split(';').Length > 3)
                symbology = layer.Split(';')[3];

            return true;
        }

        private async Task<IEnumerable<IFeature>> FetchFeaturesAsync2(string tableName, MRect queryExtent, Dictionary<string, string> layerDefs = null)
        {
            try
            {
                // از TableInfo.Name برایtableName استفاده می‌کنیم، پس نیازی به تغییر اینجا نیست.
                if (!_context.TableInfos.Any(t => t.Name == tableName))
                    throw new ArgumentException($"جدول {tableName} در سیستم ثبت نشده است.");

                string filter = "";
                List<SqlParameter> parameters = new List<SqlParameter>();
                if (layerDefs != null && layerDefs.TryGetValue(tableName, out var filterValue) && !string.IsNullOrWhiteSpace(filterValue))
                {
                    var (sqlFilter, filterParams) = ParseFilterToSql(filterValue, tableName);
                    if (!string.IsNullOrEmpty(sqlFilter))
                    {
                        filter = $" AND ({sqlFilter})";
                        if (filterParams != null)
                        {
                            parameters.AddRange(filterParams);
                        }
                    }
                }

                parameters.Add(new SqlParameter("@minX", queryExtent.MinX));
                parameters.Add(new SqlParameter("@minY", queryExtent.MinY));
                parameters.Add(new SqlParameter("@maxX", queryExtent.MaxX));
                parameters.Add(new SqlParameter("@maxY", queryExtent.MaxY));
                parameters.Add(new SqlParameter("@srid", DatabaseSRID));

                var sql = $@"
                     SELECT TOP 1000 *
                     FROM [{tableName}]
                     WHERE Shape.STIntersects(geometry::STGeomFromText('POLYGON((@minX @minY, @maxX @minY, @maxX @maxY, @minX @maxY, @minX @minY))', @srid)) = 1
                     {filter}
                 ";

                var features = new List<IFeature>();
                using (var connection = new SqlConnection(_context.Database.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    using (var command = new SqlCommand(sql, connection))
                    {
                        foreach (var param in parameters)
                        {
                            command.Parameters.Add(param);
                        }

                        using var reader = await command.ExecuteReaderAsync();
                        var wkbReader = new WKBReader();
                        var schemaTable = reader.GetSchemaTable();
                        var fieldNames = new List<string>();
                        foreach (System.Data.DataRow row in schemaTable.Rows)
                        {
                            fieldNames.Add(row["ColumnName"].ToString());
                        }

                        while (await reader.ReadAsync())
                        {
                            var feature = new Mapsui.Nts.GeometryFeature();
                            foreach (var fieldName in fieldNames)
                            {
                                var value = reader[fieldName] == DBNull.Value ? null : reader[fieldName];
                                if (fieldName.Equals("Shape", StringComparison.OrdinalIgnoreCase) && value is byte[] wkb)
                                {
                                    try
                                    {
                                        feature.Geometry = wkbReader.Read(wkb);
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"Error reading WKB for feature in layer '{tableName}': {ex.Message}");
                                        feature.Geometry = null;
                                    }
                                }
                                else
                                {
                                    feature[fieldName] = value;
                                }
                            }
                            if (feature.Geometry != null)
                                features.Add(feature);
                        }
                    }
                }
                return features;
            }
            catch (Exception ex)
            {

                throw;
            }
        }
        private async Task<IEnumerable<IFeature>> FetchFeaturesAsync(string tableName, MRect queryExtent, Dictionary<string, string> layerDefs = null)
        {
            try
            {
                // بررسی وجود جدول
                if (!_context.TableInfos.Any(t => t.Name == tableName))
                    throw new ArgumentException($"جدول {tableName} در سیستم ثبت نشده است.");

                string filter = "";
                List<SqlParameter> parameters = new List<SqlParameter>();
                if (layerDefs != null && layerDefs.TryGetValue(tableName, out var filterValue) && !string.IsNullOrWhiteSpace(filterValue))
                {
                    var (sqlFilter, filterParams) = ParseFilterToSql(filterValue, tableName);
                    if (!string.IsNullOrEmpty(sqlFilter))
                    {
                        filter = $" AND ({sqlFilter})";
                        if (filterParams != null)
                        {
                            parameters.AddRange(filterParams);
                        }
                    }
                }

                // افزودن پارامتر SRID
                parameters.Add(new SqlParameter("@srid", DatabaseSRID));

                // ساخت رشته WKT با مقادیر واقعی
                var wkt = $"POLYGON(({queryExtent.MinX} {queryExtent.MinY}, {queryExtent.MaxX} {queryExtent.MinY}, {queryExtent.MaxX} {queryExtent.MaxY}, {queryExtent.MinX} {queryExtent.MaxY}, {queryExtent.MinX} {queryExtent.MinY}))";

                var sql = $@"
                    SELECT TOP 100000 Shape.STAsBinary() AS ShapeWKB, *
                    FROM [{tableName}]
                    WHERE Shape.STIntersects(geometry::STGeomFromText('{wkt}', @srid)) = 1
                    {filter}
                ";

                var features = new List<IFeature>();
                using (var connection = new SqlConnection(_context.Database.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    using (var command = new SqlCommand(sql, connection))
                    {
                        foreach (var param in parameters)
                        {
                            command.Parameters.Add(param);
                        }

                        using var reader = await command.ExecuteReaderAsync();
                        var wkbReader = new WKBReader();
                        var schemaTable = reader.GetSchemaTable();
                        var fieldNames = new List<string>();
                        foreach (System.Data.DataRow row in schemaTable.Rows)
                        {
                            fieldNames.Add(row["ColumnName"].ToString());
                        }

                        while (await reader.ReadAsync())
                        {
                            var feature = new Mapsui.Nts.GeometryFeature();
                            foreach (var fieldName in fieldNames)
                            {
                                var value = reader[fieldName] == DBNull.Value ? null : reader[fieldName];
                                if (fieldName.Equals("ShapeWKB", StringComparison.OrdinalIgnoreCase) && value is byte[] wkb)
                                {
                                    try
                                    {
                                        feature.Geometry = wkbReader.Read(wkb);
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"Error reading WKB for feature in layer '{tableName}': {ex.Message}");
                                        feature.Geometry = null;
                                    }
                                }
                                else if (!fieldName.Equals("Shape", StringComparison.OrdinalIgnoreCase)) // نادیده گرفتن ستون Shape اصلی
                                {
                                    feature[fieldName] = value;
                                }
                            }
                            if (feature.Geometry != null)
                                features.Add(feature);
                        }
                    }
                }
                return features;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in FetchFeaturesAsync for table '{tableName}': {ex.Message}");
                throw new Exception($"خطا در دریافت ویژگی‌ها از جدول {tableName}: {ex.Message}", ex);
            }
        }
        private (string sqlFilter, List<SqlParameter> parameters) ParseFilterToSql(string filterValue, string tableName)
        {
            var allowedFields = _context.TableInfos
                .Where(t => t.Name == tableName)
                .SelectMany(t => _context.SymbologyInfos
                    .Where(s => s.TableInfoId == t.Id)
                    .Select(s => s.FieldName))
                .Distinct()
                .Where(f => !string.IsNullOrEmpty(f))
                .ToList();

            var allowedOperators = new[] { "=", "<", ">", "<=", ">=", "<>", "LIKE" };

            var parts = filterValue.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length != 3)
                return (null, null); // Invalid filter format

            var field = parts[0];
            var op = parts[1];
            var value = parts[2];

            if (!allowedFields.Contains(field, StringComparer.OrdinalIgnoreCase) ||
                !allowedOperators.Contains(op, StringComparer.OrdinalIgnoreCase))
                return (null, null); // Field or operator not allowed

            var paramName = $"@filterValue_{Guid.NewGuid().ToString("N").Substring(0, 8)}";
            var sqlFilter = $"[{field}] {op} {paramName}";
            var parameters = new List<SqlParameter>
              {
                  new SqlParameter(paramName, value)
              };

            return (sqlFilter, parameters);
        }

        private (double[] bbox, string error) ParseBbox(string bboxString)
        {
            var parts = bboxString.Split(',');
            if (parts.Length != 4 || !double.TryParse(parts[0], out double minX) ||
                !double.TryParse(parts[1], out double minY) ||
                !double.TryParse(parts[2], out double maxX) ||
                !double.TryParse(parts[3], out double maxY))
            {
                return (null, "Invalid BBOX format. Expected 'minX,minY,maxX,maxY'.");
            }
            return (new double[] { minX, minY, maxX, maxY }, null);
        }

        private byte[] GenerateTransparentPng(int width, int height)
        {
            using (var surface = SKSurface.Create(new SKImageInfo(width, height, SKColorType.Rgba8888, SKAlphaType.Premul)))
            {
                var canvas = surface.Canvas;
                canvas.Clear(SKColors.Transparent);
                using (var image = surface.Snapshot())
                using (var data = image.Encode(SKEncodedImageFormat.Png, 100))
                {
                    return data.ToArray();
                }
            }
        }


    }

}