﻿using BaseGIS.Core.Entities;
using BaseGIS.Infrastructure.Persistence;
using BaseGIS.Web.Utilities;
using Mapsui;
using Mapsui.Layers;
using Mapsui.Rendering.Skia;
using Mapsui.Styles;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.IO;
using SkiaSharp;
using System.Text.Json;
using Color = Mapsui.Styles.Color;
using TableInfo = BaseGIS.Core.Entities.TableInfo;

namespace BaseGIS.Web.Controllers
{
    [Route("rest")]
    public partial class RESTController : Controller
    {
        private readonly ApplicationDbContext _context;
        private const int DatabaseSRID = 3857;

        public RESTController(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// Handles requests for the map service description, typically for L.esri.dynamicMapLayer's initial handshake.
        /// URL: /rest/services/map/mapserver?f=json
        /// </summary>
        [HttpGet("services/{Name}/{Types}")] // Matches /rest/services/map/mapserver
        public async Task<IActionResult> ServiceDescription(string Name, string Types, [FromQuery] string f)
        {
            if (Name.ToLower() == "map" && Types.ToLower() == "mapserver" && f?.ToLower() == "json")
            {
                // اینجا اگر ShortName رو هم میخواین به کلاینت بفرستید، باید به Select اضافه کنید.
                // در حال حاضر L.esri.dynamicMapLayer فقط به name و id نیاز داره،
                // ولی اگر UI شما بخواد ShortName رو نمایش بده، باید اینجا اضافه بشه.
                var layers = _context.TableInfos.Select(t => new
                {
                    id = t.Id, // از ID واقعی (عددی) TableInfo استفاده کنید. این برای L.esri مهم است.
                    name = t.Name, // این نام در L.esri استفاده می‌شود
                    shortName = t.ShortName, // اضافه کردن ShortName اگر در TableInfo هست
                    defaultVisibility = true,
                    parentLayerId = -1,
                    subLayerIds = new int[] { },
                    minScale = 0,
                    maxScale = 0
                }).ToList();
                var serviceInfo = new
                {
                    currentVersion = 10.81, // Or whatever version you want to mimic
                    serviceDescription = "GIS Map Service",
                    mapName = "Layers",
                    description = "Dynamic map service for BaseGIS layers.",
                    spatialReference = new { wkid = DatabaseSRID, latestWkid = DatabaseSRID },
                    singleFusedMapCache = false,
                    capabilities = "Map,Query,Data",
                    layers = layers,
                    supportedImageFormatTypes = "PNG32,PNG24,PNG,JPG,DIB,TIFF,GIF",
                    supportsDynamicLayers = true,
                };

                return Ok(serviceInfo);
            }

            return NotFound($"Service description not found for path: /{Name}/{Types}. Expected /services/map/mapserver?f=json");
        }

        /// <summary>
        /// This action serves as the main entry point for L.esri.dynamicMapLayer image requests.
        /// URL structure: /rest/services/{Name}/{Types}/{Layer} (where Layer is "export")
        /// </summary>
        [HttpGet("services/{Name}/{Types}/{Layer}")]
        public IActionResult Services(string Name, string Types, string Layer)
        {
            if (Name.ToLower() == "map" && Types.ToLower() == "mapserver" && Layer.ToLower() == "export")
            {
                // به جای RedirectToAction خالی، همه query parameters را به صورت RouteValueDictionary منتقل می‌کنیم
                // این اطمینان می‌دهد که [FromQuery] در اکشن ExportMapImage به درستی کار می‌کند.
                var routeValues = new RouteValueDictionary();
                foreach (var queryParam in HttpContext.Request.Query)
                {
                    routeValues.Add(queryParam.Key, queryParam.Value);
                }
                // همچنین، مطمئن می‌شویم که پارامترهای Name, Types, Layer که در مسیر بودند، به عنوان RouteValue منتقل نمی‌شوند
                // چون این پارامترها مخصوص این مسیر هستند و ExportMapImage به آن‌ها نیاز ندارد.
                routeValues.Remove("Name");
                routeValues.Remove("Types");
                routeValues.Remove("Layer");

                return RedirectToAction(nameof(ExportMapImage), routeValues);
            }
            else
            {
                return NotFound($"Service not found for path: /{Name}/{Types}/{Layer}. Expected /services/map/mapserver/export");
            }
        }

        /// <summary>
        /// Renders a map image based on provided parameters.
        /// This method can now be called directly by clients at /rest/export,
        /// or redirected to from the 'Services' action.
        /// </summary>
        /// <param name="layers">Comma-separated list of layer IDs/names to display.</param>
        /// <param name="bbox">Bounding box (minX,minY,maxX,maxY) for map extent.</param>
        /// <param name="size">Desired dimensions of the output image (width,height).</param>
        /// <param name="symbologyName">Optional: Name of symbology to apply.</param>
        /// <param name="layerDefs">Optional: JSON string of layer definitions for custom queries.</param>
        /// <param name="format">Output image format (e.g., "png", "jpg").</param>
        /// <param name="dpi">DPI for rendering.</param>
        /// <param name="transparent">Whether background should be transparent.</param>
        [HttpGet("export")]
        public async Task<IActionResult> ExportMapImage(
            [FromQuery] string layers = null,
            [FromQuery] string bbox = null,
            [FromQuery] string size = null,
            [FromQuery] string layerDefs = "{}",
            [FromQuery] string format = "png",
            [FromQuery] string dpi = "96",
            [FromQuery] string transparent = "true")
        {
            // 1. تجزیه پارامتر size به width و height
            int width = 0, height = 0;
            if (!string.IsNullOrEmpty(size))
            {
                var sizeParts = size.Split(',');
                if (sizeParts.Length == 2 && int.TryParse(sizeParts[0], out width) && int.TryParse(sizeParts[1], out height))
                {
                }
                else
                {
                    return BadRequest("فرمت پارامتر 'size' نامعتبر است. انتظار 'width,height' می‌رود.");
                }
            }
            else
            {
                return BadRequest("پارامتر 'size' الزامی است.");
            }

            // 2. بررسی پارامترهای ضروری
            if (string.IsNullOrEmpty(layers) || string.IsNullOrEmpty(bbox) || width <= 0 || height <= 0)
            {
                return BadRequest($"پارامترهای 'layers', 'bbox', 'width', 'height' الزامی هستند. " +
                                  $"Current: layers='{layers}', bbox='{bbox}', width={width}, height={height}");
            }

            // 3. تجزیه پارامتر layers
            var layerIdentifiers = layers.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                        .Select(s => s.Trim())
                                        .Where(s => !string.IsNullOrEmpty(s))
                                        .Select(id => id.StartsWith("show:", StringComparison.OrdinalIgnoreCase) ? id.Substring(5) :
                                                      id.StartsWith("hide:", StringComparison.OrdinalIgnoreCase) ? null : id)
                                        .Where(id => id != null)
                                        .ToList();

            if (!layerIdentifiers.Any())
            {
                Console.WriteLine("No layers specified. Returning transparent image.");
                return File(GenerateTransparentPng(width, height), "image/png");
            }

            try
            {
                // Parse BBOX
                var (bbxs, bboxError) = ParseBbox(bbox);
                if (bboxError != null) return BadRequest(bboxError);
                var extent = new MRect(bbxs[0], bbxs[1], bbxs[2], bbxs[3]);

                // اعتبارسنجی extent
                if (extent.Width <= 0 || extent.Height <= 0 || double.IsNaN(extent.MinX) || double.IsNaN(extent.MinY))
                {
                    Console.WriteLine($"Invalid extent: MinX={extent.MinX}, MinY={extent.MinY}, MaxX={extent.MaxX}, MaxY={extent.MaxY}");
                    return BadRequest("محدوده bbox نامعتبر است.");
                }

                // اعتبارسنجی ابعاد تصویر
                if (width <= 0 || height <= 0 || width > 4096 || height > 4096)
                    return BadRequest("ابعاد تصویر نامعتبر است. عرض و ارتفاع باید بین 1 و 4096 باشند.");

                Dictionary<string, string> layerDefsDict;
                try
                {
                    layerDefsDict = string.IsNullOrWhiteSpace(layerDefs) || layerDefs == "{}"
                                    ? new Dictionary<string, string>()
                                    : JsonSerializer.Deserialize<Dictionary<string, string>>(layerDefs);
                }
                catch (JsonException ex)
                {
                    return BadRequest($"فرمت layerDefs نامعتبر است: {ex.Message}");
                }

                float scale = float.TryParse(dpi, out var parsedDpi) ? parsedDpi / 96f : 1f;

                using (var map = new Map
                {
                    CRS = $"EPSG:{DatabaseSRID}",
                    BackColor = transparent.Equals("true", StringComparison.OrdinalIgnoreCase) ? Color.Transparent : Color.White
                })
                {
                    bool hasVisibleFeatures = false;
                    MRect featuresExtent = null;

                    foreach (var identifier in layerIdentifiers)
                    {
                        string layerId = "";
                        string lbl = "";
                        string opacity = "";
                        string symbology = "";

                        if (checkLayerParams(identifier, out layerId, out lbl, out opacity, out symbology))
                        {
                            //string tableId = layerId[^4..];
                            string tableId = layerId.Substring(layerId.Length - 4);
                            int tableIdInt = int.Parse(tableId);

                            TableInfo tableInfo = await _context.TableInfos.FirstOrDefaultAsync(t => t.Id == tableIdInt);
                            if (tableInfo == null)
                            {
                                Console.WriteLine($"TableInfo with ShortName '{layerId}' not found. Skipping layer.");
                                continue;
                            }
                            SymbologyInfo symbologyInfo = null;
                            if (!string.IsNullOrEmpty(symbology))
                            {
                                int symbologyId = int.Parse(symbology);
                                symbologyInfo = await _context.SymbologyInfos.FirstOrDefaultAsync(s => s.TableInfoId == tableInfo.Id && s.Id == symbologyId);
                            }
                            else
                            {
                                symbologyInfo = await _context.SymbologyInfos.FirstOrDefaultAsync(s => s.TableInfoId == tableInfo.Id && s.IsDefault);
                            }

                            var features = await FetchFeaturesAsync(tableInfo.Name, extent, layerDefsDict);
                            if (features.Any())
                            {
                                // ****** تغییر ۱: همیشه از StyleCollection استفاده کنید ******
                                var styleCollection = new StyleCollection();

                                // تبدیل و افزودن استایل اصلی
                                Mapsui.Styles.IStyle mapsuiStyle = MapsuiSymbologyConverter.CreateMapsuiStyleFromDotSpatialSymbologyJson(
                                    symbologyInfo?.Json,
                                    tableInfo.DatasetType,
                                    symbologyInfo?.FieldName
                                );

                                if (mapsuiStyle == null)
                                {
                                    Console.WriteLine($"Symbology conversion returned null. Using default style.");
                                    mapsuiStyle = MapsuiSymbologyConverter.CreateDefaultMapsuiStyle(tableInfo.DatasetType);
                                }
                                styleCollection.Styles.Add(mapsuiStyle); // افزودن استایل اصلی به کالکشن

                                // افزودن برچسب‌گذاری اگر lbl مشخص شده باشد
                                if (!string.IsNullOrEmpty(lbl))
                                {
                                    var sampleFeature = features.FirstOrDefault();
                                    if (sampleFeature != null && sampleFeature.Fields.Contains(lbl))
                                    {
                                        // ****** تغییر ۲: استفاده از LabelColumn برای تعیین فیلد برچسب ******
                                        var labelStyle = new LabelStyle
                                        {
                                            LabelColumn = lbl, // <--- اصلاح کلیدی
                                            ForeColor = Color.Black,
                                            BackColor = new Brush(Color.FromArgb(128, 255, 255, 255)),
                                            Font = new Font { FontFamily = "Arial", Size = 12 },
                                            Halo = new Pen(Color.White, 2),
                                            Offset = new Offset(0, -10),
                                            CollisionDetection = true
                                        };
                                        styleCollection.Styles.Add(labelStyle); // افزودن استایل برچسب به همان کالکشن
                                        Console.WriteLine($"Label style added for layer '{tableInfo.Name}' using field '{lbl}'.");
                                    }
                                    else
                                    {
                                        Console.WriteLine($"Field '{lbl}' not found. Skipping labeling.");
                                    }
                                }

                                // تنظیم شفافیت لایه
                                double layerOpacity = 1.0;
                                if (!string.IsNullOrEmpty(opacity) && double.TryParse(opacity, out double parsedOpacity))
                                {
                                    layerOpacity = Math.Clamp(parsedOpacity / 100.0, 0.0, 1.0);
                                }

                                // ایجاد لایه با استفاده از StyleCollection
                                var mapLayer = new MemoryLayer(tableInfo.Name)
                                {
                                    Features = features,
                                    Style = styleCollection, // <--- همیشه از کالکشن استفاده کنید
                                    IsMapInfoLayer = true,
                                    Enabled = true,
                                    Opacity = layerOpacity
                                };

                                map.Layers.Add(mapLayer);
                                Console.WriteLine($"Layer '{tableInfo.Name}' added.");
                                hasVisibleFeatures = true;
                            }
                            else
                            {
                                Console.WriteLine($"No features found for layer '{tableInfo.Name}'.");
                            }
                        }
                    }

                    if (!map.Layers.Any() || !hasVisibleFeatures)
                    {
                        Console.WriteLine("No layers with visible features were added to the map. Returning transparent image.");
                        return File(GenerateTransparentPng(width, height), "image/png");
                    }

                    // تنظیم Viewport
                    var desiredResolution = Math.Max(extent.Width / width, extent.Height / height);// 1000; // رزولوشن مشابه TestMapImage
                    map.Navigator.CenterOnAndZoomTo(new MPoint(extent.Centroid.X, extent.Centroid.Y), desiredResolution);
                    var viewport = new Viewport
                    {
                        Width = width,
                        Height = height,
                        Resolution = desiredResolution,
                        CenterX = extent.Centroid.X,
                        CenterY = extent.Centroid.Y
                    };

                    Console.WriteLine($"Viewport: Width={viewport.Width}, Height={viewport.Height}, Resolution={viewport.Resolution}, CenterX={viewport.CenterX}, CenterY={viewport.CenterY}");
                    Console.WriteLine($"Extent: MinX={extent.MinX}, MinY={extent.MinY}, MaxX={extent.MaxX}, MaxY={extent.MaxY}");

                    // رندر تصویر
                    var renderer = new MapRenderer();
                    using var skBitmapStream = renderer.RenderToBitmapStream(viewport, map.Layers, map.BackColor, scale);

                    if (skBitmapStream == null || skBitmapStream.Length == 0)
                    {
                        Console.WriteLine("RenderToBitmapStream returned null or empty stream.");
                        return File(GenerateTransparentPng(width, height), "image/png");
                    }

                    Console.WriteLine($"Rendered stream size: {skBitmapStream.Length} bytes");

                    // تبدیل به فرمت خروجی
                    byte[] imageBytes;
                    string contentType;

                    if (format.Equals("jpg", StringComparison.OrdinalIgnoreCase) || format.Equals("jpeg", StringComparison.OrdinalIgnoreCase))
                    {
                        try
                        {
                            skBitmapStream.Position = 0;
                            using var skImage = SKImage.FromEncodedData(skBitmapStream);
                            if (skImage == null)
                            {
                                Console.WriteLine("Failed to create SKImage from stream.");
                                return File(GenerateTransparentPng(width, height), "image/png");
                            }
                            using var skData = skImage.Encode(SKEncodedImageFormat.Jpeg, 80);
                            imageBytes = skData.ToArray();
                            contentType = "image/jpeg";
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error converting to JPEG: {ex.Message}");
                            return File(GenerateTransparentPng(width, height), "image/png");
                        }
                    }
                    else
                    {
                        skBitmapStream.Position = 0;
                        imageBytes = skBitmapStream.ToArray();
                        contentType = "image/png";
                    }

                    Console.WriteLine($"Output image size: {imageBytes.Length} bytes, Format: {contentType}");
                    return File(imageBytes, contentType);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ExportMapImage: {ex.Message}");
                return File(GenerateTransparentPng(width, height), "image/png");
            }
        }

        [HttpPost("legend")]
        public ActionResult Legend(string layerId, int symbologyId)
        {
            try
            {
                // 1. اعتبارسنجی ورودی‌ها
                if (string.IsNullOrEmpty(layerId) || symbologyId <= 0)
                {
                    return BadRequest("LayerId and SymbologyId are required.");
                }

                // 2. استخراج tableId
                string tableId = layerId.Substring(layerId.Length - 4);
                if (!int.TryParse(tableId, out int tableIdInt))
                {
                    return BadRequest("Invalid LayerId format.");
                }

                // 3. اطلاعات سیمبولوژی و لایه را از دیتابیس واکشی کنید
                var symbologyInfo = _context.SymbologyInfos.Find(symbologyId);
                if (symbologyInfo == null)
                {
                    return NotFound($"Symbology with ID {symbologyId} not found.");
                }

                var tableInfo = _context.TableInfos.Find(tableIdInt);
                if (tableInfo == null)
                {
                    return NotFound($"TableInfo with ID {tableIdInt} not found.");
                }

                // 4. تولید آیتم‌های لجند
                var legendItems = MapsuiLegendGenerator.GenerateLegend(symbologyInfo.Json, tableInfo, symbologyInfo.FieldName);

                // 5. تبدیل به فرمت NodeTree
                var list = new List<NodeTree>();
                foreach (var item in legendItems)
                {
                    list.Add(new NodeTree
                    {
                        title = item.Label,
                        key = "L_" + Guid.NewGuid().ToString(), // کلید منحصر به فرد
                        icon = item.ImageData,
                        iconHeight = item.ImageHeight,
                        iconWidth = item.ImageWidth
                    });
                }

                // 6. بازگشت JSON
                return Json(list);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating legend: {ex.Message}");
                return StatusCode(500, "An error occurred while generating the legend.");
            }
        }

        public IActionResult TestMapImage(
            [FromQuery] string size = "500,500",
            [FromQuery] string format = "png",
            [FromQuery] string transparent = "false")
        {
            int width = 0, height = 0;
            try
            {
                // 1. تجزیه پارامتر size به width و height

                var sizeParts = size.Split(',');
                if (sizeParts.Length != 2 || !int.TryParse(sizeParts[0], out width) || !int.TryParse(sizeParts[1], out height))
                {
                    return BadRequest("فرمت پارامتر 'size' نامعتبر است. انتظار 'width,height' می‌رود.");
                }

                if (width <= 0 || height <= 0 || width > 4096 || height > 4096)
                {
                    return BadRequest("ابعاد تصویر نامعتبر است. عرض و ارتفاع باید بین 1 و 4096 باشند.");
                }

                // 2. ایجاد نقشه
                using (var map = new Map
                {
                    CRS = "EPSG:3857",
                    BackColor = transparent.Equals("true", StringComparison.OrdinalIgnoreCase) ? Color.Transparent : Color.White
                })
                {
                    // 3. ایجاد نقطه تستی (مختصات تهران در EPSG:3857)
                    var point = new MPoint(5907474, 3968250);
                    var feature = new PointFeature(point);
                    var mapLayer = new MemoryLayer
                    {
                        Name = "TestLayer",
                        Features = new List<IFeature> { feature },
                        Style = new SymbolStyle
                        {
                            Fill = new Brush(Color.Green),
                            SymbolScale = 1.0,
                            SymbolType = Mapsui.Styles.SymbolType.Ellipse,
                            Outline = new Pen(Color.Black, 2),
                            Opacity = 1.0f
                        },
                        IsMapInfoLayer = true,
                        Enabled = true,
                        Opacity = 1.0
                    };
                    map.Layers.Add(mapLayer);
                    Console.WriteLine($"Test layer added with 1 feature at ({point.X}, {point.Y}). Style: SymbolStyle, Scale: 1.0, Color: Green");

                    // 4. تنظیم محدوده نمایش
                    var extent = new MRect(point.X - 1000, point.Y - 1000, point.X + 1000, point.Y + 1000);
                    Console.WriteLine($"Extent: MinX={extent.MinX}, MinY={extent.MinY}, MaxX={extent.MaxX}, MaxY={extent.MaxY}");

                    // 5. تنظیم Viewport
                    var desiredResolution = 100.0; // رزولوشن ثابت برای زوم مناسب
                    map.Navigator.CenterOnAndZoomTo(new MPoint(point.X, point.Y), desiredResolution);
                    var viewport = new Viewport
                    {
                        Width = width,
                        Height = height,
                        Resolution = desiredResolution,
                        CenterX = point.X,
                        CenterY = point.Y
                    };

                    Console.WriteLine($"Viewport: Width={viewport.Width}, Height={viewport.Height}, Resolution={viewport.Resolution}, CenterX={viewport.CenterX}, CenterY={viewport.CenterY}");

                    // 6. رندر نقشه به تصویر
                    var renderer = new MapRenderer();
                    using var skBitmapStream = renderer.RenderToBitmapStream(viewport, map.Layers, map.BackColor, 1.0f);

                    if (skBitmapStream == null || skBitmapStream.Length == 0)
                    {
                        Console.WriteLine("RenderToBitmapStream returned null or empty stream.");
                        return File(GenerateTransparentPng(width, height), "image/png");
                    }

                    Console.WriteLine($"Rendered stream size: {skBitmapStream.Length} bytes");

                    // 7. تبدیل به فرمت خروجی
                    byte[] imageBytes;
                    string contentType;

                    if (format.Equals("jpg", StringComparison.OrdinalIgnoreCase) || format.Equals("jpeg", StringComparison.OrdinalIgnoreCase))
                    {
                        try
                        {
                            skBitmapStream.Position = 0;
                            using var skImage = SKImage.FromEncodedData(skBitmapStream);
                            if (skImage == null)
                            {
                                Console.WriteLine("Failed to create SKImage from stream.");
                                return File(GenerateTransparentPng(width, height), "image/png");
                            }
                            using var skData = skImage.Encode(SKEncodedImageFormat.Jpeg, 90);
                            imageBytes = skData.ToArray();
                            contentType = "image/jpeg";
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error converting to JPEG: {ex.Message}");
                            return File(GenerateTransparentPng(width, height), "image/png");
                        }
                    }
                    else
                    {
                        skBitmapStream.Position = 0;
                        imageBytes = skBitmapStream.ToArray();
                        contentType = "image/png";
                    }

                    Console.WriteLine($"Output image size: {imageBytes.Length} bytes, Format: {contentType}");
                    return File(imageBytes, contentType);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in TestMapImage: {ex.Message}");
                return File(GenerateTransparentPng(width, height), "image/png");
            }
        }
        public static bool checkLayerParams(string layer, out string layerId, out string lbl, out string opacity, out string symbology)
        {
            opacity = "";
            lbl = "";
            symbology = "";
            layerId = layer.Split(';')[0];
            if (layer.Split(';').Length > 1)
                lbl = layer.Split(';')[1];

            if (layer.Split(';').Length > 2)
                opacity = layer.Split(';')[2];

            if (layer.Split(';').Length > 3)
                symbology = layer.Split(';')[3];

            return true;
        }

        private async Task<IEnumerable<IFeature>> FetchFeaturesAsync2(string tableName, MRect queryExtent, Dictionary<string, string> layerDefs = null)
        {
            try
            {
                // از TableInfo.Name برایtableName استفاده می‌کنیم، پس نیازی به تغییر اینجا نیست.
                if (!_context.TableInfos.Any(t => t.Name == tableName))
                    throw new ArgumentException($"جدول {tableName} در سیستم ثبت نشده است.");

                string filter = "";
                List<SqlParameter> parameters = new List<SqlParameter>();
                if (layerDefs != null && layerDefs.TryGetValue(tableName, out var filterValue) && !string.IsNullOrWhiteSpace(filterValue))
                {
                    var (sqlFilter, filterParams) = ParseFilterToSql(filterValue, tableName);
                    if (!string.IsNullOrEmpty(sqlFilter))
                    {
                        filter = $" AND ({sqlFilter})";
                        if (filterParams != null)
                        {
                            parameters.AddRange(filterParams);
                        }
                    }
                }

                parameters.Add(new SqlParameter("@minX", queryExtent.MinX));
                parameters.Add(new SqlParameter("@minY", queryExtent.MinY));
                parameters.Add(new SqlParameter("@maxX", queryExtent.MaxX));
                parameters.Add(new SqlParameter("@maxY", queryExtent.MaxY));
                parameters.Add(new SqlParameter("@srid", DatabaseSRID));

                var sql = $@"
                     SELECT TOP 1000 *
                     FROM [{tableName}]
                     WHERE Shape.STIntersects(geometry::STGeomFromText('POLYGON((@minX @minY, @maxX @minY, @maxX @maxY, @minX @maxY, @minX @minY))', @srid)) = 1
                     {filter}
                 ";

                var features = new List<IFeature>();
                using (var connection = new SqlConnection(_context.Database.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    using (var command = new SqlCommand(sql, connection))
                    {
                        foreach (var param in parameters)
                        {
                            command.Parameters.Add(param);
                        }

                        using var reader = await command.ExecuteReaderAsync();
                        var wkbReader = new WKBReader();
                        var schemaTable = reader.GetSchemaTable();
                        var fieldNames = new List<string>();
                        foreach (System.Data.DataRow row in schemaTable.Rows)
                        {
                            fieldNames.Add(row["ColumnName"].ToString());
                        }

                        while (await reader.ReadAsync())
                        {
                            var feature = new Mapsui.Nts.GeometryFeature();
                            foreach (var fieldName in fieldNames)
                            {
                                var value = reader[fieldName] == DBNull.Value ? null : reader[fieldName];
                                if (fieldName.Equals("Shape", StringComparison.OrdinalIgnoreCase) && value is byte[] wkb)
                                {
                                    try
                                    {
                                        feature.Geometry = wkbReader.Read(wkb);
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"Error reading WKB for feature in layer '{tableName}': {ex.Message}");
                                        feature.Geometry = null;
                                    }
                                }
                                else
                                {
                                    feature[fieldName] = value;
                                }
                            }
                            if (feature.Geometry != null)
                                features.Add(feature);
                        }
                    }
                }
                return features;
            }
            catch (Exception ex)
            {

                throw;
            }
        }
        private async Task<IEnumerable<IFeature>> FetchFeaturesAsync(string tableName, MRect queryExtent, Dictionary<string, string> layerDefs = null)
        {
            try
            {
                // بررسی وجود جدول
                if (!_context.TableInfos.Any(t => t.Name == tableName))
                    throw new ArgumentException($"جدول {tableName} در سیستم ثبت نشده است.");

                string filter = "";
                List<SqlParameter> parameters = new List<SqlParameter>();
                if (layerDefs != null && layerDefs.TryGetValue(tableName, out var filterValue) && !string.IsNullOrWhiteSpace(filterValue))
                {
                    var (sqlFilter, filterParams) = ParseFilterToSql(filterValue, tableName);
                    if (!string.IsNullOrEmpty(sqlFilter))
                    {
                        filter = $" AND ({sqlFilter})";
                        if (filterParams != null)
                        {
                            parameters.AddRange(filterParams);
                        }
                    }
                }

                // افزودن پارامتر SRID
                parameters.Add(new SqlParameter("@srid", DatabaseSRID));

                // ساخت رشته WKT با مقادیر واقعی
                var wkt = $"POLYGON(({queryExtent.MinX} {queryExtent.MinY}, {queryExtent.MaxX} {queryExtent.MinY}, {queryExtent.MaxX} {queryExtent.MaxY}, {queryExtent.MinX} {queryExtent.MaxY}, {queryExtent.MinX} {queryExtent.MinY}))";

                var sql = $@"
                    SELECT TOP 100000 Shape.STAsBinary() AS ShapeWKB, *
                    FROM [{tableName}]
                    WHERE Shape.STIntersects(geometry::STGeomFromText('{wkt}', @srid)) = 1
                    {filter}
                ";

                var features = new List<IFeature>();
                using (var connection = new SqlConnection(_context.Database.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    using (var command = new SqlCommand(sql, connection))
                    {
                        foreach (var param in parameters)
                        {
                            command.Parameters.Add(param);
                        }

                        using var reader = await command.ExecuteReaderAsync();
                        var wkbReader = new WKBReader();
                        var schemaTable = reader.GetSchemaTable();
                        var fieldNames = new List<string>();
                        foreach (System.Data.DataRow row in schemaTable.Rows)
                        {
                            fieldNames.Add(row["ColumnName"].ToString());
                        }

                        while (await reader.ReadAsync())
                        {
                            var feature = new Mapsui.Nts.GeometryFeature();
                            foreach (var fieldName in fieldNames)
                            {
                                var value = reader[fieldName] == DBNull.Value ? null : reader[fieldName];
                                if (fieldName.Equals("ShapeWKB", StringComparison.OrdinalIgnoreCase) && value is byte[] wkb)
                                {
                                    try
                                    {
                                        feature.Geometry = wkbReader.Read(wkb);
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"Error reading WKB for feature in layer '{tableName}': {ex.Message}");
                                        feature.Geometry = null;
                                    }
                                }
                                else if (!fieldName.Equals("Shape", StringComparison.OrdinalIgnoreCase)) // نادیده گرفتن ستون Shape اصلی
                                {
                                    feature[fieldName] = value;
                                }
                            }
                            if (feature.Geometry != null)
                                features.Add(feature);
                        }
                    }
                }
                return features;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in FetchFeaturesAsync for table '{tableName}': {ex.Message}");
                throw new Exception($"خطا در دریافت ویژگی‌ها از جدول {tableName}: {ex.Message}", ex);
            }
        }
        private (string sqlFilter, List<SqlParameter> parameters) ParseFilterToSql(string filterValue, string tableName)
        {
            var allowedFields = _context.TableInfos
                .Where(t => t.Name == tableName)
                .SelectMany(t => _context.SymbologyInfos
                    .Where(s => s.TableInfoId == t.Id)
                    .Select(s => s.FieldName))
                .Distinct()
                .Where(f => !string.IsNullOrEmpty(f))
                .ToList();

            var allowedOperators = new[] { "=", "<", ">", "<=", ">=", "<>", "LIKE" };

            var parts = filterValue.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length != 3)
                return (null, null); // Invalid filter format

            var field = parts[0];
            var op = parts[1];
            var value = parts[2];

            if (!allowedFields.Contains(field, StringComparer.OrdinalIgnoreCase) ||
                !allowedOperators.Contains(op, StringComparer.OrdinalIgnoreCase))
                return (null, null); // Field or operator not allowed

            var paramName = $"@filterValue_{Guid.NewGuid().ToString("N").Substring(0, 8)}";
            var sqlFilter = $"[{field}] {op} {paramName}";
            var parameters = new List<SqlParameter>
              {
                  new SqlParameter(paramName, value)
              };

            return (sqlFilter, parameters);
        }

        private (double[] bbox, string error) ParseBbox(string bboxString)
        {
            var parts = bboxString.Split(',');
            if (parts.Length != 4 || !double.TryParse(parts[0], out double minX) ||
                !double.TryParse(parts[1], out double minY) ||
                !double.TryParse(parts[2], out double maxX) ||
                !double.TryParse(parts[3], out double maxY))
            {
                return (null, "Invalid BBOX format. Expected 'minX,minY,maxX,maxY'.");
            }
            return (new double[] { minX, minY, maxX, maxY }, null);
        }

        private byte[] GenerateTransparentPng(int width, int height)
        {
            using (var surface = SKSurface.Create(new SKImageInfo(width, height, SKColorType.Rgba8888, SKAlphaType.Premul)))
            {
                var canvas = surface.Canvas;
                canvas.Clear(SKColors.Transparent);
                using (var image = surface.Snapshot())
                using (var data = image.Encode(SKEncodedImageFormat.Png, 100))
                {
                    return data.ToArray();
                }
            }
        }


    }

    public class NodeTree
    {
        public bool active { get; set; }
        public bool checkbox { get; set; }
        public bool expanded { get; set; }

        public bool focus { get; set; }

        public bool folder { get; set; }

        public string key { get; set; }

        public bool lazy { get; set; }

        public string icon { get; set; }

        public bool selected { get; set; }

        public string title { get; set; }

        public string tooltip { get; set; }

        public object data { get; set; }

        public int iconWidth { get; set; }
        public int iconHeight { get; set; }
    }
}