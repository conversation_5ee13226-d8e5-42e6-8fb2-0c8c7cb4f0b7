using BaseGIS.Core.Entities;
using BaseGIS.Core.Interfaces;
using BaseGIS.Core.Services;
using BaseGIS.Infrastructure.Persistence;
using BaseGIS.Infrastructure.Repositories;
using BaseGIS.Web.DTOs;
using BaseGIS.Web.Helper;
using BaseGIS.Web.Hubs;
using BaseGIS.Web.Services;
using BaseGIS.Web.ViewModels;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.SqlServer.Types;
using NetTopologySuite;
using NetTopologySuite.Features;
using NetTopologySuite.Geometries;
using NetTopologySuite.IO;
using NetTopologySuite.IO.ShapeFile.Extended;
using NetTopologySuite.IO.VectorTiles;
using ProjNet.CoordinateSystems;
using ProjNet.CoordinateSystems.Transformations;
using System.Data;
using System.IO.Compression;
using System.Linq.Dynamic.Core;

namespace BaseGIS.Web.Controllers
{
    public class DatabaseController : Controller
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly RoleManager<ApplicationRole> _roleManager;
        private readonly IDdlService _ddlService;
        private readonly ILogger<DatabaseController> _logger;
        private readonly IConfiguration _configuration;
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly IWebHostEnvironment _environment;
        private readonly IHubContext<TileProgressHub> _hubContext;
        private IGroupInfoRepository _groupInfoRepository;
        public DatabaseController(ApplicationDbContext dbContext, RoleManager<ApplicationRole> roleManager,
            IDdlService ddlService, ILogger<DatabaseController> logger, IConfiguration configuration,
            IServiceScopeFactory scopeFactory, IWebHostEnvironment environment,
            IHubContext<TileProgressHub> hubContext)
        {
            _dbContext = dbContext;
            _roleManager = roleManager;
            _ddlService = ddlService;
            _logger = logger;
            _configuration = configuration;
            _scopeFactory = scopeFactory;
            _environment = environment;
            _hubContext = hubContext;

            this._groupInfoRepository = new GroupInfoRepository(dbContext);
        }
        #region Layer Setting

        #region Layer
        public ActionResult LayerSetting()
        {
            ViewBag.PageTitle = " مدیریت ساختار لایه ";
            var tableInfos = _dbContext.TableInfos.Include("GroupInfo").ToList();//from tableInfo in _tableInfoRepository.GetTableInfos()                             select tableInfo;
            return View(tableInfos);
        }

        public ActionResult _Layer_List()
        {
            //var userName = User.Identity.Name;
            //var user = _dbContext.Users.FirstOrDefault(u => u.UserName == userName);
            //ApplicationRole role = null;

            //if (user != null)
            //{
            //    var roleName = _dbContext.UserRoles
            //        .Where(ur => ur.UserId == user.Id)
            //        .Join(_dbContext.Roles, ur => ur.RoleId, r => r.Id, (ur, r) => r.Name)
            //        .FirstOrDefault();

            //    if (roleName != null)
            //    {
            //        role = _roleManager.Roles.FirstOrDefault(r => r.Name == roleName);
            //    }
            //}

            //ViewBag.Role = role;

            List<TableInfo> tableInfos = _dbContext.TableInfos.Include("GroupInfo").ToList();

            return PartialView(tableInfos);
        }

        public ActionResult _Layer_Managment()
        {
            string id = Request.Query["id"];
            if (id != null)
            {
                var tblinfo = _dbContext.TableInfos.Find(int.Parse(id));
                ViewBag.tableInfo = tblinfo;
                DBManagement db = new DBManagement(_configuration);
                var dt = db.SelectTableSQL("select Count(objectid),Max(Time) from " + tblinfo.Name);
                if (dt.Rows.Count > 0)
                {
                    ViewBag.Count = "تعداد رکورد: " + dt.Rows[0][0].ToString();
                    ViewBag.Time = "آخرین ویرایش: " + dt.Rows[0][1].ToString();
                }
            }
            var groupInfos = _groupInfoRepository.GetGroupInfos();

            return PartialView(groupInfos);
        }


        /// <summary>
        /// Create Or Update Layer Or Table In Database
        /// </summary>
        /// <param name="collection">
        /// Parameter In Table Or Database
        /// </param>
        /// <returns></returns>

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult _Layer_Managment(IFormCollection collection)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    int ids = 0;
                    int.TryParse(collection["ID"], out ids);

                    if (ids > 0)
                    {
                        //== Update Table Or Feature
                        var tableInfo = _dbContext.TableInfos.Find(ids);// _tableInfoRepository.GetTableInfoByID(ids);
                        tableInfo.AliasName = collection["AliasName"];

                        //== Set Layer in Group
                        int groupID = 0;
                        int.TryParse(collection["GroupInfo"], out groupID);
                        tableInfo.GroupInfo = _dbContext.GroupInfos.Find(groupID);

                        double MaxScale = 0;
                        double.TryParse(collection["MaxScale"], out MaxScale);
                        tableInfo.MaxScale = MaxScale;

                        double MinScale = 0;
                        double.TryParse(collection["MinScale"], out MinScale);
                        tableInfo.MinScale = MinScale;

                        double MaxLabelScale = 0;
                        double.TryParse(collection["MaxLabelScale"], out MaxLabelScale);
                        tableInfo.MaxLabelScale = MaxLabelScale;

                        double MinLabelScale = 0;
                        double.TryParse(collection["MinLabelScale"], out MinLabelScale);
                        tableInfo.MinLabelScale = MinLabelScale;

                        //tableInfo.IsIntegration = collection["IsIntegration"] == "on" ? true : false;

                        _dbContext.SaveChanges();
                        return Json(new { success = true, responseText = "با موفقیت ذخیره شد" });
                    }
                    else
                    {
                        var nm = collection["Name"].ToString().ToLower();
                        var regexItem = new System.Text.RegularExpressions.Regex("^[a-zA-Z0-9 _]*$");
                        if (!regexItem.IsMatch(nm))
                        {
                            return Json(new { success = false, responseText = "نام دیتابیس غیر قابل قبول است" });
                        }
                        else
                        {
                            var tblExist = _dbContext.TableInfos.Where(a => a.Name.ToLower() == nm).FirstOrDefault();
                            if (tblExist != null)
                                return Json(new { success = false, responseText = "این جدول قبلا ایجاد شده است" });

                            var tableInfo = new TableInfo();
                            //tableInfo.IsIntegration = collection["IsIntegration"] == "on" ? true : false;


                            var shrt = collection["ShortName"].ToString().ToLower();
                            if (shrt.Length != 2)
                                return Json(new { success = false, responseText = "نام کوتاه باید دو حرفی باشد" });


                            var charFirst = shrt[0].ToString();
                            //if (tableInfo.IsIntegration)
                            //    if (!(charFirst == "t" || charFirst == "u" || charFirst == "v" || charFirst == "w" || charFirst == "x"))
                            //        return Json(new { success = false, responseText = "نام کوتاه باید دو حرفی و با T,U,V,W,X شروع شده باشد" } );


                            var tblShrtExist = _dbContext.TableInfos.Where(a => a.ShortName.ToLower() == shrt).FirstOrDefault();
                            if (tblShrtExist != null)
                                return Json(new { success = false, responseText = "نام کوتاه قبلا ایجاد شده است" });

                            //== Create New Table Or Feature 

                            tableInfo.Name = collection["Name"];
                            tableInfo.DatasetType = collection["DatasetType"];
                            tableInfo.AliasName = collection["AliasName"];
                            tableInfo.ShortName = collection["ShortName"];
                            //== Create And Check Table In database

                            //== Set Layer in Group
                            int groupID = 0;
                            int.TryParse(collection["GroupInfo"], out groupID);
                            tableInfo.GroupInfo = _groupInfoRepository.GetGroupInfoByID(groupID);

                            double MaxScale = 0;
                            double.TryParse(collection["MaxScale"], out MaxScale);
                            tableInfo.MaxScale = MaxScale;

                            double MinScale = 0;
                            double.TryParse(collection["MinScale"], out MinScale);
                            tableInfo.MinScale = MinScale;

                            double MaxLabelScale = 0;
                            double.TryParse(collection["MaxLabelScale"], out MaxLabelScale);
                            tableInfo.MaxLabelScale = MaxLabelScale;

                            double MinLabelScale = 0;
                            double.TryParse(collection["MinLabelScale"], out MinLabelScale);
                            tableInfo.MinLabelScale = MinLabelScale;
                            tableInfo.ValidationRule = string.Empty;

                            tableInfo.FieldInfos = new List<FieldInfo>();

                            var datMin = DateTime.Now.Subtract(new TimeSpan(1000, 0, 0, 0));
                            tableInfo.FieldInfos.Add(new FieldInfo() { Name = "OBJECTID", FieldType = "int", AliasName = "شناسه", Editable = false, IsDisplay = true, IsRequired = true, IsUnique = true, Updated = datMin });
                            tableInfo.FieldInfos.Add(new FieldInfo() { Name = "GCode", FieldType = "nvarchar", FieldLength = 254, AliasName = "GCode", Editable = false, IsDisplay = true, IsRequired = true, IsUnique = true, Updated = datMin });
                            tableInfo.FieldInfos.Add(new FieldInfo() { Name = "CreateTime", FieldType = "datetime", FieldLength = 50, AliasName = "تاریخ ایجاد", Editable = false, IsDisplay = true, IsRequired = false, IsUnique = false, Updated = datMin });
                            tableInfo.FieldInfos.Add(new FieldInfo() { Name = "CreatorUser", FieldType = "nvarchar", FieldLength = 50, AliasName = "کاربر ایجادکننده", Editable = false, IsDisplay = true, IsRequired = false, IsUnique = false, Updated = datMin });

                            tableInfo.FieldInfos.Add(new FieldInfo() { Name = "LastTime", FieldType = "datetime", FieldLength = 50, AliasName = "تاریخ آخرین تغییر", Editable = false, IsDisplay = true, IsRequired = false, IsUnique = false, Updated = datMin });
                            tableInfo.FieldInfos.Add(new FieldInfo() { Name = "LastUser", FieldType = "nvarchar", FieldLength = 50, AliasName = "کاربر آخرین تغییر", Editable = false, IsDisplay = true, IsRequired = false, IsUnique = false, Updated = datMin });
                            tableInfo.FieldInfos.Add(new FieldInfo() { Name = "Org", FieldType = "nvarchar", FieldLength = 254, AliasName = "نام شرکت", Editable = false, IsDisplay = true, IsRequired = false, IsUnique = false, Updated = datMin });
                            if (DBManagement.GetFeatureType(tableInfo.DatasetType) == DBManagement.FeatureType.Polygon)
                            {
                                tableInfo.FieldInfos.Add(new FieldInfo() { Name = "Length", FieldType = "numeric", FieldLength = 50, AliasName = "محیط", Editable = false, IsDisplay = true, IsRequired = false, IsUnique = false, Updated = datMin });
                                tableInfo.FieldInfos.Add(new FieldInfo() { Name = "Area", FieldType = "numeric", FieldLength = 50, AliasName = "مساحت", Editable = false, IsDisplay = true, IsRequired = false, IsUnique = false, Updated = datMin });
                            }
                            else if (DBManagement.GetFeatureType(tableInfo.DatasetType) == DBManagement.FeatureType.Polyline)
                            {
                                tableInfo.FieldInfos.Add(new FieldInfo() { Name = "Length", FieldType = "numeric", FieldLength = 50, AliasName = "محیط", Editable = false, IsDisplay = true, IsRequired = false, IsUnique = false, Updated = datMin });
                            }



                            //== Insert in Table Info
                            _dbContext.TableInfos.Add(tableInfo);
                            _dbContext.SaveChanges();

                            DBManagement dbManagement = new DBManagement(_configuration);
                            string msg = "";
                            bool excute = dbManagement.CreateTable(tableInfo.Name, DBManagement.GetFeatureType(tableInfo.DatasetType), out msg);
                            if (!excute)
                            {
                                _dbContext.TableInfos.Remove(tableInfo);
                                _dbContext.SaveChanges();
                                return Json(new { success = false, responseText = msg });
                            }

                            return Json(new { success = true, responseText = "با موفقیت ذخیره شد" });
                        }
                    }

                    //== Save Table Info 
                }
            }
            catch (Exception exc)
            {
                return Json(new { success = false, responseText = exc.Message });
            }
            return null;
        }

        /// <summary>
        /// Create Or Update Layer Or Table In Database
        /// </summary>
        /// <param name="collection">
        /// Parameter In Table Or Database
        /// </param>
        /// <returns></returns>
        /// 

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult _Layer_Del(string id)
        {
            try
            {
                if (ModelState.IsValid)
                {

                    if (id != null)
                    {
                        var tableInfo = _dbContext.TableInfos.Find(int.Parse(id));
                        int ids = int.Parse(id);
                        try
                        {
                            _ddlService.DropTable(tableInfo.Name);
                        }
                        catch (Exception ex)
                        {
                            return Json(new { success = false, message = $"خطا در حذف ستون از جدول فیزیکی: {ex.Message}" });
                        }

                        _dbContext.TableInfos.Remove(tableInfo);
                        _dbContext.SaveChanges();

                        return Json(new { success = true, responseText = "با موفقیت حذف شد" });
                    }
                    else
                        return Json(new { success = true, responseText = "هیچ لایه ای انتخاب نشده است" });

                }
            }
            catch (Exception exc)
            {
                //== return Fail Message
                return Json(new { success = false, responseText = exc.Message });

            }
            return null;
        }
        #endregion

        #region Group
        public ActionResult _Layer_Group()
        {
            var group = _groupInfoRepository.GetGroupInfos();
            return PartialView(group);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult _Layer_Group(IFormCollection collection)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var SelectName = collection["SelectName"];

                    var groupInfo = new GroupInfo();
                    if (SelectName != "0")
                        groupInfo = _groupInfoRepository.GetGroupInfoByID(int.Parse(SelectName));

                    groupInfo.Name = collection["Name"];
                    groupInfo.AliasName = collection["AliasName"];



                    if (SelectName != "0")
                        _groupInfoRepository.UpdateGroupInfo(groupInfo);
                    else
                        _groupInfoRepository.InsertGroupInfo(groupInfo);
                    _groupInfoRepository.Save();
                    //return "با موفقیت ذخیره شد.";
                    return Json(new { success = true, responseText = "با موفقیت ذخیره شد" });

                }
            }
            catch (Exception exc)
            {
                return Json(new { success = false, responseText = exc.Message });

            }
            return null;
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult _Layer_GroupDel(FormCollection collection)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var SelectName = collection["SelectName"];

                    if (SelectName != "0")
                    {
                        int ids = int.Parse(SelectName);
                        _groupInfoRepository.DeleteGroupInfo(ids);//.UpdateDownSystem(downSystem);
                        _dbContext.SaveChanges();
                        //return "با موفقیت ذخیره شد.";
                        return Json(new { success = true, responseText = "با موفقیت حذف شد" });
                    }
                    else
                        return Json(new { success = true, responseText = "هیچ گروهی انتخاب نشده است" });

                }
            }
            catch (Exception exc)
            {
                return Json(new { success = false, responseText = exc.Message });

            }
            return null;


        }

        #endregion

        #region Field

        public ActionResult _Layer_Fields(string id)
        {
            var fieldInfos = new List<FieldInfo>();
            if (!int.TryParse(id, out int tableId))
            {
                return PartialView("_Layer_Fields", new List<FieldInfo>()); // یا مدل خالی
            }
            if (tableId > 0)
            {
                fieldInfos = _dbContext.FieldInfos.Include("TableInfo").Where(a => a.TableInfo.Id == tableId).ToList();
            }
            return PartialView("_Layer_Fields", fieldInfos);
        }

        public ActionResult _Layer_Field(string id)
        {
            FieldInfo fieldInfo = new FieldInfo() { Name = "" };
            if (id != null)
            {
                fieldInfo = _dbContext.FieldInfos.Find(int.Parse(id));

                //ViewBag.RelTableList = _dbContext.CalFieldConfigs.Where(c => c.CalculatedField.Id == fieldInfo.Id).Select(c => c.RelTable).ToList();
            }
            ViewBag.TableList = _dbContext.TableInfos.ToList();
            return PartialView(fieldInfo);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult _Layer_Field(FormCollection collection)
        {
            try
            {
                //Address,PostBox,Tel,City,State,PostCode
                if (ModelState.IsValid)
                {
                    int ids = 0;
                    int.TryParse(collection["ID"], out ids);
                    int TBLID = 0;
                    int.TryParse(collection["TBLID"], out TBLID);

                    var fieldInfo = new FieldInfo();
                    if (ids > 0)
                    {
                        fieldInfo = _dbContext.FieldInfos.Include("TableInfo").Where(a => a.Id == ids).FirstOrDefault();
                        string name = fieldInfo.Name.ToLower();
                        if (name == "gcode" || name == "objectid" || name == "shape" || name == "org" || name == "creatoruser" || name == "createtime" || name == "lastuser" || name == "lasttime" || name == "length" || name == "area")
                        {
                            fieldInfo.FieldIndex = int.Parse(collection["FieldIndex"]);
                            _dbContext.SaveChanges();
                            return Json(new { success = true, responseText = "با موفقیت ذخیره شد" });
                        }

                        fieldInfo.AliasName = collection["AliasName"];
                        fieldInfo.UnitName = collection["UnitName"];
                        fieldInfo.Editable = collection["Editable"] == "on" ? true : false;

                        fieldInfo.WebService_URL = collection["WebService_URL"] == "" ? string.Empty : collection["WebService_URL"];
                        int period = 60;
                        int.TryParse(collection["WebService_Period"], out period);
                        fieldInfo.WebService_Period = period < 30 ? 30 : period;

                        if (fieldInfo.WebService_URL == null)
                        {
                            fieldInfo.SQLCalc = collection["SQL_Calc"] == "" ? string.Empty : collection["SQL_Calc"];
                            int calc_Period = 0;
                            int.TryParse(collection["Calc_Period"], out calc_Period);
                            fieldInfo.CalcPeriod = calc_Period;
                        }
                        else
                        {
                            fieldInfo.SQLCalc = null;
                            fieldInfo.CalcPeriod = 0;
                        }

                        if (fieldInfo.Updated < DateTime.Now.Subtract(new TimeSpan(1000, 0, 0, 0)))
                            fieldInfo.Updated = DateTime.Now.Subtract(new TimeSpan(1000, 0, 0, 0));

                        //fieldInfo.IsRequired = collection["IsRequired"] == "on" ? true : false;
                        _dbContext.SaveChanges();

                        bool okSQLCalc = true; string msgSQLCalc = "";
                        if (fieldInfo.SQLCalc != null && fieldInfo.SQLCalc.Trim() != "")
                        {
                            okSQLCalc = Excute_CalcSQL(fieldInfo.TableInfo.Name, fieldInfo.Name, fieldInfo.SQLCalc, out msgSQLCalc);
                            string cal_RelTable = collection["cal_RelTable"];
                            string[] arrRelTables = cal_RelTable.Split(',');

                            //_dbContext.CalFieldConfigs.RemoveRange(_dbContext.CalFieldConfigs.Where(c => c.CalculatedField.Id == ids));
                            //var calList = _dbContext.CalFieldConfigs.Where(c => c.CalculatedField.Id == ids);
                            //foreach (var c in calList)
                            //{
                            //    _dbContext.CalFieldConfigs.Remove(c);
                            //}
                            //for (int j = 0; j < arrRelTables.Length; j++)
                            //{
                            //    int idRelTable = 0;
                            //    int.TryParse(arrRelTables[j], out idRelTable);
                            //    CalFieldConfig calFieldConfig = new CalFieldConfig()
                            //    {
                            //        CalculatedField = fieldInfo,
                            //        RelTable = _dbContext.TableInfos.Where(t => t.Id == idRelTable).FirstOrDefault()
                            //    };
                            //    _dbContext.CalFieldConfigs.Add(calFieldConfig);
                            //}
                            _dbContext.SaveChanges();
                        }
                        else if (fieldInfo.WebService_URL != null && fieldInfo.WebService_URL.Trim() != "")
                            okSQLCalc = Excute_WebService(fieldInfo.TableInfo.Name, fieldInfo.Name, fieldInfo.WebService_URL, false, out msgSQLCalc);

                        return Json(new { success = true, responseText = "با موفقیت ذخیره شد" + (!okSQLCalc ? "/r/n" + msgSQLCalc : "") });

                    }
                    else
                    {
                        string msg = "";
                        fieldInfo.Name = collection["Name"];
                        var name = collection["Name"].ToString().ToLower().Trim();
                        if (name == "gcode" || name == "objectid" || name == "shape" || name == "org" || name == "user" || name == "time" || name == "length" || name == "area")
                            return Json(new { success = false, responseText = "این نام جز نام های رزرو شده می باشد. لطفا از نام دیگری استفاده نمایید" });

                        var fldExist = _dbContext.FieldInfos.Include("TableInfo").Where(a => a.Name.ToLower() == name && a.TableInfo.Id == TBLID).FirstOrDefault();
                        if (fldExist != null)
                            return Json(new { success = false, responseText = "این فیلد وجود دارد" });


                        fieldInfo.FieldType = collection["FieldType"];
                        fieldInfo.IsRequired = collection["IsRequired"] == "on" ? true : false;
                        fieldInfo.TableInfo = _dbContext.TableInfos.Find(TBLID);
                        int length = 0;
                        bool ok = int.TryParse(collection["FieldLength"], out length);
                        if (ok)
                            fieldInfo.FieldLength = length;

                        fieldInfo.AliasName = collection["AliasName"];
                        fieldInfo.UnitName = collection["UnitName"];
                        fieldInfo.Editable = collection["Editable"] == "on" ? true : false;
                        fieldInfo.IsUnique = collection["IsUnique"] == "on" ? true : false;


                        fieldInfo.WebService_URL = collection["WebService_URL"] == "" ? string.Empty : collection["WebService_URL"];
                        int period = 30;
                        int.TryParse(collection["WebService_Period"], out period);
                        fieldInfo.WebService_Period = period < 30 ? 30 : period;

                        if (fieldInfo.WebService_URL == null)
                        {
                            fieldInfo.SQLCalc = collection["SQL_Calc"] == "" ? string.Empty : collection["SQL_Calc"];
                            int calc_Period = 0;
                            int.TryParse(collection["Calc_Period"], out calc_Period);
                            fieldInfo.CalcPeriod = calc_Period;
                        }
                        else
                        {
                            fieldInfo.SQLCalc = null;
                            fieldInfo.CalcPeriod = 0;
                        }
                        if (fieldInfo.Updated < DateTime.Now.Subtract(new TimeSpan(1000, 0, 0, 0)))
                            fieldInfo.Updated = DateTime.Now.Subtract(new TimeSpan(1000, 0, 0, 0));

                        //_fieldInfoRepository.InsertFieldInfo(fieldInfo);
                        _dbContext.FieldInfos.Add(fieldInfo);
                        int returnValue = _dbContext.SaveChanges();

                        DBManagement dbManagment = new DBManagement(_configuration);
                        if (returnValue > 0)
                        {
                            bool excute = dbManagment.CreateField(fieldInfo, out msg);
                            if (!excute)
                            {
                                _dbContext.FieldInfos.Remove(fieldInfo);
                                _dbContext.SaveChanges();
                                return Json(new { success = false, responseText = msg });
                            }

                            bool okSQLCalc = true; string msgSQLCalc = "";
                            if (fieldInfo.SQLCalc != null && fieldInfo.SQLCalc.Trim() != "")
                            {
                                okSQLCalc = Excute_CalcSQL(fieldInfo.TableInfo.Name, fieldInfo.Name, fieldInfo.SQLCalc, out msgSQLCalc);
                                string cal_RelTable = collection["cal_RelTable"];
                                string[] arrRelTables = cal_RelTable.Split(',');
                                for (int j = 0; j < arrRelTables.Length; j++)
                                {
                                    //int idRelTable = 0;
                                    //int.TryParse(arrRelTables[j], out idRelTable);
                                    //CalFieldConfig calFieldConfig = new CalFieldConfig()
                                    //{
                                    //    CalculatedField = fieldInfo,
                                    //    RelTable = _dbContext.TableInfos.Where(t => t.Id == idRelTable).FirstOrDefault()
                                    //};
                                    //_dbContext.CalFieldConfigs.Add(calFieldConfig);
                                }
                                _dbContext.SaveChanges();
                            }
                            else if (fieldInfo.WebService_URL != null && fieldInfo.WebService_URL.Trim() != "")
                                okSQLCalc = Excute_WebService(fieldInfo.TableInfo.Name, fieldInfo.Name, fieldInfo.WebService_URL, false, out msgSQLCalc);


                            string m = "با موفقیت ذخیره شد";
                            return Json(new { success = true, responseText = m + (!okSQLCalc ? "/r/n" + msgSQLCalc : "") });
                        }
                        else

                            return Json(new { success = false, responseText = "خطایی در ذخیره رخ داده است" });





                    }



                }
            }
            catch (Exception exc)
            {

                return Json(new { success = false, responseText = exc.Message });

            }
            return null;
        }

        public string SampleWebService(string table, string field)
        {
            var tblInfo = _dbContext.TableInfos.Where(a => a.Name.ToLower() == table.ToLower()).FirstOrDefault();
            if (tblInfo != null)
            {

                DBManagement db = new DBManagement(_configuration);
                var dt = db.SelectTableSQL("select GCode," + field + " from  " + table);

                var ary = new List<string>();
                for (int i = 0; i < dt.Rows.Count; i++)
                    ary.Add("\"" + dt.Rows[i]["GCode"].ToString() + "\":\"" + dt.Rows[i][field].ToString() + "\"");

                var json = "{" + string.Join(",", ary.ToArray()) + "}";


                return json;// Json(new { responseText = json } );
            }
            return null;
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult _Layer_FieldDel(string id)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    // int id = int.Parse(collection["ID"]);

                    if (id != null)
                    {
                        int ids = int.Parse(id);
                        var fieldInfo = _dbContext.FieldInfos.Include("TableInfo").Where(a => a.Id == ids).FirstOrDefault();//.Find(ids);// _fieldInfoRepository.GetFieldInfoByID(ids);
                        DBManagement dbManagement = new DBManagement(_configuration);
                        string msg = "";
                        bool excute = dbManagement.DropField(fieldInfo, out msg);
                        if (!excute)
                            return Json(new { success = false, responseText = msg });
                        bool isIntegeration = false;
                        int tblid = fieldInfo.TableInfo.Id;
                        _dbContext.FieldInfos.Remove(fieldInfo);
                        _dbContext.SaveChanges();
                        string m = "با موفقیت حذف شد";
                        if (isIntegeration)
                        {
                            string message = "";

                        }



                        //return "با موفقیت ذخیره شد.";
                        return Json(new { success = true, responseText = m });
                    }
                    else
                        return Json(new { success = true, responseText = "هیچ فیلدی انتخاب نشده است" });

                }
            }
            catch (Exception exc)
            {

                return Json(new { success = false, responseText = exc.Message });

            }
            return null;


        }
        #endregion

        #region Domain

        public ActionResult _Layer_Field_Domain(string id)
        {
            FieldInfo fieldInfo = new FieldInfo();
            if (id != null)
            {
                int ids = int.Parse(id);
                fieldInfo = _dbContext.FieldInfos.Include("DomainInfos").Where(a => a.Id == ids).FirstOrDefault();

            }
            return PartialView(fieldInfo);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult _Layer_Field_Domain(FormCollection collection)
        {
            try
            {
                //Address,PostBox,Tel,City,State,PostCode
                if (ModelState.IsValid)
                {
                    int ids = 0;
                    int.TryParse(collection["ID"], out ids);


                    if (ids > 0)
                    {
                        DomainInfo domain = new DomainInfo();
                        domain.FieldInfo = _dbContext.FieldInfos.Include("TableInfo").Where(a => a.Id == ids).FirstOrDefault();
                        domain.Name = collection["Name"];
                        domain.Code = int.Parse(collection["Code"]);


                        //_fieldInfoRepository.InsertFieldInfo(fieldInfo);
                        _dbContext.DomainInfos.Add(domain);
                        _dbContext.SaveChanges();

                        return Json(new { success = true, id = domain.Id, responseText = "با موفقیت ذخیره شد" });

                    }
                }
            }
            catch (Exception exc)
            {
                return Json(new { success = false, responseText = exc.Message });

            }
            return null;
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult _Layer_Field_DomainDel(string id)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    // int id = int.Parse(collection["ID"]);

                    if (id != null)
                    {
                        int ids = int.Parse(id);
                        var domainInfos = _dbContext.DomainInfos.Include("FieldInfo").Include("FieldInfo.TableInfo").Where(a => a.Id == ids).FirstOrDefault();//.Find(ids);//.Find(ids);// _fieldInfoRepository.GetFieldInfoByID(ids);


                        _dbContext.DomainInfos.Remove(domainInfos);
                        _dbContext.SaveChanges();

                        //if (domainInfos.FieldInfo.TableInfo.IsIntegration)
                        //{
                        //    string m = "";
                        //}


                        //return "با موفقیت ذخیره شد.";
                        return Json(new { success = true, responseText = "با موفقیت حذف شد" });
                    }
                    else
                        return Json(new { success = true, responseText = "هیچ فیلدی انتخاب نشده است" });

                }
            }
            catch (Exception exc)
            {
                return Json(new { success = false, responseText = exc.Message });

            }
            return null;


        }

        #endregion

        #region relation
        public ActionResult _Layer_Relations(string id)
        {
            var relationInfos = new List<RelationInfo>();
            if (!int.TryParse(id, out int tableId))
            {
                return PartialView("_Layer_Relations", relationInfos);
            }
            if (tableId > 0)
            {
                var tableInfo = _dbContext.TableInfos.Find(tableId);
                // relationInfos = _dbContext.RelationInfos.Include("RelatedTable").Include("MainTable").Where(a => a.MainTable.Id == tableInfo.Id || a.RelatedTable.Id == tableInfo.Id).ToList();
            }

            return PartialView("_Layer_Relations", relationInfos);
        }

        public ActionResult _Layer_Relation(string id)
        {
            RelationInfo relationInfo = new RelationInfo();
            if (id != null)
            {
                //relationInfo = _dbContext.RelationInfos.Find(int.Parse(id));
            }

            ViewBag.ListLayers = _dbContext.TableInfos.ToList();
            return PartialView(relationInfo);
        }

        public ActionResult _Get_Fields(string tablename)
        {
            var fieldInfos = _dbContext.FieldInfos.Where(a => a.Name == tablename);
            return Json(fieldInfos);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult _Layer_Relation(FormCollection collection)
        {
            try
            {
                //Address,PostBox,Tel,City,State,PostCode
                if (ModelState.IsValid)
                {
                    int ids = 0;
                    int.TryParse(collection["ID"], out ids);
                    int TBLID = 0;
                    int.TryParse(collection["TBLID"], out TBLID);



                    if (ids > 0)
                    {
                        //var relationInfo = _dbContext.RelationInfos.Find(ids);
                        //relationInfo.AliasName = collection["AliasName"];
                        //relationInfo.MainTable = _context.TableInfos.Where(a => a.Name == collection["MainTable"]).First();
                        //relationInfo.RelatedTable = _context.TableInfos.Where(a => a.Name == collection["RelatedTable"]).First();
                        //relationInfo.RelationType = collection["RelationType"];
                        //relationInfo.Name = relationInfo.MainTable.Name + "_" + relationInfo.RelatedTable;

                        _dbContext.SaveChanges();


                        return Json(new { success = true, responseText = "با موفقیت ذخیره شد" });
                    }
                    else
                    {
                        var relationInfo = new RelationInfo();

                        relationInfo.AliasName = collection["AliasName"];
                        string main = collection["MainTable"];
                        relationInfo.MainTable = _dbContext.TableInfos.Where(a => a.Name == main).First();
                        string relat = collection["RelatedTable"];
                        relationInfo.RelatedTable = _dbContext.TableInfos.Where(a => a.Name == relat).First();
                        relationInfo.RelationType = collection["RelationType"];
                        relationInfo.Name = relationInfo.MainTable.Name + "_" + relationInfo.RelatedTable.Name;


                        //_dbContext.RelationInfos.Add(relationInfo);
                        _dbContext.SaveChanges();
                        DBManagement dbManagement = new DBManagement(_configuration);
                        string msg = "";
                        bool excute = dbManagement.CreateForignTable(relationInfo, out msg);
                        if (!excute)
                        {
                            //_dbContext.RelationInfos.Remove(relationInfo);
                            _dbContext.SaveChanges();
                            return Json(new { success = false, responseText = msg });
                        }





                        return Json(new { success = true, responseText = "با موفقیت ذخیره شد" });
                    }


                }
            }
            catch (Exception exc)
            {
                return Json(new { success = false, responseText = exc.Message });

            }
            return null;
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult _Layer_RelationDel(string id)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    // int id = int.Parse(collection["ID"]);

                    if (id != null)
                    {
                        int ids = int.Parse(id);

                        //RelationInfo relationInfo = _dbContext.RelationInfos.Include("MainTable").Include("RelatedTable").Where(a => a.Id == ids).First();
                        //DBManagement dbManagement = new DBManagement(_configuration);

                        //string msg = "";
                        //bool excute = dbManagement.DropForignTable(relationInfo, out msg);
                        //if (!excute)
                        //    return Json(new { success = false, responseText = msg });


                        //_dbContext.RelationInfos.Remove(relationInfo);
                        //_dbContext.SaveChanges();
                        //return "با موفقیت ذخیره شد.";
                        return Json(new { success = true, responseText = "با موفقیت حذف شد" });
                    }
                    else
                        return Json(new { success = true, responseText = "هیچ فیلدی انتخاب نشده است" });

                }
            }
            catch (Exception exc)
            {
                return Json(new { success = false, responseText = exc.Message });

            }
            return null;


        }
        #endregion

        #endregion

        #region Symbology
        public ActionResult SymbologyManager()
        {
            var tableInfoes = _dbContext.TableInfos.Include("GroupInfo").ToList();
            return View(tableInfoes);
        }

        public ActionResult _Layer_Symbols(string id)
        {
            TableInfo tableinfo = null;
            if (!int.TryParse(id, out int tableId))
            {
                return PartialView("_Layer_Relations", tableinfo);
            }

            tableinfo = _dbContext.TableInfos.Include(t => t.Symbologies).Where(a => a.Id == tableId).FirstOrDefault();

            return PartialView(tableinfo);
        }

        #endregion

        public bool Excute_CalcSQL(string table, string field, string SQL, out string msg, string where = null)
        {
            bool ok = false;
            msg = "";
            try
            {
                string query = string.Format("Update {0} Set  {1} = CalculatedTable.CalculatedValue FROM ({2}) as CalculatedTable Where CalculatedTable.GCode = {0}.GCode {3}", table, field, SQL, (string.IsNullOrEmpty(where) ? "" : " AND " + where));
                DBManagement db = new DBManagement(_configuration);
                ok = db.ExcuteSQL(query, out msg);
            }
            catch (Exception exc)
            {
                msg = exc.Message;
                ok = false;
            }
            return ok;
        }
        public bool Excute_WebService(string table, string field, string URL, bool isIntegration, out string msg)
        {
            bool ok = true;
            msg = "";
            try
            {
                using (System.Net.WebClient wc = new System.Net.WebClient())
                {
                    var str = wc.DownloadString(URL);
                    Dictionary<string, string> json = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, string>>(str);

                    if (json.Count > 0)
                    {
                        var SQL = "";
                        var keys = json.Keys.ToArray();
                        for (int i = 0; i < json.Count; i++)
                            SQL += " Update " + table + " SET " + field + " = '" + json[keys[i]] + "' WHERE GCode = '" + keys[i] + "';";
                        DBManagement db = new DBManagement(_configuration);
                        ok = db.ExcuteSQL(SQL, out msg);
                    }
                }
            }
            catch (Exception exc)
            {
                msg = exc.Message;
                ok = false;
            }
            return ok;
        }


        #region Insert/Update/delete
        public IActionResult Insert()
        {
            ViewBag.PageTitle = "افزودن یکباره اطلاعات";
            return View();
        }

        public IActionResult _Insert_List()
        {
            var appUser = _dbContext.Users.FirstOrDefault(a => a.UserName == User.Identity.Name);
            var tableInfos = _dbContext.TableInfos.Include(t => t.GroupInfo).ToList();
            ViewBag.Role = null; // MustChange
            return PartialView("_Insert_List", tableInfos);
        }
        public IActionResult _Insert_Wizard(string id, string path)
        {
            var model = new InsertWizardViewModel
            {
                Id = id,
                PathFile = path,
                ShapeFileColumns = new List<string>()
            };

            // دریافت اطلاعات فیلدها و تعداد رکوردها
            if (!string.IsNullOrEmpty(id) && int.TryParse(id, out int tableId))
            {
                var fieldInfos = _dbContext.FieldInfos
                    .Include(f => f.TableInfo)
                    .Where(f => f.TableInfo.Id == tableId)
                    .ToList();

                int countRecords = 0;
                try
                {
                    if (fieldInfos.Any())
                    {
                        var db = new DBManagement(_configuration);
                        var dt = db.SelectTableSQL($"SELECT COUNT(objectid) FROM {fieldInfos[0].TableInfo.Name}");
                        if (dt?.Rows.Count > 0)
                        {
                            countRecords = int.Parse(dt.Rows[0][0].ToString());
                        }
                    }
                }
                catch (Exception ex)
                {
                    // لاگ خطا
                    _logger?.LogError(ex, "Error fetching record count for table ID {TableId}", tableId);
                }

                model.FieldInfos = fieldInfos;
                model.CountRecords = countRecords;
            }

            // خواندن فایل Shapefile با NetTopologySuite
            if (!string.IsNullOrEmpty(path))
            {
                var uploadPath = Path.Combine(_environment.WebRootPath, "Upload", path);
                var dir = new DirectoryInfo(uploadPath);
                if (dir.Exists)
                {
                    var shpFile = dir.GetFiles("*.shp").FirstOrDefault();
                    if (shpFile != null)
                    {
                        try
                        {
                            using var reader = new ShapefileDataReader(shpFile.FullName, new GeometryFactory());
                            if (reader != null && reader.Read())
                            {
                                var geometry = reader.Geometry;
                                var dbaseHeader = reader.DbaseHeader;

                                // استخراج نام ستون‌ها از فیلدهای .dbf
                                model.ShapeFileColumns = dbaseHeader.Fields
                                    .Where(f => !string.IsNullOrEmpty(f.Name))
                                    .Select(f => f.Name)
                                    .ToList();
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "Error reading Shapefile at {Path}", shpFile.FullName);
                            model.ShapeFileColumns = new List<string>();
                        }
                    }
                }
            }

            return PartialView("_Insert_Wizard", model);
        }

        [HttpPost]
        //[ValidateAntiForgeryToken]
        public async Task<IActionResult> _FileUpload(string id, IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                return Json(new { success = false, responseText = "هیچ فایلی انتخاب نشده است." });
            }

            if (Path.GetExtension(file.FileName).ToLower() != ".zip")
            {
                return Json(new { success = false, responseText = "نوع فایل معتبر نیست. فقط فایل ZIP مجاز است." });
            }

            var random = new Random();
            var folderName = random.Next(10000000, 99999999).ToString();
            var uploadPath = Path.Combine(_environment.WebRootPath, "Upload", folderName);
            Directory.CreateDirectory(uploadPath);

            var filePath = Path.Combine(uploadPath, file.FileName);
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            var extractedFiles = UnZipFile(folderName, filePath);
            var dir = new DirectoryInfo(uploadPath);
            var shpFile = dir.GetFiles("*.shp").FirstOrDefault();

            if (shpFile == null)
            {
                return Json(new { success = false, responseText = "فایل ZIP حاوی Shapefile نیست." });
            }

            if (!string.IsNullOrEmpty(id) && int.TryParse(id, out int tableId))
            {
                try
                {
                    using (var sdrReader = new ShapefileDataReader(shpFile.FullName, new GeometryFactory()))
                    {
                        var geometryType = sdrReader.ShapeHeader.ShapeType;
                        string featureType = geometryType switch
                        {
                            ShapeGeometryType.LineString => "polyline",
                            ShapeGeometryType.LineStringM => "polyline",
                            ShapeGeometryType.Polygon => "polygon",
                            ShapeGeometryType.PolygonM => "polygon",
                            ShapeGeometryType.Point => "point",
                            ShapeGeometryType.MultiPoint => "point",
                            _ => geometryType.ToString().ToLower()
                        };

                        var table = _dbContext.TableInfos.FirstOrDefault(a => a.Id == tableId);
                        if (table == null || featureType != table.DatasetType.ToLower())
                        {
                            return Json(new { success = false, responseText = $"Shapefile باید از نوع {table?.DatasetType} باشد." });
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "Error validating Shapefile type at {Path}", shpFile.FullName);
                    return Json(new { success = false, responseText = "خطا در اعتبارسنجی نوع Shapefile." });
                }
            }

            return Json(new { success = true, responseText = folderName });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult PublishData(string id, string path, string FieldSource, string FieldDest)
        {
            try
            {
                if (string.IsNullOrEmpty(path) || string.IsNullOrEmpty(id))
                {
                    return Json(new { success = false, responseText = "پارامترهای ورودی نامعتبر هستند." });
                }

                var uploadPath = Path.Combine(_environment.WebRootPath, "Upload", path);
                var dir = new DirectoryInfo(uploadPath);
                if (!dir.Exists)
                {
                    return Json(new { success = false, responseText = "مسیر فایل یافت نشد." });
                }

                var shpFile = dir.GetFiles("*.shp").FirstOrDefault();
                if (shpFile == null)
                {
                    return Json(new { success = false, responseText = "فایل Shapefile یافت نشد." });
                }

                InsertShp(id, shpFile, FieldSource, FieldDest);
                return Json(new { success = true, responseText = "با موفقیت ذخیره شد" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, responseText = ex.Message });
            }
        }

        private void InsertShp(string id, FileInfo file, string FieldSource, string FieldDest)
        {
            var user = _dbContext.Users.FirstOrDefault(a => a.UserName == User.Identity.Name);
            var time = Convertion.ConvertDateTimeToPersianLong(DateTime.Now);

            // خواندن Shapefile با NetTopologySuite
            using (var sdrReader = new ShapeDataReader(file.FullName))
            {
                var features = sdrReader.ReadByMBRFilter(sdrReader.ShapefileBounds).ToList();
                var dt = new DataTable();

                // افزودن ستون‌های مورد نیاز
                dt.Columns.Add("Shape", typeof(SqlGeometry));
                if (dt.Columns.Contains("OBJECTID")) dt.Columns.Remove("OBJECTID");
                dt.Columns.Add("OBJECTID", typeof(int));
                if (dt.Columns.Contains("GCode")) dt.Columns.Remove("GCode");
                dt.Columns.Add("GCode", typeof(string));
                if (dt.Columns.Contains("CreateTime")) dt.Columns.Remove("CreateTime");
                dt.Columns.Add("CreateTime", typeof(string));
                if (dt.Columns.Contains("CreatorUser")) dt.Columns.Remove("CreatorUser");
                dt.Columns.Add("CreatorUser", typeof(string));
                if (dt.Columns.Contains("LastTime")) dt.Columns.Remove("LastTime");
                dt.Columns.Add("LastTime", typeof(string));
                if (dt.Columns.Contains("LastUser")) dt.Columns.Remove("LastUser");
                dt.Columns.Add("LastUser", typeof(string));
                if (dt.Columns.Contains("Org")) dt.Columns.Remove("Org");
                dt.Columns.Add("Org", typeof(string));

                var isLine = features.Any(f => f.Geometry.OgcGeometryType == OgcGeometryType.LineString || f.Geometry.OgcGeometryType == OgcGeometryType.MultiLineString);
                var isArea = features.Any(f => f.Geometry.OgcGeometryType == OgcGeometryType.Polygon || f.Geometry.OgcGeometryType == OgcGeometryType.MultiPolygon);

                if (isLine)
                {
                    if (dt.Columns.Contains("Length")) dt.Columns.Remove("Length");
                    dt.Columns.Add("Length", typeof(float));
                }

                if (isArea)
                {
                    if (dt.Columns.Contains("Length")) dt.Columns.Remove("Length");
                    dt.Columns.Add("Length", typeof(float));
                    if (dt.Columns.Contains("Area")) dt.Columns.Remove("Area");
                    dt.Columns.Add("Area", typeof(float));
                }

                var coCode = _configuration["CoCode"] ?? "GW";
                var tableInfo = _dbContext.TableInfos
                    .Include(t => t.FieldInfos)
                    .ThenInclude(f => f.DomainInfos)
                    .FirstOrDefault(a => a.Id.ToString() == id);

                if (tableInfo == null)
                {
                    throw new Exception("جدول یافت نشد.");
                }

                var lyr = tableInfo.ShortName;
                var fieldDest = FieldDest?.Split(',', StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>();
                var fieldSource = FieldSource?.Split(',', StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>();

                if (fieldSource.Length != fieldDest.Length)
                {
                    throw new Exception("تعداد فیلدهای منبع و مقصد مطابقت ندارد.");
                }

                var domain = new Dictionary<int, List<DomainInfo>>();
                for (int i = 0; i < fieldDest.Length; i++)
                {
                    var field = tableInfo.FieldInfos.FirstOrDefault(f => string.Equals(f.Name, fieldDest[i], StringComparison.OrdinalIgnoreCase));
                    if (field != null && field.FieldType.ToLower().Contains("domain"))
                    {
                        for (int j = 0; j < dt.Columns.Count; j++)
                        {
                            if (string.Equals(dt.Columns[j].ColumnName, fieldSource[i], StringComparison.OrdinalIgnoreCase))
                            {
                                if (dt.Columns[j].DataType != typeof(string))
                                {
                                    var columnName = dt.Columns[j].ColumnName;
                                    var newColumn = new DataColumn(columnName + "_new", typeof(string));
                                    var ordinal = dt.Columns[columnName].Ordinal;
                                    dt.Columns.Add(newColumn);
                                    newColumn.SetOrdinal(ordinal);
                                    foreach (DataRow row in dt.Rows)
                                    {
                                        row[newColumn.ColumnName] = Convert.ChangeType(row[columnName], typeof(string));
                                    }
                                    dt.Columns.Remove(columnName);
                                    newColumn.ColumnName = columnName;
                                }
                                domain.Add(j, field.DomainInfos);
                                break;
                            }
                        }
                    }
                }

                if (!int.TryParse(id, out int tableId))
                {
                    throw new Exception("شناسه جدول نامعتبر است.");
                }

                var table = _dbContext.TableInfos.FirstOrDefault(a => a.Id == tableId);
                if (table == null)
                {
                    throw new Exception("جدول یافت نشد.");
                }

                var db = new DBManagement(_configuration);
                var org = "Test";
                var maxObjectId = db.SelectMax(table.Name, "objectid");
                var lastId = db.GetLastIdForCode(tableInfo.Name);

                // خواندن فایل .prj برای سیستم مختصات
                var prjFile = file.FullName.Replace(".shp", ".prj");
                CoordinateSystem sourceCoordSystem = null;
                if (System.IO.File.Exists(prjFile))
                {
                    try
                    {
                        var prjContent = System.IO.File.ReadAllText(prjFile);
                        var csFactory = new CoordinateSystemFactory();
                        sourceCoordSystem = csFactory.CreateFromWkt(prjContent);
                    }
                    catch
                    {
                        // لاگ خطا
                    }
                }

                var targetCoordSystem = ProjectedCoordinateSystem.WebMercator;
                targetCoordSystem.Name = "WGS_1984_Web_Mercator_Auxiliary_Sphere";
                ICoordinateTransformation transformation = null;
                if (!IsSameCoordinateSystem(sourceCoordSystem, targetCoordSystem))
                {
                    try
                    {
                        var ctFactory = new CoordinateTransformationFactory();
                        transformation = ctFactory.CreateFromCoordinateSystems(sourceCoordSystem, targetCoordSystem);
                        _logger.LogInformation("ImportShapefileApply: Coordinate transformation created from {SourceWKT} to Web Mercator.", sourceCoordSystem.WKT);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "ImportShapefileApply: Failed to create coordinate transformation from {SourceWKT} to Web Mercator.", sourceCoordSystem.WKT);
                        TempData["ErrorMessage"] = "خطا در تبدیل سیستم مختصات شپ‌فایل.";
                        throw new Exception("جدول یافت نشد.");
                    }
                }
                else
                {
                    _logger.LogInformation("ImportShapefileApply: Source coordinate system is already Web Mercator. No transformation needed.");
                }
                // تنظیم کارخانه هندسه
                var geometryFactory = NtsGeometryServices.Instance.CreateGeometryFactory();

                for (int j = 0; j < features.Count; j++)
                {
                    var feature = features[j];
                    if (feature?.Geometry == null)
                    {
                        continue;
                    }

                    maxObjectId++;
                    var row = dt.NewRow();

                    var geometry = feature.Geometry;
                    if (transformation != null)
                    {
                        geometry = TransformGeometry(geometry, transformation);
                    }

                    // تبدیل به UTM
                    var utmGeometry = TransformToUTM(geometry, targetCoordSystem);
                    var wkt = geometry.AsText();
                    var sqlGeometry = SqlGeometry.Parse(wkt);
                    sqlGeometry.STSrid = 3857;
                    row["Shape"] = sqlGeometry;

                    row["OBJECTID"] = maxObjectId;
                    row["GCode"] = $"{coCode}{lyr}{lastId:0000000}";
                    lastId++;
                    row["CreateTime"] = time;
                    row["CreatorUser"] = user?.FullName ?? "test";
                    row["LastTime"] = time;
                    row["LastUser"] = user?.FullName ?? "test";
                    row["Org"] = org;

                    // افزودن مقادیر ویژگی‌ها
                    foreach (var attribute in feature.Attributes.GetNames())
                    {
                        if (dt.Columns.Contains(attribute))
                        {
                            row[attribute] = feature.Attributes[attribute] ?? DBNull.Value;
                        }
                    }

                    if (isLine)
                    {
                        row["Length"] = (float)utmGeometry.Length;
                    }
                    if (isArea)
                    {
                        row["Length"] = (float)utmGeometry.Length;
                        row["Area"] = (float)utmGeometry.Area;
                    }

                    if (domain.Any())
                    {
                        foreach (var domainEntry in domain)
                        {
                            var columnIndex = domainEntry.Key;
                            var domainInfos = domainEntry.Value;
                            row[columnIndex] = BaseGIS.Web.Helper.Utilities.ReplaceWithDomain(row[columnIndex]?.ToString(), domainInfos);
                        }
                    }

                    dt.Rows.Add(row);
                }

                using (var bulkCopy = new SqlBulkCopy(db.ConnectionString, SqlBulkCopyOptions.KeepIdentity))
                {
                    bulkCopy.ColumnMappings.Add("Shape", "Shape");
                    bulkCopy.ColumnMappings.Add("OBJECTID", "OBJECTID");
                    bulkCopy.ColumnMappings.Add("GCode", "GCode");
                    bulkCopy.ColumnMappings.Add("CreateTime", "CreateTime");
                    bulkCopy.ColumnMappings.Add("CreatorUser", "CreatorUser");
                    bulkCopy.ColumnMappings.Add("LastTime", "LastTime");
                    bulkCopy.ColumnMappings.Add("LastUser", "LastUser");
                    bulkCopy.ColumnMappings.Add("Org", "Org");

                    if (isLine)
                    {
                        bulkCopy.ColumnMappings.Add("Length", "Length");
                    }
                    if (isArea)
                    {
                        bulkCopy.ColumnMappings.Add("Length", "Length");
                        bulkCopy.ColumnMappings.Add("Area", "Area");
                    }

                    for (int i = 0; i < fieldSource.Length; i++)
                    {
                        if (!string.IsNullOrWhiteSpace(fieldSource[i]) && !string.IsNullOrWhiteSpace(fieldDest[i]))
                        {
                            bulkCopy.ColumnMappings.Add(fieldSource[i], fieldDest[i]);
                        }
                    }

                    bulkCopy.BulkCopyTimeout = 6000;
                    bulkCopy.DestinationTableName = table.Name;
                    bulkCopy.WriteToServer(dt);
                }
            }
        }


        public ActionResult Update()
        {
            ViewBag.PageTitle = "افزودن یکباره اطلاعات ";
            return View();
        }

        public ActionResult _Update_List()
        {
            var tableInfos = _dbContext.TableInfos.Include("GroupInfo").ToList();
            return PartialView(tableInfos);
        }

        public ActionResult _Update_Wizard()
        {

            string id = Request.Query["id"];
            if (id != null)
            {
                int ids = int.Parse(id);
                //==>
                ViewBag.FieldInfos = _dbContext.FieldInfos.Where(a => a.TableInfo.Id == ids).ToList();// .GetTableInfoByID(int.Parse(id));
            }

            string path = Request.Query["path"];
            if (path != null)
            {
                DirectoryInfo dir = new DirectoryInfo(Server.MapPath("~/Upload") + @"\" + path);
                var list = dir.GetFiles();

                for (int i = 0; i < list.Length; i++)
                    if (list[i].Extension.ToLower() == ".shp")
                    {
                        DotSpatial.Data.IFeatureSet fs = DotSpatial.Data.FeatureSet.Open(((System.IO.FileInfo)list[i]).FullName);
                        //==>
                        ViewBag.ShapeFileColumn = fs.DataTable.Columns;
                        break;
                    }
                    else if (list[i].Extension.ToLower() == ".xls")
                    {
                        NPOI.HSSF.UserModel.HSSFWorkbook hssfwb;
                        using (FileStream file = new FileStream(((System.IO.FileInfo)list[i]).FullName, FileMode.Open, FileAccess.Read))
                        {
                            hssfwb = new NPOI.HSSF.UserModel.HSSFWorkbook(file);
                        }

                        NPOI.SS.UserModel.ISheet sheet = hssfwb.GetSheetAt(0);
                        ViewBag.SheetXLS = sheet;
                        break;
                    }


            }
            return PartialView();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult UpdateData(string id, string path, string FieldSource, string FieldDest, string FieldCheck)
        {
            try
            {
                if (path != null)
                {
                    DirectoryInfo dir = new DirectoryInfo(Server.MapPath("~/Upload") + @"\" + path);
                    var list = dir.GetFiles();

                    for (int i = 0; i < list.Length; i++)
                        if (list[i].Extension.ToLower() == ".shp")
                        {

                            updateShp(id, (System.IO.FileInfo)list[i], FieldSource, FieldDest, FieldCheck);
                            return Json(new { success = true, responseText = "با موفقیت ذخیره شد" });
                        }
                        else if (list[i].Extension.ToLower() == ".xls")
                        {
                            updateXls(id, (System.IO.FileInfo)list[i], FieldSource, FieldDest, FieldCheck);
                            return Json(new { success = true, responseText = "با موفقیت ذخیره شد" });
                        }


                }
            }
            catch (Exception exc)
            {
                return Json(new { success = false, responseText = exc.Message });

            }



            return null;
        }
        private void updateShp(string id, FileInfo file, string FieldSource, string FieldDest, string FieldCheck)
        {
            var user = _dbContext.Users.Where(a => a.UserName == User.Identity.Name).FirstOrDefault();
            string org = "Test"; // Utilities.GetUserCompaniyAlias(user);

            string time = FN.GeoServer.DomainClasses.Convertion.ConvertDateTimeToPersianLong(DateTime.Now);
            DotSpatial.Data.IFeatureSet fs = DotSpatial.Data.FeatureSet.Open((file).FullName);
            DataTable dt = fs.DataTable.Copy();
            dt.Columns.Add("Shape", typeof(SqlGeometry));

            if (dt.Columns.Contains("LastTime"))
                dt.Columns.Remove("LastTime");
            dt.Columns.Add("LastTime", typeof(string));

            if (dt.Columns.Contains("LastUser"))
                dt.Columns.Remove("LastUser");
            dt.Columns.Add("LastUser", typeof(string));


            bool line = false;
            if (fs.FeatureType == DotSpatial.Topology.FeatureType.Line)
            {
                line = true;
                if (dt.Columns.Contains("Length"))
                    dt.Columns.Remove("Length");
                dt.Columns.Add("Length", typeof(float));
            }

            bool area = false;
            if (fs.FeatureType == DotSpatial.Topology.FeatureType.Polygon)
            {
                area = true;
                if (dt.Columns.Contains("Length"))
                    dt.Columns.Remove("Length");
                dt.Columns.Add("Length", typeof(float));
                if (dt.Columns.Contains("Area"))
                    dt.Columns.Remove("Area");
                dt.Columns.Add("Area", typeof(float));
            }

            string coCode = "Test";// Utilities.GetAppSetting("CoCode");
            coCode = coCode != null ? coCode : "GW";
            string lyr = _dbContext.TableInfos.Find(int.Parse(id)).ShortName;
            for (int j = 0; j < dt.Rows.Count; j++)
            {
                var strFeature = fs.Features[j].BasicGeometry.ToString();

                dt.Rows[j]["Shape"] = SqlGeometry.Parse(strFeature).MakeValid();
                // dt.Rows[j]["OBJECTID"] = j + 1;
                // dt.Rows[j]["GCode"] = co + lyr + (j + 1).ToString("0000000");
                dt.Rows[j]["LastTime"] = time;
                dt.Rows[j]["LastUser"] = user.FullName;

                DotSpatial.Projections.ProjectionInfo outProjectionInfo = DotSpatialUtils.GetUtmProjectionInfo(fs.Extent.Center, fs.Projection);
                DotSpatial.Topology.IGeometry utmGeometry = (DotSpatial.Topology.IGeometry)DotSpatialUtils.ReprojectGeometry((DotSpatial.Topology.IGeometry)fs.Features[j].BasicGeometry, fs.Projection, outProjectionInfo);
                if (line)
                    dt.Rows[j]["Length"] = utmGeometry.Length;
                if (area)
                {
                    dt.Rows[j]["Length"] = utmGeometry.Length;
                    dt.Rows[j]["Area"] = utmGeometry.Area;
                }

            }
            int cc = 0;
            int ids = int.Parse(id);
            var tbl = _dbContext.TableInfos.Where(a => a.Id == ids).FirstOrDefault();
            UpdateFromTable(DBManagment.GetConnectionString(QueryOnDBMode.Main), tbl.Name, FieldSource, FieldDest, FieldCheck, dt, org);

        }

        private void updateXls(string id, FileInfo file, string FieldSource, string FieldDest, string FieldCheck)
        {
            var user = _dbContext.Users.Where(a => a.UserName == User.Identity.Name).FirstOrDefault();
            string org = "Test"; // Utilities.GetUserCompaniyAlias(user);

            DataTable dt = Utilities.GetDataTableFromExcel((file).FullName, 0);

            if (dt.Columns.Contains("ObjectID"))
                dt.Columns.Remove("Time");

            if (dt.Columns.Contains("LastTime"))
                dt.Columns.Remove("LastTime");

            if (dt.Columns.Contains("LastUser"))
                dt.Columns.Remove("LastUser");


            if (dt.Columns.Contains("Org"))
                dt.Columns.Remove("Org");
            if (dt.Columns.Contains("Area"))
                dt.Columns.Remove("Area");
            if (dt.Columns.Contains("Length"))
                dt.Columns.Remove("Length");

            // if (name == "gcode" || name == "objectid" || name == "shape" || name == "user" || name == "time" || name == "length" || name == "area" || name == "usergroup")

            int ids = int.Parse(id);
            var tbl = _dbContext.TableInfos.Where(a => a.Id == ids).FirstOrDefault();
            UpdateFromTable(DBManagment.GetConnectionString(QueryOnDBMode.Main), tbl.Name, FieldSource, FieldDest, FieldCheck, dt, org);
        }

        private void UpdateFromTable(string conString, string tableName, string FieldSource, string FieldDest, string FieldCheck, DataTable dt, string org)
        {
            var user = _dbContext.Users.Where(a => a.UserName == User.Identity.Name).FirstOrDefault();
            string time = FN.GeoServer.DomainClasses.Convertion.ConvertDateTimeToPersianLong(DateTime.Now);


            var field_d = FieldDest.Split(',');
            var field_s = FieldSource.Split(',');
            var field_c = FieldCheck.Split(',');

            List<string> fieldsources = new List<string>();
            for (int i = 0; i < field_s.Length; i++)
                if (field_d[i] != "")
                    fieldsources.Add(field_s[i] + "=@" + field_s[i]);

            fieldsources.Add("LastTime=@LastTime");
            fieldsources.Add("[LastUser]=@LastUser");

            fieldsources.Add("Org=@Org");

            List<string> fieldcheck = new List<string>();
            for (int i = 0; i < field_c.Length; i++)
                if (field_c[i] != "")
                    fieldcheck.Add(field_c[i] + "=@c" + field_c[i]);

            using (System.Data.SqlClient.SqlConnection con = new System.Data.SqlClient.SqlConnection(conString))
            {
                con.Open();
                var tran = con.BeginTransaction();
                try
                {
                    foreach (DataRow item in dt.Rows)
                    {
                        using (System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand("", con, tran))
                        {
                            cmd.CommandText = "Update " + tableName + " Set " + string.Join(",", fieldsources) + " Where " + string.Join(" and ", fieldcheck);

                            for (int i = 0; i < field_s.Length; i++)
                            {
                                if (field_d[i] != "")
                                    cmd.Parameters.AddWithValue("@" + field_s[i], item[field_d[i]]);
                                if (field_c[i] != "")
                                    cmd.Parameters.AddWithValue("@c" + field_c[i], item[field_c[i]]);
                            }

                            cmd.Parameters.AddWithValue("@LastTime", time);
                            cmd.Parameters.AddWithValue("@LastUser", user.FullName);
                            //cmd.Parameters.AddWithValue("@UserGroup", (user != null && user.UserGroup != null) ? user.UserGroup.Name.ToString() : null);
                            cmd.Parameters.AddWithValue("@Org", org);

                            cmd.ExecuteNonQuery();
                        }
                    }
                    tran.Commit();
                }
                catch (Exception e)
                {
                    tran.Rollback();
                }
            }
        }


        // متد کمکی برای تبدیل هندسه - نسخه بهبودیافته
        private Geometry TransformGeometry(Geometry geometry, ICoordinateTransformation transformation)
        {
            if (geometry == null)
                throw new ArgumentNullException(nameof(geometry));

            if (transformation == null)
                return geometry.Copy();

            try
            {
                var geometryFactory = geometry.Factory;

                return geometry switch
                {
                    Point point => TransformPoint(point, transformation, geometryFactory),
                    LineString lineString => TransformLineString(lineString, transformation, geometryFactory),
                    Polygon polygon => TransformPolygon(polygon, transformation, geometryFactory),
                    MultiPoint multiPoint => TransformMultiPoint(multiPoint, transformation, geometryFactory),
                    MultiLineString multiLineString => TransformMultiLineString(multiLineString, transformation, geometryFactory),
                    MultiPolygon multiPolygon => TransformMultiPolygon(multiPolygon, transformation, geometryFactory),
                    GeometryCollection geometryCollection => TransformGeometryCollection(geometryCollection, transformation, geometryFactory),
                    _ => throw new ArgumentException($"نوع هندسه {geometry.GeometryType} پشتیبانی نمی‌شود.")
                };
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطا در تبدیل هندسه نوع {GeometryType}", geometry.GeometryType);
                throw new InvalidOperationException($"خطا در تبدیل هندسه: {ex.Message}", ex);
            }
        }

        private Point TransformPoint(Point point, ICoordinateTransformation transformation, GeometryFactory factory)
        {
            var coord = TransformCoordinate(point.Coordinate, transformation);
            return factory.CreatePoint(coord);
        }

        private LineString TransformLineString(LineString lineString, ICoordinateTransformation transformation, GeometryFactory factory)
        {
            var coords = lineString.Coordinates.Select(c => TransformCoordinate(c, transformation)).ToArray();
            return factory.CreateLineString(coords);
        }

        private Polygon TransformPolygon(Polygon polygon, ICoordinateTransformation transformation, GeometryFactory factory)
        {
            // تبدیل حلقه خارجی
            var exteriorCoords = polygon.ExteriorRing.Coordinates.Select(c => TransformCoordinate(c, transformation)).ToArray();
            var exteriorRing = factory.CreateLinearRing(exteriorCoords);

            // تبدیل حلقه‌های داخلی (سوراخ‌ها)
            var interiorRings = new LinearRing[polygon.NumInteriorRings];
            for (int i = 0; i < polygon.NumInteriorRings; i++)
            {
                var interiorCoords = polygon.GetInteriorRingN(i).Coordinates.Select(c => TransformCoordinate(c, transformation)).ToArray();
                interiorRings[i] = factory.CreateLinearRing(interiorCoords);
            }

            return factory.CreatePolygon(exteriorRing, interiorRings);
        }

        private MultiPoint TransformMultiPoint(MultiPoint multiPoint, ICoordinateTransformation transformation, GeometryFactory factory)
        {
            var points = new Point[multiPoint.NumGeometries];
            for (int i = 0; i < multiPoint.NumGeometries; i++)
            {
                points[i] = TransformPoint((Point)multiPoint.GetGeometryN(i), transformation, factory);
            }
            return factory.CreateMultiPoint(points);
        }

        private MultiLineString TransformMultiLineString(MultiLineString multiLineString, ICoordinateTransformation transformation, GeometryFactory factory)
        {
            var lineStrings = new LineString[multiLineString.NumGeometries];
            for (int i = 0; i < multiLineString.NumGeometries; i++)
            {
                lineStrings[i] = TransformLineString((LineString)multiLineString.GetGeometryN(i), transformation, factory);
            }
            return factory.CreateMultiLineString(lineStrings);
        }

        private MultiPolygon TransformMultiPolygon(MultiPolygon multiPolygon, ICoordinateTransformation transformation, GeometryFactory factory)
        {
            var polygons = new Polygon[multiPolygon.NumGeometries];
            for (int i = 0; i < multiPolygon.NumGeometries; i++)
            {
                polygons[i] = TransformPolygon((Polygon)multiPolygon.GetGeometryN(i), transformation, factory);
            }
            return factory.CreateMultiPolygon(polygons);
        }

        private GeometryCollection TransformGeometryCollection(GeometryCollection geometryCollection, ICoordinateTransformation transformation, GeometryFactory factory)
        {
            var geometries = new Geometry[geometryCollection.NumGeometries];
            for (int i = 0; i < geometryCollection.NumGeometries; i++)
            {
                geometries[i] = TransformGeometry(geometryCollection.GetGeometryN(i), transformation);
            }
            return factory.CreateGeometryCollection(geometries);
        }

        private Coordinate TransformCoordinate(Coordinate coordinate, ICoordinateTransformation transformation)
        {
            try
            {
                var transformed = transformation.MathTransform.Transform(new[] { coordinate.X, coordinate.Y });
                return new Coordinate(transformed[0], transformed[1]);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "خطا در تبدیل مختصات ({X}, {Y})", coordinate.X, coordinate.Y);
                // در صورت خطا، مختصات اصلی را برگردان
                return coordinate.Copy();
            }
        }

        // متد به‌روزرسانی‌شده TransformToUTM - استفاده از GeometryTransformer
        public Geometry TransformToUTM(Geometry geometry, CoordinateSystem sourceCoordSystem)
        {
            var transformer = new Helper.GeometryTransformer();
            return transformer.TransformToUTM(geometry, sourceCoordSystem);
        }

        private List<FileInfo> UnZipFile(string folderName, string filePath)
        {
            var extractedFiles = new List<FileInfo>();
            var baseDirectory = Path.Combine(_environment.WebRootPath, "Upload");

            try
            {
                using (var zipStream = new ICSharpCode.SharpZipLib.Zip.ZipInputStream(System.IO.File.OpenRead(filePath)))
                {
                    ICSharpCode.SharpZipLib.Zip.ZipEntry entry;
                    while ((entry = zipStream.GetNextEntry()) != null)
                    {
                        if (entry.IsFile && !string.IsNullOrEmpty(entry.Name))
                        {
                            var newFilePath = Path.Combine(baseDirectory, folderName, entry.Name);
                            Directory.CreateDirectory(Path.GetDirectoryName(newFilePath));

                            using (var streamWriter = System.IO.File.Create(newFilePath))
                            {
                                var buffer = new byte[2048];
                                int size;
                                while ((size = zipStream.Read(buffer, 0, buffer.Length)) > 0)
                                {
                                    streamWriter.Write(buffer, 0, size);
                                }
                            }
                            extractedFiles.Add(new FileInfo(newFilePath));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // لاگ خطا
                throw new Exception("خطا در استخراج فایل ZIP: " + ex.Message, ex);
            }

            return extractedFiles;
        }

        #endregion

        // Import Shapefile
        public IActionResult ImportShapefile(int id)
        {
            ViewData["TableId"] = id;
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ImportShapefile(int tableId, IFormFile shapefile)
        {
            try
            {
                var tableInfo = await _dbContext.TableInfos.Include(t => t.FieldInfos).FirstOrDefaultAsync(t => t.Id == tableId);
                if (tableInfo == null)
                {
                    _logger.LogWarning("ImportShapefile: TableInfo with ID {TableInfoId} not found.", tableId);
                    TempData["ErrorMessage"] = "جدول یافت نشد.";
                    return RedirectToAction("ImportShapefile", new { tableId });
                }

                if (shapefile == null || shapefile.Length == 0)
                {
                    _logger.LogWarning("ImportShapefile: No file uploaded for TableInfo ID {TableInfoId}.", tableId);
                    TempData["ErrorMessage"] = "فایلی آپلود نشده است.";
                    return RedirectToAction("ImportShapefile", new { tableId });
                }

                var tempDir = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
                Directory.CreateDirectory(tempDir);
                var zipPath = Path.Combine(tempDir, shapefile.FileName);
                using (var stream = new FileStream(zipPath, FileMode.Create))
                {
                    await shapefile.CopyToAsync(stream);
                }
                ZipFile.ExtractToDirectory(zipPath, tempDir);
                _logger.LogInformation("Successfully extracted shapefile zip to temporary directory: {TempDir}", tempDir);

                var shpFile = Directory.GetFiles(tempDir, "*.shp").FirstOrDefault();
                if (shpFile == null)
                {
                    _logger.LogWarning("ImportShapefile: No .shp file found in zip for TableInfo ID {TableInfoId}.", tableId);
                    TempData["ErrorMessage"] = "فایل .shp در زیپ یافت نشد.";
                    return RedirectToAction("ImportShapefile", new { tableId });
                }

                using var reader = new ShapeDataReader(shpFile);
                var features = reader.ReadByMBRFilter(reader.ShapefileBounds);
                var firstFeature = features.FirstOrDefault();
                if (firstFeature == null)
                {
                    _logger.LogWarning("ImportShapefile: No features found in shapefile for TableInfo ID {TableInfoId}.", tableId);
                    TempData["ErrorMessage"] = "هیچ فیچری در شپ‌فایل یافت نشد.";
                    return RedirectToAction("ImportShapefile", new { tableId });
                }

                string geomType = firstFeature.Geometry.GeometryType.ToLower();
                string tableGeomType = tableInfo.DatasetType.ToLower();
                bool typeMatch = (tableGeomType.Contains("polygon") && (geomType.Contains("polygon") || geomType.Contains("multipolygon"))) ||
                                 (tableGeomType.Contains("polyline") && (geomType.Contains("polyline") || geomType.Contains("multilinestring"))) ||
                                 (tableGeomType.Contains("point") && geomType.Contains("point"));
                if (!typeMatch)
                {
                    _logger.LogWarning("ImportShapefile: Geometry type mismatch for TableInfo ID {TableInfoId}. Table expects {TableGeomType}, shapefile is {GeomType}.", tableId, tableInfo.DatasetType, geomType);
                    TempData["ErrorMessage"] = $"عدم تطابق نوع هندسی. جدول انتظار {tableInfo.DatasetType} دارد، شپ‌فایل {geomType} است.";
                    return RedirectToAction("ImportShapefile", new { tableId });
                }

                _logger.LogWarning("ImportShapefile: CRS transformation to EPSG:3857 is not implemented.");

                var shapeFields = firstFeature.Attributes.GetNames().Select(n => new { Name = n, Type = firstFeature.Attributes[n]?.GetType().Name ?? "string" }).ToList();
                // حذف ObjectId و Shape از tableFields
                var tableFields = tableInfo.FieldInfos
                    .Where(f => f.Name != "ObjectId" && f.Name != "Shape")
                    .Select(f => new { f.Name, f.FieldType })
                    .ToList();

                // تولید نگاشت فقط برای فیلدهای غیرپیش‌فرض
                var mapping = tableFields.Select(tf => new
                {
                    TableField = tf.Name,
                    ShapeField = shapeFields.FirstOrDefault(sf => sf.Name.ToLower() == tf.Name.ToLower())?.Name ?? ""
                }).ToList();

                _logger.LogInformation("Successfully read shapefile and prepared mapping info for TableInfo ID {TableInfoId}.", tableId);
                ViewData["TableId"] = tableId;
                ViewData["ShapefilePath"] = shpFile;
                ViewData["ShapeFields"] = shapeFields;
                ViewData["TableFields"] = tableFields;
                ViewData["Mapping"] = mapping;
                return View("ImportShapefileMapping");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing shapefile for TableInfo ID {TableInfoId}.", tableId);
                TempData["ErrorMessage"] = "خطایی در وارد کردن شپ‌فایل رخ داد.";
                return RedirectToAction("ImportShapefile", new { tableId });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ImportShapefileApply(ShapefileImportMappingDto dto)
        {
            int imported = 0;
            try
            {
                // اعتبارسنجی TableInfo
                var tableInfo = await _dbContext.TableInfos
                    .Include(t => t.FieldInfos)
                    .FirstOrDefaultAsync(t => t.Id == dto.TableInfoId);
                if (tableInfo == null)
                {
                    _logger.LogWarning("ImportShapefileApply: TableInfo with ID {TableInfoId} not found.", dto.TableInfoId);
                    TempData["ErrorMessage"] = "جدول یافت نشد.";
                    return RedirectToAction("ImportShapefile", new { tableId = dto.TableInfoId });
                }

                // اعتبارسنجی وجود فایل Shapefile
                if (!System.IO.File.Exists(dto.ShapefilePath))
                {
                    _logger.LogWarning("ImportShapefileApply: Shapefile not found on server at path: {ShapefilePath}", dto.ShapefilePath);
                    TempData["ErrorMessage"] = "شپ‌فایل در سرور یافت نشد.";
                    return RedirectToAction("ImportShapefile", new { tableId = dto.TableInfoId });
                }

                // مسیر فایل‌های مرتبط
                var shpEntry = dto.ShapefilePath;
                var prjEntry = Path.ChangeExtension(dto.ShapefilePath, ".prj");

                // خواندن سیستم مختصات از فایل .prj
                CoordinateSystem sourceCoordSystem = null;
                try
                {
                    if (System.IO.File.Exists(prjEntry))
                    {
                        var prjContent = await System.IO.File.ReadAllTextAsync(prjEntry);
                        var csFactory = new CoordinateSystemFactory();
                        sourceCoordSystem = csFactory.CreateFromWkt(prjContent);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "ImportShapefileApply: Failed to read .prj file for Shapefile at {ShapefilePath}. Assuming WGS84.", dto.ShapefilePath);
                }

                // تنظیم سیستم مختصات پیش‌فرض (WGS84) اگر .prj وجود نداشت
                if (sourceCoordSystem == null)
                {
                    sourceCoordSystem = GeographicCoordinateSystem.WGS84;
                    _logger.LogInformation("ImportShapefileApply: No .prj file found. Assuming WGS84 for Shapefile at {ShapefilePath}.", dto.ShapefilePath);
                }

                // تعریف سیستم مختصات هدف (Web Mercator, EPSG:3857)
                var targetCoordSystem = ProjectedCoordinateSystem.WebMercator;
                targetCoordSystem.Name = "WGS_1984_Web_Mercator_Auxiliary_Sphere";
                ICoordinateTransformation transformation = null;
                if (!IsSameCoordinateSystem(sourceCoordSystem, targetCoordSystem))
                {
                    try
                    {
                        var ctFactory = new CoordinateTransformationFactory();
                        transformation = ctFactory.CreateFromCoordinateSystems(sourceCoordSystem, targetCoordSystem);
                        _logger.LogInformation("ImportShapefileApply: Coordinate transformation created from {SourceWKT} to Web Mercator.", sourceCoordSystem.WKT);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "ImportShapefileApply: Failed to create coordinate transformation from {SourceWKT} to Web Mercator.", sourceCoordSystem.WKT);
                        TempData["ErrorMessage"] = "خطا در تبدیل سیستم مختصات شپ‌فایل.";
                        return RedirectToAction("ImportShapefile", new { tableId = dto.TableInfoId });
                    }
                }
                else
                {
                    _logger.LogInformation("ImportShapefileApply: Source coordinate system is already Web Mercator. No transformation needed.");
                }

                // تنظیم DataTable برای Bulk Insert
                var dataTable = new DataTable();
                var fieldMappings = new List<(string TableField, string ShapeField, string FieldType)>();
                foreach (var map in dto.FieldMappings)
                {
                    var tableField = tableInfo.FieldInfos.FirstOrDefault(f => f.Name == map.TableField);
                    if (tableField == null || tableField.Name == "ObjectId") // نادیده گرفتن ObjectId
                    {
                        _logger.LogWarning("ImportShapefileApply: Field {TableField} not found or is ObjectId in TableInfo ID {TableInfoId}.", map.TableField, dto.TableInfoId);
                        continue;
                    }
                    dataTable.Columns.Add(tableField.Name, GetColumnType(tableField.FieldType));
                    fieldMappings.Add((tableField.Name, map.ShapeField, tableField.FieldType));
                }
                dataTable.Columns.Add("Shape", typeof(SqlGeometry));

                // تنظیمات Bulk Insert
                const int batchSize = 1000;
                var batchRows = new List<DataRow>();

                // خواندن Shapefile
                var precisionModel = PrecisionModel.Floating.Value;
                using var reader = new ShapefileDataReader(dto.ShapefilePath, new GeometryFactory(precisionModel, 3857));
                var dbaseHeader = reader.DbaseHeader;

                while (reader.Read())
                {
                    var geometry = reader.Geometry;
                    if (geometry == null)
                    {
                        _logger.LogWarning("ImportShapefileApply: Skipping feature with null geometry.");
                        continue;
                    }

                    if (!geometry.IsValid)
                    {
                        _logger.LogWarning("ImportShapefileApply: Invalid geometry detected. Attempting to fix.");
                        geometry = geometry.Buffer(0);
                        if (!geometry.IsValid)
                        {
                            _logger.LogWarning("ImportShapefileApply: Geometry is still invalid. Skipping.");
                            continue;
                        }
                    }

                    try
                    {
                        if (transformation != null)
                        {
                            geometry = TransformGeometry(geometry, transformation, precisionModel);
                            geometry.SRID = 3857;
                        }
                        else
                        {
                            geometry.SRID = 3857;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "ImportShapefileApply: Failed to transform geometry for feature. Skipping.");
                        continue;
                    }

                    var row = dataTable.NewRow();
                    foreach (var map in fieldMappings)
                    {
                        var shapeFieldIndex = Array.FindIndex(dbaseHeader.Fields, f => f.Name.Equals(map.ShapeField, StringComparison.OrdinalIgnoreCase));
                        object val = shapeFieldIndex >= 0 ? reader.GetValue(shapeFieldIndex) : null;

                        if (val == null || string.IsNullOrEmpty(val.ToString()))
                        {
                            row[map.TableField] = DBNull.Value;
                        }
                        else
                        {
                            var stringVal = val.ToString();
                            if (map.FieldType == "int" || map.FieldType == "bigint" || map.FieldType == "numeric")
                            {
                                row[map.TableField] = Convert.ChangeType(stringVal, GetColumnType(map.FieldType));
                            }
                            else
                            {
                                row[map.TableField] = stringVal;
                            }
                        }
                    }

                    try
                    {
                        var wkt = geometry.AsText();
                        var sqlGeometry = SqlGeometry.Parse(wkt);
                        sqlGeometry.STSrid = 3857;
                        row["Shape"] = sqlGeometry;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "ImportShapefileApply: Failed to convert WKT to SqlGeometry for feature. Skipping.");
                        continue;
                    }

                    batchRows.Add(row);

                    if (batchRows.Count >= batchSize)
                    {
                        imported += await BulkInsertAsync(tableInfo.Name, dataTable, batchRows);
                        batchRows.Clear();
                        dataTable.Rows.Clear();
                    }
                }

                if (batchRows.Any())
                {
                    imported += await BulkInsertAsync(tableInfo.Name, dataTable, batchRows);
                }

                _logger.LogInformation("ImportShapefileApply: Successfully imported {ImportedCount} records into TableInfo ID {TableInfoId}.", imported, dto.TableInfoId);
                TempData["SuccessMessage"] = $"تعداد {imported} رکورد وارد شد.";
                return RedirectToAction("TableDetails", new { id = dto.TableInfoId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ImportShapefileApply: Error applying shapefile import for TableInfo ID {TableInfoId}.", dto.TableInfoId);
                TempData["ErrorMessage"] = "خطایی در اعمال وارد کردن شپ‌فایل رخ داد.";
                return RedirectToAction("ImportShapefile", new { tableId = dto.TableInfoId });
            }
        }

        private async Task<int> BulkInsertAsync(string tableName, DataTable dataTable, List<DataRow> rows)
        {
            foreach (var row in rows)
            {
                dataTable.Rows.Add(row);
            }

            try
            {
                var connectionString = _dbContext.Database.GetConnectionString();
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();

                using var bulkCopy = new SqlBulkCopy(connection)
                {
                    DestinationTableName = $"[{tableName}]",
                    BulkCopyTimeout = 600 // 10 دقیقه
                };

                // نگاشت ستون‌ها
                foreach (DataColumn column in dataTable.Columns)
                {
                    bulkCopy.ColumnMappings.Add(column.ColumnName, column.ColumnName);
                }

                await bulkCopy.WriteToServerAsync(dataTable);
                _logger.LogDebug("BulkInsertAsync: Successfully inserted {RowCount} rows into table {TableName}.", rows.Count, tableName);
                return rows.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "BulkInsertAsync: Error inserting {RowCount} rows into table {TableName}.", rows.Count, tableName);
                throw;
            }
        }

        private Geometry TransformGeometry(Geometry geometry, ICoordinateTransformation transformation, PrecisionModel precisionModel)
        {
            if (geometry == null || transformation == null)
                return geometry;

            var coords = geometry.Coordinates.Select(c =>
            {
                var transformed = transformation.MathTransform.Transform(new[] { c.X, c.Y });
                return new Coordinate(transformed[0], transformed[1]);
            }).ToArray();

            var factory = new GeometryFactory(precisionModel, 3857);
            if (geometry is Point point)
            {
                return factory.CreatePoint(coords[0]);
            }
            else if (geometry is LineString lineString)
            {
                return factory.CreateLineString(coords);
            }
            else if (geometry is Polygon polygon)
            {
                var shell = factory.CreateLinearRing(coords);
                return factory.CreatePolygon(shell);
            }
            else if (geometry is MultiPoint multiPoint)
            {
                return factory.CreateMultiPoint(coords.Select(c => factory.CreatePoint(c)).ToArray());
            }
            else if (geometry is MultiLineString multiLineString)
            {
                return factory.CreateMultiLineString(new[] { factory.CreateLineString(coords) });
            }
            else if (geometry is MultiPolygon multiPolygon)
            {
                return factory.CreateMultiPolygon(new[] { factory.CreatePolygon(factory.CreateLinearRing(coords)) });
            }

            throw new NotSupportedException($"Geometry type {geometry.GeometryType} is not supported for transformation.");
        }

        private Type GetColumnType(string fieldType)
        {
            return fieldType switch
            {
                "int" => typeof(int),
                "bigint" => typeof(long),
                "numeric" => typeof(decimal),
                "bit" => typeof(bool),
                _ => typeof(string)
            };
        }

        private bool IsSameCoordinateSystem(CoordinateSystem source, CoordinateSystem target)
        {
            if (source == null || target == null)
                return false;

            // مقایسه با استفاده از WKT
            if (source.WKT == target.WKT)
                return true;
            if (source.Name == target.Name)
                return true;
            // مقایسه با استفاده از کد EPSG
            long sourceEpsg = source.AuthorityCode;
            long targetEpsg = target.AuthorityCode;
            return sourceEpsg == targetEpsg && sourceEpsg == 3857; // EPSG:3857 برای Web Mercator
        }



        [HttpGet]
        public IActionResult GenerateTile(int tableId)
        {
            var model = new GenerateTileViewModel
            {
                TableInfoId = tableId
            };
            return View(model);
        }

        // اکشن برای پردازش درخواست تولید تایل
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> GenerateTile(GenerateTileViewModel dto, string operationId)
        {
            if (!ModelState.IsValid)
            {
                await _hubContext.Clients.Group(operationId).SendAsync("ReceiveError", "پارامترهای ورودی نامعتبر هستند.");
                return Json(new { success = false, message = "پارامترهای ورودی نامعتبر هستند." });
            }

            try
            {
                var tableInfo = await _dbContext.TableInfos.FirstOrDefaultAsync(t => t.Id == dto.TableInfoId);
                if (tableInfo == null)
                {
                    await _hubContext.Clients.Group(operationId).SendAsync("ReceiveError", "جدول موردنظر یافت نشد.");
                    return NotFound();
                }

                // اعتبارسنجی ورودی‌ها
                if (dto.MinZoom < 0 || dto.MaxZoom < dto.MinZoom)
                {
                    await _hubContext.Clients.Group(operationId).SendAsync("ReceiveError", "سطوح زوم نامعتبر هستند.");
                    return BadRequest("Invalid zoom levels.");
                }
                if (dto.MinLat.HasValue && dto.MaxLat.HasValue && (dto.MinLat > dto.MaxLat || dto.MinLat < -85 || dto.MaxLat > 85))
                {
                    await _hubContext.Clients.Group(operationId).SendAsync("ReceiveError", "محدوده عرض جغرافیایی نامعتبر است.");
                    return BadRequest("Invalid latitude range.");
                }
                if (dto.MinLon.HasValue && dto.MaxLon.HasValue && (dto.MinLon > dto.MaxLon || dto.MinLon < -180 || dto.MaxLon > 180))
                {
                    await _hubContext.Clients.Group(operationId).SendAsync("ReceiveError", "محدوده طول جغرافیایی نامعتبر است.");
                    return BadRequest("Invalid longitude range.");
                }

                await _hubContext.Clients.Group(operationId).SendAsync("ReceiveProgress", 0, "شروع فرآیند تولید تایل...");

                if (dto.UseBatchData)
                {
                    var results = await GenerateTilesFromAllData(tableInfo, dto, operationId);
                    await _hubContext.Clients.Group(operationId).SendAsync("ReceiveProgress", 100, "تایل‌ها با موفقیت تولید شدند.");
                    return Json(new { success = true, message = "تایل‌ها با موفقیت تولید شدند.", results });
                }
                else
                {
                    if (dto.MinZoom == dto.MaxZoom && dto.MinLat.HasValue && dto.MinLon.HasValue && dto.MaxLat.HasValue && dto.MaxLon.HasValue)
                    {
                        // تولید یک تایل خاص
                        int z = dto.MinZoom;
                        (int x, int y) = LatLonToTileXY(dto.MinLat.Value, dto.MinLon.Value, z);
                        var result = await GenerateAndSaveTile(tableInfo, z, x, y, operationId);
                        await _hubContext.Clients.Group(operationId).SendAsync("ReceiveProgress", 100, "تایل با موفقیت تولید شد.");
                        return Ok(result);
                    }

                    // تولید دسته‌ای تایل‌ها
                    var results = new List<object>();
                    int totalTiles = CalculateTotalTiles(dto);
                    int processedTiles = 0;

                    for (int z = dto.MinZoom; z <= dto.MaxZoom; z++)
                    {
                        (int minX, int maxY) = LatLonToTileXY(dto.MinLat ?? -85, dto.MinLon ?? -180, z);
                        (int maxX, int minY) = LatLonToTileXY(dto.MaxLat ?? 85, dto.MaxLon ?? 180, z);
                        for (int x = minX; x <= maxX; x++)
                        {
                            for (int y = minY; y <= maxY; y++)
                            {
                                var result = await GenerateAndSaveTile(tableInfo, z, x, y, operationId);
                                results.Add(result);
                                processedTiles++;
                                int progress = (int)((double)processedTiles / totalTiles * 100);
                                await _hubContext.Clients.Group(operationId).SendAsync("ReceiveProgress", progress, $"Processing tile z={z}, x={x}, y={y}");
                            }
                        }
                    }
                    await _hubContext.Clients.Group(operationId).SendAsync("ReceiveProgress", 100, "All tiles generated successfully.");
                    return Json(new { success = true, message = "تایل‌ها با موفقیت تولید شدند.", results });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GenerateTile: Error generating tiles for TableInfo ID {TableId}.", dto.TableInfoId);
                await _hubContext.Clients.Group(operationId).SendAsync("ReceiveError", $"خطا در تولید تایل: {ex.Message}");
                return Json(new { success = false, message = $"خطا در تولید تایل: {ex.Message}" });
            }
        }

        private async Task<List<object>> GenerateTilesFromAllData(TableInfo tableInfo, GenerateTileViewModel dto, string operationId)
        {
            var results = new List<object>();
            var features = new List<(Geometry Geom, AttributesTable Attrs)>();

            try
            {
                using (var connection = new SqlConnection(_dbContext.Database.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    await _hubContext.Clients.Group(operationId).SendAsync("ReceiveProgress", 5, "Reading data from database...");
                    using (var cmd = connection.CreateCommand())
                    {
                        cmd.CommandText = $@"SELECT [Shape].STAsBinary() as geom, * FROM [{tableInfo.Name}]";
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var wkb = (byte[])reader["geom"];
                                var geom = new NetTopologySuite.IO.WKBReader().Read(wkb);
                                if (!geom.IsValid)
                                {
                                    _logger.LogWarning("GenerateTilesFromAllData: Invalid geometry detected. Attempting to fix.");
                                    geom = geom.Buffer(0);
                                    if (!geom.IsValid)
                                    {
                                        _logger.LogWarning("GenerateTilesFromAllData: Geometry is still invalid. Skipping.");
                                        continue;
                                    }
                                }

                                var attrs = new AttributesTable();
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    if (reader.GetName(i) != "geom")
                                        attrs.Add(reader.GetName(i), reader.GetValue(i));
                                }
                                features.Add((geom, attrs));
                            }
                        }
                    }
                }
                _logger.LogInformation("GenerateTilesFromAllData: Loaded {count} features for TableInfo ID {tableId}.", features.Count, tableInfo.Id);
                await _hubContext.Clients.Group(operationId).SendAsync("ReceiveProgress", 10, $"Loaded {features.Count} features.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GenerateTilesFromAllData: Failed to load features for TableInfo ID {TableId}.", tableInfo.Id);
                await _hubContext.Clients.Group(operationId).SendAsync("ReceiveError", $"Failed to load features: {ex.Message}");
                throw;
            }

            int totalTiles = CalculateTotalTiles(dto);
            int processedTiles = 0;

            for (int z = dto.MinZoom; z <= dto.MaxZoom; z++)
            {
                (int minX, int maxY) = LatLonToTileXY(dto.MinLat ?? -85, dto.MinLon ?? -180, z);
                (int maxX, int minY) = LatLonToTileXY(dto.MaxLat ?? 85, dto.MaxLon ?? 180, z);

                for (int x = minX; x <= maxX; x++)
                {
                    for (int y = minY; y <= maxY; y++)
                    {
                        var tileBounds = GetTileBounds3857(x, y, z);
                        var layer = new Layer { Name = "layer0" };

                        foreach (var (geom, attrs) in features)
                        {
                            var tileGeom = GeometryFromText(tileBounds.wkt, 3857);
                            if (geom.Intersects(tileGeom))
                            {
                                layer.Features.Add(new Feature(geom, attrs));
                            }
                        }

                        if (layer.Features.Count == 0)
                        {
                            _logger.LogInformation("GenerateTilesFromAllData: Tile z={z}, x={x}, y={y} is empty. Skipping.", z, x, y);
                            results.Add(new { z, x, y, filePath = (string)null, size = 0, empty = true });
                        }
                        else
                        {
                            results.Add(await SaveTile(tableInfo, z, x, y, layer, operationId));
                        }

                        processedTiles++;
                        int progress = (int)((double)processedTiles / totalTiles * 90) + 10;
                        await _hubContext.Clients.Group(operationId).SendAsync("ReceiveProgress", progress, $"Processing tile z={z}, x={x}, y={y}");
                    }
                }
            }

            return results;
        }

        private async Task<object> SaveTile(TableInfo tableInfo, int z, int x, int y, Layer layer, string operationId)
        {
            var tileDef = new NetTopologySuite.IO.VectorTiles.Tiles.Tile(x, y, z);
            var vt = new VectorTile { TileId = tileDef.Id };
            vt.Layers.Add(layer);
            byte[] mvtBytes;
            try
            {
                using (var ms = new MemoryStream())
                {
                    MapboxTileWriter2.Write(vt, ms, 1, 2, 4096);
                    mvtBytes = ms.ToArray();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SaveTile: Failed to encode MVT for tile z={z}, x={x}, y={y}.", z, x, y);
                await _hubContext.Clients.Group(operationId).SendAsync("ReceiveError", $"Failed to encode tile: {ex.Message}");
                throw;
            }

            string basePath = _configuration["TileStoragePath"] ?? "wwwroot/tiles";
            string dir = Path.Combine(basePath, tableInfo.Id.ToString(), z.ToString(), x.ToString());
            string filePath;

            try
            {
                Directory.CreateDirectory(dir);
                filePath = Path.Combine(dir, $"{y}.mvt");
                await System.IO.File.WriteAllBytesAsync(filePath, mvtBytes);

                var strategy = _dbContext.Database.CreateExecutionStrategy();
                await strategy.ExecuteAsync(async () =>
                {
                    using var scope = _scopeFactory.CreateScope();
                    var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                    using var transaction = await _dbContext.Database.BeginTransactionAsync();
                    try
                    {
                        var meta = await _dbContext.TileMetas
                            .FirstOrDefaultAsync(t => t.TableInfoId == tableInfo.Id && t.Z == z && t.X == x && t.Y == y);

                        if (meta == null)
                        {
                            meta = new TileMeta
                            {
                                TableInfoId = tableInfo.Id,
                                Z = z,
                                X = x,
                                Y = y,
                                FilePath = filePath,
                                Size = mvtBytes.Length,
                                CreatedAt = DateTime.UtcNow
                            };
                            _dbContext.TileMetas.Add(meta);
                        }
                        else
                        {
                            meta.FilePath = filePath;
                            meta.Size = mvtBytes.Length;
                            meta.UpdatedAt = DateTime.UtcNow;
                        }

                        await _dbContext.SaveChangesAsync();
                        await transaction.CommitAsync();

                        _logger.LogInformation("SaveTile: Successfully saved tile z={z}, x={x}, y={y} to {filePath}.", z, x, y, filePath);
                    }
                    catch (Exception ex)
                    {
                        await transaction.RollbackAsync();
                        _logger.LogError(ex, "SaveTile: Failed to save tile metadata z={z}, x={x}, y={y}.", z, x, y);
                        await _hubContext.Clients.Group(operationId).SendAsync("ReceiveError", $"Failed to save tile metadata: {ex.Message}");
                        throw;
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SaveTile: Failed to save tile z={z}, x={x}, y={y}.", z, x, y);
                await _hubContext.Clients.Group(operationId).SendAsync("ReceiveError", $"Failed to save tile: {ex.Message}");
                throw;
            }

            return new { z, x, y, filePath, size = mvtBytes.Length };
        }

        private async Task<object> GenerateAndSaveTile(TableInfo tableInfo, int z, int x, int y, string operationId)
        {
            var tileBounds = GetTileBounds3857(x, y, z);
            var sql = $@"SELECT [Shape].STAsBinary() as geom, * FROM [{tableInfo.Name}] WHERE [Shape].STIntersects(geometry::STGeomFromText('{tileBounds.wkt}', 3857)) = 1";
            var layer = new Layer { Name = "layer0" };

            try
            {
                using (var connection = new SqlConnection(_dbContext.Database.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    using (var cmd = connection.CreateCommand())
                    {
                        cmd.CommandText = sql;
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var wkb = (byte[])reader["geom"];
                                var geom = new NetTopologySuite.IO.WKBReader().Read(wkb);
                                if (!geom.IsValid)
                                {
                                    _logger.LogWarning("GenerateAndSaveTile: Invalid geometry detected for tile z={z}, x={x}, y={y}. Attempting to fix.", z, x, y);
                                    geom = geom.Buffer(0);
                                    if (!geom.IsValid)
                                    {
                                        _logger.LogWarning("GenerateAndSaveTile: Geometry is still invalid. Skipping.");
                                        continue;
                                    }
                                }
                                var attr = new AttributesTable();
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    if (reader.GetName(i) != "geom")
                                        attr.Add(reader.GetName(i), reader.GetValue(i));
                                }
                                layer.Features.Add(new Feature(geom, attr));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GenerateAndSaveTile: Failed to query features for tile z={z}, x={x}, y={y}.", z, x, y);
                await _hubContext.Clients.Group(operationId).SendAsync("ReceiveError", $"Failed to query features: {ex.Message}");
                throw;
            }

            if (layer.Features.Count == 0)
            {
                _logger.LogInformation("GenerateAndSaveTile: Tile z={z}, x={x}, y={y} is empty. Skipping.", z, x, y);
                return new { z, x, y, filePath = (string)null, size = 0, empty = true };
            }

            return await SaveTile(tableInfo, z, x, y, layer, operationId);
        }

        private Geometry GeometryFromText(string wkt, int srid)
        {
            var reader = new NetTopologySuite.IO.WKTReader();
            var geom = reader.Read(wkt);
            geom.SRID = srid;
            return geom;
        }

        private int CalculateTotalTiles(GenerateTileViewModel dto)
        {
            int totalTiles = 0;
            for (int z = dto.MinZoom; z <= dto.MaxZoom; z++)
            {
                (int minX, int maxY) = LatLonToTileXY(dto.MinLat ?? -85, dto.MinLon ?? -180, z);
                (int maxX, int minY) = LatLonToTileXY(dto.MaxLat ?? 85, dto.MaxLon ?? 180, z);
                totalTiles += (maxX - minX + 1) * (maxY - minY + 1);
            }
            return totalTiles;
        }
        private (int x, int y) LatLonToTileXY(double lat, double lon, int z)
        {
            // Web Mercator tile calculation
            int x = (int)Math.Floor((lon + 180.0) / 360.0 * (1 << z));
            int y = (int)Math.Floor((1.0 - Math.Log(Math.Tan(lat * Math.PI / 180.0) + 1.0 / Math.Cos(lat * Math.PI / 180.0)) / Math.PI) / 2.0 * (1 << z));
            return (x, y);
        }

        private (string wkt, double minX, double minY, double maxX, double maxY) GetTileBounds3857(int x, int y, int z)
        {
            double tileSize = 4096; // یا 512، هر چه با extent هماهنگ است
            double initialResolution = 2 * Math.PI * 6378137 / tileSize;
            double originShift = 2 * Math.PI * 6378137 / 2.0;
            double resolution = initialResolution / Math.Pow(2, z);
            double minX = x * tileSize * resolution - originShift;
            double maxY = originShift - y * tileSize * resolution;
            double maxX = (x + 1) * tileSize * resolution - originShift;
            double minY = originShift - (y + 1) * tileSize * resolution;
            string wkt = $"POLYGON (({minX} {minY}, {maxX} {minY}, {maxX} {maxY}, {minX} {maxY}, {minX} {minY}))";
            return (wkt, minX, minY, maxX, maxY);
        }


    }
}