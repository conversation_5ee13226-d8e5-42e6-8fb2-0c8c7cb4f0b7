/**
 * Table Manager Module
 * مدیریت جداول
 */

class TableManager {
    constructor() {
        this.tables = new Map();
        this.defaultOptions = this.getDefaultOptions();
    }

    /**
     * تنظیمات پیش‌فرض جدول
     */
    getDefaultOptions() {
        return {
            destroy: true,
            searching: true,
            responsive: true,
            paging: false,
            info: false,
            scrollY: 340,
            fixedHeader: true,
            language: {
                lengthMenu: "نمایش _MENU_ رکورد",
                zeroRecords: "هیچ داده‌ای یافت نشد",
                info: "نمایش صفحه _PAGE_ از _PAGES_",
                infoEmpty: "هیچ رکوردی یافت نشد",
                infoFiltered: "(فیلتر شده از _MAX_ کل رکوردها)",
                search: "",
                searchPlaceholder: "جستجو",
                paginate: {
                    first: "اول",
                    last: "آخر",
                    next: "بعدی",
                    previous: "قبلی"
                }
            }
        };
    }

    /**
     * ایجاد جدول
     */
    createTable(containerId, tableConfig) {
        const container = $(`#${containerId}`);
        if (container.length === 0) {
            console.error(`Container with id ${containerId} not found`);
            return null;
        }

        // Generate table HTML
        const tableHtml = this.generateTableHtml(tableConfig);
        container.html(tableHtml);

        // Initialize DataTable
        const options = { ...this.defaultOptions, ...tableConfig.options };
        const table = container.find('table').DataTable(options);

        // Bind events
        this.bindTableEvents(table, tableConfig);

        // Store table instance
        this.tables.set(containerId, table);

        return table;
    }

    /**
     * تولید HTML جدول
     */
    generateTableHtml(config) {
        const { headers, data, hasClickableRows, panelId } = config;
        
        let html = '<table class="table table-bordered table-hover table-striped w-100">';
        
        // Generate header
        html += '<thead class="bg-warning-200"><tr>';
        headers.forEach(header => {
            html += `<th>${header.title}</th>`;
        });
        html += '</tr></thead>';
        
        // Generate body
        html += '<tbody>';
        data.forEach((row, rowIndex) => {
            const rowClass = hasClickableRows ? 'table-row-clickable' : '';
            const rowData = hasClickableRows ? this.generateRowData(row, config, rowIndex) : '';
            
            html += `<tr class="${rowClass}" ${rowData}>`;
            
            headers.forEach(header => {
                const cellValue = row[header.field] || '';
                html += `<td>${this.formatCellValue(cellValue, header)}</td>`;
            });
            
            html += '</tr>';
        });
        html += '</tbody>';
        
        html += '</table>';
        
        return html;
    }

    /**
     * تولید data attributes برای ردیف
     */
    generateRowData(row, config, rowIndex) {
        if (!config.hasClickableRows) return '';
        
        const dataAttrs = [];
        
        // Panel ID
        if (config.panelId) {
            dataAttrs.push(`data-panel-id="${config.panelId}"`);
        }
        
        // Row data for child panel loading
        if (config.clickableFields) {
            config.clickableFields.forEach((field, index) => {
                const value = row[field] || '';
                dataAttrs.push(`data-input${index === 0 ? '' : index}="${value}"`);
            });
        }
        
        // Row index
        dataAttrs.push(`data-row-index="${rowIndex}"`);
        
        return dataAttrs.join(' ');
    }

    /**
     * فرمت کردن مقدار سلول
     */
    formatCellValue(value, header) {
        if (value === null || value === undefined) return '';
        
        switch (header.type) {
            case 'number':
                return this.formatNumber(value);
            case 'currency':
                return this.formatCurrency(value);
            case 'date':
                return this.formatDate(value);
            case 'boolean':
                return this.formatBoolean(value);
            case 'link':
                return `<a href="${value}" target="_blank">${value}</a>`;
            default:
                return value.toString();
        }
    }

    /**
     * فرمت کردن اعداد
     */
    formatNumber(value) {
        if (typeof value !== 'number') {
            value = parseFloat(value);
        }
        if (isNaN(value)) return '';
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }

    /**
     * فرمت کردن ارز
     */
    formatCurrency(value) {
        const formatted = this.formatNumber(value);
        return formatted ? `${formatted} ریال` : '';
    }

    /**
     * فرمت کردن تاریخ
     */
    formatDate(value) {
        if (!value) return '';
        
        try {
            const date = new Date(value);
            return date.toLocaleDateString('fa-IR');
        } catch {
            return value.toString();
        }
    }

    /**
     * فرمت کردن boolean
     */
    formatBoolean(value) {
        if (typeof value === 'boolean') {
            return value ? '✓' : '✗';
        }
        
        const strValue = value.toString().toLowerCase();
        if (strValue === 'true' || strValue === '1') return '✓';
        if (strValue === 'false' || strValue === '0') return '✗';
        
        return value.toString();
    }

    /**
     * اتصال رویدادهای جدول
     */
    bindTableEvents(table, config) {
        // Row click event
        if (config.hasClickableRows) {
            $(table.table().container()).off('click', 'tbody tr').on('click', 'tbody tr', (e) => {
                this.handleRowClick(e, table, config);
            });
        }

        // Custom events
        if (config.events) {
            Object.keys(config.events).forEach(eventName => {
                table.on(eventName, config.events[eventName]);
            });
        }
    }

    /**
     * مدیریت کلیک روی ردیف
     */
    handleRowClick(event, table, config) {
        const row = $(event.currentTarget);
        
        // Highlight selected row
        this.highlightRow(row, table);
        
        // Extract row data
        const rowData = this.extractRowData(row);
        
        // Custom click handler
        if (config.onRowClick && typeof config.onRowClick === 'function') {
            config.onRowClick(rowData, row, table);
            return;
        }
        
        // Default behavior - load child panels
        if (rowData.panelId && window.dashboardCore) {
            window.dashboardCore.loadChildPanels({
                panelId: rowData.panelId,
                input: rowData.input || '',
                input1: rowData.input1 || '',
                input2: rowData.input2 || '',
                input3: rowData.input3 || '',
                input4: rowData.input4 || '',
                input5: rowData.input5 || '',
                input6: rowData.input6 || '',
                input7: rowData.input7 || '',
                level: config.level || 1
            });
        }
    }

    /**
     * هایلایت کردن ردیف انتخاب شده
     */
    highlightRow(selectedRow, table) {
        // Remove previous highlights
        $(table.table().container()).find('tbody tr').removeClass('table-row-selected');
        
        // Add highlight to selected row
        selectedRow.addClass('table-row-selected');
    }

    /**
     * استخراج داده‌های ردیف
     */
    extractRowData(row) {
        const data = {};
        
        // Extract all data attributes
        $.each(row.get(0).attributes, function() {
            if (this.name.startsWith('data-')) {
                const key = this.name.substring(5); // Remove 'data-' prefix
                data[key] = this.value;
            }
        });
        
        return data;
    }

    /**
     * بروزرسانی جدول
     */
    updateTable(containerId, newConfig) {
        const table = this.tables.get(containerId);
        if (table) {
            // Destroy existing table
            table.destroy();
            
            // Create new table
            this.createTable(containerId, newConfig);
        }
    }

    /**
     * اضافه کردن ردیف
     */
    addRow(containerId, rowData) {
        const table = this.tables.get(containerId);
        if (table) {
            table.row.add(rowData).draw();
        }
    }

    /**
     * حذف ردیف
     */
    removeRow(containerId, rowIndex) {
        const table = this.tables.get(containerId);
        if (table) {
            table.row(rowIndex).remove().draw();
        }
    }

    /**
     * پاک کردن جدول
     */
    clearTable(containerId) {
        const table = this.tables.get(containerId);
        if (table) {
            table.clear().draw();
        }
    }

    /**
     * حذف جدول
     */
    destroyTable(containerId) {
        const table = this.tables.get(containerId);
        if (table) {
            table.destroy();
            this.tables.delete(containerId);
        }
    }

    /**
     * دریافت جدول
     */
    getTable(containerId) {
        return this.tables.get(containerId);
    }

    /**
     * دریافت تمام جداول
     */
    getAllTables() {
        return Array.from(this.tables.values());
    }

    /**
     * صادرات داده‌های جدول
     */
    exportTable(containerId, format = 'csv') {
        const table = this.tables.get(containerId);
        if (!table) return;
        
        const data = table.data().toArray();
        
        switch (format.toLowerCase()) {
            case 'csv':
                this.exportToCsv(data, `table_${containerId}.csv`);
                break;
            case 'excel':
                this.exportToExcel(data, `table_${containerId}.xlsx`);
                break;
            case 'json':
                this.exportToJson(data, `table_${containerId}.json`);
                break;
        }
    }

    /**
     * صادرات به CSV
     */
    exportToCsv(data, filename) {
        const csv = this.convertToCsv(data);
        this.downloadFile(csv, filename, 'text/csv');
    }

    /**
     * صادرات به JSON
     */
    exportToJson(data, filename) {
        const json = JSON.stringify(data, null, 2);
        this.downloadFile(json, filename, 'application/json');
    }

    /**
     * تبدیل به CSV
     */
    convertToCsv(data) {
        if (!data || data.length === 0) return '';
        
        const headers = Object.keys(data[0]);
        const csvHeaders = headers.join(',');
        
        const csvRows = data.map(row => 
            headers.map(header => {
                const value = row[header] || '';
                return `"${value.toString().replace(/"/g, '""')}"`;
            }).join(',')
        );
        
        return [csvHeaders, ...csvRows].join('\n');
    }

    /**
     * دانلود فایل
     */
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
    }
}

// Global instance
window.tableManager = new TableManager();
