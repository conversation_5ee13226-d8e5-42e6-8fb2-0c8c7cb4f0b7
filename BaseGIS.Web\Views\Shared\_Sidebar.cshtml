﻿@{

}
<aside class="app-sidebar bg-body-secondary shadow" data-bs-theme="light">
    <!--begin::Sidebar Brand-->
    <div class="sidebar-brand">
        <!--begin::Brand Link-->
        <a href="@Url.Action("Index", "Home")" class="brand-link">
            <!--begin::Brand Image-->
            <img src="~/assets/img/AdminLTELogo.png"
                 alt="AdminLTE Logo"
                 class="brand-image opacity-75 shadow" />
            <!--end::Brand Image-->
            <!--begin::Brand Text-->
            <span class="brand-text fw-light">AdminLTE 4</span>
            <!--end::Brand Text-->
        </a>
        <!--end::Brand Link-->
    </div>
    <!--end::Sidebar Brand-->
    <!--begin::Sidebar Wrapper-->
    <div class="sidebar-wrapper">
        <nav class="mt-2">
            <!--begin::Sidebar Menu-->
            <ul class="nav sidebar-menu flex-column"
                data-lte-toggle="treeview"
                role="menu"
                data-accordion="false">
                <li class="nav-item">
                    <a href="@Url.Action("Index", "Home")" class="nav-link @Html.RouteIf("Home" , "active" )" title="میز کار">
                        <i class="nav-icon bi bi-speedometer text-info"></i>
                        <p>میز کار</p>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link @Html.RouteIf("Database" , "active" )" >
                        <i class="nav-icon bi bi-palette text-danger"></i>
                        <p>
                            مدیریت داده
                            <i class="nav-arrow bi bi-chevron-right"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <li class="nav-item">
                            <a href="@Url.Action("LayerSetting", "Database")" class="nav-link @Html.RouteIf("Database", "LayerSetting" , "active" )">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>مدیریت ساختار لایه</p>
                            </a>
                        </li>
                        <li class="nav-item ">
                            <a href="@Url.Action("SymbologyManager", "Database")" class="nav-link @Html.RouteIf("Database", "SymbologyManager", "active")">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>مدیریت نماد لایه</p>
                            </a>
                        </li>
                        <li class="nav-item ">
                            <a href="@Url.Action("Insert", "Database")" class="nav-link @Html.RouteIf("Database", "Insert", "active")">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>افزودن یکباره اطلاعات</p>
                            </a>
                        </li>
                        <li class="nav-item ">
                            <a href="@Url.Action("Update", "Database")" class="nav-link @Html.RouteIf("Database", "Update", "active")">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>بروزرسانی اقلام اطلاعات</p>
                            </a>
                        </li>
                        <li class="nav-item ">
                            <a href="@Url.Action("Delete", "Database")" class="nav-link @Html.RouteIf("Database", "Delete", "active")">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>حذف محتویات لایه</p>
                            </a>
                        </li>
                    </ul>
                </li>

                <li class="nav-item">
                    <a href="#" class="nav-link @Html.RouteIf("Setting" , "active" )">
                        <i class="nav-icon bi bi-palette text-warning"></i>
                        <p>
                           تنظیمات
                            <i class="nav-arrow bi bi-chevron-right"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <li class="nav-item">
                            <a href="@Url.Action("AppSetting", "Setting")" class="nav-link @Html.RouteIf("Setting", "AppSetting" , "active" )">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>تنظیمات سامانه</p>
                            </a>
                        </li>
                    </ul>
                </li>
                <li class="nav-item menu-open">
                    <a href="#" class="nav-link ">
                        <i class="nav-icon bi bi-clipboard-fill"></i>
                        <p>
                            Layout Options
                            <span class="nav-badge badge text-bg-secondary me-3">6</span>
                            <i class="nav-arrow bi bi-chevron-right"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <li class="nav-item">
                            <a href="../layout/unfixed-sidebar.html" class="nav-link">
                                <i class="nav-icon bi bi-circle"></i>
                                <p>Default Sidebar</p>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
            <!--end::Sidebar Menu-->
        </nav>
    </div>
    <!--end::Sidebar Wrapper-->
</aside> 