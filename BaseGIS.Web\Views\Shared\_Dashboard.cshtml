<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>@ViewBag.Title - BaseGIS Dashboard</title>
    <meta name="description" content="BaseGIS Dashboard">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=no, minimal-ui">
    
    <!-- Call App Mode on ios devices -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    
    <!-- Remove Tap Highlight on iOS -->
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="~/Content/img/favicon/favicon-32x32.png">
    <link rel="mask-icon" href="~/Content/img/favicon/safari-pinned-tab.svg" color="#5bbad5">
    
    <!-- Font CSS -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Vazirmatn:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" media="screen, print" href="~/Content/css/vendors.bundle.css">
    <link rel="stylesheet" media="screen, print" href="~/Content/css/app.bundle.css">
    <link rel="stylesheet" media="screen, print" href="~/Content/css/fa-solid.css">
    <link rel="stylesheet" media="screen, print" href="~/Content/css/fa-brands.css">
    <link rel="stylesheet" media="screen, print" href="~/Content/css/fa-regular.css">
    <link rel="stylesheet" media="screen, print" href="~/Content/css/fa-light.css">
    <link rel="stylesheet" media="screen, print" href="~/Content/css/fa-duotone.css">
    
    <!-- Dashboard specific styles -->
    @RenderSection("styles", required: false)
    
    <style>
        body {
            font-family: 'Vazirmatn', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-wrapper {
            background: transparent;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .page-content {
            background: transparent;
            padding: 20px;
        }
        
        .panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }
        
        .modal-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
        }
        
        .nav-function-top .page-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        
        .nav-function-top .page-content-wrapper {
            margin-top: 60px;
        }
    </style>
</head>

<body class="mod-bg-1 nav-function-top header-function-fixed">
    <!-- Page Wrapper -->
    <div class="page-wrapper">
        <!-- Page Header -->
        <header class="page-header" role="banner">
            <div class="page-logo">
                <a href="/" class="page-logo-link press-scale-down d-flex align-items-center position-relative">
                    <img src="~/Content/img/logo.png" alt="BaseGIS" aria-roledescription="logo" style="height: 40px;">
                    <span class="page-logo-text mr-1">BaseGIS Dashboard</span>
                </a>
            </div>
            
            <!-- Page Header Actions -->
            <div class="hidden-md-down dropdown-icon-menu position-relative">
                <a href="#" class="header-btn btn js-get-date" data-toggle="dropdown" title="تاریخ و زمان">
                    <i class="fal fa-clock"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-animated dropdown-lg">
                    <div class="dropdown-header bg-trans-gradient d-flex justify-content-center align-items-center rounded-top">
                        <h4 class="m-0 text-center color-white">
                            تاریخ و زمان
                            <small class="mb-0 opacity-80">امروز</small>
                        </h4>
                    </div>
                    <div class="p-3 text-center">
                        <div id="js-get-date"></div>
                    </div>
                </div>
            </div>
            
            <!-- User Menu -->
            <div class="ml-auto d-flex">
                <div class="dropdown">
                    <a class="header-icon" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
                        <img src="~/Content/img/demo/avatars/avatar-admin.png" class="profile-image rounded-circle" alt="کاربر">
                    </a>
                    <div class="dropdown-menu dropdown-menu-animated dropdown-lg">
                        <div class="dropdown-header bg-trans-gradient d-flex justify-content-center align-items-center rounded-top">
                            <h4 class="m-0 text-center color-white">
                                @User.Identity?.Name
                                <small class="mb-0 opacity-80">کاربر سیستم</small>
                            </h4>
                        </div>
                        <div class="dropdown-divider m-0"></div>
                        <a class="dropdown-item" href="/Account/Logout">
                            <span>خروج از سیستم</span>
                        </a>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Page Content Wrapper -->
        <div class="page-content-wrapper">
            <!-- Page Content -->
            <div class="page-content">
                @RenderBody()
            </div>
        </div>
    </div>
    
    <!-- Shortcut Menu -->
    @RenderSection("shortcutmenu", required: false)
    
    <!-- Base Scripts -->
    <script src="~/Content/js/vendors.bundle.js"></script>
    <script src="~/Content/js/app.bundle.js"></script>
    
    <!-- Page Scripts -->
    @RenderSection("scripts", required: false)
    
    <script>
        $(document).ready(function() {
            // Initialize date display
            var today = new Date();
            var options = { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                weekday: 'long'
            };
            $('#js-get-date').text(today.toLocaleDateString('fa-IR', options));
            
            // Initialize tooltips
            $('[data-toggle="tooltip"]').tooltip();
            
            // Initialize popovers
            $('[data-toggle="popover"]').popover();
        });
    </script>
</body>
</html>
