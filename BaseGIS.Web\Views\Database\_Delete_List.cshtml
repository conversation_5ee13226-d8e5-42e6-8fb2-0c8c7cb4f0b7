﻿@using BaseGIS.Core.Entities
@{
    List<TableInfo> tableInfos = Model;
    var GroupList = tableInfos.Select(x => x.GroupInfo).Distinct().ToList();
    string id = Context.Request.Query["id"];

    ApplicationRole Role = ViewBag.Role;
    TableAccess Role_TableAccess = null;
    if (Role != null)
    {
        Role_TableAccess = Role.ConvertTable(Role.TableAccess);
    }
}
<link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />

<div class="col-12" id="DeleteList">
    <div class="card border-danger mb-3 samanFont">
        <div class="card-header bg-danger text-white">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="fa fa-trash me-2"></i>
                    <h5 class="card-title samanFont mb-0">انتخاب لایه برای حذف</h5>
                </div>
            </div>
        </div>
        <div class="collapse show" id="layerListContent">
            <div class="card-body">
                <div class="alert alert-warning" role="alert">
                    <i class="fa fa-exclamation-triangle me-2"></i>
                    <strong>هشدار:</strong> عملیات حذف غیرقابل بازگشت است. لطفاً با دقت انتخاب کنید.
                </div>
                <div class="row g-3">
                    <div class="col-lg-10 col-md-12">
                        <select class="form-select select2" id="GList" data-placeholder="یک جدول انتخاب کنید">
                            <option value="0"></option>
                            @foreach (var group in GroupList)
                            {
                                var list = tableInfos.Where(a => a.GroupInfo == group).ToList();
                                if (list.Any())
                                {
                                    var lblG = group != null ? group.AliasName : "بدون گروه‌بندی";
                                    <optgroup label="@lblG">
                                        @foreach (var item in list)
                                        {
                                            bool access = true;
                                            if (Role_TableAccess != null)
                                            {
                                                access = false;
                                                var RoleAccessTable = Role_TableAccess.Tables.Find(a => a.Name == item.Name);
                                                if (RoleAccessTable != null && RoleAccessTable.IsDel)
                                                {
                                                    access = true;
                                                }
                                            }
                                            if (access)
                                            {
                                                string symbol = "fa-table";
                                                if (item.DatasetType.ToLower() == "point") { symbol = "fa-map-marker"; }
                                                else if (item.DatasetType.ToLower() == "polyline") { symbol = "fa-flash"; }
                                                else if (item.DatasetType.ToLower() == "polygon") { symbol = "fa-square-o"; }

                                                if (id != null && int.Parse(id) == item.Id)
                                                {
                                                    <option value="@item.Id" selected data-icon="@symbol">@item.AliasName</option>
                                                }
                                                else
                                                {
                                                    <option value="@item.Id" data-icon="@symbol">@item.AliasName</option>
                                                }
                                            }
                                        }
                                    </optgroup>
                                }
                            }
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-12">
                        <button type="button" onclick="selectLayer();" class="btn btn-outline-primary w-100">
                            تایید
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="~/lib/select2/js/select2.min.js"></script>
<script src="~/lib/select2/js/i18n/fa.js"></script>
<script type="text/javascript">
    $(document).ready(function () {
        $('.select2').select2({
            theme: 'bootstrap-5',
            language: 'fa',
            dir: 'rtl',
            placeholder: 'یک جدول انتخاب کنید',
            allowClear: true,
            width: '100%'
        });
    });

    function selectLayer() {
        var selectedId = $('#GList').val();
        if (selectedId && selectedId !== '0') {
            window.onItemSelected(selectedId); // فراخوانی تابع از ویوی اصلی
        } else {
            $("#_Delete_Query").hide();
        }
    }
</script>





