﻿@using BaseGIS.Core.Entities
@{
    List<TableInfo> tableInfos = Model;
    var GroupList = tableInfos.Select(x => x.GroupInfo).Distinct();
    string id = Context.Request.Query["id"];

    ApplicationRole Role = ViewBag.Role;
    TableAccess Role_TableAccess = null;
    if (Role != null)
    {
        Role_TableAccess = Role.ConvertTable(Role.TableAccess);
    }
}
<div class="col-12" id="InsertList">
    <div class="card border-danger mb-3 samanFont">
        <div class="card-header bg-danger text-white">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h5 class="card-title samanFont mb-0">فهرست لایه ها</h5>
                </div>
            </div>
        </div>

        <div class="collapse show" id="layerListContent">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-lg-10 col-md-12">
                        <select class="chosen-select form-select" id="GList" data-placeholder="یک جدول انتخاب کنید">
                            <option value="0"></option>
                            @foreach (var group in GroupList)
                            {
                                var list = tableInfos.Where(a => a.GroupInfo == group);
                                if (list.Count() > 0)
                                {
                                    var lblG = "";
                                    if (group != null)
                                    {
                                        lblG = group.AliasName;
                                    }
                                    else
                                    {
                                        lblG = "بدون گروه بندی";
                                    }
                                    <optgroup label="@lblG">
                                        @foreach (var item in list)
                                        {
                                            bool access = true;
                                            if (Role_TableAccess != null)
                                            {
                                                access = false;
                                                var RoleAccessTable = Role_TableAccess.Tables.Find(a => a.Name == item.Name);
                                                if (RoleAccessTable != null && RoleAccessTable.IsDel)
                                                {
                                                    access = true;
                                                }
                                            }
                                            if (access)
                                            {
                                                if (id != null && int.Parse(id) == item.Id)
                                                {
                                                    <option value="@item.Id" selected>@item.AliasName</option>
                                                }
                                                else
                                                {
                                                    <option value="@item.Id" >@item.AliasName</option>
                                                }
                                            }
                                        }
                                    </optgroup>
                                }
                            }
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-12">
                        <button type="button" onclick="var sel = $('#GList').val(); window.location.href = '?id=' + sel;"
                                class="btn btn-primary w-100">
                            تایید
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function () {
        $('.chosen-select').chosen({ width: '100%', rtl: true, no_results_text: "یافت نشد" });

    });
</script>





