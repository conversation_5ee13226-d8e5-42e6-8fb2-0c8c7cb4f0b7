# قابلیت انتخاب عارضه‌ها بر اساس Geometry

این سند قابلیت جدید انتخاب عارضه‌ها بر اساس geometry در سیستم BaseGIS را شرح می‌دهد.

## 🎯 هدف

ایجاد قابلیت انتخاب عارضه‌ها از لایه‌های مختلف بر اساس geometry ارسال شده (نقطه، خط، چندضلعی، مستطیل و غیره).

## 📋 اجزای پیاده‌سازی شده

### 1. **Backend API (RESTController.cs)**

#### **متد اصلی:**
```csharp
[HttpPost]
private async Task<IActionResult> SelectByGeometry(RouteValueDictionary routeValues)
```

#### **URL:**
```
POST /rest/services/map/mapserver/selectbygeometry
```

#### **پارامترهای ورودی:**
| پارامتر | نوع | الزامی | توضیح |
|---------|-----|--------|-------|
| `geometry` | string | ✅ | Geometry به فرمت WKT، GeoJSON یا coordinates |
| `layers` | string | ✅ | لیست لایه‌ها (جدا شده با کاما) |
| `spatialRel` | string | ❌ | نوع رابطه مکانی (پیش‌فرض: intersects) |
| `returnGeometry` | boolean | ❌ | بازگشت geometry عارضه‌ها (پیش‌فرض: false) |
| `returnIdsOnly` | boolean | ❌ | فقط بازگشت ID ها (پیش‌فرض: false) |
| `maxRecordCount` | number | ❌ | حداکثر تعداد رکورد (پیش‌فرض: 1000) |

#### **انواع رابطه مکانی:**
- `esriSpatialRelIntersects` - تقاطع (پیش‌فرض)
- `esriSpatialRelContains` - شامل
- `esriSpatialRelWithin` - درون
- `esriSpatialRelTouches` - مماس
- `esriSpatialRelOverlaps` - همپوشانی

#### **فرمت‌های پشتیبانی شده برای Geometry:**

##### **1. WKT (Well-Known Text):**
```
POINT(51.3890 35.6892)
POLYGON((51.3 35.6, 51.4 35.6, 51.4 35.7, 51.3 35.7, 51.3 35.6))
LINESTRING(51.3 35.6, 51.4 35.7)
```

##### **2. Bounding Box:**
```
51.3,35.6,51.4,35.7  // minX,minY,maxX,maxY
```

##### **3. ESRI JSON:**
```json
{
  "rings": [[[51.3, 35.6], [51.4, 35.6], [51.4, 35.7], [51.3, 35.7], [51.3, 35.6]]],
  "spatialReference": {"wkid": 4326}
}
```

#### **نمونه پاسخ:**
```json
{
  "success": true,
  "totalCount": 15,
  "layers": [
    {
      "LayerId": 1,
      "LayerName": "Buildings",
      "FeatureCount": 10,
      "Features": [
        {
          "attributes": {
            "ID": 123,
            "Name": "ساختمان A",
            "Area": 500.5
          },
          "geometry": "POLYGON(...)" // اگر returnGeometry=true
        }
      ]
    }
  ],
  "spatialReference": {
    "wkid": 3857,
    "latestWkid": 3857
  }
}
```

### 2. **Frontend JavaScript Functions**

#### **تابع اصلی:**
```javascript
function selectFeaturesByGeometry(geometry, layers, options = {})
```

#### **توابع کمکی:**

##### **انتخاب بر اساس مستطیل:**
```javascript
function selectFeaturesByBounds(bounds, layers, options = {})

// مثال استفاده:
const bounds = map.getBounds();
const layers = ["L000000000001", "L000000000002"];
selectFeaturesByBounds(bounds, layers, {
    returnGeometry: false,
    maxRecordCount: 100
});
```

##### **انتخاب بر اساس نقطه:**
```javascript
function selectFeaturesByPoint(latlng, layers, options = {})

// مثال استفاده:
const point = L.latLng(35.6892, 51.3890);
const layers = ["L000000000001"];
selectFeaturesByPoint(point, layers);
```

##### **انتخاب بر اساس چندضلعی:**
```javascript
function selectFeaturesByPolygon(coordinates, layers, options = {})

// مثال استفاده:
const coords = [[51.3, 35.6], [51.4, 35.6], [51.4, 35.7], [51.3, 35.7], [51.3, 35.6]];
const layers = ["L000000000001"];
selectFeaturesByPolygon(coords, layers);
```

##### **انتخاب بر اساس WKT:**
```javascript
function selectFeaturesByWKT(wktGeometry, layerIds)

// مثال استفاده:
const wkt = "POLYGON((51.3 35.6, 51.4 35.6, 51.4 35.7, 51.3 35.7, 51.3 35.6))";
const layers = ["L000000000001", "L000000000002"];
selectFeaturesByWKT(wkt, layers);
```

## 🚀 نحوه استفاده

### **1. انتخاب در محدوده فعلی نقشه:**
```javascript
function selectFeaturesInCurrentView() {
    const bounds = map.getBounds();
    const visibleLayers = getVisibleLayerIds();
    
    selectFeaturesByBounds(bounds, visibleLayers, {
        returnGeometry: false,
        maxRecordCount: 500
    })
    .then(result => {
        displaySelectionResults(result);
        console.log(`${result.totalCount} عارضه انتخاب شد`);
    })
    .catch(error => {
        console.error('خطا در انتخاب:', error);
    });
}
```

### **2. انتخاب بر اساس کلیک روی نقشه:**
```javascript
map.on('click', function(e) {
    if (isSelectionMode) {
        selectFeaturesAtPoint(e);
    }
});

function selectFeaturesAtPoint(e) {
    const visibleLayers = getVisibleLayerIds();
    
    selectFeaturesByPoint(e.latlng, visibleLayers, {
        returnGeometry: true,
        maxRecordCount: 10
    })
    .then(result => {
        if (result.totalCount > 0) {
            displaySelectionResults(result);
        }
    });
}
```

### **3. انتخاب بر اساس چندضلعی ترسیم شده:**
```javascript
map.on('draw:created', function(e) {
    if (e.layerType === 'polygon') {
        selectFeaturesInDrawnPolygon(e.layer);
    }
});

function selectFeaturesInDrawnPolygon(layer) {
    const coordinates = layer.getLatLngs()[0].map(latlng => [latlng.lng, latlng.lat]);
    coordinates.push(coordinates[0]); // بستن چندضلعی
    
    const visibleLayers = getVisibleLayerIds();
    
    selectFeaturesByPolygon(coordinates, visibleLayers, {
        returnGeometry: false,
        maxRecordCount: 1000
    })
    .then(result => {
        displaySelectionResults(result);
    });
}
```

### **4. انتخاب بر اساس geometry دلخواه:**
```javascript
// انتخاب بر اساس WKT
const wktPolygon = "POLYGON((51.3 35.6, 51.4 35.6, 51.4 35.7, 51.3 35.7, 51.3 35.6))";
const layers = ["L000000000001", "L000000000002"];

selectFeaturesByGeometry(wktPolygon, layers, {
    spatialRel: 'esriSpatialRelIntersects',
    returnGeometry: true,
    maxRecordCount: 500
})
.then(result => {
    console.log(`انتخاب موفق: ${result.totalCount} عارضه`);
    displaySelectionResults(result);
})
.catch(error => {
    console.error('خطا:', error);
});
```

## 🔧 توابع کمکی

### **دریافت لایه‌های قابل مشاهده:**
```javascript
function getVisibleLayerIds() {
    const visibleLayers = [];
    const selectedNodes = $("#tree").fancytree('getTree').getSelectedNodes();
    
    selectedNodes.forEach(node => {
        if (node.key && node.key.startsWith('L') && node.key.length === 12) {
            const layerId = node.key.substring(1);
            if ($("#C" + layerId).is(':checked')) {
                visibleLayers.push(node.key);
            }
        }
    });
    
    return visibleLayers;
}
```

### **نمایش نتایج انتخاب:**
```javascript
function displaySelectionResults(selectionResult) {
    if (!selectionResult.success) {
        console.error('خطا در نتایج انتخاب:', selectionResult.message);
        return;
    }
    
    // ایجاد HTML برای نمایش نتایج
    let html = '<div class="p-3">';
    html += `<h5>نتایج انتخاب (${selectionResult.totalCount} عارضه)</h5>`;
    
    // نمایش اطلاعات هر لایه
    selectionResult.layers.forEach(layer => {
        html += `<div class="card mb-2">`;
        html += `<div class="card-header">${layer.LayerName} (${layer.FeatureCount} عارضه)</div>`;
        // ... نمایش جزئیات عارضه‌ها
        html += `</div>`;
    });
    
    html += '</div>';
    $("#panel-Identify").html(html).show();
}
```

## 📊 مزایای این قابلیت

### **1. انعطاف‌پذیری بالا:**
- پشتیبانی از انواع مختلف geometry
- قابلیت تنظیم نوع رابطه مکانی
- کنترل بر تعداد نتایج و نوع اطلاعات بازگشتی

### **2. عملکرد بهینه:**
- استفاده از کوئری‌های مکانی SQL Server
- محدودیت تعداد نتایج برای جلوگیری از بارگذاری بیش از حد
- پردازش ناهمزمان (Async)

### **3. سازگاری:**
- سازگار با استانداردهای ESRI
- پشتیبانی از فرمت‌های مختلف geometry
- قابلیت ادغام با سایر قابلیت‌های نقشه

### **4. قابلیت توسعه:**
- ساختار ماژولار
- امکان اضافه کردن انواع جدید رابطه مکانی
- پشتیبانی از فرمت‌های جدید geometry

## 🔍 مثال‌های کاربردی

### **1. انتخاب ساختمان‌ها در محدوده:**
```javascript
// انتخاب تمام ساختمان‌ها در یک محدوده مشخص
const bounds = L.latLngBounds([[35.6, 51.3], [35.7, 51.4]]);
selectFeaturesByBounds(bounds, ["L000000000001"], {
    returnGeometry: true
}).then(result => {
    console.log(`${result.totalCount} ساختمان یافت شد`);
});
```

### **2. جستجوی عارضه‌ها اطراف نقطه:**
```javascript
// جستجوی عارضه‌ها در شعاع 100 متری یک نقطه
const center = L.latLng(35.6892, 51.3890);
selectFeaturesByPoint(center, ["L000000000001", "L000000000002"], {
    spatialRel: 'esriSpatialRelWithin',
    maxRecordCount: 50
});
```

### **3. تحلیل مکانی:**
```javascript
// انتخاب عارضه‌هایی که با یک چندضلعی تقاطع دارند
const analysisPolygon = "POLYGON((51.35 35.65, 51.45 35.65, 51.45 35.75, 51.35 35.75, 51.35 35.65))";
selectFeaturesByWKT(analysisPolygon, getVisibleLayerIds())
.then(result => {
    // انجام تحلیل روی نتایج
    analyzeSelectedFeatures(result);
});
```

## 📝 نکات مهم

1. **عملکرد:** برای بهبود عملکرد، از `maxRecordCount` مناسب استفاده کنید
2. **حافظه:** `returnGeometry=true` فقط زمانی استفاده کنید که واقعاً نیاز دارید
3. **خطایابی:** همیشه از `.catch()` برای مدیریت خطاها استفاده کنید
4. **سازگاری:** geometry ها باید در سیستم مختصات صحیح باشند

## 🎉 نتیجه

این قابلیت امکان انتخاب پیشرفته و انعطاف‌پذیر عارضه‌ها را فراهم می‌کند و پایه‌ای قوی برای توسعه قابلیت‌های تحلیل مکانی پیشرفته‌تر محسوب می‌شود.
