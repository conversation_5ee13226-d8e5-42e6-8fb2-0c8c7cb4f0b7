﻿@using BaseGIS.Core.Entities
@using BaseGIS.Web.ViewModels
@{
    string id = Context.Request.Query["id"];
    string tblId = Context.Request.Query["tblId"];
    SymbologyInfo sym = Model;
    int cont = 1;

    var symbologyJson = Newtonsoft.Json.JsonConvert.DeserializeObject<Symbology>(sym.Json);
    var GType = (symbologyJson.GeometryType == GeometryType.Point ? 1 : (symbologyJson.GeometryType == GeometryType.Polyline ? 2 : 3));

     
}
<link href="~/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.css" rel="stylesheet" />
<script src="~/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.min.js"></script> 

<form action="/symbology/index" method="post" id="formSymbology">
    @Html.AntiForgeryToken()
    <input id="ID" name="ID" value="@id" type="hidden" />
    <input id="TblID" name="TblID" value="@tblId" type="hidden" />
    <input id="geomType" name="geomType" value="@symbologyJson.GeometryType.ToString()" type="hidden" />

    <div class="modal-header bg-light">
        <h5 class="modal-title samanFont">نمادگذاری</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>
    <div class="modal-body">
        <div class="row g-3" id="symMainForm">
            <div class="col-md-9 mb-1">
                <label for="symName" class="form-label">نام:</label>
                <input type="text" id="symName" name="symName" value="@(sym.Name)" class="form-control" />
            </div>
            <div class="col-md-3 mb-3">
                <div class="form-check mt-4">
                    <input type="checkbox" class="form-check-input" id="symDefault" name="symDefault" @(sym.IsDefault ? "checked" : "") />
                    <label class="form-check-label" for="symDefault">پیش فرض</label>
                </div>
            </div>

            <div class="col-12 mb-3">
                <label class="form-label me-3">نوع:</label>
                <div class="form-check form-check-inline">
                    <input type="radio" class="form-check-input" id="chSymType1" name="SymType"
                           onchange="if($(this).is(':checked')){ $('#divField').hide();$('#LNumSymType3').hide()}"
                           value="Simple" @(sym.Type == SymbologyType.Simple ? "checked" : "") />
                    <label class="form-check-label" for="chSymType1">ساده</label>
                </div>
                <div class="form-check form-check-inline">
                    <input type="radio" class="form-check-input" id="chSymType2" name="SymType"
                           onchange="if($(this).is(':checked')){ $('#divField').show();$('#LNumSymType3').hide()}"
                           value="Unique" @(sym.Type == SymbologyType.Unique ? "checked" : "") />
                    <label class="form-check-label" for="chSymType2">مقادیر یکتا</label>
                </div>
                <div class="form-check form-check-inline">
                    <input type="radio" class="form-check-input" id="chSymType3" name="SymType"
                           onchange="if($(this).is(':checked')){ $('#divField').show();$('#LNumSymType3').show()}"
                           value="Quantity" @(sym.Type == SymbologyType.Quantity ? "checked" : "") />
                    <label class="form-check-label" for="chSymType3">دسته بندی عددی</label>
                </div>
            </div>

            <div class="col-md-4" id="divField" style="@(sym.Type ==  SymbologyType.Simple ? "display:none" :"")">
                <label for="symField" class="form-label">نام فیلد:</label>
                <select name="symField" id="symField" class="form-select">
                    @foreach (var item in sym.TableInfo.FieldInfos)
                    {
                        bool exist = false;
                        if (sym.FieldName != null && item.Name.ToLower() == sym.FieldName.ToLower())
                        {
                            <option value="@item.Name" selected>@item.AliasName</option>
                        }
                        else{
                            <option value="@item.Name" >@item.AliasName</option>
                        }
                    }
                </select>
            </div>

            <div class="col-md-2" id="LNumSymType3" style="@(sym.Type == SymbologyType.Quantity ? "" :"display:none")">
                <label for="NumSymType3" class="form-label">تعداد دسته:</label>
                <input type="number" class="form-control" id="NumSymType3" value="5">
            </div>

            <div class="col-12 mb-3">
                <button type="button" class="btn btn-success float-end" onclick="setdistinctorquantity();">
                    تایید
                </button>
            </div>
            <div class="col-md-12">
                <div class="table-responsive">
                    <table id="dt_basic_Field" class="table table-striped table-hover align-middle" style="font-size:small;">
                        <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th class="text-center">برچسب</th>
                                <th class="text-center">مقدار</th>
                                <th class="text-center"></th>
                                <th class="text-center" style="display:none">Json</th>
                                <th class="text-center">
                                    <div class="btn-group btn-group-sm">

                                        <button type="button" onclick="symAddRow(@(GType));" class="btn btn-sm btn btn-outline-info" title="اضافه کردن">
                                            <i class="fa fa-plus-circle"></i>
                                        </button>
                                        <button type="button" onclick="symAddRowNull(@(GType));" class="btn btn-sm btn-outline-warning" title="اضافه کردن تعریف نشده">
                                            <i class="fa fa-plus-circle"></i>
                                        </button>
                                    </div>
                                </th>
                                <th class="text-center"></th>
                            </tr>
                        </thead>
                        <tbody id="symBodyTable">
                            @for (int i = 0; i < symbologyJson.Categories.Count; i++)
                            {
                                <tr class="success text-center" id="symtrow@(i)">
                                    @if (symbologyJson.GeometryType == GeometryType.Point)
                                    {

                                        var catobject = ((Newtonsoft.Json.Linq.JObject)(Newtonsoft.Json.JsonConvert.DeserializeObject(sym.Json)))["Categories"][i];
                                        var cat = Newtonsoft.Json.JsonConvert.DeserializeObject<PointCategory>(catobject.ToString());

                                        string json = catobject["Symbol"].ToString();

                                        <td>@(cont++)</td>
                                        <td class="p-0">
                                            <input type="text" id="symlbl@(i)" name="symlbl@(i)" value="@(cat.LegendText)"
                                                   class="form-control form-control-sm" />
                                        </td>
                                        <td class="p-0">
                                            <input type="text" id="symin@(i)" name="symin@(i)" value="@(cat.FilterExpression)"
                                                   class="form-control form-control-sm" dir="ltr" />
                                        </td>
                                        <td>
                                            <canvas id="myCanvas@(i)" width="40" height="25" class="canvas" value="@(json)">
                                                Not Support
                                            </canvas>
                                        </td>
                                        <td style="display:none">
                                            <input type="text" id="symjson@(i)" name="symjson@(i)" value="@(json)" />
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" onclick="DesignForm($('#symjson@(i)').val(),1,'symjson@(i)');"
                                                        class="btn btn-outline-primary" title="ویرایش">
                                                    <i class="fa fa-pencil"></i>
                                                </button>
                                                <button type="button" onclick="$('#symtrow@(i)').remove();"
                                                        class="btn btn-outline-danger" title="حذف">
                                                    <i class="fa fa-times"></i>
                                                </button>
                                            </div>
                                        </td>
                                    }
                                    else if (symbologyJson.GeometryType == GeometryType.Polyline)
                                    {
                                        var catobject = ((Newtonsoft.Json.Linq.JObject)(Newtonsoft.Json.JsonConvert.DeserializeObject(sym.Json)))["Categories"][i];
                                        var cat = Newtonsoft.Json.JsonConvert.DeserializeObject<LineCategory>(catobject.ToString());

                                        string json = catobject["Outline"].ToString();

                                        <td>@(cont++)</td>
                                        <td class="p-0">
                                            <input type="text" id="symlbl@(i)" name="symlbl@(i)" value="@(cat.LegendText)"
                                                   class="form-control form-control-sm" />
                                        </td>
                                        <td class="p-0">
                                            <input type="text" id="symin@(i)" name="symin@(i)" value="@(cat.FilterExpression)"
                                                   class="form-control form-control-sm" dir="ltr" />
                                        </td>
                                        <td>
                                            <canvas id="myCanvas@(i)" width="40" height="25" value="@(json)" class="canvas">
                                                Not Support
                                            </canvas>
                                        </td>
                                        <td style="display:none">
                                            <input type="text" id="symjson@(i)" name="symjson@(i)" value="@(json)" />
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" onclick="DesignForm($('#symjson@(i)').val(),2,'symjson@(i)');"
                                                        class="btn btn-outline-primary" title="ویرایش">
                                                    <i class="fa fa-pencil"></i>
                                                </button>
                                                <button type="button" onclick="$('#symtrow@(i)').remove();"
                                                        class="btn btn-outline-danger" title="حذف">
                                                    <i class="fa fa-times"></i>
                                                </button>
                                            </div>
                                        </td>
                                    }
                                    else if (symbologyJson.GeometryType == GeometryType.Polygon)
                                    {

                                        var catobject = ((Newtonsoft.Json.Linq.JObject)(Newtonsoft.Json.JsonConvert.DeserializeObject(sym.Json)))["Categories"][i];
                                        var cat = Newtonsoft.Json.JsonConvert.DeserializeObject<PolygonCategory>(catobject.ToString());

                                        string json = catobject["Pattern"].ToString();

                                        <td>@(cont++)</td>
                                        <td class="p-0">
                                            <input type="text" id="symlbl@(i)" name="symlbl@(i)" value="@(cat.LegendText)"
                                                   class="form-control form-control-sm" />
                                        </td>
                                        <td class="p-0">
                                            <input type="text" id="symin@(i)" name="symin@(i)" value="@(cat.FilterExpression)"
                                                   class="form-control form-control-sm" dir="ltr" />
                                        </td>
                                        <td style="display:none">
                                            <input type="text" id="symjson@(i)" name="symjson@(i)" value="@(json)" />
                                        </td>
                                        <td>
                                            <canvas id="myCanvas@(i)" width="40" height="25" class="canvas" value="@(json)">
                                                Not Support
                                            </canvas>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" onclick="DesignForm($('#symjson@(i)').val(),3,'symjson@(i)');"
                                                        class="btn btn-outline-primary" title="ویرایش">
                                                    <i class="fa fa-pencil"></i>
                                                </button>
                                                <button type="button" onclick="$('#symtrow@(i)').remove();"
                                                        class="btn btn-outline-danger" title="حذف">
                                                    <i class="fa fa-times"></i>
                                                </button>
                                            </div>
                                        </td>
                                    }
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-secondary up" title="بالا">
                                                <i class="fa fa-arrow-up"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary down" title="پایین">
                                                <i class="fa fa-arrow-down"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-12 mt-3">
                <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">
                    انصراف
                </button>
                <button type="button" class="btn btn-success" onclick="saveFormSym();">
                    ذخیره
                </button>
            </div>
        </div>

        <!-- Point Symbol Design Form -->
        <div class="row g-3" id="symDesignPoint" style="display: none;">
            <input type="hidden" id="symDesignPoint_ID" />
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6 mb-3">
                                <label for="symDesignPoint_PointShape" class="form-label">نوع:</label>
                                <select id="symDesignPoint_PointShape" class="form-select">
                                    <option value="0">لوزی</option>
                                    <option value="1">بیضی</option>
                                    <option value="2">هشت ضلعی</option>
                                    <option value="3">چهار ضلعی</option>
                                    <option value="4">پنج ضلعی</option>
                                    <option value="5">ستاره</option>
                                    <option value="6">مثلث</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="symDesignPoint_Color" class="form-label">رنگ:</label>
                                <select id="symDesignPoint_Color" class="form-select"
                                        onchange="$('#symDesignPoint_Color').css('background-color', $('#symDesignPoint_Color').val());">
                                    @foreach (System.Drawing.KnownColor knownColor in Enum.GetValues(typeof(System.Drawing.KnownColor)))
                                    {
                                        <option value="@knownColor"
                                                style="background-color:@System.Drawing.ColorTranslator.ToHtml(System.Drawing.Color.FromKnownColor(knownColor))">
                                            @knownColor
                                        </option>
                                    }
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="symDesignPoint_Opacity" class="form-label">میزان غلظت:</label>
                                <input type="number" id="symDesignPoint_Opacity" class="form-control"
                                       min="0" max="1" step="0.1" />
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="symDesignPoint_OutlineColor" class="form-label">رنگ حاشیه:</label>
                                <select id="symDesignPoint_OutlineColor" class="form-select"
                                        onchange="$('#symDesignPoint_OutlineColor').css('background-color', $('#symDesignPoint_OutlineColor').val());">
                                    @foreach (System.Drawing.KnownColor knownColor in Enum.GetValues(typeof(System.Drawing.KnownColor)))
                                    {
                                        <option value="@knownColor"
                                                style="background-color:@System.Drawing.ColorTranslator.ToHtml(System.Drawing.Color.FromKnownColor(knownColor))">
                                            @knownColor
                                        </option>
                                    }
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="symDesignPoint_OutlineWidth" class="form-label">اندازه حاشیه:</label>
                                <input type="number" id="symDesignPoint_OutlineWidth" class="form-control" />
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">اندازه:</label>
                                <div class="row g-2">
                                    <div class="col-6">
                                        <input type="number" id="symDesignPoint_SizeX" class="form-control"
                                               placeholder="طول" />
                                    </div>
                                    <div class="col-6">
                                        <input type="number" id="symDesignPoint_SizeY" class="form-control"
                                               placeholder="عرض" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <button type="button" class="btn btn-secondary me-2" onclick="closeDesignForm();">
                        انصراف
                    </button>
                    <button type="button" class="btn btn-success" onclick="saveDesignFormPoint();">
                        ذخیره
                    </button>
                </div>
            </div>
        </div>

        <!-- Line Symbol Design Form -->
        <div class="row g-3" id="symDesignLine" style="display: none;">
            <input type="hidden" id="symDesignLine_ID" />
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6 mb-3">
                                <label for="symDesignLine_DashStyle" class="form-label">نوع:</label>
                                <select id="symDesignLine_DashStyle" class="form-select">
                                    <option value="0">Solid</option>
                                    <option value="1">Dash</option>
                                    <option value="2">Dash Dot</option>
                                    <option value="3">Dash Dot Dot</option>
                                    <option value="4">Dot</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="symDesignLine_Color" class="form-label">رنگ:</label>
                                <select id="symDesignLine_Color" class="form-select"
                                        onchange="$('#symDesignLine_Color').css('background-color', $('#symDesignLine_Color').val());">
                                    @foreach (System.Drawing.KnownColor knownColor in Enum.GetValues(typeof(System.Drawing.KnownColor)))
                                    {
                                        <option value="@knownColor"
                                                style="background-color:@System.Drawing.ColorTranslator.ToHtml(System.Drawing.Color.FromKnownColor(knownColor))">
                                            @knownColor
                                        </option>
                                    }
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="symDesignLine_Opacity" class="form-label">میزان غلظت:</label>
                                <input type="number" id="symDesignLine_Opacity" class="form-control"
                                       min="0" max="1" step="0.1" />
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="symDesignLine_Width" class="form-label">اندازه:</label>
                                <input type="number" id="symDesignLine_Width" class="form-control" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <button type="button" class="btn btn-secondary me-2" onclick="closeDesignForm();">
                        انصراف
                    </button>
                    <button type="button" class="btn btn-success" onclick="saveDesignFormLine();">
                        ذخیره
                    </button>
                </div>
            </div>
        </div>

        <!-- Polygon Symbol Design Form -->
        <div class="row g-3" id="symDesignPolygon" style="display: none;">
            <input type="hidden" id="symDesignPolygon_ID" />
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6 mb-3">
                                <label for="symDesignPolygon_Fill_Color_input" class="form-label">رنگ:</label>
                                <div class="input-group">
                                    <input type="color" id="symDesignPolygon_Fill_Color_picker" class="form-control form-control-color" value="#00AABB">
                                    <input type="text" id="symDesignPolygon_Fill_Color_input" value="#00AABB" class="form-control" dir="ltr" />
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="symDesignPolygon_Opacity" class="form-label">میزان غلظت:</label>
                                <input type="number" id="symDesignPolygon_Opacity" class="form-control"
                                       min="0" max="1" step="0.1" />
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="symDesignPolygon_Outline_DashStyle" class="form-label">نوع حاشیه:</label>
                                <select id="symDesignPolygon_Outline_DashStyle" class="form-select">
                                    <option value="1">Dash</option>
                                    <option value="2">Dash Dot</option>
                                    <option value="3">Dash Dot Dot</option>
                                    <option value="4">Dot</option>
                                    <option value="5">Solid</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="symDesignPolygon_Outline_Color_input" class="form-label">رنگ حاشیه:</label>
                                <div class="input-group">
                                    <input type="color" id="symDesignPolygon_Outline_Color_picker" class="form-control form-control-color" value="#00AABB">
                                    <input type="text" id="symDesignPolygon_Outline_Color_input" value="#00AABB" class="form-control" dir="ltr" />
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="symDesignPolygon_Outline_Opacity" class="form-label">میزان غلظت حاشیه:</label>
                                <input type="number" id="symDesignPolygon_Outline_Opacity" class="form-control"
                                       min="0" max="1" step="0.1" />
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="symDesignPolygon_Outline_Width" class="form-label">اندازه حاشیه:</label>
                                <input type="number" id="symDesignPolygon_Outline_Width" class="form-control" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <button type="button" class="btn btn-secondary me-2" onclick="closeDesignForm();">
                        انصراف
                    </button>
                    <button type="button" class="btn btn-success" onclick="saveDesignFormPolygon();">
                        ذخیره
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

<div class="modal fade" id="ModalFeature" tabindex="-1" aria-labelledby="ModalFeatureLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <!-- Modal content will be loaded here -->
        </div>
    </div>
</div>

@section Scripts {
<script> 

    $(document).ready(function () {        
        //Sync the color input with the text input
        $('#symDesignPolygon_Outline_Color_picker').on('input', function () {
            $('#symDesignPolygon_Outline_Color_input').val($(this).val().toUpperCase());
        });

        $('#symDesignPolygon_Outline_Color_input').on('change', function () {
            $('#symDesignPolygon_Outline_Color_picker').val($(this).val());
        });

        $('#symDesignPolygon_Fill_Color_picker').on('input', function () {
            $('#symDesignPolygon_Fill_Color_input').val($(this).val().toUpperCase());
        });

        $('#symDesignPolygon_Fill_Color_input').on('change', function () {
            $('#symDesignPolygon_Fill_Color_picker').val($(this).val());
        });
         
        var gtype = @(GType);
        if (gtype == 1) {
            saveDesignFormPoint();
        }
        else if (gtype == 2) {
            saveDesignFormLine();
        }
        else{
            saveDesignFormPolygon();
        }
    });

    var trowcurrent = 0;

    function symAddRow(type) {
        trowcurrent = 0;
        var trs = $("#symBodyTable tr");
        if (trs.length > 0)
            trowcurrent = parseInt(trs[trs.length - 1].id.substring(7));

        trowcurrent++;
        var i = trowcurrent;
        var json = createSymbol(type);

        var html = '<tr id="symtrow' + i + '" class="text-center">' +
            '<td>' + (i + 1) + '</td>' +
            '<td class="p-0"><input type="text" id="symlbl' + i + '" name="symlbl' + i + '" value="" class="form-control form-control-sm" /></td>' +
            '<td class="p-0"><input type="text" id="symin' + i + '" name="symin' + i + '" value="" class="form-control form-control-sm" dir="ltr" /></td>' +
            '<td style="display:none"><input type="text" id="symjson' + i + '" name="symjson' + i + '" value="' + json + '" /></td>' +
            '<td><canvas id="myCanvas' + i + '" width="40" height="25" class="canvas" value="' + json + '">Not Support</canvas></td>' +
            '<td>' +
            '<div class="btn-group btn-group-sm">' +
            '<button type="button" onclick="DesignForm($(\'#symjson' + i + '\').val(),' + type + ',\'symjson' + i + '\');" class="btn btn-outline-primary" title="ویرایش"><i class="fa fa-pencil"></i></button>' +
            '<button type="button" onclick="$(\'#symtrow' + i + '\').remove();" class="btn btn-outline-danger" title="حذف"><i class="fa fa-times"></i></button>' +
            '</div>' +
            '</td>' +
            '<td>' +
            '<div class="btn-group btn-group-sm">' +
            '<button type="button" class="btn btn-outline-secondary up" title="بالا">' +
            '<i class="fa fa-arrow-up"></i>' +
            '</button>' +
            '<button type="button" class="btn btn-outline-secondary down" title="پایین">' +
            '<i class="fa fa-arrow-down"></i>' +
            '</button>' +
            '</div>' +
            '</td>' +
            '</tr>';
        $("#symBodyTable").append(html);
        GenerateGraphic();
    }

    function symAddRowNull(type) {
        trowcurrent = 0;
        var trs = $("#symBodyTable tr");
        if (trs.length > 0)
            trowcurrent = parseInt(trs[trs.length - 1].id.substring(7));

        trowcurrent++;
        var i = trowcurrent;
        var json = createSymbol(type);
        var nullvalue = "[" + $("#symField").val() + "] is null";

        var html = '<tr id="symtrow' + i + '" class="text-center">' +
            '<td>' + (i + 1) + '</td>' +
            '<td class="p-0"><input type="text" id="symlbl' + i + '" name="symlbl' + i + '" value="تعریف نشده" class="form-control form-control-sm" /></td>' +
            '<td class="p-0"><input type="text" id="symin' + i + '" name="symin' + i + '" value="' + nullvalue + '" class="form-control form-control-sm" dir="ltr" /></td>' +
            '<td style="display:none"><input type="text" id="symjson' + i + '" name="symjson' + i + '" value="' + json + '" /></td>' +
            '<td><canvas id="myCanvas' + i + '" width="40" height="25" class="canvas" value="' + json + '">Not Support</canvas></td>' +
            '<td>' +
            '<div class="btn-group btn-group-sm">' +
            '<button type="button" onclick="DesignForm($(\'#symjson' + i + '\').val(),' + type + ',\'symjson' + i + '\');" class="btn btn-outline-primary" title="ویرایش"><i class="fa fa-pencil"></i></button>' +
            '<button type="button" onclick="$(\'#symtrow' + i + '\').remove();" class="btn btn-outline-danger" title="حذف"><i class="fa fa-times"></i></button>' +
            '</div>' +
            '</td>' +
            '<td>' +
            '<div class="btn-group btn-group-sm">' +
            '<button type="button" class="btn btn-outline-secondary up" title="بالا">' +
            '<i class="fa fa-arrow-up"></i>' +
            '</button>' +
            '<button type="button" class="btn btn-outline-secondary down" title="پایین">' +
            '<i class="fa fa-arrow-down"></i>' +
            '</button>' +
            '</div>' +
            '</td>' +
            '</tr>';
        $("#symBodyTable").append(html);
        GenerateGraphic();
    }

    function setSimple(type) {
        $("#symBodyTable").html("");
        symAddRow(type);
    }

    function setDistinct(tableId, fieldName, type) {
        $("#symBodyTable").html("");
        $.ajax({
            type: 'Get',
            url: "../Symbology/GetDistinict",
            data: { "tableId": tableId, "fieldName": fieldName },
            dataType: 'json',
            success: function (response) {
                 var data = response.data;
                var html = "";
                for (var i = 0; i < data.length; i++) {
                    var json = createSymbol(type);
                    html += '<tr  id="symtrow' + i + '"> <td style="padding-left: 0px; padding-right: 0px; ">' + (i + 1) + '</td>  <td><input type="text" id="symlbl' + i + '" name="symlbl' + i + '" value="' + data[i] + '"  style="width:100%"/></td> <td style="padding-left: 0px; padding-right: 0px; "><input type="text" id="symin' + i + '" name="symin' + i + '" value="[' + $('#symField').val() + ']=\'' + data[i] + '\'" style="width:100%;"  dir="ltr"/></td> <td style="display:none"><input type="text" id="symjson' + i + '" name="symjson' + i + '" value="' + json + '" /></td><td ><canvas id="myCanvas' + i + '" width="40" height="25" style="" value="' + json + '" class="canvas"> Not Support</canvas></td> <td> <a href="javascript:void(0);" onclick="DesignForm($(\'#symjson' + i + '\').val(),' + type + ',\'symjson' + i + '\');" class="btn btn-xs btn-outline-primary" title="ویرایش"><i class="fa fa-pencil"></i></a> <a href="javascript:void(0);" onclick="$(\'#symtrow' + i + '\').remove();" class="btn btn-xs btn-outline-danger" data-original-title="حذف" title="حذف"><i class="fa fa-times"></i></a>  </td>'
                        + '<td><div class="btn-group btn-group-sm"> <a href="javascript:void(0);" class="btn btn-xs btn-outline-secondary up"><i class="fa fa-arrow-up"></i></a>  <a href="javascript:void(0);"  class="btn btn-xs btn-outline-secondary down"><i class="fa fa-arrow-down"></i></a></div> </td></tr>';
                }
                $("#symBodyTable").html(html);
                GenerateGraphic();
            },
            error: function (data) {
                $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
            }
        });
    }

    function setQuantity(tableId,fieldName,type,num)
    {
        $("#symBodyTable").html("");
        $.ajax({
            type: 'Get',
            url: "../Symbology/GetQuantity",
            data: { "tableId": tableId, "fieldName": fieldName ,"Num":num },
            dataType: 'json',
                success: function (response) {
                      var data = response.data;
                var html = "";
                for (var i = 0; i < data.length; i++) {
                    var json = createSymbol(type);
                    html += '<tr  id="symtrow' + i + '"> <td>' + (i + 1) + '</td>  <td style="padding-left: 0px; padding-right: 0px; "><input type="text" id="symlbl' + i + '" name="symlbl' + i + '" value="' + data[i] + '"  style="width:100%"/></td> <td style="padding-left: 0px; padding-right: 0px; "><input type="text" id="symin' + i + '" name="symin' + i + '" value="' + data[i] + '" style="width:100%;"  dir="ltr"/></td> <td style="display:none"><input type="text" id="symjson' + i + '" name="symjson' + i + '" value="' + json + '" /></td> <td ><canvas id="myCanvas' + i + '" width="40" height="25" style="" value="' + json + '" class="canvas"> Not Support</canvas></td><td> <a href="javascript:void(0);" onclick="DesignForm($(\'#symjson' + i + '\').val(),' + type + ',\'symjson' + i + '\');" class="btn btn-xs btn-outline-primary" title="ویرایش"><i class="fa fa-pencil"></i></a> <a href="javascript:void(0);" onclick="$(\'#symtrow' + i + '\').remove();" class="btn btn-xs btn-outline-danger" data-original-title="حذف" title="حذف"><i class="fa fa-times"></i></a>  </td>  '
                        + '<td><div class="btn-group btn-group-sm"> <a href="javascript:void(0);" class="btn btn-xs btn-outline-secondary up"><i class="fa fa-arrow-up"></i></a>  <a href="javascript:void(0);"  class="btn btn-xs btn-outline-secondary down"><i class="fa fa-arrow-down"></i></a></div> </td></tr>';
                }
                $("#symBodyTable").html(html);
                GenerateGraphic();
            },
            error: function (data) {
                $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
            }
        });
    }

    function createSymbol(type) {
        var back =  ["AliceBlue","Blue","BlueViolet","Brown","Gold","Gray","GreenYellow","Green","Magenta","Pink","Purple","Red",  "Yellow", "Orange"];//["aliceblue","lightsalmon","antiquewhite","lightseagreen","aqua","lightskyblue","aquamarine","lightslategray","azure","lightsteelblue","beige","lightyellow","bisque","lime","black","limegreen","blanchedalmond","linen","blue","magenta","blueviolet","maroon","brown","mediumaquamarine","burlywood","mediumblue","cadetblue","mediumorchid","chartreuse","mediumpurple","chocolate","mediumseagreen","coral","mediumslateblue","cornflowerblue","mediumspringgreen","cornsilk","mediumturquoise","crimson","mediumvioletred","cyan","midnightblue","darkblue","mintcream","darkcyan","mistyrose","darkgoldenrod","moccasin","darkgray","navajowhite","darkgreen","navy","darkkhaki","oldlace","darkmagenta","olive","darkolivegreen","olivedrab","darkorange","orange","darkorchid","orangered","darkred","orchid","darksalmon","palegoldenrod","darkseagreen","palegreen","darkslateblue","paleturquoise","darkslategray","palevioletred","darkturquoise","papayawhip","darkviolet","peachpuff","deeppink","peru","deepskyblue","pink","dimgray","plum","dodgerblue","powderblue","firebrick","purple","floralwhite","red","forestgreen","rosybrown","fuchsia","royalblue","gainsboro","saddlebrown","ghostwhite","salmon","gold","sandybrown","goldenrod","seagreen","gray","seashell","green","sienna","greenyellow","silver","honeydew","skyblue","hotpink","slateblue","indianred","slategray","indigo","snow","ivory","springgreen","khaki","steelblue","lavender","tan","lavenderblush","teal","lawngreen","thistle","lemonchiffon","tomato","lightblue","turquoise","lightcoral","violet","lightcyan","wheat","lightgoldenrodyellow","white","lightgreen","whitesmoke","lightgrey","yellow","lightpink","yellowgreen" ];//
        if (type == 1) {
            var color = back[Math.floor(Math.random() * back.length)];

            var pt = {};
            pt.Angle = 0;
            pt.Color = color;
            pt.Offset = "0, 0";
            pt.Opacity = 1;
            pt.OutlineColor = "Black";
            pt.OutlineOpacity = 1;
            pt.OutlineWidth = 1;
            pt.PointShape = randomNumberFromRange(0, 6);
            pt.Size = "10,10";
            pt.SymbolType = 3;
            pt.UseOutline = true;
            pt.XmlColor = color;
            pt.XmlOutlineColor = "Black";
            var jsn = JSON.stringify(pt).replace(/"/g, '\'');
            return jsn;
        }
        else if (type == 2) {
            var color = back[Math.floor(Math.random() * back.length)];

            var line = {};

            line.Color = color;
            line.DashStyle = 0;
            line.Opacity = 1;
            line.Width = 1;
            var jsn = JSON.stringify(line).replace(/"/g, '\'');
            return jsn;
        }
        else if (type == 3) {
            var color1 = back[Math.floor(Math.random() * back.length)];
            var color2 = back[Math.floor(Math.random() * back.length)];

            var poly = {};
            poly.Outline = {};
            poly.FillColor = color1;
            poly.Opacity = 1;
            poly.Outline.Color = "Black";
            poly.Outline.DashStyle =5;// randomNumberFromRange(1, 5);
            poly.Outline.Opacity = 1;
            poly.Outline.Width = 1;
            poly.PatternType = 4;
            poly.PatType = 0;
            poly.UseOutline = true;
            var jsn = JSON.stringify(poly).replace(/"/g, '\'');

            return jsn;
        }
    }

    function randomNumberFromRange(min, max) {
        return Math.floor(Math.random() * (max - min + 1) + min);
    }

    function DesignForm(json, type, id) {
        $("#symDesignPoint").hide();
        $("#symDesignLine").hide();
        $("#symDesignPolygon").hide();

        if (type == 1) {
            $("#symDesignPoint_ID").val(id);
            $("#symMainForm").hide();
            $("#symDesignPoint").show();
            var jsn = JSON.parse(JSON.parse(JSON.stringify(json.replace(/\'/g,"\""))));

            $("#symDesignPoint_PointShape").val(jsn.PointShape);
            $("#symDesignPoint_Color").val(jsn.Color);
            $("#symDesignPoint_Opacity").val(jsn.Opacity);
            $("#symDesignPoint_OutlineColor").val(jsn.OutlineColor);
            $("#symDesignPoint_OutlineWidth").val(jsn.OutlineWidth);
            var size = jsn.Size.split(',');
            $("#symDesignPoint_SizeX").val(size[0].trim());
            $("#symDesignPoint_SizeY").val(size[1].trim());
        }
        else if (type == 2) {
            $("#symDesignLine_ID").val(id);
            $("#symMainForm").hide();
            $("#symDesignLine").show();
            var line = JSON.parse(JSON.parse(JSON.stringify(json.replace(/\'/g,"\""))));

            $("#symDesignLine_DashStyle").val(line.DashStyle);
            $("#symDesignLine_Color").val(line.Color);
            $("#symDesignLine_Opacity").val(line.Opacity);
            $("#symDesignLine_Width").val(line.Width);
        }
        else if (type == 3) {
            $("#symDesignPolygon_ID").val(id);
            $("#symMainForm").hide();
            $("#symDesignPolygon").show();
            var poly = JSON.parse(JSON.parse(JSON.stringify(json.replace(/\'/g,"\""))));

            $("#symDesignPolygon_Outline_DashStyle").val(poly.Outline.DashStyle);
            $("#symDesignPolygon_Outline_Color_input").val(poly.Outline.Color);
            $('#symDesignPolygon_Outline_Color_picker').val(poly.Outline.Color);
            $("#symDesignPolygon_Outline_Opacity").val(poly.Outline.Opacity);
            $("#symDesignPolygon_Outline_Width").val(poly.Outline.Width);
            $("#symDesignPolygon_Fill_Color_input").val(poly.FillColor);
            $("#symDesignPolygon_Fill_Color_picker").val(poly.FillColor);

            $("#symDesignPolygon_Opacity").val(poly.Opacity);
        }
    }
    function closeDesignForm() {
        $("#symMainForm").show();
        $("#symDesignPoint").hide();
        $("#symDesignLine").hide();
        $("#symDesignPolygon").hide();
    }

    function saveDesignFormPoint() {
        var jsonID = $("#symDesignPoint_ID").val();
        var pt = {};
        pt.Angle = 0;
        pt.Color = $("#symDesignPoint_Color").val();
        pt.Offset = "0, 0";
        pt.Opacity = ($("#symDesignPoint_Opacity").val());
        pt.OutlineColor = $("#symDesignPoint_OutlineColor").val();
        pt.OutlineOpacity = 1;
        pt.OutlineWidth = ($("#symDesignPoint_OutlineWidth").val());
        pt.PointShape = parseInt($("#symDesignPoint_PointShape").val());
        pt.Size = $("#symDesignPoint_SizeX").val() + ", " + $("#symDesignPoint_SizeY").val();
        pt.SymbolType = 3;
        pt.UseOutline = true;
        pt.XmlColor = $("#symDesignPoint_Color").val();
        pt.XmlOutlineColor = $("#symDesignPoint_OutlineColor").val();

        $("#" + jsonID).val(JSON.stringify(pt));
        var indexID= jsonID.replace("symjson","");
        $("#myCanvas"+indexID).attr("value",JSON.stringify(pt));
        GenerateGraphic();
        closeDesignForm();
    }
    function saveDesignFormLine() {
        var jsonID = $("#symDesignLine_ID").val();
        var line = {};
        line.DashStyle = parseInt($("#symDesignLine_DashStyle").val());;
        line.Color = $("#symDesignLine_Color").val();
        line.Opacity = $("#symDesignLine_Opacity").val();
        line.Width = $("#symDesignLine_Width").val();

        $("#" + jsonID).val(JSON.stringify(line));
        var indexID= jsonID.replace("symjson","");
        $("#myCanvas"+indexID).attr("value",JSON.stringify(line));
        GenerateGraphic();
        closeDesignForm();
    }

    function saveDesignFormPolygon() {
        var jsonID = $("#symDesignPolygon_ID").val();
        var poly = {};
        poly.Outline = {};
        poly.Outline.DashStyle = parseInt($("#symDesignPolygon_Outline_DashStyle").val());;
        poly.Outline.Color = $("#symDesignPolygon_Outline_Color_input").val();
        poly.Outline.Opacity = $("#symDesignPolygon_Outline_Opacity").val();
        poly.Outline.Width = $("#symDesignPolygon_Outline_Width").val();
        poly.FillColor = $("#symDesignPolygon_Fill_Color_input").val();
        poly.Opacity = $("#symDesignPolygon_Opacity").val();
        poly.PatternType = 4;
        poly.PatType = 0;
        poly.UseOutline = true;

        $("#" + jsonID).val(JSON.stringify(poly));
        var indexID= jsonID.replace("symjson","");
        $("#myCanvas"+indexID).attr("value",JSON.stringify(poly));
        GenerateGraphic();
        closeDesignForm();
    }

    function saveFormSym() {
        var form = $("#formSymbology").serialize();
        $.ajax({
            type: 'POST',
            url: "../Symbology/_Save",
            data: form,
            dataType: 'json',
            success: function (data) {
                if (data.success)
                    $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "green", iconSmall: "fa fa-thumbs-up bounce animated", timeout: 4000 });
                else
                    $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
            },
            error: function (data) {
                $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
            }
        });
    }

    function setdistinctorquantity(){
        var gtype=@(symbologyJson.GeometryType == GeometryType.Point ? 1 : (symbologyJson.GeometryType == GeometryType.Polyline ? 2 : 3));
        var chk1=$('#chSymType1').prop('checked');
        var chk2=$('#chSymType2').prop('checked');
        var chk3=$('#chSymType3').prop('checked');

        if(chk1)
            setSimple(gtype);
        else if(chk2)
            setDistinct(@tblId, $('#symField').val(),gtype );
        else if(chk3)
        {
            var val=$("#NumSymType3").val();
            setQuantity(@tblId, $('#symField').val(),gtype ,val);
        }
    }

    function GenerateGraphic()
    {
        $('.canvas').each(function (index, item) {
            var it = $(item).attr("value").replace(/\'/g, "\"");
            var id=$(item).attr("id");
            var jsn= JSON.parse(it);
            @if(symbologyJson.GeometryType == GeometryType.Polygon)
            {
                <text>
            CreateRect(id,jsn);
            </text>
            }
            else if(symbologyJson.GeometryType == GeometryType.Polyline)
            {
                <text>
            CreatePath(id,jsn);
            </text>
            }
             else if(symbologyJson.GeometryType == GeometryType.Point)
            {
                <text>
            createPoint(id,jsn);
            </text>
            }
        });
    }
    function CreateRect(id,json)
    {
        var c = document.getElementById(id);
        var ctx = c.getContext("2d");
        ctx.clearRect(0, 0, c.width, c.height);
        clearCanvas(ctx,c);
        // Red rectangle
        ctx.beginPath();
        ctx.rect(3, 3, 30, 15);

        ctx.fillStyle = json.FillColor;
        ctx.globalAlpha = json.Opacity;
        ctx.fill();
        if(json.UseOutline)
        {
            ctx.lineWidth = json.Outline.Width;
            ctx.strokeStyle = json.Outline.Color;
            var dashStyle=json.Outline.DashStyle;
            if(dashStyle == 1){ ctx.setLineDash([5, 2]);}
            else if(dashStyle == 2){ ctx.setLineDash([5, 2,5]);}
            else if(dashStyle == 3){ ctx.setLineDash([5, 2,2]);}
            else if(dashStyle == 4){ ctx.setLineDash([2, 2]);}
            else{ ctx.setLineDash([0, 0]);}

            ctx.stroke();
        }
    }

    function CreatePath(id,json)
    {
        var c=document.getElementById(id);
        var ctx=c.getContext("2d");
        ctx.clearRect(0, 0, c.width, c.height);
        clearCanvas(ctx,c);
        ctx.beginPath();
        ctx.moveTo(0,8);
        ctx.lineTo(30,8);
        ctx.lineWidth = json.Width;
        ctx.globalAlpha = json.Opacity;
        ctx.strokeStyle = json.Color;
        var dashStyle=json.DashStyle;
        if(dashStyle == 1){ ctx.setLineDash([5, 2]);}
        else if(dashStyle == 2){ ctx.setLineDash([5, 2,5]);}
        else if(dashStyle == 3){ ctx.setLineDash([5, 2,2]);}
        else if(dashStyle == 4){ ctx.setLineDash([2, 2]);}
        else{ ctx.setLineDash([0, 0]);}
        ctx.stroke();
    }

    function createPoint(id,json)
    {
        var c=document.getElementById(id);
        var ctx=c.getContext("2d");
        ctx.clearRect(0, 0, c.width, c.height);
        clearCanvas(ctx,c);
        if(json.PointShape == 5)//<option value="5">ستاره </option>
        {
            var length = 5;ctx.beginPath();ctx.translate(10, 10);ctx.rotate((Math.PI * 1 / 10));for (var i = 5; i--;) {ctx.lineTo(0, length);ctx.translate(0, length);ctx.rotate((Math.PI * 2 / 10));ctx.lineTo(0, -length);ctx.translate(0, -length);ctx.rotate(-(Math.PI * 6 / 10));}ctx.lineTo(0, length);ctx.closePath();
        }
        else if(json.PointShape == 1)//   <option value="1">بیضی </option>
        {
            ctx.beginPath();ctx.arc(10,10,7,0,2*Math.PI);ctx.stroke();
        }
        else if(json.PointShape == 0)//<option value="0">لوزی </option>
        {
            ctx.beginPath(); ctx.translate( 10, 0 );ctx.rotate( Math.PI/4);  ctx.rect(3, 3, 10, 10);
        }
        else if(json.PointShape == 3)// <option value="3">پنج ضلعی </option>
        {
            var x=8, y=8, radius=7, sides=5, startAngle=0, anticlockwise=true;            if (sides < 3) return;            var a = (Math.PI * 2)/sides;            a = anticlockwise?-a:a;            ctx.save();            ctx.translate(x,y);            ctx.rotate(startAngle);            ctx.moveTo(radius,0);            for (var i = 1; i < sides; i++) {                ctx.lineTo(radius*Math.cos(a*i),radius*Math.sin(a*i));            }            ctx.closePath();            ctx.restore();
        }
        else if(json.PointShape == 2)// <option value="2">هشت ضلعی </option>
        {
            var x=8, y=8, radius=7, sides=8, startAngle=0, anticlockwise=true;            if (sides < 3) return;            var a = (Math.PI * 2)/sides;            a = anticlockwise?-a:a;            ctx.save();            ctx.translate(x,y);            ctx.rotate(startAngle);            ctx.moveTo(radius,0);            for (var i = 1; i < sides; i++) {                ctx.lineTo(radius*Math.cos(a*i),radius*Math.sin(a*i));            }            ctx.closePath();            ctx.restore();
        }
        else if(json.PointShape == 4)// <option value="4">چهار ضلعی </option>
        {
            var x=8, y=8, radius=7, sides=4, startAngle=0, anticlockwise=true;            if (sides < 3) return;            var a = (Math.PI * 2)/sides;            a = anticlockwise?-a:a;            ctx.save();            ctx.translate(x,y);            ctx.rotate(startAngle);            ctx.moveTo(radius,0);            for (var i = 1; i < sides; i++) {                ctx.lineTo(radius*Math.cos(a*i),radius*Math.sin(a*i));            }            ctx.closePath();            ctx.restore();
        }
        else
        {
            var x=8, y=8, radius=7, sides=3, startAngle=0, anticlockwise=true;            if (sides < 3) return;            var a = (Math.PI * 2)/sides;            a = anticlockwise?-a:a;            ctx.save();            ctx.translate(x,y);            ctx.rotate(startAngle);            ctx.moveTo(radius,0);            for (var i = 1; i < sides; i++) {                ctx.lineTo(radius*Math.cos(a*i),radius*Math.sin(a*i));            }            ctx.closePath();            ctx.restore();
        }
        ctx.lineWidth = json.OutlineWidth;ctx.globalAlpha = json.Opacity;ctx.strokeStyle = json.OutlineColor;ctx.stroke();ctx.fillStyle = json.Color;ctx.fill();
    }

    function clearCanvas(context, canvas) {
        context.clearRect(0, 0, canvas.width, canvas.height);
        var w = canvas.width;
        canvas.width = 1;
        canvas.width = w;
    }

    $('.up,.down').click(function () {
        var row = $(this).parents('tr:first'), $reindex_start;
        if ($(this).is('.up')) {
            row.insertBefore(row.prev());
            $reindex_start=row;
        }
        else {
            $reindex_start=row.next()
            row.insertAfter(  $reindex_start );
        }
        /*var index= $reindex_start.index();
        $reindex_start.nextAll().andSelf().each(function(i){

            $(this).find('.form-label[id^="index"]').attr( 'id', 'index'+  index+i  );
        })
        */
    });

    function toRGBA( c ) {
        var
            can  = document.createElement( 'canvas' ),
            ctx  = can.getContext( '2d' );
        can.width = can.height = 1;
        ctx.fillStyle = c;
        console.log( ctx.fillStyle ); //always css 6 digit hex color string, e.g. '#ffffff'
        ctx.fillRect( 0, 0, 1, 1 ); //paint the canvas
        var
            img  = ctx.getImageData( 0, 0, 1, 1 ),
            data = img.data,
            rgba = {
                r: data[ 0 ], //0-255 red
                g: data[ 1 ], //0-255 green
                b: data[ 2 ], //0-255 blue
                a: data[ 3 ]  //0-255 opacity (0 being transparent, 255 being opaque)
            };
        return rgba;
    };
</script>
}