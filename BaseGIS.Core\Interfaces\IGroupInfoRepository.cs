﻿using BaseGIS.Core.Entities;

namespace BaseGIS.Core.Interfaces
{
    public interface IGroupInfoRepository : IDisposable
    {
        IEnumerable<GroupInfo> GetGroupInfos();
        GroupInfo GetGroupInfoByID(int groupInfoId);
        void InsertGroupInfo(GroupInfo groupInfo);
        void DeleteGroupInfo(int groupInfoID);
        void UpdateGroupInfo(GroupInfo groupInfo);
        void Save();
    }
}
