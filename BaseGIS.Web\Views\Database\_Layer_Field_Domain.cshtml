﻿@using BaseGIS.Core.Entities
@{
    string tblid = Context.Request.Query["tblid"];
    string id = Context.Request.Query["id"];
    FieldInfo fieldInfo = Model;
    int counter = 1;

    var domainInfos = fieldInfo.DomainInfos.OrderBy(d => d.Code).ToList();
}
<form action="/Database/_Layer_Field_Domain" method="post" id="formLayerFieldDomain">
    @Html.AntiForgeryToken()
    <input id="ID" name="ID" value="@id" type="hidden" />
    <input id="TBLID" name="TBLID" value="@tblid" type="hidden" />

    <div class="modal-header bg-light">
        <h5 class="modal-title samanFont">اطلاعات دامنه فیلد</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>
    
    <div class="modal-body">
        <div class="row g-3 mb-3">
            <div class="col-md-6">
                <label for="Code" class="form-label">کد:</label>
                <div class="input-group">
                    <input id="Code" name="Code" class="form-control" type="number" placeholder="کد" value="0">
                    <span class="input-group-text"><i class="fa fa-database"></i></span>
                </div>
            </div>
            
            <div class="col-md-6">
                <label for="Name" class="form-label">نام:</label>
                <div class="input-group">
                    <input id="Name" name="Name" class="form-control" type="text" placeholder="نام" value="">
                    <span class="input-group-text"><i class="fa fa-database"></i></span>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover align-middle mb-0">
                <thead class="table-light">
                    <tr>
                        <th scope="col" style="width: 50px">#</th>
                        <th scope="col" class="text-center">کد</th>
                        <th scope="col" class="text-center">نام</th>
                        <th scope="col" class="text-center" style="width: 80px">عملیات</th>
                    </tr>
                </thead>
                <tbody id="domainsList">
                    @if (domainInfos.Any())
                    {
                        foreach (var item in domainInfos)
                        {
                            <tr class="text-center" id="d__@(item.Id)">
                                <td>@(counter++)</td>
                                <td>@item.Code</td>
                                <td>@item.Name</td>
                                <td>
                                    <button type="button" onclick="frmFieldDomianDel(@item.Id);" 
                                            class="btn btn-sm btn-outline-danger" title="حذف">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </td>
                            </tr>
                        }
                    }
                    else
                    {
                        <tr>
                            <td colspan="4" class="text-center py-3">
                                <div class="text-muted">
                                    <i class="fa fa-info-circle me-1"></i>هیچ مقداری یافت نشد
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            انصراف
        </button>
        <button type="button" class="btn btn-success" onclick="saveFormFieldDomain();">
            ذخیره
        </button>
    </div>
</form>

<script>
    var counter = @(counter);
    
    function saveFormFieldDomain() {
        var form = $("#formLayerFieldDomain").serialize();
        $.ajax({
            type: 'POST',
            url: "../Database/_Layer_Field_Domain",
            data: form,
            dataType: 'json',
            success: function (data) {
                if (data.success) { 
                    // Check if there's a "no data" row and remove it
                    if ($("#domainsList tr td[colspan='4']").length > 0) {
                        $("#domainsList").empty();
                    }                    
                    var html = `
                        <tr id="d__${data.id}" class="text-center">
                            <td>${counter++}</td>
                            <td>${$("#Code").val()}</td>
                            <td>${$("#Name").val()}</td>
                            <td>
                                <button type="button" onclick="frmFieldDomianDel(${data.id});" 
                                        class="btn btn-sm btn-outline-danger" title="حذف">
                                    <i class="fa fa-times"></i>
                                </button>
                            </td>
                        </tr>`;
                    $("#domainsList").append(html);
                    
                    // Clear the form for next entry
                    $("#Code").val("0");
                    $("#Name").val("");
                    $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "green", iconSmall: "fa fa-thumbs-up bounce animated", timeout: 4000 });
                } else
                    $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
            },
            error: function (data) {
                $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
            }
        });
    }

    function frmFieldDomianDel(idField) {
        if (idField) {
            Swal.fire({
                title: 'تأیید حذف',
                html: "<b class='samanFont'>آیا مطمئن به حذف مقدار انتخاب شده هستید؟</b>",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545', // Bootstrap 5 danger color
                cancelButtonColor: '#6c757d', // Bootstrap 5 secondary color
                confirmButtonText: 'حذف',
                cancelButtonText: 'لغو',
                reverseButtons: true,
                customClass: {
                    popup: 'samanFont',
                    title: 'samanFont',
                    htmlContainer: 'samanFont',
                    confirmButton: 'samanFont btn btn-danger',
                    cancelButton: 'samanFont btn btn-secondary'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        type: 'POST',
                        url: "../database/_Layer_Field_DomainDel",
                        data: {
                            id: idField,
                            "__RequestVerificationToken": $("input[name='__RequestVerificationToken']").last().val()
                        },
                        dataType: 'json',
                        success: function (data) {
                            if (data.success) {
                                $("#d__" + idField).remove();
                                
                                // If no items left, show empty message
                                if ($("#domainsList tr").length === 0) {
                                    $("#domainsList").html(`
                                        <tr>
                                            <td colspan="4" class="text-center py-3">
                                                <div class="text-muted">
                                                    <i class="fa fa-info-circle me-1"></i>هیچ مقداری یافت نشد
                                                </div>
                                            </td>
                                        </tr>
                                    `);
                                }
                                Swal.fire({
                                    title: 'موفق',
                                    html: data.responseText,
                                    icon: 'success',
                                    timer: 2000
                                });
                            } else {
                                Swal.fire({
                                    title: 'خطا',
                                    html: data.responseText,
                                    icon: 'error'
                                });
                            }
                        },
                        error: function (data) {
                            Swal.fire({
                                title: 'خطا',
                                html: data.statusText,
                                icon: 'error'
                            });
                        }
                    });
                }
            });
        } else {
            Swal.fire({
                title: 'خطا',
                html: 'لطفا یک مقدار را انتخاب نمایید',
                icon: 'error'
            });
        }
    }
     
</script>








