/**
 * Property Panel Styles
 * استایل‌های پنل خصوصیات
 */

.property-panel {
    position: absolute;
    top: 50px;
    right: 0;
    width: 400px;
    height: calc(100vh - 60px);
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px 0 0 8px;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
    flex-direction: column;
    font-family: 'Vazir', sans-serif;
}

.property-panel.visible {
    display: flex;
}

/* Panel Header */
.panel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 8px 0 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: grab;
    user-select: none;
}

.panel-header:active {
    cursor: grabbing;
}

.panel-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.panel-controls {
    display: flex;
    gap: 4px;
}

.panel-controls .btn {
    padding: 4px 8px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.panel-controls .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Panel Body */
.panel-body {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    background: #f8f9fa;
}

.property-content {
    height: 100%;
}

.no-selection {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #6c757d;
}

.no-selection i {
    margin-bottom: 12px;
    opacity: 0.5;
}

.no-selection p {
    margin: 0;
    text-align: center;
    font-size: 14px;
}

/* Property Sections */
.property-sections {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.property-section {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.section-title {
    background: #f8f9fa;
    padding: 12px 16px;
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-title i {
    color: #6c757d;
}

.section-content {
    padding: 12px 16px;
}

.property-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.property-item:last-child {
    border-bottom: none;
}

.property-label {
    font-weight: 500;
    color: #495057;
    font-size: 13px;
    min-width: 80px;
}

.property-value {
    color: #212529;
    font-size: 13px;
    text-align: left;
    direction: ltr;
    flex: 1;
    margin-right: 8px;
}

.property-value input {
    width: 100%;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
}

/* Panel Footer */
.panel-footer {
    background: white;
    border-top: 1px solid #e9ecef;
    padding: 12px 16px;
}

.panel-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.panel-actions .btn {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

/* Property Tools Toolbar */
.property-tools-toolbar {
    margin-bottom: 16px;
}

.property-tools-toolbar .btn-group {
    width: 100%;
}

.property-tools-toolbar .btn {
    flex: 1;
    padding: 8px 12px;
    font-size: 13px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.property-tools-toolbar .btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

/* Settings Dialog */
.property-settings-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: none;
    justify-content: center;
    align-items: center;
}

.property-settings-dialog.show {
    display: flex;
}

.dialog-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dialog-header {
    background: #f8f9fa;
    padding: 16px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dialog-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.dialog-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.dialog-footer {
    background: #f8f9fa;
    padding: 16px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .property-panel {
        width: 100%;
        right: 0;
        border-radius: 0;
    }
    
    .dialog-content {
        width: 95%;
        margin: 10px;
    }
}

/* Animation */
.property-panel {
    transition: transform 0.3s ease, opacity 0.3s ease;
    transform: translateX(100%);
    opacity: 0;
}

.property-panel.visible {
    transform: translateX(0);
    opacity: 1;
}

/* Scrollbar Styling */
.panel-body::-webkit-scrollbar {
    width: 6px;
}

.panel-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.panel-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.panel-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading State */
.property-panel.loading .panel-body {
    display: flex;
    justify-content: center;
    align-items: center;
}

.property-panel.loading .property-content {
    display: none;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
