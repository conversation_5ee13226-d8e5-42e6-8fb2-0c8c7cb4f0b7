!function(e,a){"function"==typeof define&&define.amd?define(["exports","echarts"],a):"object"==typeof exports&&"string"!=typeof exports.nodeName?a(0,require("echarts/lib/echarts")):a(0,e.echarts)}(this,function(e,a){a.registerLocale("CS",{time:{month:["Leden","Únor","Březen","Duben","Kv<PERSON><PERSON>","Červen","Červenec","Srpen","<PERSON><PERSON>ř<PERSON>","Říjen","Listopad","Prosinec"],monthAbbr:["Led","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON>","<PERSON>v<PERSON>","<PERSON>vn","<PERSON>vc","Srp","<PERSON><PERSON><PERSON>","Říj","Lis","Pro"],dayOfWeek:["Neděle","Pondělí","Úterý","Středa","Čtvrtek","Pátek","Sobot<PERSON>"],dayOfWeekAbbr:["<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","Č<PERSON>","<PERSON><PERSON>","So"]},legend:{selector:{all:"Vše",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Obdélníkový výběr",polygon:"Lasso výběr",lineX:"Horizontální výběr",lineY:"Vertikální výběr",keep:"Ponechat výběr",clear:"Zrušit výběr"}},dataView:{title:"Data",lang:["Data","Zavřít","Obnovit"]},dataZoom:{title:{zoom:"Přiblížit",back:"Oddálit"}},magicType:{title:{line:"Změnit na Spojnicový graf",bar:"Změnit na Sloupcový graf",stack:"Plošný",tiled:"Tile"}},restore:{title:"Obnovit"},saveAsImage:{title:"Uložit jako obrázek",lang:["Obrázek uložte pravým kliknutím"]}},series:{typeNames:{pie:"Výsečový graf",bar:"Sloupcový graf",line:"Spojnicový graf",scatter:"XY bodový graf",effectScatter:"Effect XY bodový graf",radar:"Paprskový graf",tree:"Strom",treemap:"Stromová mapa",boxplot:"Krabicový graf",candlestick:"Burzovní graf",k:"K spojnicový graf",heatmap:"Teplotní mapa",map:"Mapa",parallel:"Rovnoběžné souřadnice",lines:"Spojnicový graf",graph:"Graf vztahů",sankey:"Sankeyův diagram",funnel:"Trychtýř (Funnel)",gauge:"Indikátor",pictorialBar:"Obrázkový sloupcový graf",themeRiver:"Theme River Map",sunburst:"Vícevrstvý prstencový graf"}},aria:{general:{withTitle:'Toto je graf o "{title}"',withoutTitle:"Toto je graf"},series:{single:{prefix:"",withName:"{seriesName} s typem {seriesType}.",withoutName:" s typem {seriesType}."},multiple:{prefix:". Obsahuje {seriesCount} řad.",withName:" Řada {seriesId} je typu {seriesType} repreyentující {seriesName}.",withoutName:" Řada {seriesId} je typu {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"Všechna data jsou: ",partialData:"První {displayCnt} položky jsou: ",withName:"data pro {name} jsou {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}})});