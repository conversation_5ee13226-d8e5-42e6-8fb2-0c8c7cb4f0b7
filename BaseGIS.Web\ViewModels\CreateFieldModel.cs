﻿using System.ComponentModel.DataAnnotations;

namespace BaseGIS.Web.ViewModels
{
    public class CreateFieldViewModel
    {
        public int TableInfoId { get; set; }
        [Required(ErrorMessage = "نام فیلد الزامی است.")]
        public string Name { get; set; }
        [Required(ErrorMessage = "عنوان فیلد الزامی است.")]
        public string AliasName { get; set; }
        public string? UnitName { get; set; } // اختیاری
        [Required(ErrorMessage = "نوع فیلد الزامی است.")]
        public string FieldType { get; set; }
        public int FieldLength { get; set; } // پیش‌فرض 0
        public bool IsRequired { get; set; } // پیش‌فرض false
    }
}
