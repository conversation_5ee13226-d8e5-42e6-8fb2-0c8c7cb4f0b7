﻿using System.ComponentModel.DataAnnotations;

namespace BaseGIS.Web.ViewModels
{
    public class UserViewModel
    {
        [Display(Name = "شناسه")]
        public string Id { get; set; }
        [Display(Name = "نام کاربری")]
        public string UserName { get; set; }
        [Display(Name = "نام کامل")]
        public string FullName { get; set; }
        [Display(Name = "موبایل")]
        public string PhoneNumber { get; set; }
        [Display(Name = "ایمیل")]
        public string Email { get; set; }

        public List<string> Roles { get; set; }

        public bool Enabled { get; set; }
        public bool TwoFactorEnabled { get; set; }
        public string? Image { get; set; }

    }

    public class CreateUserViewModel
    {
        [Display(Name = "نام کاربری")]
        public string UserName { get; set; }
        [Display(Name = "نام کامل")]
        public string FullName { get; set; }
        [Display(Name = "موبایل")]
        public string PhoneNumber { get; set; }
        [Display(Name = "ایمیل")]
        public string Email { get; set; }
        [Display(Name = "رمز عبور")]
        public string Password { get; set; }

        public string[] SelectedRoles { get; set; }

        public bool Enabled { get; set; } = true;
        public bool TwoFactorEnabled { get; set; }
        public string? Image { get; set; }
    }

    public class EditUserViewModel
    {
        [Display(Name = "شناسه")]
        public string Id { get; set; }
        [Display(Name = "نام کاربری")]
        public string UserName { get; set; }
        [Display(Name = "نام کامل")]
        public string FullName { get; set; }
        [Display(Name = "موبایل")]
        public string PhoneNumber { get; set; }
        [Display(Name = "ایمیل")]
        public string Email { get; set; }

        public IEnumerable<string> SelectedRoles { get; set; }

        public bool Enabled { get; set; }
        public bool TwoFactorEnabled { get; set; }
        public string? Image { get; set; }
    }

    public class ForgotPasswordViewModel
    {
        [Required(ErrorMessage = "لطفاً ایمیل یا نام کاربری خود را وارد کنید.")]
        [Display(Name = "ایمیل یا نام کاربری")]
        public string EmailOrUsername { get; set; }
    }
    public class EnableTwoFactorViewModel
    {
        [Required(ErrorMessage = "لطفاً ایمیل خود را وارد کنید.")]
        [EmailAddress(ErrorMessage = "لطفاً یک ایمیل معتبر وارد کنید.")]
        [Display(Name = "ایمیل")]
        public string Email
        {
            get; set;
        }
    }
    public class TwoFactorLoginViewModel
    {
        [Required(ErrorMessage = "لطفاً کد تأیید را وارد کنید.")]
        [StringLength(6, ErrorMessage = "کد تأیید باید 6 رقمی باشد.", MinimumLength = 6)]
        [Display(Name = "کد تأیید")]
        public string TwoFactorCode { get; set; }

        public bool RememberMe { get; set; }
    }

    public class ResetPasswordViewModel
    {
        [Required(ErrorMessage = "لطفاً ایمیل خود را وارد کنید.")]
        [EmailAddress(ErrorMessage = "لطفاً یک ایمیل معتبر وارد کنید.")]
        [Display(Name = "ایمیل")]
        public string Email { get; set; }

        [Required(ErrorMessage = "لطفاً رمز عبور جدید را وارد کنید.")]
        [StringLength(100, ErrorMessage = "رمز عبور باید حداقل {2} کاراکتر باشد.", MinimumLength = 4)]
        [DataType(DataType.Password)]
        [Display(Name = "رمز عبور جدید")]
        public string Password { get; set; }

        [DataType(DataType.Password)]
        [Display(Name = "تأیید رمز عبور")]
        [Compare("Password", ErrorMessage = "رمز عبور و تأیید آن مطابقت ندارند.")]
        public string ConfirmPassword { get; set; }

        public string Code { get; set; } // توکن بازنشانی
    }
}
