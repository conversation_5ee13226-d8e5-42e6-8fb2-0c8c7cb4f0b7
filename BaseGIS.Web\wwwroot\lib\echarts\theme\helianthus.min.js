!function(e,o){"function"==typeof define&&define.amd?define(["exports","echarts"],o):"object"==typeof exports&&"string"!=typeof exports.nodeName?o(0,require("echarts/lib/echarts")):o(0,e.echarts)}(this,function(e,o){o?o.registerTheme("helianthus",{color:["#44B7D3","#E42B6D","#F4E24E","#FE9616","#8AED35","#ff69b4","#ba55d3","#cd5c5c","#ffa500","#40e0d0","#E95569","#ff6347","#7b68ee","#00fa9a","#ffd700","#6699FF","#ff6666","#3cb371","#b8860b","#30e0e0"],title:{textStyle:{fontWeight:"normal",color:"#8A826D"}},dataRange:{x:"right",y:"center",itemWidth:5,itemHeight:25,color:["#E42B6D","#F9AD96"],text:["High","Low"],textStyle:{color:"#8A826D"}},toolbox:{color:["#E95569","#E95569","#E95569","#E95569"],effectiveColor:"#ff4500",itemGap:8},tooltip:{backgroundColor:"rgba(138,130,109,0.7)",axisPointer:{type:"line",lineStyle:{color:"#6B6455",type:"dashed"},crossStyle:{color:"#A6A299"},shadowStyle:{color:"rgba(200,200,200,0.3)"}}},dataZoom:{dataBackgroundColor:"rgba(130,197,209,0.6)",fillerColor:"rgba(233,84,105,0.1)",handleColor:"rgba(107,99,84,0.8)"},grid:{borderWidth:0},categoryAxis:{axisLine:{lineStyle:{color:"#6B6455"}},splitLine:{show:!1}},valueAxis:{axisLine:{show:!0},splitArea:{show:!1},splitLine:{lineStyle:{color:["#FFF"],type:"dashed"}}},polar:{axisLine:{lineStyle:{color:"#ddd"}},splitArea:{show:!0,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(200,200,200,0.2)"]}},splitLine:{lineStyle:{color:"#ddd"}}},timeline:{lineStyle:{color:"#6B6455"},controlStyle:{color:"#6B6455",borderColor:"#6B6455"}},line:{smooth:!0,symbol:"emptyCircle",symbolSize:3},candlestick:{itemStyle:{color:"#e42B6d",color0:"#44B7d3"},lineStyle:{width:1,color:"#e42B6d",color0:"#44B7d3"},areaStyle:{color:"#fe994e",color0:"#e42B6d"}},map:{itemStyle:{color:"#6b6455"},areaStyle:{color:"#ddd"},label:{color:"#e42B6d"}},graph:{itemStyle:{color:"#e42B6d"},linkStyle:{color:"#6b6455"}},chord:{padding:4,itemStyle:{color:"#e42B6d",borderWidth:1,borderColor:"rgba(128, 128, 128, 0.5)"},lineStyle:{color:"rgba(128, 128, 128, 0.5)"},areaStyle:{color:"#6b6455"}},gauge:{axisLine:{lineStyle:{color:[[.2,"#44B7D3"],[.8,"#6B6455"],[1,"#E42B6D"]],width:8}}}}):"undefined"!=typeof console&&console&&console.error&&console.error("ECharts is not Loaded")});