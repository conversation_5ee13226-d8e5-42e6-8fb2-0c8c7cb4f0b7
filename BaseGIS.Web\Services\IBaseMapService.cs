using BaseGIS.Core.Entities;
using BaseGIS.Web.ViewModels;
using BaseGIS.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BaseGIS.Web.Services
{
    /// <summary>
    /// سرویس مدیریت نقشه‌های پایه
    /// </summary>
    public interface IBaseMapService
    {
        /// <summary>
        /// دریافت تمام نقشه‌های پایه فعال
        /// </summary>
        Task<ServiceResult<List<BaseMapViewModel>>> GetActiveBaseMapsAsync(string userId);

        /// <summary>
        /// دریافت نقشه پایه پیش‌فرض کاربر
        /// </summary>
        Task<ServiceResult<BaseMapViewModel>> GetDefaultBaseMapAsync(string userId);

        /// <summary>
        /// تنظیم نقشه پایه پیش‌فرض کاربر
        /// </summary>
        Task<ServiceResult<bool>> SetDefaultBaseMapAsync(string userId, int baseMapId);

        /// <summary>
        /// دریافت اطلاعات نقشه پایه با ID
        /// </summary>
        Task<ServiceResult<BaseMapViewModel>> GetBaseMapByIdAsync(int id, string userId);

        /// <summary>
        /// اعتبارسنجی دسترسی کاربر به نقشه پایه
        /// </summary>
        Task<bool> ValidateBaseMapAccessAsync(int baseMapId, string userId);

        /// <summary>
        /// دریافت تنظیمات JavaScript برای نقشه‌های پایه
        /// </summary>
        Task<ServiceResult<BaseMapConfigViewModel>> GetBaseMapConfigAsync(string userId);
    }

    /// <summary>
    /// پیاده‌سازی سرویس مدیریت نقشه‌های پایه
    /// </summary>
    public class BaseMapService : IBaseMapService
    {
        private readonly ApplicationDbContext _context;
        private readonly ICacheService _cacheService;
        private readonly ILogger<BaseMapService> _logger;
        private readonly IConfiguration _configuration;

        public BaseMapService(
            ApplicationDbContext context,
            ICacheService cacheService,
            ILogger<BaseMapService> logger,
            IConfiguration configuration)
        {
            _context = context;
            _cacheService = cacheService;
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<ServiceResult<List<BaseMapViewModel>>> GetActiveBaseMapsAsync(string userId)
        {
            try
            {
                var cacheKey = $"basemaps:active:{userId}";
                var cached = await _cacheService.GetAsync<List<BaseMapViewModel>>(cacheKey);
                if (cached != null)
                {
                    return ServiceResult<List<BaseMapViewModel>>.Success(cached);
                }

                var baseMaps = await _context.BaseMaps
                    .Where(b => b.User == null || b.User.Id == userId) // Public or user-specific
                    .OrderBy(b => b.Title)
                    .Select(b => new BaseMapViewModel
                    {
                        Id = b.ID,
                        Title = b.Title,
                        Description = b.Des,
                        Url = SanitizeUrl(b.Url),
                        Image = b.Image,
                        WmsLayers = b.WMSLayers,
                        Styles = b.Styles,
                        IsWms = !string.IsNullOrEmpty(b.WMSLayers),
                        DateTime = b.DateTime
                    })
                    .ToListAsync();

                await _cacheService.SetAsync(cacheKey, baseMaps, TimeSpan.FromHours(1));

                return ServiceResult<List<BaseMapViewModel>>.Success(baseMaps);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active base maps for user {UserId}", userId);
                return ServiceResult<List<BaseMapViewModel>>.Failure("خطا در بارگذاری نقشه‌های پایه");
            }
        }

        public async Task<ServiceResult<BaseMapViewModel>> GetDefaultBaseMapAsync(string userId)
        {
            try
            {
                // Default to first available base map or system default
                var baseMaps = await GetActiveBaseMapsAsync(userId);
                if (!baseMaps.IsSuccess || !baseMaps.Data.Any())
                {
                    return ServiceResult<BaseMapViewModel>.Failure("هیچ نقشه پایه‌ای یافت نشد");
                }

                // Return first base map as default (can be enhanced with user preferences)
                return ServiceResult<BaseMapViewModel>.Success(baseMaps.Data.First());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting default base map for user {UserId}", userId);
                return ServiceResult<BaseMapViewModel>.Failure("خطا در بارگذاری نقشه پایه پیش‌فرض");
            }
        }

        public async Task<ServiceResult<bool>> SetDefaultBaseMapAsync(string userId, int baseMapId)
        {
            try
            {
                // Validate access first
                if (!await ValidateBaseMapAccessAsync(baseMapId, userId))
                {
                    return ServiceResult<bool>.Failure("دسترسی غیرمجاز");
                }

                // Here you can implement user preference storage
                // For now, we'll just validate the operation
                
                // Clear cache
                var cacheKey = $"basemaps:active:{userId}";
                await _cacheService.RemoveAsync(cacheKey);

                return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting default base map for user {UserId}", userId);
                return ServiceResult<bool>.Failure("خطا در تنظیم نقشه پایه پیش‌فرض");
            }
        }

        public async Task<ServiceResult<BaseMapViewModel>> GetBaseMapByIdAsync(int id, string userId)
        {
            try
            {
                var baseMap = await _context.BaseMaps
                    .Where(b => b.ID == id && (b.User == null || b.User.Id == userId))
                    .Select(b => new BaseMapViewModel
                    {
                        Id = b.ID,
                        Title = b.Title,
                        Description = b.Des,
                        Url = SanitizeUrl(b.Url),
                        Image = b.Image,
                        WmsLayers = b.WMSLayers,
                        Styles = b.Styles,
                        IsWms = !string.IsNullOrEmpty(b.WMSLayers),
                        DateTime = b.DateTime
                    })
                    .FirstOrDefaultAsync();

                if (baseMap == null)
                {
                    return ServiceResult<BaseMapViewModel>.Failure("نقشه پایه یافت نشد");
                }

                return ServiceResult<BaseMapViewModel>.Success(baseMap);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting base map {Id} for user {UserId}", id, userId);
                return ServiceResult<BaseMapViewModel>.Failure("خطا در بارگذاری نقشه پایه");
            }
        }

        public async Task<bool> ValidateBaseMapAccessAsync(int baseMapId, string userId)
        {
            try
            {
                var hasAccess = await _context.BaseMaps
                    .AnyAsync(b => b.ID == baseMapId && (b.User == null || b.User.Id == userId));

                return hasAccess;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating base map access for user {UserId}", userId);
                return false;
            }
        }

        public async Task<ServiceResult<BaseMapConfigViewModel>> GetBaseMapConfigAsync(string userId)
        {
            try
            {
                var baseMapsResult = await GetActiveBaseMapsAsync(userId);
                if (!baseMapsResult.IsSuccess)
                {
                    return ServiceResult<BaseMapConfigViewModel>.Failure(baseMapsResult.Message);
                }

                var defaultBaseMapResult = await GetDefaultBaseMapAsync(userId);
                var defaultBaseMapId = defaultBaseMapResult.IsSuccess ? defaultBaseMapResult.Data.Id : 0;

                var config = new BaseMapConfigViewModel
                {
                    BaseMaps = baseMapsResult.Data,
                    DefaultBaseMapId = defaultBaseMapId,
                    EsriBaseUrl = _configuration["MapSettings:EsriBaseUrl"] ?? "",
                    DefaultImagePath = _configuration["MapSettings:DefaultBaseMapImage"] ?? "/Content/mapService/img/basemap/0.png"
                };

                return ServiceResult<BaseMapConfigViewModel>.Success(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting base map config for user {UserId}", userId);
                return ServiceResult<BaseMapConfigViewModel>.Failure("خطا در بارگذاری تنظیمات نقشه‌های پایه");
            }
        }

        /// <summary>
        /// پاکسازی و اعتبارسنجی URL
        /// </summary>
        private string SanitizeUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return string.Empty;

            try
            {
                // Basic URL validation and sanitization
                var uri = new Uri(url);
                return uri.ToString();
            }
            catch
            {
                _logger.LogWarning("Invalid URL detected: {Url}", url);
                return string.Empty;
            }
        }
    }
}
