using BaseGIS.Core.Entities;
using BaseGIS.Core.Interfaces;
using BaseGIS.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace BaseGIS.Infrastructure.Repositories
{
    public class SymbologyInfoRepository : ISymbologyInfoRepository
    {
        private readonly ApplicationDbContext _context;

        public SymbologyInfoRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<SymbologyInfo> GetByIdAsync(int id)
        {
            return await _context.SymbologyInfos
                .Include(s => s.TableInfo)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<List<SymbologyInfo>> GetByTableInfoIdAsync(int tableInfoId)
        {
            return await _context.SymbologyInfos
                .Where(s => s.TableInfoId == tableInfoId)
                .Include(s => s.TableInfo)
                .ToListAsync();
        }

        public async Task AddAsync(SymbologyInfo symbologyInfo)
        {
            await _context.SymbologyInfos.AddAsync(symbologyInfo);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(SymbologyInfo symbologyInfo)
        {
            _context.SymbologyInfos.Update(symbologyInfo);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var symbologyInfo = await _context.SymbologyInfos.FindAsync(id);
            if (symbologyInfo != null)
            {
                _context.SymbologyInfos.Remove(symbologyInfo);
                await _context.SaveChangesAsync();
            }
        }
    }
}