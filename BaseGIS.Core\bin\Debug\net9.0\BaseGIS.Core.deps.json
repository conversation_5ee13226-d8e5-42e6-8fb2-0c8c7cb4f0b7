{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"BaseGIS.Core/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.3", "Newtonsoft.Json": "13.0.3"}, "runtime": {"BaseGIS.Core.dll": {}}}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.3": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.3": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.3": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "9.0.3", "Microsoft.Extensions.Identity.Stores": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.325.11220"}}}, "Microsoft.EntityFrameworkCore/9.0.3": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.3", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.3", "Microsoft.Extensions.Caching.Memory": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.325.11202"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.3": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.325.11202"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.3": {}, "Microsoft.EntityFrameworkCore.Relational/9.0.3": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.3", "Microsoft.Extensions.Caching.Memory": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.325.11202"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Caching.Memory/9.0.3": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Identity.Core/9.0.3": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11220"}}}, "Microsoft.Extensions.Identity.Stores/9.0.3": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.3", "Microsoft.Extensions.Identity.Core": "9.0.3", "Microsoft.Extensions.Logging": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11220"}}}, "Microsoft.Extensions.Logging/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Options/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Primitives/9.0.3": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.325.11113"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}}}, "libraries": {"BaseGIS.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-tKXLFMMefKRyDbbVJM+5Gtd1CgPS6vwc9yTLnaStT9+DmkI6iFrEOdKMjZt5q3ijqQVjWew0/EcB9EbEKeP0DA==", "path": "microsoft.aspnetcore.cryptography.internal/9.0.3", "hashPath": "microsoft.aspnetcore.cryptography.internal.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-gkhJ1uMfYnOH7hylbLx4VxeHHIpNWKEMXAZH3BJwUd+pkTGbm0etmL2dzHJLBrlblvTjZy4e9sg8Tpvkfetj7A==", "path": "microsoft.aspnetcore.cryptography.keyderivation/9.0.3", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-qC4KdZVBI+aKRIcTVkepcfs0VDlcXT6yOQshyG4lDcBhWcingOGU87c19pQBqOB8cG9/cGpphet4xCgMBXQZrQ==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/9.0.3", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.9.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ji6fWE8bFWTvqaMue49RSCcAeqlec13hAbitTBZyz/RmYsQDhqM9VkWUoXS1x1mjcsyWjc5EQr2XyuAkwp0eNQ==", "path": "microsoft.entityframeworkcore/9.0.3", "hashPath": "microsoft.entityframeworkcore.9.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HQH7HgwTl6IyasW7t6F2+ihuHLDmf9+8XaS41v8VHN5Z7x5ZMQIiHCbwxme4P5ICAjBG6VsWcjMCoh4fHbEwJg==", "path": "microsoft.entityframeworkcore.abstractions/9.0.3", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-M+iOxejD3HqPV8/KE148wIehHA3cmMF+FgnpiN8CFB7DLokVSKQPPrwtHjcrNYJ/BDEssviEPNok/jtQWxj7xA==", "path": "microsoft.entityframeworkcore.analyzers/9.0.3", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-xsS+5TM7M5f3tCSRaSbzouGCoIgD2zokQxBGXvf9z3DusRztWvT1NNT9XJaY2JoK1qEEDcHah8is6azYmpZhIg==", "path": "microsoft.entityframeworkcore.relational/9.0.3", "hashPath": "microsoft.entityframeworkcore.relational.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-t8b0R6wtqC4o0hJ+oQkLPydw2MMLEoLEpQXCWbzXAm9NBMOngkDZNcvwF6DxbYdL5SlfZJXbYmiOxKZmwHNgNg==", "path": "microsoft.extensions.caching.abstractions/9.0.3", "hashPath": "microsoft.extensions.caching.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-TXggBGDDd6r+J7FV09plXpzGmWcknVyoDsHlY2qcCbcAhmb0eH7Q9IkfIZl54/zEedVTa9jPgiPFTxH9WuCGMQ==", "path": "microsoft.extensions.caching.memory/9.0.3", "hashPath": "microsoft.extensions.caching.memory.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-q5qlbm6GRUrle2ZZxy9aqS/wWoc+mRD3JeP6rcpiJTh5XcemYkplAcJKq8lU11ZfPom5lfbZZfnQvDqcUhqD5Q==", "path": "microsoft.extensions.configuration.abstractions/9.0.3", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-lDbxJpkl6X8KZGpkAxgrrthQ42YeiR0xjPp7KPx+sCPc3ZbpaIbjzd0QQ+9kDdK2RU2DOl3pc6tQyAgEZY3V0A==", "path": "microsoft.extensions.dependencyinjection/9.0.3", "hashPath": "microsoft.extensions.dependencyinjection.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-TfaHPSe39NyL2wxkisRxXK7xvHGZYBZ+dy3r+mqGvnxKgAPdHkMu3QMQZI4pquP6W5FIQBqs8FJpWV8ffCgDqQ==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.3", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-3cmtvVU2ejnXPI9drZNruEygs3utAtF+My9vcFomIN5nCDwVCQv2IJjZntJW0iyn2WM0epIsUGWdy+OfNNET8Q==", "path": "microsoft.extensions.identity.core/9.0.3", "hashPath": "microsoft.extensions.identity.core.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-IlOCMVEXGZ2uKpbtKaAMdq6LEeIc3VXlH0SO2gxGckgI7LFSVGWpzmB0ebrQEeiVyzD4yDKS8UH9Pk0Zgpugfw==", "path": "microsoft.extensions.identity.stores/9.0.3", "hashPath": "microsoft.extensions.identity.stores.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-utIi2R1nm+PCWkvWBf1Ou6LWqg9iLfHU23r8yyU9VCvda4dEs7xbTZSwGa5KuwbpzpgCbHCIuKaFHB3zyFmnGw==", "path": "microsoft.extensions.logging/9.0.3", "hashPath": "microsoft.extensions.logging.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-H/MBMLt9A/69Ux4OrV7oCKt3DcMT04o5SCqDolulzQA66TLFEpYYb4qedMs/uwrLtyHXGuDGWKZse/oa8W9AZw==", "path": "microsoft.extensions.logging.abstractions/9.0.3", "hashPath": "microsoft.extensions.logging.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-xE7MpY70lkw1oiid5y6FbL9dVw8oLfkx8RhSNGN8sSzBlCqGn0SyT3Fqc8tZnDaPIq7Z8R9RTKlS564DS+MV3g==", "path": "microsoft.extensions.options/9.0.3", "hashPath": "microsoft.extensions.options.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-yCCJHvBcRyqapMSNzP+kTc57Eaavq2cr5Tmuil6/XVnipQf5xmskxakSQ1enU6S4+fNg3sJ27WcInV64q24JsA==", "path": "microsoft.extensions.primitives/9.0.3", "hashPath": "microsoft.extensions.primitives.9.0.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}}}