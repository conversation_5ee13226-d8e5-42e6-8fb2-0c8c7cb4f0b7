/**
 * Measurement Tools Component Styles
 * استایل‌های کامپوننت ابزارهای اندازه‌گیری
 */

/* ========================================
   Measurement Toolbar
======================================== */
.measurement-toolbar {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.measurement-toolbar .btn-group {
    display: flex;
    gap: 2px;
}

.measurement-toolbar .btn {
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 4px;
    transition: all 0.2s ease;
    border: 1px solid #dee2e6;
    background: #fff;
    color: #495057;
}

.measurement-toolbar .btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.measurement-toolbar .btn.btn-primary {
    background: #007bff;
    border-color: #007bff;
    color: #fff;
}

.measurement-toolbar .btn.btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
}

.measurement-toolbar .btn.btn-outline-danger:hover {
    background: #dc3545;
    color: #fff;
}

/* ========================================
   Measurement Dialog
======================================== */
.measurement-dialog,
.coordinate-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1050;
    display: none;
    width: 400px;
    max-width: 90vw;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid #dee2e6;
}

.dialog-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.dialog-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.btn-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-close:hover {
    background: #e9ecef;
    color: #495057;
}

.btn-close::before {
    content: "×";
    font-size: 20px;
    line-height: 1;
}

.dialog-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
}

.dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* ========================================
   Measurement Controls
======================================== */
.measurement-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.measurement-tools {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.measurement-tools .btn {
    flex: 1;
    min-width: 100px;
    padding: 10px 15px;
    font-size: 13px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.measurement-tools .btn i {
    margin-right: 5px;
}

.measurement-result {
    margin-top: 15px;
}

.measurement-result .alert {
    margin: 0;
    padding: 12px 15px;
    border-radius: 6px;
    font-size: 14px;
}

.measurement-result .alert strong {
    display: block;
    margin-bottom: 5px;
    color: #495057;
}

/* ========================================
   Coordinate Controls
======================================== */
.coordinate-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.coordinate-input .row {
    margin: 0;
}

.coordinate-input .col-md-6 {
    padding: 0 5px;
}

.coordinate-input .col-md-6:first-child {
    padding-left: 0;
}

.coordinate-input .col-md-6:last-child {
    padding-right: 0;
}

.coordinate-display .alert {
    margin: 0;
    padding: 12px 15px;
    border-radius: 6px;
    font-size: 13px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
}

.coordinate-display .alert strong {
    display: block;
    margin-bottom: 5px;
    color: #333;
}

#currentCoordinates {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
}

/* ========================================
   Form Controls
======================================== */
.form-label {
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 5px;
}

.form-select,
.form-control {
    font-size: 13px;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus,
.form-control:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* ========================================
   Map Markers
======================================== */
.measurement-marker,
.gotoxy-marker {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

.measurement-marker i,
.gotoxy-marker i {
    filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.3));
}

/* ========================================
   Responsive Design
======================================== */
@media (max-width: 768px) {
    .measurement-dialog,
    .coordinate-dialog {
        width: 95vw;
        max-height: 90vh;
    }
    
    .measurement-toolbar {
        flex-wrap: wrap;
        gap: 3px;
    }
    
    .measurement-toolbar .btn {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .measurement-tools {
        flex-direction: column;
    }
    
    .measurement-tools .btn {
        min-width: auto;
    }
    
    .coordinate-input .row {
        flex-direction: column;
    }
    
    .coordinate-input .col-md-6 {
        padding: 0;
        margin-bottom: 10px;
    }
    
    .coordinate-input .col-md-6:last-child {
        margin-bottom: 0;
    }
}

/* ========================================
   Animation Effects
======================================== */
.measurement-dialog,
.coordinate-dialog {
    animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.measurement-toolbar {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ========================================
   Dark Theme Support
======================================== */
@media (prefers-color-scheme: dark) {
    .measurement-toolbar {
        background: rgba(33, 37, 41, 0.9);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .measurement-dialog,
    .coordinate-dialog {
        background: #212529;
        border-color: #495057;
        color: #fff;
    }
    
    .dialog-header,
    .dialog-footer {
        background: #343a40;
        border-color: #495057;
    }
    
    .dialog-header h5 {
        color: #fff;
    }
    
    .form-select,
    .form-control {
        background: #495057;
        border-color: #6c757d;
        color: #fff;
    }
    
    .coordinate-display .alert {
        background: #343a40;
        border-color: #495057;
        color: #adb5bd;
    }
}
