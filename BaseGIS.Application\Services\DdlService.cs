﻿using BaseGIS.Application.DTOs;
using BaseGIS.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using System.Text;

namespace BaseGIS.Core.Services
{
    public class DdlService : IDdlService
    {
        private readonly ApplicationDbContext _dbContext;

        public DdlService(ApplicationDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public void CreateTable(TableCreateRequestDto request)
        {
            var sb = new StringBuilder();
            sb.Append($"CREATE TABLE [{request.TableName}] (");
            var columns = new List<string>();

            foreach (var field in request.Fields)
            {
                var columnDef = GenerateColumnDefinition(field);
                columns.Add(columnDef);
            }

            // افزودن PRIMARY KEY برای ObjectId
            columns.Add("PRIMARY KEY ([ObjectId])");

            sb.Append(string.Join(", ", columns));
            sb.Append(");");

            string sql = sb.ToString();
            try
            {
                _dbContext.Database.ExecuteSqlRaw(sql);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error creating table: {ex.Message}", ex);
            }
        }
        public void DropTable(string tableName)
        {
            string sql = "if exists (select * from INFORMATION_SCHEMA.TABLES where TABLE_NAME = '" + tableName + "' AND TABLE_SCHEMA = 'dbo')    drop table " + tableName + ";";
            try
            {
                _dbContext.Database.ExecuteSqlRaw(sql);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error droping table: {ex.Message}", ex);
            }
        }

        private string GenerateColumnDefinition(FieldCreateRequestDto field)
        {
            // مدیریت ObjectId با IDENTITY
            if (field.Name == "ObjectId" && field.FieldType == "bigint")
            {
                return $"[{field.Name}] BIGINT IDENTITY(1,1) NOT NULL";
            }

            string typeSql = field.FieldType switch
            {
                "int" => "INT",
                "bigint" => "BIGINT",
                "numeric" => "FLOAT",
                "datetime" => "DATETIME2",
                "domain" => "NVARCHAR(255)",
                "multidomain" => "NVARCHAR(255)",
                "nvarchar" => field.FieldLength.HasValue ? $"NVARCHAR({field.FieldLength.Value})" : "NVARCHAR(MAX)",
                "geometry" => "GEOMETRY",
                _ => "NVARCHAR(MAX)"
            };

            string notNull = field.IsRequired ? "NOT NULL" : "NULL";
            return $"[{field.Name}] {typeSql} {notNull}";
        }

        public void AddField(string tableName, FieldCreateRequestDto field)
        {
            var columnDef = GenerateColumnDefinition(field);
            string sql = $"ALTER TABLE [{tableName}] ADD {columnDef};";
            try
            {
                _dbContext.Database.ExecuteSqlRaw(sql);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error adding field: {ex.Message}", ex);
            }
        }

        public void RemoveField(string tableName, string fieldName)
        {
            string sql = $"ALTER TABLE [{tableName}] DROP COLUMN [{fieldName}];";
            try
            {
                _dbContext.Database.ExecuteSqlRaw(sql);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error removing field: {ex.Message}", ex);
            }
        }
    }
}