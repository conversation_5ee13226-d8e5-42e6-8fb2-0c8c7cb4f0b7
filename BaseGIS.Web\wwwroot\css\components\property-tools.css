/**
 * Property Tools Component Styles
 * استایل‌های کامپوننت ابزارهای خصوصیات
 */

/* ========================================
   Property Tools Toolbar
======================================== */
.property-tools-toolbar {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.property-tools-toolbar .btn-group {
    display: flex;
    gap: 2px;
}

.property-tools-toolbar .btn {
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 4px;
    transition: all 0.2s ease;
    border: 1px solid #dee2e6;
    background: #fff;
    color: #495057;
}

.property-tools-toolbar .btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.property-tools-toolbar .btn.active {
    background: #007bff;
    border-color: #007bff;
    color: #fff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

/* ========================================
   Property Panel
======================================== */
.property-panel {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 350px;
    max-width: 90vw;
    max-height: 80vh;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid #dee2e6;
    z-index: 1000;
    display: none;
    animation: slideInRight 0.3s ease-out;
}

.property-panel .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
    cursor: grab;
}

.property-panel .panel-header:active {
    cursor: grabbing;
}

.property-panel .panel-header h5 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.property-panel .panel-controls {
    display: flex;
    gap: 5px;
}

.property-panel .panel-controls .btn {
    padding: 4px 8px;
    font-size: 12px;
    line-height: 1;
}

.property-panel .panel-body {
    padding: 15px;
    max-height: 60vh;
    overflow-y: auto;
}

.property-panel .panel-footer {
    padding: 10px 15px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.property-panel .panel-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

/* ========================================
   Property Content
======================================== */
.property-content {
    font-size: 13px;
}

.no-selection {
    padding: 40px 20px;
}

.no-selection i {
    color: #dee2e6;
}

.no-selection p {
    margin: 0;
    font-size: 13px;
}

.property-sections {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.property-section {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.section-title {
    background: #f8f9fa;
    padding: 8px 12px;
    margin: 0;
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-title i {
    color: #007bff;
    width: 14px;
    text-align: center;
}

.section-content {
    padding: 10px 12px;
}

.property-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 4px 0;
    border-bottom: 1px solid #f1f3f4;
    gap: 10px;
}

.property-item:last-child {
    border-bottom: none;
}

.property-label {
    font-weight: 500;
    color: #6c757d;
    font-size: 11px;
    min-width: 80px;
    flex-shrink: 0;
}

.property-value {
    color: #333;
    font-size: 11px;
    word-break: break-word;
    text-align: left;
    direction: ltr;
}

.property-value input {
    width: 100%;
    font-size: 11px;
    padding: 2px 6px;
    border: 1px solid #ced4da;
    border-radius: 3px;
}

.property-value input:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.25);
}

/* ========================================
   Property Settings Dialog
======================================== */
.property-settings-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1050;
    width: 400px;
    max-width: 90vw;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid #dee2e6;
    animation: fadeInScale 0.3s ease-out;
}

.property-settings-dialog .dialog-content {
    display: flex;
    flex-direction: column;
}

.property-settings-dialog .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.property-settings-dialog .dialog-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.property-settings-dialog .dialog-body {
    padding: 20px;
}

.property-settings-dialog .dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.settings-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.settings-form .form-check {
    margin-bottom: 8px;
}

.settings-form .form-check-input {
    margin-right: 8px;
}

.settings-form .form-check-label {
    font-size: 13px;
    color: #495057;
}

.settings-form .form-label {
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 5px;
}

.settings-form .form-select {
    font-size: 13px;
    padding: 6px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.settings-form .form-select:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* ========================================
   Feature Popup
======================================== */
.feature-popup {
    font-size: 12px;
    line-height: 1.4;
    min-width: 150px;
}

.feature-popup strong {
    display: block;
    margin-bottom: 5px;
    color: #333;
    font-size: 13px;
}

.feature-popup .btn {
    font-size: 11px;
    padding: 4px 8px;
}

/* ========================================
   Responsive Design
======================================== */
@media (max-width: 768px) {
    .property-panel {
        width: 95vw;
        right: 2.5vw;
        max-height: 70vh;
    }
    
    .property-tools-toolbar {
        flex-wrap: wrap;
        gap: 3px;
    }
    
    .property-tools-toolbar .btn {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .property-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .property-label {
        min-width: auto;
    }
    
    .property-value {
        text-align: right;
        direction: rtl;
    }
    
    .property-settings-dialog {
        width: 95vw;
    }
}

@media (max-width: 480px) {
    .property-panel {
        top: 10px;
        right: 5px;
        left: 5px;
        width: auto;
    }
    
    .property-panel .panel-header h5 {
        font-size: 13px;
    }
    
    .section-title {
        font-size: 11px;
        padding: 6px 10px;
    }
    
    .section-content {
        padding: 8px 10px;
    }
}

/* ========================================
   Animation Effects
======================================== */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.property-tools-toolbar {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ========================================
   Dark Theme Support
======================================== */
@media (prefers-color-scheme: dark) {
    .property-tools-toolbar {
        background: rgba(33, 37, 41, 0.9);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .property-panel {
        background: #212529;
        border-color: #495057;
        color: #fff;
    }
    
    .property-panel .panel-header,
    .property-panel .panel-footer {
        background: #343a40;
        border-color: #495057;
    }
    
    .property-panel .panel-header h5 {
        color: #fff;
    }
    
    .property-section {
        border-color: #495057;
    }
    
    .section-title {
        background: #343a40;
        border-color: #495057;
        color: #fff;
    }
    
    .property-item {
        border-color: #495057;
    }
    
    .property-label {
        color: #adb5bd;
    }
    
    .property-value {
        color: #fff;
    }
    
    .property-value input {
        background: #495057;
        border-color: #6c757d;
        color: #fff;
    }
    
    .property-value input:focus {
        background: #495057;
        border-color: #007bff;
        color: #fff;
    }
    
    .property-settings-dialog {
        background: #212529;
        border-color: #495057;
        color: #fff;
    }
    
    .property-settings-dialog .dialog-header,
    .property-settings-dialog .dialog-footer {
        background: #343a40;
        border-color: #495057;
    }
    
    .property-settings-dialog .dialog-header h5 {
        color: #fff;
    }
    
    .settings-form .form-select {
        background: #495057;
        border-color: #6c757d;
        color: #fff;
    }
    
    .settings-form .form-select:focus {
        background: #495057;
        border-color: #007bff;
        color: #fff;
    }
    
    .settings-form .form-check-label,
    .settings-form .form-label {
        color: #adb5bd;
    }
}
