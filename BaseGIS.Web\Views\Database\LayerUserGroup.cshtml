﻿@using BaseGIS.Core.Entities

@{


    string idTbl = Context.Request.Query["id"];
    List<TableInfo> tableInfos = Model;
    TableInfo tableInfo = new TableInfo();

    if (idTbl != null)
    {
        tableInfo = tableInfos.Where(a => a.Id == int.Parse(idTbl)).FirstOrDefault();
    }
}



<!-- MAIN CONTENT -->
<div id="content" class="samanFont">
    <!-- widget grid -->
    <section id="widget-grid" class="">
        <!-- row -->
        <div class="row" id="rowMaster">
            <div id="_Layer_List">
                @Html.Partial("_LayerUserGroup_List", tableInfos)
            </div>
        </div>
    </section>
    <!-- end widget grid -->
</div>
<script>

</script>







