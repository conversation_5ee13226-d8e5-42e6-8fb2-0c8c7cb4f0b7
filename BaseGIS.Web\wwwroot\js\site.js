﻿
function round(num, places) {
    num = Number(num);
    var multiplier = Math.pow(10, places);
    return Math.round((num + 0.00001) * multiplier) / multiplier;
}

function formatCellValue(value, item) {
    let len = 12;
    try {
        if (value && value.length > len) {
            //return value.substring(0, len) + "...";
            return $("<div>").prop("title", value).text(value.substring(0, len) + "...");
        }
        else
            return value;
    } catch (e) {
        console.log(e);
    }
}

function formatNumericCellValue(value, item, arf) {
    try {
        if (value) {
            return numberWithCommas(round(value, 2));
        }
        else
            return value;
    } catch (e) {
        return '';
    }
}
function numberWithCommas(x) {
    var parts = x.toString().split(".");
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    return parts.join(".");
}

// تابع برای نمایش Toast
function showToast(message, type = "success", timeout =5000 ) {
    const toastContainer = document.querySelector(".toast-container");
    const toastId = "toast-" + Date.now(); // شناسه یکتا برای هر Toast

    // انتخاب کلاس بر اساس نوع پیام (موفقیت یا خطا)
    const bgClass = type === "success" ? "bg-success" : "bg-danger";
    const textClass = "text-white";

    // ساخت ساختار Toast
    const toastHtml = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="true" data-bs-delay="${timeout}">
            <div class="toast-header ${bgClass} ${textClass}">
                <strong class="me-auto">${type === "success" ? "موفقیت" : "خطا"}</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    // اضافه کردن Toast به کانتینر
    toastContainer.innerHTML += toastHtml;

    // فعال‌سازی Toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement);
    toast.show();

    // حذف Toast از DOM بعد از بسته شدن
    toastElement.addEventListener("hidden.bs.toast", () => {
        toastElement.remove();
    });
}

function pureArabic4(value) {
    var s = (' ' + value).slice(1);
    s = s.replace(/آ/g, "آ");
    s = s.replace(/ا/g, "ا");
    s = s.replace(/ب/g, "ب");
    s = s.replace(/پ/g, "پ");
    s = s.replace(/ت/g, "ت");
    s = s.replace(/ٹ/g, "ٹ");
    s = s.replace(/ث/g, "ث");
    s = s.replace(/ج/g, "ج");
    s = s.replace(/چ/g, "چ");
    s = s.replace(/خ/g, "خ");
    s = s.replace(/ح/g, "ح");
    s = s.replace(/د/g, "د");
    s = s.replace(/ڈ/g, "ڈ");
    s = s.replace(/ذ/g, "ذ");
    s = s.replace(/ر/g, "ر");
    s = s.replace(/ڑ/g, "ڑ");
    s = s.replace(/ز/g, "ز");
    s = s.replace(/ژ/g, "ژ");
    s = s.replace(/س/g, "س");
    s = s.replace(/ش/g, "ش");
    s = s.replace(/ص/g, "ص");
    s = s.replace(/ض/g, "ض");
    s = s.replace(/ط/g, "ط");
    s = s.replace(/ظ/g, "ظ");
    s = s.replace(/ع/g, "ع");
    s = s.replace(/غ/g, "غ");
    s = s.replace(/ف/g, "ف");
    s = s.replace(/ک/g, "ك");
    s = s.replace(/ق/g, "ق");
    s = s.replace(/گ/g, "گ");
    s = s.replace(/ل/g, "ل");
    s = s.replace(/م/g, "م");
    s = s.replace(/ن/g, "ن");
    s = s.replace(/و/g, "و");
    s = s.replace(/ہ/g, "ه");
    s = s.replace(/ء/g, "ء");
    s = s.replace(/ی/g, "ي");
    s = s.replace(/ئ/g, "ئ");
    s = s.replace(/ے/g, "ے");
    s = s.replace(/ۃ/g, "ة");
    s = s.replace(/ؤ/g, "ؤ");
    s = s.replace(/إ/g, "إ");
    s = s.replace(/أ/g, "أ");
    return s;
}
