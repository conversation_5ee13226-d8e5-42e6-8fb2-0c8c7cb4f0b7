using NetTopologySuite.Geometries;
using NetTopologySuite.IO;
using BaseGIS.Web.Controllers;

namespace BaseGIS.Web.Examples
{
    /// <summary>
    /// نمونه‌هایی از استفاده از متد TransformToUTM بهبودیافته
    /// </summary>
    public class TransformToUTMExample
    {
        private readonly DatabaseController _databaseController;

        public TransformToUTMExample(DatabaseController databaseController)
        {
            _databaseController = databaseController;
        }

        /// <summary>
        /// نمونه 1: تبدیل نقطه از WGS84 به UTM
        /// </summary>
        public void Example1_WGS84ToUTM()
        {
            // ایجاد نقطه در WGS84 (طول و عرض جغرافیایی تهران)
            var geometryFactory = new GeometryFactory();
            var point = geometryFactory.CreatePoint(new Coordinate(51.3890, 35.6892));
            point.SRID = 4326; // WGS84

            // تبدیل به UTM
            var utmPoint = _databaseController.TransformToUTM(point);
            
            Console.WriteLine($"نقطه اصلی (WGS84): {point}");
            Console.WriteLine($"نقطه تبدیل‌شده (UTM): {utmPoint}");
            Console.WriteLine($"SRID خروجی: {utmPoint.SRID}");
        }

        /// <summary>
        /// نمونه 2: تبدیل چندضلعی از Web Mercator به UTM
        /// </summary>
        public void Example2_WebMercatorToUTM()
        {
            var geometryFactory = new GeometryFactory();
            
            // ایجاد چندضلعی در Web Mercator
            var coordinates = new[]
            {
                new Coordinate(5718499.0, 4268076.0), // تهران در Web Mercator
                new Coordinate(5719000.0, 4268076.0),
                new Coordinate(5719000.0, 4268500.0),
                new Coordinate(5718499.0, 4268500.0),
                new Coordinate(5718499.0, 4268076.0)  // بسته کردن چندضلعی
            };
            
            var polygon = geometryFactory.CreatePolygon(coordinates);
            polygon.SRID = 3857; // Web Mercator

            // تبدیل به UTM
            var utmPolygon = _databaseController.TransformToUTM(polygon);
            
            Console.WriteLine($"چندضلعی اصلی (Web Mercator): {polygon}");
            Console.WriteLine($"چندضلعی تبدیل‌شده (UTM): {utmPolygon}");
            Console.WriteLine($"مساحت در UTM: {utmPolygon.Area} متر مربع");
        }

        /// <summary>
        /// نمونه 3: تبدیل خط از UTM زون دیگر به UTM محلی
        /// </summary>
        public void Example3_UTMToUTM()
        {
            var geometryFactory = new GeometryFactory();
            
            // ایجاد خط در UTM Zone 38N
            var coordinates = new[]
            {
                new Coordinate(500000, 3950000),
                new Coordinate(510000, 3960000),
                new Coordinate(520000, 3970000)
            };
            
            var lineString = geometryFactory.CreateLineString(coordinates);
            lineString.SRID = 32638; // UTM Zone 38N

            // تبدیل به UTM محلی (بر اساس مرکز هندسه)
            var utmLineString = _databaseController.TransformToUTM(lineString);
            
            Console.WriteLine($"خط اصلی (UTM Zone 38N): {lineString}");
            Console.WriteLine($"خط تبدیل‌شده (UTM محلی): {utmLineString}");
            Console.WriteLine($"طول در UTM: {utmLineString.Length} متر");
        }

        /// <summary>
        /// نمونه 4: تبدیل هندسه بدون SRID (فرض WGS84)
        /// </summary>
        public void Example4_NoSRID()
        {
            var geometryFactory = new GeometryFactory();
            
            // ایجاد نقطه بدون SRID (فرض می‌شود WGS84 باشد)
            var point = geometryFactory.CreatePoint(new Coordinate(59.6168, 36.2605)); // مشهد
            // SRID تنظیم نشده (0)

            // تبدیل به UTM
            var utmPoint = _databaseController.TransformToUTM(point);
            
            Console.WriteLine($"نقطه اصلی (بدون SRID): {point}");
            Console.WriteLine($"نقطه تبدیل‌شده (UTM): {utmPoint}");
            Console.WriteLine($"زون UTM تشخیص داده شده: {GetUTMZoneFromSRID(utmPoint.SRID)}");
        }

        /// <summary>
        /// نمونه 5: مدیریت خطا برای مختصات نامعتبر
        /// </summary>
        public void Example5_ErrorHandling()
        {
            var geometryFactory = new GeometryFactory();
            
            try
            {
                // ایجاد نقطه با مختصات نامعتبر
                var invalidPoint = geometryFactory.CreatePoint(new Coordinate(200, 100)); // خارج از محدوده
                invalidPoint.SRID = 4326;

                var utmPoint = _databaseController.TransformToUTM(invalidPoint);
                Console.WriteLine($"تبدیل موفق: {utmPoint}");
            }
            catch (ArgumentException ex)
            {
                Console.WriteLine($"خطای مختصات: {ex.Message}");
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"خطای تبدیل: {ex.Message}");
            }
        }

        /// <summary>
        /// نمونه 6: تبدیل هندسه پیچیده (MultiPolygon)
        /// </summary>
        public void Example6_ComplexGeometry()
        {
            var geometryFactory = new GeometryFactory();
            
            // ایجاد دو چندضلعی جداگانه
            var polygon1 = geometryFactory.CreatePolygon(new[]
            {
                new Coordinate(51.3, 35.6),
                new Coordinate(51.4, 35.6),
                new Coordinate(51.4, 35.7),
                new Coordinate(51.3, 35.7),
                new Coordinate(51.3, 35.6)
            });

            var polygon2 = geometryFactory.CreatePolygon(new[]
            {
                new Coordinate(51.5, 35.8),
                new Coordinate(51.6, 35.8),
                new Coordinate(51.6, 35.9),
                new Coordinate(51.5, 35.9),
                new Coordinate(51.5, 35.8)
            });

            // ایجاد MultiPolygon
            var multiPolygon = geometryFactory.CreateMultiPolygon(new[] { polygon1, polygon2 });
            multiPolygon.SRID = 4326;

            // تبدیل به UTM
            var utmMultiPolygon = _databaseController.TransformToUTM(multiPolygon);
            
            Console.WriteLine($"MultiPolygon اصلی: {multiPolygon.NumGeometries} چندضلعی");
            Console.WriteLine($"MultiPolygon تبدیل‌شده: {utmMultiPolygon.NumGeometries} چندضلعی");
            Console.WriteLine($"مجموع مساحت: {utmMultiPolygon.Area} متر مربع");
        }

        /// <summary>
        /// متد کمکی برای استخراج زون UTM از SRID
        /// </summary>
        private string GetUTMZoneFromSRID(int srid)
        {
            if (srid >= 32601 && srid <= 32660)
            {
                int zone = srid - 32600;
                return $"Zone {zone}N";
            }
            else if (srid >= 32701 && srid <= 32760)
            {
                int zone = srid - 32700;
                return $"Zone {zone}S";
            }
            return "نامشخص";
        }

        /// <summary>
        /// اجرای تمام نمونه‌ها
        /// </summary>
        public void RunAllExamples()
        {
            Console.WriteLine("=== نمونه‌های استفاده از TransformToUTM ===\n");

            Console.WriteLine("1. تبدیل از WGS84 به UTM:");
            Example1_WGS84ToUTM();
            Console.WriteLine();

            Console.WriteLine("2. تبدیل از Web Mercator به UTM:");
            Example2_WebMercatorToUTM();
            Console.WriteLine();

            Console.WriteLine("3. تبدیل از UTM به UTM:");
            Example3_UTMToUTM();
            Console.WriteLine();

            Console.WriteLine("4. تبدیل بدون SRID:");
            Example4_NoSRID();
            Console.WriteLine();

            Console.WriteLine("5. مدیریت خطا:");
            Example5_ErrorHandling();
            Console.WriteLine();

            Console.WriteLine("6. هندسه پیچیده:");
            Example6_ComplexGeometry();
            Console.WriteLine();
        }
    }
}
