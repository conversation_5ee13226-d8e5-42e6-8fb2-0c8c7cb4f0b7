!function(a){"function"==typeof define&&define.amd?define(["exports"],a):"object"==typeof exports&&"string"!=typeof exports.nodeName?a(exports):a({})}(function(a){var t,i={time:{month:["tammikuuta","helmikuuta","maaliskuuta","huhtikuuta","toukokuuta","kesäku<PERSON>","hein<PERSON>ku<PERSON>","elokuuta","syyskuuta","lokakuuta","marraskuuta","joulukuuta"],monthAbbr:["tammik","helmik","maalisk","huhtik","toukok","kesäk","heinäk","elok","syysk","lokak","marrask","jouluk"],dayOfWeek:["sunnuntaina","maanantaina","tiistaina","keskiviikkona","torstaina","perjantaina","lauantaina"],dayOfWeekAbbr:["su","ma","ti","ke","to","pe","la"]},legend:{selector:{all:"Kaikki",inverse:"Kää<PERSON><PERSON>"}},toolbox:{brush:{title:{rect:"Laatikko valinta",polygon:"Lasso valinta",lineX:"Vaakataso valinta",lineY:"Pysty valinta",keep:"Pidä valinta",clear:"Poista valinta"}},dataView:{title:"Data näkymä",lang:["Data näkymä","Sulje","Päivitä"]},dataZoom:{title:{zoom:"Zoomaa",back:"Zoomin nollaus"}},magicType:{title:{line:"Vaihda Viivakaavioon",bar:"Vaihda palkkikaavioon",stack:"Pinoa",tiled:"Erottele"}},restore:{title:"Palauta"},saveAsImage:{title:"Tallenna kuvana",lang:["Paina oikeaa hiirennappia tallentaaksesi kuva"]}}};for(t in i)i.hasOwnProperty(t)&&(a[t]=i[t])});