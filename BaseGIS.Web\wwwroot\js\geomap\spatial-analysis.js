/**
 * Spatial Analysis Tools Component
 * کامپوننت ابزارهای تحلیل مکانی
 */

class SpatialAnalysisTools extends BaseComponent {
    getDefaultOptions() {
        return {
            ...super.getDefaultOptions(),
            bufferUnits: ['meters', 'kilometers', 'feet', 'miles'],
            defaultBufferUnit: 'meters',
            defaultBufferDistance: 100,
            bufferStyle: {
                color: '#ff7800',
                weight: 2,
                opacity: 0.8,
                fillColor: '#ff7800',
                fillOpacity: 0.3
            },
            unionStyle: {
                color: '#0078ff',
                weight: 2,
                opacity: 0.8,
                fillColor: '#0078ff',
                fillOpacity: 0.3
            },
            onBufferCreated: null,
            onUnionCreated: null,
            onAnalysisStart: null,
            onAnalysisEnd: null
        };
    }

    beforeInit() {
        this.currentBufferDistance = this.options.defaultBufferDistance;
        this.currentBufferUnit = this.options.defaultBufferUnit;
        this.map = window.map;
        this.editableLayers = window.editableLayers;
        this.identifiedFeature = null;
    }

    render() {
        this.renderToolbar();
        this.renderDialogs();
    }

    renderToolbar() {
        const toolbar = this.createToolbar();
        this.element.appendChild(toolbar);
    }

    createToolbar() {
        const toolbar = document.createElement('div');
        toolbar.className = 'spatial-analysis-toolbar';
        toolbar.innerHTML = `
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary buffer-btn" 
                        title="ایجاد بافر">
                    <i class="fas fa-circle-notch"></i>
                </button>
                <button type="button" class="btn btn-outline-primary union-btn" 
                        title="ادغام اشکال">
                    <i class="fas fa-object-group"></i>
                </button>
                <button type="button" class="btn btn-outline-secondary intersect-btn" 
                        title="تقاطع اشکال">
                    <i class="fas fa-intersection"></i>
                </button>
                <button type="button" class="btn btn-outline-secondary difference-btn" 
                        title="تفاضل اشکال">
                    <i class="fas fa-minus-circle"></i>
                </button>
            </div>
        `;
        return toolbar;
    }

    renderDialogs() {
        // Buffer Dialog
        const bufferDialog = this.createBufferDialog();
        document.body.appendChild(bufferDialog);

        // Batch Operations Dialog
        const batchDialog = this.createBatchOperationsDialog();
        document.body.appendChild(batchDialog);
    }

    createBufferDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'spatial-analysis-dialog';
        dialog.id = 'bufferDialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-header">
                    <h5>ایجاد بافر</h5>
                    <button type="button" class="btn-close" data-action="close"></button>
                </div>
                <div class="dialog-body">
                    <div class="buffer-controls">
                        <div class="mb-3">
                            <label for="bufferDistance" class="form-label">فاصله بافر:</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="bufferDistance" 
                                       value="${this.options.defaultBufferDistance}" min="0" step="1">
                                <select class="form-select" id="bufferUnit" style="max-width: 100px;">
                                    <option value="meters">متر</option>
                                    <option value="kilometers">کیلومتر</option>
                                    <option value="feet">فوت</option>
                                    <option value="miles">مایل</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="bufferColor" class="form-label">رنگ بافر:</label>
                            <input type="color" class="form-control form-control-color" 
                                   id="bufferColor" value="#ff7800">
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="replaceOriginal">
                                <label class="form-check-label" for="replaceOriginal">
                                    جایگزینی شکل اصلی
                                </label>
                            </div>
                        </div>
                        
                        <div class="buffer-options">
                            <h6>گزینه‌های بافر:</h6>
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary" data-action="buffer-selected">
                                    بافر شکل انتخابی
                                </button>
                                <button type="button" class="btn btn-outline-secondary" data-action="buffer-all">
                                    بافر همه اشکال
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button type="button" class="btn btn-secondary" data-action="close">انصراف</button>
                    <button type="button" class="btn btn-primary" data-action="apply-buffer">اعمال</button>
                </div>
            </div>
        `;
        return dialog;
    }

    createBatchOperationsDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'spatial-analysis-dialog';
        dialog.id = 'batchOperationsDialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-header">
                    <h5>عملیات دسته‌ای</h5>
                    <button type="button" class="btn-close" data-action="close"></button>
                </div>
                <div class="dialog-body">
                    <div class="batch-controls">
                        <div class="mb-3">
                            <label class="form-label">انتخاب عملیات:</label>
                            <div class="operation-buttons">
                                <button type="button" class="btn btn-outline-primary w-100 mb-2" 
                                        data-operation="union">
                                    <i class="fas fa-object-group"></i> ادغام همه اشکال
                                </button>
                                <button type="button" class="btn btn-outline-secondary w-100 mb-2" 
                                        data-operation="buffer-all">
                                    <i class="fas fa-circle-notch"></i> بافر همه اشکال
                                </button>
                                <button type="button" class="btn btn-outline-warning w-100 mb-2" 
                                        data-operation="dissolve">
                                    <i class="fas fa-compress-alt"></i> حذف مرزهای داخلی
                                </button>
                                <button type="button" class="btn btn-outline-danger w-100" 
                                        data-operation="clear-all">
                                    <i class="fas fa-trash"></i> پاک کردن همه
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3" id="batchBufferControls" style="display: none;">
                            <label for="batchBufferDistance" class="form-label">فاصله بافر:</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="batchBufferDistance" 
                                       value="100" min="0" step="1">
                                <select class="form-select" id="batchBufferUnit" style="max-width: 100px;">
                                    <option value="meters">متر</option>
                                    <option value="kilometers">کیلومتر</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="deletePrevious" checked>
                                <label class="form-check-label" for="deletePrevious">
                                    حذف اشکال قبلی
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button type="button" class="btn btn-secondary" data-action="close">بستن</button>
                </div>
            </div>
        `;
        return dialog;
    }

    bindEvents() {
        super.bindEvents();

        // Toolbar events
        this.bindToolbarEvents();
        
        // Dialog events
        this.bindDialogEvents();
    }

    bindToolbarEvents() {
        // Buffer button
        this.find('.buffer-btn')?.addEventListener('click', () => {
            this.showBufferDialog();
        });

        // Union button
        this.find('.union-btn')?.addEventListener('click', () => {
            this.performUnion();
        });

        // Intersect button
        this.find('.intersect-btn')?.addEventListener('click', () => {
            this.performIntersection();
        });

        // Difference button
        this.find('.difference-btn')?.addEventListener('click', () => {
            this.performDifference();
        });
    }

    bindDialogEvents() {
        // Buffer dialog events
        const bufferDialog = document.getElementById('bufferDialog');
        if (bufferDialog) {
            this.bindBufferDialogEvents(bufferDialog);
        }

        // Batch operations dialog events
        const batchDialog = document.getElementById('batchOperationsDialog');
        if (batchDialog) {
            this.bindBatchDialogEvents(batchDialog);
        }
    }

    bindBufferDialogEvents(dialog) {
        // Distance and unit change
        const distanceInput = dialog.querySelector('#bufferDistance');
        const unitSelect = dialog.querySelector('#bufferUnit');
        
        if (distanceInput) {
            distanceInput.addEventListener('change', (e) => {
                this.currentBufferDistance = parseFloat(e.target.value) || 0;
            });
        }
        
        if (unitSelect) {
            unitSelect.addEventListener('change', (e) => {
                this.currentBufferUnit = e.target.value;
            });
        }

        // Action buttons
        dialog.querySelectorAll('[data-action]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.handleBufferAction(action);
            });
        });
    }

    bindBatchDialogEvents(dialog) {
        // Operation buttons
        dialog.querySelectorAll('[data-operation]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const operation = e.currentTarget.dataset.operation;
                this.handleBatchOperation(operation);
            });
        });

        // Show/hide buffer controls
        const bufferAllBtn = dialog.querySelector('[data-operation="buffer-all"]');
        const bufferControls = dialog.querySelector('#batchBufferControls');
        
        if (bufferAllBtn && bufferControls) {
            bufferAllBtn.addEventListener('click', () => {
                bufferControls.style.display = 'block';
            });
        }

        // Close button
        dialog.querySelectorAll('[data-action="close"]').forEach(btn => {
            btn.addEventListener('click', () => {
                this.hideBatchOperationsDialog();
            });
        });
    }

    // ========================================
    // Buffer Operations
    // ========================================

    handleBufferAction(action) {
        switch (action) {
            case 'close':
                this.hideBufferDialog();
                break;
            case 'buffer-selected':
                this.bufferSelected();
                break;
            case 'buffer-all':
                this.showBatchOperationsDialog();
                break;
            case 'apply-buffer':
                this.applyBuffer();
                break;
        }
    }

    bufferSelected() {
        if (!this.identifiedFeature) {
            this.showToast('لطفاً ابتدا یک شکل انتخاب کنید', 'warning');
            return;
        }

        try {
            const distance = this.getBufferDistanceInMeters();
            const color = document.getElementById('bufferColor')?.value || '#ff7800';
            const replaceOriginal = document.getElementById('replaceOriginal')?.checked || false;

            const leafletGeometry = this.identifiedFeature.toGeoJSON();
            const buffered = turf.buffer(leafletGeometry, distance / 111325, { units: 'degrees' });

            const style = {
                ...this.options.bufferStyle,
                color: color,
                fillColor: color
            };

            const layer = L.geoJSON(buffered, { style: style });

            if (this.editableLayers) {
                this.editableLayers.addLayer(layer);
                layer.tool = "selecttool";
                layer.type = "polygon";
                layer.buffer = true;
                layer.bindPopup(this.createFeaturePopup(layer));
            }

            if (replaceOriginal && this.editableLayers.hasLayer(this.identifiedFeature)) {
                this.editableLayers.removeLayer(this.identifiedFeature);
            }

            this.hideBufferDialog();
            this.showToast('بافر با موفقیت ایجاد شد', 'success');

            if (this.options.onBufferCreated) {
                this.options.onBufferCreated(layer, this.identifiedFeature);
            }

            this.trigger('bufferCreated', { layer, original: this.identifiedFeature });

        } catch (error) {
            console.error('Error creating buffer:', error);
            this.showToast('خطا در ایجاد بافر', 'danger');
        }
    }

    bufferAllGraphics(distance, deletePrev = false, fillColor = '#ff7800') {
        if (!this.editableLayers) {
            this.showToast('لایه قابل ویرایش یافت نشد', 'warning');
            return;
        }

        try {
            const selectedLayers = this.getSelectedLayers();

            if (selectedLayers.length === 0) {
                this.showToast('هیچ شکلی برای بافر یافت نشد', 'warning');
                return;
            }

            if (deletePrev) {
                selectedLayers.forEach(layer => {
                    this.editableLayers.removeLayer(layer);
                });
            }

            const style = {
                ...this.options.bufferStyle,
                color: fillColor,
                fillColor: fillColor
            };

            selectedLayers.forEach(layer => {
                try {
                    const leafletGeometry = layer.toGeoJSON();
                    const buffered = turf.buffer(leafletGeometry, distance / 111325, { units: 'degrees' });

                    const bufferLayer = L.geoJSON(buffered, { style: style });

                    this.editableLayers.addLayer(bufferLayer);
                    bufferLayer.tool = "selecttool";
                    bufferLayer.buffer = true;
                    bufferLayer.type = "polygon";
                    bufferLayer.bindPopup(this.createFeaturePopup(bufferLayer));

                } catch (error) {
                    console.error('Error buffering layer:', error);
                }
            });

            this.showToast(`${selectedLayers.length} شکل با موفقیت بافر شدند`, 'success');

        } catch (error) {
            console.error('Error in batch buffer:', error);
            this.showToast('خطا در ایجاد بافر دسته‌ای', 'danger');
        }
    }

    applyBuffer() {
        const distance = this.getBufferDistanceInMeters();
        const color = document.getElementById('bufferColor')?.value || '#ff7800';

        if (this.identifiedFeature) {
            this.bufferSelected();
        } else {
            this.bufferAllGraphics(distance, false, color);
        }
    }

    // ========================================
    // Union Operations
    // ========================================

    performUnion() {
        if (!this.editableLayers) {
            this.showToast('لایه قابل ویرایش یافت نشد', 'warning');
            return;
        }

        try {
            const selectedLayers = this.getSelectedLayers();

            if (selectedLayers.length < 2) {
                this.showToast('برای ادغام حداقل دو شکل انتخاب کنید', 'warning');
                return;
            }

            // Remove original layers
            selectedLayers.forEach(layer => {
                this.editableLayers.removeLayer(layer);
            });

            let unionGeometry = null;

            selectedLayers.forEach((layer, index) => {
                const geojson = layer.toGeoJSON();
                const feature = geojson.features ? geojson.features[0] : geojson;

                if (index === 0) {
                    unionGeometry = feature;
                } else {
                    unionGeometry = turf.union(unionGeometry, feature);
                }
            });

            if (unionGeometry) {
                const style = {
                    ...this.options.unionStyle
                };

                const unionLayer = L.geoJSON(unionGeometry, { style: style });

                this.editableLayers.addLayer(unionLayer);
                unionLayer.tool = "selecttool";
                unionLayer.type = "polygon";
                unionLayer.union = true;
                unionLayer.bindPopup(this.createFeaturePopup(unionLayer));

                this.showToast('اشکال با موفقیت ادغام شدند', 'success');

                if (this.options.onUnionCreated) {
                    this.options.onUnionCreated(unionLayer, selectedLayers);
                }

                this.trigger('unionCreated', { layer: unionLayer, originals: selectedLayers });
            }

        } catch (error) {
            console.error('Error performing union:', error);
            this.showToast('خطا در ادغام اشکال', 'danger');
        }
    }

    // ========================================
    // Other Spatial Operations
    // ========================================

    performIntersection() {
        if (!this.editableLayers) {
            this.showToast('لایه قابل ویرایش یافت نشد', 'warning');
            return;
        }

        const selectedLayers = this.getSelectedLayers();

        if (selectedLayers.length !== 2) {
            this.showToast('برای تقاطع دقیقاً دو شکل انتخاب کنید', 'warning');
            return;
        }

        try {
            const geojson1 = selectedLayers[0].toGeoJSON();
            const geojson2 = selectedLayers[1].toGeoJSON();

            const feature1 = geojson1.features ? geojson1.features[0] : geojson1;
            const feature2 = geojson2.features ? geojson2.features[0] : geojson2;

            const intersection = turf.intersect(feature1, feature2);

            if (intersection) {
                const style = {
                    color: '#ff0000',
                    weight: 2,
                    opacity: 0.8,
                    fillColor: '#ff0000',
                    fillOpacity: 0.3
                };

                const intersectionLayer = L.geoJSON(intersection, { style: style });

                this.editableLayers.addLayer(intersectionLayer);
                intersectionLayer.tool = "selecttool";
                intersectionLayer.type = "polygon";
                intersectionLayer.intersection = true;
                intersectionLayer.bindPopup(this.createFeaturePopup(intersectionLayer));

                this.showToast('تقاطع با موفقیت ایجاد شد', 'success');
            } else {
                this.showToast('تقاطعی بین اشکال یافت نشد', 'info');
            }

        } catch (error) {
            console.error('Error performing intersection:', error);
            this.showToast('خطا در ایجاد تقاطع', 'danger');
        }
    }

    performDifference() {
        if (!this.editableLayers) {
            this.showToast('لایه قابل ویرایش یافت نشد', 'warning');
            return;
        }

        const selectedLayers = this.getSelectedLayers();

        if (selectedLayers.length !== 2) {
            this.showToast('برای تفاضل دقیقاً دو شکل انتخاب کنید', 'warning');
            return;
        }

        try {
            const geojson1 = selectedLayers[0].toGeoJSON();
            const geojson2 = selectedLayers[1].toGeoJSON();

            const feature1 = geojson1.features ? geojson1.features[0] : geojson1;
            const feature2 = geojson2.features ? geojson2.features[0] : geojson2;

            const difference = turf.difference(feature1, feature2);

            if (difference) {
                const style = {
                    color: '#800080',
                    weight: 2,
                    opacity: 0.8,
                    fillColor: '#800080',
                    fillOpacity: 0.3
                };

                const differenceLayer = L.geoJSON(difference, { style: style });

                this.editableLayers.addLayer(differenceLayer);
                differenceLayer.tool = "selecttool";
                differenceLayer.type = "polygon";
                differenceLayer.difference = true;
                differenceLayer.bindPopup(this.createFeaturePopup(differenceLayer));

                this.showToast('تفاضل با موفقیت ایجاد شد', 'success');
            } else {
                this.showToast('تفاضلی بین اشکال یافت نشد', 'info');
            }

        } catch (error) {
            console.error('Error performing difference:', error);
            this.showToast('خطا در ایجاد تفاضل', 'danger');
        }
    }

    // ========================================
    // Batch Operations
    // ========================================

    handleBatchOperation(operation) {
        switch (operation) {
            case 'union':
                this.performUnion();
                break;
            case 'buffer-all':
                this.performBatchBuffer();
                break;
            case 'dissolve':
                this.performDissolve();
                break;
            case 'clear-all':
                this.clearAllGraphics();
                break;
        }
    }

    performBatchBuffer() {
        const distance = parseFloat(document.getElementById('batchBufferDistance')?.value) || 100;
        const unit = document.getElementById('batchBufferUnit')?.value || 'meters';
        const deletePrev = document.getElementById('deletePrevious')?.checked || false;

        const distanceInMeters = this.convertToMeters(distance, unit);
        this.bufferAllGraphics(distanceInMeters, deletePrev);
    }

    performDissolve() {
        // Similar to union but keeps individual features
        const selectedLayers = this.getSelectedLayers();

        if (selectedLayers.length === 0) {
            this.showToast('هیچ شکلی برای حذف مرزها یافت نشد', 'warning');
            return;
        }

        try {
            const features = selectedLayers.map(layer => {
                const geojson = layer.toGeoJSON();
                return geojson.features ? geojson.features[0] : geojson;
            });

            const dissolved = turf.dissolve(turf.featureCollection(features));

            // Remove original layers
            selectedLayers.forEach(layer => {
                this.editableLayers.removeLayer(layer);
            });

            dissolved.features.forEach(feature => {
                const style = {
                    color: '#00ff00',
                    weight: 2,
                    opacity: 0.8,
                    fillColor: '#00ff00',
                    fillOpacity: 0.3
                };

                const dissolvedLayer = L.geoJSON(feature, { style: style });

                this.editableLayers.addLayer(dissolvedLayer);
                dissolvedLayer.tool = "selecttool";
                dissolvedLayer.type = "polygon";
                dissolvedLayer.dissolved = true;
                dissolvedLayer.bindPopup(this.createFeaturePopup(dissolvedLayer));
            });

            this.showToast('مرزهای داخلی با موفقیت حذف شدند', 'success');

        } catch (error) {
            console.error('Error performing dissolve:', error);
            this.showToast('خطا در حذف مرزهای داخلی', 'danger');
        }
    }

    clearAllGraphics() {
        if (!this.editableLayers) {
            this.showToast('لایه قابل ویرایش یافت نشد', 'warning');
            return;
        }

        if (confirm('آیا از پاک کردن همه اشکال اطمینان دارید؟')) {
            this.editableLayers.clearLayers();
            this.showToast('همه اشکال پاک شدند', 'success');
        }
    }

    // ========================================
    // Utility Methods
    // ========================================

    getSelectedLayers() {
        if (!this.editableLayers) return [];

        const layers = [];
        this.editableLayers.eachLayer(layer => {
            if (layer.tool === "selecttool") {
                layers.push(layer);
            }
        });

        return layers;
    }

    getBufferDistanceInMeters() {
        const distance = parseFloat(document.getElementById('bufferDistance')?.value) || this.currentBufferDistance;
        const unit = document.getElementById('bufferUnit')?.value || this.currentBufferUnit;

        return this.convertToMeters(distance, unit);
    }

    convertToMeters(distance, unit) {
        switch (unit) {
            case 'kilometers':
                return distance * 1000;
            case 'feet':
                return distance * 0.3048;
            case 'miles':
                return distance * 1609.34;
            default:
                return distance; // meters
        }
    }

    createFeaturePopup(layer) {
        return `
            <div class="feature-popup">
                <button type="button" class="btn btn-danger btn-sm"
                        onclick="window.spatialAnalysisTools.removeLayer(${layer._leaflet_id})">
                    <i class="fas fa-trash"></i> حذف
                </button>
                <button type="button" class="btn btn-info btn-sm"
                        onclick="window.spatialAnalysisTools.showLayerInfo(${layer._leaflet_id})">
                    <i class="fas fa-info"></i> اطلاعات
                </button>
            </div>
        `;
    }

    removeLayer(layerId) {
        if (this.editableLayers) {
            const layer = this.editableLayers.getLayer(layerId);
            if (layer) {
                this.editableLayers.removeLayer(layer);
                this.showToast('شکل حذف شد', 'success');
            }
        }
    }

    showLayerInfo(layerId) {
        if (this.editableLayers) {
            const layer = this.editableLayers.getLayer(layerId);
            if (layer) {
                const geojson = layer.toGeoJSON();
                const info = {
                    type: layer.type || 'نامشخص',
                    area: this.calculateArea(geojson),
                    perimeter: this.calculatePerimeter(geojson),
                    buffer: layer.buffer || false,
                    union: layer.union || false
                };

                alert(`نوع: ${info.type}\nمساحت: ${info.area}\nمحیط: ${info.perimeter}\nبافر: ${info.buffer ? 'بله' : 'خیر'}\nادغام: ${info.union ? 'بله' : 'خیر'}`);
            }
        }
    }

    calculateArea(geojson) {
        try {
            const area = turf.area(geojson);
            return `${(area / 1000000).toFixed(2)} کیلومتر مربع`;
        } catch {
            return 'نامحدود';
        }
    }

    calculatePerimeter(geojson) {
        try {
            const length = turf.length(geojson, { units: 'kilometers' });
            return `${length.toFixed(2)} کیلومتر`;
        } catch {
            return 'نامحدود';
        }
    }

    showToast(message, type = 'info', duration = 3000) {
        // Use existing toast system or create simple alert
        if (window.showToast) {
            window.showToast(message, type, duration);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    // ========================================
    // Dialog Management
    // ========================================

    showBufferDialog() {
        const dialog = document.getElementById('bufferDialog');
        if (dialog) {
            dialog.style.display = 'block';
        }
    }

    hideBufferDialog() {
        const dialog = document.getElementById('bufferDialog');
        if (dialog) {
            dialog.style.display = 'none';
        }
    }

    showBatchOperationsDialog() {
        const dialog = document.getElementById('batchOperationsDialog');
        if (dialog) {
            dialog.style.display = 'block';
        }
    }

    hideBatchOperationsDialog() {
        const dialog = document.getElementById('batchOperationsDialog');
        if (dialog) {
            dialog.style.display = 'none';
        }
    }

    // ========================================
    // Public API
    // ========================================

    setIdentifiedFeature(feature) {
        this.identifiedFeature = feature;
    }

    getIdentifiedFeature() {
        return this.identifiedFeature;
    }

    setBufferDistance(distance, unit = 'meters') {
        this.currentBufferDistance = distance;
        this.currentBufferUnit = unit;

        const distanceInput = document.getElementById('bufferDistance');
        const unitSelect = document.getElementById('bufferUnit');

        if (distanceInput) distanceInput.value = distance;
        if (unitSelect) unitSelect.value = unit;
    }

    getBufferDistance() {
        return {
            distance: this.currentBufferDistance,
            unit: this.currentBufferUnit
        };
    }

    // ========================================
    // Cleanup
    // ========================================

    destroy() {
        // Remove dialogs
        const bufferDialog = document.getElementById('bufferDialog');
        if (bufferDialog) {
            bufferDialog.remove();
        }

        const batchDialog = document.getElementById('batchOperationsDialog');
        if (batchDialog) {
            batchDialog.remove();
        }

        super.destroy();
    }
}

// Register component
window.ComponentFactory.register('spatial-analysis', SpatialAnalysisTools);
