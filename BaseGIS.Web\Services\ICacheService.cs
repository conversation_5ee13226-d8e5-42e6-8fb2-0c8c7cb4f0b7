namespace BaseGIS.Web.Services
{
    /// <summary>
    /// سرویس مدیریت Cache
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// دریافت مقدار از Cache
        /// </summary>
        Task<T?> GetAsync<T>(string key) where T : class;

        /// <summary>
        /// ذخیره مقدار در Cache
        /// </summary>
        Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class;

        /// <summary>
        /// حذف مقدار از Cache
        /// </summary>
        Task RemoveAsync(string key);

        /// <summary>
        /// حذف مقادیر با الگوی مشخص
        /// </summary>
        Task RemoveByPatternAsync(string pattern);

        /// <summary>
        /// بررسی وجود کلید در Cache
        /// </summary>
        Task<bool> ExistsAsync(string key);

        /// <summary>
        /// دریافت یا ایجاد مقدار در Cache
        /// </summary>
        Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> getItem, TimeSpan? expiration = null) where T : class;
    }

    /// <summary>
    /// پیاده‌سازی سرویس Cache با Memory Cache
    /// </summary>
    public class MemoryCacheService : ICacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<MemoryCacheService> _logger;
        private readonly HashSet<string> _cacheKeys;
        private readonly object _lock = new object();

        public MemoryCacheService(IMemoryCache memoryCache, ILogger<MemoryCacheService> logger)
        {
            _memoryCache = memoryCache;
            _logger = logger;
            _cacheKeys = new HashSet<string>();
        }

        public Task<T?> GetAsync<T>(string key) where T : class
        {
            try
            {
                var value = _memoryCache.Get<T>(key);
                return Task.FromResult(value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache value for key {Key}", key);
                return Task.FromResult<T?>(null);
            }
        }

        public Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
        {
            try
            {
                var options = new MemoryCacheEntryOptions();
                
                if (expiration.HasValue)
                {
                    options.AbsoluteExpirationRelativeToNow = expiration.Value;
                }
                else
                {
                    // Default expiration: 30 minutes
                    options.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30);
                }

                // Set priority
                options.Priority = CacheItemPriority.Normal;

                // Add callback for when item is removed
                options.RegisterPostEvictionCallback((k, v, reason, state) =>
                {
                    lock (_lock)
                    {
                        _cacheKeys.Remove(k.ToString() ?? string.Empty);
                    }
                });

                _memoryCache.Set(key, value, options);

                lock (_lock)
                {
                    _cacheKeys.Add(key);
                }

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cache value for key {Key}", key);
                return Task.CompletedTask;
            }
        }

        public Task RemoveAsync(string key)
        {
            try
            {
                _memoryCache.Remove(key);
                
                lock (_lock)
                {
                    _cacheKeys.Remove(key);
                }

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache value for key {Key}", key);
                return Task.CompletedTask;
            }
        }

        public Task RemoveByPatternAsync(string pattern)
        {
            try
            {
                List<string> keysToRemove;
                
                lock (_lock)
                {
                    keysToRemove = _cacheKeys
                        .Where(key => key.Contains(pattern, StringComparison.OrdinalIgnoreCase))
                        .ToList();
                }

                foreach (var key in keysToRemove)
                {
                    _memoryCache.Remove(key);
                }

                lock (_lock)
                {
                    foreach (var key in keysToRemove)
                    {
                        _cacheKeys.Remove(key);
                    }
                }

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache values by pattern {Pattern}", pattern);
                return Task.CompletedTask;
            }
        }

        public Task<bool> ExistsAsync(string key)
        {
            try
            {
                return Task.FromResult(_memoryCache.TryGetValue(key, out _));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking cache existence for key {Key}", key);
                return Task.FromResult(false);
            }
        }

        public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> getItem, TimeSpan? expiration = null) where T : class
        {
            try
            {
                var cachedValue = await GetAsync<T>(key);
                if (cachedValue != null)
                {
                    return cachedValue;
                }

                var value = await getItem();
                if (value != null)
                {
                    await SetAsync(key, value, expiration);
                }

                return value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetOrSetAsync for key {Key}", key);
                return await getItem();
            }
        }
    }

    /// <summary>
    /// کلیدهای Cache برای داشبورد
    /// </summary>
    public static class CacheKeys
    {
        public const string DashboardPrefix = "dashboard:";
        public const string PanelPrefix = "panel:";
        public const string ChartDataPrefix = "chart_data:";
        public const string TableDataPrefix = "table_data:";
        public const string UserParametersPrefix = "user_params:";
        public const string ColorsPrefix = "colors:";
        public const string SqlPrefix = "sql:";

        public static string Dashboard(int id) => $"{DashboardPrefix}{id}";
        public static string DashboardList(string userId) => $"{DashboardPrefix}list:{userId}";
        public static string Panel(int id) => $"{PanelPrefix}{id}";
        public static string PanelList(int dashboardId) => $"{PanelPrefix}list:{dashboardId}";
        public static string ChartData(int panelId, string inputHash) => $"{ChartDataPrefix}{panelId}:{inputHash}";
        public static string TableData(int panelId, string inputHash) => $"{TableDataPrefix}{panelId}:{inputHash}";
        public static string UserParameters(int dashboardId, int panelId, string userId) => $"{UserParametersPrefix}{dashboardId}:{panelId}:{userId}";
        public static string Colors() => $"{ColorsPrefix}all";
        public static string SqlList() => $"{SqlPrefix}list";

        public static string GetInputHash(Dictionary<string, string> inputs)
        {
            var sortedInputs = inputs.OrderBy(x => x.Key).ToList();
            var inputString = string.Join("|", sortedInputs.Select(x => $"{x.Key}={x.Value}"));
            return inputString.GetHashCode().ToString();
        }
    }

    /// <summary>
    /// Extension methods برای Cache
    /// </summary>
    public static class CacheExtensions
    {
        /// <summary>
        /// ذخیره داشبورد در Cache
        /// </summary>
        public static async Task SetDashboardAsync(this ICacheService cache, Dashboard dashboard)
        {
            await cache.SetAsync(CacheKeys.Dashboard(dashboard.ID), dashboard, TimeSpan.FromMinutes(60));
        }

        /// <summary>
        /// حذف Cache داشبورد
        /// </summary>
        public static async Task InvalidateDashboardAsync(this ICacheService cache, int dashboardId)
        {
            await cache.RemoveAsync(CacheKeys.Dashboard(dashboardId));
            await cache.RemoveByPatternAsync($"{CacheKeys.DashboardPrefix}{dashboardId}");
        }

        /// <summary>
        /// ذخیره پنل در Cache
        /// </summary>
        public static async Task SetPanelAsync(this ICacheService cache, DashboardPanel panel)
        {
            await cache.SetAsync(CacheKeys.Panel(panel.ID), panel, TimeSpan.FromMinutes(30));
        }

        /// <summary>
        /// حذف Cache پنل
        /// </summary>
        public static async Task InvalidatePanelAsync(this ICacheService cache, int panelId)
        {
            await cache.RemoveAsync(CacheKeys.Panel(panelId));
            await cache.RemoveByPatternAsync($"{CacheKeys.PanelPrefix}{panelId}");
            await cache.RemoveByPatternAsync($"{CacheKeys.ChartDataPrefix}{panelId}");
            await cache.RemoveByPatternAsync($"{CacheKeys.TableDataPrefix}{panelId}");
        }

        /// <summary>
        /// ذخیره داده‌های نمودار در Cache
        /// </summary>
        public static async Task SetChartDataAsync(this ICacheService cache, int panelId, Dictionary<string, string> inputs, ChartDataViewModel data)
        {
            var key = CacheKeys.ChartData(panelId, CacheKeys.GetInputHash(inputs));
            await cache.SetAsync(key, data, TimeSpan.FromMinutes(15));
        }

        /// <summary>
        /// دریافت داده‌های نمودار از Cache
        /// </summary>
        public static async Task<ChartDataViewModel?> GetChartDataAsync(this ICacheService cache, int panelId, Dictionary<string, string> inputs)
        {
            var key = CacheKeys.ChartData(panelId, CacheKeys.GetInputHash(inputs));
            return await cache.GetAsync<ChartDataViewModel>(key);
        }
    }
}
