2025-06-17 19:54:56.123 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-17 19:54:56.222 +03:30 [ERR] An error occurred while seeding the database
System.InvalidOperationException: No service for type 'Microsoft.AspNetCore.Identity.RoleManager`1[Microsoft.AspNetCore.Identity.IdentityRole]' has been registered.
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<Main>$(String[] args) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\Program.cs:line 164
2025-06-17 19:54:58.399 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-17 19:54:58.401 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-17 19:54:58.481 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-17 19:54:58.484 +03:30 [INF] Hosting environment: Development
2025-06-17 19:54:58.485 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-17 19:54:58.489 +03:30 [INF] The application has started
2025-06-17 19:55:24.204 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-17 19:55:24.516 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-17 19:55:24.533 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-17 19:55:24.721 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:55:24.728 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.6447ms.
2025-06-17 19:55:24.747 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-17 19:55:25.045 +03:30 [INF] Executed ViewResult - view Index executed in 303.5904ms.
2025-06-17 19:55:25.051 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 514.6923ms
2025-06-17 19:55:25.053 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-17 19:55:25.062 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 866.0513ms
2025-06-17 19:55:25.126 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-17 19:55:25.126 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-17 19:55:25.126 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - null null
2025-06-17 19:55:25.126 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-17 19:55:25.166 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - null null
2025-06-17 19:55:25.166 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/avatars/male.png - null null
2025-06-17 19:55:25.177 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - null null
2025-06-17 19:55:25.189 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - null null
2025-06-17 19:55:25.200 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - null null
2025-06-17 19:55:25.217 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - null null
2025-06-17 19:55:25.218 +03:30 [INF] The file /lib/overlayscrollbars/styles/overlayscrollbars.min.css was not modified
2025-06-17 19:55:25.219 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 19:55:25.224 +03:30 [INF] The file /lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js was not modified
2025-06-17 19:55:25.224 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-17 19:55:25.224 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-17 19:55:25.227 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-17 19:55:25.231 +03:30 [INF] The file /css/adminlte.rtl.css was not modified
2025-06-17 19:55:25.235 +03:30 [INF] The file /lib/source-sans-3/index.css was not modified
2025-06-17 19:55:25.236 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - 304 null text/css 70.3388ms
2025-06-17 19:55:25.241 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - 304 null text/javascript 64.0072ms
2025-06-17 19:55:25.244 +03:30 [INF] Sending file. Request path: '/assets/img/AdminLTELogo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\AdminLTELogo.png'
2025-06-17 19:55:25.245 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 200 2750 image/jpeg 119.1408ms
2025-06-17 19:55:25.249 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 200 3398 image/jpeg 122.54ms
2025-06-17 19:55:25.251 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/avatars/male.png - 200 268 image/png 85.4479ms
2025-06-17 19:55:25.253 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - 304 null text/css 52.6968ms
2025-06-17 19:55:25.254 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - 304 null text/css 37.1213ms
2025-06-17 19:55:25.260 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - null null
2025-06-17 19:55:25.265 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 19:55:25.267 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - 200 2637 image/png 77.995ms
2025-06-17 19:55:25.274 +03:30 [INF] Sending file. Request path: '/css/site.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site.css'
2025-06-17 19:55:25.278 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-17 19:55:25.310 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=Nmb9k2AhsnpOv4cI_Fjp8KgGhTAxQTOTwjkpS20OWjE - null null
2025-06-17 19:55:25.294 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js - null null
2025-06-17 19:55:25.300 +03:30 [INF] The file /lib/bootstrap-icons/font/bootstrap-icons.min.css was not modified
2025-06-17 19:55:25.285 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 66.5452ms
2025-06-17 19:55:25.289 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - null null
2025-06-17 19:55:25.321 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - 200 7707 text/css 194.652ms
2025-06-17 19:55:25.325 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 200 4983 image/jpeg 198.5228ms
2025-06-17 19:55:25.330 +03:30 [INF] The file /js/site.js was not modified
2025-06-17 19:55:25.333 +03:30 [INF] The file /js/adminlte.js was not modified
2025-06-17 19:55:25.365 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js - 304 null text/javascript 70.775ms
2025-06-17 19:55:25.348 +03:30 [INF] The file /lib/popper.js/dist/umd/popper.min.js was not modified
2025-06-17 19:55:25.363 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=Nmb9k2AhsnpOv4cI_Fjp8KgGhTAxQTOTwjkpS20OWjE - 304 null text/javascript 53.5584ms
2025-06-17 19:55:25.335 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - 304 null text/css 74.8024ms
2025-06-17 19:55:25.375 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 110.1327ms
2025-06-17 19:55:25.375 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - 304 null text/javascript 86.4435ms
2025-06-17 19:55:25.378 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-17 19:55:25.390 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-17 19:55:25.390 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - null null
2025-06-17 19:55:25.599 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-17 19:55:25.599 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-17 19:55:25.768 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/webfonts/fa-solid-900.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\webfonts\fa-solid-900.woff2'
2025-06-17 19:55:25.770 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 499 45276 application/font-woff 391.3729ms
2025-06-17 19:55:25.771 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-17 19:55:25.926 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-17 19:55:25.927 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - 499 158220 font/woff2 537.1472ms
2025-06-17 19:55:25.936 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - 200 130396 font/woff2 546.4794ms
2025-06-17 19:55:25.941 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 499 5430 image/x-icon 377.6619ms
2025-06-17 19:55:32.344 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting - null null
2025-06-17 19:55:32.397 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 19:55:32.402 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:55:32.437 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:55:35.333 +03:30 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 19:55:35.471 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 3027.3541ms.
2025-06-17 19:55:35.476 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-17 19:55:35.535 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 59.6651ms.
2025-06-17 19:55:35.539 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 3132.6749ms
2025-06-17 19:55:35.541 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 19:55:35.555 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting - 200 null text/html; charset=utf-8 3211.2722ms
2025-06-17 19:55:35.602 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css - null null
2025-06-17 19:55:35.608 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 19:55:35.609 +03:30 [INF] The file /lib/jquery.fancytree/skin-win8/ui.fancytree.min.css was not modified
2025-06-17 19:55:35.609 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 19:55:35.625 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/jquery.fancytree-all.min.js - null null
2025-06-17 19:55:35.625 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css - 304 null text/css 23.5731ms
2025-06-17 19:55:35.631 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 21.6073ms
2025-06-17 19:55:35.632 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 24.4797ms
2025-06-17 19:55:35.636 +03:30 [INF] The file /lib/jquery.fancytree/jquery.fancytree-all.min.js was not modified
2025-06-17 19:55:35.656 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/jquery.fancytree-all.min.js - 304 null text/javascript 38.4728ms
2025-06-17 19:55:38.180 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-17 19:55:38.180 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-17 19:55:38.185 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-17 19:55:38.189 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-17 19:55:38.191 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 10.9917ms
2025-06-17 19:55:38.193 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 13.2327ms
2025-06-17 19:55:45.813 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js.map - null null
2025-06-17 19:55:45.813 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - null null
2025-06-17 19:55:45.813 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js.map - null null
2025-06-17 19:55:45.830 +03:30 [INF] Sending file. Request path: '/lib/popper.js/dist/umd/popper.min.js.map'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\popper.js\dist\umd\popper.min.js.map'
2025-06-17 19:55:45.833 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js.map - 200 110046 text/plain 19.9702ms
2025-06-17 19:55:45.835 +03:30 [INF] Sending file. Request path: '/js/adminlte.js.map'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\adminlte.js.map'
2025-06-17 19:55:45.836 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.js.map'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.js.map'
2025-06-17 19:55:45.842 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js.map - 200 46475 text/plain 29.0812ms
2025-06-17 19:55:45.845 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - 200 225544 text/plain 31.3282ms
2025-06-17 19:55:45.889 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js.map - null null
2025-06-17 19:55:45.894 +03:30 [INF] The file /lib/bootstrap/js/bootstrap.bundle.min.js.map was not modified
2025-06-17 19:55:45.896 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js.map - 304 null text/plain 7.876ms
2025-06-17 19:55:46.106 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting - null null
2025-06-17 19:55:46.125 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 19:55:46.127 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:55:46.161 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:55:46.174 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 19:55:46.181 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 17.4313ms.
2025-06-17 19:55:46.184 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-17 19:55:46.189 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 5.3462ms.
2025-06-17 19:55:46.193 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 60.1123ms
2025-06-17 19:55:46.195 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 19:55:46.198 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting - 200 null text/html; charset=utf-8 91.4976ms
2025-06-17 19:55:52.670 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager - null null
2025-06-17 19:55:52.721 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 19:55:52.724 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:55:52.757 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:55:52.831 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 19:55:52.838 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 77.6252ms.
2025-06-17 19:55:52.841 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 19:55:52.850 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 9.7055ms.
2025-06-17 19:55:52.852 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 123.6518ms
2025-06-17 19:55:52.856 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 19:55:52.860 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager - 200 null text/html; charset=utf-8 190.5303ms
2025-06-17 19:55:52.915 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 19:55:52.916 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 19:55:52.933 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 16.6859ms
2025-06-17 19:55:52.935 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 19.4427ms
2025-06-17 19:55:57.494 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-17 19:55:57.609 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-17 19:55:57.612 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:55:57.641 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:55:57.768 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 123.8607ms.
2025-06-17 19:55:57.774 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-17 19:55:57.821 +03:30 [INF] Executed ViewResult - view Insert executed in 47.9573ms.
2025-06-17 19:55:57.824 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 208.8132ms
2025-06-17 19:55:57.827 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-17 19:55:57.830 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 335.5869ms
2025-06-17 19:55:57.881 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/dropzone/min/dropzone.min.css - null null
2025-06-17 19:55:57.884 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/smartwizard/dist/css/smart_wizard_all.min.css - null null
2025-06-17 19:55:57.884 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/select2/css/select2.min.css - null null
2025-06-17 19:55:57.886 +03:30 [INF] The file /lib/dropzone/min/dropzone.min.css was not modified
2025-06-17 19:55:57.892 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 19:55:57.892 +03:30 [INF] The file /lib/smartwizard/dist/css/smart_wizard_all.min.css was not modified
2025-06-17 19:55:57.901 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/dropzone/min/dropzone.min.css - 304 null text/css 19.8223ms
2025-06-17 19:55:57.900 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/dropzone/min/dropzone.min.js - null null
2025-06-17 19:55:57.901 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/smartwizard/dist/js/jquery.smartWizard.min.js - null null
2025-06-17 19:55:57.905 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/select2/js/i18n/fa.js - null null
2025-06-17 19:55:57.893 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 19:55:57.897 +03:30 [INF] The file /lib/select2/css/select2.min.css was not modified
2025-06-17 19:55:57.910 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/select2/js/select2.min.js - null null
2025-06-17 19:55:57.913 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/smartwizard/dist/css/smart_wizard_all.min.css - 304 null text/css 28.8946ms
2025-06-17 19:55:57.923 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 31.0921ms
2025-06-17 19:55:57.924 +03:30 [INF] The file /lib/dropzone/min/dropzone.min.js was not modified
2025-06-17 19:55:57.930 +03:30 [INF] The file /lib/smartwizard/dist/js/jquery.smartWizard.min.js was not modified
2025-06-17 19:55:57.932 +03:30 [INF] The file /lib/select2/js/i18n/fa.js was not modified
2025-06-17 19:55:57.935 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 41.7687ms
2025-06-17 19:55:57.937 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/select2/css/select2.min.css - 304 null text/css 52.0102ms
2025-06-17 19:55:57.940 +03:30 [INF] The file /lib/select2/js/select2.min.js was not modified
2025-06-17 19:55:57.950 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/dropzone/min/dropzone.min.js - 304 null text/javascript 49.3457ms
2025-06-17 19:55:57.952 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/smartwizard/dist/js/jquery.smartWizard.min.js - 304 null text/javascript 51.2495ms
2025-06-17 19:55:57.956 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/select2/js/i18n/fa.js - 304 null text/javascript 50.8905ms
2025-06-17 19:55:57.965 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/select2/js/select2.min.js - 304 null text/javascript 55.0508ms
2025-06-17 19:55:58.056 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1750177557974 - null null
2025-06-17 19:55:58.060 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-17 19:55:58.064 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:55:58.093 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:55:58.152 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-17 19:55:58.178 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 19:55:58.296 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 200.3354ms.
2025-06-17 19:55:58.302 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-17 19:55:58.466 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 164.8862ms.
2025-06-17 19:55:58.468 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 399.0092ms
2025-06-17 19:55:58.470 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-17 19:55:58.472 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1750177557974 - 200 null text/html; charset=utf-8 416.1819ms
2025-06-17 19:56:04.958 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Update - null null
2025-06-17 19:56:04.996 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web)'
2025-06-17 19:56:05.002 +03:30 [INF] Route matched with {action = "Update", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Update() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:56:05.028 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:56:05.143 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 110.4647ms.
2025-06-17 19:56:05.148 +03:30 [INF] Executing ViewResult, running view Update.
2025-06-17 19:56:05.161 +03:30 [INF] Executed ViewResult - view Update executed in 13.7963ms.
2025-06-17 19:56:05.165 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web) in 159.8661ms
2025-06-17 19:56:05.169 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web)'
2025-06-17 19:56:05.173 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Update - 200 null text/html; charset=utf-8 215.2502ms
2025-06-17 19:56:05.231 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 19:56:05.233 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 19:56:05.240 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 7.3689ms
2025-06-17 19:56:05.241 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 10.0842ms
2025-06-17 19:56:05.339 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Update_List?_=1750177565267 - null null
2025-06-17 19:56:05.345 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web)'
2025-06-17 19:56:05.349 +03:30 [INF] Route matched with {action = "_Update_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Update_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:56:05.383 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:56:05.392 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-17 19:56:05.397 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 19:56:05.404 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 18.4365ms.
2025-06-17 19:56:05.408 +03:30 [INF] Executing PartialViewResult, running view _Update_List.
2025-06-17 19:56:05.540 +03:30 [INF] Executed PartialViewResult - view _Update_List executed in 132.41ms.
2025-06-17 19:56:05.543 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web) in 188.8203ms
2025-06-17 19:56:05.544 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web)'
2025-06-17 19:56:05.546 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Update_List?_=1750177565267 - 200 null text/html; charset=utf-8 206.4558ms
2025-06-17 19:57:42.343 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Update - null null
2025-06-17 19:57:42.397 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web)'
2025-06-17 19:57:42.401 +03:30 [INF] Route matched with {action = "Update", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Update() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:57:42.437 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:57:42.439 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0399ms.
2025-06-17 19:57:42.442 +03:30 [INF] Executing ViewResult, running view Update.
2025-06-17 19:57:42.452 +03:30 [INF] Executed ViewResult - view Update executed in 10.158ms.
2025-06-17 19:57:42.453 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web) in 48.9719ms
2025-06-17 19:57:42.457 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web)'
2025-06-17 19:57:42.459 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Update - 200 null text/html; charset=utf-8 115.129ms
2025-06-17 19:57:42.516 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 19:57:42.517 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 19:57:42.535 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 19.2263ms
2025-06-17 19:57:42.548 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 30.3026ms
2025-06-17 19:57:42.625 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Update_List?_=1750177662555 - null null
2025-06-17 19:57:42.640 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web)'
2025-06-17 19:57:42.642 +03:30 [INF] Route matched with {action = "_Update_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Update_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:57:42.664 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:57:42.673 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-17 19:57:42.680 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 19:57:42.689 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 21.6843ms.
2025-06-17 19:57:42.692 +03:30 [INF] Executing PartialViewResult, running view _Update_List.
2025-06-17 19:57:42.695 +03:30 [INF] Executed PartialViewResult - view _Update_List executed in 3.7431ms.
2025-06-17 19:57:42.697 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web) in 51.5026ms
2025-06-17 19:57:42.701 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web)'
2025-06-17 19:57:42.703 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Update_List?_=1750177662555 - 200 null text/html; charset=utf-8 78.1091ms
2025-06-17 19:57:49.101 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting - null null
2025-06-17 19:57:49.137 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 19:57:49.140 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:57:49.174 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:57:49.179 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 19:57:49.184 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 6.4834ms.
2025-06-17 19:57:49.187 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-17 19:57:49.214 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 27.0498ms.
2025-06-17 19:57:49.216 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 73.0969ms
2025-06-17 19:57:49.219 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 19:57:49.221 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting - 200 null text/html; charset=utf-8 120.0488ms
2025-06-17 19:57:49.277 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jqueryui/themes/base/jquery-ui.min.css - null null
2025-06-17 19:57:49.282 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 19:57:49.283 +03:30 [INF] The file /lib/jqueryui/themes/base/jquery-ui.min.css was not modified
2025-06-17 19:57:49.287 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 19:57:49.308 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jqueryui/jquery-ui.min.js - null null
2025-06-17 19:57:49.298 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jqueryui/themes/base/jquery-ui.min.css - 304 null text/css 21.4365ms
2025-06-17 19:57:49.380 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 97.7277ms
2025-06-17 19:57:49.326 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 38.6862ms
2025-06-17 19:57:49.386 +03:30 [INF] The file /lib/jqueryui/jquery-ui.min.js was not modified
2025-06-17 19:57:49.402 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jqueryui/jquery-ui.min.js - 304 null text/javascript 95.0571ms
2025-06-17 19:57:49.476 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/icons-rtl.gif - null null
2025-06-17 19:57:49.482 +03:30 [INF] Sending file. Request path: '/lib/jquery.fancytree/skin-win8/icons-rtl.gif'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery.fancytree\skin-win8\icons-rtl.gif'
2025-06-17 19:57:49.485 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/icons-rtl.gif - 200 5513 image/gif 9.2747ms
2025-06-17 19:57:57.755 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-17 19:57:57.755 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-17 19:57:57.760 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-17 19:57:57.764 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 8.6373ms
2025-06-17 19:57:57.762 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-17 19:57:57.772 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 17.0083ms
2025-06-17 19:58:39.534 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting?id=6 - null null
2025-06-17 19:58:39.573 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 19:58:39.575 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:58:39.622 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:58:39.628 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 19:58:39.634 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 8.0881ms.
2025-06-17 19:58:39.636 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-17 19:58:39.641 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 5.159ms.
2025-06-17 19:58:39.645 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 62.8123ms
2025-06-17 19:58:39.647 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 19:58:39.650 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting?id=6 - 200 null text/html; charset=utf-8 115.8843ms
2025-06-17 19:58:39.848 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 19:58:39.850 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 19:58:39.857 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 7.4949ms
2025-06-17 19:58:39.908 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 59.9017ms
2025-06-17 19:58:39.999 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/DataBase/_Layer_Relations?id=6 - null null
2025-06-17 19:58:40.004 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web)'
2025-06-17 19:58:40.019 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/DataBase/_Layer_Fields?id=6 - null null
2025-06-17 19:58:40.023 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web)'
2025-06-17 19:58:40.026 +03:30 [INF] Route matched with {action = "_Layer_Relations", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Relations(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:58:40.030 +03:30 [INF] Route matched with {action = "_Layer_Fields", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Fields(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:58:40.077 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:58:40.119 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:58:40.306 +03:30 [INF] Executed DbCommand (138ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-17 19:58:40.307 +03:30 [INF] Executed DbCommand (136ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-17 19:58:40.313 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 191.1391ms.
2025-06-17 19:58:40.327 +03:30 [INF] Executing PartialViewResult, running view _Layer_Relations.
2025-06-17 19:58:40.330 +03:30 [ERR] The partial view '_Layer_Relations' was not found. Searched locations: ["/Views/Database/_Layer_Relations.cshtml","/Views/Shared/_Layer_Relations.cshtml"]
2025-06-17 19:58:40.620 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-17 19:58:40.620 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-17 19:58:40.699 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web) in 667.4326ms
2025-06-17 19:58:40.701 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-17 19:58:40.704 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-17 19:58:40.835 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 215.4622ms
2025-06-17 19:58:40.834 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web)'
2025-06-17 19:58:40.849 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 229.9586ms
2025-06-17 19:58:40.856 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 730.6702ms.
2025-06-17 19:58:40.976 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The view '_Layer_Relations' was not found. The following locations were searched:
/Views/Database/_Layer_Relations.cshtml
/Views/Shared/_Layer_Relations.cshtml
   at Microsoft.AspNetCore.Mvc.ViewEngines.ViewEngineResult.EnsureSuccessful(IEnumerable`1 originalLocations)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.PartialViewResultExecutor.ExecuteAsync(ActionContext context, PartialViewResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-17 19:58:40.979 +03:30 [INF] Executing PartialViewResult, running view _Layer_Fields.
2025-06-17 19:58:41.017 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/DataBase/_Layer_Relations?id=6 - 500 null text/plain; charset=utf-8 1018.3884ms
2025-06-17 19:58:41.134 +03:30 [INF] Executed PartialViewResult - view _Layer_Fields executed in 155.8434ms.
2025-06-17 19:58:41.136 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web) in 1100.9617ms
2025-06-17 19:58:41.138 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web)'
2025-06-17 19:58:41.140 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/DataBase/_Layer_Fields?id=6 - 200 null text/html; charset=utf-8 1120.6478ms
2025-06-17 19:59:09.412 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting?id=7 - null null
2025-06-17 19:59:09.447 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 19:59:09.449 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:59:09.480 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:59:09.488 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 19:59:09.495 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 12.4459ms.
2025-06-17 19:59:09.497 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-17 19:59:09.532 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 35.538ms.
2025-06-17 19:59:09.535 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 82.7424ms
2025-06-17 19:59:09.538 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 19:59:09.542 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting?id=7 - 200 null text/html; charset=utf-8 129.3829ms
2025-06-17 19:59:09.643 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 19:59:09.647 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 19:59:09.652 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 4.7529ms
2025-06-17 19:59:09.688 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 44.9253ms
2025-06-17 19:59:09.873 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/DataBase/_Layer_Relations?id=7 - null null
2025-06-17 19:59:09.874 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/DataBase/_Layer_Fields?id=7 - null null
2025-06-17 19:59:09.878 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web)'
2025-06-17 19:59:09.882 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web)'
2025-06-17 19:59:09.883 +03:30 [INF] Route matched with {action = "_Layer_Relations", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Relations(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:59:09.885 +03:30 [INF] Route matched with {action = "_Layer_Fields", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Fields(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 19:59:09.918 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:59:09.941 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 19:59:09.947 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-17 19:59:09.952 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 8.6007ms.
2025-06-17 19:59:09.952 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-17 19:59:09.957 +03:30 [INF] Executing PartialViewResult, running view _Layer_Relations.
2025-06-17 19:59:09.964 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 17.1311ms.
2025-06-17 19:59:09.964 +03:30 [ERR] The partial view '_Layer_Relations' was not found. Searched locations: ["/Views/Database/_Layer_Relations.cshtml","/Views/Shared/_Layer_Relations.cshtml"]
2025-06-17 19:59:09.967 +03:30 [INF] Executing PartialViewResult, running view _Layer_Fields.
2025-06-17 19:59:10.319 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web) in 429.5981ms
2025-06-17 19:59:10.319 +03:30 [INF] Executed PartialViewResult - view _Layer_Fields executed in 352.4857ms.
2025-06-17 19:59:10.390 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web)'
2025-06-17 19:59:10.391 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web) in 470.1514ms
2025-06-17 19:59:10.477 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The view '_Layer_Relations' was not found. The following locations were searched:
/Views/Database/_Layer_Relations.cshtml
/Views/Shared/_Layer_Relations.cshtml
   at Microsoft.AspNetCore.Mvc.ViewEngines.ViewEngineResult.EnsureSuccessful(IEnumerable`1 originalLocations)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.PartialViewResultExecutor.ExecuteAsync(ActionContext context, PartialViewResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-17 19:59:10.478 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web)'
2025-06-17 19:59:10.501 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/DataBase/_Layer_Fields?id=7 - 200 null text/html; charset=utf-8 627.6561ms
2025-06-17 19:59:10.502 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/DataBase/_Layer_Relations?id=7 - 500 null text/plain; charset=utf-8 628.9249ms
2025-06-17 19:59:18.578 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-17 19:59:18.578 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-17 19:59:18.581 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-17 19:59:18.585 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-17 19:59:18.588 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 10.4299ms
2025-06-17 19:59:18.591 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 12.3796ms
2025-06-17 20:01:21.783 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager - null null
2025-06-17 20:01:21.823 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:01:21.827 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:01:21.856 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:01:21.864 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:01:21.870 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 10.8169ms.
2025-06-17 20:01:21.874 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:01:21.921 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 47.5394ms.
2025-06-17 20:01:21.935 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 105.152ms
2025-06-17 20:01:21.937 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:01:21.941 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager - 200 null text/html; charset=utf-8 157.6967ms
2025-06-17 20:01:21.973 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:01:21.974 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:01:21.991 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 17.2688ms
2025-06-17 20:01:22.007 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 34.2766ms
2025-06-17 20:02:32.601 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager - null null
2025-06-17 20:02:32.646 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:02:32.651 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:02:32.690 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:02:32.696 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:02:32.700 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 5.8393ms.
2025-06-17 20:02:32.704 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:02:32.732 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 30.0737ms.
2025-06-17 20:02:32.745 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 90.1621ms
2025-06-17 20:02:32.747 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:02:32.749 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager - 200 null text/html; charset=utf-8 148.6455ms
2025-06-17 20:02:32.806 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:02:32.807 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:02:32.863 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 17.739ms
2025-06-17 20:02:32.868 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 61.4177ms
2025-06-17 20:02:45.381 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=6 - null null
2025-06-17 20:02:45.440 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:02:45.442 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:02:45.479 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:02:45.484 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:02:45.489 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 7.0134ms.
2025-06-17 20:02:45.491 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:02:45.494 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 2.9213ms.
2025-06-17 20:02:45.498 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 52.9019ms
2025-06-17 20:02:45.501 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:02:45.505 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=6 - 200 null text/html; charset=utf-8 123.3747ms
2025-06-17 20:02:45.559 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:02:45.560 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:02:45.567 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 7.4801ms
2025-06-17 20:02:45.571 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 12.0313ms
2025-06-17 20:02:45.690 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=6 - null null
2025-06-17 20:02:45.690 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=6 - null null
2025-06-17 20:02:45.694 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:02:45.699 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:02:45.705 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:02:45.707 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:02:45.740 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:02:45.764 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:02:45.836 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:02:45.836 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:02:45.845 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 74.4867ms.
2025-06-17 20:02:45.847 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 79.4734ms.
2025-06-17 20:02:45.853 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:02:45.859 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:02:45.872 +03:30 [INF] Executed PartialViewResult - view null executed in 21.1023ms.
2025-06-17 20:02:45.872 +03:30 [INF] Executed PartialViewResult - view null executed in 13.2979ms.
2025-06-17 20:02:45.885 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 142.4977ms
2025-06-17 20:02:45.888 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 179.3627ms
2025-06-17 20:02:45.891 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:02:45.892 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:02:45.894 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=6 - 200 null text/html; charset=utf-8 203.7867ms
2025-06-17 20:02:45.896 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=6 - 200 null text/html; charset=utf-8 205.8332ms
2025-06-17 20:02:48.239 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - null null
2025-06-17 20:02:48.275 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:02:48.277 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:02:48.311 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:02:48.315 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:02:48.323 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 9.0894ms.
2025-06-17 20:02:48.326 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:02:48.328 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 2.6053ms.
2025-06-17 20:02:48.331 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 50.4817ms
2025-06-17 20:02:48.336 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:02:48.339 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - 200 null text/html; charset=utf-8 99.9641ms
2025-06-17 20:02:48.384 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:02:48.385 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:02:48.393 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 9.0619ms
2025-06-17 20:02:48.395 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 9.9136ms
2025-06-17 20:02:48.512 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:02:48.516 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:02:48.520 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:02:48.543 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:02:48.549 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:02:48.580 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 33.4061ms.
2025-06-17 20:02:48.584 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:02:48.591 +03:30 [INF] Executed PartialViewResult - view null executed in 6.666ms.
2025-06-17 20:02:48.593 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:02:48.593 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 68.3072ms
2025-06-17 20:02:48.599 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:02:48.604 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:02:48.606 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:02:48.609 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 97.1373ms
2025-06-17 20:02:48.634 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:02:48.634 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - null null
2025-06-17 20:02:48.647 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:02:48.650 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - 404 0 null 15.8242ms
2025-06-17 20:02:48.658 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 12.5828ms.
2025-06-17 20:02:48.665 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Database/content/img/demo/64x64.png, Response status code: 404
2025-06-17 20:02:48.667 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:02:48.674 +03:30 [INF] Executed PartialViewResult - view null executed in 6.8461ms.
2025-06-17 20:02:48.675 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 63.05ms
2025-06-17 20:02:48.679 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:02:48.680 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 87.6487ms
2025-06-17 20:02:48.690 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - null null
2025-06-17 20:02:48.695 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - 404 0 null 5.0736ms
2025-06-17 20:02:48.700 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Database/content/img/demo/64x64.png, Response status code: 404
2025-06-17 20:04:22.067 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - null null
2025-06-17 20:04:22.105 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:04:22.108 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:04:22.137 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:04:22.141 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:04:22.146 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 5.877ms.
2025-06-17 20:04:22.149 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:04:22.166 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 17.5692ms.
2025-06-17 20:04:22.172 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 59.8434ms
2025-06-17 20:04:22.174 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:04:22.176 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - 200 null text/html; charset=utf-8 108.7651ms
2025-06-17 20:04:22.218 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:04:22.219 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:04:22.226 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 7.3534ms
2025-06-17 20:04:22.240 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 22.5494ms
2025-06-17 20:04:22.866 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:04:22.866 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:04:22.887 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:04:22.887 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:04:22.892 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:04:22.894 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:04:22.915 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:04:22.938 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:04:22.943 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:04:22.946 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:04:22.951 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 10.4275ms.
2025-06-17 20:04:22.962 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 17.4189ms.
2025-06-17 20:04:22.968 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:04:22.965 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:04:22.972 +03:30 [INF] Executed PartialViewResult - view null executed in 3.4682ms.
2025-06-17 20:04:22.975 +03:30 [INF] Executed PartialViewResult - view null executed in 10.2073ms.
2025-06-17 20:04:22.977 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 57.0085ms
2025-06-17 20:04:22.980 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 83.3033ms
2025-06-17 20:04:22.982 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:04:22.984 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:04:22.986 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 120.1427ms
2025-06-17 20:04:22.988 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 121.7707ms
2025-06-17 20:04:22.995 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - null null
2025-06-17 20:04:23.006 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - 404 0 null 11.0082ms
2025-06-17 20:04:23.014 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Database/content/img/demo/64x64.png, Response status code: 404
2025-06-17 20:04:26.443 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=6 - null null
2025-06-17 20:04:26.483 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:04:26.485 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:04:26.525 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:04:26.530 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:04:26.534 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 4.7801ms.
2025-06-17 20:04:26.536 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:04:26.539 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 2.5707ms.
2025-06-17 20:04:26.544 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 51.0699ms
2025-06-17 20:04:26.547 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:04:26.548 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=6 - 200 null text/html; charset=utf-8 104.8133ms
2025-06-17 20:04:26.601 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:04:26.602 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:04:26.615 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 13.4378ms
2025-06-17 20:04:26.620 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 19.266ms
2025-06-17 20:04:26.742 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=6 - null null
2025-06-17 20:04:26.742 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=6 - null null
2025-06-17 20:04:26.746 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:04:26.749 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:04:26.751 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:04:26.753 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:04:26.777 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:04:26.800 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:04:26.805 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:04:26.810 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:04:26.814 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 11.5455ms.
2025-06-17 20:04:26.821 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 13.2542ms.
2025-06-17 20:04:26.827 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:04:26.829 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:04:26.831 +03:30 [INF] Executed PartialViewResult - view null executed in 4.5899ms.
2025-06-17 20:04:26.835 +03:30 [INF] Executed PartialViewResult - view null executed in 5.529ms.
2025-06-17 20:04:26.840 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 82.8471ms
2025-06-17 20:04:26.842 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 61.2535ms
2025-06-17 20:04:26.844 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:04:26.846 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:04:26.848 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=6 - 200 null text/html; charset=utf-8 106.0113ms
2025-06-17 20:04:26.850 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=6 - 200 null text/html; charset=utf-8 107.4825ms
2025-06-17 20:04:30.263 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting - null null
2025-06-17 20:04:30.295 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:04:30.298 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:04:30.338 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:04:30.344 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:04:30.350 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 7.7479ms.
2025-06-17 20:04:30.354 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-17 20:04:30.380 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 26.8307ms.
2025-06-17 20:04:30.383 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 80.646ms
2025-06-17 20:04:30.387 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:04:30.390 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting - 200 null text/html; charset=utf-8 127.1869ms
2025-06-17 20:04:30.444 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:04:30.445 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:04:30.455 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 9.994ms
2025-06-17 20:04:30.455 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 11.6746ms
2025-06-17 20:04:32.833 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting?id=6 - null null
2025-06-17 20:04:32.879 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:04:32.882 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:04:32.915 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:04:32.921 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:04:32.925 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 5.7662ms.
2025-06-17 20:04:32.927 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-17 20:04:32.930 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 3.4344ms.
2025-06-17 20:04:32.936 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 48.147ms
2025-06-17 20:04:32.940 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:04:32.942 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting?id=6 - 200 null text/html; charset=utf-8 109.7713ms
2025-06-17 20:04:33.003 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:04:33.005 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:04:33.034 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 29.1786ms
2025-06-17 20:04:33.053 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 50.8397ms
2025-06-17 20:04:33.158 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/DataBase/_Layer_Relations?id=6 - null null
2025-06-17 20:04:33.161 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/DataBase/_Layer_Fields?id=6 - null null
2025-06-17 20:04:33.163 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web)'
2025-06-17 20:04:33.174 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web)'
2025-06-17 20:04:33.180 +03:30 [INF] Route matched with {action = "_Layer_Relations", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Relations(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:04:33.183 +03:30 [INF] Route matched with {action = "_Layer_Fields", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Fields(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:04:33.217 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:04:33.255 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:04:33.290 +03:30 [INF] Executed DbCommand (33ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-17 20:04:33.294 +03:30 [INF] Executed DbCommand (34ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-17 20:04:33.297 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 40.7625ms.
2025-06-17 20:04:33.306 +03:30 [INF] Executing PartialViewResult, running view _Layer_Relations.
2025-06-17 20:04:33.307 +03:30 [ERR] The partial view '_Layer_Relations' was not found. Searched locations: ["/Views/Database/_Layer_Relations.cshtml","/Views/Shared/_Layer_Relations.cshtml"]
2025-06-17 20:04:33.304 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 45.2942ms.
2025-06-17 20:04:33.547 +03:30 [INF] Executing PartialViewResult, running view _Layer_Fields.
2025-06-17 20:04:33.589 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web) in 403.7974ms
2025-06-17 20:04:33.592 +03:30 [INF] Executed PartialViewResult - view _Layer_Fields executed in 62.9196ms.
2025-06-17 20:04:33.658 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web)'
2025-06-17 20:04:33.660 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web) in 434.3876ms
2025-06-17 20:04:33.754 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The view '_Layer_Relations' was not found. The following locations were searched:
/Views/Database/_Layer_Relations.cshtml
/Views/Shared/_Layer_Relations.cshtml
   at Microsoft.AspNetCore.Mvc.ViewEngines.ViewEngineResult.EnsureSuccessful(IEnumerable`1 originalLocations)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.PartialViewResultExecutor.ExecuteAsync(ActionContext context, PartialViewResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-17 20:04:33.755 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web)'
2025-06-17 20:04:33.794 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/DataBase/_Layer_Fields?id=6 - 200 null text/html; charset=utf-8 633.5158ms
2025-06-17 20:04:33.794 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/DataBase/_Layer_Relations?id=6 - 500 null text/plain; charset=utf-8 635.7138ms
2025-06-17 20:05:15.412 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-17 20:05:15.412 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-17 20:05:15.437 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-17 20:05:15.440 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-17 20:05:15.441 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 29.3589ms
2025-06-17 20:05:15.443 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 31.3172ms
2025-06-17 20:08:29.527 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting?id=6 - null null
2025-06-17 20:08:29.577 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:08:29.580 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:08:29.621 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:08:29.629 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:08:29.634 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 9.7747ms.
2025-06-17 20:08:29.638 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-17 20:08:29.680 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 43.9148ms.
2025-06-17 20:08:29.700 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 116.7936ms
2025-06-17 20:08:29.707 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:08:29.711 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting?id=6 - 200 null text/html; charset=utf-8 183.8508ms
2025-06-17 20:08:29.855 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:08:29.856 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:08:29.862 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 5.7486ms
2025-06-17 20:08:29.864 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 8.6358ms
2025-06-17 20:08:30.008 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/DataBase/_Layer_Relations?id=6 - null null
2025-06-17 20:08:30.011 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/DataBase/_Layer_Fields?id=6 - null null
2025-06-17 20:08:30.027 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web)'
2025-06-17 20:08:30.027 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web)'
2025-06-17 20:08:30.034 +03:30 [INF] Route matched with {action = "_Layer_Relations", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Relations(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:08:30.035 +03:30 [INF] Route matched with {action = "_Layer_Fields", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Fields(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:08:30.060 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:08:30.083 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:08:30.086 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-17 20:08:30.091 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-17 20:08:30.091 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 6.9402ms.
2025-06-17 20:08:30.099 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 11.7677ms.
2025-06-17 20:08:30.103 +03:30 [INF] Executing PartialViewResult, running view _Layer_Relations.
2025-06-17 20:08:30.106 +03:30 [INF] Executing PartialViewResult, running view _Layer_Fields.
2025-06-17 20:08:30.107 +03:30 [ERR] The partial view '_Layer_Relations' was not found. Searched locations: ["/Views/Database/_Layer_Relations.cshtml","/Views/Shared/_Layer_Relations.cshtml"]
2025-06-17 20:08:30.111 +03:30 [INF] Executed PartialViewResult - view _Layer_Fields executed in 5.1405ms.
2025-06-17 20:08:30.370 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web) in 306.8195ms
2025-06-17 20:08:30.458 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web) in 418.8046ms
2025-06-17 20:08:30.459 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web)'
2025-06-17 20:08:30.539 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web)'
2025-06-17 20:08:30.540 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/DataBase/_Layer_Fields?id=6 - 200 null text/html; charset=utf-8 528.7122ms
2025-06-17 20:08:30.628 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The view '_Layer_Relations' was not found. The following locations were searched:
/Views/Database/_Layer_Relations.cshtml
/Views/Shared/_Layer_Relations.cshtml
   at Microsoft.AspNetCore.Mvc.ViewEngines.ViewEngineResult.EnsureSuccessful(IEnumerable`1 originalLocations)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.PartialViewResultExecutor.ExecuteAsync(ActionContext context, PartialViewResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-17 20:08:30.653 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/DataBase/_Layer_Relations?id=6 - 500 null text/plain; charset=utf-8 645.5781ms
2025-06-17 20:08:30.749 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-17 20:08:30.753 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-17 20:08:30.755 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 5.6644ms
2025-06-17 20:08:30.773 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-17 20:08:30.779 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-17 20:08:30.781 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 7.8817ms
2025-06-17 20:08:35.440 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting?id=7 - null null
2025-06-17 20:08:35.479 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:08:35.482 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:08:35.510 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:08:35.515 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:08:35.520 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 5.8112ms.
2025-06-17 20:08:35.522 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-17 20:08:35.525 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 2.9194ms.
2025-06-17 20:08:35.527 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 42.3883ms
2025-06-17 20:08:35.530 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:08:35.534 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting?id=7 - 200 null text/html; charset=utf-8 94.1556ms
2025-06-17 20:08:35.598 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:08:35.598 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:08:35.605 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 6.804ms
2025-06-17 20:08:35.607 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 9.8959ms
2025-06-17 20:08:35.717 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/DataBase/_Layer_Relations?id=7 - null null
2025-06-17 20:08:35.721 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/DataBase/_Layer_Fields?id=7 - null null
2025-06-17 20:08:35.722 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web)'
2025-06-17 20:08:35.726 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web)'
2025-06-17 20:08:35.728 +03:30 [INF] Route matched with {action = "_Layer_Relations", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Relations(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:08:35.732 +03:30 [INF] Route matched with {action = "_Layer_Fields", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Fields(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:08:35.758 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:08:35.784 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:08:35.788 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-17 20:08:35.791 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tableId_0
2025-06-17 20:08:35.795 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 9.1638ms.
2025-06-17 20:08:35.804 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 14.4769ms.
2025-06-17 20:08:35.806 +03:30 [INF] Executing PartialViewResult, running view _Layer_Relations.
2025-06-17 20:08:35.811 +03:30 [INF] Executing PartialViewResult, running view _Layer_Fields.
2025-06-17 20:08:35.813 +03:30 [ERR] The partial view '_Layer_Relations' was not found. Searched locations: ["/Views/Database/_Layer_Relations.cshtml","/Views/Shared/_Layer_Relations.cshtml"]
2025-06-17 20:08:35.816 +03:30 [INF] Executed PartialViewResult - view _Layer_Fields executed in 4.618ms.
2025-06-17 20:08:35.991 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web) in 228.2355ms
2025-06-17 20:08:36.066 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web) in 329.4186ms
2025-06-17 20:08:36.067 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Fields (BaseGIS.Web)'
2025-06-17 20:08:36.129 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Relations (BaseGIS.Web)'
2025-06-17 20:08:36.130 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/DataBase/_Layer_Fields?id=7 - 200 null text/html; charset=utf-8 408.5752ms
2025-06-17 20:08:36.211 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The view '_Layer_Relations' was not found. The following locations were searched:
/Views/Database/_Layer_Relations.cshtml
/Views/Shared/_Layer_Relations.cshtml
   at Microsoft.AspNetCore.Mvc.ViewEngines.ViewEngineResult.EnsureSuccessful(IEnumerable`1 originalLocations)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.PartialViewResultExecutor.ExecuteAsync(ActionContext context, PartialViewResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeResultAsync>g__Logged|22_0(ResourceInvoker invoker, IActionResult result)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-17 20:08:36.237 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/DataBase/_Layer_Relations?id=7 - 500 null text/plain; charset=utf-8 519.3044ms
2025-06-17 20:08:46.139 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager - null null
2025-06-17 20:08:46.174 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:08:46.179 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:08:46.208 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:08:46.213 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:08:46.217 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 4.6297ms.
2025-06-17 20:08:46.219 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:08:46.248 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 29.5521ms.
2025-06-17 20:08:46.252 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 68.493ms
2025-06-17 20:08:46.254 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:08:46.257 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager - 200 null text/html; charset=utf-8 117.8212ms
2025-06-17 20:08:46.314 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:08:46.314 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:08:46.322 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 8.5849ms
2025-06-17 20:08:46.323 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 9.3131ms
2025-06-17 20:08:48.705 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=6 - null null
2025-06-17 20:08:48.737 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:08:48.741 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:08:48.770 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:08:48.775 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:08:48.779 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 5.7645ms.
2025-06-17 20:08:48.781 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:08:48.785 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 3.9771ms.
2025-06-17 20:08:48.787 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 42.2189ms
2025-06-17 20:08:48.789 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:08:48.792 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=6 - 200 null text/html; charset=utf-8 86.5907ms
2025-06-17 20:08:48.837 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:08:48.838 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:08:48.846 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 8.0445ms
2025-06-17 20:08:48.848 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 11.1806ms
2025-06-17 20:08:48.966 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=6 - null null
2025-06-17 20:08:48.968 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=6 - null null
2025-06-17 20:08:48.970 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:08:48.975 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:08:48.979 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:08:48.981 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:08:49.005 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:08:49.031 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:08:49.035 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:08:49.038 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:08:49.043 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 9.9139ms.
2025-06-17 20:08:49.052 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 15.5446ms.
2025-06-17 20:08:49.055 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:08:49.057 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:08:49.061 +03:30 [INF] Executed PartialViewResult - view null executed in 6.8469ms.
2025-06-17 20:08:49.061 +03:30 [INF] Executed PartialViewResult - view null executed in 4.143ms.
2025-06-17 20:08:49.066 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 80.5529ms
2025-06-17 20:08:49.068 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 59.1578ms
2025-06-17 20:08:49.069 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:08:49.071 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:08:49.073 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=6 - 200 null text/html; charset=utf-8 107.0934ms
2025-06-17 20:08:49.075 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=6 - 200 null text/html; charset=utf-8 107.3709ms
2025-06-17 20:08:51.068 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - null null
2025-06-17 20:08:51.106 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:08:51.108 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:08:51.144 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:08:51.149 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:08:51.153 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 5.9362ms.
2025-06-17 20:08:51.155 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:08:51.161 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 5.5949ms.
2025-06-17 20:08:51.166 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 51.9163ms
2025-06-17 20:08:51.168 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:08:51.170 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - 200 null text/html; charset=utf-8 101.9656ms
2025-06-17 20:08:51.238 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:08:51.244 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:08:51.252 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 8.0311ms
2025-06-17 20:08:51.262 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 24.0075ms
2025-06-17 20:08:51.400 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:08:51.402 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:08:51.407 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:08:51.403 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:08:51.409 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:08:51.411 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:08:51.463 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:08:51.490 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:08:51.495 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:08:51.497 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:08:51.503 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 10.8003ms.
2025-06-17 20:08:51.510 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 14.2962ms.
2025-06-17 20:08:51.514 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:08:51.518 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:08:51.521 +03:30 [INF] Executed PartialViewResult - view null executed in 6.6976ms.
2025-06-17 20:08:51.522 +03:30 [INF] Executed PartialViewResult - view null executed in 4.0809ms.
2025-06-17 20:08:51.524 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 109.946ms
2025-06-17 20:08:51.527 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 60.4569ms
2025-06-17 20:08:51.529 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:08:51.533 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:08:51.535 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 132.8885ms
2025-06-17 20:08:51.537 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 137.2343ms
2025-06-17 20:08:51.547 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - null null
2025-06-17 20:08:51.554 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - 404 0 null 7.1709ms
2025-06-17 20:08:51.560 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Database/content/img/demo/64x64.png, Response status code: 404
2025-06-17 20:10:46.261 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - null null
2025-06-17 20:10:46.297 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:10:46.301 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:10:46.326 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:10:46.332 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:10:46.339 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 8.5725ms.
2025-06-17 20:10:46.345 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:10:46.370 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 27.2312ms.
2025-06-17 20:10:46.374 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 69.9398ms
2025-06-17 20:10:46.378 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:10:46.380 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - 200 null text/html; charset=utf-8 119.6107ms
2025-06-17 20:10:46.427 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:10:46.428 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:10:46.435 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 7.3538ms
2025-06-17 20:10:46.483 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 56.0077ms
2025-06-17 20:10:47.436 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:10:47.437 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:10:47.450 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:10:47.450 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:10:47.456 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:10:47.458 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:10:47.476 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:10:47.495 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:10:47.500 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:10:47.503 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:10:47.509 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 10.5369ms.
2025-06-17 20:10:47.514 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 11.7105ms.
2025-06-17 20:10:47.517 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:10:47.522 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:10:47.524 +03:30 [INF] Executed PartialViewResult - view null executed in 7.9899ms.
2025-06-17 20:10:47.525 +03:30 [INF] Executed PartialViewResult - view null executed in 3.0173ms.
2025-06-17 20:10:47.527 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 64.9786ms
2025-06-17 20:10:47.529 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 48.7387ms
2025-06-17 20:10:47.530 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:10:47.531 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:10:47.535 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 98.5621ms
2025-06-17 20:10:47.537 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 100.0299ms
2025-06-17 20:10:47.543 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - null null
2025-06-17 20:10:47.551 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - 404 0 null 8.3738ms
2025-06-17 20:10:47.556 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Database/content/img/demo/64x64.png, Response status code: 404
2025-06-17 20:10:59.834 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-17 20:10:59.834 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-17 20:10:59.839 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-17 20:10:59.842 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-17 20:10:59.844 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 10.7223ms
2025-06-17 20:10:59.846 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 12.1402ms
2025-06-17 20:15:48.727 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting - null null
2025-06-17 20:15:48.788 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:15:48.797 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:15:48.835 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:15:48.869 +03:30 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:15:48.877 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 39.2247ms.
2025-06-17 20:15:48.881 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-17 20:15:48.890 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 10.1712ms.
2025-06-17 20:15:48.905 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 104.1097ms
2025-06-17 20:15:48.908 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:15:48.910 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting - 200 null text/html; charset=utf-8 184.4092ms
2025-06-17 20:15:48.977 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:15:48.978 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:15:48.987 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 9.4508ms
2025-06-17 20:15:49.007 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 29.9726ms
2025-06-17 20:16:00.586 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-17 20:16:00.586 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-17 20:16:00.592 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-17 20:16:00.596 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-17 20:16:00.599 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 12.9829ms
2025-06-17 20:16:00.601 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 14.9413ms
2025-06-17 20:20:13.486 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting - null null
2025-06-17 20:20:13.542 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:20:13.548 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:20:13.578 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:20:13.590 +03:30 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:20:13.599 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 17.0553ms.
2025-06-17 20:20:13.602 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-17 20:20:13.638 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 36.7067ms.
2025-06-17 20:20:13.640 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 87.9564ms
2025-06-17 20:20:13.644 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:20:13.646 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting - 200 null text/html; charset=utf-8 159.3492ms
2025-06-17 20:20:13.762 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:20:13.765 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:20:13.780 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 15.9574ms
2025-06-17 20:20:13.786 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 24.5999ms
2025-06-17 20:20:14.337 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-17 20:20:14.337 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-17 20:20:14.342 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-17 20:20:14.347 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-17 20:20:14.349 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 11.8537ms
2025-06-17 20:20:14.351 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 13.6466ms
2025-06-17 20:20:39.951 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager - null null
2025-06-17 20:20:39.993 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:20:39.998 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:20:40.024 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:20:40.032 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:20:40.040 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 11.5566ms.
2025-06-17 20:20:40.046 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:20:40.069 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 24.1445ms.
2025-06-17 20:20:40.073 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 71.3165ms
2025-06-17 20:20:40.075 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:20:40.077 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager - 200 null text/html; charset=utf-8 126.1065ms
2025-06-17 20:20:40.136 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:20:40.136 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:20:40.145 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 8.9466ms
2025-06-17 20:20:40.149 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 13.8088ms
2025-06-17 20:20:45.723 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting - null null
2025-06-17 20:20:45.756 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:20:45.758 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:20:45.788 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:20:45.794 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:20:45.800 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 7.1329ms.
2025-06-17 20:20:45.802 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-17 20:20:45.805 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 2.9956ms.
2025-06-17 20:20:45.810 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 45.4493ms
2025-06-17 20:20:45.814 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:20:45.816 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting - 200 null text/html; charset=utf-8 93.6908ms
2025-06-17 20:20:45.867 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:20:45.868 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:20:45.881 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 12.5981ms
2025-06-17 20:20:45.882 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 14.7738ms
2025-06-17 20:22:54.648 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting - null null
2025-06-17 20:22:54.697 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:22:54.701 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:22:54.727 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:22:54.735 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:22:54.741 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 10.37ms.
2025-06-17 20:22:54.745 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-17 20:22:54.780 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 37.0468ms.
2025-06-17 20:22:54.783 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 79.0389ms
2025-06-17 20:22:54.787 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:22:54.790 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting - 200 null text/html; charset=utf-8 142.1559ms
2025-06-17 20:22:54.850 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:22:54.852 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:22:54.861 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 10.9252ms
2025-06-17 20:22:54.862 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 10.0473ms
2025-06-17 20:25:10.827 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager - null null
2025-06-17 20:25:10.872 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:25:10.875 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:25:10.909 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:25:10.918 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:25:10.924 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 12.5437ms.
2025-06-17 20:25:10.927 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:25:10.934 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 7.7811ms.
2025-06-17 20:25:10.950 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 71.559ms
2025-06-17 20:25:10.953 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:25:10.956 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager - 200 null text/html; charset=utf-8 128.3441ms
2025-06-17 20:25:11.005 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:25:11.005 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:25:11.047 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 42.2329ms
2025-06-17 20:25:11.054 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 49.5717ms
2025-06-17 20:29:43.148 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - null null
2025-06-17 20:29:43.196 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:29:43.199 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:29:43.226 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:29:43.235 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:29:43.240 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 10.0806ms.
2025-06-17 20:29:43.242 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:29:43.246 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 4.0991ms.
2025-06-17 20:29:43.267 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 64.4912ms
2025-06-17 20:29:43.269 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:29:43.271 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - 200 null text/html; charset=utf-8 122.5168ms
2025-06-17 20:29:43.310 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:29:43.311 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:29:43.329 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 17.5301ms
2025-06-17 20:29:43.359 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 48.638ms
2025-06-17 20:29:43.450 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:29:43.451 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:29:43.454 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:29:43.458 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:29:43.463 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:29:43.464 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:29:43.492 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:29:43.541 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:29:43.551 +03:30 [INF] Executed DbCommand (6ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:29:43.556 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:29:43.561 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 16.5926ms.
2025-06-17 20:29:43.563 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 14.8181ms.
2025-06-17 20:29:43.570 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:29:43.577 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:29:43.583 +03:30 [INF] Executed PartialViewResult - view null executed in 5.5785ms.
2025-06-17 20:29:43.586 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 88.2363ms
2025-06-17 20:29:43.595 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:29:43.583 +03:30 [INF] Executed PartialViewResult - view null executed in 12.776ms.
2025-06-17 20:29:43.596 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 145.7276ms
2025-06-17 20:29:43.598 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 130.1461ms
2025-06-17 20:29:43.614 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:29:43.616 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 165.5664ms
2025-06-17 20:29:43.616 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - null null
2025-06-17 20:29:43.627 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - 404 0 null 11.0267ms
2025-06-17 20:29:43.633 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Database/content/img/demo/64x64.png, Response status code: 404
2025-06-17 20:29:51.616 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-17 20:29:51.647 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-17 20:29:51.651 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:29:51.682 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:29:51.688 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.029ms.
2025-06-17 20:29:51.694 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-17 20:29:51.698 +03:30 [INF] Executed ViewResult - view Insert executed in 4.4131ms.
2025-06-17 20:29:51.702 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 46.9126ms
2025-06-17 20:29:51.706 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-17 20:29:51.709 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 93.1323ms
2025-06-17 20:29:51.762 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:29:51.763 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:29:51.772 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 8.6734ms
2025-06-17 20:29:51.773 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 11.2494ms
2025-06-17 20:29:51.878 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1750179591794 - null null
2025-06-17 20:29:51.883 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-17 20:29:51.888 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:29:51.919 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:29:51.928 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-17 20:29:51.933 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:29:51.942 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 20.3789ms.
2025-06-17 20:29:51.946 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-17 20:29:51.949 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 4.12ms.
2025-06-17 20:29:51.952 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 58.3958ms
2025-06-17 20:29:51.957 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-17 20:29:51.958 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1750179591794 - 200 null text/html; charset=utf-8 80.3504ms
2025-06-17 20:32:42.088 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-17 20:32:42.314 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-17 20:32:42.319 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:32:42.361 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:32:42.365 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.037ms.
2025-06-17 20:32:42.370 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-17 20:32:42.400 +03:30 [INF] Executed ViewResult - view Insert executed in 32.0597ms.
2025-06-17 20:32:42.404 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 82.9424ms
2025-06-17 20:32:42.408 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-17 20:32:42.410 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 330.6016ms
2025-06-17 20:32:42.452 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:32:42.453 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:32:42.461 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 8.2926ms
2025-06-17 20:32:42.476 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 23.7564ms
2025-06-17 20:32:42.556 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1750179762485 - null null
2025-06-17 20:32:42.574 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-17 20:32:42.579 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:32:42.604 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:32:42.615 +03:30 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-17 20:32:42.625 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:32:42.632 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 23.988ms.
2025-06-17 20:32:42.634 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-17 20:32:42.637 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 3.2556ms.
2025-06-17 20:32:42.641 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 57.267ms
2025-06-17 20:32:42.644 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-17 20:32:42.647 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1750179762485 - 200 null text/html; charset=utf-8 91.0169ms
2025-06-17 20:32:45.568 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager - null null
2025-06-17 20:32:45.602 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:32:45.606 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:32:45.631 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:32:45.636 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:32:45.639 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 4.378ms.
2025-06-17 20:32:45.644 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:32:45.647 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 3.7737ms.
2025-06-17 20:32:45.650 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 39.7037ms
2025-06-17 20:32:45.652 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:32:45.655 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager - 200 null text/html; charset=utf-8 87.5888ms
2025-06-17 20:32:45.703 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:32:45.703 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:32:45.713 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 9.3589ms
2025-06-17 20:32:45.714 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 10.4214ms
2025-06-17 20:32:49.612 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Update - null null
2025-06-17 20:32:49.640 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web)'
2025-06-17 20:32:49.645 +03:30 [INF] Route matched with {action = "Update", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Update() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:32:49.667 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:32:49.672 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0142ms.
2025-06-17 20:32:49.678 +03:30 [INF] Executing ViewResult, running view Update.
2025-06-17 20:32:49.682 +03:30 [INF] Executed ViewResult - view Update executed in 4.2259ms.
2025-06-17 20:32:49.685 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web) in 37.489ms
2025-06-17 20:32:49.689 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web)'
2025-06-17 20:32:49.693 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Update - 200 null text/html; charset=utf-8 81.2296ms
2025-06-17 20:32:49.751 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:32:49.752 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:32:49.763 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 11.5419ms
2025-06-17 20:32:49.763 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 11.2202ms
2025-06-17 20:32:49.848 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Update_List?_=1750179769780 - null null
2025-06-17 20:32:49.853 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web)'
2025-06-17 20:32:49.856 +03:30 [INF] Route matched with {action = "_Update_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Update_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:32:49.878 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:32:49.882 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-17 20:32:49.888 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:32:49.898 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 17.018ms.
2025-06-17 20:32:49.901 +03:30 [INF] Executing PartialViewResult, running view _Update_List.
2025-06-17 20:32:49.904 +03:30 [INF] Executed PartialViewResult - view _Update_List executed in 3.394ms.
2025-06-17 20:32:49.906 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web) in 44.6369ms
2025-06-17 20:32:49.911 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web)'
2025-06-17 20:32:49.915 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Update_List?_=1750179769780 - 200 null text/html; charset=utf-8 66.1986ms
2025-06-17 20:34:37.252 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Update - null null
2025-06-17 20:34:37.288 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web)'
2025-06-17 20:34:37.292 +03:30 [INF] Route matched with {action = "Update", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Update() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:34:37.320 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:34:37.325 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0209ms.
2025-06-17 20:34:37.329 +03:30 [INF] Executing ViewResult, running view Update.
2025-06-17 20:34:37.335 +03:30 [INF] Executed ViewResult - view Update executed in 7.0805ms.
2025-06-17 20:34:37.352 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web) in 55.0618ms
2025-06-17 20:34:37.356 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web)'
2025-06-17 20:34:37.359 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Update - 200 null text/html; charset=utf-8 107.4801ms
2025-06-17 20:34:37.400 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:34:37.403 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:34:37.410 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 6.7815ms
2025-06-17 20:34:37.411 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 11.5568ms
2025-06-17 20:34:38.237 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Update_List?_=1750179877438 - null null
2025-06-17 20:34:38.259 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web)'
2025-06-17 20:34:38.264 +03:30 [INF] Route matched with {action = "_Update_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Update_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:34:38.292 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:34:38.301 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-17 20:34:38.312 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:34:38.318 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 23.7077ms.
2025-06-17 20:34:38.321 +03:30 [INF] Executing PartialViewResult, running view _Update_List.
2025-06-17 20:34:38.491 +03:30 [INF] Executed PartialViewResult - view _Update_List executed in 170.5514ms.
2025-06-17 20:34:38.493 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web) in 220.443ms
2025-06-17 20:34:38.495 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web)'
2025-06-17 20:34:38.496 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Update_List?_=1750179877438 - 200 null text/html; charset=utf-8 259.1973ms
2025-06-17 20:35:31.468 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Update - null null
2025-06-17 20:35:31.502 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web)'
2025-06-17 20:35:31.506 +03:30 [INF] Route matched with {action = "Update", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Update() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:35:31.537 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:35:31.541 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0199ms.
2025-06-17 20:35:31.545 +03:30 [INF] Executing ViewResult, running view Update.
2025-06-17 20:35:31.556 +03:30 [INF] Executed ViewResult - view Update executed in 11.8599ms.
2025-06-17 20:35:31.573 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web) in 62.6463ms
2025-06-17 20:35:31.576 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Update (BaseGIS.Web)'
2025-06-17 20:35:31.578 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Update - 200 null text/html; charset=utf-8 109.588ms
2025-06-17 20:35:31.617 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:35:31.618 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:35:31.641 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 23.6607ms
2025-06-17 20:35:31.643 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 25.6982ms
2025-06-17 20:35:31.720 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Update_List?_=1750179931644 - null null
2025-06-17 20:35:31.738 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web)'
2025-06-17 20:35:31.742 +03:30 [INF] Route matched with {action = "_Update_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Update_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:35:31.765 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:35:31.773 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-17 20:35:31.786 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:35:31.791 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 23.0559ms.
2025-06-17 20:35:31.797 +03:30 [INF] Executing PartialViewResult, running view _Update_List.
2025-06-17 20:35:31.927 +03:30 [INF] Executed PartialViewResult - view _Update_List executed in 132.1742ms.
2025-06-17 20:35:31.929 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web) in 184.3257ms
2025-06-17 20:35:31.931 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_List (BaseGIS.Web)'
2025-06-17 20:35:31.934 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Update_List?_=1750179931644 - 200 null text/html; charset=utf-8 213.1957ms
2025-06-17 20:35:43.711 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Update_Wizard?id=6&_=1750179931645 - null null
2025-06-17 20:35:43.715 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_Wizard (BaseGIS.Web)'
2025-06-17 20:35:43.720 +03:30 [INF] Route matched with {action = "_Update_Wizard", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Update_Wizard(System.String, System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:35:43.742 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Update_Wizard (BaseGIS.Web) - Validation state: "Invalid"
2025-06-17 20:35:43.772 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__ids_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__ids_0
2025-06-17 20:35:43.780 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Update_Wizard (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 33.9419ms.
2025-06-17 20:35:43.784 +03:30 [INF] Executing PartialViewResult, running view _Update_Wizard.
2025-06-17 20:35:43.817 +03:30 [INF] Executed PartialViewResult - view null executed in 33.186ms.
2025-06-17 20:35:43.820 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Update_Wizard (BaseGIS.Web) in 97.0438ms
2025-06-17 20:35:43.823 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Update_Wizard (BaseGIS.Web)'
2025-06-17 20:35:43.824 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Update_Wizard?id=6&_=1750179931645 - 200 null text/html; charset=utf-8 113.6483ms
2025-06-17 20:35:51.973 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Insert - null null
2025-06-17 20:35:52.005 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-17 20:35:52.009 +03:30 [INF] Route matched with {action = "Insert", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Insert() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:35:52.036 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:35:52.039 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0162ms.
2025-06-17 20:35:52.042 +03:30 [INF] Executing ViewResult, running view Insert.
2025-06-17 20:35:52.046 +03:30 [INF] Executed ViewResult - view Insert executed in 4.6594ms.
2025-06-17 20:35:52.048 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web) in 35.3801ms
2025-06-17 20:35:52.053 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Insert (BaseGIS.Web)'
2025-06-17 20:35:52.056 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Insert - 200 null text/html; charset=utf-8 83.1142ms
2025-06-17 20:35:52.110 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:35:52.111 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:35:52.135 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 24.3619ms
2025-06-17 20:35:52.136 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 26.1579ms
2025-06-17 20:35:52.218 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1750179952135 - null null
2025-06-17 20:35:52.225 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-17 20:35:52.233 +03:30 [INF] Route matched with {action = "_Insert_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult _Insert_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:35:52.257 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:35:52.260 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-17 20:35:52.265 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:35:52.271 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 12.5197ms.
2025-06-17 20:35:52.274 +03:30 [INF] Executing PartialViewResult, running view _Insert_List.
2025-06-17 20:35:52.411 +03:30 [INF] Executed PartialViewResult - view _Insert_List executed in 137.5687ms.
2025-06-17 20:35:52.413 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web) in 173.9171ms
2025-06-17 20:35:52.415 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Insert_List (BaseGIS.Web)'
2025-06-17 20:35:52.416 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Insert_List?_=1750179952135 - 200 null text/html; charset=utf-8 198.3177ms
2025-06-17 20:35:59.349 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Delete - null null
2025-06-17 20:35:59.385 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Delete (BaseGIS.Web)'
2025-06-17 20:35:59.484 +03:30 [INF] Route matched with {action = "Delete", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult Delete() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:35:59.516 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Delete (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:35:59.632 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Delete (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 112.071ms.
2025-06-17 20:35:59.670 +03:30 [INF] Executing ViewResult, running view Delete.
2025-06-17 20:35:59.710 +03:30 [INF] Executed ViewResult - view Delete executed in 74.6067ms.
2025-06-17 20:35:59.713 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Delete (BaseGIS.Web) in 221.3835ms
2025-06-17 20:35:59.717 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Delete (BaseGIS.Web)'
2025-06-17 20:35:59.720 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Delete - 200 null text/html; charset=utf-8 370.8402ms
2025-06-17 20:35:59.772 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:35:59.772 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:35:59.781 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 9.5848ms
2025-06-17 20:35:59.781 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 9.3568ms
2025-06-17 20:35:59.870 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Delete_List?_=1750179959806 - null null
2025-06-17 20:35:59.876 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Delete_List (BaseGIS.Web)'
2025-06-17 20:35:59.880 +03:30 [INF] Route matched with {action = "_Delete_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Delete_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:35:59.907 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Delete_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:35:59.915 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-17 20:35:59.925 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:35:59.933 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Delete_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 22.75ms.
2025-06-17 20:35:59.937 +03:30 [INF] Executing PartialViewResult, running view _Delete_List.
2025-06-17 20:36:00.077 +03:30 [INF] Executed PartialViewResult - view _Delete_List executed in 141.2347ms.
2025-06-17 20:36:00.081 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Delete_List (BaseGIS.Web) in 195.2011ms
2025-06-17 20:36:00.082 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Delete_List (BaseGIS.Web)'
2025-06-17 20:36:00.084 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Delete_List?_=1750179959806 - 200 null text/html; charset=utf-8 213.5846ms
2025-06-17 20:36:12.756 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Delete_Query?id=6&_=1750179959807 - null null
2025-06-17 20:36:12.762 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Delete_Query (BaseGIS.Web)'
2025-06-17 20:36:12.764 +03:30 [INF] Route matched with {action = "_Delete_Query", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Delete_Query(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:36:12.783 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Delete_Query (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:36:12.800 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__ids_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__ids_0
2025-06-17 20:36:12.893 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Delete_Query (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 106.3635ms.
2025-06-17 20:36:12.899 +03:30 [INF] Executing PartialViewResult, running view _Delete_Query.
2025-06-17 20:36:12.995 +03:30 [INF] Executed PartialViewResult - view null executed in 97.19ms.
2025-06-17 20:36:12.998 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Delete_Query (BaseGIS.Web) in 231.465ms
2025-06-17 20:36:13.000 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Delete_Query (BaseGIS.Web)'
2025-06-17 20:36:13.001 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Delete_Query?id=6&_=1750179959807 - 200 null text/html; charset=utf-8 245.5844ms
2025-06-17 20:37:04.281 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/Delete - null null
2025-06-17 20:37:04.330 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.Delete (BaseGIS.Web)'
2025-06-17 20:37:04.333 +03:30 [INF] Route matched with {action = "Delete", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult Delete() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:37:04.363 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.Delete (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:37:04.366 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.Delete (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0247ms.
2025-06-17 20:37:04.371 +03:30 [INF] Executing ViewResult, running view Delete.
2025-06-17 20:37:04.391 +03:30 [INF] Executed ViewResult - view Delete executed in 21.1468ms.
2025-06-17 20:37:04.392 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.Delete (BaseGIS.Web) in 56.0879ms
2025-06-17 20:37:04.394 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.Delete (BaseGIS.Web)'
2025-06-17 20:37:04.396 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/Delete - 200 null text/html; charset=utf-8 114.8288ms
2025-06-17 20:37:04.457 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:37:04.457 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:37:04.462 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 5.311ms
2025-06-17 20:37:04.467 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 10.0886ms
2025-06-17 20:37:04.589 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Delete_List?_=1750180024486 - null null
2025-06-17 20:37:04.620 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Delete_List (BaseGIS.Web)'
2025-06-17 20:37:04.628 +03:30 [INF] Route matched with {action = "_Delete_List", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Delete_List() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:37:04.663 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Delete_List (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:37:04.676 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[UserName] IS NULL
2025-06-17 20:37:04.691 +03:30 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:37:04.697 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Delete_List (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 26.6841ms.
2025-06-17 20:37:04.702 +03:30 [INF] Executing PartialViewResult, running view _Delete_List.
2025-06-17 20:37:04.835 +03:30 [INF] Executed PartialViewResult - view _Delete_List executed in 135.2863ms.
2025-06-17 20:37:04.838 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Delete_List (BaseGIS.Web) in 199.1969ms
2025-06-17 20:37:04.840 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Delete_List (BaseGIS.Web)'
2025-06-17 20:37:04.841 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Delete_List?_=1750180024486 - 200 null text/html; charset=utf-8 252.1088ms
2025-06-17 20:37:08.861 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Delete_Query?id=6&_=1750180024487 - null null
2025-06-17 20:37:08.865 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Delete_Query (BaseGIS.Web)'
2025-06-17 20:37:08.870 +03:30 [INF] Route matched with {action = "_Delete_Query", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Delete_Query(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:37:08.891 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Delete_Query (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:37:08.897 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__ids_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__ids_0
2025-06-17 20:37:08.905 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Delete_Query (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 10.6879ms.
2025-06-17 20:37:08.907 +03:30 [INF] Executing PartialViewResult, running view _Delete_Query.
2025-06-17 20:37:08.910 +03:30 [INF] Executed PartialViewResult - view null executed in 2.9803ms.
2025-06-17 20:37:08.912 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Delete_Query (BaseGIS.Web) in 37.2511ms
2025-06-17 20:37:08.913 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Delete_Query (BaseGIS.Web)'
2025-06-17 20:37:08.918 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Delete_Query?id=6&_=1750180024487 - 200 null text/html; charset=utf-8 56.8567ms
2025-06-17 20:37:17.179 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/LayerSetting - null null
2025-06-17 20:37:17.208 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:37:17.214 +03:30 [INF] Route matched with {action = "LayerSetting", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult LayerSetting() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:37:17.238 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:37:17.246 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:37:17.254 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 11.8161ms.
2025-06-17 20:37:17.260 +03:30 [INF] Executing ViewResult, running view LayerSetting.
2025-06-17 20:37:17.266 +03:30 [INF] Executed ViewResult - view LayerSetting executed in 6.6439ms.
2025-06-17 20:37:17.270 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web) in 52.182ms
2025-06-17 20:37:17.275 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.LayerSetting (BaseGIS.Web)'
2025-06-17 20:37:17.277 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/LayerSetting - 200 null text/html; charset=utf-8 98.1214ms
2025-06-17 20:37:17.326 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:37:17.326 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:37:17.335 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 8.0429ms
2025-06-17 20:37:17.349 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 22.5221ms
2025-06-17 20:41:41.333 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-17 20:41:41.413 +03:30 [ERR] An error occurred while seeding the database
System.InvalidOperationException: No service for type 'Microsoft.AspNetCore.Identity.RoleManager`1[Microsoft.AspNetCore.Identity.IdentityRole]' has been registered.
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Program.<Main>$(String[] args) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\Program.cs:line 164
2025-06-17 20:41:41.983 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-17 20:41:41.988 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-17 20:41:42.052 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-17 20:41:42.055 +03:30 [INF] Hosting environment: Development
2025-06-17 20:41:42.057 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-17 20:41:42.061 +03:30 [INF] The application has started
2025-06-17 20:41:44.068 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-17 20:41:44.338 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-17 20:41:44.353 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-17 20:41:44.460 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:41:44.470 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.7121ms.
2025-06-17 20:41:44.505 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-17 20:41:44.726 +03:30 [INF] Executed ViewResult - view Index executed in 232.2474ms.
2025-06-17 20:41:44.734 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 377.5625ms
2025-06-17 20:41:44.739 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-17 20:41:44.750 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 690.0417ms
2025-06-17 20:41:44.846 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-17 20:41:44.866 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:41:44.863 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-17 20:41:44.863 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-17 20:41:44.928 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-17 20:41:44.989 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/avatars/male.png - null null
2025-06-17 20:41:45.494 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-17 20:41:45.494 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-17 20:41:45.494 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-17 20:41:45.620 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-17 20:41:45.038 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:41:45.347 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 401.6729ms
2025-06-17 20:41:45.750 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-17 20:41:45.753 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 499 2750 image/jpeg 890.6369ms
2025-06-17 20:41:45.756 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 499 4983 image/jpeg 893.4952ms
2025-06-17 20:41:45.759 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - 499 232911 text/css 913.5632ms
2025-06-17 20:41:45.761 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 499 3398 image/jpeg 871.3776ms
2025-06-17 20:41:45.782 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/avatars/male.png - 499 268 image/png 826.1246ms
2025-06-17 20:41:45.810 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 772.8333ms
2025-06-17 20:42:19.050 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-17 20:42:19.104 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-17 20:42:19.108 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-17 20:42:19.151 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:42:19.154 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0635ms.
2025-06-17 20:42:19.158 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-17 20:42:19.222 +03:30 [INF] Executed ViewResult - view Index executed in 65.6929ms.
2025-06-17 20:42:19.237 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 125.5516ms
2025-06-17 20:42:19.240 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-17 20:42:19.244 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 193.4398ms
2025-06-17 20:42:19.252 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:42:19.254 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:42:19.270 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 16.582ms
2025-06-17 20:42:19.279 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 26.7702ms
2025-06-17 20:42:31.873 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-17 20:42:31.927 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-17 20:42:31.931 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-17 20:42:31.963 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:42:31.968 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0219ms.
2025-06-17 20:42:31.971 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-17 20:42:31.975 +03:30 [INF] Executed ViewResult - view Index executed in 3.5557ms.
2025-06-17 20:42:31.977 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 41.3623ms
2025-06-17 20:42:31.982 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-17 20:42:31.985 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 112.0665ms
2025-06-17 20:42:32.028 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:42:32.030 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:42:32.047 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 17.2801ms
2025-06-17 20:42:32.049 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 20.9337ms
2025-06-17 20:42:34.293 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-17 20:42:34.328 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-17 20:42:34.333 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-17 20:42:34.361 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:42:35.950 +03:30 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:42:36.394 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 2028.9234ms.
2025-06-17 20:42:36.399 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-17 20:42:37.035 +03:30 [INF] Executed ViewResult - view Index executed in 636.808ms.
2025-06-17 20:42:37.039 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 2702.684ms
2025-06-17 20:42:37.041 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-17 20:42:37.054 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 2760.7972ms
2025-06-17 20:42:37.108 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jqueryui/themes/base/jquery-ui.min.css - null null
2025-06-17 20:42:37.108 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site_map.css?v=aoP519SFZBIvvQFvf5ec-er8O88aoHY0TCfrrJFfnUw - null null
2025-06-17 20:42:37.125 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/logo.png - null null
2025-06-17 20:42:37.110 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/ion-rangeslider/css/ion.rangeslider.min.css - null null
2025-06-17 20:42:37.892 +03:30 [INF] Sending file. Request path: '/img/logo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\logo.png'
2025-06-17 20:42:37.109 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css - null null
2025-06-17 20:42:37.159 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:42:37.977 +03:30 [INF] Sending file. Request path: '/css/site_map.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site_map.css'
2025-06-17 20:42:37.984 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/logo.png - 499 13521 image/png 859.402ms
2025-06-17 20:42:38.051 +03:30 [INF] Sending file. Request path: '/lib/ion-rangeslider/css/ion.rangeslider.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\ion-rangeslider\css\ion.rangeSlider.min.css'
2025-06-17 20:42:38.108 +03:30 [INF] Sending file. Request path: '/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery.fancytree\skin-win8\ui.fancytree.min.css'
2025-06-17 20:42:37.977 +03:30 [INF] Sending file. Request path: '/lib/jqueryui/themes/base/jquery-ui.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jqueryui\themes\base\jquery-ui.min.css'
2025-06-17 20:42:37.244 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:42:37.274 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/esri-leaflet/dist/esri-leaflet.js - null null
2025-06-17 20:42:37.326 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js - null null
2025-06-17 20:42:37.415 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/ion-rangeslider/js/ion.rangeslider.min.js - null null
2025-06-17 20:42:37.537 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/proj4/dist/proj4.js - null null
2025-06-17 20:42:38.116 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site_map.css?v=aoP519SFZBIvvQFvf5ec-er8O88aoHY0TCfrrJFfnUw - 200 20799 text/css 1008.6088ms
2025-06-17 20:42:38.119 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 960.2043ms
2025-06-17 20:42:38.125 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/ion-rangeslider/css/ion.rangeslider.min.css - 200 11084 text/css 1015.2974ms
2025-06-17 20:42:38.129 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css - 499 17671 text/css 1020.0375ms
2025-06-17 20:42:38.136 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jqueryui/themes/base/jquery-ui.min.css - 200 30724 text/css 1027.4554ms
2025-06-17 20:42:38.141 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 982.3327ms
2025-06-17 20:42:38.145 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-Bold-FD.woff - null null
2025-06-17 20:42:38.151 +03:30 [INF] The file /lib/esri-leaflet/dist/esri-leaflet.js was not modified
2025-06-17 20:42:38.153 +03:30 [INF] The file /lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js was not modified
2025-06-17 20:42:38.157 +03:30 [INF] The file /lib/ion-rangeslider/js/ion.rangeslider.min.js was not modified
2025-06-17 20:42:38.160 +03:30 [INF] The file /lib/proj4/dist/proj4.js was not modified
2025-06-17 20:42:38.335 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-Bold-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-Bold-FD.woff'
2025-06-17 20:42:38.336 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/esri-leaflet/dist/esri-leaflet.js - 304 null text/javascript 1061.3186ms
2025-06-17 20:42:38.338 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js - 304 null text/javascript 1013.0213ms
2025-06-17 20:42:38.340 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/ion-rangeslider/js/ion.rangeslider.min.js - 304 null text/javascript 925.2867ms
2025-06-17 20:42:38.342 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/proj4/dist/proj4.js - 304 null text/javascript 805.1076ms
2025-06-17 20:42:38.347 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-Bold-FD.woff - 499 47016 application/font-woff 202.5198ms
2025-06-17 20:42:38.404 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-17 20:42:38.415 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-17 20:42:38.438 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:42:38.469 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/mapService/img/basemap/0.png - null null
2025-06-17 20:42:38.470 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/icons-rtl.gif - null null
2025-06-17 20:42:38.470 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/icons.gif - null null
2025-06-17 20:42:38.473 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/mapService/img/polygon.png - null null
2025-06-17 20:42:39.109 +03:30 [INF] Sending file. Request path: '/mapService/img/basemap/0.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\mapService\img\basemap\0.png'
2025-06-17 20:42:39.129 +03:30 [INF] Sending file. Request path: '/lib/jquery.fancytree/skin-win8/icons.gif'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery.fancytree\skin-win8\icons.gif'
2025-06-17 20:42:39.127 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:42:39.067 +03:30 [INF] Sending file. Request path: '/lib/jquery.fancytree/skin-win8/icons-rtl.gif'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery.fancytree\skin-win8\icons-rtl.gif'
2025-06-17 20:42:38.470 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/mapService/img/group.png - null null
2025-06-17 20:42:38.474 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/mapService/img/polyline.png - null null
2025-06-17 20:42:39.276 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/icons.gif - 200 5510 image/gif 806.1535ms
2025-06-17 20:42:39.271 +03:30 [INF] Sending file. Request path: '/mapService/img/polygon.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\mapService\img\polygon.png'
2025-06-17 20:42:39.273 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/mapService/img/basemap/0.png - 499 17007 image/png 804.6336ms
2025-06-17 20:42:39.283 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/icons-rtl.gif - 499 5513 image/gif 813.7371ms
2025-06-17 20:42:39.414 +03:30 [INF] Sending file. Request path: '/mapService/img/group.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\mapService\img\group.png'
2025-06-17 20:42:39.555 +03:30 [INF] Sending file. Request path: '/mapService/img/polyline.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\mapService\img\polyline.png'
2025-06-17 20:42:39.564 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/mapService/img/polygon.png - 499 643 image/png 1090.6512ms
2025-06-17 20:42:39.575 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/mapService/img/group.png - 499 1504 image/png 1104.9766ms
2025-06-17 20:42:39.577 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/mapService/img/polyline.png - 499 450 image/png 1103.8514ms
2025-06-17 20:42:39.679 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-17 20:42:39.692 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 412.3272ms.
2025-06-17 20:42:39.700 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType24`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType25`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType23`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-17 20:42:39.737 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 1293.142ms
2025-06-17 20:42:39.741 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-17 20:42:39.743 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 1339.4885ms
2025-06-17 20:42:49.542 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=5028944.9649383165%2C4654709.274454093%2C7748880.179438029%2C3321647.5011606184&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - null null
2025-06-17 20:42:49.549 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:42:49.554 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:42:49.586 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:42:50.199 +03:30 [INF] Executed DbCommand (52ms) [Parameters=[@__tableIdInt_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableIdInt_0
2025-06-17 20:42:50.248 +03:30 [INF] Executed DbCommand (13ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-17 20:42:50.386 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-17 20:42:52.238 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 2649.3105ms.
2025-06-17 20:42:52.248 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-17 20:42:52.259 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 2700.8667ms
2025-06-17 20:42:52.262 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:42:52.264 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=5028944.9649383165%2C4654709.274454093%2C7748880.179438029%2C3321647.5011606184&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - 200 74681 image/png 2722.0703ms
2025-06-17 20:42:54.452 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4637587.3801182145%2C4674277.153695098%2C7357522.594617927%2C3341215.3804016234&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - null null
2025-06-17 20:42:54.457 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:42:54.460 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:42:54.487 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:42:54.495 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableIdInt_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableIdInt_0
2025-06-17 20:42:54.502 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-17 20:42:54.507 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-17 20:42:55.010 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 519.8978ms.
2025-06-17 20:42:55.018 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-17 20:42:55.021 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 558.5007ms
2025-06-17 20:42:55.024 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:42:55.026 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4637587.3801182145%2C4674277.153695098%2C7357522.594617927%2C3341215.3804016234&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - 200 76252 image/png 574.0185ms
2025-06-17 20:42:56.773 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4278027.599064745%2C4688953.0631258525%2C6997962.813564457%2C3355891.2898323783&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - null null
2025-06-17 20:42:56.779 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:42:56.781 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:42:56.822 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:42:56.829 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableIdInt_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableIdInt_0
2025-06-17 20:42:56.836 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-17 20:42:56.841 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-17 20:42:57.229 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 403.1763ms.
2025-06-17 20:42:57.234 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-17 20:42:57.236 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 445.2843ms
2025-06-17 20:42:57.239 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:42:57.243 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4278027.599064745%2C4688953.0631258525%2C6997962.813564457%2C3355891.2898323783&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - 200 76013 image/png 469.7583ms
2025-06-17 20:43:17.566 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4846719.089506457%2C4384427.942437712%2C6206686.696756313%2C3717897.0557909743&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - null null
2025-06-17 20:43:17.575 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:43:17.578 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:43:17.621 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:43:17.626 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableIdInt_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableIdInt_0
2025-06-17 20:43:17.633 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-17 20:43:17.637 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-17 20:43:17.914 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 290.0524ms.
2025-06-17 20:43:17.917 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-17 20:43:17.920 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 324.0271ms
2025-06-17 20:43:17.922 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:43:17.924 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4846719.089506457%2C4384427.942437712%2C6206686.696756313%2C3717897.0557909743&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - 200 63278 image/png 358.3824ms
2025-06-17 20:43:24.170 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=5142683.263026658%2C4223604.434925701%2C5822667.066651587%2C3890338.9916023314&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - null null
2025-06-17 20:43:24.182 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:43:24.186 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:43:24.224 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:43:24.228 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableIdInt_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableIdInt_0
2025-06-17 20:43:24.233 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-17 20:43:24.238 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-17 20:43:24.387 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 161.5295ms.
2025-06-17 20:43:24.392 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-17 20:43:24.394 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 202.3074ms
2025-06-17 20:43:24.397 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:43:24.399 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=5142683.263026658%2C4223604.434925701%2C5822667.066651587%2C3890338.9916023314&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - 200 48343 image/png 228.7036ms
2025-06-17 20:43:25.334 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4847942.081959019%2C4384427.942437712%2C6207909.689208875%2C3717897.0557909743&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - null null
2025-06-17 20:43:25.340 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:43:25.343 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:43:25.385 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:43:25.392 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableIdInt_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableIdInt_0
2025-06-17 20:43:25.400 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-17 20:43:25.404 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-17 20:43:25.628 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 239.3607ms.
2025-06-17 20:43:25.631 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-17 20:43:25.633 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 283.8891ms
2025-06-17 20:43:25.636 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:43:25.638 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4847942.081959019%2C4384427.942437712%2C6207909.689208875%2C3717897.0557909743&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - 200 63294 image/png 304.7208ms
2025-06-17 20:43:25.944 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4258459.71982374%2C4706074.9574617315%2C6978394.9343234515%2C3373013.1841682596&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - null null
2025-06-17 20:43:25.949 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:43:25.950 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:43:25.985 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:43:25.992 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableIdInt_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableIdInt_0
2025-06-17 20:43:26.002 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-17 20:43:26.030 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-17 20:43:26.355 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 366.3586ms.
2025-06-17 20:43:26.375 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-17 20:43:26.379 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 424.6273ms
2025-06-17 20:43:26.381 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:43:26.382 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4258459.71982374%2C4706074.9574617315%2C6978394.9343234515%2C3373013.1841682596&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - 200 76104 image/png 438.2415ms
2025-06-17 20:43:28.665 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4790461.436688567%2C4355076.123576204%2C6150429.043938423%2C3688545.236929465&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - null null
2025-06-17 20:43:28.669 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:43:28.671 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:43:28.702 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:43:28.707 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableIdInt_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableIdInt_0
2025-06-17 20:43:28.712 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-17 20:43:28.718 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-17 20:43:28.931 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 225.8719ms.
2025-06-17 20:43:28.936 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-17 20:43:28.954 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 277.2636ms
2025-06-17 20:43:28.959 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:43:28.961 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=4790461.436688567%2C4355076.123576204%2C6150429.043938423%2C3688545.236929465&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - 200 60501 image/png 295.8942ms
2025-06-17 20:43:41.037 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/_Buffer?_=1750180358152 - null null
2025-06-17 20:43:41.042 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/_Buffer?_=1750180358152 - 404 0 null 5.1955ms
2025-06-17 20:43:41.048 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/GeoMap/_Buffer, Response status code: 404
2025-06-17 20:43:44.674 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=5057685.287573542%2C4157562.8424873073%2C5737669.091198471%2C3824297.3991639395&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - null null
2025-06-17 20:43:44.680 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:43:44.683 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:43:44.721 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:43:44.729 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__tableIdInt_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableIdInt_0
2025-06-17 20:43:44.738 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-17 20:43:44.762 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-17 20:43:44.905 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 180.3041ms.
2025-06-17 20:43:44.909 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-17 20:43:44.912 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 223.3093ms
2025-06-17 20:43:44.915 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:43:44.916 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=5057685.287573542%2C4157562.8424873073%2C5737669.091198471%2C3824297.3991639395&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B&f=image - 200 54607 image/png 242.4979ms
2025-06-17 20:43:52.290 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-17 20:43:52.344 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-17 20:43:52.346 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-17 20:43:52.382 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:43:52.389 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:43:52.398 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 12.3046ms.
2025-06-17 20:43:52.404 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-17 20:43:52.426 +03:30 [INF] Executed ViewResult - view Index executed in 21.5747ms.
2025-06-17 20:43:52.428 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 77.8681ms
2025-06-17 20:43:52.432 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-17 20:43:52.435 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 144.8184ms
2025-06-17 20:43:52.479 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:43:52.482 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:43:52.485 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 6.3047ms
2025-06-17 20:43:52.507 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 24.7844ms
2025-06-17 20:43:52.560 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-17 20:43:52.564 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-17 20:43:52.567 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:43:52.611 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:43:52.619 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-17 20:43:52.623 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 9.591ms.
2025-06-17 20:43:52.626 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType24`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType25`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType23`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-17 20:43:52.637 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 66.3805ms
2025-06-17 20:43:52.640 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-17 20:43:52.642 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 81.508ms
2025-06-17 20:43:54.806 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/Property?Layerid=10000010006&_=1750180432472 - null null
2025-06-17 20:43:54.807 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Rest/Legend - application/x-www-form-urlencoded; charset=UTF-8 35
2025-06-17 20:43:54.812 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Property (BaseGIS.Web)'
2025-06-17 20:43:54.820 +03:30 [INF] CORS policy execution successful.
2025-06-17 20:43:54.821 +03:30 [INF] Route matched with {action = "Property", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult Property(System.String) on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-17 20:43:54.823 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web)'
2025-06-17 20:43:54.844 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Property (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:43:54.848 +03:30 [INF] Route matched with {action = "Legend", controller = "REST"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult Legend(System.String, Int32) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:43:55.043 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:43:55.048 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 1.5137ms.
2025-06-17 20:43:55.052 +03:30 [INF] Executing BadRequestObjectResult, writing value of type 'System.String'.
2025-06-17 20:43:55.054 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web) in 111.4483ms
2025-06-17 20:43:55.056 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web)'
2025-06-17 20:43:55.058 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Rest/Legend - 400 null text/plain; charset=utf-8 250.118ms
2025-06-17 20:43:55.066 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__tblID_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [SymbologyInfos] AS [s]
INNER JOIN [TableInfos] AS [t] ON [s].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tblID_0
2025-06-17 20:43:55.156 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Property (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 305.7804ms.
2025-06-17 20:43:55.161 +03:30 [INF] Executing PartialViewResult, running view Property.
2025-06-17 20:43:55.466 +03:30 [INF] Executed PartialViewResult - view null executed in 305.2861ms.
2025-06-17 20:43:55.470 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Property (BaseGIS.Web) in 644.5432ms
2025-06-17 20:43:55.472 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Property (BaseGIS.Web)'
2025-06-17 20:43:55.474 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/Property?Layerid=10000010006&_=1750180432472 - 200 null text/html; charset=utf-8 668.0045ms
2025-06-17 20:43:55.489 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/GeoMap/GetLayerFields - application/x-www-form-urlencoded; charset=UTF-8 198
2025-06-17 20:43:55.494 +03:30 [INF] CORS policy execution successful.
2025-06-17 20:43:55.495 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetLayerFields (BaseGIS.Web)'
2025-06-17 20:43:55.499 +03:30 [INF] Route matched with {action = "GetLayerFields", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.JsonResult GetLayerFields(System.String) on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-17 20:43:55.502 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Rest/Legend - application/x-www-form-urlencoded; charset=UTF-8 33
2025-06-17 20:43:55.534 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetLayerFields (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:43:55.539 +03:30 [INF] CORS policy execution successful.
2025-06-17 20:43:55.543 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web)'
2025-06-17 20:43:55.545 +03:30 [INF] Route matched with {action = "Legend", controller = "REST"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult Legend(System.String, Int32) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:43:55.579 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web) - Validation state: "Invalid"
2025-06-17 20:43:55.583 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.BadRequestObjectResult in 0.0178ms.
2025-06-17 20:43:55.586 +03:30 [INF] Executing BadRequestObjectResult, writing value of type 'System.String'.
2025-06-17 20:43:55.589 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web) in 40.6898ms
2025-06-17 20:43:55.591 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web)'
2025-06-17 20:43:55.593 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[@__tblID_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Name], [f].[AliasName]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tblID_0 AND [f].[IsDisplay] = CAST(1 AS bit)
ORDER BY [f].[FieldIndex]
2025-06-17 20:43:55.593 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Rest/Legend - 400 null text/plain; charset=utf-8 91.187ms
2025-06-17 20:43:55.600 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetLayerFields (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 59.0102ms.
2025-06-17 20:43:55.610 +03:30 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType21`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 20:43:55.620 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetLayerFields (BaseGIS.Web) in 114.5074ms
2025-06-17 20:43:55.624 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetLayerFields (BaseGIS.Web)'
2025-06-17 20:43:55.626 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/GeoMap/GetLayerFields - 200 null application/json; charset=utf-8 137.0189ms
2025-06-17 20:44:01.416 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=3067265.0710275527%2C5190379.968676608%2C8507135.500026979%2C2524256.422089661&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B%2C10000010007%3Bfalse%3B%3B3&f=image - null null
2025-06-17 20:44:01.421 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:44:01.423 +03:30 [INF] Route matched with {action = "Services", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Services(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:44:01.456 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:44:01.462 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableIdInt_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableIdInt_0
2025-06-17 20:44:01.467 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableInfo_Id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__tableInfo_Id_0 AND [s].[IsDefault] = CAST(1 AS bit)
2025-06-17 20:44:01.471 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-17 20:44:01.601 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableIdInt_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__tableIdInt_0
2025-06-17 20:44:01.617 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__8__locals1_tableInfo_Id_0='?' (DbType = Int32), @__symbologyId_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[TableInfoId] = @__8__locals1_tableInfo_Id_0 AND [s].[Id] = @__symbologyId_1
2025-06-17 20:44:01.640 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__tableName_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT CASE
    WHEN EXISTS (
        SELECT 1
        FROM [TableInfos] AS [t]
        WHERE [t].[Name] = @__tableName_0) THEN CAST(1 AS bit)
    ELSE CAST(0 AS bit)
END
2025-06-17 20:44:02.374 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/Property?Layerid=10000010007&_=1750180432473 - null null
2025-06-17 20:44:02.376 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Rest/Legend - application/x-www-form-urlencoded; charset=UTF-8 34
2025-06-17 20:44:02.379 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Property (BaseGIS.Web)'
2025-06-17 20:44:02.383 +03:30 [INF] CORS policy execution successful.
2025-06-17 20:44:02.385 +03:30 [INF] Route matched with {action = "Property", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult Property(System.String) on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-17 20:44:02.386 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web)'
2025-06-17 20:44:02.393 +03:30 [INF] Route matched with {action = "Legend", controller = "REST"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult Legend(System.String, Int32) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:44:02.416 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Property (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:44:02.441 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:44:02.446 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tblID_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type], [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [SymbologyInfos] AS [s]
INNER JOIN [TableInfos] AS [t] ON [s].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tblID_0
2025-06-17 20:44:02.452 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Property (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 33.2932ms.
2025-06-17 20:44:02.456 +03:30 [INF] Executing PartialViewResult, running view Property.
2025-06-17 20:44:02.459 +03:30 [INF] Executed PartialViewResult - view null executed in 3.0584ms.
2025-06-17 20:44:02.471 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Property (BaseGIS.Web) in 81.1995ms
2025-06-17 20:44:02.473 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Property (BaseGIS.Web)'
2025-06-17 20:44:02.475 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/Property?Layerid=10000010007&_=1750180432473 - 200 null text/html; charset=utf-8 100.7437ms
2025-06-17 20:44:02.485 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[Id] = @__p_0
2025-06-17 20:44:02.494 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/GeoMap/GetLayerFields - application/x-www-form-urlencoded; charset=UTF-8 198
2025-06-17 20:44:02.500 +03:30 [INF] CORS policy execution successful.
2025-06-17 20:44:02.501 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetLayerFields (BaseGIS.Web)'
2025-06-17 20:44:02.504 +03:30 [INF] Route matched with {action = "GetLayerFields", controller = "GeoMap"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.JsonResult GetLayerFields(System.String) on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-17 20:44:02.506 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Rest/Legend - application/x-www-form-urlencoded; charset=UTF-8 34
2025-06-17 20:44:02.529 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetLayerFields (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:44:02.532 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-17 20:44:02.532 +03:30 [INF] CORS policy execution successful.
2025-06-17 20:44:02.541 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web)'
2025-06-17 20:44:02.543 +03:30 [INF] Route matched with {action = "Legend", controller = "REST"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult Legend(System.String, Int32) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-17 20:44:02.544 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[@__tblID_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [f].[Name], [f].[AliasName]
FROM [FieldInfos] AS [f]
INNER JOIN [TableInfos] AS [t] ON [f].[TableInfoId] = [t].[Id]
WHERE [t].[Id] = @__tblID_0 AND [f].[IsDisplay] = CAST(1 AS bit)
ORDER BY [f].[FieldIndex]
2025-06-17 20:44:02.566 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:44:02.570 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetLayerFields (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 35.0634ms.
2025-06-17 20:44:02.574 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM [SymbologyInfos] AS [s]
WHERE [s].[Id] = @__p_0
2025-06-17 20:44:02.577 +03:30 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType21`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 20:44:02.583 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__p_0
2025-06-17 20:44:02.585 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetLayerFields (BaseGIS.Web) in 76.673ms
2025-06-17 20:44:02.596 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetLayerFields (BaseGIS.Web)'
2025-06-17 20:44:02.598 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/GeoMap/GetLayerFields - 200 null application/json; charset=utf-8 103.3226ms
2025-06-17 20:44:03.557 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 1113.556ms.
2025-06-17 20:44:03.557 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 984.6746ms.
2025-06-17 20:44:03.560 +03:30 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[BaseGIS.Web.ViewModels.NodeTree, BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 20:44:03.562 +03:30 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[BaseGIS.Web.ViewModels.NodeTree, BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 20:44:03.578 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web) in 1161.7225ms
2025-06-17 20:44:03.578 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web) in 1029.2919ms
2025-06-17 20:44:03.580 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web)'
2025-06-17 20:44:03.583 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Legend (BaseGIS.Web)'
2025-06-17 20:44:03.584 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Rest/Legend - 200 null application/json; charset=utf-8 1208.014ms
2025-06-17 20:44:03.586 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Rest/Legend - 200 null application/json; charset=utf-8 1079.6322ms
2025-06-17 20:44:06.090 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.FileContentResult in 4629.5303ms.
2025-06-17 20:44:06.096 +03:30 [INF] Executing FileContentResult, sending file with download name '' ...
2025-06-17 20:44:06.100 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web) in 4674.0292ms
2025-06-17 20:44:06.102 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.Services (BaseGIS.Web)'
2025-06-17 20:44:06.105 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/export?bbox=3067265.0710275527%2C5190379.968676608%2C8507135.500026979%2C2524256.422089661&size=1112%2C545&dpi=96&format=png32&transparent=true&bboxSR=3857&imageSR=3857&layers=show%3A10000010006%3BName%3B%3B%2C10000010007%3Bfalse%3B%3B3&f=image - 200 212311 image/png 4688.9153ms
2025-06-17 20:44:11.101 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-17 20:44:11.143 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-17 20:44:11.146 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-17 20:44:11.186 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:44:11.189 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0225ms.
2025-06-17 20:44:11.192 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-17 20:44:11.195 +03:30 [INF] Executed ViewResult - view Index executed in 2.7719ms.
2025-06-17 20:44:11.197 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 47.0262ms
2025-06-17 20:44:11.202 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-17 20:44:11.205 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 103.4927ms
2025-06-17 20:44:11.269 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:44:11.270 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:44:11.280 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 10.4849ms
2025-06-17 20:44:11.282 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 13.1228ms
2025-06-17 20:44:13.016 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-17 20:44:13.059 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-17 20:44:13.061 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-17 20:44:13.092 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:44:13.097 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0352ms.
2025-06-17 20:44:13.100 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-17 20:44:13.104 +03:30 [INF] Executed ViewResult - view Index executed in 3.6868ms.
2025-06-17 20:44:13.107 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 41.1342ms
2025-06-17 20:44:13.110 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-17 20:44:13.113 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 96.3779ms
2025-06-17 20:44:13.160 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:44:13.161 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:44:13.172 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 10.7604ms
2025-06-17 20:44:13.172 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 11.7246ms
2025-06-17 20:44:16.116 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager - null null
2025-06-17 20:44:16.151 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:44:16.156 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:44:16.190 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:44:16.207 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:44:16.215 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 21.871ms.
2025-06-17 20:44:16.219 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:44:16.242 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 24.0628ms.
2025-06-17 20:44:16.245 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 85.8387ms
2025-06-17 20:44:16.248 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:44:16.253 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager - 200 null text/html; charset=utf-8 137.3597ms
2025-06-17 20:44:16.313 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:44:16.314 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:44:16.333 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 19.6851ms
2025-06-17 20:44:16.333 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 18.9176ms
2025-06-17 20:44:20.009 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - null null
2025-06-17 20:44:20.053 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:44:20.056 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:44:20.094 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:44:20.099 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:44:20.103 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 6.0056ms.
2025-06-17 20:44:20.106 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:44:20.109 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 3.0009ms.
2025-06-17 20:44:20.113 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 48.2899ms
2025-06-17 20:44:20.116 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:44:20.120 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - 200 null text/html; charset=utf-8 110.6274ms
2025-06-17 20:44:20.179 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:44:20.180 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:44:20.190 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 9.4975ms
2025-06-17 20:44:20.206 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 27.0671ms
2025-06-17 20:44:20.301 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:44:20.301 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:44:20.305 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:44:20.308 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:44:20.313 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:44:20.315 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:44:20.373 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:44:20.393 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:44:20.462 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:44:20.462 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:44:20.480 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 84.1958ms.
2025-06-17 20:44:20.482 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 81.0751ms.
2025-06-17 20:44:20.488 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:44:20.489 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:44:20.504 +03:30 [INF] Executed PartialViewResult - view null executed in 17.1149ms.
2025-06-17 20:44:20.504 +03:30 [INF] Executed PartialViewResult - view null executed in 14.9344ms.
2025-06-17 20:44:20.507 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 186.5268ms
2025-06-17 20:44:20.509 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 191.3066ms
2025-06-17 20:44:20.511 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:44:20.515 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:44:20.517 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 215.4585ms
2025-06-17 20:44:20.519 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 217.3857ms
2025-06-17 20:44:20.524 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - null null
2025-06-17 20:44:20.537 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - 404 0 null 12.6737ms
2025-06-17 20:44:20.541 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Database/content/img/demo/64x64.png, Response status code: 404
2025-06-17 20:44:22.318 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/symbology/index?tblid=7&_=1750180460215 - null null
2025-06-17 20:44:22.320 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/symbology/index?tblid=7&_=************* - null null
2025-06-17 20:44:22.324 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web)'
2025-06-17 20:44:22.328 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web)'
2025-06-17 20:44:22.330 +03:30 [INF] Route matched with {action = "Index", controller = "Symbology"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult Index() on controller BaseGIS.Web.Controllers.SymbologyController (BaseGIS.Web).
2025-06-17 20:44:22.332 +03:30 [INF] Route matched with {action = "Index", controller = "Symbology"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult Index() on controller BaseGIS.Web.Controllers.SymbologyController (BaseGIS.Web).
2025-06-17 20:44:22.382 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:44:22.403 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:44:22.421 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__ids_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__ids_0
2025-06-17 20:44:22.460 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__tblIds_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tblIds_0
) AS [t0]
LEFT JOIN [FieldInfos] AS [f] ON [t0].[Id] = [f].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:44:22.585 +03:30 [ERR] An exception occurred while iterating over the results of a query for context type 'BaseGIS.Infrastructure.Persistence.ApplicationDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-06-17 20:44:22.735 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 329.2652ms.
2025-06-17 20:44:22.847 +03:30 [INF] Executed action BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web) in 460.3889ms
2025-06-17 20:44:22.851 +03:30 [INF] Executing PartialViewResult, running view Index.
2025-06-17 20:44:22.916 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web)'
2025-06-17 20:44:23.007 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Linq.Enumerable.TryGetSingle[TSource](IEnumerable`1 source, Boolean& found)
   at lambda_method819(Closure, QueryContext)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.Execute[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.Execute[TResult](Expression expression)
   at BaseGIS.Web.Controllers.SymbologyController.Index()
   at lambda_method785(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-17 20:44:23.047 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/symbology/index?tblid=7&_=************* - 500 null text/plain; charset=utf-8 727.5698ms
2025-06-17 20:44:23.084 +03:30 [INF] Executed PartialViewResult - view null executed in 233.9947ms.
2025-06-17 20:44:23.087 +03:30 [INF] Executed action BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web) in 748.0411ms
2025-06-17 20:44:23.089 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web)'
2025-06-17 20:44:23.090 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/symbology/index?tblid=7&_=1750180460215 - 200 null text/html; charset=utf-8 771.6074ms
2025-06-17 20:44:23.100 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.css - null null
2025-06-17 20:44:23.101 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.min.js - null null
2025-06-17 20:44:23.104 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.css - 404 0 null 3.7037ms
2025-06-17 20:44:23.108 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.min.js - 404 0 null 6.827ms
2025-06-17 20:44:23.117 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.css, Response status code: 404
2025-06-17 20:44:23.124 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.min.js, Response status code: 404
2025-06-17 20:44:43.747 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-17 20:44:43.755 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-17 20:44:43.763 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-17 20:44:43.768 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-17 20:44:43.771 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 24.7454ms
2025-06-17 20:44:43.773 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 25.6753ms
2025-06-17 20:49:37.465 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js.map - null null
2025-06-17 20:49:37.465 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js.map - null null
2025-06-17 20:49:37.477 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js.map - null null
2025-06-17 20:49:37.486 +03:30 [INF] The file /lib/bootstrap/js/bootstrap.bundle.min.js.map was not modified
2025-06-17 20:49:37.481 +03:30 [INF] The file /lib/popper.js/dist/umd/popper.min.js.map was not modified
2025-06-17 20:49:37.505 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js.map - 304 null text/plain 39.8061ms
2025-06-17 20:49:37.503 +03:30 [INF] The file /js/adminlte.js.map was not modified
2025-06-17 20:49:37.519 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js.map - 304 null text/plain 53.9292ms
2025-06-17 20:49:37.527 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js.map - 304 null text/plain 49.7382ms
2025-06-17 20:49:37.533 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - null null
2025-06-17 20:49:37.539 +03:30 [INF] The file /lib/leaflet/leaflet.js.map was not modified
2025-06-17 20:49:37.540 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - 304 null text/plain 7.6235ms
2025-06-17 20:50:03.586 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - null null
2025-06-17 20:50:03.675 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:50:03.678 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:50:03.712 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:50:03.726 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:50:03.733 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 16.8201ms.
2025-06-17 20:50:03.736 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:50:03.740 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 3.5793ms.
2025-06-17 20:50:03.744 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 62.344ms
2025-06-17 20:50:03.746 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:50:03.748 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - 200 null text/html; charset=utf-8 161.7987ms
2025-06-17 20:50:03.958 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-17 20:50:03.960 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-17 20:50:03.964 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - null null
2025-06-17 20:50:04.027 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - null null
2025-06-17 20:50:04.102 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - null null
2025-06-17 20:50:04.111 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js - null null
2025-06-17 20:50:04.072 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-17 20:50:04.013 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - null null
2025-06-17 20:50:03.967 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - null null
2025-06-17 20:50:03.962 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - null null
2025-06-17 20:50:04.013 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - null null
2025-06-17 20:50:04.013 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - null null
2025-06-17 20:50:04.027 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - null null
2025-06-17 20:50:04.039 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - null null
2025-06-17 20:50:04.050 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jqueryui/themes/base/jquery-ui.min.css - null null
2025-06-17 20:50:04.062 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css - null null
2025-06-17 20:50:04.072 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-17 20:50:04.082 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-17 20:50:04.092 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/avatars/male.png - null null
2025-06-17 20:50:04.145 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.js - null null
2025-06-17 20:50:04.111 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery/jquery.min.js - null null
2025-06-17 20:50:04.121 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery-validation/dist/jquery.validate.min.js - null null
2025-06-17 20:50:04.132 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - null null
2025-06-17 20:50:04.146 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - null null
2025-06-17 20:50:04.159 +03:30 [INF] Sending file. Request path: '/assets/img/AdminLTELogo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\AdminLTELogo.png'
2025-06-17 20:50:04.170 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - null null
2025-06-17 20:50:04.184 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - null null
2025-06-17 20:50:04.194 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js - null null
2025-06-17 20:50:04.231 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=Nmb9k2AhsnpOv4cI_Fjp8KgGhTAxQTOTwjkpS20OWjE - null null
2025-06-17 20:50:04.242 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jqueryui/jquery-ui.min.js - null null
2025-06-17 20:50:04.252 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/jquery.fancytree-all.min.js - null null
2025-06-17 20:50:04.262 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:50:04.263 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:50:04.422 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\browser\overlayscrollbars.browser.es6.min.js'
2025-06-17 20:50:04.444 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - 200 29334 text/javascript 431.1351ms
2025-06-17 20:50:04.272 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.css'
2025-06-17 20:50:04.284 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-17 20:50:04.298 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-17 20:50:04.473 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - 200 14806 text/css 511.0773ms
2025-06-17 20:50:04.298 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/styles/overlayscrollbars.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\styles\overlayscrollbars.min.css'
2025-06-17 20:50:04.303 +03:30 [INF] Sending file. Request path: '/css/site.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site.css'
2025-06-17 20:50:04.309 +03:30 [INF] Sending file. Request path: '/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery.fancytree\skin-win8\ui.fancytree.min.css'
2025-06-17 20:50:04.311 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-17 20:50:04.315 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-17 20:50:04.319 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-17 20:50:04.420 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js'
2025-06-17 20:50:04.373 +03:30 [INF] Sending file. Request path: '/js/site.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\site.js'
2025-06-17 20:50:04.342 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - 200 2637 image/png 240.879ms
2025-06-17 20:50:04.424 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-17 20:50:04.424 +03:30 [INF] Sending file. Request path: '/lib/popper.js/dist/umd/popper.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\popper.js\dist\umd\popper.min.js'
2025-06-17 20:50:04.360 +03:30 [INF] Sending file. Request path: '/lib/jqueryui/themes/base/jquery-ui.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jqueryui\themes\base\jquery-ui.min.css'
2025-06-17 20:50:04.430 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/bootstrap-icons.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\bootstrap-icons.min.css'
2025-06-17 20:50:04.440 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.js'
2025-06-17 20:50:04.440 +03:30 [INF] Sending file. Request path: '/js/adminlte.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\adminlte.js'
2025-06-17 20:50:04.444 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 180.2791ms
2025-06-17 20:50:04.268 +03:30 [INF] Sending file. Request path: '/lib/source-sans-3/index.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\source-sans-3\index.css'
2025-06-17 20:50:04.452 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation/dist/jquery.validate.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js'
2025-06-17 20:50:04.457 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/js/bootstrap.bundle.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\js\bootstrap.bundle.min.js'
2025-06-17 20:50:04.459 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.js'
2025-06-17 20:50:04.461 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-17 20:50:04.461 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - 200 5267 text/css 497.555ms
2025-06-17 20:50:04.464 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 200 4983 image/jpeg 392.7186ms
2025-06-17 20:50:04.302 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.css'
2025-06-17 20:50:04.488 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.js'
2025-06-17 20:50:04.491 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - 200 13202 text/css 478.1865ms
2025-06-17 20:50:04.495 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - 200 7707 text/css 455.89ms
2025-06-17 20:50:04.499 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css - 200 17671 text/css 437.6195ms
2025-06-17 20:50:04.502 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 200 2750 image/jpeg 430.2324ms
2025-06-17 20:50:04.505 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 200 3398 image/jpeg 422.882ms
2025-06-17 20:50:04.507 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/avatars/male.png - 200 268 image/png 415.2417ms
2025-06-17 20:50:04.511 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - 200 5824 text/javascript 379.1879ms
2025-06-17 20:50:04.515 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=Nmb9k2AhsnpOv4cI_Fjp8KgGhTAxQTOTwjkpS20OWjE - 200 5215 text/javascript 283.7545ms
2025-06-17 20:50:04.522 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - ********** text/css 561.5507ms
2025-06-17 20:50:04.524 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - 200 20122 text/javascript 341.4509ms
2025-06-17 20:50:04.527 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jqueryui/themes/base/jquery-ui.min.css - 200 30724 text/css 476.7686ms
2025-06-17 20:50:04.531 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - 200 85875 text/css 504.0536ms
2025-06-17 20:50:04.533 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.js - 200 67484 text/javascript 387.7253ms
2025-06-17 20:50:04.535 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js - 200 29455 text/javascript 341.0439ms
2025-06-17 20:50:04.544 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - 200 2520 text/css 530.9192ms
2025-06-17 20:50:04.546 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery-validation/dist/jquery.validate.min.js - 200 25308 text/javascript 424.8351ms
2025-06-17 20:50:04.548 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js - 200 80721 text/javascript 436.9477ms
2025-06-17 20:50:04.550 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - 200 46668 text/javascript 379.8357ms
2025-06-17 20:50:04.552 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - 200 232911 text/css 593.518ms
2025-06-17 20:50:04.562 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - 200 23932 text/css 596.1813ms
2025-06-17 20:50:04.564 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - 200 147552 text/javascript 418.0358ms
2025-06-17 20:50:04.629 +03:30 [INF] Sending file. Request path: '/lib/jquery/jquery.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery\jquery.min.js'
2025-06-17 20:50:04.633 +03:30 [INF] Sending file. Request path: '/lib/jquery.fancytree/jquery.fancytree-all.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery.fancytree\jquery.fancytree-all.min.js'
2025-06-17 20:50:04.648 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 386.3474ms
2025-06-17 20:50:04.658 +03:30 [INF] Sending file. Request path: '/css/adminlte.rtl.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\adminlte.rtl.css'
2025-06-17 20:50:04.666 +03:30 [INF] Sending file. Request path: '/lib/jqueryui/jquery-ui.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jqueryui\jquery-ui.min.js'
2025-06-17 20:50:04.683 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery/jquery.min.js - 200 87533 text/javascript 571.203ms
2025-06-17 20:50:04.686 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/jquery.fancytree-all.min.js - 200 125100 text/javascript 433.5911ms
2025-06-17 20:50:04.693 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - 200 358126 text/css 666.17ms
2025-06-17 20:50:04.697 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jqueryui/jquery-ui.min.js - 200 253669 text/javascript 455.7391ms
2025-06-17 20:50:04.754 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-17 20:50:04.764 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-17 20:50:04.766 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - null null
2025-06-17 20:50:04.773 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-17 20:50:04.774 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-17 20:50:04.777 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - 200 130396 font/woff2 13.0949ms
2025-06-17 20:50:04.780 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 200 45276 application/font-woff 26.0056ms
2025-06-17 20:50:04.786 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/webfonts/fa-solid-900.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\webfonts\fa-solid-900.woff2'
2025-06-17 20:50:04.795 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - 200 158220 font/woff2 29.4723ms
2025-06-17 20:50:04.842 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js.map - null null
2025-06-17 20:50:04.842 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - null null
2025-06-17 20:50:04.842 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js.map - null null
2025-06-17 20:50:04.845 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js.map - null null
2025-06-17 20:50:04.846 +03:30 [INF] The file /lib/bootstrap/js/bootstrap.bundle.min.js.map was not modified
2025-06-17 20:50:04.848 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-17 20:50:04.850 +03:30 [INF] The file /lib/leaflet/leaflet.js.map was not modified
2025-06-17 20:50:04.853 +03:30 [INF] The file /lib/popper.js/dist/umd/popper.min.js.map was not modified
2025-06-17 20:50:04.856 +03:30 [INF] The file /js/adminlte.js.map was not modified
2025-06-17 20:50:04.858 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js.map - 304 null text/plain 15.8498ms
2025-06-17 20:50:04.862 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-17 20:50:04.863 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - 304 null text/plain 20.7698ms
2025-06-17 20:50:04.864 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js.map - 304 null text/plain 22.1473ms
2025-06-17 20:50:04.869 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js.map - 304 null text/plain 23.6318ms
2025-06-17 20:50:04.877 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 200 45276 application/font-woff 28.4894ms
2025-06-17 20:50:04.915 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/icons-rtl.gif - null null
2025-06-17 20:50:04.920 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:50:04.921 +03:30 [INF] Sending file. Request path: '/lib/jquery.fancytree/skin-win8/icons-rtl.gif'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery.fancytree\skin-win8\icons-rtl.gif'
2025-06-17 20:50:04.921 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:50:04.924 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:50:04.927 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/icons-rtl.gif - 200 5513 image/gif 11.8426ms
2025-06-17 20:50:04.934 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:50:04.936 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:50:04.945 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:50:04.979 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:50:05.007 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:50:05.016 +03:30 [INF] Executed DbCommand (6ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:50:05.020 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:50:05.031 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 21.7771ms.
2025-06-17 20:50:05.042 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 27.2062ms.
2025-06-17 20:50:05.050 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:50:05.055 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:50:05.058 +03:30 [INF] Executed PartialViewResult - view null executed in 8.7458ms.
2025-06-17 20:50:05.061 +03:30 [INF] Executed PartialViewResult - view null executed in 5.5952ms.
2025-06-17 20:50:05.063 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 113.8718ms
2025-06-17 20:50:05.065 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 81.424ms
2025-06-17 20:50:05.071 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:50:05.069 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:50:05.074 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 152.3348ms
2025-06-17 20:50:05.080 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 159.9395ms
2025-06-17 20:50:05.090 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-17 20:50:05.105 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-17 20:50:05.108 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 200 5430 image/x-icon 17.5149ms
2025-06-17 20:50:05.142 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - null null
2025-06-17 20:50:05.145 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - 404 0 null 3.8154ms
2025-06-17 20:50:05.150 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Database/content/img/demo/64x64.png, Response status code: 404
2025-06-17 20:50:08.760 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/symbology/index?tblid=7&_=1750180804742 - null null
2025-06-17 20:50:08.763 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/symbology/index?tblid=7&_=************* - null null
2025-06-17 20:50:08.765 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web)'
2025-06-17 20:50:08.768 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web)'
2025-06-17 20:50:08.771 +03:30 [INF] Route matched with {action = "Index", controller = "Symbology"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult Index() on controller BaseGIS.Web.Controllers.SymbologyController (BaseGIS.Web).
2025-06-17 20:50:08.774 +03:30 [INF] Route matched with {action = "Index", controller = "Symbology"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult Index() on controller BaseGIS.Web.Controllers.SymbologyController (BaseGIS.Web).
2025-06-17 20:50:08.804 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:50:08.825 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:50:08.831 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__ids_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__ids_0
2025-06-17 20:50:08.871 +03:30 [ERR] An exception occurred while iterating over the results of a query for context type 'BaseGIS.Infrastructure.Persistence.ApplicationDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-06-17 20:50:08.885 +03:30 [INF] Executed DbCommand (6ms) [Parameters=[@__tblIds_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tblIds_0
) AS [t0]
LEFT JOIN [FieldInfos] AS [f] ON [t0].[Id] = [f].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:50:09.166 +03:30 [INF] Executed action BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web) in 357.181ms
2025-06-17 20:50:09.173 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 344.8802ms.
2025-06-17 20:50:09.238 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web)'
2025-06-17 20:50:09.240 +03:30 [INF] Executing PartialViewResult, running view Index.
2025-06-17 20:50:09.340 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Linq.Enumerable.TryGetSingle[TSource](IEnumerable`1 source, Boolean& found)
   at lambda_method819(Closure, QueryContext)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.Execute[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.Execute[TResult](Expression expression)
   at BaseGIS.Web.Controllers.SymbologyController.Index()
   at lambda_method785(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-17 20:50:09.350 +03:30 [INF] Executed PartialViewResult - view null executed in 110.6138ms.
2025-06-17 20:50:09.372 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/symbology/index?tblid=7&_=************* - 500 null text/plain; charset=utf-8 608.5173ms
2025-06-17 20:50:09.374 +03:30 [INF] Executed action BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web) in 594.1663ms
2025-06-17 20:50:09.380 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web)'
2025-06-17 20:50:09.383 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/symbology/index?tblid=7&_=1750180804742 - 200 null text/html; charset=utf-8 622.6915ms
2025-06-17 20:50:09.402 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.css - null null
2025-06-17 20:50:09.405 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.min.js - null null
2025-06-17 20:50:09.407 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.css - 404 0 null 5.1951ms
2025-06-17 20:50:09.411 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.min.js - 404 0 null 6.6965ms
2025-06-17 20:50:09.416 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.css, Response status code: 404
2025-06-17 20:50:09.422 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.min.js, Response status code: 404
2025-06-17 20:51:01.576 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-17 20:51:01.577 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-17 20:51:01.581 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-17 20:51:01.586 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-17 20:51:01.590 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 14.1586ms
2025-06-17 20:51:01.609 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 32.4288ms
2025-06-17 20:52:45.677 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - null null
2025-06-17 20:52:45.720 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:52:45.722 +03:30 [INF] Route matched with {action = "SymbologyManager", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult SymbologyManager() on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:52:45.758 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:52:45.768 +03:30 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
2025-06-17 20:52:45.772 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 11.8016ms.
2025-06-17 20:52:45.775 +03:30 [INF] Executing ViewResult, running view SymbologyManager.
2025-06-17 20:52:45.781 +03:30 [INF] Executed ViewResult - view SymbologyManager executed in 6.3481ms.
2025-06-17 20:52:45.797 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web) in 72.0914ms
2025-06-17 20:52:45.800 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController.SymbologyManager (BaseGIS.Web)'
2025-06-17 20:52:45.802 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/SymbologyManager?id=7 - 200 null text/html; charset=utf-8 124.9003ms
2025-06-17 20:52:45.884 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-17 20:52:45.886 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 20:52:45.893 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 7.5378ms
2025-06-17 20:52:45.922 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 38.4011ms
2025-06-17 20:52:46.026 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js.map - null null
2025-06-17 20:52:46.027 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - null null
2025-06-17 20:52:46.029 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js.map - null null
2025-06-17 20:52:46.031 +03:30 [INF] The file /lib/bootstrap/js/bootstrap.bundle.min.js.map was not modified
2025-06-17 20:52:46.033 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js.map - null null
2025-06-17 20:52:46.040 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js.map - 304 null text/plain 14.0313ms
2025-06-17 20:52:46.037 +03:30 [INF] The file /lib/popper.js/dist/umd/popper.min.js.map was not modified
2025-06-17 20:52:46.034 +03:30 [INF] The file /lib/leaflet/leaflet.js.map was not modified
2025-06-17 20:52:46.056 +03:30 [INF] The file /js/adminlte.js.map was not modified
2025-06-17 20:52:46.062 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js.map - 304 null text/plain 33.3295ms
2025-06-17 20:52:46.064 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - 304 null text/plain 36.8999ms
2025-06-17 20:52:46.066 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js.map - 304 null text/plain 32.9197ms
2025-06-17 20:52:46.135 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:52:46.139 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:52:46.139 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - null null
2025-06-17 20:52:46.141 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:52:46.144 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:52:46.171 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:52:46.172 +03:30 [INF] Route matched with {action = "_Layer_Symbols", controller = "Database"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult _Layer_Symbols(System.String) on controller BaseGIS.Web.Controllers.DatabaseController (BaseGIS.Web).
2025-06-17 20:52:46.209 +03:30 [INF] Executed DbCommand (33ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:52:46.209 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:52:46.219 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 44.1468ms.
2025-06-17 20:52:46.222 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__tableId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [s].[Id], [s].[FieldAlias], [s].[FieldName], [s].[IsDefault], [s].[Json], [s].[Name], [s].[TableInfoId], [s].[Type]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tableId_0
) AS [t0]
LEFT JOIN [SymbologyInfos] AS [s] ON [t0].[Id] = [s].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:52:46.226 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:52:46.234 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 13.0769ms.
2025-06-17 20:52:46.236 +03:30 [INF] Executed PartialViewResult - view null executed in 10.0434ms.
2025-06-17 20:52:46.241 +03:30 [INF] Executing PartialViewResult, running view _Layer_Symbols.
2025-06-17 20:52:46.243 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 93.6994ms
2025-06-17 20:52:46.246 +03:30 [INF] Executed PartialViewResult - view null executed in 4.9839ms.
2025-06-17 20:52:46.248 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:52:46.250 +03:30 [INF] Executed action BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web) in 67.5677ms
2025-06-17 20:52:46.252 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 117.0535ms
2025-06-17 20:52:46.256 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.DatabaseController._Layer_Symbols (BaseGIS.Web)'
2025-06-17 20:52:46.262 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/_Layer_Symbols?id=7 - 200 null text/html; charset=utf-8 122.6188ms
2025-06-17 20:52:46.473 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - null null
2025-06-17 20:52:46.477 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Database/content/img/demo/64x64.png - 404 0 null 3.4379ms
2025-06-17 20:52:46.484 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Database/content/img/demo/64x64.png, Response status code: 404
2025-06-17 20:52:46.857 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - null null
2025-06-17 20:52:46.858 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-17 20:52:46.863 +03:30 [INF] The file /css/adminlte.rtl.css.map was not modified
2025-06-17 20:52:46.869 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-17 20:52:46.872 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css.map - 304 null text/plain 14.966ms
2025-06-17 20:52:46.875 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 16.6037ms
2025-06-17 20:52:50.407 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/symbology/index?tblid=7&_=1750180965958 - null null
2025-06-17 20:52:50.411 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/symbology/index?tblid=7&_=************* - null null
2025-06-17 20:52:50.411 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web)'
2025-06-17 20:52:50.415 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web)'
2025-06-17 20:52:50.416 +03:30 [INF] Route matched with {action = "Index", controller = "Symbology"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult Index() on controller BaseGIS.Web.Controllers.SymbologyController (BaseGIS.Web).
2025-06-17 20:52:50.421 +03:30 [INF] Route matched with {action = "Index", controller = "Symbology"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult Index() on controller BaseGIS.Web.Controllers.SymbologyController (BaseGIS.Web).
2025-06-17 20:52:50.446 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:52:50.473 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-17 20:52:50.485 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[@__ids_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
FROM [TableInfos] AS [t]
WHERE [t].[Id] = @__ids_0
2025-06-17 20:52:50.538 +03:30 [ERR] An exception occurred while iterating over the results of a query for context type 'BaseGIS.Infrastructure.Persistence.ApplicationDbContext'.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-06-17 20:52:50.549 +03:30 [INF] Executed DbCommand (7ms) [Parameters=[@__tblIds_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT [t0].[Id], [t0].[AliasName], [t0].[DatasetType], [t0].[GroupInfoId], [t0].[MaxLabelScale], [t0].[MaxScale], [t0].[MinLabelScale], [t0].[MinScale], [t0].[Name], [t0].[ShortName], [t0].[SimplifyFactor], [t0].[ValidationRule], [f].[Id], [f].[AliasName], [f].[CalcPeriod], [f].[DisableRule], [f].[Editable], [f].[FieldIndex], [f].[FieldLength], [f].[FieldType], [f].[IsDisplay], [f].[IsRequired], [f].[IsUnique], [f].[Name], [f].[SQLCalc], [f].[ShpFieldName], [f].[TableInfoId], [f].[UnitName], [f].[Updated], [f].[ValidationRule], [f].[WebService_Period], [f].[WebService_URL]
FROM (
    SELECT TOP(1) [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule]
    FROM [TableInfos] AS [t]
    WHERE [t].[Id] = @__tblIds_0
) AS [t0]
LEFT JOIN [FieldInfos] AS [f] ON [t0].[Id] = [f].[TableInfoId]
ORDER BY [t0].[Id]
2025-06-17 20:52:50.838 +03:30 [INF] Executed action BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web) in 385.2133ms
2025-06-17 20:52:50.840 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.PartialViewResult in 363.7668ms.
2025-06-17 20:52:50.903 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web)'
2025-06-17 20:52:50.905 +03:30 [INF] Executing PartialViewResult, running view Index.
2025-06-17 20:52:50.985 +03:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Linq.Enumerable.TryGetSingle[TSource](IEnumerable`1 source, Boolean& found)
   at lambda_method819(Closure, QueryContext)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.Execute[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.Execute[TResult](Expression expression)
   at BaseGIS.Web.Controllers.SymbologyController.Index()
   at lambda_method785(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeFilterPipelineAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-17 20:52:50.991 +03:30 [INF] Executed PartialViewResult - view null executed in 85.6415ms.
2025-06-17 20:52:51.022 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/symbology/index?tblid=7&_=************* - 500 null text/plain; charset=utf-8 611.2194ms
2025-06-17 20:52:51.022 +03:30 [INF] Executed action BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web) in 595.9208ms
2025-06-17 20:52:51.033 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.SymbologyController.Index (BaseGIS.Web)'
2025-06-17 20:52:51.036 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/symbology/index?tblid=7&_=1750180965958 - 200 null text/html; charset=utf-8 628.9544ms
2025-06-17 20:52:51.062 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.css - null null
2025-06-17 20:52:51.066 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.min.js - null null
2025-06-17 20:52:51.067 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.css - 404 0 null 5.2522ms
2025-06-17 20:52:51.072 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.min.js - 404 0 null 6.739ms
2025-06-17 20:52:51.079 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.css, Response status code: 404
2025-06-17 20:52:51.083 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Scripts/plugin/bootstrap-colorpicker/bootstrap-colorpicker.min.js, Response status code: 404
