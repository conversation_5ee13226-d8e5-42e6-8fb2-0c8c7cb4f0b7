﻿using Microsoft.AspNetCore.Identity;

namespace BaseGIS.Core.Entities
{
    public class ApplicationRole : IdentityRole
    {
        public ApplicationRole() : base() { }
        public ApplicationRole(string name, string description)
            : base(name)
        {
            this.Description = description;
        }
        public virtual string Description { get; set; }

        public virtual string Department { get; set; }

        public virtual string Position { get; set; }

        public virtual string FromTimeActivity { get; set; }

        public virtual string ToTimeActivity { get; set; }

        public string TableAccess { get; set; }
        public string ToolAccess { get; set; }

        public virtual string ConvertTable(TableAccess tableAccess)
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(tableAccess);
        }
        public virtual TableAccess ConvertTable(string tableAccess)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<TableAccess>(tableAccess);
        }

        public virtual string ConvertTool(ToolAccess toolAccess)
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(toolAccess);
        }
        public virtual ToolAccess ConvertTool(string toolAccess)
        {
            return Newtonsoft.Json.JsonConvert.DeserializeObject<ToolAccess>(toolAccess);
        }
    }


    #region Table Access
    public class TableAccess
    {
        public List<Table> Tables { get; set; }
    }

    public class Table
    {
        public string Name { get; set; }
        public List<Field> Fields { get; set; }
        public bool IsAdd { get; set; }
        public bool IsEdit { get; set; }
        public bool IsView { get; set; }
        public bool IsDel { get; set; }
    }

    public class Field
    {
        public string Name { get; set; }
        public string Query { get; set; }
    }
    #endregion


    #region Tool Access
    public class ToolAccess
    {
        public List<string> DashboardIDs { get; set; }

        public List<string> ReportIDs { get; set; }

        public Service Service { get; set; }

        public Tools Tools { get; set; }
    }

    public class Service
    {
        public bool IsView { get; set; }
        public bool isEdit { get; set; }
    }

    public class Tools
    {
        public bool Edit { get; set; }
        public bool Search_Att { get; set; }
        public bool Search_Spatial { get; set; }
        public bool Search_Manual { get; set; }

        public bool Report_Quick { get; set; }
        public bool Report { get; set; }
        public bool Report_Overlay { get; set; }

        public bool Import { get; set; }
        public bool Export { get; set; }
        public bool Print { get; set; }
    }
    #endregion

}
