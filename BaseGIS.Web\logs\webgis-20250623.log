2025-06-23 10:03:21.578 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-23 10:03:24.464 +03:30 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-23 10:03:24.555 +03:30 [INF] Executed DbCommand (62ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-23 10:03:24.713 +03:30 [ERR] An error occurred while seeding the database
System.InvalidOperationException: No service for type 'Microsoft.AspNetCore.Identity.RoleManager`1[Microsoft.AspNetCore.Identity.IdentityRole]' has been registered.
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at BaseGIS.Infrastructure.Persistence.SeedData.Initialize(IServiceProvider serviceProvider, ILogger logger) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Infrastructure\Persistence\SeedData.cs:line 17
2025-06-23 10:03:24.869 +03:30 [INF] Database seeding completed
2025-06-23 10:03:26.685 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-23 10:03:26.686 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-23 10:03:27.048 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 10:03:27.049 +03:30 [INF] Hosting environment: Development
2025-06-23 10:03:27.050 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-23 10:03:27.055 +03:30 [INF] The application has started
2025-06-23 10:03:38.570 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-23 10:03:39.110 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-23 10:03:39.140 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-23 10:03:39.399 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:03:39.407 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 1.3564ms.
2025-06-23 10:03:39.527 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:03:39.733 +03:30 [INF] Executed ViewResult - view Index executed in 312.3701ms.
2025-06-23 10:03:39.741 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 596.1336ms
2025-06-23 10:03:39.743 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-23 10:03:39.752 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 1194.6053ms
2025-06-23 10:03:39.818 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-23 10:03:39.818 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - null null
2025-06-23 10:03:39.818 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - null null
2025-06-23 10:03:39.865 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - null null
2025-06-23 10:03:39.818 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-23 10:03:39.866 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - null null
2025-06-23 10:03:39.875 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - null null
2025-06-23 10:03:39.895 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - null null
2025-06-23 10:03:39.895 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - null null
2025-06-23 10:03:40.518 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.css'
2025-06-23 10:03:40.518 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-23 10:03:40.518 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-23 10:03:40.518 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-23 10:03:40.518 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.css'
2025-06-23 10:03:40.528 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - 499 23932 text/css 662.5277ms
2025-06-23 10:03:40.529 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - ********** text/css 711.5018ms
2025-06-23 10:03:40.532 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - 499 14806 text/css 714.1029ms
2025-06-23 10:03:40.534 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - ********** text/css 716.2532ms
2025-06-23 10:03:40.534 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - null null
2025-06-23 10:03:40.535 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-23 10:03:40.536 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - 499 5267 text/css 717.5897ms
2025-06-23 10:03:40.536 +03:30 [INF] Sending file. Request path: '/lib/source-sans-3/index.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\source-sans-3\index.css'
2025-06-23 10:03:40.542 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-23 10:03:40.545 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-23 10:03:40.549 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/avatars/male.png - null null
2025-06-23 10:03:40.553 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - null null
2025-06-23 10:03:40.563 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - null null
2025-06-23 10:03:40.576 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery-validation/dist/jquery.validate.min.js - null null
2025-06-23 10:03:40.658 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-23 10:03:40.706 +03:30 [INF] Sending file. Request path: '/assets/img/AdminLTELogo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\AdminLTELogo.png'
2025-06-23 10:03:40.706 +03:30 [INF] The file /css/adminlte.rtl.css was not modified
2025-06-23 10:03:40.698 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-23 10:03:40.566 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-23 10:03:40.557 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery/jquery.min.js - null null
2025-06-23 10:03:40.565 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - 200 2520 text/css 699.447ms
2025-06-23 10:03:40.835 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - 200 2637 image/png 281.9742ms
2025-06-23 10:03:40.837 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/adminlte.rtl.css - 304 null text/css 273.9049ms
2025-06-23 10:03:40.838 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/avatars/male.png - 499 268 image/png 289.0917ms
2025-06-23 10:03:40.831 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation/dist/jquery.validate.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js'
2025-06-23 10:03:40.832 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 200 3398 image/jpeg 286.9214ms
2025-06-23 10:03:40.840 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 200 2750 image/jpeg 305.4112ms
2025-06-23 10:03:40.568 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js - null null
2025-06-23 10:03:41.024 +03:30 [INF] Sending file. Request path: '/lib/jquery/jquery.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery\jquery.min.js'
2025-06-23 10:03:41.034 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - null null
2025-06-23 10:03:41.038 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - null null
2025-06-23 10:03:41.042 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.js - null null
2025-06-23 10:03:41.343 +03:30 [INF] Sending file. Request path: '/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js'
2025-06-23 10:03:41.476 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.js'
2025-06-23 10:03:41.044 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery-validation/dist/jquery.validate.min.js - 499 25308 text/javascript 468.7165ms
2025-06-23 10:03:41.048 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - null null
2025-06-23 10:03:41.053 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - null null
2025-06-23 10:03:41.652 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - 499 5824 text/javascript 618.4876ms
2025-06-23 10:03:41.655 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js - 499 147552 text/javascript 617.0331ms
2025-06-23 10:03:41.832 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:03:41.660 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js - null null
2025-06-23 10:03:41.218 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery/jquery.min.js - 499 87533 text/javascript 660.8078ms
2025-06-23 10:03:41.818 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=Nmb9k2AhsnpOv4cI_Fjp8KgGhTAxQTOTwjkpS20OWjE - null null
2025-06-23 10:03:41.652 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.js'
2025-06-23 10:03:41.812 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.js'
2025-06-23 10:03:41.866 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - 499 46668 text/javascript 817.638ms
2025-06-23 10:03:41.218 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/js/bootstrap.bundle.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\js\bootstrap.bundle.min.js'
2025-06-23 10:03:41.871 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js - 499 80721 text/javascript 1303.2944ms
2025-06-23 10:03:41.875 +03:30 [INF] Sending file. Request path: '/lib/popper.js/dist/umd/popper.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\popper.js\dist\umd\popper.min.js'
2025-06-23 10:03:41.864 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.js - 499 67484 text/javascript 821.3989ms
2025-06-23 10:03:41.029 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:03:41.868 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-23 10:03:41.850 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/styles/overlayscrollbars.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\styles\overlayscrollbars.min.css'
2025-06-23 10:03:41.857 +03:30 [INF] Sending file. Request path: '/css/site.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site.css'
2025-06-23 10:03:41.877 +03:30 [INF] Sending file. Request path: '/js/site.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\site.js'
2025-06-23 10:03:41.877 +03:30 [INF] Sending file. Request path: '/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\overlayscrollbars\browser\overlayscrollbars.browser.es6.min.js'
2025-06-23 10:03:41.878 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - 200 20122 text/javascript 825.9515ms
2025-06-23 10:03:41.886 +03:30 [INF] Sending file. Request path: '/js/adminlte.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\adminlte.js'
2025-06-23 10:03:41.888 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 200 4983 image/jpeg 1346.4988ms
2025-06-23 10:03:41.890 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/styles/overlayscrollbars.min.css - 200 13202 text/css 2015.2219ms
2025-06-23 10:03:41.893 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 864.1008ms
2025-06-23 10:03:41.895 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - 200 7707 text/css 1360.5061ms
2025-06-23 10:03:41.897 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=Nmb9k2AhsnpOv4cI_Fjp8KgGhTAxQTOTwjkpS20OWjE - 200 5215 text/javascript 78.6292ms
2025-06-23 10:03:41.899 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/overlayscrollbars/browser/overlayscrollbars.browser.es6.min.js - 200 29334 text/javascript 2004.3804ms
2025-06-23 10:03:41.904 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/bootstrap-icons.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\bootstrap-icons.min.css'
2025-06-23 10:03:41.905 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js - 200 29455 text/javascript 244.5972ms
2025-06-23 10:03:41.941 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - 200 85875 text/css 2045.5067ms
2025-06-23 10:03:41.984 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-23 10:03:42.190 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - null null
2025-06-23 10:03:42.210 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-23 10:03:42.231 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-23 10:03:42.463 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 499 45276 application/font-woff 479.1188ms
2025-06-23 10:03:42.458 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/webfonts/fa-solid-900.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\webfonts\fa-solid-900.woff2'
2025-06-23 10:03:42.402 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-23 10:03:42.469 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - 499 158220 font/woff2 329.1171ms
2025-06-23 10:03:42.472 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-23 10:03:42.632 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-23 10:03:42.637 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - 200 130396 font/woff2 498.5225ms
2025-06-23 10:03:42.638 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 805.5385ms
2025-06-23 10:03:42.640 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 499 5430 image/x-icon 288.7748ms
2025-06-23 10:05:28.979 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:05:29.083 +03:30 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-23 10:05:29.091 +03:30 [INF] AuthenticationScheme: Cookies was challenged.
2025-06-23 10:05:29.094 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 302 0 null 115.636ms
2025-06-23 10:05:29.101 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Account/Login?ReturnUrl=%2FGeoMap - null null
2025-06-23 10:05:29.145 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 10:05:29.157 +03:30 [INF] Route matched with {action = "Login", controller = "Account"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Login(System.String) on controller BaseGIS.Web.Controllers.AccountController (BaseGIS.Web).
2025-06-23 10:05:29.216 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:05:29.220 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.347ms.
2025-06-23 10:05:29.224 +03:30 [INF] Executing ViewResult, running view Login.
2025-06-23 10:05:29.378 +03:30 [INF] Executed ViewResult - view Login executed in 155.6781ms.
2025-06-23 10:05:29.381 +03:30 [INF] Executed action BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) in 220.374ms
2025-06-23 10:05:29.383 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 10:05:29.384 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Account/Login?ReturnUrl=%2FGeoMap - 200 null text/html; charset=utf-8 283.0788ms
2025-06-23 10:05:29.427 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/dist/css/bootstrap.rtl.min.css - null null
2025-06-23 10:05:29.428 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery/dist/jquery.min.js - null null
2025-06-23 10:05:29.427 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/login.css - null null
2025-06-23 10:05:29.442 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/dist/js/bootstrap.bundle.min.js - null null
2025-06-23 10:05:29.450 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:05:29.481 +03:30 [INF] Sending file. Request path: '/css/login.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\login.css'
2025-06-23 10:05:29.455 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:05:29.505 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/login.css - 200 2788 text/css 77.6554ms
2025-06-23 10:05:29.522 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 66.8398ms
2025-06-23 10:05:29.507 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/dist/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.min.css'
2025-06-23 10:05:29.535 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/dist/js/bootstrap.bundle.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js'
2025-06-23 10:05:29.536 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 92.1806ms
2025-06-23 10:05:29.540 +03:30 [INF] Sending file. Request path: '/lib/jquery/dist/jquery.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery\dist\jquery.min.js'
2025-06-23 10:05:29.543 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/dist/css/bootstrap.rtl.min.css - 200 232911 text/css 116.4435ms
2025-06-23 10:05:29.546 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/dist/js/bootstrap.bundle.min.js - 200 80721 text/javascript 104.0239ms
2025-06-23 10:05:29.552 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery/dist/jquery.min.js - 200 87533 text/javascript 124.13ms
2025-06-23 10:05:39.890 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Account/Login - application/x-www-form-urlencoded 252
2025-06-23 10:05:39.957 +03:30 [INF] CORS policy execution successful.
2025-06-23 10:05:39.985 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 10:05:39.993 +03:30 [INF] Route matched with {action = "Login", controller = "Account"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(BaseGIS.Web.ViewModels.LoginViewModel, System.String) on controller BaseGIS.Web.Controllers.AccountController (BaseGIS.Web).
2025-06-23 10:05:40.051 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:05:47.284 +03:30 [INF] Executed DbCommand (207ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedUserName] = @__normalizedUserName_0
2025-06-23 10:06:09.125 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 29071.844ms.
2025-06-23 10:06:09.130 +03:30 [INF] Executing ViewResult, running view Login.
2025-06-23 10:06:09.137 +03:30 [INF] Executed ViewResult - view Login executed in 7.0894ms.
2025-06-23 10:06:09.180 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:06:09.181 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:06:09.194 +03:30 [INF] Executed action BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) in 29197.2009ms
2025-06-23 10:06:09.203 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 22.0982ms
2025-06-23 10:06:09.227 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 10:06:09.228 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 47.3096ms
2025-06-23 10:06:09.237 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Account/Login - 200 null text/html; charset=utf-8 29347.194ms
2025-06-23 10:08:59.412 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-23 10:09:00.785 +03:30 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-23 10:09:00.850 +03:30 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-23 10:09:00.960 +03:30 [ERR] An error occurred while seeding the database
System.InvalidOperationException: No service for type 'Microsoft.AspNetCore.Identity.RoleManager`1[Microsoft.AspNetCore.Identity.IdentityRole]' has been registered.
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at BaseGIS.Infrastructure.Persistence.SeedData.Initialize(IServiceProvider serviceProvider, ILogger logger) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Infrastructure\Persistence\SeedData.cs:line 17
2025-06-23 10:09:00.997 +03:30 [INF] Database seeding completed
2025-06-23 10:09:01.629 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-23 10:09:01.631 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-23 10:09:01.705 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 10:09:01.708 +03:30 [INF] Hosting environment: Development
2025-06-23 10:09:01.709 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-23 10:09:01.712 +03:30 [INF] The application has started
2025-06-23 10:09:03.646 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-23 10:09:04.083 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-23 10:09:04.339 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-23 10:09:04.499 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:09:04.508 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 1.5233ms.
2025-06-23 10:09:04.532 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:09:04.757 +03:30 [INF] Executed ViewResult - view Index executed in 230.4981ms.
2025-06-23 10:09:04.876 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-23 10:09:04.881 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 538.2446ms
2025-06-23 10:09:04.878 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - null null
2025-06-23 10:09:04.920 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-23 10:09:04.877 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-23 10:09:05.229 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - null null
2025-06-23 10:09:05.552 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-23 10:09:05.671 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-23 10:09:05.552 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-23 10:09:04.903 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - null null
2025-06-23 10:09:05.310 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:09:05.809 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 2114.437ms
2025-06-23 10:09:05.810 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - 499 14806 text/css 932.1169ms
2025-06-23 10:09:05.812 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - ********** text/css 934.9486ms
2025-06-23 10:09:05.808 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.css'
2025-06-23 10:09:05.814 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - ********** text/css 938.5577ms
2025-06-23 10:09:05.534 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js - null null
2025-06-23 10:09:05.484 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - null null
2025-06-23 10:09:05.959 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 649.54ms
2025-06-23 10:09:05.965 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - 499 23932 text/css 831.2442ms
2025-06-23 10:09:05.948 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.css'
2025-06-23 10:09:05.955 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:09:06.121 +03:30 [INF] Sending file. Request path: '/js/adminlte.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\adminlte.js'
2025-06-23 10:09:06.249 +03:30 [INF] Sending file. Request path: '/lib/popper.js/dist/umd/popper.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\popper.js\dist\umd\popper.min.js'
2025-06-23 10:09:06.259 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - 499 5267 text/css 1356.1915ms
2025-06-23 10:09:06.265 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js - 499 29455 text/javascript 745.3095ms
2025-06-23 10:09:06.266 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - 499 20122 text/javascript 796.9925ms
2025-06-23 10:09:06.305 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 349.5702ms
2025-06-23 10:09:45.051 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-23 10:09:46.349 +03:30 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-23 10:09:46.400 +03:30 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-23 10:22:17.566 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-23 10:22:18.767 +03:30 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-23 10:22:18.820 +03:30 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-23 10:22:25.577 +03:30 [ERR] An error occurred while seeding the database
System.InvalidOperationException: No service for type 'Microsoft.AspNetCore.Identity.RoleManager`1[Microsoft.AspNetCore.Identity.IdentityRole]' has been registered.
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at BaseGIS.Infrastructure.Persistence.SeedData.Initialize(IServiceProvider serviceProvider, ILogger logger) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Infrastructure\Persistence\SeedData.cs:line 17
2025-06-23 10:22:25.614 +03:30 [INF] Database seeding completed
2025-06-23 10:22:26.154 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-23 10:22:26.157 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-23 10:22:26.223 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 10:22:26.226 +03:30 [INF] Hosting environment: Development
2025-06-23 10:22:26.227 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-23 10:22:26.230 +03:30 [INF] The application has started
2025-06-23 10:22:28.694 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-23 10:22:29.061 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-23 10:22:29.088 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-23 10:22:29.262 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:22:29.273 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 1.4043ms.
2025-06-23 10:22:29.304 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:22:29.583 +03:30 [INF] Executed ViewResult - view Index executed in 286.5387ms.
2025-06-23 10:22:29.591 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 499.511ms
2025-06-23 10:22:29.593 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-23 10:22:29.603 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 916.6495ms
2025-06-23 10:22:29.687 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-23 10:22:29.687 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-23 10:22:29.704 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-23 10:22:29.748 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/avatars/male.png - null null
2025-06-23 10:22:29.704 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-23 10:22:29.748 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-23 10:22:30.556 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-23 10:22:30.556 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-23 10:22:30.556 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-23 10:22:30.690 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-23 10:22:30.556 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-23 10:22:29.804 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - null null
2025-06-23 10:22:29.875 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:22:30.844 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/avatars/male.png - 499 268 image/png 1096.6749ms
2025-06-23 10:22:30.847 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 499 4983 image/jpeg 1142.0701ms
2025-06-23 10:22:30.849 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - ********** text/css 1161.9304ms
2025-06-23 10:22:30.851 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 499 2750 image/jpeg 1146.145ms
2025-06-23 10:22:30.852 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - ********** text/css 1165.9851ms
2025-06-23 10:22:30.842 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-23 10:22:29.911 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - null null
2025-06-23 10:22:30.505 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js - null null
2025-06-23 10:22:31.022 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:22:31.027 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-23 10:22:31.031 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-23 10:22:31.453 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-23 10:22:31.034 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - null null
2025-06-23 10:22:31.040 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 1188.7148ms
2025-06-23 10:22:31.593 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 499 45276 application/font-woff 566.4573ms
2025-06-23 10:22:31.592 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-23 10:22:31.042 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 499 3398 image/jpeg 1294.2382ms
2025-06-23 10:22:31.015 +03:30 [INF] Sending file. Request path: '/assets/img/AdminLTELogo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\AdminLTELogo.png'
2025-06-23 10:22:31.022 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=Nmb9k2AhsnpOv4cI_Fjp8KgGhTAxQTOTwjkpS20OWjE - null null
2025-06-23 10:22:31.193 +03:30 [INF] Sending file. Request path: '/lib/popper.js/dist/umd/popper.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\popper.js\dist\umd\popper.min.js'
2025-06-23 10:22:31.738 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 715.4604ms
2025-06-23 10:22:31.730 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - ********** font/woff2 698.7743ms
2025-06-23 10:22:31.720 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/webfonts/fa-solid-900.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\webfonts\fa-solid-900.woff2'
2025-06-23 10:22:31.724 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:22:31.736 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - 499 2637 image/png 1974.3087ms
2025-06-23 10:22:31.876 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - 499 158220 font/woff2 842.1981ms
2025-06-23 10:22:31.864 +03:30 [INF] Sending file. Request path: '/js/site.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\site.js'
2025-06-23 10:22:31.866 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - 499 20122 text/javascript 1991.7731ms
2025-06-23 10:22:31.317 +03:30 [INF] Sending file. Request path: '/js/adminlte.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\adminlte.js'
2025-06-23 10:22:31.924 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:22:31.928 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=Nmb9k2AhsnpOv4cI_Fjp8KgGhTAxQTOTwjkpS20OWjE - 499 5215 text/javascript 906.2055ms
2025-06-23 10:22:31.935 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js - 499 29455 text/javascript 1473.7194ms
2025-06-23 10:22:31.947 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:22:31.986 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:22:32.008 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.RedirectToActionResult in 19.6768ms.
2025-06-23 10:22:32.013 +03:30 [INF] Executing RedirectResult, redirecting to /Account/Login.
2025-06-23 10:22:32.015 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 64.3898ms
2025-06-23 10:22:32.016 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:22:32.017 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 302 0 null 293.6152ms
2025-06-23 10:22:32.026 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Account/Login - null null
2025-06-23 10:22:32.073 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 10:22:32.083 +03:30 [INF] Route matched with {action = "Login", controller = "Account"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Login(System.String) on controller BaseGIS.Web.Controllers.AccountController (BaseGIS.Web).
2025-06-23 10:22:32.124 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:22:32.127 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.2712ms.
2025-06-23 10:22:32.130 +03:30 [INF] Executing ViewResult, running view Login.
2025-06-23 10:22:32.214 +03:30 [INF] Executed ViewResult - view Login executed in 84.2226ms.
2025-06-23 10:22:32.218 +03:30 [INF] Executed action BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) in 129.1624ms
2025-06-23 10:22:32.221 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 10:22:32.223 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Account/Login - 200 null text/html; charset=utf-8 197.1956ms
2025-06-23 10:22:32.264 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:22:32.265 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:22:32.284 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 19.1725ms
2025-06-23 10:22:32.297 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 32.5469ms
2025-06-23 10:22:46.779 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:22:46.785 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 5.5522ms
2025-06-23 10:22:48.068 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:22:48.138 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:22:48.141 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:22:48.182 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:33:07.053 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-23 10:33:08.273 +03:30 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-23 10:33:08.353 +03:30 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-23 10:33:08.486 +03:30 [ERR] An error occurred while seeding the database
System.InvalidOperationException: No service for type 'Microsoft.AspNetCore.Identity.RoleManager`1[Microsoft.AspNetCore.Identity.IdentityRole]' has been registered.
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at BaseGIS.Infrastructure.Persistence.SeedData.Initialize(IServiceProvider serviceProvider, ILogger logger) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Infrastructure\Persistence\SeedData.cs:line 17
2025-06-23 10:33:08.523 +03:30 [INF] Database seeding completed
2025-06-23 10:33:09.063 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-23 10:33:09.067 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-23 10:33:09.143 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 10:33:09.146 +03:30 [INF] Hosting environment: Development
2025-06-23 10:33:09.149 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-23 10:33:09.153 +03:30 [INF] The application has started
2025-06-23 10:33:11.542 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-23 10:33:11.954 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-23 10:33:12.047 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-23 10:33:12.207 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:33:12.221 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 1.1731ms.
2025-06-23 10:33:12.247 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:33:12.469 +03:30 [INF] Executed ViewResult - view Index executed in 230.1576ms.
2025-06-23 10:33:12.476 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 422.6645ms
2025-06-23 10:33:12.479 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-23 10:33:12.488 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 955.6866ms
2025-06-23 10:33:12.584 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - null null
2025-06-23 10:33:12.585 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - null null
2025-06-23 10:33:12.584 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - null null
2025-06-23 10:33:12.584 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - null null
2025-06-23 10:33:12.584 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - null null
2025-06-23 10:33:12.722 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - null null
2025-06-23 10:33:12.683 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - null null
2025-06-23 10:33:13.752 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.css'
2025-06-23 10:33:13.752 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/css/all.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\css\all.css'
2025-06-23 10:33:13.752 +03:30 [INF] Sending file. Request path: '/lib/bootstrap/css/bootstrap.rtl.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap\css\bootstrap.rtl.min.css'
2025-06-23 10:33:13.752 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.css'
2025-06-23 10:33:13.786 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/leaflet.draw.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\leaflet.draw.css'
2025-06-23 10:33:13.786 +03:30 [INF] Sending file. Request path: '/lib/popper.js/dist/umd/popper.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\popper.js\dist\umd\popper.min.js'
2025-06-23 10:33:12.683 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:33:12.722 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/adminlte.js - null null
2025-06-23 10:33:13.642 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/site.js?v=Nmb9k2AhsnpOv4cI_Fjp8KgGhTAxQTOTwjkpS20OWjE - null null
2025-06-23 10:33:13.954 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.css - 499 23932 text/css 1367.9963ms
2025-06-23 10:33:13.956 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/css/all.css - ********** text/css 1371.9471ms
2025-06-23 10:33:13.958 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css - ********** text/css 1374.1685ms
2025-06-23 10:33:13.961 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.css - 499 14806 text/css 1376.7276ms
2025-06-23 10:33:13.963 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/leaflet.draw.css - 499 5267 text/css 1378.3846ms
2025-06-23 10:33:13.965 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/popper.js/dist/umd/popper.min.js - 499 20122 text/javascript 1272.4035ms
2025-06-23 10:33:13.953 +03:30 [INF] Sending file. Request path: '/lib/source-sans-3/index.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\source-sans-3\index.css'
2025-06-23 10:33:14.111 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:33:14.111 +03:30 [INF] Sending file. Request path: '/js/adminlte.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\adminlte.js'
2025-06-23 10:33:14.240 +03:30 [INF] Sending file. Request path: '/js/site.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\site.js'
2025-06-23 10:33:14.247 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 1563.6702ms
2025-06-23 10:33:14.266 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/source-sans-3/index.css - 499 2520 text/css 1583.1959ms
2025-06-23 10:33:14.272 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/adminlte.js - 499 29455 text/javascript 1550.307ms
2025-06-23 10:33:14.274 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/site.js?v=Nmb9k2AhsnpOv4cI_Fjp8KgGhTAxQTOTwjkpS20OWjE - 499 5215 text/javascript 632.3434ms
2025-06-23 10:33:14.318 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 207.2967ms
2025-06-23 10:33:14.362 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-23 10:33:14.496 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-23 10:33:14.498 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 499 5430 image/x-icon 136.0823ms
2025-06-23 10:33:18.829 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:33:18.884 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:33:18.894 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:33:18.936 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:33:22.879 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.RedirectToActionResult in 3939.3361ms.
2025-06-23 10:33:22.883 +03:30 [INF] Executing RedirectResult, redirecting to /Account/Login.
2025-06-23 10:33:22.885 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 3987.6761ms
2025-06-23 10:33:22.886 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:33:22.888 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 302 0 null 4058.583ms
2025-06-23 10:33:22.901 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Account/Login - null null
2025-06-23 10:33:22.939 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 10:33:22.947 +03:30 [INF] Route matched with {action = "Login", controller = "Account"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Login(System.String) on controller BaseGIS.Web.Controllers.AccountController (BaseGIS.Web).
2025-06-23 10:33:22.982 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:33:22.984 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.2393ms.
2025-06-23 10:33:22.988 +03:30 [INF] Executing ViewResult, running view Login.
2025-06-23 10:33:23.045 +03:30 [INF] Executed ViewResult - view Login executed in 57.1119ms.
2025-06-23 10:33:23.047 +03:30 [INF] Executed action BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) in 97.7768ms
2025-06-23 10:33:23.050 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 10:33:23.052 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Account/Login - 200 null text/html; charset=utf-8 150.6901ms
2025-06-23 10:33:23.086 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:33:23.087 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:33:23.096 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 8.7704ms
2025-06-23 10:33:23.108 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 21.918ms
2025-06-23 10:33:33.117 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Account/Login - application/x-www-form-urlencoded 243
2025-06-23 10:33:33.169 +03:30 [INF] CORS policy execution successful.
2025-06-23 10:33:33.191 +03:30 [WRN] SQL Injection attempt detected: CfDJ8DEpU4sArOBOlYviGOulFZsDHOqtkPAkUEivxKnQp6cjmMpIrzGNJ3Tm-YYI0lD_NJ2nCZCZS5nrkF1LmzNhhxegGNlumROV8fc532ErHj2EAjcFUvTrm1t2QXTxRphaXZtDoSb2-s--KKFoAY9ppws
2025-06-23 10:33:33.194 +03:30 [WRN] Invalid SQL parameter detected: __RequestVerificationToken=CfDJ8DEpU4sArOBOlYviGOulFZsDHOqtkPAkUEivxKnQp6cjmMpIrzGNJ3Tm-YYI0lD_NJ2nCZCZS5nrkF1LmzNhhxegGNlumROV8fc532ErHj2EAjcFUvTrm1t2QXTxRphaXZtDoSb2-s--KKFoAY9ppws
2025-06-23 10:33:33.196 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Account/Login - 400 null null 78.9347ms
2025-06-23 10:33:45.622 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:33:45.627 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 4.8726ms
2025-06-23 10:33:48.236 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:33:48.273 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:33:48.275 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:33:48.305 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:33:51.039 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.RedirectToActionResult in 2730.7086ms.
2025-06-23 10:33:51.043 +03:30 [INF] Executing RedirectResult, redirecting to /Account/Login.
2025-06-23 10:33:51.044 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 2767.2575ms
2025-06-23 10:33:51.047 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:33:51.048 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 302 0 null 2812.7533ms
2025-06-23 10:33:51.055 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Account/Login - null null
2025-06-23 10:33:51.087 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 10:33:51.090 +03:30 [INF] Route matched with {action = "Login", controller = "Account"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Login(System.String) on controller BaseGIS.Web.Controllers.AccountController (BaseGIS.Web).
2025-06-23 10:33:51.116 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:33:51.119 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0209ms.
2025-06-23 10:33:51.122 +03:30 [INF] Executing ViewResult, running view Login.
2025-06-23 10:33:51.134 +03:30 [INF] Executed ViewResult - view Login executed in 12.7393ms.
2025-06-23 10:33:51.137 +03:30 [INF] Executed action BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) in 44.7394ms
2025-06-23 10:33:51.140 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 10:33:51.141 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Account/Login - 200 null text/html; charset=utf-8 86.2921ms
2025-06-23 10:33:51.168 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:33:51.169 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:33:51.184 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 15.3634ms
2025-06-23 10:33:51.186 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 17.601ms
2025-06-23 10:34:05.198 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Account/Login - application/x-www-form-urlencoded 243
2025-06-23 10:34:05.239 +03:30 [INF] CORS policy execution successful.
2025-06-23 10:34:05.242 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 10:34:05.256 +03:30 [INF] Route matched with {action = "Login", controller = "Account"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(BaseGIS.Web.ViewModels.LoginViewModel, System.String) on controller BaseGIS.Web.Controllers.AccountController (BaseGIS.Web).
2025-06-23 10:34:05.310 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:34:10.284 +03:30 [INF] Executed DbCommand (54ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedUserName] = @__normalizedUserName_0
2025-06-23 10:34:14.802 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 10:34:21.679 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedUserName] = @__normalizedUserName_0
2025-06-23 10:34:21.881 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedUserName] = @__normalizedUserName_0
2025-06-23 10:34:22.131 +03:30 [INF] Executed DbCommand (20ms) [Parameters=[@p18='?' (Size = 450), @p0='?' (DbType = Int32), @p1='?' (Size = 4000), @p19='?' (Size = 4000), @p2='?' (Size = 256), @p3='?' (DbType = Boolean), @p4='?' (DbType = Boolean), @p5='?' (Size = 4000), @p6='?' (Size = -1), @p7='?' (DbType = Boolean), @p8='?' (DbType = DateTimeOffset), @p9='?' (Size = 256), @p10='?' (Size = 256), @p11='?' (Size = 4000), @p12='?' (Size = 4000), @p13='?' (DbType = Boolean), @p14='?' (Size = 4000), @p15='?' (DbType = Boolean), @p16='?' (Size = 4000), @p17='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [AspNetUsers] SET [AccessFailedCount] = @p0, [ConcurrencyStamp] = @p1, [Email] = @p2, [EmailConfirmed] = @p3, [Enabled] = @p4, [FullName] = @p5, [Image] = @p6, [LockoutEnabled] = @p7, [LockoutEnd] = @p8, [NormalizedEmail] = @p9, [NormalizedUserName] = @p10, [PasswordHash] = @p11, [PhoneNumber] = @p12, [PhoneNumberConfirmed] = @p13, [SecurityStamp] = @p14, [TwoFactorEnabled] = @p15, [UserGroup] = @p16, [UserName] = @p17
OUTPUT 1
WHERE [Id] = @p18 AND [ConcurrencyStamp] IS NULL;
2025-06-23 10:34:22.187 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__user_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ClaimType], [a].[ClaimValue], [a].[UserId]
FROM [AspNetUserClaims] AS [a]
WHERE [a].[UserId] = @__user_Id_0
2025-06-23 10:34:22.195 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 10:34:22.220 +03:30 [INF] Executed DbCommand (7ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Department], [a].[Description], [a].[FromTimeActivity], [a].[Name], [a].[NormalizedName], [a].[Position], [a].[TableAccess], [a].[ToTimeActivity], [a].[ToolAccess]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-23 10:34:22.252 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__role_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0
2025-06-23 10:34:22.263 +03:30 [INF] AuthenticationScheme: Identity.Application signed in.
2025-06-23 10:34:33.630 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 10:34:33.641 +03:30 [INF] AuthenticationScheme: Cookies signed in.
2025-06-23 10:34:33.643 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.RedirectResult in 28329.0932ms.
2025-06-23 10:34:33.647 +03:30 [INF] Executing RedirectResult, redirecting to /User.
2025-06-23 10:34:33.649 +03:30 [INF] Executed action BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) in 28390.0717ms
2025-06-23 10:34:33.651 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 10:34:33.654 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Account/Login - 302 0 null 28456.547ms
2025-06-23 10:34:33.667 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/User - null null
2025-06-23 10:34:33.704 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.UserController.Index (BaseGIS.Web)'
2025-06-23 10:34:33.708 +03:30 [INF] Route matched with {action = "Index", controller = "User"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.UserController (BaseGIS.Web).
2025-06-23 10:34:33.733 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.UserController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:34:33.739 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.UserController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.189ms.
2025-06-23 10:34:33.742 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:34:33.861 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:34:33.872 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 10:34:33.886 +03:30 [INF] Executed ViewResult - view Index executed in 144.4041ms.
2025-06-23 10:34:33.890 +03:30 [INF] Executed action BaseGIS.Web.Controllers.UserController.Index (BaseGIS.Web) in 180.0063ms
2025-06-23 10:34:33.892 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.UserController.Index (BaseGIS.Web)'
2025-06-23 10:34:33.895 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/User - 200 null text/html; charset=utf-8 228.0868ms
2025-06-23 10:34:33.950 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:34:33.950 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:34:33.967 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 16.3623ms
2025-06-23 10:34:33.968 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 18.3701ms
2025-06-23 10:34:37.066 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:34:37.103 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:34:37.107 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:34:37.135 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:34:39.193 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:34:41.686 +03:30 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
ORDER BY [g].[Id], [t].[AliasName]
2025-06-23 10:34:42.412 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\Services\IBaseMapService.cs:line 79
2025-06-23 10:34:42.439 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 10:34:42.823 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 5684.7681ms.
2025-06-23 10:34:42.829 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:34:45.776 +03:30 [INF] Executed DbCommand (1,810ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 10:34:45.989 +03:30 [INF] Executed ViewResult - view Index executed in 3161.3298ms.
2025-06-23 10:34:45.992 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 8883.1925ms
2025-06-23 10:34:45.994 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:34:45.997 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 8931.5148ms
2025-06-23 10:34:46.076 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site_map.css?v=aoP519SFZBIvvQFvf5ec-er8O88aoHY0TCfrrJFfnUw - null null
2025-06-23 10:34:46.077 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css - null null
2025-06-23 10:34:46.076 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jqueryui/themes/base/jquery-ui.min.css - null null
2025-06-23 10:34:46.091 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - null null
2025-06-23 10:34:46.077 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - null null
2025-06-23 10:34:46.310 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/property-tools.js - null null
2025-06-23 10:34:46.310 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/goto-xy.js - null null
2025-06-23 10:34:46.329 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - 404 0 null 231.8406ms
2025-06-23 10:34:46.310 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jqueryui/jquery-ui.min.js - null null
2025-06-23 10:34:46.332 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - 404 0 null 255.6441ms
2025-06-23 10:34:46.091 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/ion-rangeslider/css/ion.rangeslider.min.css - null null
2025-06-23 10:34:46.155 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/components/measurement-tools.css - null null
2025-06-23 10:34:46.120 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/components.css - null null
2025-06-23 10:34:46.155 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/components/spatial-analysis.css - null null
2025-06-23 10:34:46.167 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/select2/css/select2.min.css - null null
2025-06-23 10:34:46.178 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/components/goto-xy.css - null null
2025-06-23 10:34:46.200 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/components/drawing-tools.css - null null
2025-06-23 10:34:46.200 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/components/property-tools.css - null null
2025-06-23 10:34:46.214 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/logo.png - null null
2025-06-23 10:34:46.225 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/modules/baseMapManager.js - null null
2025-06-23 10:34:46.235 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/base-component.js - null null
2025-06-23 10:34:46.246 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:34:46.284 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/measurement-tools.js - null null
2025-06-23 10:34:46.266 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/geomap-core.js - null null
2025-06-23 10:34:46.277 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/layer-tree.js - null null
2025-06-23 10:34:46.288 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/leaflet.control.custom.js - null null
2025-06-23 10:34:46.299 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/spatial-analysis.js - null null
2025-06-23 10:34:46.311 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/drawing-tools.js - null null
2025-06-23 10:34:46.322 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/search-tools.js - null null
2025-06-23 10:34:46.333 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:34:46.344 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:34:46.355 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:34:46.365 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/esri-leaflet/dist/esri-leaflet.js - null null
2025-06-23 10:34:46.367 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/jquery.fancytree-all.min.js - null null
2025-06-23 10:34:46.369 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:34:46.373 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css, Response status code: 404
2025-06-23 10:34:46.376 +03:30 [INF] The file /lib/jqueryui/jquery-ui.min.js was not modified
2025-06-23 10:34:46.379 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css, Response status code: 404
2025-06-23 10:34:46.382 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js - null null
2025-06-23 10:34:46.392 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/select2/js/select2.min.js - null null
2025-06-23 10:34:46.391 +03:30 [INF] Sending file. Request path: '/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jquery.fancytree\skin-win8\ui.fancytree.min.css'
2025-06-23 10:34:46.391 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/ion-rangeslider/js/ion.rangeslider.min.js - null null
2025-06-23 10:34:46.395 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/proj4/dist/proj4.js - null null
2025-06-23 10:34:46.404 +03:30 [INF] Sending file. Request path: '/lib/ion-rangeslider/css/ion.rangeslider.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\ion-rangeslider\css\ion.rangeSlider.min.css'
2025-06-23 10:34:46.404 +03:30 [INF] Sending file. Request path: '/css/components/measurement-tools.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\components\measurement-tools.css'
2025-06-23 10:34:46.407 +03:30 [INF] Sending file. Request path: '/css/components.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\components.css'
2025-06-23 10:34:46.421 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 175.3754ms
2025-06-23 10:34:46.437 +03:30 [INF] The file /js/leaflet.control.custom.js was not modified
2025-06-23 10:34:46.448 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 115.332ms
2025-06-23 10:34:46.450 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 106.4021ms
2025-06-23 10:34:46.455 +03:30 [INF] The file /lib/esri-leaflet/dist/esri-leaflet.js was not modified
2025-06-23 10:34:46.458 +03:30 [INF] The file /lib/jquery.fancytree/jquery.fancytree-all.min.js was not modified
2025-06-23 10:34:46.465 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jqueryui/jquery-ui.min.js - 304 null text/javascript 154.9532ms
2025-06-23 10:34:46.469 +03:30 [INF] The file /lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js was not modified
2025-06-23 10:34:46.471 +03:30 [INF] The file /lib/select2/js/select2.min.js was not modified
2025-06-23 10:34:46.473 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/skin-win8/ui.fancytree.min.css - 200 17671 text/css 395.7777ms
2025-06-23 10:34:46.475 +03:30 [INF] The file /lib/ion-rangeslider/js/ion.rangeslider.min.js was not modified
2025-06-23 10:34:46.479 +03:30 [INF] The file /lib/proj4/dist/proj4.js was not modified
2025-06-23 10:34:46.481 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/ion-rangeslider/css/ion.rangeslider.min.css - 200 11084 text/css 389.6221ms
2025-06-23 10:34:46.482 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/components/measurement-tools.css - 200 7659 text/css 327.3641ms
2025-06-23 10:34:46.484 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/components.css - 200 7998 text/css 365.2622ms
2025-06-23 10:34:46.487 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:34:46.488 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/leaflet.control.custom.js - 304 null text/javascript 199.7103ms
2025-06-23 10:34:46.491 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:34:46.496 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:34:46.497 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/esri-leaflet/dist/esri-leaflet.js - 304 null text/javascript 132.3214ms
2025-06-23 10:34:46.498 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jquery.fancytree/jquery.fancytree-all.min.js - 304 null text/javascript 131.4593ms
2025-06-23 10:34:46.502 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js - 304 null text/javascript 119.4559ms
2025-06-23 10:34:46.504 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/select2/js/select2.min.js - 304 null text/javascript 111.7145ms
2025-06-23 10:34:46.510 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/ion-rangeslider/js/ion.rangeslider.min.js - 304 null text/javascript 118.8861ms
2025-06-23 10:34:46.511 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/proj4/dist/proj4.js - 304 null text/javascript 115.7094ms
2025-06-23 10:34:46.528 +03:30 [INF] Sending file. Request path: '/lib/jqueryui/themes/base/jquery-ui.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jqueryui\themes\base\jquery-ui.min.css'
2025-06-23 10:34:46.531 +03:30 [INF] Sending file. Request path: '/css/site_map.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site_map.css'
2025-06-23 10:34:46.534 +03:30 [INF] Sending file. Request path: '/css/components/spatial-analysis.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\components\spatial-analysis.css'
2025-06-23 10:34:46.536 +03:30 [INF] Sending file. Request path: '/lib/select2/css/select2.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\select2\css\select2.min.css'
2025-06-23 10:34:46.540 +03:30 [INF] Sending file. Request path: '/css/components/goto-xy.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\components\goto-xy.css'
2025-06-23 10:34:46.543 +03:30 [INF] Sending file. Request path: '/css/components/drawing-tools.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\components\drawing-tools.css'
2025-06-23 10:34:46.548 +03:30 [INF] Sending file. Request path: '/js/geomap/goto-xy.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\geomap\goto-xy.js'
2025-06-23 10:34:46.552 +03:30 [INF] Sending file. Request path: '/css/components/property-tools.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\components\property-tools.css'
2025-06-23 10:34:46.554 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jqueryui/themes/base/jquery-ui.min.css - 200 30724 text/css 477.8928ms
2025-06-23 10:34:46.558 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site_map.css?v=aoP519SFZBIvvQFvf5ec-er8O88aoHY0TCfrrJFfnUw - 200 20799 text/css 482.4225ms
2025-06-23 10:34:46.561 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/components/spatial-analysis.css - 200 8407 text/css 405.2483ms
2025-06-23 10:34:46.563 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/select2/css/select2.min.css - 200 16264 text/css 396.3187ms
2025-06-23 10:34:46.567 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/components/goto-xy.css - 200 9272 text/css 389.6484ms
2025-06-23 10:34:46.569 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/components/drawing-tools.css - 200 11880 text/css 372.6019ms
2025-06-23 10:34:46.572 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/goto-xy.js - 200 28989 text/javascript 264.1333ms
2025-06-23 10:34:46.574 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/components/property-tools.css - 200 10176 text/css 373.8759ms
2025-06-23 10:34:46.578 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 208.9796ms
2025-06-23 10:34:46.582 +03:30 [INF] Sending file. Request path: '/js/modules/baseMapManager.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\modules\baseMapManager.js'
2025-06-23 10:34:46.585 +03:30 [INF] Sending file. Request path: '/js/components/base-component.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\components\base-component.js'
2025-06-23 10:34:46.588 +03:30 [INF] Sending file. Request path: '/img/logo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\logo.png'
2025-06-23 10:34:46.593 +03:30 [INF] Sending file. Request path: '/js/geomap/geomap-core.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\geomap\geomap-core.js'
2025-06-23 10:34:46.596 +03:30 [INF] Sending file. Request path: '/js/geomap/layer-tree.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\geomap\layer-tree.js'
2025-06-23 10:34:46.600 +03:30 [INF] Sending file. Request path: '/js/geomap/property-tools.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\geomap\property-tools.js'
2025-06-23 10:34:46.605 +03:30 [INF] Sending file. Request path: '/js/geomap/measurement-tools.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\geomap\measurement-tools.js'
2025-06-23 10:34:46.609 +03:30 [INF] Sending file. Request path: '/js/geomap/spatial-analysis.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\geomap\spatial-analysis.js'
2025-06-23 10:34:46.612 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/modules/baseMapManager.js - 200 11208 text/javascript 387.1793ms
2025-06-23 10:34:46.615 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/base-component.js - 200 10725 text/javascript 379.6764ms
2025-06-23 10:34:46.617 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/logo.png - 200 13521 image/png 403.3851ms
2025-06-23 10:34:46.619 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/geomap-core.js - 200 14843 text/javascript 352.979ms
2025-06-23 10:34:46.622 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/layer-tree.js - 200 12979 text/javascript 344.669ms
2025-06-23 10:34:46.624 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/property-tools.js - 200 37923 text/javascript 313.461ms
2025-06-23 10:34:46.626 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/measurement-tools.js - 200 30262 text/javascript 347.2207ms
2025-06-23 10:34:46.628 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/spatial-analysis.js - 200 32650 text/javascript 328.5023ms
2025-06-23 10:34:46.631 +03:30 [INF] Sending file. Request path: '/js/geomap/search-tools.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\geomap\search-tools.js'
2025-06-23 10:34:46.635 +03:30 [INF] Sending file. Request path: '/js/geomap/drawing-tools.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\geomap\drawing-tools.js'
2025-06-23 10:34:46.641 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 286.1822ms
2025-06-23 10:34:46.662 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/search-tools.js - 200 24244 text/javascript 340.5209ms
2025-06-23 10:34:46.664 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/drawing-tools.js - 200 47958 text/javascript 353.0974ms
2025-06-23 10:34:46.722 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:34:46.732 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 9.4355ms
2025-06-23 10:34:46.736 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:34:46.768 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:34:46.773 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 5.0829ms
2025-06-23 10:34:46.782 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:34:46.804 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:34:46.809 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 5.3776ms
2025-06-23 10:34:46.818 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:34:46.819 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-Bold-FD.woff - null null
2025-06-23 10:34:46.831 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-Bold-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-Bold-FD.woff'
2025-06-23 10:34:46.834 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-Bold-FD.woff - 200 47016 application/font-woff 15.0064ms
2025-06-23 10:34:46.864 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-23 10:34:46.866 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - null null
2025-06-23 10:34:46.869 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:34:46.873 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:34:46.882 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-23 10:34:46.883 +03:30 [INF] Route matched with {action = "GetBaseMaps", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.JsonResult] GetBaseMaps() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:34:46.942 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:34:47.011 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:34:47.015 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:34:47.235 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\Services\IBaseMapService.cs:line 79
2025-06-23 10:34:47.252 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 307.0827ms.
2025-06-23 10:34:47.258 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType14`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:34:47.262 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-23 10:34:47.266 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) in 365.0482ms
2025-06-23 10:34:47.268 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:34:47.270 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - 200 null application/json; charset=utf-8 404.4368ms
2025-06-23 10:34:47.274 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 261.1841ms.
2025-06-23 10:34:47.284 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 10:34:47.286 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType36`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType37`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType35`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:34:47.287 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 3.2421ms
2025-06-23 10:34:47.294 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 10:34:47.309 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 422.839ms
2025-06-23 10:34:47.311 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:34:47.313 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 448.73ms
2025-06-23 10:34:49.311 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - null null
2025-06-23 10:34:49.322 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 10:34:49.329 +03:30 [INF] Route matched with {action = "GetAllMapViews", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllMapViews() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:34:49.383 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:34:49.392 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:34:49.420 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__currentUser_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ID], [m].[Name], [m].[Description], [m].[GroupName], [m].[Date], [m].[Public], [a].[UserName], [m].[Zoom], [m].[BaseMapID]
FROM [MapViews] AS [m]
LEFT JOIN [AspNetUsers] AS [a] ON [m].[UserId] = [a].[Id]
WHERE [m].[Public] = CAST(1 AS bit) OR [a].[Id] = @__currentUser_Id_0
ORDER BY [m].[GroupName], [m].[Name]
2025-06-23 10:34:49.426 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 39.6721ms.
2025-06-23 10:34:49.430 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType23`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType33`9[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:34:49.440 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) in 109.4971ms
2025-06-23 10:34:49.444 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 10:34:49.447 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - 200 null application/json; charset=utf-8 136.3717ms
2025-06-23 10:34:58.696 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:34:58.697 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - null null
2025-06-23 10:34:58.701 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:34:58.802 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:34:58.847 +03:30 [INF] Sending file. Request path: '/lib/sweetalert2/sweetalert2.min.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\sweetalert2\sweetalert2.min.js'
2025-06-23 10:34:58.868 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:34:58.870 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/sweetalert2/sweetalert2.min.js - 499 46668 text/javascript 172.7994ms
2025-06-23 10:35:02.233 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.RedirectToActionResult in 3360.8368ms.
2025-06-23 10:35:02.233 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - null null
2025-06-23 10:35:02.233 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-23 10:35:02.244 +03:30 [INF] Executing RedirectResult, redirecting to /Account/Login.
2025-06-23 10:35:02.250 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.js.map'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.js.map'
2025-06-23 10:35:02.253 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-23 10:35:02.256 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 3406.3126ms
2025-06-23 10:35:02.258 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - 200 225544 text/plain 25.0473ms
2025-06-23 10:35:02.260 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 26.8887ms
2025-06-23 10:35:02.262 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:35:02.272 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 302 0 null 3575.9427ms
2025-06-23 10:35:02.278 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Account/Login - null null
2025-06-23 10:35:02.283 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 10:35:02.285 +03:30 [INF] Route matched with {action = "Login", controller = "Account"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Login(System.String) on controller BaseGIS.Web.Controllers.AccountController (BaseGIS.Web).
2025-06-23 10:35:02.308 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:35:02.311 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.021ms.
2025-06-23 10:35:02.314 +03:30 [INF] Executing ViewResult, running view Login.
2025-06-23 10:35:02.318 +03:30 [INF] Executed ViewResult - view Login executed in 3.9957ms.
2025-06-23 10:35:02.320 +03:30 [INF] Executed action BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) in 32.8304ms
2025-06-23 10:35:02.322 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 10:35:02.323 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Account/Login - 200 null text/html; charset=utf-8 45.0826ms
2025-06-23 10:35:18.092 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:35:18.161 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:35:18.165 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:35:18.204 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:35:18.209 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:35:18.428 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\Services\IBaseMapService.cs:line 79
2025-06-23 10:35:18.450 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 10:35:18.452 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 245.6502ms.
2025-06-23 10:35:18.455 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:35:18.460 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 10:35:18.470 +03:30 [INF] Executed ViewResult - view Index executed in 14.2296ms.
2025-06-23 10:35:18.472 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 304.9502ms
2025-06-23 10:35:18.475 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:35:18.476 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 384.5411ms
2025-06-23 10:35:18.646 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - null null
2025-06-23 10:35:18.648 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - null null
2025-06-23 10:35:18.652 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - 404 0 null 5.4713ms
2025-06-23 10:35:18.655 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - 404 0 null 7.1287ms
2025-06-23 10:35:18.660 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css, Response status code: 404
2025-06-23 10:35:18.667 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css, Response status code: 404
2025-06-23 10:35:18.696 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:35:18.699 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 3.1584ms
2025-06-23 10:35:18.703 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:35:18.716 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:35:18.717 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:35:18.719 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 3.5119ms
2025-06-23 10:35:18.720 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:35:18.723 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 5.457ms
2025-06-23 10:35:18.723 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:35:18.728 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:35:18.736 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:35:18.736 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 16.0736ms
2025-06-23 10:35:18.739 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 16.3364ms
2025-06-23 10:35:18.855 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:35:18.859 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 3.7674ms
2025-06-23 10:35:18.863 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:35:18.915 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:35:18.919 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 4.2359ms
2025-06-23 10:35:18.923 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:35:18.932 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:35:18.936 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 4.4242ms
2025-06-23 10:35:18.943 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:35:19.118 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-23 10:35:19.124 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:35:19.126 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-23 10:35:19.162 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:35:19.162 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - null null
2025-06-23 10:35:19.167 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-23 10:35:19.170 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:35:19.175 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 10.0339ms.
2025-06-23 10:35:19.177 +03:30 [INF] Route matched with {action = "GetBaseMaps", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.JsonResult] GetBaseMaps() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:35:19.180 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType36`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType37`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType35`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:35:19.208 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:35:19.211 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 82.3946ms
2025-06-23 10:35:19.215 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:35:19.216 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:35:19.217 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 99.3221ms
2025-06-23 10:35:19.400 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\Services\IBaseMapService.cs:line 79
2025-06-23 10:35:19.417 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 203.9997ms.
2025-06-23 10:35:19.420 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType14`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:35:19.422 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) in 238.7353ms
2025-06-23 10:35:19.424 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:35:19.426 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - 200 null application/json; charset=utf-8 263.4022ms
2025-06-23 10:35:19.458 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - null null
2025-06-23 10:35:19.462 +03:30 [INF] The file /lib/leaflet/leaflet.js.map was not modified
2025-06-23 10:35:19.463 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - 304 null text/plain 4.6896ms
2025-06-23 10:35:19.503 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 10:35:19.508 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 4.2497ms
2025-06-23 10:35:19.511 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 10:35:38.180 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - null null
2025-06-23 10:35:38.185 +03:30 [INF] The file /lib/leaflet/leaflet.js.map was not modified
2025-06-23 10:35:38.186 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - 304 null text/plain 5.9211ms
2025-06-23 10:35:38.243 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js.map - null null
2025-06-23 10:35:38.244 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/esri-leaflet/dist/esri-leaflet.js.map - null null
2025-06-23 10:35:38.244 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js.map - null null
2025-06-23 10:35:38.250 +03:30 [INF] The file /lib/bootstrap/js/bootstrap.bundle.min.js.map was not modified
2025-06-23 10:35:38.254 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/esri-leaflet/dist/esri-leaflet.js.map - 404 0 null 10.1126ms
2025-06-23 10:35:38.258 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js.map - 404 0 null 14.2854ms
2025-06-23 10:35:38.259 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js.map - 304 null text/plain 15.2018ms
2025-06-23 10:35:38.262 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/esri-leaflet/dist/esri-leaflet.js.map, Response status code: 404
2025-06-23 10:35:38.268 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js.map, Response status code: 404
2025-06-23 10:36:24.190 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-23 10:36:24.195 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-23 10:36:24.196 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 5.9084ms
2025-06-23 10:37:37.693 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-23 10:37:37.697 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-23 10:37:37.698 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 5.1712ms
2025-06-23 10:38:36.757 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:38:36.816 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:38:36.821 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:38:36.865 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:38:36.869 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:38:37.051 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 10:38:37.068 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 10:38:37.069 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 201.3399ms.
2025-06-23 10:38:37.073 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:38:38.116 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 10:38:38.351 +03:30 [INF] Executed ViewResult - view Index executed in 1279.8344ms.
2025-06-23 10:38:38.354 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 1528.7481ms
2025-06-23 10:38:38.355 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:38:38.356 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 1598.9495ms
2025-06-23 10:38:40.855 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:38:40.891 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:38:40.893 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:38:40.923 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:38:40.928 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:38:41.069 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 10:38:41.084 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 10:38:41.085 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 158.5756ms.
2025-06-23 10:38:41.087 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:38:41.089 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 10:38:41.098 +03:30 [INF] Executed ViewResult - view Index executed in 11.6617ms.
2025-06-23 10:38:41.101 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 205.6887ms
2025-06-23 10:38:41.103 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:38:41.109 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 253.6384ms
2025-06-23 10:38:41.190 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - null null
2025-06-23 10:38:41.192 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - null null
2025-06-23 10:38:41.195 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - 404 0 null 4.623ms
2025-06-23 10:38:41.198 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - 404 0 null 6.3836ms
2025-06-23 10:38:41.202 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css, Response status code: 404
2025-06-23 10:38:41.207 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css, Response status code: 404
2025-06-23 10:38:41.220 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:38:41.225 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 5.3126ms
2025-06-23 10:38:41.229 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:38:41.247 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:38:41.250 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 3.3566ms
2025-06-23 10:38:41.252 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:38:41.256 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:38:41.274 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 21.8558ms
2025-06-23 10:38:41.254 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:38:41.254 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:38:41.319 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:38:41.314 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 58.1137ms
2025-06-23 10:38:41.329 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 75.0037ms
2025-06-23 10:38:41.426 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:38:41.430 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 3.8519ms
2025-06-23 10:38:41.434 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:38:41.446 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:38:41.449 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 3.409ms
2025-06-23 10:38:41.453 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:38:41.457 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:38:41.461 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 4.8611ms
2025-06-23 10:38:41.465 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:38:41.803 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-23 10:38:41.804 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - null null
2025-06-23 10:38:41.808 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:38:41.811 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:38:41.817 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-23 10:38:41.817 +03:30 [INF] Route matched with {action = "GetBaseMaps", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.JsonResult] GetBaseMaps() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:38:41.861 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:38:41.891 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:38:41.907 +03:30 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-23 10:38:41.912 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 18.4839ms.
2025-06-23 10:38:41.918 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:38:41.918 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType36`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType37`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType35`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:38:42.165 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 10:38:42.170 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 350.0956ms
2025-06-23 10:38:42.182 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 285.6112ms.
2025-06-23 10:38:42.183 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:38:42.186 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType14`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:38:42.187 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 384.2376ms
2025-06-23 10:38:42.190 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) in 327.5007ms
2025-06-23 10:38:42.195 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:38:42.196 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - 200 null application/json; charset=utf-8 392.4971ms
2025-06-23 10:38:42.386 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-23 10:38:42.391 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-23 10:38:42.392 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 6.1536ms
2025-06-23 10:38:42.423 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 10:38:42.429 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 5.9801ms
2025-06-23 10:38:42.434 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 10:39:18.431 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:39:18.473 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:39:18.477 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:39:18.515 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:39:18.520 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:39:18.660 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 10:39:18.678 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 10:39:18.679 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 160.2016ms.
2025-06-23 10:39:18.683 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:39:19.825 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 10:39:20.073 +03:30 [INF] Executed ViewResult - view Index executed in 1391.8473ms.
2025-06-23 10:39:20.075 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 1595.0745ms
2025-06-23 10:39:20.076 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:39:20.078 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 1647.7224ms
2025-06-23 10:39:20.161 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - null null
2025-06-23 10:39:20.163 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - null null
2025-06-23 10:39:20.167 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - 404 0 null 6.2209ms
2025-06-23 10:39:20.168 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - 404 0 null 5.2963ms
2025-06-23 10:39:20.171 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css, Response status code: 404
2025-06-23 10:39:20.175 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css, Response status code: 404
2025-06-23 10:39:20.199 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:39:20.202 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 3.1155ms
2025-06-23 10:39:20.206 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:39:20.221 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:39:20.223 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:39:20.225 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 3.4914ms
2025-06-23 10:39:20.228 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 5.6074ms
2025-06-23 10:39:20.227 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:39:20.241 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:39:20.244 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:39:20.248 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:39:20.255 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 13.628ms
2025-06-23 10:39:20.270 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 43.7468ms
2025-06-23 10:39:20.357 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:39:20.360 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 3.1814ms
2025-06-23 10:39:20.364 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:39:20.409 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:39:20.413 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 4.1153ms
2025-06-23 10:39:20.419 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:39:20.450 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:39:20.455 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 4.3767ms
2025-06-23 10:39:20.459 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:39:20.797 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-23 10:39:20.803 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-23 10:39:20.805 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 8.1996ms
2025-06-23 10:39:21.603 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-23 10:39:21.608 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:39:21.614 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-23 10:39:21.651 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:39:21.651 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - null null
2025-06-23 10:39:21.655 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-23 10:39:21.657 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:39:21.660 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 6.2873ms.
2025-06-23 10:39:21.664 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType36`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType37`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType35`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:39:21.667 +03:30 [INF] Route matched with {action = "GetBaseMaps", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.JsonResult] GetBaseMaps() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:39:21.695 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:39:21.697 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 79.6945ms
2025-06-23 10:39:21.699 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:39:21.699 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:39:21.700 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 97.4708ms
2025-06-23 10:39:21.925 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 10:39:21.938 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 241.144ms.
2025-06-23 10:39:21.940 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType14`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:39:21.942 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) in 270.4844ms
2025-06-23 10:39:21.944 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:39:21.945 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - 200 null application/json; charset=utf-8 294.3068ms
2025-06-23 10:39:21.957 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 10:39:21.961 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 4.225ms
2025-06-23 10:39:21.965 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 10:40:07.073 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:40:07.128 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:40:07.133 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:40:07.173 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:40:07.210 +03:30 [INF] Executed DbCommand (10ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:40:07.370 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 10:40:07.383 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 10:40:07.384 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 185.175ms.
2025-06-23 10:40:07.388 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:40:08.455 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 10:40:08.713 +03:30 [INF] Executed ViewResult - view Index executed in 1326.9034ms.
2025-06-23 10:40:08.716 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 1580.4635ms
2025-06-23 10:40:08.718 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:40:08.720 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 1647.2254ms
2025-06-23 10:40:08.736 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - null null
2025-06-23 10:40:08.740 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - null null
2025-06-23 10:40:08.746 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - 404 0 null 9.8177ms
2025-06-23 10:40:08.747 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - 404 0 null 7.3323ms
2025-06-23 10:40:08.753 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css, Response status code: 404
2025-06-23 10:40:08.759 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css, Response status code: 404
2025-06-23 10:40:08.817 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:40:08.822 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 4.8569ms
2025-06-23 10:40:08.829 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:40:08.829 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:40:08.868 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:40:08.832 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:40:08.830 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:40:08.845 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 16.4166ms
2025-06-23 10:40:08.884 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:40:08.885 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 55.7811ms
2025-06-23 10:40:08.907 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:40:08.872 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 3.4855ms
2025-06-23 10:40:08.898 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 66.1875ms
2025-06-23 10:40:08.900 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:40:08.903 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 19.2635ms
2025-06-23 10:40:08.843 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:40:08.910 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:40:08.913 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:40:08.923 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:40:08.927 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 83.3382ms
2025-06-23 10:40:08.930 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 19.736ms
2025-06-23 10:40:08.942 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:40:09.000 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-23 10:40:09.003 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - null null
2025-06-23 10:40:09.008 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:40:09.011 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:40:09.019 +03:30 [INF] Route matched with {action = "GetBaseMaps", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.JsonResult] GetBaseMaps() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:40:09.020 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-23 10:40:09.049 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:40:09.081 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:40:09.085 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:40:09.086 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-23 10:40:09.092 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 7.6624ms.
2025-06-23 10:40:09.176 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType36`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType37`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType35`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:40:09.329 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 10:40:09.346 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 294.1785ms
2025-06-23 10:40:09.357 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 274.6382ms.
2025-06-23 10:40:09.358 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:40:09.362 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType14`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:40:09.364 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 364.4726ms
2025-06-23 10:40:09.367 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) in 345.706ms
2025-06-23 10:40:09.372 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:40:09.374 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - 200 null application/json; charset=utf-8 371.1407ms
2025-06-23 10:40:09.385 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 10:40:09.389 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 4.0096ms
2025-06-23 10:40:09.393 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 10:40:43.452 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:40:43.488 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:40:43.492 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:40:43.524 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:40:43.528 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:40:43.663 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 10:40:43.678 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 10:40:43.679 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 153.6143ms.
2025-06-23 10:40:43.681 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:40:44.804 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 10:40:45.045 +03:30 [INF] Executed ViewResult - view Index executed in 1364.604ms.
2025-06-23 10:40:45.047 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 1553.5631ms
2025-06-23 10:40:45.050 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:40:45.051 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 1599.5529ms
2025-06-23 10:40:45.113 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - null null
2025-06-23 10:40:45.114 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - null null
2025-06-23 10:40:45.122 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - 404 0 null 9.1658ms
2025-06-23 10:40:45.123 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - 404 0 null 9.107ms
2025-06-23 10:40:45.128 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css, Response status code: 404
2025-06-23 10:40:45.134 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css, Response status code: 404
2025-06-23 10:40:45.135 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:40:45.144 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 8.0758ms
2025-06-23 10:40:45.148 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:40:45.149 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:40:45.184 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 36.0464ms
2025-06-23 10:40:45.146 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:40:45.149 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:40:45.166 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:40:45.204 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:40:45.207 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 61.2767ms
2025-06-23 10:40:45.215 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 65.7649ms
2025-06-23 10:40:45.216 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 49.6172ms
2025-06-23 10:40:45.222 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:40:45.224 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:40:45.237 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 14.7505ms
2025-06-23 10:40:45.245 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:40:45.250 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:40:45.254 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 4.6611ms
2025-06-23 10:40:45.260 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:40:45.260 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:40:45.264 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 4.0791ms
2025-06-23 10:40:45.268 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:40:45.336 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-23 10:40:45.337 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - null null
2025-06-23 10:40:45.341 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:40:45.344 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:40:45.351 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-23 10:40:45.354 +03:30 [INF] Route matched with {action = "GetBaseMaps", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.JsonResult] GetBaseMaps() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:40:45.382 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:40:45.570 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:40:45.577 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-23 10:40:45.577 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:40:45.581 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 7.8694ms.
2025-06-23 10:40:45.588 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType36`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType37`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType35`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:40:45.750 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 10:40:45.756 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 401.4108ms
2025-06-23 10:40:45.767 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 192.9237ms.
2025-06-23 10:40:45.768 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:40:45.770 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType14`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:40:45.772 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 436.0965ms
2025-06-23 10:40:45.774 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) in 390.3214ms
2025-06-23 10:40:45.779 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:40:45.780 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - 200 null application/json; charset=utf-8 443.253ms
2025-06-23 10:40:45.790 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 10:40:45.793 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 3.4754ms
2025-06-23 10:40:45.798 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 10:41:12.183 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - null null
2025-06-23 10:41:12.192 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 10:41:12.197 +03:30 [INF] Route matched with {action = "GetAllMapViews", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllMapViews() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:41:12.219 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:41:12.223 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:41:12.232 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__currentUser_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ID], [m].[Name], [m].[Description], [m].[GroupName], [m].[Date], [m].[Public], [a].[UserName], [m].[Zoom], [m].[BaseMapID]
FROM [MapViews] AS [m]
LEFT JOIN [AspNetUsers] AS [a] ON [m].[UserId] = [a].[Id]
WHERE [m].[Public] = CAST(1 AS bit) OR [a].[Id] = @__currentUser_Id_0
ORDER BY [m].[GroupName], [m].[Name]
2025-06-23 10:41:12.237 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 14.9752ms.
2025-06-23 10:41:12.238 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType23`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType33`9[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:41:12.242 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) in 39.4892ms
2025-06-23 10:41:12.244 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 10:41:12.245 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - 200 null application/json; charset=utf-8 61.3421ms
2025-06-23 10:42:16.330 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:42:16.376 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:42:16.382 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:42:16.418 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:42:16.421 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:42:16.653 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 10:42:16.669 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 10:42:16.670 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 249.8653ms.
2025-06-23 10:42:16.675 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:42:17.794 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 10:42:18.148 +03:30 [INF] Executed ViewResult - view Index executed in 1475.8467ms.
2025-06-23 10:42:18.150 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 1765.4914ms
2025-06-23 10:42:18.152 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:42:18.154 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 1824.1632ms
2025-06-23 10:42:18.215 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - null null
2025-06-23 10:42:18.215 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - null null
2025-06-23 10:42:18.221 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - 404 0 null 6.4955ms
2025-06-23 10:42:18.223 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - 404 0 null 7.7748ms
2025-06-23 10:42:18.227 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css, Response status code: 404
2025-06-23 10:42:18.231 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css, Response status code: 404
2025-06-23 10:42:18.241 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:42:18.245 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 3.8061ms
2025-06-23 10:42:18.249 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:42:18.252 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:42:18.254 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:42:18.255 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:42:18.258 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 5.121ms
2025-06-23 10:42:18.286 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:42:18.278 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 23.6086ms
2025-06-23 10:42:18.256 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:42:18.300 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 45.2064ms
2025-06-23 10:42:18.306 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:42:18.309 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 52.9453ms
2025-06-23 10:42:18.324 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:42:18.328 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 3.2957ms
2025-06-23 10:42:18.331 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:42:18.340 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:42:18.343 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 3.189ms
2025-06-23 10:42:18.347 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:42:18.348 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:42:18.353 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 4.3872ms
2025-06-23 10:42:18.356 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:42:18.398 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-23 10:42:18.399 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - null null
2025-06-23 10:42:18.401 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:42:18.405 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:42:18.410 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-23 10:42:18.411 +03:30 [INF] Route matched with {action = "GetBaseMaps", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.JsonResult] GetBaseMaps() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:42:18.453 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:42:18.486 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:42:18.490 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-23 10:42:18.492 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:42:18.493 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 5.6819ms.
2025-06-23 10:42:18.502 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType36`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType37`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType35`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:42:18.767 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 10:42:18.774 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 361.6648ms
2025-06-23 10:42:18.783 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 292.9552ms.
2025-06-23 10:42:18.784 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:42:18.786 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType14`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:42:18.788 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 389.71ms
2025-06-23 10:42:18.790 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) in 334.8958ms
2025-06-23 10:42:18.795 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:42:18.797 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - 200 null application/json; charset=utf-8 397.5941ms
2025-06-23 10:42:18.807 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 10:42:18.812 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 5.3912ms
2025-06-23 10:42:18.817 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 10:42:26.291 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - null null
2025-06-23 10:42:26.302 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 10:42:26.308 +03:30 [INF] Route matched with {action = "GetAllMapViews", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllMapViews() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:42:26.336 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:42:26.341 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:42:26.348 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__currentUser_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ID], [m].[Name], [m].[Description], [m].[GroupName], [m].[Date], [m].[Public], [a].[UserName], [m].[Zoom], [m].[BaseMapID]
FROM [MapViews] AS [m]
LEFT JOIN [AspNetUsers] AS [a] ON [m].[UserId] = [a].[Id]
WHERE [m].[Public] = CAST(1 AS bit) OR [a].[Id] = @__currentUser_Id_0
ORDER BY [m].[GroupName], [m].[Name]
2025-06-23 10:42:26.352 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 13.1936ms.
2025-06-23 10:42:26.356 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType23`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType33`9[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:42:26.361 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) in 49.5595ms
2025-06-23 10:42:26.363 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 10:42:26.364 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - 200 null application/json; charset=utf-8 72.7675ms
2025-06-23 10:43:59.004 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 10:43:59.013 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 9.0215ms
2025-06-23 10:43:59.037 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 10:44:25.313 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-23 10:44:25.368 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-23 10:44:25.372 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-23 10:44:25.405 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:44:25.408 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0162ms.
2025-06-23 10:44:25.411 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:44:25.416 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:44:25.424 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 10:44:25.458 +03:30 [INF] Executed ViewResult - view Index executed in 47.0388ms.
2025-06-23 10:44:25.462 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 84.317ms
2025-06-23 10:44:25.463 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-23 10:44:25.464 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 151.574ms
2025-06-23 10:44:25.518 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:44:25.523 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:44:25.523 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 5.022ms
2025-06-23 10:44:25.547 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 23.8148ms
2025-06-23 10:44:34.289 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:44:34.323 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:44:34.326 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:44:34.350 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:44:34.356 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:44:34.487 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 10:44:34.503 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 10:44:34.504 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 149.5751ms.
2025-06-23 10:44:34.505 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:44:34.507 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 10:44:34.515 +03:30 [INF] Executed ViewResult - view Index executed in 9.8341ms.
2025-06-23 10:44:34.519 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 190.8384ms
2025-06-23 10:44:34.521 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:44:34.523 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 234.1609ms
2025-06-23 10:44:34.592 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - null null
2025-06-23 10:44:34.595 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - null null
2025-06-23 10:44:34.597 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - 404 0 null 5.0331ms
2025-06-23 10:44:34.601 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - 404 0 null 6.5335ms
2025-06-23 10:44:34.606 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css, Response status code: 404
2025-06-23 10:44:34.612 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css, Response status code: 404
2025-06-23 10:44:34.621 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:44:34.625 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 4.326ms
2025-06-23 10:44:34.629 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:44:34.631 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:44:34.633 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:44:34.652 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 23.2033ms
2025-06-23 10:44:34.630 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:44:34.631 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:44:34.672 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 39.4818ms
2025-06-23 10:44:34.686 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 55.253ms
2025-06-23 10:44:34.689 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:44:34.692 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 62.4377ms
2025-06-23 10:44:34.700 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:44:34.708 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:44:34.711 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 11.4786ms
2025-06-23 10:44:34.717 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:44:34.722 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:44:34.725 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 3.1624ms
2025-06-23 10:44:34.728 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:44:34.728 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:44:34.733 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 4.1318ms
2025-06-23 10:44:34.736 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:44:34.786 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-23 10:44:34.789 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - null null
2025-06-23 10:44:34.793 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:44:34.795 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:44:34.796 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-23 10:44:34.798 +03:30 [INF] Route matched with {action = "GetBaseMaps", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.JsonResult] GetBaseMaps() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:44:34.829 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:44:34.854 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:44:34.858 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-23 10:44:34.860 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:44:34.862 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 5.782ms.
2025-06-23 10:44:34.868 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType36`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType37`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType35`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:44:35.070 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 10:44:35.071 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 266.2772ms
2025-06-23 10:44:35.087 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 228.288ms.
2025-06-23 10:44:35.088 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:44:35.091 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType14`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:44:35.092 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 306.4682ms
2025-06-23 10:44:35.094 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) in 262.29ms
2025-06-23 10:44:35.101 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:44:35.102 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - 200 null application/json; charset=utf-8 313.5983ms
2025-06-23 10:44:35.114 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 10:44:35.117 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 3.7497ms
2025-06-23 10:44:35.121 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 10:44:50.319 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - null null
2025-06-23 10:44:50.323 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 10:44:50.324 +03:30 [INF] Route matched with {action = "GetAllMapViews", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllMapViews() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:44:50.343 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:44:50.348 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:44:50.356 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__currentUser_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ID], [m].[Name], [m].[Description], [m].[GroupName], [m].[Date], [m].[Public], [a].[UserName], [m].[Zoom], [m].[BaseMapID]
FROM [MapViews] AS [m]
LEFT JOIN [AspNetUsers] AS [a] ON [m].[UserId] = [a].[Id]
WHERE [m].[Public] = CAST(1 AS bit) OR [a].[Id] = @__currentUser_Id_0
ORDER BY [m].[GroupName], [m].[Name]
2025-06-23 10:44:50.358 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 12.0205ms.
2025-06-23 10:44:50.360 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType23`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType33`9[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:44:50.363 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) in 37.3102ms
2025-06-23 10:44:50.366 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 10:44:50.368 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - 200 null application/json; charset=utf-8 49.7979ms
2025-06-23 10:45:02.225 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - null null
2025-06-23 10:45:02.231 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 10:45:02.231 +03:30 [INF] Route matched with {action = "GetAllMapViews", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllMapViews() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:45:02.252 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:45:02.257 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:45:02.265 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__currentUser_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ID], [m].[Name], [m].[Description], [m].[GroupName], [m].[Date], [m].[Public], [a].[UserName], [m].[Zoom], [m].[BaseMapID]
FROM [MapViews] AS [m]
LEFT JOIN [AspNetUsers] AS [a] ON [m].[UserId] = [a].[Id]
WHERE [m].[Public] = CAST(1 AS bit) OR [a].[Id] = @__currentUser_Id_0
ORDER BY [m].[GroupName], [m].[Name]
2025-06-23 10:45:02.271 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 15.1299ms.
2025-06-23 10:45:02.275 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType23`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType33`9[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:45:02.278 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) in 44.1703ms
2025-06-23 10:45:02.281 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 10:45:02.283 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - 200 null application/json; charset=utf-8 57.7967ms
2025-06-23 10:45:09.063 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - null null
2025-06-23 10:45:09.068 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 10:45:09.069 +03:30 [INF] Route matched with {action = "GetAllMapViews", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllMapViews() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:45:09.086 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:45:09.092 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:45:09.099 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__currentUser_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ID], [m].[Name], [m].[Description], [m].[GroupName], [m].[Date], [m].[Public], [a].[UserName], [m].[Zoom], [m].[BaseMapID]
FROM [MapViews] AS [m]
LEFT JOIN [AspNetUsers] AS [a] ON [m].[UserId] = [a].[Id]
WHERE [m].[Public] = CAST(1 AS bit) OR [a].[Id] = @__currentUser_Id_0
ORDER BY [m].[GroupName], [m].[Name]
2025-06-23 10:45:09.104 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 12.7314ms.
2025-06-23 10:45:09.106 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType23`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType33`9[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:45:09.108 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) in 37.6674ms
2025-06-23 10:45:09.111 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 10:45:09.113 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - 200 null application/json; charset=utf-8 49.6304ms
2025-06-23 10:46:33.846 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 10:46:33.891 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:46:33.899 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:46:33.931 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:46:33.935 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:46:34.073 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 10:46:34.088 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 10:46:34.091 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 156.9947ms.
2025-06-23 10:46:34.095 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 10:46:35.219 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 10:46:35.490 +03:30 [INF] Executed ViewResult - view Index executed in 1396.8027ms.
2025-06-23 10:46:35.492 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 1589.7007ms
2025-06-23 10:46:35.495 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 10:46:35.496 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 1649.7184ms
2025-06-23 10:46:35.552 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - null null
2025-06-23 10:46:35.553 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - null null
2025-06-23 10:46:35.560 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - 404 0 null 7.0413ms
2025-06-23 10:46:35.561 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - 404 0 null 8.536ms
2025-06-23 10:46:35.572 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css, Response status code: 404
2025-06-23 10:46:35.566 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css, Response status code: 404
2025-06-23 10:46:35.583 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:46:35.588 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 5.5082ms
2025-06-23 10:46:35.595 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:46:35.595 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:46:35.598 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 10:46:35.614 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 10:46:35.619 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 23.3578ms
2025-06-23 10:46:35.637 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 23.2044ms
2025-06-23 10:46:35.597 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:46:35.651 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 53.1099ms
2025-06-23 10:46:35.653 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:46:35.660 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 10:46:35.661 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 64.08ms
2025-06-23 10:46:35.669 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 8.8625ms
2025-06-23 10:46:35.671 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:46:35.674 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 10:46:35.677 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 10:46:35.682 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 5.2301ms
2025-06-23 10:46:35.686 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 10:46:35.687 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 10:46:35.691 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 3.8281ms
2025-06-23 10:46:35.694 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 10:46:35.756 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-23 10:46:35.758 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - null null
2025-06-23 10:46:35.759 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:46:35.764 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:46:35.770 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-23 10:46:35.772 +03:30 [INF] Route matched with {action = "GetBaseMaps", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.JsonResult] GetBaseMaps() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:46:35.799 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:46:35.823 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:46:35.826 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-23 10:46:35.832 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:46:35.835 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 11.0168ms.
2025-06-23 10:46:35.841 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType36`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType37`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType35`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:46:36.034 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 10:46:36.040 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 266.7097ms
2025-06-23 10:46:36.051 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 223.5042ms.
2025-06-23 10:46:36.053 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 10:46:36.055 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType14`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:46:36.056 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 300.5222ms
2025-06-23 10:46:36.059 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) in 258.1734ms
2025-06-23 10:46:36.066 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 10:46:36.068 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - 200 null application/json; charset=utf-8 310.2168ms
2025-06-23 10:46:36.078 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 10:46:36.082 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 3.5944ms
2025-06-23 10:46:36.086 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 10:46:37.670 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - null null
2025-06-23 10:46:37.679 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 10:46:37.684 +03:30 [INF] Route matched with {action = "GetAllMapViews", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllMapViews() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 10:46:37.707 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 10:46:37.711 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 10:46:37.719 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__currentUser_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ID], [m].[Name], [m].[Description], [m].[GroupName], [m].[Date], [m].[Public], [a].[UserName], [m].[Zoom], [m].[BaseMapID]
FROM [MapViews] AS [m]
LEFT JOIN [AspNetUsers] AS [a] ON [m].[UserId] = [a].[Id]
WHERE [m].[Public] = CAST(1 AS bit) OR [a].[Id] = @__currentUser_Id_0
ORDER BY [m].[GroupName], [m].[Name]
2025-06-23 10:46:37.723 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 13.8293ms.
2025-06-23 10:46:37.725 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType23`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType33`9[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:46:37.729 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) in 41.7681ms
2025-06-23 10:46:37.732 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 10:46:37.734 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - 200 null application/json; charset=utf-8 64.3343ms
2025-06-23 14:16:43.747 +03:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-23 14:16:47.296 +03:30 [INF] Executed DbCommand (33ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-23 14:16:47.369 +03:30 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']

IF EXISTS
    (SELECT *
     FROM [sys].[objects] o
     WHERE [o].[type] = 'U'
     AND [o].[is_ms_shipped] = 0
     AND NOT EXISTS (SELECT *
         FROM [sys].[extended_properties] AS [ep]
         WHERE [ep].[major_id] = [o].[object_id]
             AND [ep].[minor_id] = 0
             AND [ep].[class] = 1
             AND [ep].[name] = N'microsoft_database_tools_support'
    )
)
SELECT 1 ELSE SELECT 0
2025-06-23 14:16:47.547 +03:30 [ERR] An error occurred while seeding the database
System.InvalidOperationException: No service for type 'Microsoft.AspNetCore.Identity.RoleManager`1[Microsoft.AspNetCore.Identity.IdentityRole]' has been registered.
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at BaseGIS.Infrastructure.Persistence.SeedData.Initialize(IServiceProvider serviceProvider, ILogger logger) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Infrastructure\Persistence\SeedData.cs:line 17
2025-06-23 14:16:47.662 +03:30 [INF] Database seeding completed
2025-06-23 14:16:49.857 +03:30 [INF] Now listening on: https://localhost:7172
2025-06-23 14:16:49.858 +03:30 [INF] Now listening on: http://localhost:5200
2025-06-23 14:16:49.942 +03:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 14:16:49.944 +03:30 [INF] Hosting environment: Development
2025-06-23 14:16:49.946 +03:30 [INF] Content root path: D:\Backups\BaseProject\BaseGIS\BaseGIS.Web
2025-06-23 14:16:49.951 +03:30 [INF] The application has started
2025-06-23 14:16:52.148 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/ - null null
2025-06-23 14:16:52.717 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-23 14:16:52.746 +03:30 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.HomeController (BaseGIS.Web).
2025-06-23 14:16:52.944 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:16:52.950 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.8182ms.
2025-06-23 14:16:52.981 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 14:16:53.221 +03:30 [INF] Executed ViewResult - view Index executed in 260.3905ms.
2025-06-23 14:16:53.231 +03:30 [INF] Executed action BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web) in 481.3285ms
2025-06-23 14:16:53.233 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.HomeController.Index (BaseGIS.Web)'
2025-06-23 14:16:53.240 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/ - 200 null text/html; charset=utf-8 1105.8576ms
2025-06-23 14:16:53.356 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - null null
2025-06-23 14:16:53.357 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - null null
2025-06-23 14:16:53.356 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - null null
2025-06-23 14:16:53.356 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - null null
2025-06-23 14:16:53.356 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - null null
2025-06-23 14:16:53.433 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/avatars/male.png - null null
2025-06-23 14:16:53.444 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - null null
2025-06-23 14:16:53.479 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 14:16:53.478 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 14:16:54.347 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - null null
2025-06-23 14:16:54.457 +03:30 [INF] Sending file. Request path: '/assets/img/user8-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user8-128x128.jpg'
2025-06-23 14:16:54.457 +03:30 [INF] Sending file. Request path: '/assets/img/user3-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user3-128x128.jpg'
2025-06-23 14:16:54.457 +03:30 [INF] Sending file. Request path: '/assets/img/user1-128x128.jpg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\user1-128x128.jpg'
2025-06-23 14:16:54.457 +03:30 [INF] Sending file. Request path: '/css/site.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site.css'
2025-06-23 14:16:54.457 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/bootstrap-icons.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\bootstrap-icons.min.css'
2025-06-23 14:16:54.457 +03:30 [INF] Sending file. Request path: '/img/avatars/male.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\avatars\male.png'
2025-06-23 14:16:54.457 +03:30 [INF] Sending file. Request path: '/assets/img/AdminLTELogo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\assets\img\AdminLTELogo.png'
2025-06-23 14:16:54.588 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-FD.woff'
2025-06-23 14:16:54.588 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user8-128x128.jpg - 499 4983 image/jpeg 1218.1761ms
2025-06-23 14:16:54.590 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user3-128x128.jpg - 499 3398 image/jpeg 1234.1314ms
2025-06-23 14:16:54.592 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 1113.7047ms
2025-06-23 14:16:54.593 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/user1-128x128.jpg - 499 2750 image/jpeg 1237.2795ms
2025-06-23 14:16:54.596 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site.css?v=tkr1cVATUbyhtFtCCIX8wTgQyfFlbIfhWvNk_X67rRU - 499 7707 text/css 1240.3889ms
2025-06-23 14:16:54.599 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/bootstrap-icons.min.css - 499 85875 text/css 1243.6238ms
2025-06-23 14:16:54.601 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/avatars/male.png - 499 268 image/png 1208.3385ms
2025-06-23 14:16:54.604 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/assets/img/AdminLTELogo.png - 499 2637 image/png 1166.3761ms
2025-06-23 14:16:54.606 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-FD.woff - 499 45276 application/font-woff 259.8255ms
2025-06-23 14:16:54.611 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - null null
2025-06-23 14:16:54.613 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - null null
2025-06-23 14:16:54.763 +03:30 [INF] Sending file. Request path: '/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\bootstrap-icons\font\fonts\bootstrap-icons.woff2'
2025-06-23 14:16:54.894 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/favicon.ico - null null
2025-06-23 14:16:54.985 +03:30 [INF] Sending file. Request path: '/lib/font-awesome/webfonts/fa-solid-900.woff2'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\font-awesome\webfonts\fa-solid-900.woff2'
2025-06-23 14:16:54.986 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap-icons/font/fonts/bootstrap-icons.woff2?dd67030699838ea613ee6dbda90effa6 - ********** font/woff2 375.6022ms
2025-06-23 14:16:55.110 +03:30 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\favicon.ico'
2025-06-23 14:16:55.111 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/font-awesome/webfonts/fa-solid-900.woff2 - 499 158220 font/woff2 497.7231ms
2025-06-23 14:16:55.118 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/favicon.ico - 499 5430 image/x-icon 283.0296ms
2025-06-23 14:16:55.124 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 1645.1307ms
2025-06-23 14:17:24.678 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 14:17:24.739 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 14:17:24.851 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:17:24.909 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:17:24.985 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.RedirectToActionResult in 73.6548ms.
2025-06-23 14:17:24.990 +03:30 [INF] Executing RedirectResult, redirecting to /Account/Login.
2025-06-23 14:17:24.991 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 128.9014ms
2025-06-23 14:17:24.992 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 14:17:24.994 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 302 0 null 316.0777ms
2025-06-23 14:17:25.000 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Account/Login - null null
2025-06-23 14:17:25.037 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 14:17:25.047 +03:30 [INF] Route matched with {action = "Login", controller = "Account"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Login(System.String) on controller BaseGIS.Web.Controllers.AccountController (BaseGIS.Web).
2025-06-23 14:17:25.088 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:17:25.091 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.3013ms.
2025-06-23 14:17:25.095 +03:30 [INF] Executing ViewResult, running view Login.
2025-06-23 14:17:25.158 +03:30 [INF] Executed ViewResult - view Login executed in 64.1898ms.
2025-06-23 14:17:25.162 +03:30 [INF] Executed action BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) in 110.2106ms
2025-06-23 14:17:25.166 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 14:17:25.167 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Account/Login - 200 null text/html; charset=utf-8 167.5743ms
2025-06-23 14:17:25.211 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 14:17:25.211 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 14:17:25.217 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 5.8219ms
2025-06-23 14:17:25.276 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 64.298ms
2025-06-23 14:17:39.893 +03:30 [INF] Request starting HTTP/2 POST https://localhost:7172/Account/Login - application/x-www-form-urlencoded 259
2025-06-23 14:17:39.953 +03:30 [INF] CORS policy execution successful.
2025-06-23 14:17:39.981 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 14:17:39.992 +03:30 [INF] Route matched with {action = "Login", controller = "Account"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(BaseGIS.Web.ViewModels.LoginViewModel, System.String) on controller BaseGIS.Web.Controllers.AccountController (BaseGIS.Web).
2025-06-23 14:17:40.049 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:17:44.670 +03:30 [INF] Executed DbCommand (139ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedUserName] = @__normalizedUserName_0
2025-06-23 14:17:45.070 +03:30 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 14:17:45.090 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[NormalizedUserName] = @__normalizedUserName_0
2025-06-23 14:17:45.404 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[@__user_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ClaimType], [a].[ClaimValue], [a].[UserId]
FROM [AspNetUserClaims] AS [a]
WHERE [a].[UserId] = @__user_Id_0
2025-06-23 14:17:45.412 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 14:17:45.436 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Department], [a].[Description], [a].[FromTimeActivity], [a].[Name], [a].[NormalizedName], [a].[Position], [a].[TableAccess], [a].[ToTimeActivity], [a].[ToolAccess]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-23 14:17:45.487 +03:30 [INF] Executed DbCommand (3ms) [Parameters=[@__role_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0
2025-06-23 14:17:45.500 +03:30 [INF] AuthenticationScheme: Identity.Application signed in.
2025-06-23 14:17:45.507 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 14:17:45.515 +03:30 [INF] AuthenticationScheme: Cookies signed in.
2025-06-23 14:17:45.519 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.RedirectResult in 5466.4047ms.
2025-06-23 14:17:45.526 +03:30 [INF] Executing RedirectResult, redirecting to /User.
2025-06-23 14:17:45.528 +03:30 [INF] Executed action BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) in 5530.917ms
2025-06-23 14:17:45.529 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 14:17:45.537 +03:30 [INF] Request finished HTTP/2 POST https://localhost:7172/Account/Login - 302 0 null 5643.6496ms
2025-06-23 14:17:45.540 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/User - null null
2025-06-23 14:17:45.596 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.UserController.Index (BaseGIS.Web)'
2025-06-23 14:17:45.705 +03:30 [INF] Route matched with {action = "Index", controller = "User"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller BaseGIS.Web.Controllers.UserController (BaseGIS.Web).
2025-06-23 14:17:45.751 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.UserController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:17:45.753 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.UserController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.1936ms.
2025-06-23 14:17:45.756 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 14:17:45.933 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:17:45.941 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 14:17:45.976 +03:30 [INF] Executed ViewResult - view Index executed in 220.3611ms.
2025-06-23 14:17:46.036 +03:30 [INF] Executed action BaseGIS.Web.Controllers.UserController.Index (BaseGIS.Web) in 328.5912ms
2025-06-23 14:17:46.038 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.UserController.Index (BaseGIS.Web)'
2025-06-23 14:17:46.041 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/User - 200 null text/html; charset=utf-8 500.909ms
2025-06-23 14:17:46.056 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 14:17:46.056 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 14:17:46.067 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 10.3883ms
2025-06-23 14:17:46.090 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 33.5655ms
2025-06-23 14:17:50.029 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 14:17:50.065 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 14:17:50.069 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:17:50.097 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:17:50.105 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:17:50.290 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
ORDER BY [g].[Id], [t].[AliasName]
2025-06-23 14:17:51.305 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\Services\IBaseMapService.cs:line 79
2025-06-23 14:17:51.335 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 14:17:51.706 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 1604.4989ms.
2025-06-23 14:17:51.836 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 14:17:53.299 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 14:17:53.555 +03:30 [INF] Executed ViewResult - view Index executed in 1846.2054ms.
2025-06-23 14:17:53.557 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 3485.3824ms
2025-06-23 14:17:53.558 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 14:17:53.560 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 3531.6218ms
2025-06-23 14:17:53.696 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - null null
2025-06-23 14:17:53.697 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - null null
2025-06-23 14:17:53.711 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 14:17:53.758 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 14:17:53.758 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - 404 0 null 20.0135ms
2025-06-23 14:17:53.854 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 139.4395ms
2025-06-23 14:17:53.973 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css, Response status code: 404
2025-06-23 14:17:54.039 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 14:17:53.712 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 14:17:53.758 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - 404 0 null 19.297ms
2025-06-23 14:17:53.807 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 96.2007ms
2025-06-23 14:17:53.807 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/site_map.css?v=aoP519SFZBIvvQFvf5ec-er8O88aoHY0TCfrrJFfnUw - null null
2025-06-23 14:17:53.818 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 14:17:54.100 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 388.5338ms
2025-06-23 14:17:54.104 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css, Response status code: 404
2025-06-23 14:17:54.108 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 14:17:54.259 +03:30 [INF] Sending file. Request path: '/css/site_map.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\site_map.css'
2025-06-23 14:17:53.818 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/jqueryui/themes/base/jquery-ui.min.css - null null
2025-06-23 14:17:53.831 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 14:17:54.274 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 14:17:54.283 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/site_map.css?v=aoP519SFZBIvvQFvf5ec-er8O88aoHY0TCfrrJFfnUw - 499 20799 text/css 475.1851ms
2025-06-23 14:17:53.843 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/components.css - null null
2025-06-23 14:17:53.854 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/components/measurement-tools.css - null null
2025-06-23 14:17:53.867 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/components/spatial-analysis.css - null null
2025-06-23 14:17:53.879 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/components/goto-xy.css - null null
2025-06-23 14:17:53.888 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/components/property-tools.css - null null
2025-06-23 14:17:53.899 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/css/components/drawing-tools.css - null null
2025-06-23 14:17:53.910 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/img/logo.png - null null
2025-06-23 14:17:53.930 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/measurement-tools.js - null null
2025-06-23 14:17:54.433 +03:30 [INF] Sending file. Request path: '/css/components/measurement-tools.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\components\measurement-tools.css'
2025-06-23 14:17:54.414 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 583.0823ms
2025-06-23 14:17:54.409 +03:30 [INF] Sending file. Request path: '/lib/jqueryui/themes/base/jquery-ui.min.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\jqueryui\themes\base\jquery-ui.min.css'
2025-06-23 14:17:54.437 +03:30 [INF] Sending file. Request path: '/css/components/goto-xy.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\components\goto-xy.css'
2025-06-23 14:17:54.434 +03:30 [INF] Sending file. Request path: '/css/components/spatial-analysis.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\components\spatial-analysis.css'
2025-06-23 14:17:54.440 +03:30 [INF] Sending file. Request path: '/css/components/property-tools.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\components\property-tools.css'
2025-06-23 14:17:54.443 +03:30 [INF] Sending file. Request path: '/css/components/drawing-tools.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\components\drawing-tools.css'
2025-06-23 14:17:53.931 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/property-tools.js - null null
2025-06-23 14:17:53.974 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/search-tools.js - null null
2025-06-23 14:17:53.986 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/base-component.js - null null
2025-06-23 14:17:53.999 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/modules/baseMapManager.js - null null
2025-06-23 14:17:54.012 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/geomap-core.js - null null
2025-06-23 14:17:54.026 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/layer-tree.js - null null
2025-06-23 14:17:54.040 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/spatial-analysis.js - null null
2025-06-23 14:17:54.054 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/goto-xy.js - null null
2025-06-23 14:17:54.067 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/geomap/drawing-tools.js - null null
2025-06-23 14:17:54.275 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 457.1464ms
2025-06-23 14:17:54.433 +03:30 [INF] Sending file. Request path: '/css/components.css'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\css\components.css'
2025-06-23 14:17:54.576 +03:30 [INF] Sending file. Request path: '/img/logo.png'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\img\logo.png'
2025-06-23 14:17:54.582 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/components/measurement-tools.css - 200 7659 text/css 727.8243ms
2025-06-23 14:17:54.590 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/jqueryui/themes/base/jquery-ui.min.css - 499 30724 text/css 771.2583ms
2025-06-23 14:17:54.591 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/components/goto-xy.css - 200 9272 text/css 712.9133ms
2025-06-23 14:17:54.594 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/components/spatial-analysis.css - 200 8407 text/css 727.0992ms
2025-06-23 14:17:54.597 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/components/property-tools.css - 200 10176 text/css 708.7655ms
2025-06-23 14:17:54.600 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/components/drawing-tools.css - 200 11880 text/css 701.4066ms
2025-06-23 14:17:54.605 +03:30 [INF] The file /js/geomap/property-tools.js was not modified
2025-06-23 14:17:54.608 +03:30 [INF] The file /js/geomap/search-tools.js was not modified
2025-06-23 14:17:54.609 +03:30 [INF] Sending file. Request path: '/js/geomap/measurement-tools.js'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\js\geomap\measurement-tools.js'
2025-06-23 14:17:54.612 +03:30 [INF] The file /js/components/base-component.js was not modified
2025-06-23 14:17:54.617 +03:30 [INF] The file /js/modules/baseMapManager.js was not modified
2025-06-23 14:17:54.619 +03:30 [INF] The file /js/geomap/geomap-core.js was not modified
2025-06-23 14:17:54.621 +03:30 [INF] The file /js/geomap/layer-tree.js was not modified
2025-06-23 14:17:54.624 +03:30 [INF] The file /js/geomap/spatial-analysis.js was not modified
2025-06-23 14:17:54.626 +03:30 [INF] The file /js/geomap/goto-xy.js was not modified
2025-06-23 14:17:54.629 +03:30 [INF] The file /js/geomap/drawing-tools.js was not modified
2025-06-23 14:17:54.635 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/css/components.css - 200 7998 text/css 792.1536ms
2025-06-23 14:17:54.637 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/img/logo.png - 499 13521 image/png 728.2082ms
2025-06-23 14:17:54.658 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/fonts/Shabnam-Bold-FD.woff - null null
2025-06-23 14:17:54.667 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/property-tools.js - 304 null text/javascript 735.8481ms
2025-06-23 14:17:54.671 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/measurement-tools.js - 200 30262 text/javascript 744.8398ms
2025-06-23 14:17:54.668 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/search-tools.js - 304 null text/javascript 694.5694ms
2025-06-23 14:17:54.673 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/base-component.js - 304 null text/javascript 687.292ms
2025-06-23 14:17:54.674 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/modules/baseMapManager.js - 304 null text/javascript 675.7855ms
2025-06-23 14:17:54.676 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/geomap-core.js - 304 null text/javascript 664.0351ms
2025-06-23 14:17:54.678 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/layer-tree.js - 304 null text/javascript 652.7508ms
2025-06-23 14:17:54.680 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/spatial-analysis.js - 304 null text/javascript 639.9559ms
2025-06-23 14:17:54.682 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/goto-xy.js - 304 null text/javascript 627.9534ms
2025-06-23 14:17:54.684 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/geomap/drawing-tools.js - 304 null text/javascript 617.2578ms
2025-06-23 14:17:54.834 +03:30 [INF] Sending file. Request path: '/fonts/Shabnam-Bold-FD.woff'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\fonts\Shabnam-Bold-FD.woff'
2025-06-23 14:17:54.848 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 14:17:54.870 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/fonts/Shabnam-Bold-FD.woff - 499 47016 application/font-woff 211.5026ms
2025-06-23 14:17:54.872 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 24.2879ms
2025-06-23 14:17:54.877 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 14:17:54.879 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 14:17:54.882 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 5.8711ms
2025-06-23 14:17:54.888 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 14:17:54.893 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 14:17:54.897 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 3.3004ms
2025-06-23 14:17:54.900 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 14:17:54.941 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-23 14:17:54.942 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - null null
2025-06-23 14:17:54.947 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 14:17:54.950 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 14:17:54.957 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-23 14:17:54.958 +03:30 [INF] Route matched with {action = "GetBaseMaps", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.JsonResult] GetBaseMaps() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:17:55.010 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:17:55.044 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:17:55.051 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:17:55.302 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId) in D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\Services\IBaseMapService.cs:line 79
2025-06-23 14:17:55.426 +03:30 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-23 14:17:55.434 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 389.8081ms.
2025-06-23 14:17:55.437 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 388.1222ms.
2025-06-23 14:17:55.439 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType14`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:17:55.445 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) in 480.6707ms
2025-06-23 14:17:55.446 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType36`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType37`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType35`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:17:55.447 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 14:17:55.451 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - 200 null application/json; charset=utf-8 509.2758ms
2025-06-23 14:17:55.462 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 14:17:55.467 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 4.6161ms
2025-06-23 14:17:55.471 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 14:17:55.472 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 512.4123ms
2025-06-23 14:17:55.475 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 14:17:55.476 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 535.0363ms
2025-06-23 14:18:00.817 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Account/Login - null null
2025-06-23 14:18:00.822 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 14:18:00.823 +03:30 [INF] Route matched with {action = "Login", controller = "Account"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Login(System.String) on controller BaseGIS.Web.Controllers.AccountController (BaseGIS.Web).
2025-06-23 14:18:00.867 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:18:00.871 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 0.0205ms.
2025-06-23 14:18:00.874 +03:30 [INF] Executing ViewResult, running view Login.
2025-06-23 14:18:00.878 +03:30 [INF] Executed ViewResult - view Login executed in 4.3196ms.
2025-06-23 14:18:00.881 +03:30 [INF] Executed action BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web) in 55.2601ms
2025-06-23 14:18:00.882 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.AccountController.Login (BaseGIS.Web)'
2025-06-23 14:18:00.885 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Account/Login - 200 null text/html; charset=utf-8 68.1816ms
2025-06-23 14:18:00.900 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-23 14:18:00.904 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-23 14:18:00.905 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 5.115ms
2025-06-23 14:18:01.743 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - null null
2025-06-23 14:18:01.761 +03:30 [INF] Sending file. Request path: '/lib/leaflet/leaflet.js.map'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet\leaflet.js.map'
2025-06-23 14:18:01.765 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - 200 225544 text/plain 22.2139ms
2025-06-23 14:18:44.937 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 14:18:44.977 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 40.4687ms
2025-06-23 14:18:44.985 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 14:19:11.877 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - null null
2025-06-23 14:19:11.881 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/esri-leaflet/dist/esri-leaflet.js.map - null null
2025-06-23 14:19:11.882 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js.map - null null
2025-06-23 14:19:11.899 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/esri-leaflet/dist/esri-leaflet.js.map - 404 0 null 17.7735ms
2025-06-23 14:19:11.895 +03:30 [INF] The file /lib/leaflet/leaflet.js.map was not modified
2025-06-23 14:19:11.895 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js.map - null null
2025-06-23 14:19:11.924 +03:30 [INF] The file /lib/bootstrap/js/bootstrap.bundle.min.js.map was not modified
2025-06-23 14:19:11.929 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/esri-leaflet/dist/esri-leaflet.js.map, Response status code: 404
2025-06-23 14:19:11.930 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - 304 null text/plain 52.7352ms
2025-06-23 14:19:11.934 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js.map - 404 0 null 38.7561ms
2025-06-23 14:19:11.935 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/js/bootstrap.bundle.min.js.map - 304 null text/plain 53.1049ms
2025-06-23 14:19:11.947 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/esri-leaflet-renderers/dist/esri-leaflet-renderers.js.map, Response status code: 404
2025-06-23 14:21:57.796 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - null null
2025-06-23 14:21:57.813 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 14:21:57.818 +03:30 [INF] Route matched with {action = "GetAllMapViews", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllMapViews() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:21:57.845 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:21:57.945 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:21:57.983 +03:30 [INF] Executed DbCommand (7ms) [Parameters=[@__currentUser_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ID], [m].[Name], [m].[Description], [m].[GroupName], [m].[Date], [m].[Public], [a].[UserName], [m].[Zoom], [m].[BaseMapID]
FROM [MapViews] AS [m]
LEFT JOIN [AspNetUsers] AS [a] ON [m].[UserId] = [a].[Id]
WHERE [m].[Public] = CAST(1 AS bit) OR [a].[Id] = @__currentUser_Id_0
ORDER BY [m].[GroupName], [m].[Name]
2025-06-23 14:21:57.989 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 141.3384ms.
2025-06-23 14:21:57.992 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType23`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType33`9[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:21:58.006 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) in 185.1824ms
2025-06-23 14:21:58.008 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 14:21:58.013 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - 200 null application/json; charset=utf-8 218.035ms
2025-06-23 14:22:03.631 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - null null
2025-06-23 14:22:03.636 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 14:22:03.637 +03:30 [INF] Route matched with {action = "GetAllMapViews", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllMapViews() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:22:03.664 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:22:03.668 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:22:03.677 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__currentUser_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ID], [m].[Name], [m].[Description], [m].[GroupName], [m].[Date], [m].[Public], [a].[UserName], [m].[Zoom], [m].[BaseMapID]
FROM [MapViews] AS [m]
LEFT JOIN [AspNetUsers] AS [a] ON [m].[UserId] = [a].[Id]
WHERE [m].[Public] = CAST(1 AS bit) OR [a].[Id] = @__currentUser_Id_0
ORDER BY [m].[GroupName], [m].[Name]
2025-06-23 14:22:03.682 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 14.9209ms.
2025-06-23 14:22:03.684 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType23`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType33`9[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:22:03.687 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) in 45.5563ms
2025-06-23 14:22:03.689 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 14:22:03.691 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - 200 null application/json; charset=utf-8 59.6315ms
2025-06-23 14:24:27.426 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/images/spritesheet.svg - null null
2025-06-23 14:24:27.455 +03:30 [INF] Sending file. Request path: '/lib/leaflet-draw/dist/images/spritesheet.svg'. Physical path: 'D:\Backups\BaseProject\BaseGIS\BaseGIS.Web\wwwroot\lib\leaflet-draw\dist\images\spritesheet.svg'
2025-06-23 14:24:27.468 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-draw/dist/images/spritesheet.svg - 200 5551 image/svg+xml 42.9128ms
2025-06-23 14:25:06.959 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - null null
2025-06-23 14:25:06.965 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 14:25:06.968 +03:30 [INF] Route matched with {action = "GetAllMapViews", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllMapViews() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:25:06.993 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:25:07.018 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:25:07.026 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__currentUser_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ID], [m].[Name], [m].[Description], [m].[GroupName], [m].[Date], [m].[Public], [a].[UserName], [m].[Zoom], [m].[BaseMapID]
FROM [MapViews] AS [m]
LEFT JOIN [AspNetUsers] AS [a] ON [m].[UserId] = [a].[Id]
WHERE [m].[Public] = CAST(1 AS bit) OR [a].[Id] = @__currentUser_Id_0
ORDER BY [m].[GroupName], [m].[Name]
2025-06-23 14:25:07.031 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 26.5666ms.
2025-06-23 14:25:07.036 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType23`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType33`9[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:25:07.039 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) in 68.1252ms
2025-06-23 14:25:07.041 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 14:25:07.043 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - 200 null application/json; charset=utf-8 83.9003ms
2025-06-23 14:25:48.903 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 14:25:48.947 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 14:25:48.954 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:25:48.986 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:25:48.991 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:25:49.156 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 14:25:49.173 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 14:25:49.174 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 184.6374ms.
2025-06-23 14:25:49.178 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 14:25:50.182 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 14:25:50.487 +03:30 [INF] Executed ViewResult - view Index executed in 1311.4693ms.
2025-06-23 14:25:50.489 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 1532.4018ms
2025-06-23 14:25:50.491 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 14:25:50.492 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 1589.8168ms
2025-06-23 14:25:50.504 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - null null
2025-06-23 14:25:50.505 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - null null
2025-06-23 14:25:50.511 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - 404 0 null 7.0974ms
2025-06-23 14:25:50.512 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - 404 0 null 6.8651ms
2025-06-23 14:25:50.521 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css, Response status code: 404
2025-06-23 14:25:50.526 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css, Response status code: 404
2025-06-23 14:25:50.580 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 14:25:50.581 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 14:25:50.587 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 7.4297ms
2025-06-23 14:25:50.693 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 112.7689ms
2025-06-23 14:25:50.585 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 14:25:50.640 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 14:25:50.740 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 14:25:50.734 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 14:25:50.745 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 159.6673ms
2025-06-23 14:25:50.641 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 14:25:50.694 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 14:25:50.782 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 142.186ms
2025-06-23 14:25:50.787 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 14:25:50.791 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 150.105ms
2025-06-23 14:25:50.794 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 100.2579ms
2025-06-23 14:25:50.799 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 14:25:50.807 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 14:25:50.812 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 12.7281ms
2025-06-23 14:25:50.819 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 14:25:50.821 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 14:25:50.825 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 3.8684ms
2025-06-23 14:25:50.829 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 14:25:50.878 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-23 14:25:50.881 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - null null
2025-06-23 14:25:50.882 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 14:25:50.885 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 14:25:50.891 +03:30 [INF] Route matched with {action = "GetBaseMaps", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.JsonResult] GetBaseMaps() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:25:50.891 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-23 14:25:50.925 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:25:50.956 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:25:50.958 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:25:50.965 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-23 14:25:50.970 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 11.807ms.
2025-06-23 14:25:51.188 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 14:25:51.189 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType36`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType37`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType35`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:25:51.204 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 247.7722ms.
2025-06-23 14:25:51.210 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType14`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:25:51.212 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) in 317.387ms
2025-06-23 14:25:51.213 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 14:25:51.215 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - 200 null application/json; charset=utf-8 334.857ms
2025-06-23 14:25:51.216 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 290.2048ms
2025-06-23 14:25:51.221 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 14:25:51.223 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 344.6828ms
2025-06-23 14:25:51.226 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 14:25:51.232 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 5.9834ms
2025-06-23 14:25:51.236 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 14:26:32.226 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-23 14:26:32.232 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-23 14:26:32.234 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 7.9326ms
2025-06-23 14:26:33.189 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - null null
2025-06-23 14:26:33.194 +03:30 [INF] The file /lib/leaflet/leaflet.js.map was not modified
2025-06-23 14:26:33.195 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - 304 null text/plain 6.1325ms
2025-06-23 14:26:35.683 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 14:26:35.730 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 14:26:35.733 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:26:35.770 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:26:35.776 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:26:35.924 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 14:26:35.942 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 14:26:35.943 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 168.7612ms.
2025-06-23 14:26:35.947 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 14:26:35.951 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 14:26:35.971 +03:30 [INF] Executed ViewResult - view Index executed in 23.9987ms.
2025-06-23 14:26:35.974 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 239.2195ms
2025-06-23 14:26:35.975 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 14:26:35.977 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 293.7502ms
2025-06-23 14:26:36.152 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - null null
2025-06-23 14:26:36.154 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - null null
2025-06-23 14:26:36.156 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - 404 0 null 3.9204ms
2025-06-23 14:26:36.159 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - 404 0 null 5.4447ms
2025-06-23 14:26:36.164 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css, Response status code: 404
2025-06-23 14:26:36.168 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css, Response status code: 404
2025-06-23 14:26:36.189 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 14:26:36.190 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 14:26:36.193 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 4.2048ms
2025-06-23 14:26:36.196 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 6.3744ms
2025-06-23 14:26:36.199 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 14:26:36.199 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 14:26:36.203 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 14:26:36.207 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 7.9566ms
2025-06-23 14:26:36.223 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 14:26:36.227 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 14:26:36.230 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 14:26:36.246 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 19.1815ms
2025-06-23 14:26:36.261 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 37.5606ms
2025-06-23 14:26:36.374 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 14:26:36.377 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 3.4458ms
2025-06-23 14:26:36.381 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 14:26:36.384 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 14:26:36.388 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 3.6917ms
2025-06-23 14:26:36.391 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 14:26:36.427 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 14:26:36.432 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 5.3944ms
2025-06-23 14:26:36.437 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 14:26:36.539 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-23 14:26:36.540 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - null null
2025-06-23 14:26:36.547 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 14:26:36.548 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-23 14:26:36.590 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:26:36.590 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 14:26:36.594 +03:30 [INF] Route matched with {action = "GetBaseMaps", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.JsonResult] GetBaseMaps() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:26:36.595 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-23 14:26:36.621 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:26:36.624 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 31.908ms.
2025-06-23 14:26:36.628 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:26:36.628 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType36`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType37`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType35`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:26:36.639 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 87.1988ms
2025-06-23 14:26:36.640 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 14:26:36.642 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 102.9456ms
2025-06-23 14:26:36.750 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - null null
2025-06-23 14:26:37.004 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 14:26:37.006 +03:30 [INF] The file /lib/leaflet/leaflet.js.map was not modified
2025-06-23 14:26:37.023 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 397.1715ms.
2025-06-23 14:26:37.024 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - 304 null text/plain 273.7753ms
2025-06-23 14:26:37.027 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType14`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:26:37.032 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) in 434.3319ms
2025-06-23 14:26:37.034 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 14:26:37.035 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - 200 null application/json; charset=utf-8 495.593ms
2025-06-23 14:26:37.052 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 14:26:37.055 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 3.8509ms
2025-06-23 14:26:37.060 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 14:29:05.001 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 14:29:05.046 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 14:29:05.051 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:29:05.100 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:29:05.106 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:29:05.235 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 14:29:05.253 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 14:29:05.255 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 150.9005ms.
2025-06-23 14:29:05.258 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 14:29:06.361 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 14:29:06.652 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - null null
2025-06-23 14:29:06.653 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - null null
2025-06-23 14:29:06.717 +03:30 [INF] Executed ViewResult - view Index executed in 1460.4992ms.
2025-06-23 14:29:06.723 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - 404 0 null 70.6526ms
2025-06-23 14:29:06.723 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - 404 0 null 70.8705ms
2025-06-23 14:29:06.726 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 1669.3114ms
2025-06-23 14:29:06.731 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css, Response status code: 404
2025-06-23 14:29:06.736 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css, Response status code: 404
2025-06-23 14:29:06.738 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 14:29:06.747 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 1745.285ms
2025-06-23 14:29:06.758 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 14:29:06.761 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 14:29:06.761 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 3.6956ms
2025-06-23 14:29:06.764 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 3.3822ms
2025-06-23 14:29:06.769 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 14:29:06.772 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 14:29:06.773 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 14:29:06.780 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 8.3565ms
2025-06-23 14:29:06.788 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 14:29:06.872 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 14:29:06.873 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 14:29:06.878 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 4.9479ms
2025-06-23 14:29:06.880 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 7.9923ms
2025-06-23 14:29:06.951 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 14:29:06.955 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 4.4905ms
2025-06-23 14:29:06.959 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 14:29:06.981 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 14:29:06.985 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 4.4636ms
2025-06-23 14:29:06.991 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 14:29:07.014 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 14:29:07.018 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 4.7617ms
2025-06-23 14:29:07.023 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 14:29:07.700 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-23 14:29:07.704 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - null null
2025-06-23 14:29:07.706 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 14:29:07.709 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 14:29:07.716 +03:30 [INF] Route matched with {action = "GetBaseMaps", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.JsonResult] GetBaseMaps() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:29:07.716 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-23 14:29:07.735 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:29:07.755 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:29:07.760 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:29:07.762 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-23 14:29:07.767 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 6.6726ms.
2025-06-23 14:29:07.884 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 14:29:07.888 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType36`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType37`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType35`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:29:07.903 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 144.8871ms.
2025-06-23 14:29:07.908 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType14`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:29:07.909 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 169.4647ms
2025-06-23 14:29:07.910 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) in 190.7079ms
2025-06-23 14:29:07.911 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 14:29:07.912 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 14:29:07.914 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 214.6549ms
2025-06-23 14:29:07.916 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - 200 null application/json; charset=utf-8 212.292ms
2025-06-23 14:29:07.926 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 14:29:07.929 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 2.5468ms
2025-06-23 14:29:07.935 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 14:29:50.828 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - null null
2025-06-23 14:29:50.848 +03:30 [INF] The file /lib/leaflet/leaflet.js.map was not modified
2025-06-23 14:29:50.852 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet/leaflet.js.map - 304 null text/plain 23.6041ms
2025-06-23 14:30:51.612 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-23 14:30:51.617 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-23 14:30:51.618 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 6.5105ms
2025-06-23 14:30:59.845 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/mapService/img/basemap/osm.png - null null
2025-06-23 14:30:59.849 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/mapService/img/basemap/osm.png - 404 0 null 3.951ms
2025-06-23 14:30:59.854 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 14:31:31.444 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - null null
2025-06-23 14:31:31.458 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 14:31:31.464 +03:30 [INF] Route matched with {action = "GetAllMapViews", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllMapViews() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:31:31.492 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:31:31.498 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:31:31.507 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__currentUser_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ID], [m].[Name], [m].[Description], [m].[GroupName], [m].[Date], [m].[Public], [a].[UserName], [m].[Zoom], [m].[BaseMapID]
FROM [MapViews] AS [m]
LEFT JOIN [AspNetUsers] AS [a] ON [m].[UserId] = [a].[Id]
WHERE [m].[Public] = CAST(1 AS bit) OR [a].[Id] = @__currentUser_Id_0
ORDER BY [m].[GroupName], [m].[Name]
2025-06-23 14:31:31.512 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 16.6872ms.
2025-06-23 14:31:31.515 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType23`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType33`9[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:31:31.522 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) in 52.8496ms
2025-06-23 14:31:31.538 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 14:31:31.541 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - 200 null application/json; charset=utf-8 96.5237ms
2025-06-23 14:41:53.393 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 14:41:53.439 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 14:41:53.445 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:41:53.474 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:41:53.533 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:41:53.666 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 14:41:53.682 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 14:41:53.683 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 206.0487ms.
2025-06-23 14:41:53.688 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 14:41:54.657 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 14:41:54.898 +03:30 [INF] Executed ViewResult - view Index executed in 1212.0118ms.
2025-06-23 14:41:54.901 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 1452.7822ms
2025-06-23 14:41:54.902 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 14:41:54.905 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 1511.5736ms
2025-06-23 14:41:54.968 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - null null
2025-06-23 14:41:54.969 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - null null
2025-06-23 14:41:54.974 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - 404 0 null 6.3006ms
2025-06-23 14:41:54.975 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - 404 0 null 6.6434ms
2025-06-23 14:41:54.979 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css, Response status code: 404
2025-06-23 14:41:54.985 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css, Response status code: 404
2025-06-23 14:41:54.986 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 14:41:55.013 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 14:41:54.984 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 14:41:55.030 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 43.5219ms
2025-06-23 14:41:55.043 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 58.6806ms
2025-06-23 14:41:55.000 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 14:41:55.013 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 14:41:55.059 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 45.9765ms
2025-06-23 14:41:55.059 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 14:41:55.063 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 14:41:55.067 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 66.9397ms
2025-06-23 14:41:55.070 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 56.3168ms
2025-06-23 14:41:55.083 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 14:41:55.105 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 14:41:55.108 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 3.1104ms
2025-06-23 14:41:55.112 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 14:41:55.136 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 14:41:55.140 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 4.2841ms
2025-06-23 14:41:55.144 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 14:41:55.171 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 14:41:55.174 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 3.7937ms
2025-06-23 14:41:55.179 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 14:41:55.947 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-23 14:41:55.948 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - null null
2025-06-23 14:41:55.951 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 14:41:55.954 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 14:41:55.958 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-23 14:41:55.959 +03:30 [INF] Route matched with {action = "GetBaseMaps", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.JsonResult] GetBaseMaps() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:41:55.982 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:41:55.998 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:41:56.002 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-23 14:41:56.014 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 14.4011ms.
2025-06-23 14:41:56.020 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:41:56.021 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType36`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType37`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType35`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:41:56.136 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 14:41:56.140 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 178.7168ms
2025-06-23 14:41:56.155 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 151.9604ms.
2025-06-23 14:41:56.156 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 14:41:56.158 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType14`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:41:56.159 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 212.1381ms
2025-06-23 14:41:56.162 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) in 178.6546ms
2025-06-23 14:41:56.166 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 14:41:56.168 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - 200 null application/json; charset=utf-8 220.0653ms
2025-06-23 14:41:56.182 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 14:41:56.186 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 3.9186ms
2025-06-23 14:41:56.189 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
2025-06-23 14:51:33.208 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - null null
2025-06-23 14:51:33.250 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:51:33.261 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__user_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[Id], [a].[ClaimType], [a].[ClaimValue], [a].[UserId]
FROM [AspNetUserClaims] AS [a]
WHERE [a].[UserId] = @__user_Id_0
2025-06-23 14:51:33.313 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 14:51:33.317 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[ConcurrencyStamp], [a].[Department], [a].[Description], [a].[FromTimeActivity], [a].[Name], [a].[NormalizedName], [a].[Position], [a].[TableAccess], [a].[ToTimeActivity], [a].[ToolAccess]
FROM [AspNetRoles] AS [a]
WHERE [a].[NormalizedName] = @__normalizedName_0
2025-06-23 14:51:33.324 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__role_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a].[ClaimType], [a].[ClaimValue]
FROM [AspNetRoleClaims] AS [a]
WHERE [a].[RoleId] = @__role_Id_0
2025-06-23 14:51:33.329 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 14:51:33.332 +03:30 [INF] Route matched with {action = "GetAllMapViews", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetAllMapViews() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:51:33.414 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:51:33.419 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__currentUser_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[ID], [m].[Name], [m].[Description], [m].[GroupName], [m].[Date], [m].[Public], [a].[UserName], [m].[Zoom], [m].[BaseMapID]
FROM [MapViews] AS [m]
LEFT JOIN [AspNetUsers] AS [a] ON [m].[UserId] = [a].[Id]
WHERE [m].[Public] = CAST(1 AS bit) OR [a].[Id] = @__currentUser_Id_0
ORDER BY [m].[GroupName], [m].[Name]
2025-06-23 14:51:33.426 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 8.3037ms.
2025-06-23 14:51:33.428 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType23`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType33`9[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:51:33.431 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web) in 92.5671ms
2025-06-23 14:51:33.433 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetAllMapViews (BaseGIS.Web)'
2025-06-23 14:51:33.436 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetAllMapViews - 200 null application/json; charset=utf-8 227.984ms
2025-06-23 14:51:54.675 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - null null
2025-06-23 14:51:54.683 +03:30 [INF] The file /lib/bootstrap/css/bootstrap.rtl.min.css.map was not modified
2025-06-23 14:51:54.684 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/bootstrap/css/bootstrap.rtl.min.css.map - 304 null text/plain 15.9769ms
2025-06-23 14:52:19.098 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap - null null
2025-06-23 14:52:19.145 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 14:52:19.150 +03:30 [INF] Route matched with {action = "Index", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:52:19.177 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:52:19.182 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:52:19.189 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id], [t].[AliasName], [t].[DatasetType], [t].[GroupInfoId], [t].[MaxLabelScale], [t].[MaxScale], [t].[MinLabelScale], [t].[MinScale], [t].[Name], [t].[ShortName], [t].[SimplifyFactor], [t].[ValidationRule], [g].[Id], [g].[AliasName], [g].[Name]
FROM [TableInfos] AS [t]
INNER JOIN [GroupInfos] AS [g] ON [t].[GroupInfoId] = [g].[Id]
ORDER BY [g].[Id], [t].[AliasName]
2025-06-23 14:52:19.348 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 14:52:19.365 +03:30 [WRN] Failed to load base map config: خطا در بارگذاری نقشه‌های پایه
2025-06-23 14:52:19.366 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.ViewResult in 186.3533ms.
2025-06-23 14:52:19.369 +03:30 [INF] Executing ViewResult, running view Index.
2025-06-23 14:52:20.468 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [a0].[Name]
FROM [AspNetUserRoles] AS [a]
INNER JOIN [AspNetRoles] AS [a0] ON [a].[RoleId] = [a0].[Id]
WHERE [a].[UserId] = @__userId_0
2025-06-23 14:52:20.733 +03:30 [INF] Executed ViewResult - view Index executed in 1365.1679ms.
2025-06-23 14:52:20.736 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web) in 1584.118ms
2025-06-23 14:52:20.738 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.Index (BaseGIS.Web)'
2025-06-23 14:52:20.739 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap - 200 null text/html; charset=utf-8 1641.3583ms
2025-06-23 14:52:20.836 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - null null
2025-06-23 14:52:20.838 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - null null
2025-06-23 14:52:20.844 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css - 404 0 null 7.8826ms
2025-06-23 14:52:20.844 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css - 404 0 null 6.6388ms
2025-06-23 14:52:20.850 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.css, Response status code: 404
2025-06-23 14:52:20.855 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.css, Response status code: 404
2025-06-23 14:52:20.866 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 14:52:20.868 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 14:52:20.870 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 4.2585ms
2025-06-23 14:52:20.873 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 14:52:20.874 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 6.1246ms
2025-06-23 14:52:20.891 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 14:52:20.892 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - null null
2025-06-23 14:52:20.895 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 21.9484ms
2025-06-23 14:52:20.926 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/_vs/browserLink - null null
2025-06-23 14:52:20.912 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 14:52:20.926 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 14:52:20.921 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_framework/aspnetcore-browser-refresh.js - 200 16507 application/javascript; charset=utf-8 28.7115ms
2025-06-23 14:52:20.980 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/_vs/browserLink - 200 null text/javascript; charset=UTF-8 54.2317ms
2025-06-23 14:52:21.081 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - null null
2025-06-23 14:52:21.085 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js - 404 0 null 3.5544ms
2025-06-23 14:52:21.089 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-dialog/Leaflet.Dialog.js, Response status code: 404
2025-06-23 14:52:21.093 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - null null
2025-06-23 14:52:21.096 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js - 404 0 null 3.1783ms
2025-06-23 14:52:21.100 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/lib/leaflet-measure/leaflet-measure.js, Response status code: 404
2025-06-23 14:52:21.106 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/js/components/component-factory.js - null null
2025-06-23 14:52:21.110 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/js/components/component-factory.js - 404 0 null 4.1244ms
2025-06-23 14:52:21.114 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/js/components/component-factory.js, Response status code: 404
2025-06-23 14:52:21.218 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - null null
2025-06-23 14:52:21.219 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - null null
2025-06-23 14:52:21.222 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 14:52:21.225 +03:30 [INF] Executing endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 14:52:21.231 +03:30 [INF] Route matched with {action = "ServiceDescription", controller = "REST"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ServiceDescription(System.String, System.String, System.String) on controller BaseGIS.Web.Controllers.RESTController (BaseGIS.Web).
2025-06-23 14:52:21.233 +03:30 [INF] Route matched with {action = "GetBaseMaps", controller = "GeoMap"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.JsonResult] GetBaseMaps() on controller BaseGIS.Web.Controllers.GeoMapController (BaseGIS.Web).
2025-06-23 14:52:21.260 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:52:21.281 +03:30 [INF] Executing action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) - Validation state: "Valid"
2025-06-23 14:52:21.285 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [t].[Id] AS [id], [t].[Name] AS [name], [t].[ShortName] AS [shortName], CAST(1 AS bit) AS [defaultVisibility], -1 AS [parentLayerId], N'[]' AS [subLayerIds], 0 AS [minScale]
FROM [TableInfos] AS [t]
2025-06-23 14:52:21.300 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 16.8131ms.
2025-06-23 14:52:21.328 +03:30 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType36`10[[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType37`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Collections.Generic.List`1[[<>f__AnonymousType35`8[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], BaseGIS.Web, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:52:21.329 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [a].[Id], [a].[AccessFailedCount], [a].[ConcurrencyStamp], [a].[Email], [a].[EmailConfirmed], [a].[Enabled], [a].[FullName], [a].[Image], [a].[LockoutEnabled], [a].[LockoutEnd], [a].[NormalizedEmail], [a].[NormalizedUserName], [a].[PasswordHash], [a].[PhoneNumber], [a].[PhoneNumberConfirmed], [a].[SecurityStamp], [a].[TwoFactorEnabled], [a].[UserGroup], [a].[UserName]
FROM [AspNetUsers] AS [a]
WHERE [a].[Id] = @__p_0
2025-06-23 14:52:21.335 +03:30 [INF] Executed action BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web) in 102.0502ms
2025-06-23 14:52:21.338 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.RESTController.ServiceDescription (BaseGIS.Web)'
2025-06-23 14:52:21.339 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/rest/services/map/mapserver/?f=json - 200 null application/json; charset=utf-8 121.3496ms
2025-06-23 14:52:21.576 +03:30 [ERR] Error getting active base maps for user cdd9c250-37eb-4171-9e9e-02bf76708ae9
System.InvalidOperationException: The client projection contains a reference to a constant expression of 'BaseGIS.Web.Services.BaseMapService' through the instance method 'SanitizeUrl'. This could potentially cause a memory leak; consider making the method static so that it does not capture constant in the instance. See https://go.microsoft.com/fwlink/?linkid=2103067 for more information and examples.
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.ConstantVerifyingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberAssignment(MemberAssignment node)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberBinding(MemberBinding node)
   at System.Linq.Expressions.ExpressionVisitor.Visit[T](ReadOnlyCollection`1 nodes, Func`2 elementVisitor)
   at System.Linq.Expressions.ExpressionVisitor.VisitMemberInit(MemberInitExpression node)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VerifyNoClientConstant(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitShapedQuery(ShapedQueryExpression shapedQueryExpression)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.RelationalShapedQueryCompilingExpressionVisitor.VisitExtension(Expression extensionExpression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at BaseGIS.Web.Services.BaseMapService.GetActiveBaseMapsAsync(String userId)
2025-06-23 14:52:21.601 +03:30 [INF] Executed action method BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web), returned result Microsoft.AspNetCore.Mvc.JsonResult in 316.4207ms.
2025-06-23 14:52:21.603 +03:30 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType14`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:52:21.606 +03:30 [INF] Executed action BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web) in 344.1061ms
2025-06-23 14:52:21.607 +03:30 [INF] Executed endpoint 'BaseGIS.Web.Controllers.GeoMapController.GetBaseMaps (BaseGIS.Web)'
2025-06-23 14:52:21.608 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/GeoMap/GetBaseMaps - 200 null application/json; charset=utf-8 388.8586ms
2025-06-23 14:52:21.760 +03:30 [INF] Request starting HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - null null
2025-06-23 14:52:21.775 +03:30 [INF] Request finished HTTP/2 GET https://localhost:7172/Content/mapService/img/basemap/osm.png - 404 0 null 15.7435ms
2025-06-23 14:52:21.833 +03:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7172/Content/mapService/img/basemap/osm.png, Response status code: 404
