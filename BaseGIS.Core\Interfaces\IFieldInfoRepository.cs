using BaseGIS.Core.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BaseGIS.Core.Interfaces
{
    public interface IFieldInfoRepository
    {
        Task<FieldInfo> GetByIdAsync(int id);
        Task<List<FieldInfo>> GetByTableInfoIdAsync(int tableInfoId);
        Task AddAsync(FieldInfo fieldInfo);
        Task UpdateAsync(FieldInfo fieldInfo);
        Task DeleteAsync(int id);
    }
}