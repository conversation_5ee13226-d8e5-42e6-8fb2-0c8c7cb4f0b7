﻿@{
    ViewData["Title"] = "مپینگ شپ‌فایل";
    var tableId = ViewData["TableId"];
    var shapeFields = ViewData["ShapeFields"] as IEnumerable<dynamic>;
    var tableFields = ViewData["TableFields"] as IEnumerable<dynamic>;
    var mapping = ViewData["Mapping"] as IEnumerable<dynamic>;
    var shapefilePath = ViewData["ShapefilePath"] as string;
}

@section Styles {
    <style>
        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .form-control {
            font-family: 'Shabnam', sans-serif;
            direction: rtl;
        }
    </style>
}

<div class="container-fluid">
    <div class="card shadow-sm">
        <div class="card-header">
            <h6 class="mb-0">مپینگ شپ‌فایل</h6>
        </div>
        <div class="card-body">
            <form id="importShapefileForm" asp-action="ImportShapefileApply" method="post">
                <input type="hidden" name="TableInfoId" value="@tableId" />
                <input type="hidden" name="ShapefilePath" value="@shapefilePath" />
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>فیلد جدول</th>
                            <th>فیلد شپ‌فایل</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var tf in tableFields)
                        {
                            var map = mapping.FirstOrDefault(m => m.TableField == tf.Name);
                            <tr>
                                <td>@tf.Name</td>
                                <td>
                                    <select name="FieldMappings[@(tableFields.ToList().IndexOf(tf))].TableField" class="form-control" hidden>
                                        <option value="@tf.Name" selected>@tf.Name</option>
                                    </select>
                                    <select name="FieldMappings[@(tableFields.ToList().IndexOf(tf))].ShapeField" class="form-control">
                                        <option value="">-- انتخاب کنید --</option>
                                        @foreach (var sf in shapeFields)
                                        {
                                            if (map != null && sf.Name == map.ShapeField)
                                            {
                                                <option value="@sf.Name" selected>@sf.Name</option>
                                            }
                                            else
                                            {
                                                <option value="@sf.Name">@sf.Name</option>
                                            }
                                        }
                                    </select>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
                <button type="submit" class="btn btn-success mt-3">اعمال مپینگ</button>
                <a asp-action="TableDetails" asp-route-id="@tableId" class="btn btn-secondary mt-3">بازگشت</a>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // اعتبارسنجی فرم (در صورت نیاز می‌توان اضافه کرد)
            $("#importShapefileForm").on("submit", function (e) {
                // در حال حاضر هیچ اعتبارسنجی اضافی لازم نیست
            });
        });
    </script>
}