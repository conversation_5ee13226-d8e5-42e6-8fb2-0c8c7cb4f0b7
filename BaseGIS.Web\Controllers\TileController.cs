﻿using BaseGIS.Core.Services;
using BaseGIS.Infrastructure.Persistence;
using Microsoft.AspNetCore.Mvc;

namespace BaseGIS.Web.Controllers
{

    public class TileController : Controller
    {
        private readonly ApplicationDbContext _db;
        private readonly IDdlService _ddlService;
        private readonly ILogger<DatabaseController> _logger;
        private readonly IConfiguration _configuration;
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly IWebHostEnvironment _environment;

        public TileController(ApplicationDbContext db, IDdlService ddlService, ILogger<DatabaseController> logger, IConfiguration configuration,
            IServiceScopeFactory scopeFactory, IWebHostEnvironment environment)
        {
            _db = db;
            _ddlService = ddlService;
            _logger = logger;
            _configuration = configuration;
            _scopeFactory = scopeFactory;
            _environment = environment;
        }

        [HttpGet("api/Tile/tile/{tableId}/{z}/{x}/{y}")]
        public IActionResult GetTile(int tableId, int z, int x, int y)
        {
            try
            {
                string basePath = _configuration["TileStoragePath"] ?? "wwwroot/tiles";
                var tilePath = Path.Combine(basePath, tableId.ToString(), z.ToString(), x.ToString(), $"{y}.mvt");

                _logger.LogInformation("Attempting to read tile from: {TilePath}", tilePath);

                if (!System.IO.File.Exists(tilePath))
                {
                    _logger.LogWarning("Tile not found at: {TilePath}", tilePath);
                    return NotFound();
                }

                var tileData = System.IO.File.ReadAllBytes(tilePath);
                _logger.LogInformation("Successfully read tile z={Z}, x={X}, y={Y} from {TilePath}", z, x, y, tilePath);

                Response.Headers.Add("Cache-Control", "public, max-age=3600");
                Response.Headers.Add("ETag", Convert.ToBase64String(System.Security.Cryptography.SHA256.Create().ComputeHash(tileData)));
                return File(tileData, "application/vnd.mapbox-vector-tile");
            }
            catch (IOException ex)
            {
                _logger.LogError(ex, "Failed to read tile z={Z}, x={X}, y={Y} for tableId={TableId}", z, x, y, tableId);
                return StatusCode(500, "Error reading tile file.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error while serving tile z={Z}, x={X}, y={Y} for tableId={TableId}", z, x, y, tableId);
                return StatusCode(500, "Unexpected error.");
            }
        }

        [HttpGet("proxy/osm/{z}/{x}/{y}.png")]
        public async Task<IActionResult> ProxyOsmTile(int z, int x, int y)
        {
            try
            {
                // انتخاب تصادفی یکی از زیر دامنه‌های a, b, c
                var subdomains = new[] { "a", "b", "c" };
                var subdomain = subdomains[new Random().Next(0, subdomains.Length)];
                var url = $"https://{subdomain}.tile.openstreetmap.org/{z}/{x}/{y}.png";

                using var client = new HttpClient();
                // افزودن هدر User-Agent برای رعایت سیاست‌های OpenStreetMap
                client.DefaultRequestHeaders.Add("User-Agent", "BaseGIS/1.0 (YourAppName)");

                var response = await client.GetAsync(url);
                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Failed to fetch OSM tile z={Z}, x={X}, y={Y} from {Url}. Status: {Status}", z, x, y, url, response.StatusCode);
                    return NotFound();
                }

                var tileData = await response.Content.ReadAsByteArrayAsync();
                _logger.LogInformation("Successfully proxied OSM tile z={Z}, x={X}, y={Y} from {Url}", z, x, y, url);

                Response.Headers.Add("Cache-Control", "public, max-age=86400"); // 24 ساعت
                return File(tileData, "image/png");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error proxying OSM tile z={Z}, x={X}, y={Y}", z, x, y);
                return StatusCode(500, "Error fetching tile.");
            }
        }

        [HttpGet("glyphs/{fontstack}/{range}.pbf")]
        public IActionResult GetGlyphs(string fontstack, string range)
        {
            try
            {
                var glyphPath = Path.Combine(_environment.WebRootPath, "glyphs", fontstack, $"{range}.pbf");
                if (!System.IO.File.Exists(glyphPath))
                {
                    _logger.LogWarning("Glyph not found at: {GlyphPath}", glyphPath);
                    return NotFound();
                }
                var glyphData = System.IO.File.ReadAllBytes(glyphPath);
                return File(glyphData, "application/x-protobuf");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error serving glyphs for fontstack={Fontstack}, range={Range}", fontstack, range);
                return StatusCode(500, "Error fetching glyphs.");
            }
        }
    }
}