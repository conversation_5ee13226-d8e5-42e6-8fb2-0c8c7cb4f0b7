namespace BaseGIS.Core.Entities
{
    public class TableInfo
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string AliasName { get; set; }
        public int GroupInfoId { get; set; }
        public GroupInfo GroupInfo { get; set; }
        public string DatasetType { get; set; }
        public string ShortName { get; set; }
        public string? ValidationRule { get; set; }
        public double MinScale { get; set; }
        public double MaxScale { get; set; }
        public double MinLabelScale { get; set; }
        public double MaxLabelScale { get; set; }
        public List<FieldInfo> FieldInfos { get; set; }
        public double SimplifyFactor { get; set; }
        public List<SymbologyInfo> Symbologies { get; set; }
    }
}