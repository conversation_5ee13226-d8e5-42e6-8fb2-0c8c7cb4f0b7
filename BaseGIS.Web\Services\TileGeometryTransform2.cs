﻿using NetTopologySuite.Geometries;

namespace BaseGIS.Web.Services
{
    /// <summary>
    /// Transforms Web Mercator (EPSG:3857) coordinates to Mapbox Vector Tile (MVT) local coordinates (0-extent).
    /// It also handles the conversion from global pixel coordinates to tile-local pixel coordinates.
    /// </summary>
    internal struct TileGeometryTransform
    {
        private readonly NetTopologySuite.IO.VectorTiles.Tiles.Tile _tileInfo;
        private readonly uint _extent;
        // Changed to double to avoid precision loss and type conversion errors
        private readonly double _tileOriginXGlobalPx;
        private readonly double _tileOriginYGlobalPx;
        private readonly int _inputTileSize; // e.g. 256 or 512

        public double ZoomResolution { get; } // meters per pixel at current zoom level

        public TileGeometryTransform(NetTopologySuite.IO.VectorTiles.Tiles.Tile tileInfo, uint extent)
        {
            _tileInfo = tileInfo;
            _extent = extent;
            _inputTileSize = WebMercatorHandler.DefaultTileSize;

            ZoomResolution = WebMercatorHandler.Resolution(tileInfo.Zoom, _inputTileSize);

            // Calculate the top-left corner of the tile in Web Mercator Meters
            (double tileLeftMeter, double tileTopMeter) = WebMercatorHandler.GetTileTopLeftMeters(tileInfo.X, tileInfo.Y, tileInfo.Zoom, _inputTileSize);

            // Calculate the top-left corner of the tile in GLOBAL PIXEL coordinates at the current zoom level.
            // This is the origin from which we will calculate relative pixel positions.
            (_tileOriginXGlobalPx, _tileOriginYGlobalPx) = WebMercatorHandler.FromMetersToPixels((tileLeftMeter, tileTopMeter), ZoomResolution);
        }

        /// <summary>
        /// Transforms a coordinate from Web Mercator (sequence) to tile-local (0-extent) coordinates
        /// and calculates the delta from the previous point.
        /// </summary>
        /// <param name="sequence">The coordinate sequence containing the Web Mercator point.</param>
        /// <param name="index">The index of the coordinate in the sequence.</param>
        /// <param name="currentX">Reference to the last transformed X coordinate (updated by this method).</param>
        /// <param name="currentY">Reference to the last transformed Y coordinate (updated by this method).</param>
        /// <returns>A tuple (deltaX, deltaY) representing the change in coordinates in the 0-extent space.</returns>
        public (int x, int y) Transform(CoordinateSequence sequence, int index, ref int currentX, ref int currentY)
        {
            if (sequence == null)
            {
                throw new ArgumentNullException(nameof(sequence));
            }
            if (sequence.Count == 0)
            {
                throw new ArgumentException("sequence is empty.", nameof(sequence));
            }

            double xMeter = sequence.GetOrdinate(index, Ordinate.Spatial1);
            double yMeter = sequence.GetOrdinate(index, Ordinate.Spatial2);

            // 1. Convert input meters to GLOBAL PIXEL coordinates at current zoom level.
            (double pxGlobal, double pyGlobal) = WebMercatorHandler.FromMetersToPixels((xMeter, yMeter), ZoomResolution);

            // 2. Calculate pixel coordinates relative to the top-left of the current tile.
            double tileRelativePxX = pxGlobal - _tileOriginXGlobalPx;
            double tileRelativePxY = pyGlobal - _tileOriginYGlobalPx;

            // 3. Scale these tile-relative pixels to the MVT extent (e.g., 4096).
            double scaleFactorFromTilePixelsToExtent = (double)_extent / _inputTileSize;

            int scaledX = (int)Math.Round(tileRelativePxX * scaleFactorFromTilePixelsToExtent);
            int scaledY = (int)Math.Round(tileRelativePxY * scaleFactorFromTilePixelsToExtent);

            // Apply buffer clipping as per MVT spec
            const int buffer = 128; // Standard MVT buffer
            scaledX = Math.Max(-buffer, Math.Min((int)_extent + buffer, scaledX));
            scaledY = Math.Max(-buffer, Math.Min((int)_extent + buffer, scaledY));

            // Calculate delta from the last point
            int deltaX = scaledX - currentX;
            int deltaY = scaledY - currentY;

            // Update currentX and currentY for the next point's delta calculation
            currentX = scaledX;
            currentY = scaledY;

            return (deltaX, deltaY);
        }

        /// <summary>
        /// Transforms tile-local coordinates (0-extent) back to geographical (Lat/Lon) coordinates.
        /// Useful for debugging the output of the MVT.
        /// </summary>
        /// <param name="x">X coordinate in the 0-extent space.</param>
        /// <param name="y">Y coordinate in the 0-extent space.</param>
        /// <returns>A tuple (longitude, latitude) in degrees.</returns>
        public (double longitude, double latitude) TransformInverse(int x, int y)
        {
            // First, scale back from extent to original tile pixels (e.g., 256)
            double scaleFactorFromExtentToTilePixels = (double)_inputTileSize / _extent;
            double originalTilePxX = x * scaleFactorFromExtentToTilePixels;
            double originalTilePyY = y * scaleFactorFromExtentToTilePixels;

            // Then, add the global pixel offset of the tile origin to get global pixels
            double pxGlobal = _tileOriginXGlobalPx + originalTilePxX;
            double pyGlobal = _tileOriginYGlobalPx + originalTilePyY;

            // Convert global pixels back to Web Mercator meters
            (double xMeter, double yMeter) = WebMercatorHandler.FromPixelsToMeters((pxGlobal, pyGlobal), ZoomResolution);

            // Finally, convert Web Mercator meters to Lat/Lon
            return WebMercatorHandler.MetersToLatLon((xMeter, yMeter));
        }

        /// <summary>
        /// Checks if a point in 0-extent coordinates is within the tile's extent (including buffer).
        /// </summary>
        public bool IsPointInExtent(int x, int y)
        {
            const int buffer = 128;
            return x >= -buffer && y >= -buffer && x < _extent + buffer && y < _extent + buffer;
        }

        /// <summary>
        /// Calculates the extent (width and height) of an envelope in scaled pixels (0-extent space).
        /// These values represent the dimensions within the MVT extent (e.g., 0-4096),
        /// not global pixel coordinates.
        /// </summary>
        /// <param name="env">The envelope in Web Mercator meters.</param>
        /// <returns>A tuple (width, height) in scaled pixels within the MVT extent.</returns>
        public (int width, int height) ExtentInScaledPixel(Envelope env)
        {
            // Convert min/max meters to global pixels.
            (double minXGlobalPx, double minYGlobalPx) = WebMercatorHandler.FromMetersToPixels((env.MinX, env.MinY), ZoomResolution);
            (double maxXGlobalPx, double maxYGlobalPx) = WebMercatorHandler.FromMetersToPixels((env.MaxX, env.MaxY), ZoomResolution);

            // Calculate width and height in global pixels
            double widthGlobalPx = Math.Abs(maxXGlobalPx - minXGlobalPx);
            double heightGlobalPx = Math.Abs(maxYGlobalPx - minYGlobalPx);

            // Scale the width and height to the MVT extent
            double scaleFactorFromTilePixelsToExtent = (double)_extent / _inputTileSize;

            // Round to int as these represent dimensions within the tile's extent (e.g., 0-4096)
            int scaledWidth = (int)Math.Round(widthGlobalPx * scaleFactorFromTilePixelsToExtent);
            int scaledHeight = (int)Math.Round(heightGlobalPx * scaleFactorFromTilePixelsToExtent);

            // Ensure non-negative results for dimensions
            scaledWidth = Math.Max(0, scaledWidth);
            scaledHeight = Math.Max(0, scaledHeight);

            return (scaledWidth, scaledHeight);
        }
    }
}