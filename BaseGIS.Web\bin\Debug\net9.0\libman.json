{"version": "1.0", "defaultProvider": "cdnjs", "libraries": [{"library": "bootstrap@5.3.3", "destination": "wwwroot/lib/bootstrap/", "files": ["css/bootstrap.rtl.min.css", "css/bootstrap.rtl.min.css.map", "js/bootstrap.bundle.min.js", "js/bootstrap.bundle.min.js.map"]}, {"library": "jquery@3.7.1", "destination": "wwwroot/lib/jquery/"}, {"library": "font-awesome@6.7.2", "destination": "wwwroot/lib/font-awesome/"}, {"library": "leaflet@1.9.4", "destination": "wwwroot/lib/leaflet/"}, {"library": "echarts@5.4.3", "destination": "wwwroot/lib/echarts/"}, {"provider": "js<PERSON><PERSON><PERSON>", "library": "persian-date@1.1.0", "destination": "wwwroot/lib/persian-date/", "files": ["dist/persian-date.min.js"]}, {"provider": "js<PERSON><PERSON><PERSON>", "library": "persian-datepicker@1.2.0", "destination": "wwwroot/lib/persian-datepicker/", "files": ["dist/js/persian-datepicker.min.js", "dist/css/persian-datepicker.min.css"]}, {"library": "vis-timeline@7.7.0", "destination": "wwwroot/lib/vis-timeline/", "files": ["vis-timeline-graph2d.min.js", "vis-timeline-graph2d.min.css"]}, {"provider": "unpkg", "library": "leaflet-draw@1.0.4", "destination": "wwwroot/lib/leaflet-draw/", "files": ["dist/leaflet.draw.js", "dist/leaflet.draw.css", "dist/images/spritesheet.svg", "dist/images/spritesheet.png", "dist/images/spritesheet-2x.png"]}, {"library": "sweetalert2@11.14.4", "destination": "wwwroot/lib/sweetalert2/", "files": ["sweetalert2.min.js", "sweetalert2.min.css"]}, {"library": "jstree@3.3.12", "destination": "wwwroot/lib/jstree/", "files": ["jstree.min.js", "themes/default/style.min.css"]}, {"library": "xlsx@0.18.5", "destination": "wwwroot/lib/xlsx/"}, {"library": "jsgrid@1.5.3", "destination": "wwwroot/lib/jsgrid/", "files": ["jsgrid.min.js", "jsgrid.min.css", "jsgrid-theme.min.css"]}, {"provider": "unpkg", "library": "terraformer@1.0.9", "destination": "wwwroot/lib/terraformer/", "files": ["terraformer.js"]}, {"provider": "unpkg", "library": "terraformer-wkt-parser@1.2.1", "destination": "wwwroot/lib/terraformer-wkt-parser/", "files": ["terraformer-wkt-parser.js"]}, {"provider": "unpkg", "library": "leaflet.vectorgrid@1.3.0", "destination": "wwwroot/lib/leaflet-vectorgrid", "files": ["dist/Leaflet.VectorGrid.bundled.js"]}, {"provider": "unpkg", "library": "mapbox-gl@2.15.0", "destination": "wwwroot/lib/mapbox-gl", "files": ["dist/mapbox-gl.js", "dist/mapbox-gl.css"]}, {"provider": "unpkg", "library": "@mapbox/mapbox-gl-draw@1.4.3", "destination": "wwwroot/lib/mapbox-gl-draw", "files": ["dist/mapbox-gl-draw.js", "dist/mapbox-gl-draw.css"]}, {"provider": "unpkg", "library": "bootstrap-treeview@1.2.0", "destination": "wwwroot/lib/bootstrap-treeview", "files": ["dist/bootstrap-treeview.min.js", "dist/bootstrap-treeview.min.css"]}, {"provider": "unpkg", "library": "@microsoft/signalr@6.0.0", "destination": "wwwroot/lib/signalr", "files": ["dist/browser/signalr.min.js"]}, {"library": "overlayscrollbars@2.11.0", "destination": "wwwroot/lib/overlayscrollbars"}, {"library": "bootstrap-icons@1.11.3", "destination": "wwwroot/lib/bootstrap-icons"}, {"provider": "unpkg", "library": "@fontsource-variable/source-sans-3@5.0.7", "destination": "wwwroot/lib/source-sans-3"}, {"provider": "unpkg", "library": "@popperjs/core@2.11.8", "destination": "wwwroot/lib/popper.js"}, {"library": "jquery.fancytree@2.38.0", "destination": "wwwroot/lib/jquery.fancytree", "files": ["jquery.fancytree-all.min.js", "skin-win8/ui.fancytree.min.css", "skin-win8/icons.gif", "skin-win8/icons-rtl.gif"]}, {"library": "jqueryui@1.12.1", "destination": "wwwroot/lib/jqueryui", "files": ["jquery-ui.min.js", "themes/base/jquery-ui.min.css"]}, {"provider": "unpkg", "library": "esri-leaflet@2.5.3", "destination": "wwwroot/lib/esri-leaflet", "files": ["dist/esri-leaflet.js"]}, {"provider": "unpkg", "library": "esri-leaflet-renderers@2.0.2", "destination": "wwwroot/lib/esri-leaflet-renderers", "files": ["dist/esri-leaflet-renderers.js"]}, {"library": "ion-rangeslider@2.3.1", "destination": "wwwroot/lib/ion-rangeslider/"}, {"provider": "unpkg", "library": "smartwizard@6.0.5", "destination": "wwwroot/lib/smartwizard/", "files": ["dist/css/smart_wizard_all.min.css", "dist/js/jquery.smartWizard.min.js"]}, {"library": "select2@4.1.0-rc.0", "destination": "wwwroot/lib/select2/", "files": ["css/select2.min.css", "js/select2.min.js", "js/i18n/fa.js"]}, {"library": "dropzone@5.9.3", "destination": "wwwroot/lib/dropzone/", "files": ["min/dropzone.min.css", "min/dropzone.min.js"]}, {"provider": "unpkg", "library": "proj4@2.6.2", "destination": "wwwroot/lib/proj4/", "files": ["dist/proj4.js"]}, {"provider": "unpkg", "library": "leaflet-dialog@1.0.5", "destination": "wwwroot/lib/leaflet-dialog/", "files": ["Leaflet.Dialog.js", "Leaflet.Dialog.css"]}, {"provider": "unpkg", "library": "leaflet-measure@3.1.0", "destination": "wwwroot/lib/leaflet-measure/", "files": ["leaflet-measure.js", "leaflet-measure.css"]}]}