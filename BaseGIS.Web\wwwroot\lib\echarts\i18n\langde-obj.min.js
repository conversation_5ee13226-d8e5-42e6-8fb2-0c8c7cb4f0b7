!function(e){"function"==typeof define&&define.amd?define(["exports"],e):"object"==typeof exports&&"string"!=typeof exports.nodeName?e(exports):e({})}(function(e){var a,i={time:{month:["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","April","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","August","September","Ok<PERSON>ber","November","Dezember"],monthAbbr:["<PERSON>","Feb","<PERSON><PERSON>","Apr","Mai","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],dayOfWeek:["Sonntag","Montag","<PERSON><PERSON>ag","<PERSON><PERSON><PERSON><PERSON>","Donnerstag","Freitag","Samstag"],dayOfWeekAbbr:["So","Mo","Di","Mi","Do","Fr","Sa"]},legend:{selector:{all:"Alle",inverse:"Invertiert"}},toolbox:{brush:{title:{rect:"Box Auswahl",polygon:"<PERSON><PERSON> Auswahl",lineX:"Horizontale Auswahl",lineY:"Vertikale Auswahl",keep:"Bereich Auswahl",clear:"Auswahl zurücksetzen"}},dataView:{title:"Daten Ansicht",lang:["Daten Ansicht","Schließen","Aktualisieren"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom zurücksetzen"}},magicType:{title:{line:"Zu Liniendiagramm wechseln",bar:"Zu Balkendiagramm wechseln",stack:"Stapel",tiled:"Kachel"}},restore:{title:"Wiederherstellen"},saveAsImage:{title:"Als Bild speichern",lang:["Rechtsklick zum Speichern des Bildes"]}},series:{typeNames:{pie:"Tortendiagramm",bar:"Balkendiagramm",line:"Liniendiagramm",scatter:"Streudiagramm",effectScatter:"Welligkeits-Streudiagramm",radar:"Radar-Karte",tree:"Baum",treemap:"Baumkarte",boxplot:"Boxplot",candlestick:"Kerzenständer",k:"K Liniendiagramm",heatmap:"Heatmap",map:"Karte",parallel:"Parallele Koordinatenkarte",lines:"Liniendiagramm",graph:"Beziehungsgrafik",sankey:"Sankey-Diagramm",funnel:"Trichterdiagramm",gauge:"Meßanzeige",pictorialBar:"Bildlicher Balken",themeRiver:"Thematische Flusskarte",sunburst:"Sonnenausbruch"}},aria:{general:{withTitle:'Dies ist ein Diagramm über "{title}"',withoutTitle:"Dies ist ein Diagramm"},series:{single:{prefix:"",withName:" mit Typ {seriesType} namens {seriesName}.",withoutName:" mit Typ {seriesType}."},multiple:{prefix:". Es besteht aus {seriesCount} Serienzählung.",withName:" Die Serie {seriesId} ist ein {seriesType} welcher {seriesName} darstellt.",withoutName:" Die {seriesId}-Reihe ist ein {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"Die Daten sind wie folgt: ",partialData:"Die ersten {displayCnt} Elemente sind: ",withName:"die Daten für {name} sind {value}",withoutName:"{value}",separator:{middle:",",end:"."}}}};for(a in i)i.hasOwnProperty(a)&&(e[a]=i[a])});