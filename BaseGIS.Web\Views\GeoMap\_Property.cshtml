﻿@using BaseGIS.Core.Entities
@{
    var layerId = ViewBag.LayerId;
    var layerType = ViewBag.LayerType;
    bool viewJoinTables = false;

    List<SymbologyInfo> symbolgies = ViewBag.Symbolgies;
    SymbologyInfo symbolgyDefault = null;

    if (layerType == "1")
    {
        for (int i = 0; i < symbolgies.Count; i++)
        {
            if (symbolgies[i].IsDefault)
            {
                symbolgyDefault = symbolgies[i];
                break;
            }
        }
        viewJoinTables = layerId > 2; 
    }
    

}

@Html.AntiForgeryToken()
<div class="p-1">
    <input type="hidden" id="propertyLayerId" />
    <ul class="nav nav-tabs">
        <li class="nav-item active">
            <button class="nav-link active" id="tab1-tab" data-bs-toggle="tab" data-bs-target="#proptab1" type="button" role="tab" aria-controls="proptab1" aria-selected="true">نمایش</button>
        </li>
        <li class="nav-item">
            <button class="nav-link" id="tab2-tab" data-bs-toggle="tab" data-bs-target="#proptab2" type="button" role="tab" aria-controls="proptab2" aria-selected="false">نماد</button>
        </li>
    </ul>
    <div class="tab-content">
        <div class="tab-pane active" id="proptab1">
            <form id="contact-form" dir="rtl" style="padding:10px;">
                <fieldset>
                    <div class="row">
                        <div style="margin:0px;" dir="ltr">
                            <button class="btn btn-outline-danger btn-mini" style="float:left;" type="button" onclick="ZoomToExtentLayer($('#propertyLayerId').val(), false);" title="بزرگنمایی لایه"><i class="fa fa-search"></i></button>
                            <button class="btn btn-outline-primary btn-mini" style="float:left;" type="button" onclick="ZoomToExtentLayer($('#propertyLayerId').val(), true);" title="بزرگنمایی انتخاب شده ها"><i class="fa fa-search"></i></button>
                        </div>
                    </div>
                    <div class="row" style="border:1px solid #ccc;border-radius:2px;margin:2px;padding:2px;">
                        <div class="col-sm-12">
                            <label for="layerchecked" class="inline-label SamanFont">برچسب گذاری</label>
                            <div class="col-sm-12" style='padding:2px;'>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="form-check form-switch me-3">
                                        <input class="form-check-input" type="checkbox" name="layerchecked" id="layerchecked">
                                        <label class="form-check-label" for="layerchecked"></label>
                                    </div>
                                    <button class="btn btn-outline-warning btn-sm" type="button" onclick="$('#layerlabledd').val('');$('#layerlablejoindd').val('');" title="پاک کردن"><i class="fa fa-trash fa-lg"></i></button>
                                </div>
                               
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class='col-sm-12 well' dir="rtl">
                                اقلام جدول اصلی
                                <div class="input-group mb-1">
                                    <select id="lableField" class="form-select">
                                        <option value="" disabled selected hidden></option>
                                    </select>
                                    <button class="btn btn-outline-danger" type="button" onclick="$('#layerlabledd').val($('#layerlabledd').val() + '[' + $('#lableField').val() + ']');"><i class="fa fa-angle-double-down fa-lg"></i></button>
                                </div>
                                <div>
                                    <textarea name="layerlabledd" id="layerlabledd" style="width:100%; margin-top:5px; min-height:83px;"> </textarea>
                                </div>
                            </div>
                        </div>
                        <div class=" col-sm-6  " >
                            <div class='col-sm-12 well' dir="rtl" @(viewJoinTables ? "" : "hidden")>
                                اقلام جدول مرتبط
                                <select class="form-select" id="cboJoinEntityList" name="cboJoinEntityList">
                                    <option value="" tag=""></option>
                                </select>

                                <div class="input-group mb-1">
                                    
                                    <select id="cboJoinFieldList" class="form-select">
                                        <option value="" disabled selected hidden></option>
                                    </select>

                                    <button class="btn btn-sm btn-outline-danger pull-right" style="float:right;" type="button" onclick=" $('#layerlablejoindd').val($('#cboJoinEntityList').val() + '___' + $('#cboJoinFieldList').val());"><i class="fa fa-angle-double-down fa-lg"></i></button>
                                </div>
                                <div>
                                    <textarea name="layerlablejoindd" id="layerlablejoindd" style="width:100%; margin-top:5px"> </textarea>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="row" style="border:1px solid #ccc;border-radius:2px;margin:2px;padding:4px;">
                        <div class="col-md-12" dir="rtl">
                            <label for="name">میزان شفافیت</label>
                            <div id="layeropacity" name="layeropacity">
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-outline-danger pull-right samanFont" onclick="setlableopacity();">اعمال</button>
                </fieldset>
            </form>
        </div>
        <div class="tab-pane" id="proptab2">
           <form id="contact-form" dir="rtl" style="padding:10px;">
                <fieldset>
                    <div class="row">
                        <div class="col-sm-6" style="height:250px;overflow-y:auto;padding:5px;">
                            @if (layerType == "1")
                            {
                                for (int i = 0; i < symbolgies.Count; i++)
                                {
                                    <p>
                                        <input type="radio" name="radio_Sym" value="@symbolgies[i].Id" @(symbolgies[i].IsDefault ? "checked" : "") onclick="getLegend(@symbolgies[i].Id);" />
                                        <label for="sym1" class="inline-label SamanFont">@symbolgies[i].Name </label>
                                    </p>
                                }
                            }

                        </div>
                        <div class="col-sm-6" id="legendview" dir="ltr" style="height:250px;overflow-y:auto;padding:5px;">
                        </div>
                    </div>
                    <button type="button" class="btn btn-outline-danger pull-right samanFont" onclick="setSymbology();"> اعمال</button>
                </fieldset>
            </form>
        </div>
    </div>
</div>
<script>
    $(document).ready(function () {
        //$("#contact-form").css("border", "none").css("background-color", "white");
        var symid = $("#Sym" + settinglayerid).val();
        if (symid)
            $("input[name=radio_Sym][value='" + symid + "']").prop("checked", true);
        else {
    @{
        if (symbolgyDefault != null)
        {
            <text>
                            getLegend(@symbolgyDefault.Id);
            </text>
        }
        
    }
        }

    });

    function getFieldinLable(id) {
        // showLoading();
        $("#lableField").html("");
        $.ajax({
            type: 'POST',
            url: "/GeoMap/GetLayerFields",
            data: {
                "ID": id,
                 "__RequestVerificationToken": $("input[name='__RequestVerificationToken']").last().val()
            },
            dataType: 'json',
            success: function (data) {
                debugger;
                for (var i = 0; i < data.length; i++) {
                    $("#lableField").append('<option value="' + data[i].name + '">' + data[i].aliasName + '</option>');
                }
                // hideLoding();
            },
            error: function (data) {
                console.log(data);
                //hideLoding();
            }
        });
    }

    function getLegend(sym) {
        var html = "";
        $.ajax({
            type: 'POST',
            url:  "/Rest/Legend",
            data: { "layerId": "L" + settinglayerid, "symbologyId": sym },
            dataType: 'json',
            success: function (data) {
                html += "<ul style='list-style-type: none;padding:0px;' id='legendviewUL'>";
                for (var i = 0; i < data.length; i++) {
                    html += "<li style='padding-bottom:10px;'>";
                    html += "<img src='" + data[i].icon + "'/>&nbsp;&nbsp;";
                    html += data[i].title;
                    html += "</li>";
                }
                if (data.length >= 10) {
                    html += "<li style='padding-bottom:10px;'  id='__otherLegend'>";
                    html += " &nbsp;&nbsp;&nbsp;";
                    html += "<a href='#' onclick='getLegendOther(" + sym + ",1)'>...</a>";
                    html += "</li>";
                }

                html += "</ul>";
                $("#legendview").html(html);
            },
            error: function (data) {

            }
        });
    }
    function getLegendOther(sym, page) {
        var html = "";
        $.ajax({
            type: 'POST',
            url: "/Rest/Legend",
            data: { "layerId": "L" + settinglayerid, "symbologyId": sym, "page": page },
            dataType: 'json',
            success: function (data) {
                html += "<ul style='list-style-type: none;padding:0px;'>";
                for (var i = 0; i < data.length; i++) {
                    html += "<li style='padding-bottom:10px;'>";
                    html += "<img src='" + data[i].icon + "'/>&nbsp;&nbsp;";
                    html += data[i].title;
                    html += "</li>";
                }
                if (data.length >= 10) {
                    html += "<li style='padding-bottom:10px;' id='__otherLegend'>";
                    html += " &nbsp;&nbsp;&nbsp;";
                    html += "<a href='#' onclick='getLegendOther(" + sym + "," + parseInt(parseInt(page) + 1) + ")'>...</a>";
                    html += "</li>";
                }

                html += "</ul>";
                $('#__otherLegend').remove();
                $("#legendviewUL").append(html);
            },
            error: function (data) {
            }
        });
    }
</script>
