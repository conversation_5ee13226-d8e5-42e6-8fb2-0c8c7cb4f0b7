/**
 * Toolbar Component
 * کامپوننت نوار ابزار اصلی نقشه
 */

class Toolbar extends BaseComponent {
    getDefaultOptions() {
        return {
            ...super.getDefaultOptions(),
            position: 'topleft',
            tools: [
                'pan',
                'identify',
                'measurement',
                'spatial-analysis',
                'goto-xy',
                'drawing',
                'property',
                'search'
            ],
            showLabels: false,
            orientation: 'vertical', // vertical, horizontal
            onToolActivate: null,
            onToolDeactivate: null
        };
    }

    beforeInit() {
        this.activeTool = null;
        this.tools = new Map();
        this.toolButtons = new Map();
    }

    render() {
        this.renderToolbar();
        this.bindToolbarEvents();
    }

    renderToolbar() {
        const toolbarClass = `toolbar-${this.options.orientation}`;
        
        this.element.innerHTML = `
            <div class="map-toolbar ${toolbarClass}">
                ${this.renderToolButtons()}
            </div>
        `;
    }

    renderToolButtons() {
        const toolConfigs = {
            'pan': {
                icon: 'fa-hand',
                title: 'جابجایی',
                action: 'resetTools'
            },
            'identify': {
                icon: 'fa-info',
                title: 'شناسایی',
                action: 'activateIdentifyTool'
            },
            'measurement': {
                icon: 'fa-ruler',
                title: 'اندازه‌گیری',
                component: 'measurement-tools'
            },
            'spatial-analysis': {
                icon: 'fa-cogs',
                title: 'تحلیل مکانی',
                component: 'spatial-analysis'
            },
            'goto-xy': {
                icon: 'fa-crosshairs',
                title: 'رفتن به مختصات',
                component: 'goto-xy'
            },
            'drawing': {
                icon: 'fa-pencil',
                title: 'ترسیم',
                component: 'drawing-tools'
            },
            'property': {
                icon: 'fa-info-circle',
                title: 'خصوصیات',
                component: 'property-tools'
            },
            'search': {
                icon: 'fa-search',
                title: 'جستجو',
                component: 'search-tools'
            }
        };

        let html = '';
        
        this.options.tools.forEach(toolName => {
            const config = toolConfigs[toolName];
            if (!config) return;

            if (config.component) {
                // Component-based tool
                html += `
                    <div class="toolbar-component-container" data-tool="${toolName}">
                        <div id="${config.component}-container" data-component="${config.component}"></div>
                    </div>
                `;
            } else {
                // Action-based tool
                html += `
                    <button type="button" 
                            class="btn toolbar-btn" 
                            data-tool="${toolName}"
                            data-action="${config.action}"
                            title="${config.title}">
                        <i class="fa ${config.icon}"></i>
                        ${this.options.showLabels ? `<span class="toolbar-label">${config.title}</span>` : ''}
                    </button>
                `;
            }
        });

        return html;
    }

    bindToolbarEvents() {
        // Action-based tool buttons
        this.element.querySelectorAll('[data-action]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                const tool = e.currentTarget.dataset.tool;
                this.handleToolAction(action, tool);
            });
        });

        // Component containers will be initialized by their respective components
    }

    handleToolAction(action, toolName) {
        switch (action) {
            case 'resetTools':
                this.resetAllTools();
                break;
            case 'activateIdentifyTool':
                this.activateIdentifyTool();
                break;
            default:
                if (window[action] && typeof window[action] === 'function') {
                    window[action]();
                }
        }

        this.setActiveTool(toolName);
    }

    resetAllTools() {
        // Reset map cursor
        if (window.map) {
            window.map.getContainer().style.cursor = '';
        }

        // Reset global tool states
        window.isIdentify = false;
        window.isSketch = false;
        window.selecttool = false;
        window.selectBoxtool = false;

        // Deactivate all toolbar buttons
        this.element.querySelectorAll('.toolbar-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // Reset component states
        this.resetComponentStates();

        this.setActiveTool('pan');

        if (this.options.onToolDeactivate) {
            this.options.onToolDeactivate(this.activeTool);
        }

        this.trigger('toolDeactivate', { tool: this.activeTool });
    }

    resetComponentStates() {
        // Reset measurement tools
        if (window.measurementTools && window.measurementTools.reset) {
            window.measurementTools.reset();
        }

        // Reset spatial analysis tools
        if (window.spatialAnalysisTools && window.spatialAnalysisTools.reset) {
            window.spatialAnalysisTools.reset();
        }

        // Reset drawing tools
        if (window.drawingTools && window.drawingTools.reset) {
            window.drawingTools.reset();
        }

        // Reset property tools
        if (window.propertyTools && window.propertyTools.reset) {
            window.propertyTools.reset();
        }
    }

    activateIdentifyTool() {
        this.resetAllTools();
        
        window.isIdentify = true;
        
        if (window.map) {
            window.map.getContainer().style.cursor = 'crosshair';
        }

        // Activate identify button
        const identifyBtn = this.element.querySelector('[data-tool="identify"]');
        if (identifyBtn) {
            identifyBtn.classList.add('active');
        }

        this.setActiveTool('identify');
    }

    setActiveTool(toolName) {
        const previousTool = this.activeTool;
        this.activeTool = toolName;

        if (previousTool !== toolName) {
            if (this.options.onToolActivate) {
                this.options.onToolActivate(toolName, previousTool);
            }

            this.trigger('toolActivate', { 
                tool: toolName, 
                previousTool: previousTool 
            });
        }
    }

    // ========================================
    // Public API
    // ========================================

    getActiveTool() {
        return this.activeTool;
    }

    activateTool(toolName) {
        const toolBtn = this.element.querySelector(`[data-tool="${toolName}"]`);
        if (toolBtn) {
            toolBtn.click();
        }
    }

    addTool(toolName, config) {
        this.tools.set(toolName, config);
        // Re-render toolbar if needed
    }

    removeTool(toolName) {
        this.tools.delete(toolName);
        // Re-render toolbar if needed
    }

    showTool(toolName) {
        const toolElement = this.element.querySelector(`[data-tool="${toolName}"]`);
        if (toolElement) {
            toolElement.style.display = '';
        }
    }

    hideTool(toolName) {
        const toolElement = this.element.querySelector(`[data-tool="${toolName}"]`);
        if (toolElement) {
            toolElement.style.display = 'none';
        }
    }

    enableTool(toolName) {
        const toolElement = this.element.querySelector(`[data-tool="${toolName}"]`);
        if (toolElement) {
            toolElement.disabled = false;
            toolElement.classList.remove('disabled');
        }
    }

    disableTool(toolName) {
        const toolElement = this.element.querySelector(`[data-tool="${toolName}"]`);
        if (toolElement) {
            toolElement.disabled = true;
            toolElement.classList.add('disabled');
        }
    }

    // ========================================
    // Cleanup
    // ========================================

    destroy() {
        this.resetAllTools();
        this.tools.clear();
        this.toolButtons.clear();
        super.destroy();
    }
}

// Register component
window.ComponentFactory.register('toolbar', Toolbar);
