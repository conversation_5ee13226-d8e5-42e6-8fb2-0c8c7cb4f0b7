﻿@using BaseGIS.Core.Entities

@{
    string id = Context.Request.Query["id"];

    TableInfo tableInfo = ViewBag.TableInfos;
    int counter = 1;
}

<article class="col-sm-12 col-md-12 col-lg-12">
    <form id="movieForm" method="post">
        <div class="form-actions">
            <fieldset>
                <legend class="samanFont">
                    @(id == null ? "" : " لایه انتخاب شده: " + tableInfo.AliasName)

                </legend>
                <div class="form-group">
                    <label class="control-label samanFont"> لطفا کوئری خود را بنویسید</label>
                    <textarea class="form-control" name="review" rows="8" id="deletequery" dir="ltr"></textarea>
                </div>
            </fieldset>
            <div class="row">
                <div class="col-md-12">
                    <a href="javascript:void(0);" class="btn btn-danger btn-block" onclick="DeleteAttLayerByQuery();">
                        <i class="fa fa-eye"></i>
                        حذف بر اساس کوئری
                    </a>
                    <a href="javascript:void(0);" onclick="DeleteAttLayer('@id');" class="btn btn-danger btn-block">حذف همه اطلاعات</a>
                   
                </div>
            </div>
        </div>

    </form>





</article>
<script type="text/javascript">
    function DeleteAttLayerByQuery() {
        var idLayer = @id;
        var deletequery = $("#deletequery").val();
        if (idLayer) {
            Swal.fire({
                title: 'تأیید حذف',
                html: "<b class='samanFont'>آیا مطمئن به حذف محتویات لایه انتخاب شده هستید؟</b>",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545', // Bootstrap 5 danger color
                cancelButtonColor: '#6c757d', // Bootstrap 5 secondary color
                confirmButtonText: 'حذف',
                cancelButtonText: 'لغو',
                reverseButtons: true,
                customClass: {
                    popup: 'samanFont',
                    title: 'samanFont',
                    htmlContainer: 'samanFont',
                    confirmButton: 'samanFont btn btn-danger',
                    cancelButton: 'samanFont btn btn-secondary'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        type: 'POST',
                        url: "../database/_AttLayer_Del",
                        data: {
                            id: idLayer, query: deletequery,
                            "__RequestVerificationToken": $("input[name='__RequestVerificationToken']").last().val()
                        },
                        dataType: 'json',
                        success: function (data) {
                            if (data.success)
                                $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "green", iconSmall: "fa fa-thumbs-up bounce animated", timeout: 4000 });
                            else
                                $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
                        },
                        error: function (data) {
                            $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
                        }
                    });
                }
            });
        }
        else {
            $.smallBox({ title: "<b class='samanFont'>لطفا یک فیلد را انتخاب نمایید</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
        }
    }

    function DeleteAttLayer(idLayer) {

        if (idLayer) {
            Swal.fire({
                title: 'تأیید حذف',
                html: "<b class='samanFont'>آیا مطمئن به حذف محتویات لایه انتخاب شده هستید؟</b>",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545', // Bootstrap 5 danger color
                cancelButtonColor: '#6c757d', // Bootstrap 5 secondary color
                confirmButtonText: 'حذف',
                cancelButtonText: 'لغو',
                reverseButtons: true,
                customClass: {
                    popup: 'samanFont',
                    title: 'samanFont',
                    htmlContainer: 'samanFont',
                    confirmButton: 'samanFont btn btn-danger',
                    cancelButton: 'samanFont btn btn-secondary'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        type: 'POST',
                        url: "../database/_AttLayer_Del",
                        data: {
                            id: idLayer,
                            "__RequestVerificationToken": $("input[name='__RequestVerificationToken']").last().val()
                        },
                        dataType: 'json',
                        success: function (data) {
                            if (data.success)
                                $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "green", iconSmall: "fa fa-thumbs-up bounce animated", timeout: 4000 });
                            else
                                $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
                        },
                        error: function (data) {
                            $.smallBox({ title: "<b class='samanFont'>" + data.responseText + "</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
                        }
                    });
                }
            });
        }
        else {
            $.smallBox({ title: "<b class='samanFont'>لطفا یک فیلد را انتخاب نمایید</b>", content: "<i class='fa fa-clock-o'></i> <i class='samanFont'>چند ثانیه قبل</i>", color: "red", iconSmall: "fa fa-thumbs-down bounce animated", timeout: 4000 });
        }
    }

</script>

