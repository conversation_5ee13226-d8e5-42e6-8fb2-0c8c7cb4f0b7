!function(e){"function"==typeof define&&define.amd?define(["exports"],e):"object"==typeof exports&&"string"!=typeof exports.nodeName?e(exports):e({})}(function(e){var a,i={time:{month:["Styczeń","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON>wiec<PERSON>ń","Maj","Czerwiec","Lipiec","Sierpień","Wrzesień","Październik","Listopad","Grudzień"],monthAbbr:["<PERSON><PERSON>","<PERSON><PERSON>","Mar","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>e","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>ru"],dayOfWeek:["Niedzie<PERSON>","Poniedziałek","Wtorek","Środa","Czwartek","Piątek","Sobota"],dayOfWeekAbbr:["<PERSON>e","<PERSON>n","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","Sob"]},legend:{selector:{all:"<PERSON><PERSON><PERSON><PERSON><PERSON>",inverse:"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}},toolbox:{brush:{title:{rect:"Zaznaczenie prostokątne",polygon:"Zaznaczanie lasso",lineX:"Zaznaczenie poziome",lineY:"Zaznaczenie pionowe",keep:"Zachowaj zaznaczenie",clear:"Wyczyść zaznaczenie"}},dataView:{title:"Widok danych",lang:["Widok danych","Zamknij","Odśwież"]},dataZoom:{title:{zoom:"Przybliżenie",back:"Resetuj przybliżenie"}},magicType:{title:{line:"Przełącz na wykres liniowy",bar:"Przełącz na wykres słupkowy",stack:"Przełącz na wykres słupkowy skumulowany",tiled:"Przełącz na kafelki"}},restore:{title:"Przywróć"},saveAsImage:{title:"Zapisz jako obrazek",lang:["Kliknij prawym klawiszem myszy aby zapisać"]}},series:{typeNames:{pie:"Wykres kołowy",bar:"Wykres słupkowy",line:"Wykres liniowy",scatter:"Wykres punktowy",effectScatter:"Wykres punktowy z efektem falowania",radar:"Wykres radarowy",tree:"Drzewo",treemap:"Mapa drzewa",boxplot:"Wykres pudełkowy",candlestick:"Wykres świecowy",k:"Wykres linii K",heatmap:"Mapa ciepła",map:"Mapa",parallel:"Wykres współrzędnych równoległych",lines:"Diagram linii",graph:"Graf relacji",sankey:"Wykres Sankeya",funnel:"Wykres lejkowy",gauge:"Wykres zegarowy",pictorialBar:"Wykres słupkowy obrazkowy",themeRiver:"Wykres rzeki tematycznej",sunburst:"Wykres hierarchiczny słonecznikowy"}},aria:{general:{withTitle:'To jest wykres dotyczący "{title}"',withoutTitle:"To jest wykres"},series:{single:{prefix:"",withName:" typu {seriesType} nazwana {seriesName}.",withoutName:" typu {seriesType}."},multiple:{prefix:". Składający się z {seriesCount} serii danych.",withName:" Seria danych {seriesId} jest serią typu {seriesType} przedstawiającą {seriesName}.",withoutName:" Seria danych {seriesId} jest serią typu {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"Dane są następujące: ",partialData:"Pierwszych {displayCnt} elementów to: ",withName:"dane dla {name} to {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}};for(a in i)i.hasOwnProperty(a)&&(e[a]=i[a])});