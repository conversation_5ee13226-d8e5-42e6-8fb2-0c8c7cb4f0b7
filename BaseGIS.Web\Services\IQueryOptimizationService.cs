using System.Data;
using System.Text.RegularExpressions;

namespace BaseGIS.Web.Services
{
    /// <summary>
    /// سرویس بهینه‌سازی کوئری‌ها
    /// </summary>
    public interface IQueryOptimizationService
    {
        /// <summary>
        /// بهینه‌سازی SQL Query
        /// </summary>
        string OptimizeQuery(string sql);

        /// <summary>
        /// اعتبارسنجی SQL Query
        /// </summary>
        bool ValidateQuery(string sql);

        /// <summary>
        /// تجزیه و تحلیل عملکرد Query
        /// </summary>
        Task<QueryPerformanceInfo> AnalyzeQueryPerformanceAsync(string sql);

        /// <summary>
        /// پیشنهاد بهینه‌سازی برای Query
        /// </summary>
        List<QueryOptimizationSuggestion> GetOptimizationSuggestions(string sql);

        /// <summary>
        /// اضافه کردن Index های پیشنهادی
        /// </summary>
        List<string> SuggestIndexes(string sql);
    }

    /// <summary>
    /// پیاده‌سازی سرویس بهینه‌سازی کوئری
    /// </summary>
    public class QueryOptimizationService : IQueryOptimizationService
    {
        private readonly ILogger<QueryOptimizationService> _logger;
        private readonly IConfiguration _configuration;

        // Regex patterns for SQL analysis
        private static readonly Regex SelectPattern = new(@"SELECT\s+(.+?)\s+FROM", RegexOptions.IgnoreCase | RegexOptions.Compiled);
        private static readonly Regex WherePattern = new(@"WHERE\s+(.+?)(?:\s+ORDER\s+BY|\s+GROUP\s+BY|\s+HAVING|$)", RegexOptions.IgnoreCase | RegexOptions.Compiled);
        private static readonly Regex JoinPattern = new(@"(INNER|LEFT|RIGHT|FULL)\s+JOIN\s+(\w+)", RegexOptions.IgnoreCase | RegexOptions.Compiled);
        private static readonly Regex OrderByPattern = new(@"ORDER\s+BY\s+(.+?)(?:\s+LIMIT|\s+OFFSET|$)", RegexOptions.IgnoreCase | RegexOptions.Compiled);

        public QueryOptimizationService(ILogger<QueryOptimizationService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// بهینه‌سازی SQL Query
        /// </summary>
        public string OptimizeQuery(string sql)
        {
            if (string.IsNullOrWhiteSpace(sql))
                return sql;

            try
            {
                var optimizedSql = sql;

                // Remove unnecessary whitespace
                optimizedSql = Regex.Replace(optimizedSql, @"\s+", " ", RegexOptions.Compiled);

                // Optimize SELECT clause
                optimizedSql = OptimizeSelectClause(optimizedSql);

                // Optimize WHERE clause
                optimizedSql = OptimizeWhereClause(optimizedSql);

                // Optimize JOIN operations
                optimizedSql = OptimizeJoins(optimizedSql);

                // Add query hints if needed
                optimizedSql = AddQueryHints(optimizedSql);

                return optimizedSql.Trim();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error optimizing query: {Sql}", sql);
                return sql;
            }
        }

        /// <summary>
        /// اعتبارسنجی SQL Query
        /// </summary>
        public bool ValidateQuery(string sql)
        {
            if (string.IsNullOrWhiteSpace(sql))
                return false;

            try
            {
                // Check for dangerous operations
                var dangerousPatterns = new[]
                {
                    @"DROP\s+TABLE",
                    @"DELETE\s+FROM",
                    @"TRUNCATE\s+TABLE",
                    @"ALTER\s+TABLE",
                    @"CREATE\s+TABLE",
                    @"INSERT\s+INTO",
                    @"UPDATE\s+.+\s+SET",
                    @"EXEC\s*\(",
                    @"EXECUTE\s*\(",
                    @"xp_cmdshell",
                    @"sp_executesql"
                };

                foreach (var pattern in dangerousPatterns)
                {
                    if (Regex.IsMatch(sql, pattern, RegexOptions.IgnoreCase))
                    {
                        _logger.LogWarning("Dangerous SQL pattern detected: {Pattern} in query: {Sql}", pattern, sql);
                        return false;
                    }
                }

                // Check for basic SQL structure
                if (!Regex.IsMatch(sql, @"SELECT\s+.+\s+FROM\s+", RegexOptions.IgnoreCase))
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating query: {Sql}", sql);
                return false;
            }
        }

        /// <summary>
        /// تجزیه و تحلیل عملکرد Query
        /// </summary>
        public async Task<QueryPerformanceInfo> AnalyzeQueryPerformanceAsync(string sql)
        {
            var performanceInfo = new QueryPerformanceInfo { OriginalQuery = sql };

            try
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                // Simulate query execution time analysis
                await Task.Delay(10); // Simulate analysis time

                stopwatch.Stop();

                performanceInfo.AnalysisTime = stopwatch.Elapsed;
                performanceInfo.EstimatedExecutionTime = EstimateExecutionTime(sql);
                performanceInfo.ComplexityScore = CalculateComplexityScore(sql);
                performanceInfo.Suggestions = GetOptimizationSuggestions(sql);

                return performanceInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error analyzing query performance: {Sql}", sql);
                performanceInfo.Error = ex.Message;
                return performanceInfo;
            }
        }

        /// <summary>
        /// پیشنهاد بهینه‌سازی برای Query
        /// </summary>
        public List<QueryOptimizationSuggestion> GetOptimizationSuggestions(string sql)
        {
            var suggestions = new List<QueryOptimizationSuggestion>();

            try
            {
                // Check for SELECT *
                if (sql.Contains("SELECT *", StringComparison.OrdinalIgnoreCase))
                {
                    suggestions.Add(new QueryOptimizationSuggestion
                    {
                        Type = SuggestionType.Performance,
                        Priority = SuggestionPriority.High,
                        Description = "استفاده از SELECT * باعث کاهش عملکرد می‌شود. فقط ستون‌های مورد نیاز را انتخاب کنید.",
                        Impact = "کاهش ترافیک شبکه و بهبود عملکرد"
                    });
                }

                // Check for missing WHERE clause
                if (!Regex.IsMatch(sql, @"WHERE\s+", RegexOptions.IgnoreCase))
                {
                    suggestions.Add(new QueryOptimizationSuggestion
                    {
                        Type = SuggestionType.Performance,
                        Priority = SuggestionPriority.Medium,
                        Description = "عدم وجود شرط WHERE ممکن است باعث بارگذاری داده‌های اضافی شود.",
                        Impact = "کاهش حجم داده‌های بازگشتی"
                    });
                }

                // Check for ORDER BY without LIMIT
                if (Regex.IsMatch(sql, @"ORDER\s+BY", RegexOptions.IgnoreCase) && 
                    !Regex.IsMatch(sql, @"LIMIT\s+\d+", RegexOptions.IgnoreCase))
                {
                    suggestions.Add(new QueryOptimizationSuggestion
                    {
                        Type = SuggestionType.Performance,
                        Priority = SuggestionPriority.Medium,
                        Description = "استفاده از ORDER BY بدون LIMIT ممکن است باعث کاهش عملکرد شود.",
                        Impact = "محدود کردن تعداد نتایج"
                    });
                }

                // Check for complex JOINs
                var joinMatches = JoinPattern.Matches(sql);
                if (joinMatches.Count > 3)
                {
                    suggestions.Add(new QueryOptimizationSuggestion
                    {
                        Type = SuggestionType.Structure,
                        Priority = SuggestionPriority.High,
                        Description = "تعداد زیاد JOIN ها ممکن است باعث کاهش عملکرد شود. در نظر گیری denormalization یا استفاده از view ها.",
                        Impact = "بهبود زمان اجرای کوئری"
                    });
                }

                // Check for functions in WHERE clause
                if (Regex.IsMatch(sql, @"WHERE\s+.*(UPPER|LOWER|SUBSTRING|CONVERT)\s*\(", RegexOptions.IgnoreCase))
                {
                    suggestions.Add(new QueryOptimizationSuggestion
                    {
                        Type = SuggestionType.Index,
                        Priority = SuggestionPriority.Medium,
                        Description = "استفاده از توابع در شرط WHERE مانع از استفاده از Index می‌شود.",
                        Impact = "بهبود استفاده از Index ها"
                    });
                }

                return suggestions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting optimization suggestions for query: {Sql}", sql);
                return suggestions;
            }
        }

        /// <summary>
        /// پیشنهاد Index های مورد نیاز
        /// </summary>
        public List<string> SuggestIndexes(string sql)
        {
            var indexes = new List<string>();

            try
            {
                // Extract WHERE conditions
                var whereMatch = WherePattern.Match(sql);
                if (whereMatch.Success)
                {
                    var whereClause = whereMatch.Groups[1].Value;
                    
                    // Extract column names from WHERE clause
                    var columnPattern = new Regex(@"(\w+)\s*[=<>!]", RegexOptions.IgnoreCase);
                    var columnMatches = columnPattern.Matches(whereClause);
                    
                    foreach (Match match in columnMatches)
                    {
                        var columnName = match.Groups[1].Value;
                        indexes.Add($"CREATE INDEX IX_{columnName} ON TableName ({columnName})");
                    }
                }

                // Extract ORDER BY columns
                var orderByMatch = OrderByPattern.Match(sql);
                if (orderByMatch.Success)
                {
                    var orderByClause = orderByMatch.Groups[1].Value;
                    var columns = orderByClause.Split(',').Select(c => c.Trim().Split(' ')[0]);
                    
                    foreach (var column in columns)
                    {
                        indexes.Add($"CREATE INDEX IX_{column}_OrderBy ON TableName ({column})");
                    }
                }

                return indexes.Distinct().ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error suggesting indexes for query: {Sql}", sql);
                return indexes;
            }
        }

        #region Private Methods

        private string OptimizeSelectClause(string sql)
        {
            // Replace SELECT * with specific columns if possible
            // This would require schema information in a real implementation
            return sql;
        }

        private string OptimizeWhereClause(string sql)
        {
            // Optimize WHERE conditions
            // Move most selective conditions first
            return sql;
        }

        private string OptimizeJoins(string sql)
        {
            // Optimize JOIN order based on table sizes and selectivity
            return sql;
        }

        private string AddQueryHints(string sql)
        {
            // Add SQL Server query hints if beneficial
            return sql;
        }

        private TimeSpan EstimateExecutionTime(string sql)
        {
            // Simple estimation based on query complexity
            var complexity = CalculateComplexityScore(sql);
            return TimeSpan.FromMilliseconds(complexity * 10);
        }

        private int CalculateComplexityScore(string sql)
        {
            var score = 0;

            // Base score
            score += 1;

            // Add score for JOINs
            score += JoinPattern.Matches(sql).Count * 2;

            // Add score for subqueries
            score += Regex.Matches(sql, @"\(\s*SELECT", RegexOptions.IgnoreCase).Count * 3;

            // Add score for functions
            score += Regex.Matches(sql, @"\w+\s*\(", RegexOptions.IgnoreCase).Count;

            // Add score for ORDER BY
            if (Regex.IsMatch(sql, @"ORDER\s+BY", RegexOptions.IgnoreCase))
                score += 2;

            // Add score for GROUP BY
            if (Regex.IsMatch(sql, @"GROUP\s+BY", RegexOptions.IgnoreCase))
                score += 2;

            return score;
        }

        #endregion
    }

    /// <summary>
    /// اطلاعات عملکرد کوئری
    /// </summary>
    public class QueryPerformanceInfo
    {
        public string OriginalQuery { get; set; } = string.Empty;
        public TimeSpan AnalysisTime { get; set; }
        public TimeSpan EstimatedExecutionTime { get; set; }
        public int ComplexityScore { get; set; }
        public List<QueryOptimizationSuggestion> Suggestions { get; set; } = new();
        public string? Error { get; set; }
    }

    /// <summary>
    /// پیشنهاد بهینه‌سازی کوئری
    /// </summary>
    public class QueryOptimizationSuggestion
    {
        public SuggestionType Type { get; set; }
        public SuggestionPriority Priority { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Impact { get; set; } = string.Empty;
    }

    /// <summary>
    /// نوع پیشنهاد بهینه‌سازی
    /// </summary>
    public enum SuggestionType
    {
        Performance,
        Structure,
        Index,
        Security
    }

    /// <summary>
    /// اولویت پیشنهاد
    /// </summary>
    public enum SuggestionPriority
    {
        Low,
        Medium,
        High,
        Critical
    }
}
