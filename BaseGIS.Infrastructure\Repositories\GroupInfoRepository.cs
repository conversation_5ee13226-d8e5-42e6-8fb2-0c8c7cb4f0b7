﻿using BaseGIS.Core.Entities;
using BaseGIS.Core.Interfaces;
using BaseGIS.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace BaseGIS.Infrastructure.Repositories
{
    public class GroupInfoRepository : IGroupInfoRepository
    {
        private readonly ApplicationDbContext _context;

        public GroupInfoRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public IEnumerable<GroupInfo> GetGroupInfos()
        {
            return _context.GroupInfos.ToList();
        }

        public GroupInfo GetGroupInfoByID(int id)
        {
            return _context.GroupInfos.Find(id);
        }

        public void InsertGroupInfo(GroupInfo groupInfo)
        {
            _context.GroupInfos.Add(groupInfo);
        }

        public void DeleteGroupInfo(int groupInfoID)
        {
            GroupInfo groupInfo = _context.GroupInfos.Find(groupInfoID);
            _context.GroupInfos.Remove(groupInfo);
        }

        public void UpdateGroupInfo(GroupInfo groupInfo)
        {
            _context.Entry(groupInfo).State = EntityState.Modified;
        }

        public void Save()
        {
            _context.SaveChanges();
        }

        private bool disposed = false;

        protected virtual void Dispose(bool disposing)
        {
            if (!this.disposed)
            {
                if (disposing)
                {
                    _context.Dispose();
                }
            }
            this.disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
