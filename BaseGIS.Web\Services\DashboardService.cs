
using BaseGIS.Core.Entities;
using BaseGIS.Infrastructure.Persistence;
using BaseGIS.Web.Helper;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace BaseGIS.Web.Services
{
    /// <summary>
    /// پیاده‌سازی سرویس مدیریت داشبورد
    /// </summary>
    public class DashboardService : IDashboardService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<DashboardService> _logger;
        private readonly IConfiguration _configuration;

        public DashboardService(
            ApplicationDbContext context,
            ILogger<DashboardService> logger,
            IConfiguration configuration)
        {
            _context = context;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// دریافت داشبورد بر اساس ID
        /// </summary>
        public async Task<Dashboard?> GetDashboardAsync(int id, string userId, bool isAdmin)
        {
            try
            {
                var query = _context.Dashboards.AsQueryable();

                if (!isAdmin)
                {
                    query = query.Where(d => d.IsPublic.Value || d.User.Id == userId);
                }

                return await query.FirstOrDefaultAsync(d => d.ID == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard {DashboardId} for user {UserId}", id, userId);
                return null;
            }
        }

        /// <summary>
        /// دریافت لیست داشبوردها
        /// </summary>
        public async Task<IEnumerable<Dashboard>> GetDashboardsAsync(string userId, bool isAdmin)
        {
            try
            {
                var query = _context.Dashboards.AsQueryable();

                if (!isAdmin)
                {
                    query = query.Where(d => d.IsPublic.Value || d.User.Id == userId);
                }

                return await query.OrderBy(d => d.Name).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboards for user {UserId}", userId);
                return Enumerable.Empty<Dashboard>();
            }
        }

        /// <summary>
        /// ایجاد یا بروزرسانی داشبورد
        /// </summary>
        public async Task<ServiceResult<int>> SaveDashboardAsync(DashboardViewModel model, string userId, bool isAdmin)
        {
            try
            {
                Dashboard dashboard;

                if (model.ID == 0)
                {
                    // Create new dashboard
                    dashboard = new Dashboard
                    {
                        Name = model.Name,
                        IsPublic = model.IsPublic,
                        CssClass = model.CssClass,
                        FilterType = model.FilterType,
                        User = await _context.Users.FindAsync(userId),
                        Date = DateTime.Now
                    };

                    _context.Dashboards.Add(dashboard);
                }
                else
                {
                    // Update existing dashboard
                    dashboard = await _context.Dashboards.FindAsync(model.ID);
                    if (dashboard == null)
                    {
                        return ServiceResult<int>.Failure("داشبورد یافت نشد");
                    }

                    // Check access
                    if (!isAdmin && dashboard.User.Id != userId)
                    {
                        return ServiceResult<int>.Failure("دسترسی غیرمجاز");
                    }

                    dashboard.Name = model.Name;
                    dashboard.IsPublic = model.IsPublic;
                    dashboard.CssClass = model.CssClass;
                    dashboard.FilterType = model.FilterType;
                }

                await _context.SaveChangesAsync();
                return ServiceResult<int>.Success(dashboard.ID, "داشبورد با موفقیت ذخیره شد");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving dashboard for user {UserId}", userId);
                return ServiceResult<int>.Failure("خطا در ذخیره داشبورد");
            }
        }

        /// <summary>
        /// حذف داشبورد
        /// </summary>
        public async Task<ServiceResult> DeleteDashboardAsync(int id, string userId, bool isAdmin)
        {
            try
            {
                var dashboard = await _context.Dashboards.FindAsync(id);
                if (dashboard == null)
                {
                    return ServiceResult.Failure("داشبورد یافت نشد");
                }

                // Check access
                if (!isAdmin && dashboard.User.Id != userId)
                {
                    return ServiceResult.Failure("دسترسی غیرمجاز");
                }

                _context.Dashboards.Remove(dashboard);
                await _context.SaveChangesAsync();

                return ServiceResult.Success("داشبورد با موفقیت حذف شد");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting dashboard {DashboardId} for user {UserId}", id, userId);
                return ServiceResult.Failure("خطا در حذف داشبورد");
            }
        }

        /// <summary>
        /// دریافت پنل‌های داشبورد
        /// </summary>
        public async Task<ServiceResult<IEnumerable<PanelGroupViewModel>>> GetDashboardPanelsAsync(
            int dashboardId,
            string? parentPanelId,
            Dictionary<string, string> inputs,
            string userId,
            bool isAdmin)
        {
            try
            {
                // Validate dashboard access
                if (!await ValidateDashboardAccessAsync(dashboardId, userId, isAdmin))
                {
                    return ServiceResult<IEnumerable<PanelGroupViewModel>>.Failure("دسترسی غیرمجاز");
                }

                var query = _context.DashboardPanels
                    .Include(p => p.SQL)
                    .Where(p => p.Dashboard.ID == dashboardId);

                // Filter by parent panel
                if (string.IsNullOrWhiteSpace(parentPanelId))
                {
                    query = query.Where(p => p.Panels == null);
                }
                else
                {
                    query = query.Where(p => ("," + p.Panels + ",").Contains("," + parentPanelId + ","));
                }

                var panels = await query
                    .OrderBy(p => p.Group)
                    .ThenBy(p => p.Order)
                    .ToListAsync();

                // Group panels
                var groupedPanels = panels
                    .GroupBy(p => p.Group)
                    .Select(g => new PanelGroupViewModel
                    {
                        Group = g.Key,
                        GroupName = $"گروه {g.Key ?? 0}",
                        Items = g.Select(p => MapToPanelItemViewModel(p)).ToList()
                    })
                    .ToList();

                return ServiceResult<IEnumerable<PanelGroupViewModel>>.Success(groupedPanels);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard panels for dashboard {DashboardId}", dashboardId);
                return ServiceResult<IEnumerable<PanelGroupViewModel>>.Failure("خطا در بارگذاری پنل‌ها");
            }
        }

        /// <summary>
        /// دریافت پنل بر اساس ID
        /// </summary>
        public async Task<ServiceResult<DashboardPanel?>> GetPanelAsync(int id, string userId, bool isAdmin)
        {
            try
            {
                var panel = await _context.DashboardPanels
                    .Include(p => p.SQL)
                    .Include(p => p.Dashboard)
                    .FirstOrDefaultAsync(p => p.ID == id);

                if (panel == null)
                {
                    return ServiceResult<DashboardPanel?>.Failure("پنل یافت نشد");
                }

                // Check access
                if (!await ValidateDashboardAccessAsync(panel.Dashboard.ID, userId, isAdmin))
                {
                    return ServiceResult<DashboardPanel?>.Failure("دسترسی غیرمجاز");
                }

                return ServiceResult<DashboardPanel?>.Success(panel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting panel {PanelId} for user {UserId}", id, userId);
                return ServiceResult<DashboardPanel?>.Failure("خطا در بارگذاری پنل");
            }
        }

        /// <summary>
        /// ایجاد یا بروزرسانی پنل
        /// </summary>
        public async Task<ServiceResult<int>> SavePanelAsync(PanelViewModel model, string userId, bool isAdmin)
        {
            try
            {
                // Validate dashboard access
                if (!await ValidateDashboardAccessAsync(model.DashboardID, userId, isAdmin))
                {
                    return ServiceResult<int>.Failure("دسترسی غیرمجاز");
                }

                DashboardPanel panel;

                if (model.ID == 0)
                {
                    // Create new panel
                    panel = new DashboardPanel
                    {
                        Dashboard = await _context.Dashboards.FindAsync(model.DashboardID),
                        User = await _context.Users.FindAsync(userId),
                        Date = DateTime.Now
                    };

                    _context.DashboardPanels.Add(panel);
                }
                else
                {
                    // Update existing panel
                    panel = await _context.DashboardPanels.FindAsync(model.ID);
                    if (panel == null)
                    {
                        return ServiceResult<int>.Failure("پنل یافت نشد");
                    }
                }

                // Map properties
                MapPanelViewModelToEntity(model, panel);

                await _context.SaveChangesAsync();
                return ServiceResult<int>.Success(panel.ID, "پنل با موفقیت ذخیره شد");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving panel for user {UserId}", userId);
                return ServiceResult<int>.Failure("خطا در ذخیره پنل");
            }
        }

        /// <summary>
        /// حذف پنل
        /// </summary>
        public async Task<ServiceResult> DeletePanelAsync(int id, string userId, bool isAdmin)
        {
            try
            {
                var panel = await _context.DashboardPanels
                    .Include(p => p.Dashboard)
                    .FirstOrDefaultAsync(p => p.ID == id);

                if (panel == null)
                {
                    return ServiceResult.Failure("پنل یافت نشد");
                }

                // Check access
                if (!await ValidateDashboardAccessAsync(panel.Dashboard.ID, userId, isAdmin))
                {
                    return ServiceResult.Failure("دسترسی غیرمجاز");
                }

                _context.DashboardPanels.Remove(panel);
                await _context.SaveChangesAsync();

                return ServiceResult.Success("پنل با موفقیت حذف شد");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting panel {PanelId} for user {UserId}", id, userId);
                return ServiceResult.Failure("خطا در حذف پنل");
            }
        }

        /// <summary>
        /// دریافت داده‌های نمودار
        /// </summary>
        public async Task<ServiceResult<ChartDataViewModel>> GetChartDataAsync(
            int panelId,
            Dictionary<string, string> inputs,
            string? whereClause,
            string userId)
        {
            try
            {
                var panel = await _context.DashboardPanels
                    .Include(p => p.SQL)
                    .Include(p => p.Dashboard)
                    .FirstOrDefaultAsync(p => p.ID == panelId);

                if (panel == null)
                {
                    return ServiceResult<ChartDataViewModel>.Failure("پنل یافت نشد");
                }

                // Get chart parameters
                var chartParams = await GetUserChartParametersAsync(panel.Dashboard.ID, panelId, userId);

                // Execute SQL and get data
                var data = await ExecutePanelSqlAsync(panel, inputs, whereClause, userId);

                var result = new ChartDataViewModel
                {
                    Type = chartParams?.Type ?? "line",
                    TypeTwoBar = chartParams?.TypeTwoBar ?? "t1",
                    Label = chartParams?.Check ?? false,
                    IsList = DetermineIfList(data),
                    SizeH = panel.SizeH ?? 1,
                    Items = JsonConvert.SerializeObject(data),
                    Color = chartParams?.Color?.ToString(),
                    ColorTitle = chartParams?.ColorTitle,
                    ShowHelp = chartParams?.ShowHelp ?? false,
                    Grid = chartParams?.Grid,
                    Angle = chartParams?.Angle ?? 0,
                    AngleX = chartParams?.AngleX ?? 0,
                    ShowDecimal = chartParams?.ShowDecimal ?? true,
                    SizeLabel = chartParams?.SizeLabel.ToString()
                };

                return ServiceResult<ChartDataViewModel>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chart data for panel {PanelId}", panelId);
                return ServiceResult<ChartDataViewModel>.Failure("خطا در بارگذاری داده‌های نمودار");
            }
        }

        /// <summary>
        /// دریافت داده‌های جدول
        /// </summary>
        public async Task<ServiceResult<TableDataViewModel>> GetTableDataAsync(
            int panelId,
            Dictionary<string, string> inputs,
            string? whereClause,
            string userId)
        {
            try
            {
                var panel = await _context.DashboardPanels
                    .Include(p => p.SQL)
                    .Include(p => p.Dashboard)
                    .FirstOrDefaultAsync(p => p.ID == panelId);

                if (panel == null)
                {
                    return ServiceResult<TableDataViewModel>.Failure("پنل یافت نشد");
                }

                // Execute SQL and get data
                var data = await ExecutePanelSqlAsync(panel, inputs, whereClause, userId);

                var result = new TableDataViewModel
                {
                    Data = data,
                    Columns = GenerateTableColumns(data),
                    HasClickableRows = !string.IsNullOrEmpty(panel.Panels),
                    ClickableFields = panel.FilterByField
                };

                return ServiceResult<TableDataViewModel>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting table data for panel {PanelId}", panelId);
                return ServiceResult<TableDataViewModel>.Failure("خطا در بارگذاری داده‌های جدول");
            }
        }

        /// <summary>
        /// ذخیره تنظیمات نمودار کاربر
        /// </summary>
        public async Task<ServiceResult> SaveUserChartParametersAsync(
            int dashboardId,
            int panelId,
            string parameters,
            string userId)
        {
            try
            {
                var existing = await _context.DashboardUserParameters
                    .FirstOrDefaultAsync(p => p.DashboardID == dashboardId &&
                                            p.PanelID == panelId &&
                                            p.UserId == userId);

                if (existing != null)
                {
                    existing.Parameters = parameters;
                }
                else
                {
                    _context.DashboardUserParameters.Add(new DashboardUserParameter
                    {
                        DashboardID = dashboardId,
                        PanelID = panelId,
                        UserId = userId,
                        Parameters = parameters
                    });
                }

                await _context.SaveChangesAsync();
                return ServiceResult.Success("تنظیمات ذخیره شد");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving user chart parameters for user {UserId}", userId);
                return ServiceResult.Failure("خطا در ذخیره تنظیمات");
            }
        }

        /// <summary>
        /// دریافت تنظیمات نمودار کاربر
        /// </summary>
        public async Task<ChartParameter?> GetUserChartParametersAsync(int dashboardId, int panelId, string userId)
        {
            try
            {
                var userParams = await _context.DashboardUserParameters
                    .FirstOrDefaultAsync(p => p.DashboardID == dashboardId &&
                                            p.PanelID == panelId &&
                                            p.UserId == userId);

                if (userParams != null && !string.IsNullOrEmpty(userParams.Parameters))
                {
                    return JsonConvert.DeserializeObject<ChartParameter>(userParams.Parameters);
                }

                // Get default parameters from panel
                var panel = await _context.DashboardPanels.FindAsync(panelId);
                if (panel != null && !string.IsNullOrEmpty(panel.Parameters))
                {
                    return JsonConvert.DeserializeObject<ChartParameter>(panel.Parameters);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user chart parameters for user {UserId}", userId);
                return null;
            }
        }

        /// <summary>
        /// اعتبارسنجی دسترسی به داشبورد
        /// </summary>
        public async Task<bool> ValidateDashboardAccessAsync(int dashboardId, string userId, bool isAdmin)
        {
            if (isAdmin) return true;

            try
            {
                return await _context.Dashboards
                    .AnyAsync(d => d.ID == dashboardId && (d.IsPublic.Value || d.User.Id == userId));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating dashboard access for user {UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// اعتبارسنجی دسترسی به پنل
        /// </summary>
        public async Task<bool> ValidatePanelAccessAsync(int panelId, string userId, bool isAdmin)
        {
            if (isAdmin) return true;

            try
            {
                return await _context.DashboardPanels
                    .Include(p => p.Dashboard)
                    .AnyAsync(p => p.ID == panelId &&
                                 (p.Dashboard.IsPublic.Value || p.Dashboard.User.Id == userId));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating panel access for user {UserId}", userId);
                return false;
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// تبدیل Entity به ViewModel
        /// </summary>
        private PanelItemViewModel MapToPanelItemViewModel(DashboardPanel panel)
        {
            return new PanelItemViewModel
            {
                ID = panel.ID,
                Name = panel.Name,
                Name2 = panel.Name2,
                Size = panel.Size,
                SizeH = panel.SizeH,
                Order = panel.Order,
                HTML = panel.HTML,
                Parameters = panel.Parameters,
                Group = panel.Group,
                Padding = panel.Padding,
                Panels = panel.Panels,
                SQLID = panel.SQL?.ID,
                FilterByField = panel.FilterByField,
                InputMapping = panel.InputMapping,
                Type1 = panel.Type1,
                Type2 = panel.Type2,
                HideHeader = panel.HideHeader,
                BackgroundColor = panel.BackgroundColor,
                URL = panel.URL,
                MessagePanel = panel.MessagePanel,
                MessageColor = panel.MessageColor
            };
        }

        /// <summary>
        /// تبدیل ViewModel به Entity
        /// </summary>
        private void MapPanelViewModelToEntity(PanelViewModel model, DashboardPanel panel)
        {
            panel.Name = model.Name;
            panel.Name2 = model.Name2;
            panel.Type1 = model.Type1;
            panel.Type2 = model.Type2;
            panel.Size = model.Size;
            panel.SizeH = model.SizeH;
            panel.Order = model.Order;
            panel.HTML = model.HTML;
            panel.Parameters = model.Parameters;
            panel.Group = model.Group;
            panel.Padding = model.Padding;
            panel.Panels = model.Panels;
            panel.FilterByField = model.FilterByField;
            panel.InputMapping = model.InputMapping;
            panel.BackgroundColor = model.BackgroundColor;
            panel.HideHeader = model.HideHeader;
            panel.URL = model.URL;
            panel.MessagePanel = model.MessagePanel;
            panel.MessageColor = model.MessageColor;

            // Set SQL if provided
            if (model.SQLID.HasValue)
            {
                panel.SQL = _context.DashboardSQLs.Find(model.SQLID.Value);
            }
        }

        /// <summary>
        /// اجرای SQL پنل
        /// </summary>
        private async Task<List<Dictionary<string, object>>> ExecutePanelSqlAsync(
            DashboardPanel panel,
            Dictionary<string, string> inputs,
            string? whereClause,
            string userId)
        {
            if (panel.SQL == null || string.IsNullOrEmpty(panel.SQL.SQL))
            {
                return new List<Dictionary<string, object>>();
            }

            try
            {
                // Get user group for SQL conversion
                var user = await _context.Users.FindAsync(userId);
                var userGroup = user?.UserGroup ?? "0";

                // Convert SQL with parameters
                var sql = ConvertSQL(panel.SQL.SQL, userGroup, inputs, whereClause);

                // Execute SQL using DBManagement
                var dbm = new DBManagement(_configuration);
                var dataTable = dbm.SelectTableSQL(sql);

                // Convert DataTable to List<Dictionary>
                return ConvertDataTableToDictionary(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing SQL for panel {PanelId}", panel.ID);
                return new List<Dictionary<string, object>>();
            }
        }

        /// <summary>
        /// تبدیل SQL با پارامترها
        /// </summary>
        private string ConvertSQL(string sql, string userGroup, Dictionary<string, string> inputs, string? whereClause)
        {
            // Replace user group
            sql = sql.Replace("@UG", userGroup);

            // Replace input parameters
            for (int i = 0; i < 8; i++)
            {
                var key = i == 0 ? "input" : $"input{i}";
                var value = inputs.ContainsKey(key) ? inputs[key] : "";
                sql = sql.Replace($"@{key.ToUpper()}", value);
            }

            // Add where clause if provided
            if (!string.IsNullOrEmpty(whereClause))
            {
                sql += $" AND ({whereClause})";
            }

            return sql;
        }

        /// <summary>
        /// تبدیل DataTable به Dictionary
        /// </summary>
        private List<Dictionary<string, object>> ConvertDataTableToDictionary(System.Data.DataTable dataTable)
        {
            var result = new List<Dictionary<string, object>>();

            foreach (System.Data.DataRow row in dataTable.Rows)
            {
                var dict = new Dictionary<string, object>();
                foreach (System.Data.DataColumn column in dataTable.Columns)
                {
                    dict[column.ColumnName] = row[column] ?? DBNull.Value;
                }
                result.Add(dict);
            }

            return result;
        }

        /// <summary>
        /// تشخیص نوع داده (List یا Single)
        /// </summary>
        private bool DetermineIfList(List<Dictionary<string, object>> data)
        {
            if (data.Count == 0) return false;

            // If more than one row, it's a list
            if (data.Count > 1) return true;

            // If single row has multiple value columns, it's a list
            var firstRow = data[0];
            var valueColumns = firstRow.Keys.Where(k => k.ToLower().StartsWith("value")).Count();

            return valueColumns > 1;
        }

        /// <summary>
        /// تولید ستون‌های جدول
        /// </summary>
        private List<TableColumnViewModel> GenerateTableColumns(List<Dictionary<string, object>> data)
        {
            if (data.Count == 0) return new List<TableColumnViewModel>();

            var columns = new List<TableColumnViewModel>();
            var firstRow = data[0];

            foreach (var key in firstRow.Keys)
            {
                columns.Add(new TableColumnViewModel
                {
                    Field = key,
                    Title = GetColumnTitle(key),
                    Type = DetermineColumnType(firstRow[key]),
                    Sortable = true,
                    Searchable = true
                });
            }

            return columns;
        }

        /// <summary>
        /// دریافت عنوان ستون
        /// </summary>
        private string GetColumnTitle(string fieldName)
        {
            // Convert common field names to Persian
            return fieldName.ToLower() switch
            {
                "id" => "شناسه",
                "name" => "نام",
                "title" => "عنوان",
                "value" => "مقدار",
                "count" => "تعداد",
                "date" => "تاریخ",
                "description" => "توضیحات",
                _ => fieldName
            };
        }

        /// <summary>
        /// تشخیص نوع ستون
        /// </summary>
        private string DetermineColumnType(object value)
        {
            if (value == null || value == DBNull.Value) return "text";

            return value.GetType().Name.ToLower() switch
            {
                "int32" or "int64" or "decimal" or "double" or "float" => "number",
                "datetime" => "date",
                "boolean" => "boolean",
                _ => "text"
            };
        }

        #endregion
    }
}
