/**
 * مدیریت نقشه‌های پایه
 * BaseMap Manager Module
 */
class BaseMapManager {
    constructor(map, options = {}) {
        this.map = map;
        this.options = {
            position: 'bottomright',
            controlSize: 70,
            panelId: 'panel-satImages',
            ...options
        };
        
        this.baseMaps = [];
        this.imageUrls = [];
        this.titles = [];
        this.currentIndex = 0;
        this.currentBaseMap = null;
        this.isInitialized = false;
        
        this.init();
    }

    /**
     * مقداردهی اولیه
     */
    async init() {
        try {
            await this.loadBaseMaps();
            this.createControl();
            this.createPanel();
            this.bindEvents();
            this.setDefaultBaseMap();
            this.isInitialized = true;
            
            console.log('BaseMapManager initialized successfully');
        } catch (error) {
            console.error('Error initializing BaseMapManager:', error);
        }
    }

    /**
     * بارگذاری نقشه‌های پایه از سرور
     */
    async loadBaseMaps() {
        try {
            const response = await fetch('/GeoMap/GetBaseMaps');
            const result = await response.json();
            
            if (result.success) {
                this.processBaseMaps(result.data);
            } else {
                console.error('Failed to load base maps:', result.message);
                this.createFallbackBaseMaps();
            }
        } catch (error) {
            console.error('Error loading base maps:', error);
            this.createFallbackBaseMaps();
        }
    }

    /**
     * پردازش داده‌های نقشه‌های پایه
     */
    processBaseMaps(baseMapsData) {
        // اضافه کردن نقشه پایه پیش‌فرض ESRI
        this.baseMaps.push(
            L.esri.dynamicMapLayer({ 
                url: window.URLBASE + 'Rest/Services/base/MapServer', 
                opacity: 1 
            })
        );
        this.imageUrls.push(window.URLBASE + "Content/mapService/img/basemap/0.png");
        this.titles.push("نقشه پایه پیش‌فرض");

        // اضافه کردن نقشه‌های پایه کاربر
        baseMapsData.forEach(baseMap => {
            let layer;
            
            if (baseMap.isWms && baseMap.wmsLayers) {
                // WMS Layer
                layer = L.tileLayer.wms(this.sanitizeUrl(baseMap.url), {
                    layers: baseMap.wmsLayers,
                    styles: baseMap.styles || '',
                    transparent: true,
                    maxZoom: 20,
                    attribution: baseMap.title
                });
            } else {
                // Tile Layer
                layer = L.tileLayer(this.sanitizeUrl(baseMap.url), {
                    maxZoom: 20,
                    attribution: baseMap.title
                });
            }
            
            this.baseMaps.push(layer);
            this.imageUrls.push(baseMap.image || '/Content/mapService/img/basemap/default.png');
            this.titles.push(baseMap.title + (baseMap.description ? ' - ' + baseMap.description : ''));
        });
    }

    /**
     * ایجاد نقشه‌های پایه پیش‌فرض در صورت خطا
     */
    createFallbackBaseMaps() {
        // OpenStreetMap as fallback
        this.baseMaps.push(
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 19,
                attribution: '© OpenStreetMap contributors'
            })
        );
        this.imageUrls.push('/Content/mapService/img/basemap/osm.png');
        this.titles.push('OpenStreetMap');
    }

    /**
     * ایجاد کنترل نقشه پایه
     */
    createControl() {
        const controlHtml = `
            <a href="javascript:void(0)" id="basemapbtn" class="basemap-control">
                <img src="${this.imageUrls[0]}" 
                     class="img-thumbnail" 
                     id="basemapimage" 
                     style="width:${this.options.controlSize}px; height:${this.options.controlSize}px; object-fit:cover;">
            </a>
        `;

        this.control = L.control.custom({
            position: this.options.position,
            content: controlHtml,
            style: { 
                margin: '5px', 
                padding: '0px',
                borderRadius: '4px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
            }
        }).addTo(this.map);
    }

    /**
     * ایجاد پنل انتخاب نقشه پایه
     */
    createPanel() {
        const panelHtml = `
            <div id="${this.options.panelId}" 
                 class="basemap-panel" 
                 style="position:absolute; bottom:4px; right:${this.options.controlSize + 15}px; 
                        display:none; z-index:1001; background-color:#fff; 
                        border:1px solid #ccc; border-radius:4px; padding:10px;
                        box-shadow: 0 4px 8px rgba(0,0,0,0.2); max-width:400px;">
                <div class="basemap-grid" style="display:flex; flex-wrap:wrap; gap:10px;">
                    ${this.createPanelItems()}
                </div>
            </div>
        `;

        // حذف پنل قبلی در صورت وجود
        const existingPanel = document.getElementById(this.options.panelId);
        if (existingPanel) {
            existingPanel.remove();
        }

        document.body.insertAdjacentHTML('beforeend', panelHtml);
    }

    /**
     * ایجاد آیتم‌های پنل
     */
    createPanelItems() {
        return this.baseMaps.map((baseMap, index) => `
            <a href="javascript:void(0)" 
               class="basemap-item" 
               data-index="${index}"
               style="display:flex; flex-direction:column; align-items:center; 
                      text-decoration:none; padding:5px; border-radius:4px;
                      transition: background-color 0.2s;">
                <img src="${this.imageUrls[index]}" 
                     class="img-thumbnail" 
                     style="width:80px; height:60px; object-fit:cover; margin-bottom:5px;">
                <span class="basemap-title" 
                      style="font-size:12px; text-align:center; color:#333; max-width:80px; 
                             overflow:hidden; text-overflow:ellipsis; white-space:nowrap;">
                    ${this.getTitleForIndex(index)}
                </span>
            </a>
        `).join('');
    }

    /**
     * دریافت عنوان برای ایندکس
     */
    getTitleForIndex(index) {
        if (index === 0) return 'پیش فرض';
        return this.titles[index] || `نقشه ${index}`;
    }

    /**
     * اتصال رویدادها
     */
    bindEvents() {
        // رویداد کلیک روی کنترل
        document.addEventListener('click', (e) => {
            if (e.target.closest('#basemapbtn')) {
                this.togglePanel();
            }
        });

        // رویداد hover روی کنترل
        const basemapImage = document.getElementById('basemapimage');
        if (basemapImage) {
            basemapImage.addEventListener('mouseenter', () => {
                this.showPanel();
            });
        }

        // رویداد mouseleave روی پنل
        const panel = document.getElementById(this.options.panelId);
        if (panel) {
            panel.addEventListener('mouseleave', () => {
                this.hidePanel();
            });
        }

        // رویداد کلیک روی آیتم‌های پنل
        document.addEventListener('click', (e) => {
            const item = e.target.closest('.basemap-item');
            if (item) {
                const index = parseInt(item.dataset.index);
                this.changeBaseMap(index);
                this.hidePanel();
            }
        });

        // بستن پنل با کلیک خارج از آن
        document.addEventListener('click', (e) => {
            if (!e.target.closest(`#${this.options.panelId}`) && 
                !e.target.closest('#basemapbtn')) {
                this.hidePanel();
            }
        });
    }

    /**
     * تغییر نقشه پایه
     */
    changeBaseMap(index) {
        if (index < 0 || index >= this.baseMaps.length) {
            console.warn('Invalid base map index:', index);
            return;
        }

        try {
            // حذف نقشه پایه قبلی
            if (this.currentBaseMap) {
                this.map.removeLayer(this.currentBaseMap);
            }

            // اضافه کردن نقشه پایه جدید
            this.currentIndex = index;
            this.currentBaseMap = this.baseMaps[index];
            this.currentBaseMap.addTo(this.map);
            this.currentBaseMap.bringToBack();

            // بروزرسانی تصویر کنترل
            const basemapImage = document.getElementById('basemapimage');
            if (basemapImage) {
                basemapImage.src = this.imageUrls[index];
                basemapImage.title = this.titles[index];
            }

            console.log('Base map changed to index:', index);
        } catch (error) {
            console.error('Error changing base map:', error);
        }
    }

    /**
     * تنظیم نقشه پایه پیش‌فرض
     */
    setDefaultBaseMap() {
        this.changeBaseMap(0);
    }

    /**
     * نمایش پنل
     */
    showPanel() {
        const panel = document.getElementById(this.options.panelId);
        if (panel) {
            panel.style.display = 'block';
        }
    }

    /**
     * مخفی کردن پنل
     */
    hidePanel() {
        const panel = document.getElementById(this.options.panelId);
        if (panel) {
            panel.style.display = 'none';
        }
    }

    /**
     * تغییر وضعیت نمایش پنل
     */
    togglePanel() {
        const panel = document.getElementById(this.options.panelId);
        if (panel) {
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }
    }

    /**
     * پاکسازی URL
     */
    sanitizeUrl(url) {
        if (!url) return '';
        return url.replace(/&amp;/g, '&');
    }

    /**
     * دریافت نقشه پایه فعلی
     */
    getCurrentBaseMap() {
        return {
            index: this.currentIndex,
            layer: this.currentBaseMap,
            title: this.titles[this.currentIndex]
        };
    }

    /**
     * نابودی مدیریت نقشه‌های پایه
     */
    destroy() {
        if (this.currentBaseMap) {
            this.map.removeLayer(this.currentBaseMap);
        }
        
        if (this.control) {
            this.map.removeControl(this.control);
        }

        const panel = document.getElementById(this.options.panelId);
        if (panel) {
            panel.remove();
        }

        this.isInitialized = false;
    }
}

// Export for use in other modules
window.BaseMapManager = BaseMapManager;
