/**
 * مدیریت نقشه‌های پایه
 * BaseMap Manager Module
 */
import { BaseComponent } from '../components/base-component.js';

class BaseMapManagerComponent extends BaseComponent {
    getDefaultOptions() {
        return {
            ...super.getDefaultOptions(),
            map: null,
            position: 'bottomright',
            controlSize: 70,
            panelId: 'panel-satImages'
        };
    }

    init() {
        if (!this.options.map) {
            throw new Error('Leaflet map instance is required for BaseMapManagerComponent');
        }
        this.map = this.options.map;
        this.baseMaps = [];
        this.imageUrls = [];
        this.titles = [];
        this.currentIndex = 0;
        this.currentBaseMap = null;
        this.isInitialized = false;
        this.loadBaseMaps();
    }

    async loadBaseMaps() {
        try {
            const response = await fetch('/GeoMap/GetBaseMaps');
            const result = await response.json();
            if (result.success) {
                this.processBaseMaps(result.data);
            } else {
                console.error('Failed to load base maps:', result.message);
                this.createFallbackBaseMaps();
            }
        } catch (error) {
            console.error('Error loading base maps:', error);
            this.createFallbackBaseMaps();
        }
        this.render();
    }

    processBaseMaps(baseMapsData) {
        this.baseMaps.push(
            L.esri.dynamicMapLayer({ 
                url: window.URLBASE + 'Rest/Services/base/MapServer', 
                opacity: 1 
            })
        );
        this.imageUrls.push(window.URLBASE + "Content/mapService/img/basemap/0.png");
        this.titles.push("نقشه پایه پیش‌فرض");
        baseMapsData.forEach(baseMap => {
            let layer;
            if (baseMap.isWms && baseMap.wmsLayers) {
                layer = L.tileLayer.wms(this.sanitizeUrl(baseMap.url), {
                    layers: baseMap.wmsLayers,
                    styles: baseMap.styles || '',
                    transparent: true,
                    maxZoom: 20,
                    attribution: baseMap.title
                });
            } else {
                layer = L.tileLayer(this.sanitizeUrl(baseMap.url), {
                    maxZoom: 20,
                    attribution: baseMap.title
                });
            }
            this.baseMaps.push(layer);
            this.imageUrls.push(baseMap.image || '/Content/mapService/img/basemap/default.png');
            this.titles.push(baseMap.title + (baseMap.description ? ' - ' + baseMap.description : ''));
        });
    }

    createFallbackBaseMaps() {
        this.baseMaps.push(
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 19,
                attribution: '© OpenStreetMap contributors'
            })
        );
        this.imageUrls.push('/Content/mapService/img/basemap/osm.png');
        this.titles.push('OpenStreetMap');
    }

    render() {
        this.createControl();
        this.createPanel();
        this.bindEvents();
        this.setDefaultBaseMap();
        this.isInitialized = true;
    }

    createControl() {
        const controlHtml = `
            <a href="javascript:void(0)" id="basemapbtn" class="basemap-control">
                <img src="${this.imageUrls[0]}" 
                     class="img-thumbnail" 
                     id="basemapimage" 
                     style="width:${this.options.controlSize}px; height:${this.options.controlSize}px; object-fit:cover;">
            </a>
        `;
        this.control = L.control.custom({
            position: this.options.position,
            content: controlHtml,
            style: { 
                margin: '5px', 
                padding: '0px',
                borderRadius: '4px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
            }
        }).addTo(this.map);
    }

    createPanel() {
        const panelHtml = `
            <div id="${this.options.panelId}" 
                 class="basemap-panel" 
                 style="position:absolute; bottom:4px; right:${this.options.controlSize + 15}px; 
                        display:none; z-index:1001; background-color:#fff; 
                        border:1px solid #ccc; border-radius:4px; padding:10px;
                        box-shadow: 0 4px 8px rgba(0,0,0,0.2); max-width:400px;">
                <div class="basemap-grid" style="display:flex; flex-wrap:wrap; gap:10px;">
                    ${this.createPanelItems()}
                </div>
            </div>
        `;
        const existingPanel = document.getElementById(this.options.panelId);
        if (existingPanel) {
            existingPanel.remove();
        }
        document.body.insertAdjacentHTML('beforeend', panelHtml);
    }

    createPanelItems() {
        return this.baseMaps.map((baseMap, index) => `
            <a href="javascript:void(0)" 
               class="basemap-item" 
               data-index="${index}"
               style="display:flex; flex-direction:column; align-items:center; 
                      text-decoration:none; padding:5px; border-radius:4px;
                      transition: background-color 0.2s;">
                <img src="${this.imageUrls[index]}" 
                     class="img-thumbnail" 
                     style="width:80px; height:60px; object-fit:cover; margin-bottom:5px;">
                <span class="basemap-title" 
                      style="font-size:12px; text-align:center; color:#333; max-width:80px; 
                             overflow:hidden; text-overflow:ellipsis; white-space:nowrap;">
                    ${this.getTitleForIndex(index)}
                </span>
            </a>
        `).join('');
    }

    getTitleForIndex(index) {
        if (index === 0) return 'پیش فرض';
        return this.titles[index] || `نقشه ${index}`;
    }

    bindEvents() {
        document.addEventListener('click', (e) => {
            if (e.target.closest('#basemapbtn')) {
                this.togglePanel();
            }
        });
        const basemapImage = document.getElementById('basemapimage');
        if (basemapImage) {
            basemapImage.addEventListener('mouseenter', () => {
                this.showPanel();
            });
        }
        const panel = document.getElementById(this.options.panelId);
        if (panel) {
            panel.addEventListener('mouseleave', () => {
                this.hidePanel();
            });
        }
        document.addEventListener('click', (e) => {
            const item = e.target.closest('.basemap-item');
            if (item) {
                const index = parseInt(item.dataset.index);
                this.changeBaseMap(index);
                this.hidePanel();
            }
        });
        document.addEventListener('click', (e) => {
            if (!e.target.closest(`#${this.options.panelId}`) && 
                !e.target.closest('#basemapbtn')) {
                this.hidePanel();
            }
        });
    }

    changeBaseMap(index) {
        if (index < 0 || index >= this.baseMaps.length) {
            console.warn('Invalid base map index:', index);
            return;
        }
        try {
            if (this.currentBaseMap) {
                this.map.removeLayer(this.currentBaseMap);
            }
            this.currentIndex = index;
            this.currentBaseMap = this.baseMaps[index];
            this.currentBaseMap.addTo(this.map);
            this.currentBaseMap.bringToBack();
            const basemapImage = document.getElementById('basemapimage');
            if (basemapImage) {
                basemapImage.src = this.imageUrls[index];
                basemapImage.title = this.titles[index];
            }
            this.trigger('baseMapChanged', { index, layer: this.currentBaseMap });
        } catch (error) {
            console.error('Error changing base map:', error);
        }
    }

    setDefaultBaseMap() {
        this.changeBaseMap(0);
    }

    showPanel() {
        const panel = document.getElementById(this.options.panelId);
        if (panel) {
            panel.style.display = 'block';
        }
    }

    hidePanel() {
        const panel = document.getElementById(this.options.panelId);
        if (panel) {
            panel.style.display = 'none';
        }
    }

    togglePanel() {
        const panel = document.getElementById(this.options.panelId);
        if (panel) {
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }
    }

    sanitizeUrl(url) {
        if (!url) return '';
        return url.replace(/&amp;/g, '&');
    }

    getCurrentBaseMap() {
        return {
            index: this.currentIndex,
            layer: this.currentBaseMap,
            title: this.titles[this.currentIndex]
        };
    }

    destroy() {
        if (this.currentBaseMap) {
            this.map.removeLayer(this.currentBaseMap);
        }
        if (this.control) {
            this.map.removeControl(this.control);
        }
        const panel = document.getElementById(this.options.panelId);
        if (panel) {
            panel.remove();
        }
        this.isInitialized = false;
        super.destroy();
    }
}

window.ComponentFactory.register('base-map-manager', BaseMapManagerComponent);
