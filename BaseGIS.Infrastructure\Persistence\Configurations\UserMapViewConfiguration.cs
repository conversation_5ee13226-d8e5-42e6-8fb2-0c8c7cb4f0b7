using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BaseGIS.Core.Entities;

namespace BaseGIS.Infrastructure.Persistence.Configurations
{
    public class UserMapViewConfiguration : IEntityTypeConfiguration<UserMapView>
    {
        public void Configure(EntityTypeBuilder<UserMapView> builder)
        {
            builder.HasKey(x => x.Id);
            builder.Property(x => x.UserId).IsRequired();
            builder.Property(x => x.Name).IsRequired().HasMaxLength(100);
            builder.Property(x => x.Json).IsRequired();
            builder.Property(x => x.Created).IsRequired();
        }
    }
} 