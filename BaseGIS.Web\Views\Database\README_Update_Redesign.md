# بازطراحی صفحات Update بر اساس طراحی Insert

این سند تغییرات انجام شده برای بازطراحی کامل صفحات Update بر اساس طراحی موفق صفحات Insert را شرح می‌دهد.

## 🎯 هدف

1. **یکسان‌سازی تجربه کاربری** بین Insert و Update
2. **پشتیبانی از فرمت‌های مختلف** (Shapefile + Excel)
3. **بهبود فرآیند آپلود** و تناظر فیلدها
4. **استفاده از کتابخانه‌های مدرن** (SmartWizard, Select2, Dropzone)

## 📋 تغییرات انجام شده

### 1. **Update.cshtml - صفحه اصلی**

#### **قبل:**
```html
<div id="content" class="samanFont">
    <div class="row">
        <div class="col-12 col-lg-4">
            @await Html.PartialAsync("_Update_List")
        </div>
        <div class="col-12 col-lg-8">
            @if (!string.IsNullOrEmpty(id))
            {
                @await Html.PartialAsync("_Update_Wizard")
            }
        </div>
    </div>
</div>
```

#### **بعد:**
```html
<!--begin::App Content Header-->
<div class="app-content-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6"><h5 class="mb-0">بروزرسانی یکباره اطلاعات</h5></div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-end">
                    <li class="breadcrumb-item"><a href="#">مدیریت داده</a></li>
                    <li class="breadcrumb-item active">بروزرسانی یکباره اطلاعات</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<!--begin::App Content-->
<div class="app-content">
    <!-- محتوای صفحه با AJAX loading -->
</div>
```

### 2. **_Update_List.cshtml - لیست لایه‌ها**

#### **ویژگی‌های جدید:**
- ✅ استفاده از Select2 برای انتخاب لایه
- ✅ آیکون‌های مناسب برای انواع لایه
- ✅ گروه‌بندی بهتر لایه‌ها
- ✅ طراحی Bootstrap 5

```html
<select class="form-select select2" id="GList" data-placeholder="یک جدول انتخاب کنید">
    <option value="0"></option>
    @foreach (var group in GroupList)
    {
        <optgroup label="@(group?.AliasName ?? "بدون گروه‌بندی")">
            @foreach (var item in list)
            {
                <option value="@item.Id" data-icon="@symbol">@item.AliasName</option>
            }
        </optgroup>
    }
</select>
```

### 3. **_Update_Wizard.cshtml - ویزارد بروزرسانی**

#### **مراحل جدید:**
1. **آپلود فایل** - پشتیبانی از Shapefile و Excel
2. **تناظر فیلدها** - انتخاب تناظر بین فیلدهای لایه و فایل
3. **تکمیل** - نمایش نتیجه و پیام‌های موفقیت/خطا

#### **پشتیبانی از فرمت‌های مختلف:**
```html
<div class="alert alert-info">
    <strong>فرمت‌های پشتیبانی شده:</strong>
    <ul>
        <li><strong>Shapefile:</strong> فایل ZIP حاوی .shp, .dbf, .shx</li>
        <li><strong>Excel:</strong> فایل‌های .xls و .xlsx</li>
    </ul>
</div>
```

### 4. **UpdateWizardViewModel - مدل داده**

```csharp
public class UpdateWizardViewModel
{
    public string Id { get; set; }
    public string PathFile { get; set; }
    public List<FieldInfo> FieldInfos { get; set; } = new List<FieldInfo>();
    public List<string> ShapeFileColumns { get; set; } = new List<string>();
    public List<string> ExcelColumns { get; set; } = new List<string>();
    public string FileType { get; set; } // "shapefile" or "excel"
}
```

### 5. **کنترلر - متدهای جدید**

#### **_Update_Wizard (بهبود یافته):**
```csharp
public IActionResult _Update_Wizard(string id, string path)
{
    var model = new UpdateWizardViewModel
    {
        Id = id,
        PathFile = path,
        ShapeFileColumns = new List<string>(),
        ExcelColumns = new List<string>()
    };

    // خواندن فیلدهای لایه
    if (!string.IsNullOrEmpty(id) && int.TryParse(id, out int ids))
    {
        model.FieldInfos = _dbContext.FieldInfos.Where(a => a.TableInfo.Id == ids).ToList();
    }

    // خواندن فایل آپلود شده
    if (!string.IsNullOrEmpty(path))
    {
        // پردازش Shapefile یا Excel
    }

    return PartialView(model);
}
```

#### **_FileUploadUpdate (جدید):**
```csharp
[HttpPost]
public async Task<IActionResult> _FileUploadUpdate(string id, IFormFile file)
{
    var allowedExtensions = new[] { ".zip", ".xls", ".xlsx" };
    var fileExtension = Path.GetExtension(file.FileName).ToLower();
    
    if (!allowedExtensions.Contains(fileExtension))
    {
        return Json(new { success = false, responseText = "نوع فایل معتبر نیست." });
    }

    // پردازش آپلود و استخراج
    // ...
    
    return Json(new { success = true, responseText = folderName });
}
```

## 🚀 ویژگی‌های جدید

### **1. پشتیبانی از Excel:**
- ✅ آپلود فایل‌های .xls و .xlsx
- ✅ خواندن ستون‌های Excel
- ✅ تناظر فیلدها با ستون‌های Excel

### **2. ویزارد پیشرفته:**
- ✅ SmartWizard با طراحی مدرن
- ✅ مراحل واضح و قابل فهم
- ✅ اعتبارسنجی در هر مرحله

### **3. تجربه کاربری بهتر:**
- ✅ پیام‌های Toast برای اطلاع‌رسانی
- ✅ Loading spinners
- ✅ طراحی responsive

### **4. مدیریت خطا:**
- ✅ پیام‌های خطای واضح
- ✅ Logging مناسب
- ✅ Rollback در صورت خطا

## 📊 مقایسه قبل و بعد

| ویژگی | قبل | بعد |
|--------|-----|-----|
| **فرمت‌های پشتیبانی** | فقط Shapefile | Shapefile + Excel |
| **ویزارد** | Bootstrap tabs | SmartWizard |
| **انتخاب لایه** | Dropdown ساده | Select2 با آیکون |
| **آپلود فایل** | Dropzone ساده | Dropzone پیشرفته |
| **تناظر فیلدها** | جدول ساده | جدول با طراحی مدرن |
| **پیام‌ها** | Alert ساده | Toast notifications |
| **طراحی** | قدیمی | Bootstrap 5 مدرن |

## 🔧 نحوه استفاده

### **1. انتخاب لایه:**
```javascript
function selectLayer() {
    var selectedId = $('#GList').val();
    if (selectedId && selectedId !== '0') {
        window.onItemSelected(selectedId);
    }
}
```

### **2. آپلود فایل:**
```javascript
$("#mydropzone").dropzone({
    url: "/Database/_FileUploadUpdate?id=@id",
    acceptedFiles: ".zip,.xls,.xlsx",
    success: function (file, data) {
        // پردازش موفقیت‌آمیز
    }
});
```

### **3. ارسال داده‌ها:**
```javascript
function publishData() {
    const obj = {
        id: '@id',
        path: '@pathFile',
        FieldSource: fieldSource,
        FieldDest: fieldMapping,
        __RequestVerificationToken: $("input[name='__RequestVerificationToken']").val()
    };

    $.ajax({
        url: '/Database/UpdateData',
        method: 'POST',
        data: obj,
        success: function (data) {
            // نمایش نتیجه
        }
    });
}
```

## 📝 نکات مهم

1. **سازگاری:** کاملاً سازگار با فرآیند Insert موجود
2. **امنیت:** استفاده از AntiForgeryToken
3. **عملکرد:** بهینه‌سازی برای فایل‌های بزرگ
4. **خطایابی:** Logging کامل برای تمام عملیات

## 🎉 نتیجه

بازطراحی صفحات Update باعث شده:
- ✅ تجربه کاربری یکسان با Insert
- ✅ پشتیبانی از فرمت‌های بیشتر
- ✅ فرآیند ساده‌تر و قابل فهم‌تر
- ✅ کد تمیزتر و قابل نگهداری‌تر

این تغییرات باعث بهبود قابل توجه در کیفیت و کارایی فرآیند بروزرسانی داده‌ها شده است.
