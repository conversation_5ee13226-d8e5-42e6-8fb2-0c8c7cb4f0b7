/*!
 * OverlayScrollbars
 * Version: 2.11.0
 *
 * Copyright (c) <PERSON> | KingSora.
 * https://github.com/KingSora
 *
 * Released under the MIT license.
 */
var OverlayScrollbarsGlobal=function(t){"use strict";const e=(t,e)=>{const{o:n,i:r,u:o}=t;let s,i=n;const l=(t,e)=>{const n=i,l=t,c=e||(r?!r(n,l):n!==l);return(c||o)&&(i=l,s=n),[i,c,s]};return[e?t=>l(e(i,s),t):l,t=>[i,!!t,s]]},n="undefined"!=typeof window&&"undefined"!=typeof HTMLElement&&window.document?window:{},r=Math.max,o=Math.min,s=Math.round,i=Math.abs,l=Math.sign,c=n.cancelAnimationFrame,a=n.requestAnimationFrame,u=n.setTimeout,d=n.clearTimeout,p=t=>void 0!==n[t]?n[t]:void 0,y=p("MutationObserver"),h=p("IntersectionObserver"),f=p("ResizeObserver"),v=p("ScrollTimeline"),x=t=>void 0===t,b=t=>null===t,g=t=>"number"==typeof t,w=t=>"string"==typeof t,m=t=>"boolean"==typeof t,$=t=>"function"==typeof t,S=t=>Array.isArray(t),O=t=>"object"==typeof t&&!S(t)&&!b(t),M=t=>{const e=!!t&&t.length,n=g(e)&&e>-1&&e%1==0;return!(!(S(t)||!$(t)&&n)||e>0&&O(t)&&!(e-1 in t))},C=t=>!!t&&t.constructor===Object,D=t=>t instanceof HTMLElement,T=t=>t instanceof Element,k=()=>performance.now(),P=(t,e,n,o,s)=>{let i=0;const l=k(),u=r(0,n),d=n=>{const c=k(),p=c-l>=u,y=n?1:1-(r(0,l+u-c)/u||0),h=(e-t)*($(s)?s(y,y*u,0,1,u):y)+t,f=p||1===y;o&&o(h,y,f),i=f?0:a((()=>d()))};return d(),t=>{c(i),t&&d(t)}};function H(t,e){if(M(t))for(let n=0;n<t.length&&!1!==e(t[n],n,t);n++);else t&&H(Object.keys(t),(n=>e(t[n],n,t)));return t}const A=(t,e)=>t.indexOf(e)>=0,L=(t,e)=>t.concat(e),E=(t,e,n)=>(!w(e)&&M(e)?Array.prototype.push.apply(t,e):t.push(e),t),R=t=>Array.from(t||[]),U=t=>S(t)?t:!w(t)&&M(t)?R(t):[t],z=t=>!!t&&!t.length,I=t=>R(new Set(t)),_=(t,e,n)=>{H(t,(t=>!t||t.apply(void 0,e||[]))),!n&&(t.length=0)},j="paddingTop",N="paddingRight",V="paddingLeft",B="paddingBottom",F="marginLeft",Z="marginRight",W="marginBottom",q="overflowX",Y="overflowY",X="width",G="height",J="visible",K="hidden",Q="scroll",tt=(t,e,n,r)=>{if(t&&e){let r=!0;return H(n,(n=>{t[n]!==e[n]&&(r=!1)})),r}return!1},et=(t,e)=>tt(t,e,["w","h"]),nt=(t,e)=>tt(t,e,["x","y"]),rt=(t,e)=>tt(t,e,["t","r","b","l"]),ot=()=>{},st=(t,...e)=>t.bind(0,...e),it=t=>{let e;const n=t?u:a,r=t?d:c;return[o=>{r(e),e=n((()=>o()),$(t)?t():t)},()=>r(e)]},lt=(t,e)=>{const{_:n,v:r,p:o,S:s}=e||{};let i,l,p,y,h=ot;const f=function(e){h(),d(i),y=i=l=void 0,h=ot,t.apply(this,e)},v=t=>s&&l?s(l,t):t,x=()=>{h!==ot&&f(v(p)||p)},b=function(){const t=R(arguments),e=$(n)?n():n;if(g(e)&&e>=0){const n=$(r)?r():r,s=g(n)&&n>=0,b=e>0?u:a,w=e>0?d:c,m=v(t)||t,S=f.bind(0,m);let O;h(),o&&!y?(S(),y=!0,O=b((()=>y=void 0),e)):(O=b(S,e),s&&!i&&(i=u(x,n))),h=()=>w(O),l=p=m}else f(t)};return b.m=x,b},ct=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),at=t=>t?Object.keys(t):[],ut=(t,e,n,r,o,s,i)=>{const l=[e,n,r,o,s,i];return"object"==typeof t&&!b(t)||$(t)||(t={}),H(l,(e=>{H(e,((n,r)=>{const o=e[r];if(t===o)return!0;const s=S(o);if(o&&C(o)){const e=t[r];let n=e;s&&!S(e)?n=[]:s||C(e)||(n={}),t[r]=ut(n,o)}else t[r]=s?o.slice():o}))})),t},dt=(t,e)=>H(ut({},t),((t,e,n)=>{void 0===t?delete n[e]:t&&C(t)&&(n[e]=dt(t))})),pt=t=>!at(t).length,yt=(t,e,n)=>r(t,o(e,n)),ht=t=>I((S(t)?t:(t||"").split(" ")).filter((t=>t))),ft=(t,e)=>t&&t.getAttribute(e),vt=(t,e)=>t&&t.hasAttribute(e),xt=(t,e,n)=>{H(ht(e),(e=>{t&&t.setAttribute(e,String(n||""))}))},bt=(t,e)=>{H(ht(e),(e=>t&&t.removeAttribute(e)))},gt=(t,e)=>{const n=ht(ft(t,e)),r=st(xt,t,e),o=(t,e)=>{const r=new Set(n);return H(ht(t),(t=>{r[e](t)})),R(r).join(" ")};return{O:t=>r(o(t,"delete")),$:t=>r(o(t,"add")),C:t=>{const e=ht(t);return e.reduce(((t,e)=>t&&n.includes(e)),e.length>0)}}},wt=(t,e,n)=>(gt(t,e).O(n),st(mt,t,e,n)),mt=(t,e,n)=>(gt(t,e).$(n),st(wt,t,e,n)),$t=(t,e,n,r)=>(r?mt:wt)(t,e,n),St=(t,e,n)=>gt(t,e).C(n),Ot=t=>gt(t,"class"),Mt=(t,e)=>{Ot(t).O(e)},Ct=(t,e)=>(Ot(t).$(e),st(Mt,t,e)),Dt=(t,e)=>{const n=e?T(e)&&e:document;return n?R(n.querySelectorAll(t)):[]},Tt=(t,e)=>T(t)&&t.matches(e),kt=t=>Tt(t,"body"),Pt=t=>t?R(t.childNodes):[],Ht=t=>t&&t.parentElement,At=(t,e)=>T(t)&&t.closest(e),Lt=t=>document.activeElement,Et=t=>{H(U(t),(t=>{const e=Ht(t);t&&e&&e.removeChild(t)}))},Rt=(t,e)=>st(Et,t&&e&&H(U(e),(e=>{e&&t.appendChild(e)})));let Ut;const zt=t=>{const e=document.createElement("div");return xt(e,"class",t),e},It=t=>{const e=zt(),n=Ut,r=t.trim();return e.innerHTML=n?n.createHTML(r):r,H(Pt(e),(t=>Et(t)))},_t=(t,e)=>t.getPropertyValue(e)||t[e]||"",jt=t=>{const e=t||0;return isFinite(e)?e:0},Nt=t=>jt(parseFloat(t||"")),Vt=t=>Math.round(1e4*t)/1e4,Bt=t=>`${Vt(jt(t))}px`;function Ft(t,e){t&&e&&H(e,((e,n)=>{try{const r=t.style,o=b(e)||m(e)?"":g(e)?Bt(e):e;0===n.indexOf("--")?r.setProperty(n,o):r[n]=o}catch(r){}}))}function Zt(t,e,r){const o=w(e);let s=o?"":{};if(t){const i=n.getComputedStyle(t,r)||t.style;s=o?_t(i,e):R(e).reduce(((t,e)=>(t[e]=_t(i,e),t)),s)}return s}const Wt=(t,e,n)=>{const r=e?`${e}-`:"",o=n?`-${n}`:"",s=`${r}top${o}`,i=`${r}right${o}`,l=`${r}bottom${o}`,c=`${r}left${o}`,a=Zt(t,[s,i,l,c]);return{t:Nt(a[s]),r:Nt(a[i]),b:Nt(a[l]),l:Nt(a[c])}},qt=(t,e)=>`translate${O(t)?`(${t.x},${t.y})`:`${e?"X":"Y"}(${t})`}`,Yt={w:0,h:0},Xt=(t,e)=>e?{w:e[`${t}Width`],h:e[`${t}Height`]}:Yt,Gt=t=>Xt("inner",t||n),Jt=st(Xt,"offset"),Kt=st(Xt,"client"),Qt=st(Xt,"scroll"),te=t=>{const e=parseFloat(Zt(t,X))||0,n=parseFloat(Zt(t,G))||0;return{w:e-s(e),h:n-s(n)}},ee=t=>t.getBoundingClientRect(),ne=t=>!(!t||!t[G]&&!t[X]),re=(t,e)=>{const n=ne(t);return!ne(e)&&n},oe=(t,e,n,r)=>{H(ht(e),(e=>{t&&t.removeEventListener(e,n,r)}))},se=(t,e,n,r)=>{var o;const s=null==(o=r&&r.H)||o,i=r&&r.I||!1,l=r&&r.A||!1,c={passive:s,capture:i};return st(_,ht(e).map((e=>{const r=l?o=>{oe(t,e,r,i),n&&n(o)}:n;return t&&t.addEventListener(e,r,c),st(oe,t,e,r,i)})))},ie=t=>t.stopPropagation(),le=t=>t.preventDefault(),ce=t=>ie(t)||le(t),ae=(t,e)=>{const{x:n,y:r}=g(e)?{x:e,y:e}:e||{};g(n)&&(t.scrollLeft=n),g(r)&&(t.scrollTop=r)},ue=t=>({x:t.scrollLeft,y:t.scrollTop}),de=(t,e)=>{const{D:n,M:r}=t,{w:o,h:s}=e,c=(t,e,n)=>{let r=l(t)*n,o=l(e)*n;if(r===o){const n=i(t),s=i(e);o=n>s?0:o,r=n<s?0:r}return r=r===o?0:r,[r+0,o+0]},[a,u]=c(n.x,r.x,o),[d,p]=c(n.y,r.y,s);return{D:{x:a,y:d},M:{x:u,y:p}}},pe=({D:t,M:e})=>{const n=(t,e)=>0===t&&t<=e;return{x:n(t.x,e.x),y:n(t.y,e.y)}},ye=({D:t,M:e},n)=>{const r=(t,e,n)=>yt(0,1,(t-n)/(t-e)||0);return{x:r(t.x,e.x,n.x),y:r(t.y,e.y,n.y)}},he=t=>{t&&t.focus&&t.focus({preventScroll:!0})},fe=(t,e)=>{H(U(e),t)},ve=t=>{const e=new Map,n=(t,n)=>{if(t){const r=e.get(t);fe((t=>{r&&r[t?"delete":"clear"](t)}),n)}else e.forEach((t=>{t.clear()})),e.clear()},r=(t,o)=>{if(w(t)){const r=e.get(t)||new Set;return e.set(t,r),fe((t=>{$(t)&&r.add(t)}),o),st(n,t,o)}m(o)&&o&&n();const s=at(t),i=[];return H(s,(e=>{const n=t[e];n&&E(i,r(e,n))})),st(_,i)};return r(t||{}),[r,n,(t,n)=>{H(R(e.get(t)),(t=>{n&&!z(n)?t.apply(0,n):t()}))}]},xe={},be={},ge=(t,e,n)=>at(t).map((r=>{const{static:o,instance:s}=t[r],[i,l,c]=n||[],a=n?s:o;if(a){const t=n?a(i,l,e):a(e);return(c||be)[r]=t}})),we=t=>be[t],me="__osOptionsValidationPlugin",$e=`data-overlayscrollbars`,Se="os-environment",Oe=`${Se}-scrollbar-hidden`,Me=`${$e}-initialize`,Ce="noClipping",De=`${$e}-body`,Te=$e,ke="host",Pe=`${$e}-viewport`,He=q,Ae=Y,Le="arrange",Ee="measuring",Re="scrolling",Ue="scrollbarHidden",ze="noContent",Ie=`${$e}-padding`,_e=`${$e}-content`,je="os-size-observer",Ne=`${je}-appear`,Ve=`${je}-listener`,Be=`${Ve}-scroll`,Fe=`${Ve}-item`,Ze=`${Fe}-final`,We="os-trinsic-observer",qe="os-theme-none",Ye="os-scrollbar",Xe=`${Ye}-rtl`,Ge=`${Ye}-horizontal`,Je=`${Ye}-vertical`,Ke=`${Ye}-track`,Qe=`${Ye}-handle`,tn=`${Ye}-visible`,en=`${Ye}-cornerless`,nn=`${Ye}-interaction`,rn=`${Ye}-unusable`,on=`${Ye}-auto-hide`,sn=`${on}-hidden`,ln=`${Ye}-wheel`,cn=`${Ke}-interactive`,an=`${Qe}-interactive`,un="__osSizeObserverPlugin",dn=(()=>({[un]:{static:()=>(t,e,n)=>{const r=3333333,o="scroll",s=It(`<div class="${Fe}" dir="ltr"><div class="${Fe}"><div class="${Ze}"></div></div><div class="${Fe}"><div class="${Ze}" style="width: 200%; height: 200%"></div></div></div>`),i=s[0],l=i.lastChild,u=i.firstChild,d=null==u?void 0:u.firstChild;let p,y=Jt(i),h=y,f=!1;const v=()=>{ae(u,r),ae(l,r)},x=t=>{p=0,f&&(y=h,e(!0===t))},b=t=>{h=Jt(i),f=!t||!et(h,y),t?(ie(t),f&&!p&&(c(p),p=a(x))):x(!1===t),v()},g=[Rt(t,s),se(u,o,b),se(l,o,b)];return Ct(t,Be),Ft(d,{[X]:r,[G]:r}),a(v),[n?st(b,!1):v,g]}}}))(),pn=(t,e)=>{const{T:n}=e,[r,o]=t("showNativeOverlaidScrollbars");return[r&&n.x&&n.y,o]},yn=t=>0===t.indexOf(J),hn=(t,e)=>{const n=(t,e,n,r)=>{const o=t===J?K:t.replace(`${J}-`,""),s=yn(t),i=yn(n);return e||r?s&&i?J:s?e&&r?o:e?J:K:e?o:i&&r?J:K:K},r={x:n(e.x,t.x,e.y,t.y),y:n(e.y,t.y,e.x,t.x)};return{k:r,R:{x:r.x===Q,y:r.y===Q}}},fn="__osScrollbarsHidingPlugin",vn=(()=>({[fn]:{static:()=>({V:(t,e,n,r,o)=>{const{L:s,U:i}=t,{P:l,T:c,N:a}=r,u=!s&&!l&&(c.x||c.y),[d]=pn(o,r),p=t=>{const{R:e}=t,n=l||d?0:42,r=(t,e,r)=>[e&&!l?t?n:r:0,t&&!!n],[o,s]=r(c.x,e.x,a.x),[i,u]=r(c.y,e.y,a.y);return{q:{x:o,y:i},j:{x:s,y:u}}},y=(t,{B:n},r)=>{if(!s){const o=ut({},{[Z]:0,[W]:0,[F]:0}),{q:s,j:i}=p(t),{x:l,y:c}=i,{x:a,y:u}=s,{F:d}=e,y=n?F:Z,h=n?V:N,f=d[y],v=d[W],x=d[h],b=d[B];return o[X]=`calc(100% + ${u+-1*f}px)`,o[y]=-u+f,o[W]=-a+v,r&&(o[h]=x+(c?u:0),o[B]=b+(l?a:0)),o}};return{X:p,Y:(t,r,o)=>{if(u){const{F:s}=e,{q:l,j:c}=p(t),{x:a,y:u}=c,{x:d,y}=l,{B:h}=n,f=s[h?N:V],v=s.paddingTop,x=r.w+o.w,b=r.h+o.h,g={w:y&&u?`${y+x-f}px`:"",h:d&&a?`${d+b-v}px`:""};Ft(i,{"--os-vaw":g.w,"--os-vah":g.h})}return u},W:t=>{if(u){const r=t||(()=>{const t=t=>{const e=Zt(i,t);return[e,e===Q]},[e,n]=t(q),[r,o]=t(Y);return{k:{x:e,y:r},R:{x:n,y:o}}})(),{F:o}=e,{j:s}=p(r),{x:l,y:c}=s,a={},d=t=>H(t,(t=>{a[t]=o[t]}));l&&d([W,j,B]),c&&d([F,Z,V,N]);const h=Zt(i,at(a)),f=wt(i,Pe,Le);return Ft(i,a),[()=>{Ft(i,ut({},h,y(r,n,u))),f()},r]}return[ot]},G:y}}})}}))(),xn="__osClickScrollPlugin",bn=(()=>({[xn]:{static:()=>(t,e,n,r)=>{let o=!1,s=ot;const i=133,l=222,[c,a]=it(i),u=Math.sign(e),d=n*u,p=d/2,y=t=>1-(1-t)*(1-t),h=(e,n)=>P(e,n,l,t,y),f=(n,r)=>P(n,e-d,i*r,((n,r,o)=>{t(n),o&&(s=h(n,e))})),v=P(0,d,l,((i,l,a)=>{if(t(i),a&&(r(o),!o)){const t=e-i;Math.sign(t-p)===u&&c((()=>{const r=t-d,o=Math.sign(r)===u;s=o?f(i,Math.abs(r)/n):h(i,e)}))}}),y);return t=>{o=!0,t&&v(),a(),s()}}}}))(),gn=t=>JSON.stringify(t,((t,e)=>{if($(e))throw 0;return e})),wn=(t,e)=>t?`${e}`.split(".").reduce(((t,e)=>t&&ct(t,e)?t[e]:void 0),t):void 0,mn={paddingAbsolute:!1,showNativeOverlaidScrollbars:!1,update:{elementEvents:[["img","load"]],debounce:[0,33],attributes:null,ignoreMutation:null},overflow:{x:"scroll",y:"scroll"},scrollbars:{theme:"os-theme-dark",visibility:"auto",autoHide:"never",autoHideDelay:1300,autoHideSuspend:!1,dragScroll:!0,clickScroll:!1,pointers:["mouse","touch","pen"]}},$n=(t,e)=>{const n={};return H(L(at(e),at(t)),(r=>{const o=t[r],s=e[r];if(O(o)&&O(s))ut(n[r]={},$n(o,s)),pt(n[r])&&delete n[r];else if(ct(e,r)&&s!==o){let t=!0;if(S(o)||S(s))try{gn(o)===gn(s)&&(t=!1)}catch(i){}t&&(n[r]=s)}})),n},Sn=(t,e,n)=>r=>[wn(t,r),n||void 0!==wn(e,r)];let On,Mn;const Cn=()=>(Mn||(Mn=(()=>{const t=(t,e,n)=>{Rt(document.body,t),Rt(document.body,t);const r=Kt(t),o=Jt(t),s=te(e);return n&&Et(t),{x:o.h-r.h+s.h,y:o.w-r.w+s.w}},r=It(`<div class="${Se}"><div></div><style>${`.${Se}{scroll-behavior:auto!important;position:fixed;opacity:0;visibility:hidden;overflow:scroll;height:200px;width:200px;z-index:-1}.${Se} div{width:200%;height:200%;margin:10px 0}.${Oe}{scrollbar-width:none!important}.${Oe}::-webkit-scrollbar,.${Oe}::-webkit-scrollbar-corner{appearance:none!important;display:none!important;width:0!important;height:0!important}`}</style></div>`)[0],o=r.firstChild,s=r.lastChild,i=On;i&&(s.nonce=i);const[l,,c]=ve(),[a,u]=e({o:t(r,o),i:nt},st(t,r,o,!0)),[d]=u(),p=(t=>{let e=!1;const n=Ct(t,Oe);try{e="none"===Zt(t,"scrollbar-width")||"none"===Zt(t,"display","::-webkit-scrollbar")}catch(r){}return n(),e})(r),y={x:0===d.x,y:0===d.y},h={elements:{host:null,padding:!p,viewport:t=>p&&kt(t)&&t,content:!1},scrollbars:{slot:!0},cancel:{nativeScrollbarsOverlaid:!1,body:null}},f=ut({},mn),x=st(ut,{},f),b=st(ut,{},h),g={N:d,T:y,P:p,J:!!v,K:st(l,"r"),Z:b,tt:t=>ut(h,t)&&b(),nt:x,ot:t=>ut(f,t)&&x(),st:ut({},h),et:ut({},f)};if(bt(r,"style"),Et(r),se(n,"resize",(()=>{c("r",[])})),$(n.matchMedia)&&!p&&(!y.x||!y.y)){const t=e=>{const r=n.matchMedia(`(resolution: ${n.devicePixelRatio}dppx)`);se(r,"change",(()=>{e(),t(e)}),{A:!0})};t((()=>{const[t,e]=a();ut(g.N,t),c("r",[e])}))}return g})()),Mn),Dn=(t,e,n,r)=>{let o=!1;const{ct:s,rt:i,lt:l,it:c,ut:a,_t:u}=r||{},d=lt((()=>o&&n(!0)),{_:33,v:99}),[p,h]=((t,e,n)=>{let r=!1;const o=!!n&&new WeakMap,s=s=>{if(o&&n){const i=n.map((e=>{const[n,r]=e||[];return[r&&n?(s||Dt)(n,t):[],r]}));H(i,(n=>H(n[0],(s=>{const i=n[1],l=o.get(s)||[];if(t.contains(s)&&i){const t=se(s,i,(n=>{r?(t(),o.delete(s)):e(n)}));o.set(s,E(l,t))}else _(l),o.delete(s)}))))}};return s(),[()=>{r=!0},s]})(t,d,l),f=i||[],v=L(s||[],f),x=(o,s)=>{if(!z(s)){const i=a||ot,l=u||ot,d=[],p=[];let y=!1,v=!1;if(H(s,(n=>{const{attributeName:o,target:s,type:a,oldValue:u,addedNodes:h,removedNodes:x}=n,b="attributes"===a,g="childList"===a,m=t===s,$=b&&o,S=$&&ft(s,o||""),O=w(S)?S:null,M=$&&u!==O,C=A(f,o)&&M;if(e&&(g||!m)){const e=b&&M,a=e&&c&&Tt(s,c),p=(a?!i(s,o,u,O):!b||e)&&!l(n,!!a,t,r);H(h,(t=>E(d,t))),H(x,(t=>E(d,t))),v=v||p}!e&&m&&M&&!i(s,o,u,O)&&(E(p,o),y=y||C)})),h((t=>I(d).reduce(((e,n)=>(E(e,Dt(t,n)),Tt(n,t)?E(e,n):e)),[]))),e)return!o&&v&&n(!1),[!1];if(!z(p)||y){const t=[I(p),y];return!o&&n.apply(0,t),t}}},b=new y(st(x,!1));return[()=>(b.observe(t,{attributes:!0,attributeOldValue:!0,attributeFilter:v,subtree:e,childList:e,characterData:e}),o=!0,()=>{o&&(p(),b.disconnect(),o=!1)}),()=>{if(o)return d.m(),x(!0,b.takeRecords())}]},Tn=(t,n,r)=>{const{dt:o}=r||{},s=we(un),[i]=e({o:!1,u:!0});return()=>{const e=[],r=It(`<div class="${je}"><div class="${Ve}"></div></div>`)[0],l=r.firstChild,c=t=>{let e=!1,r=!1;if(t instanceof ResizeObserverEntry){const[n,,o]=i(t.contentRect),s=ne(n);r=re(n,o),e=!r&&!s}else r=!0===t;e||n({ft:!0,dt:r})};if(f){const t=new f((t=>c(t.pop())));t.observe(l),E(e,(()=>{t.disconnect()}))}else{if(!s)return ot;{const[t,n]=s(l,c,o);E(e,L([Ct(r,Ne),se(r,"animationstart",t)],n))}}return st(_,E(e,Rt(t,r)))}},kn=(t,n)=>{let r;const o=zt(We),[s]=e({o:!1}),i=(t,e)=>{if(t){const r=s((t=>0===t.h||t.isIntersecting||t.intersectionRatio>0)(t)),[,o]=r;return o&&!e&&n(r)&&[r]}},l=(t,e)=>i(e.pop(),t);return[()=>{const e=[];if(h)r=new h(st(l,!1),{root:t}),r.observe(o),E(e,(()=>{r.disconnect()}));else{const t=()=>{const t=Jt(o);i(t)};E(e,Tn(o,t)()),t()}return st(_,E(e,Rt(t,o)))},()=>r&&l(!0,r.takeRecords())]},Pn=(t,n,r,o)=>{let s,i,l,c,a,u;const d=`[${Te}]`,p=`[${Pe}]`,y=["id","class","style","open","wrap","cols","rows"],{vt:h,ht:v,U:x,gt:b,bt:w,L:m,yt:O,wt:M,St:C,Ot:D}=t,k=t=>"rtl"===Zt(t,"direction"),P={$t:!1,B:k(h)},H=Cn(),A=we(fn),[E]=e({i:et,o:{w:0,h:0}},(()=>{const e=A&&A.V(t,n,P,H,r).W,o=!(O&&m)&&St(v,Te,Ce),s=!m&&M(Le),i=s&&ue(b),l=i&&D(),c=C(Ee,o),a=s&&e&&e()[0],u=Qt(x),d=te(x);return a&&a(),ae(b,i),l&&l(),o&&c(),{w:u.w+d.w,h:u.h+d.h}})),R=lt(o,{_:()=>s,v:()=>i,S(t,e){const[n]=t,[r]=e;return[L(at(n),at(r)).reduce(((t,e)=>(t[e]=n[e]||r[e],t)),{})]}}),U=t=>{const e=k(h);ut(t,{Ct:u!==e}),ut(P,{B:e}),u=e},z=(t,e)=>{const[n,r]=t,s={xt:r};return ut(P,{$t:n}),!e&&o(s),s},I=({ft:t,dt:e})=>{const n=t&&!e||!H.P?o:R,r={ft:t||e,dt:e};U(r),n(r)},_=(t,e)=>{const[,n]=E(),r={Ht:n};return U(r),n&&!e&&(t?o:R)(r),r},j=(t,e,n)=>{const r={Et:e};return U(r),e&&!n&&R(r),r},[N,V]=w?kn(v,z):[],B=!m&&Tn(v,I,{dt:!0}),[F,Z]=Dn(v,!1,j,{rt:y,ct:y}),W=m&&f&&new f((t=>{const e=t[t.length-1].contentRect;I({ft:!0,dt:re(e,a)}),a=e})),q=lt((()=>{const[,t]=E();o({Ht:t})}),{_:222,p:!0});return[()=>{W&&W.observe(v);const t=B&&B(),e=N&&N(),n=F(),r=H.K((t=>{t?R({zt:t}):q()}));return()=>{W&&W.disconnect(),t&&t(),e&&e(),c&&c(),n(),r()}},({It:t,At:e,Dt:n})=>{const r={},[o]=t("update.ignoreMutation"),[a,u]=t("update.attributes"),[h,f]=t("update.elementEvents"),[v,b]=t("update.debounce"),O=e||n;if(f||u){l&&l(),c&&c();const[t,e]=Dn(w||x,!0,_,{ct:L(y,a||[]),lt:h,it:d,_t:(t,e)=>{const{target:n,attributeName:r}=t;return!(e||!r||m)&&((t,e,n)=>{const r=At(t,e),o=t&&((t,e)=>{const n=e?T(e)&&e:document;return n&&n.querySelector(t)})(n,r),s=At(o,e)===r;return!(!r||!o)&&(r===t||o===t||s&&At(At(t,n),e)!==r)})(n,d,p)||!!At(n,`.${Ye}`)||!!(t=>$(o)&&o(t))(t)}});c=t(),l=e}if(b)if(R.m(),S(v)){const t=v[0],e=v[1];s=g(t)&&t,i=g(e)&&e}else g(v)?(s=v,i=!1):(s=!1,i=!1);if(O){const t=Z(),e=V&&V(),n=l&&l();t&&ut(r,j(t[0],t[1],O)),e&&ut(r,z(e[0],O)),n&&ut(r,_(n[0],O))}return U(r),r},P]},Hn=(t,e)=>$(e)?e.apply(0,t):e,An=(t,e,n,r)=>{const o=x(r)?n:r;return Hn(t,o)||e.apply(0,t)},Ln=(t,e,n,r)=>{const o=x(r)?n:r,s=Hn(t,o);return!!s&&(D(s)?s:e.apply(0,t))},En=(t,e,n,r)=>{const o="--os-viewport-percent",s="--os-scroll-percent",i="--os-scroll-direction",{Z:l}=Cn(),{scrollbars:c}=l(),{slot:a}=c,{vt:u,ht:d,U:p,Mt:y,gt:h,yt:f,L:x}=e,{scrollbars:b}=y?{}:t,{slot:g}=b||{},w=[],$=[],S=[],O=Ln([u,d,p],(()=>x&&f?u:d),a,g),M=t=>{if(v){let e=null,r=[];const o=new v({source:h,axis:t}),s=()=>{e&&e.cancel(),e=null},i=i=>{const{Tt:l}=n,c=pe(l)[t],a="x"===t,u=[qt(0,a),qt(`calc(100cq${a?"w":"h"} + -100%)`,a)],d=c?u:u.reverse();return r[0]===d[0]&&r[1]===d[1]||(s(),r=d,e=i.kt.animate({clear:["left"],transform:d},{timeline:o})),s};return{Rt:i}}},C={x:M("x"),y:M("y")},D=(t,e,n)=>{const r=n?Ct:Mt;H(t,(t=>{r(t.Ut,e)}))},T=(t,e)=>{H(t,(t=>{const[n,r]=e(t);Ft(n,r)}))},k=(t,e,n)=>{const r=m(n),o=!r||!n;(!r||n)&&D($,t,e),o&&D(S,t,e)},P=t=>{const e=t?"x":"y",n=zt(`${Ye} ${t?Ge:Je}`),o=zt(Ke),s=zt(Qe),i={Ut:n,Pt:o,kt:s},l=C[e];return E(t?$:S,i),E(w,[Rt(n,o),Rt(o,s),st(Et,n),l&&l.Rt(i),r(i,k,t)]),i},A=st(P,!0),L=st(P,!1);return A(),L(),[{Nt:()=>{const t=(()=>{const{Vt:t,Lt:e}=n,r=(t,e)=>yt(0,1,t/(t+e)||0);return{x:r(e.x,t.x),y:r(e.y,t.y)}})(),e=t=>e=>[e.Ut,{[o]:Vt(t)+""}];T($,e(t.x)),T(S,e(t.y))},qt:()=>{if(!v){const{Tt:t}=n,e=ye(t,ue(h)),r=t=>e=>[e.Ut,{[s]:Vt(t)+""}];T($,r(e.x)),T(S,r(e.y))}},jt:()=>{const{Tt:t}=n,e=pe(t),r=t=>e=>[e.Ut,{[i]:t?"0":"1"}];T($,r(e.x)),T(S,r(e.y)),v&&($.forEach(C.x.Rt),S.forEach(C.y.Rt))},Bt:()=>{if(x&&!f){const{Vt:t,Tt:e}=n,r=pe(e),o=ye(e,ue(h)),s=e=>{const{Ut:n}=e,s=Ht(n)===p&&n,i=(t,e,n)=>{const r=e*t;return Bt(n?r:-r)};return[s,s&&{transform:qt({x:i(o.x,t.x,r.x),y:i(o.y,t.y,r.y)})}]};T($,s),T(S,s)}},Ft:k,Xt:{Yt:$,Wt:A,Gt:st(T,$)},Jt:{Yt:S,Wt:L,Gt:st(T,S)}},()=>(Rt(O,$[0].Ut),Rt(O,S[0].Ut),st(_,w))]},Rn=(t,e,n,r)=>(o,l,c)=>{const{ht:a,U:d,L:p,gt:y,Kt:h,Ot:f}=e,{Ut:v,Pt:x,kt:b}=o,[g,w]=it(333),[m,S]=it(444),O=t=>{$(y.scrollBy)&&y.scrollBy({behavior:"smooth",left:t.x,top:t.y})};let M=!0;return st(_,[se(b,"pointermove pointerleave",r),se(v,"pointerenter",(()=>{l(nn,!0)})),se(v,"pointerleave pointercancel",(()=>{l(nn,!1)})),!p&&se(v,"mousedown",(()=>{const t=Lt();(vt(t,Pe)||vt(t,Te)||t===document.body)&&u(st(he,d),25)})),se(v,"wheel",(t=>{const{deltaX:e,deltaY:n,deltaMode:r}=t;M&&0===r&&Ht(v)===a&&O({x:e,y:n}),M=!1,l(ln,!0),g((()=>{M=!0,l(ln)})),le(t)}),{H:!1,I:!0}),se(v,"pointerdown",st(se,h,"click",ce,{A:!0,I:!0,H:!1}),{I:!0}),(()=>{const e="pointerup pointercancel lostpointercapture",r=`client${c?"X":"Y"}`,o=c?X:G,l=c?"left":"top",a=c?"w":"h",u=c?"x":"y",d=(t,e)=>r=>{const{Vt:o}=n,s=Jt(x)[a]-Jt(b)[a],i=e*r/s*o[u];ae(y,{[u]:t+i})},p=[];return se(x,"pointerdown",(n=>{const c=At(n.target,`.${Qe}`)===b,v=c?b:x,g=t.scrollbars,w=g[c?"dragScroll":"clickScroll"],{button:$,isPrimary:M,pointerType:C}=n,{pointers:D}=g;if(0===$&&M&&w&&(D||[]).includes(C)){_(p),S();const t=!c&&(n.shiftKey||"instant"===w),g=st(ee,b),$=st(ee,x),M=(t,e)=>(t||g())[l]-(e||$())[l],C=s(ee(y)[o])/Jt(y)[a]||1,D=d(ue(y)[u],1/C),T=n[r],k=g(),P=$(),H=k[o],A=M(k,P)+H/2,L=T-P[l],R=c?0:L-A,U=t=>{_(j),v.releasePointerCapture(t.pointerId)},z=c||t,I=f(),j=[se(h,e,U),se(h,"selectstart",(t=>le(t)),{H:!1}),se(x,e,U),z&&se(x,"pointermove",(t=>D(R+(t[r]-T)))),z&&(()=>{const t=ue(y);I();const e=ue(y),n={x:e.x-t.x,y:e.y-t.y};(i(n.x)>3||i(n.y)>3)&&(f(),ae(y,t),O(n),m(I))})];if(v.setPointerCapture(n.pointerId),t)D(R);else if(!c){const t=we(xn);if(t){const e=t(D,R,H,(t=>{t?I():E(j,I)}));E(j,e),E(p,st(e,!0))}}}}))})(),w,S])},Un=t=>{const e=Cn(),{Z:r,P:o}=e,{elements:s}=r(),{padding:i,viewport:l,content:c}=s,a=D(t),u=a?{}:t,{elements:d}=u,{padding:p,viewport:y,content:h}=d||{},f=a?t:u.target,v=kt(f),x=f.ownerDocument,b=x.documentElement,g=()=>x.defaultView||n,w=st(An,[f]),m=st(Ln,[f]),$=st(zt,""),S=st(w,$,l),O=st(m,$,c),M=S(y),C=M===f,T=C&&v,k=!C&&O(h),P=!C&&M===k,H=T?b:M,L=T?H:f,R=!C&&m($,i,p),U=!P&&k,z=[U,H,R,L].map((t=>D(t)&&!Ht(t)&&t)),I=t=>t&&A(z,t),j=!I(H)&&(t=>{const e=Jt(t),n=Qt(t),r=Zt(t,q),o=Zt(t,Y);return n.w-e.w>0&&!yn(r)||n.h-e.h>0&&!yn(o)})(H)?H:f,N=T?b:H,V={vt:f,ht:L,U:H,ln:R,bt:U,gt:N,Qt:T?x:H,an:v?b:j,Kt:x,yt:v,Mt:a,L:C,un:g,wt:t=>St(H,Pe,t),St:(t,e)=>$t(H,Pe,t,e),Ot:()=>$t(N,Pe,Re,!0)},{vt:B,ht:F,ln:Z,U:W,bt:X}=V,G=[()=>{bt(F,[Te,Me]),bt(B,Me),v&&bt(b,[Me,Te])}];let J=Pt([X,W,Z,F,B].find((t=>t&&!I(t))));const K=T?B:X||W,Q=st(_,G);return[V,()=>{const t=g(),e=Lt(),n=t=>{Rt(Ht(t),Pt(t)),Et(t)},r=t=>se(t,"focusin focusout focus blur",ce,{I:!0,H:!1}),s="tabindex",i=ft(W,s),l=r(e);return xt(F,Te,C?"":ke),xt(Z,Ie,""),xt(W,Pe,""),xt(X,_e,""),C||(xt(W,s,i||"-1"),v&&xt(b,De,"")),Rt(K,J),Rt(F,Z),Rt(Z||F,!C&&W),Rt(W,X),E(G,[l,()=>{const t=Lt(),e=I(W),o=e&&t===W?B:t,l=r(o);bt(Z,Ie),bt(X,_e),bt(W,Pe),v&&bt(b,De),i?xt(W,s,i):bt(W,s),I(X)&&n(X),e&&n(W),I(Z)&&n(Z),he(o),l()}]),o&&!C&&(mt(W,Pe,Ue),E(G,st(bt,W,Pe))),he(!C&&v&&e===B&&t.top===t?W:e),l(),J=0,Q},Q]},zn=({bt:t})=>({Zt:e,_n:n,Dt:r})=>{const{xt:o}=e||{},{$t:s}=n;t&&(o||r)&&Ft(t,{[G]:s&&"100%"})},In=({ht:t,ln:n,U:r,L:o},s)=>{const[i,l]=e({i:rt,o:Wt()},st(Wt,t,"padding",""));return({It:t,Zt:e,_n:c,Dt:a})=>{let[u,d]=l(a);const{P:p}=Cn(),{ft:y,Ht:h,Ct:f}=e||{},{B:v}=c,[x,b]=t("paddingAbsolute");(y||d||a||h)&&([u,d]=i(a));const g=!o&&(b||f||d);if(g){const t=!x||!n&&!p,e=u.r+u.l,o=u.t+u.b,i={[Z]:t&&!v?-e:0,[W]:t?-o:0,[F]:t&&v?-e:0,top:t?-u.t:0,right:t?v?-u.r:"auto":0,left:t?v?"auto":-u.l:0,[X]:t&&`calc(100% + ${e}px)`},l={[j]:t?u.t:0,[N]:t?u.r:0,[B]:t?u.b:0,[V]:t?u.l:0};Ft(n||r,i),Ft(r,l),ut(s,{ln:u,dn:!t,F:n?l:ut({},i,l)})}return{fn:g}}},_n=(t,o)=>{const s=Cn(),{ht:i,ln:l,U:c,L:u,Qt:d,gt:p,yt:y,St:h,un:f}=t,{P:v}=s,x=y&&u,b=st(r,0),g={display:()=>!1,direction:t=>"ltr"!==t,flexDirection:t=>t.endsWith("-reverse"),writingMode:t=>"horizontal-tb"!==t},w=at(g),m={i:et,o:{w:0,h:0}},$={i:nt,o:{}},S=t=>{h(Ee,!x&&t)},O=t=>{const e=w.some((e=>{const n=t[e];return n&&g[e](n)}));if(!e)return{D:{x:0,y:0},M:{x:1,y:1}};S(!0);const n=ue(p),r=h(ze,!0),o=se(d,Q,(t=>{const e=ue(p);t.isTrusted&&e.x===n.x&&e.y===n.y&&ie(t)}),{I:!0,A:!0});ae(p,{x:0,y:0}),r();const s=ue(p),i=Qt(p);ae(p,{x:i.w,y:i.h});const l=ue(p);ae(p,{x:l.x-s.x<1&&-i.w,y:l.y-s.y<1&&-i.h});const c=ue(p);return ae(p,n),a((()=>o())),{D:s,M:c}},M=(t,e)=>{const r=n.devicePixelRatio%1!=0?1:0,o={w:b(t.w-e.w),h:b(t.h-e.h)};return{w:o.w>r?o.w:0,h:o.h>r?o.h:0}},[C,D]=e(m,st(te,c)),[T,k]=e(m,st(Qt,c)),[P,H]=e(m),[A]=e($),[L,E]=e(m),[R]=e($),[U]=e({i:(t,e)=>tt(t,e,w),o:{}},(()=>(t=>!!t&&(t=>!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length))(t))(c)?Zt(c,w):{})),[z,I]=e({i:(t,e)=>nt(t.D,e.D)&&nt(t.M,e.M),o:{D:{x:0,y:0},M:{x:0,y:0}}}),_=we(fn),j=(t,e)=>`${e?He:Ae}${(t=>{const e=String(t||"");return e?e[0].toUpperCase()+e.slice(1):""})(t)}`,N=t=>{const e=t=>[J,K,Q].map((e=>j(e,t))),n=e(!0).concat(e()).join(" ");h(n),h(at(t).map((e=>j(t[e],"x"===e))).join(" "),!0)};return({It:e,Zt:n,_n:r,Dt:a},{fn:u})=>{const{ft:d,Ht:p,Ct:y,dt:g,zt:w}=n||{},m=_&&_.V(t,o,r,s,e),{Y:$,W:j,G:V}=m||{},[B,F]=pn(e,s),[Z,W]=e("overflow"),q=yn(Z.x),Y=yn(Z.y);let X=D(a),G=k(a),J=H(a),K=E(a);F&&v&&h(Ue,!B);{St(i,Te,Ce)&&S(!0);const[t]=j?j():[],[e]=X=C(a),[n]=G=T(a),r=Kt(c),o=x&&Gt(f()),s={w:b(n.w+e.w),h:b(n.h+e.h)},l={w:b((o?o.w:r.w+b(r.w-n.w))+e.w),h:b((o?o.h:r.h+b(r.h-n.h))+e.h)};t&&t(),K=L(l),J=P(M(s,l),a)}const[Q,tt]=K,[et,nt]=J,[rt,ot]=G,[st,it]=X,[lt,ct]=A({x:et.w>0,y:et.h>0}),at=q&&Y&&(lt.x||lt.y)||q&&lt.x&&!lt.y||Y&&lt.y&&!lt.x,dt=u||y||w||it||ot||tt||nt||W||F||!0,pt=hn(lt,Z),[yt,ht]=R(pt.k),[ft,vt]=U(a),xt=y||g||vt||ct||a,[bt,gt]=xt?z(O(ft),a):I();return dt&&(ht&&N(pt.k),V&&$&&Ft(c,V(pt,r,$(pt,rt,st)))),S(!1),$t(i,Te,Ce,at),$t(l,Ie,Ce,at),ut(o,{k:yt,Lt:{x:Q.w,y:Q.h},Vt:{x:et.w,y:et.h},rn:lt,Tt:de(bt,et)}),{en:ht,nn:tt,sn:nt,cn:gt||nt,vn:xt}}},jn=t=>{const[e,n,r]=Un(t),o={ln:{t:0,r:0,b:0,l:0},dn:!1,F:{[Z]:0,[W]:0,[F]:0,[j]:0,[N]:0,[B]:0,[V]:0},Lt:{x:0,y:0},Vt:{x:0,y:0},k:{x:K,y:K},rn:{x:!1,y:!1},Tt:{D:{x:0,y:0},M:{x:0,y:0}}},{vt:s,gt:i,L:l,Ot:c}=e,{P:a,T:u}=Cn(),d=!a&&(u.x||u.y),p=[zn(e),In(e,o),_n(e,o)];return[n,t=>{const e={},n=d&&ue(i),r=n&&c();return H(p,(n=>{ut(e,n(t,e)||{})})),ae(i,n),r&&r(),!l&&ae(s,0),e},o,e,r]},Nn=new WeakMap,Vn=t=>Nn.get(t),Bn=(t,e,n)=>{const{nt:r}=Cn(),o=D(t),s=o?t:t.target,i=Vn(s);if(e&&!i){let i=!1;const l=[],c={},a=t=>{const e=dt(t),n=we(me);return n?n(e,!0):e},u=ut({},r(),a(e)),[d,p,y]=ve(),[h,f,v]=ve(n),g=(t,e)=>{v(t,e),y(t,e)},[w,m,$,S,O]=((t,e,n,r)=>{let o=!1;const s=Sn(e,{}),[i,l,c,a,u]=jn(t),[d,p,y]=Pn(a,c,s,(t=>{b({},t)})),[h,f,,v]=((t,e,n,r,o,s)=>{let i,l,c,a,u,d=ot,p=0;const y=["mouse","pen"],h=t=>y.includes(t.pointerType),[f,v]=it(),[x,b]=it(100),[g,w]=it(100),[m,$]=it((()=>p)),[S,O]=En(t,o,r,Rn(e,o,r,(t=>h(t)&&R()))),{ht:M,Qt:C,yt:D}=o,{Ft:T,Nt:k,qt:P,jt:H,Bt:A}=S,L=(t,e)=>{if($(),t)T(sn);else{const t=st(T,sn,!0);p>0&&!e?m(t):t()}},R=()=>{(c?i:a)||(L(!0),x((()=>{L(!1)})))},U=t=>{T(on,t,!0),T(on,t,!1)},z=t=>{h(t)&&(i=c,c&&L(!0))},I=[$,b,w,v,()=>d(),se(M,"pointerover",z,{A:!0}),se(M,"pointerenter",z),se(M,"pointerleave",(t=>{h(t)&&(i=!1,c&&L(!1))})),se(M,"pointermove",(t=>{h(t)&&l&&R()})),se(C,"scroll",(t=>{f((()=>{P(),R()})),s(t),A()}))];return[()=>st(_,E(I,O())),({It:t,Dt:e,Zt:o,tn:s})=>{const{nn:i,sn:y,en:h,cn:f}=s||{},{Ct:v,dt:x}=o||{},{B:b}=n,{T:w}=Cn(),{k:m,rn:$}=r,[S,O]=t("showNativeOverlaidScrollbars"),[M,E]=t("scrollbars.theme"),[R,z]=t("scrollbars.visibility"),[I,_]=t("scrollbars.autoHide"),[j,N]=t("scrollbars.autoHideSuspend"),[V]=t("scrollbars.autoHideDelay"),[B,F]=t("scrollbars.dragScroll"),[Z,W]=t("scrollbars.clickScroll"),[q,Y]=t("overflow"),X=x&&!e,G=$.x||$.y,K=i||y||f||v||e,tt=h||z||Y,et=S&&w.x&&w.y,nt=(t,e,n)=>{const r=t.includes(Q)&&(R===J||"auto"===R&&e===Q);return T(tn,r,n),r};if(p=V,X&&(j&&G?(U(!1),d(),g((()=>{d=se(C,"scroll",st(U,!0),{A:!0})}))):U(!0)),O&&T(qe,et),E&&(T(u),T(M,!0),u=M),N&&!j&&U(!0),_&&(l="move"===I,c="leave"===I,a="never"===I,L(a,!0)),F&&T(an,B),W&&T(cn,!!Z),tt){const t=nt(q.x,m.x,!0),e=nt(q.y,m.y,!1);T(en,!(t&&e))}K&&(P(),k(),A(),f&&H(),T(rn,!$.x,!0),T(rn,!$.y,!1),T(Xe,b&&!D))},{},S]})(t,e,y,c,a,(t=>g("scroll",[C,t]))),x=t=>at(t).some((e=>!!t[e])),b=(t,s)=>{if(n())return!1;const{pn:i,Dt:c,At:a,hn:u}=t,d=i||{},h=!!c||!o,v={It:Sn(e,d,h),pn:d,Dt:h};if(u)return f(v),!1;const b=s||p(ut({},v,{At:a})),g=l(ut({},v,{_n:y,Zt:b}));f(ut({},v,{Zt:b,tn:g}));const w=x(b),m=x(g),$=w||m||!pt(d)||h;return o=!0,$&&r(t,{Zt:b,tn:g}),$};return[()=>{const{an:t,gt:e,Ot:n}=a,r=ue(t),o=[d(),i(),h()],s=n();return ae(e,r),s(),st(_,o)},b,()=>({gn:y,bn:c}),{yn:a,wn:v},u]})(t,u,(()=>i),(({pn:t,Dt:e},{Zt:n,tn:r})=>{const{ft:o,Ct:s,xt:i,Ht:l,Et:c,dt:a}=n,{nn:u,sn:d,en:p,cn:y}=r;g("updated",[C,{updateHints:{sizeChanged:!!o,directionChanged:!!s,heightIntrinsicChanged:!!i,overflowEdgeChanged:!!u,overflowAmountChanged:!!d,overflowStyleChanged:!!p,scrollCoordinatesChanged:!!y,contentMutation:!!l,hostMutation:!!c,appear:!!a},changedOptions:t||{},force:!!e}])})),M=t=>{(t=>{Nn.delete(t)})(s),_(l),i=!0,g("destroyed",[C,t]),p(),f()},C={options(t,e){if(t){const n=e?r():{},o=$n(u,ut(n,a(t)));pt(o)||(ut(u,o),m({pn:o}))}return ut({},u)},on:h,off:(t,e)=>{t&&e&&f(t,e)},state(){const{gn:t,bn:e}=$(),{B:n}=t,{Lt:r,Vt:o,k:s,rn:l,ln:c,dn:a,Tt:u}=e;return ut({},{overflowEdge:r,overflowAmount:o,overflowStyle:s,hasOverflow:l,scrollCoordinates:{start:u.D,end:u.M},padding:c,paddingAbsolute:a,directionRTL:n,destroyed:i})},elements(){const{vt:t,ht:e,ln:n,U:r,bt:o,gt:s,Qt:i}=S.yn,{Xt:l,Jt:c}=S.wn,a=t=>{const{kt:e,Pt:n,Ut:r}=t;return{scrollbar:r,track:n,handle:e}},u=t=>{const{Yt:e,Wt:n}=t,r=a(e[0]);return ut({},r,{clone:()=>{const t=a(n());return m({hn:!0}),t}})};return ut({},{target:t,host:e,padding:n||r,viewport:r,content:o||r,scrollOffsetElement:s,scrollEventElement:i,scrollbarHorizontal:u(l),scrollbarVertical:u(c)})},update:t=>m({Dt:t,At:!0}),destroy:st(M,!1),plugin:t=>c[at(t)[0]]};return E(l,[O]),((t,e)=>{Nn.set(t,e)})(s,C),ge(xe,Bn,[C,d,c]),((t,e)=>{const{nativeScrollbarsOverlaid:n,body:r}=e||{},{T:o,P:s,Z:i}=Cn(),{nativeScrollbarsOverlaid:l,body:c}=i().cancel,a=null!=n?n:l,u=x(r)?c:r,d=(o.x||o.y)&&a,p=t&&(b(u)?!s:u);return!!d||!!p})(S.yn.yt,!o&&t.cancel)?(M(!0),C):(E(l,w()),g("initialized",[C]),C.update(),C)}return i};return Bn.plugin=t=>{const e=S(t),n=e?t:[t],r=n.map((t=>ge(t,Bn)[0]));return(t=>{H(t,(t=>H(t,((e,n)=>{xe[n]=t[n]}))))})(n),e?r:r[0]},Bn.valid=t=>{const e=t&&t.elements,n=$(e)&&e();return C(n)&&!!Vn(n.target)},Bn.env=()=>{const{N:t,T:e,P:n,J:r,st:o,et:s,Z:i,tt:l,nt:c,ot:a}=Cn();return ut({},{scrollbarsSize:t,scrollbarsOverlaid:e,scrollbarsHiding:n,scrollTimeline:r,staticDefaultInitialization:o,staticDefaultOptions:s,getDefaultInitialization:i,setDefaultInitialization:l,getDefaultOptions:c,setDefaultOptions:a})},Bn.nonce=t=>{On=t},Bn.trustedTypePolicy=t=>{Ut=t},t.ClickScrollPlugin=bn,t.OverlayScrollbars=Bn,t.ScrollbarsHidingPlugin=vn,t.SizeObserverPlugin=dn,Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t}({});