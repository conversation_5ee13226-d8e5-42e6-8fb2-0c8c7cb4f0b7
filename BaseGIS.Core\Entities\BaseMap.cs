﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BaseGIS.Core.Entities
{
    /// <summary>
    /// نقشه پایه
    /// </summary>
    public class BaseMap
    {
        [Key]
        public int ID { get; set; }

        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Des { get; set; }

        [Required]
        [StringLength(2000)]
        public string Url { get; set; } = string.Empty;

        [StringLength(50)]
        public string? DateTime { get; set; }

        [StringLength(500)]
        public string? Image { get; set; }

        /// <summary>
        /// کاربر مالک (null برای نقشه‌های عمومی)
        /// </summary>
        public ApplicationUser? User { get; set; }

        [StringLength(500)]
        public string? WMSLayers { get; set; }

        [StringLength(500)]
        public string? Styles { get; set; }

        /// <summary>
        /// وضعیت فعال بودن نقشه پایه
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ترتیب نمایش
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// نوع نقشه پایه (Tile, WMS, WMTS, etc.)
        /// </summary>
        [StringLength(50)]
        public string? MapType { get; set; } = "Tile";

        /// <summary>
        /// حداکثر زوم
        /// </summary>
        public int MaxZoom { get; set; } = 20;

        /// <summary>
        /// حداقل زوم
        /// </summary>
        public int MinZoom { get; set; } = 0;

        /// <summary>
        /// شفافیت پیش‌فرض
        /// </summary>
        [Column(TypeName = "decimal(3,2)")]
        public decimal DefaultOpacity { get; set; } = 1.0m;

        /// <summary>
        /// Attribution text
        /// </summary>
        [StringLength(500)]
        public string? Attribution { get; set; }

        /// <summary>
        /// تاریخ ایجاد
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// تاریخ آخرین بروزرسانی
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
    }
}
