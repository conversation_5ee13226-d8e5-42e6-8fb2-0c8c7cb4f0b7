﻿using System.ComponentModel.DataAnnotations;

namespace BaseGIS.Core.Entities
{
    public class BaseMap
    {
        [Key]
        public int ID { get; set; }

        public string Title { get; set; }
        public string Des { get; set; }

        public string Url { get; set; }

        public string DateTime { get; set; }

        public string Image { get; set; }

        public ApplicationUser User { get; set; }

        public string WMSLayers { get; set; }

        public string Styles { get; set; }

    }
}
