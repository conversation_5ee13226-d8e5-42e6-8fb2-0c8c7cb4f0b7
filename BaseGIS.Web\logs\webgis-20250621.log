2025-06-21 14:35:19.148 +03:30 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-21 14:35:19.221 +03:30 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-21 14:35:19.261 +03:30 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_getapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session', @LockMode = 'Exclusive';
SELECT @result
2025-06-21 14:35:19.380 +03:30 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
2025-06-21 14:35:19.393 +03:30 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-06-21 14:35:19.399 +03:30 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-06-21 14:35:19.444 +03:30 [INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-06-21 14:35:19.463 +03:30 [INF] Applying migration '20250621110340_UpdateUserSelectTable'.
2025-06-21 14:35:19.691 +03:30 [INF] Executed DbCommand (121ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @var sysname;
SELECT @var = [d].[name]
FROM [sys].[default_constraints] [d]
INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
WHERE ([d].[parent_object_id] = OBJECT_ID(N'[TableInfos]') AND [c].[name] = N'ValidationRule');
IF @var IS NOT NULL EXEC(N'ALTER TABLE [TableInfos] DROP CONSTRAINT [' + @var + '];');
ALTER TABLE [TableInfos] ALTER COLUMN [ValidationRule] nvarchar(max) NULL;
2025-06-21 14:35:19.717 +03:30 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @var1 sysname;
SELECT @var1 = [d].[name]
FROM [sys].[default_constraints] [d]
INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
WHERE ([d].[parent_object_id] = OBJECT_ID(N'[SymbologyInfos]') AND [c].[name] = N'FieldName');
IF @var1 IS NOT NULL EXEC(N'ALTER TABLE [SymbologyInfos] DROP CONSTRAINT [' + @var1 + '];');
ALTER TABLE [SymbologyInfos] ALTER COLUMN [FieldName] nvarchar(max) NULL;
2025-06-21 14:35:19.746 +03:30 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @var2 sysname;
SELECT @var2 = [d].[name]
FROM [sys].[default_constraints] [d]
INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
WHERE ([d].[parent_object_id] = OBJECT_ID(N'[SymbologyInfos]') AND [c].[name] = N'FieldAlias');
IF @var2 IS NOT NULL EXEC(N'ALTER TABLE [SymbologyInfos] DROP CONSTRAINT [' + @var2 + '];');
ALTER TABLE [SymbologyInfos] ALTER COLUMN [FieldAlias] nvarchar(max) NULL;
2025-06-21 14:35:19.771 +03:30 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [UserSelects] (
    [ID] int NOT NULL IDENTITY,
    [UserID] nvarchar(450) NOT NULL,
    [PageID] nvarchar(50) NULL,
    [Table] nvarchar(100) NOT NULL,
    [OIDs] nvarchar(max) NOT NULL,
    [Date] nvarchar(max) NOT NULL,
    [IsHighlight] bit NOT NULL,
    [Where] nvarchar(max) NULL,
    [LayerId] int NULL,
    [SelectionType] nvarchar(50) NOT NULL,
    [SelectionMetadata] nvarchar(2000) NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_UserSelects] PRIMARY KEY ([ID]),
    CONSTRAINT [FK_UserSelects_TableInfos_LayerId] FOREIGN KEY ([LayerId]) REFERENCES [TableInfos] ([Id])
);
2025-06-21 14:35:19.783 +03:30 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_UserSelects_LayerId] ON [UserSelects] ([LayerId]);
2025-06-21 14:35:19.790 +03:30 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250621110340_UpdateUserSelectTable', N'9.0.5');
2025-06-21 14:35:19.800 +03:30 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DECLARE @result int;
EXEC @result = sp_releaseapplock @Resource = '__EFMigrationsLock', @LockOwner = 'Session';
SELECT @result
