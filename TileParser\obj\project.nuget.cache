{"version": 2, "dgSpecHash": "yOW+zLtH6s0=", "success": true, "projectFilePath": "D:\\Backups\\BaseProject\\BaseGIS\\TileParser\\TileParser.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\mapbox.vectortile\\1.0.4-alpha2\\mapbox.vectortile.1.0.4-alpha2.nupkg.sha512"], "logs": [{"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Package 'Mapbox.VectorTile 1.0.4-alpha2' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net9.0'. This package may not be fully compatible with your project.", "libraryId": "Mapbox.VectorTile", "targetGraphs": ["net9.0"]}]}