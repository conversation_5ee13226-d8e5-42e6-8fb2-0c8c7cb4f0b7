﻿using BaseGIS.Core.Entities;

namespace BaseGIS.Web.Helper
{
    public class Utilities
    {
        public static string ReplaceWithDomain(string val, List<DomainInfo> dmnInfos)
        {
            string result = null;
            List<string> lst = new List<string>();
            for (int i = 0; i < dmnInfos.Count; i++)
            {
                if (!dmnInfos[i].IsMulti)
                {
                    int valint = 0;
                    bool ok = int.TryParse(val, out valint);
                    if (ok)
                        if (valint == dmnInfos[i].Code)
                        {
                            result = dmnInfos[i].Name;
                            break;
                        }
                }
                else
                {
                    var vals = val.Split(',');
                    for (int j = 0; j < vals.Length; j++)
                    {

                        int valint = 0;
                        bool ok = int.TryParse(vals[j], out valint);
                        if (ok)
                            if (valint == dmnInfos[i].Code)
                            {
                                lst.Add(dmnInfos[i].Name);

                            }
                    }
                }
            }
            if (lst.Count > 0)
                result = string.Join(",", lst.ToArray());
            return result;
        }

    }
}
