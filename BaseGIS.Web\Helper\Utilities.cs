﻿using BaseGIS.Core.Entities;
using NPOI.HSSF.UserModel;
using NPOI.XSSF.UserModel;
using System.Data;

namespace BaseGIS.Web.Helper
{
    public class Utilities
    {
        public static string ReplaceWithDomain(string val, List<DomainInfo> dmnInfos)
        {
            string result = null;
            List<string> lst = new List<string>();
            for (int i = 0; i < dmnInfos.Count; i++)
            {
                if (!dmnInfos[i].IsMulti)
                {
                    int valint = 0;
                    bool ok = int.TryParse(val, out valint);
                    if (ok)
                        if (valint == dmnInfos[i].Code)
                        {
                            result = dmnInfos[i].Name;
                            break;
                        }
                }
                else
                {
                    var vals = val.Split(',');
                    for (int j = 0; j < vals.Length; j++)
                    {

                        int valint = 0;
                        bool ok = int.TryParse(vals[j], out valint);
                        if (ok)
                            if (valint == dmnInfos[i].Code)
                            {
                                lst.Add(dmnInfos[i].Name);

                            }
                    }
                }
            }
            if (lst.Count > 0)
                result = string.Join(",", lst.ToArray());
            return result;
        }
        public static DataTable GetDataTableFromExcel(string fileName, int headerRowIndex = 0)
        {
            String Sheet_name;
            string fileExt = System.IO.Path.GetExtension(fileName);

            //Declare the sheet interface
            NPOI.SS.UserModel.ISheet sheet;

            //Get the Excel file according to the extension
            if (fileExt.ToLower() == ".xls")
            {
                //Use the NPOI Excel xls object
                HSSFWorkbook hssfwb;
                using (FileStream fileStream = new FileStream(fileName, FileMode.Open, FileAccess.Read))
                {
                    hssfwb = new HSSFWorkbook(fileStream);
                }
                Sheet_name = hssfwb.GetSheetAt(0).SheetName;  //get first sheet name
                //Assign the sheet
                sheet = hssfwb.GetSheet(Sheet_name);
            }
            else //.xlsx extension
            {
                //Use the NPOI Excel xlsx object
                XSSFWorkbook hssfwb;
                using (FileStream fileStream = new FileStream(fileName, FileMode.Open, FileAccess.Read))
                {
                    hssfwb = new XSSFWorkbook(fileStream);
                }
                Sheet_name = hssfwb.GetSheetAt(0).SheetName;
                //Assign the sheet
                sheet = hssfwb.GetSheet(Sheet_name);
            }

            DataTable DT = new DataTable();
            DT.Rows.Clear();
            DT.Columns.Clear();

            if (sheet != null && sheet.GetRow(headerRowIndex) != null)
            {
                for (int j = 0; j < sheet.GetRow(headerRowIndex).Cells.Count; j++)
                {
                    string columnName = sheet.GetRow(headerRowIndex).Cells[j].StringCellValue;
                    DT.Columns.Add(columnName);
                }

                int i = headerRowIndex + 1;
                while (sheet.GetRow(i) != null)
                {
                    DT.Rows.Add();
                    int rowIndex = i - headerRowIndex;
                    for (int j = 0; j < DT.Columns.Count; j++)
                    {
                        var cell = sheet.GetRow(i).GetCell(j);
                        if (cell != null)
                            try
                            {
                                switch (cell.CellType)
                                {
                                    case NPOI.SS.UserModel.CellType.Numeric:
                                        DT.Rows[rowIndex - 1][j] = cell.NumericCellValue;
                                        break;
                                    case NPOI.SS.UserModel.CellType.String:
                                        DT.Rows[rowIndex - 1][j] = cell.StringCellValue.ToArabicSQL();
                                        break;
                                }
                            }
                            catch (Exception ex)
                            {
                            }
                    }
                    i++;
                }
            }

            return DT;
        }

    }
}
