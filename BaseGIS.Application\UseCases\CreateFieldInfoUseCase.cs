using BaseGIS.Core.Entities;
using BaseGIS.Core.Interfaces;
using BaseGIS.Application.DTOs;

namespace BaseGIS.Application.UseCases
{
    public class CreateFieldInfoUseCase
    {
        private readonly IFieldInfoRepository _fieldInfoRepository;
        private readonly ITableInfoRepository _tableInfoRepository;

        public CreateFieldInfoUseCase(IFieldInfoRepository fieldInfoRepository, ITableInfoRepository tableInfoRepository)
        {
            _fieldInfoRepository = fieldInfoRepository;
            _tableInfoRepository = tableInfoRepository;
        }

        public async Task ExecuteAsync(FieldInfoDto dto)
        {
            // بررسی وجود TableInfo
            var tableInfo = await _tableInfoRepository.GetByIdAsync(dto.TableInfoId);
            if (tableInfo == null)
            {
                throw new InvalidOperationException("TableInfo یافت نشد.");
            }

            var fieldInfo = new FieldInfo
            {
                Name = dto.Name,
                AliasName = dto.AliasName,
                FieldType = dto.FieldType,
                FieldLength = dto.FieldLength,
                IsRequired = dto.IsRequired,
                Editable = dto.Editable,
                TableInfoId = dto.TableInfoId,
                Updated = DateTime.UtcNow
            };

            await _fieldInfoRepository.AddAsync(fieldInfo);
        }
    }
}