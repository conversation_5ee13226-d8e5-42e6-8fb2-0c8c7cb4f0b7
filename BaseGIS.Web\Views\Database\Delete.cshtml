﻿@{
    var id = Context.Request.Query["id"];
}

<!-- MAIN CONTENT -->
<div id="content" class="samanFont">
    <div class="row">
        <div class="col-12 col-lg-4">
            <h3 class="page-title txt-color-blueDark samanFont">
                <i class="fa-fw fa fa-puzzle-piece"></i>
                حذف کلیه اطلاعات
            </h3>
        </div>
    </div>

    <div class="row" id="rowMaster">
        <div class="col-12">
            @await Html.PartialAsync("_Delete_List")
        </div>
        @if (!string.IsNullOrEmpty(id))
        {
            <div class="col-12">
                @await Html.PartialAsync("_Delete_Query")
            </div>
        }
    </div>
</div>

@section scripts {
    <!-- اسکریپت‌های مورد نیاز برای ویوهای جزئی، در صورت وجود -->
    <script type="text/javascript">
        $(document).ready(function () {
            // اگر ویوهای جزئی به جاوااسکریپت خاصی نیاز دارند، اینجا اضافه کنید
        });
    </script>
}