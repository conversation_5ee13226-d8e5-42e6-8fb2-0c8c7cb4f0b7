﻿using BaseGIS.Core.Entities;
using BaseGIS.Infrastructure.Persistence;
using BaseGIS.Web.Helper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace BaseGIS.Web.Controllers
{
    public class GeoMapController : Controller
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<HomeController> _logger;
        private readonly IConfiguration _configuration;

        public GeoMapController(
            ApplicationDbContext dbContext,
            ILogger<HomeController> logger,
            IConfiguration configuration)
        {
            _dbContext = dbContext;
            _logger = logger;
            _configuration = configuration;
        }

        public IActionResult Index()
        {
            string layerDefs = "rg;on;label:Name,rv;off,vl;off";  // تعریف لایه‌ها
            string layerDefNew = ""; // برای ذخیره تعریف کوئری‌ها
            // آماده‌سازی داده‌ها برای Fancytree
            var listLayer = _dbContext.TableInfos.Include(t => t.GroupInfo).ToList();

            var treeData = GetTreeData(listLayer, layerDefs, _configuration);

            ViewBag.TreeData = treeData;
            return View();
        }
        public static string GetTreeData(List<TableInfo> listLayer, string layerDefs, IConfiguration _configuration)
        {
            int mapIndex = 1;
            string layerDefNew = "";
            string html = "";
            var group = (listLayer).GroupBy(a => a.GroupInfo);

            foreach (var item in group)
            {
                bool existLayerinGrpup = true;

                if (existLayerinGrpup)
                {
                    html += "<li id = \"G" + item.Key.Id + "\" class=\"group expanded\">";
                    html += item.Key.AliasName;
                    html += "<ul>";
                    foreach (var table in item)
                    {
                        {
                            bool showLayerfromService = false;
                            bool editLayerFromService = false;
                            string lableLayerFromService = "";
                            string symbolLayerFromService = "";
                            if (table.DatasetType != "table" && CheckLayer(table.ShortName, layerDefs, out showLayerfromService, out editLayerFromService, out symbolLayerFromService, out lableLayerFromService))
                            {
                                if (symbolLayerFromService == null || symbolLayerFromService == "")
                                {
                                    DBManagement db = new DBManagement(_configuration);
                                    var dtSymbology = db.SelectTableSQL($"Select * From SymbologyInfos Where TableInfoId = {table.Id} order by IsDefault desc");
                                    if (dtSymbology.Rows.Count > 0)
                                    {
                                        symbolLayerFromService = dtSymbology.Rows[0]["ID"].ToString();
                                    }
                                }
                                existLayerinGrpup = true;
                                var idLayer = "1" + mapIndex.ToString("000000") + table.Id.ToString("0000");
                                var title = ("base" + ":" + table.ShortName).ToString();

                                string querydef = "";
                                if (CheckLayerDefs(table.ShortName, layerDefs, out querydef))
                                {
                                    layerDefNew += "\"" + idLayer + "\":\"" + querydef + "\",";
                                }
                                html += "<li id = \"L" + idLayer + "\" class=\"" + table.DatasetType.ToLower() + "\" title=\"" + table.AliasName + "\" key=\"L" + idLayer + "\"" + (showLayerfromService ? " data-selected='true' " : "") + ">";
                                html += "<cc>" + (table.AliasName.Length > 19 ? table.AliasName.Substring(0, 19) : table.AliasName) + "</cc>";
                                html += $"<input class=\"d-none\" type=\"checkbox\" id=\"C{idLayer}\" {(lableLayerFromService != "" && lableLayerFromService != null ? "checked" : "")}/>";
                                html += $"<input class=\"d-none\" type=\"text\" id=\"O{idLayer}\" />";
                                html += $"<input class=\"d-none\" type=\"text\" id=\"La{idLayer}\" value=\"{lableLayerFromService}\" />";
                                html += $"<input class=\"d-none\" type=\"text\" id=\"Sym{idLayer}\" value=\"{symbolLayerFromService}\" />";
                                html += "<ul>";
                                html += $"<li class=\"xx\" key=\"L{idLayer}_otherlegend\"> <cc>  <a href=\"#\" onclick=\"setSymbologyByID('{idLayer}',-1);\">...</a></cc></li>";
                                html += "</ul>";
                                html += "</li>";
                            }
                        }
                    }
                    html += "</ul>";
                    html += "</li>";
                }
            }
            return html;
        }

        public static bool CheckLayer(string layer, string layers, out bool show, out bool edit, out string symbolID, out string lable)
        {

            show = false;
            edit = false;
            symbolID = null;
            lable = null;
            if (layers == null || layers == "")
                return true;


            var items = layers.Split(',');
            for (int i = 0; i < items.Length; i++)
            {
                var lyr = items[i].Trim().Split(';');
                if (lyr[0].ToLower() == layer.ToLower())
                {

                    for (int ii = 1; ii < lyr.Length; ii++)
                    {
                        var splt = lyr[ii].Split(':');
                        if (lyr[ii].ToLower() == "on")
                            show = true;
                        else if (lyr[ii].ToLower() == "edit")
                            show = true;
                        else if (splt.Length > 1 && splt[0].ToLower() == "symbolid")
                        {
                            int jjj = 0;
                            if (int.TryParse(splt[1], out jjj))
                                symbolID = splt[1];
                        }
                        else if (splt.Length > 1 && splt[0].ToLower() == "label")
                            lable = lyr[ii].Split(':')[1];
                    }

                    return true;
                }
            }


            return false;
        }

        public static bool CheckLayerDefs(string layer, string layerDefs, out string query)
        {
            query = "";
            if (layerDefs == null || layerDefs == "")
                return false;


            var items = layerDefs.Split(';');
            for (int i = 0; i < items.Length; i++)
            {
                var item = items[i].Trim().Split(':');
                if (item.Length > 1 && item[0].ToLower() == layer.ToLower())
                {
                    query = item[1];
                    return true;
                }
            }
            return false;
        }

        public ActionResult Property(string Layerid)
        {
            var layerType = Layerid.Substring(0, 1);
            string tableId = Layerid[^4..];
            var tblID = int.Parse(tableId);
            ViewBag.LayerId = tblID;
            ViewBag.LayerType = layerType;
            if (layerType == "1")
            {
                ViewBag.Symbolgies = _dbContext.SymbologyInfos.Include("TableInfo").Where(a => a.TableInfo.Id == tblID).ToList();
            }

            return PartialView();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public JsonResult GetLayerFields(string ID)
        {
            var layerType = ID.Substring(1, 1);
            string tableId = ID[^4..];
            var tblID = int.Parse(tableId);

            var data = _dbContext.FieldInfos.Where(a => a.TableInfo.Id == tblID && a.IsDisplay).OrderBy(f => f.FieldIndex).Select(a => new { a.Name, a.AliasName }).ToList();
            return Json(data);
        }
    }
}