﻿using BaseGIS.Core.Entities;
using BaseGIS.Infrastructure.Persistence;
using BaseGIS.Web.Helper;
using BaseGIS.Web.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace BaseGIS.Web.Controllers
{
    [Authorize]
    public class GeoMapController : Controller
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<GeoMapController> _logger;
        private readonly IConfiguration _configuration;
        private readonly IGeoMapService _geoMapService;
        private readonly UserManager<ApplicationUser> _userManager;

        public GeoMapController(
            ApplicationDbContext dbContext,
            ILogger<GeoMapController> logger,
            IConfiguration configuration,
            IGeoMapService geoMapService,
            UserManager<ApplicationUser> userManager)
        {
            _dbContext = dbContext;
            _logger = logger;
            _configuration = configuration;
            _geoMapService = geoMapService;
            _userManager = userManager;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return RedirectToAction("Login", "Account");
                }

                string layerDefs = "rg;on;label:Name,rv;off,vl;off";  // تعریف لایه‌ها

                // دریافت ساختار درختی لایه‌ها از سرویس
                var layerTreeResult = await _geoMapService.GetLayerTreeAsync(layerDefs, currentUser.Id);

                if (!layerTreeResult.IsSuccess)
                {
                    _logger.LogError("Error loading layer tree: {Message}", layerTreeResult.Message);
                    ViewBag.TreeData = "";
                }
                else
                {
                    ViewBag.TreeData = layerTreeResult.Data?.Html ?? "";
                    ViewBag.LayerGroups = layerTreeResult.Data?.Groups ?? new List<LayerGroupViewModel>();
                }

                ViewBag.Theme = "view_backcolor:#3b5998,view_color:#3b5998";
                ViewBag.CurrentUser = currentUser;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading GeoMap index");
                return View("Error");
            }
        }
        public static string GetTreeData(List<TableInfo> listLayer, string layerDefs, IConfiguration _configuration)
        {
            int mapIndex = 1;
            string layerDefNew = "";
            string html = "";
            var group = (listLayer).GroupBy(a => a.GroupInfo);

            foreach (var item in group)
            {
                bool existLayerinGrpup = true;

                if (existLayerinGrpup)
                {
                    html += "<li id = \"G" + item.Key.Id + "\" class=\"group expanded\">";
                    html += item.Key.AliasName;
                    html += "<ul>";
                    foreach (var table in item)
                    {
                        {
                            bool showLayerfromService = false;
                            bool editLayerFromService = false;
                            string lableLayerFromService = "";
                            string symbolLayerFromService = "";
                            if (table.DatasetType != "table" && CheckLayer(table.ShortName, layerDefs, out showLayerfromService, out editLayerFromService, out symbolLayerFromService, out lableLayerFromService))
                            {
                                if (symbolLayerFromService == null || symbolLayerFromService == "")
                                {
                                    DBManagement db = new DBManagement(_configuration);
                                    var dtSymbology = db.SelectTableSQL($"Select * From SymbologyInfos Where TableInfoId = {table.Id} order by IsDefault desc");
                                    if (dtSymbology.Rows.Count > 0)
                                    {
                                        symbolLayerFromService = dtSymbology.Rows[0]["ID"].ToString();
                                    }
                                }
                                existLayerinGrpup = true;
                                var idLayer = "1" + mapIndex.ToString("000000") + table.Id.ToString("0000");
                                var title = ("base" + ":" + table.ShortName).ToString();

                                string querydef = "";
                                if (CheckLayerDefs(table.ShortName, layerDefs, out querydef))
                                {
                                    layerDefNew += "\"" + idLayer + "\":\"" + querydef + "\",";
                                }
                                html += "<li id = \"L" + idLayer + "\" class=\"" + table.DatasetType.ToLower() + "\" title=\"" + table.AliasName + "\" key=\"L" + idLayer + "\"" + (showLayerfromService ? " data-selected='true' " : "") + ">";
                                html += "<cc>" + (table.AliasName.Length > 19 ? table.AliasName.Substring(0, 19) : table.AliasName) + "</cc>";
                                html += $"<input class=\"d-none\" type=\"checkbox\" id=\"C{idLayer}\" {(lableLayerFromService != "" && lableLayerFromService != null ? "checked" : "")}/>";
                                html += $"<input class=\"d-none\" type=\"text\" id=\"O{idLayer}\" />";
                                html += $"<input class=\"d-none\" type=\"text\" id=\"La{idLayer}\" value=\"{lableLayerFromService}\" />";
                                html += $"<input class=\"d-none\" type=\"text\" id=\"Sym{idLayer}\" value=\"{symbolLayerFromService}\" />";
                                html += "<ul>";
                                html += $"<li class=\"xx\" key=\"L{idLayer}_otherlegend\"> <cc>  <a href=\"#\" onclick=\"setSymbologyByID('{idLayer}',-1);\">...</a></cc></li>";
                                html += "</ul>";
                                html += "</li>";
                            }
                        }
                    }
                    html += "</ul>";
                    html += "</li>";
                }
            }
            return html;
        }

        public static bool CheckLayer(string layer, string layers, out bool show, out bool edit, out string symbolID, out string lable)
        {

            show = false;
            edit = false;
            symbolID = null;
            lable = null;
            if (layers == null || layers == "")
                return true;


            var items = layers.Split(',');
            for (int i = 0; i < items.Length; i++)
            {
                var lyr = items[i].Trim().Split(';');
                if (lyr[0].ToLower() == layer.ToLower())
                {

                    for (int ii = 1; ii < lyr.Length; ii++)
                    {
                        var splt = lyr[ii].Split(':');
                        if (lyr[ii].ToLower() == "on")
                            show = true;
                        else if (lyr[ii].ToLower() == "edit")
                            show = true;
                        else if (splt.Length > 1 && splt[0].ToLower() == "symbolid")
                        {
                            int jjj = 0;
                            if (int.TryParse(splt[1], out jjj))
                                symbolID = splt[1];
                        }
                        else if (splt.Length > 1 && splt[0].ToLower() == "label")
                            lable = lyr[ii].Split(':')[1];
                    }

                    return true;
                }
            }


            return false;
        }

        public static bool CheckLayerDefs(string layer, string layerDefs, out string query)
        {
            query = "";
            if (layerDefs == null || layerDefs == "")
                return false;


            var items = layerDefs.Split(';');
            for (int i = 0; i < items.Length; i++)
            {
                var item = items[i].Trim().Split(':');
                if (item.Length > 1 && item[0].ToLower() == layer.ToLower())
                {
                    query = item[1];
                    return true;
                }
            }
            return false;
        }

        public async Task<ActionResult> Property(string Layerid)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { ok = false, msg = "کاربر یافت نشد" });
                }

                if (!await _geoMapService.ValidateLayerAccessAsync(Layerid, currentUser.Id))
                {
                    return Json(new { ok = false, msg = "دسترسی غیرمجاز" });
                }

                var layerType = Layerid.Substring(0, 1);
                string tableId = Layerid[^4..];
                var tblID = int.Parse(tableId);

                ViewBag.LayerId = tblID;
                ViewBag.LayerType = layerType;

                if (layerType == "1")
                {
                    var symbologiesResult = await _geoMapService.GetSymbologiesAsync(tblID, currentUser.Id);
                    ViewBag.Symbolgies = symbologiesResult.IsSuccess ? symbologiesResult.Data : new List<SymbologyViewModel>();
                }

                return PartialView();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading layer properties for layer {LayerId}", Layerid);
                return PartialView();
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<JsonResult> GetLayerFields(string ID)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { ok = false, msg = "کاربر یافت نشد" });
                }

                var fieldsResult = await _geoMapService.GetLayerFieldsAsync(ID, currentUser.Id);

                if (fieldsResult.IsSuccess)
                {
                    var data = fieldsResult.Data?.Select(f => new { f.Name, f.AliasName }).ToList();
                    return Json(new { ok = true, data });
                }
                else
                {
                    return Json(new { ok = false, msg = fieldsResult.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting layer fields for layer {LayerId}", ID);
                return Json(new { ok = false, msg = "خطا در بارگذاری فیلدهای لایه" });
            }
        }

        public async Task<JsonResult> SearchTable(string search)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { ok = false, msg = "کاربر یافت نشد" });
                }

                var searchResult = await _geoMapService.SearchTablesAsync(search, currentUser.Id);

                if (searchResult.IsSuccess)
                {
                    return Json(new { ok = true, data = searchResult.Data });
                }
                else
                {
                    return Json(new { ok = false, msg = searchResult.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching tables with term {SearchTerm}", search);
                return Json(new { ok = false, msg = "خطا در جستجوی جداول" });
            }
        }

        // === ابزارهای نوار ابزار ===

        /// <summary>
        /// جستجوی سریع
        /// </summary>
        public async Task<IActionResult> QuickSearch()
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { ok = false, msg = "کاربر یافت نشد" });
                }

                ViewBag.CurrentUser = currentUser;
                return PartialView("_QuickSearch");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading quick search");
                return PartialView("_QuickSearch");
            }
        }

        /// <summary>
        /// انجام جستجوی سریع
        /// </summary>
        [HttpPost]
        public async Task<JsonResult> PerformQuickSearch([FromBody] QuickSearchRequest request)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { ok = false, msg = "کاربر یافت نشد" });
                }

                var result = await _geoMapService.QuickSearchAsync(request, currentUser.Id);

                if (result.IsSuccess)
                {
                    return Json(new { ok = true, data = result.Data });
                }
                else
                {
                    return Json(new { ok = false, msg = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing quick search");
                return Json(new { ok = false, msg = "خطا در جستجوی سریع" });
            }
        }

        /// <summary>
        /// جستجوی مکانی
        /// </summary>
        public async Task<IActionResult> SpatialSearch()
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { ok = false, msg = "کاربر یافت نشد" });
                }

                ViewBag.CurrentUser = currentUser;
                return PartialView("_SpatialSearch");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading spatial search");
                return PartialView("_SpatialSearch");
            }
        }

        /// <summary>
        /// انجام جستجوی مکانی
        /// </summary>
        [HttpPost]
        public async Task<JsonResult> PerformSpatialSearch([FromBody] SpatialSearchRequest request)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { ok = false, msg = "کاربر یافت نشد" });
                }

                var result = await _geoMapService.SpatialSearchAsync(request, currentUser.Id);

                if (result.IsSuccess)
                {
                    return Json(new { ok = true, data = result.Data });
                }
                else
                {
                    return Json(new { ok = false, msg = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing spatial search");
                return Json(new { ok = false, msg = "خطا در جستجوی مکانی" });
            }
        }

        /// <summary>
        /// جستجوی توصیفی
        /// </summary>
        public async Task<IActionResult> DescriptiveSearch()
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { ok = false, msg = "کاربر یافت نشد" });
                }

                ViewBag.CurrentUser = currentUser;
                return PartialView("_DescriptiveSearch");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading descriptive search");
                return PartialView("_DescriptiveSearch");
            }
        }

        /// <summary>
        /// انجام جستجوی توصیفی
        /// </summary>
        [HttpPost]
        public async Task<JsonResult> PerformDescriptiveSearch([FromBody] DescriptiveSearchRequest request)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { ok = false, msg = "کاربر یافت نشد" });
                }

                var result = await _geoMapService.DescriptiveSearchAsync(request, currentUser.Id);

                if (result.IsSuccess)
                {
                    return Json(new { ok = true, data = result.Data });
                }
                else
                {
                    return Json(new { ok = false, msg = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing descriptive search");
                return Json(new { ok = false, msg = "خطا در جستجوی توصیفی" });
            }
        }

        /// <summary>
        /// گزارش آماری
        /// </summary>
        public async Task<IActionResult> StatisticalReport()
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { ok = false, msg = "کاربر یافت نشد" });
                }

                ViewBag.CurrentUser = currentUser;
                return PartialView("_StatisticalReport");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading statistical report");
                return PartialView("_StatisticalReport");
            }
        }

        /// <summary>
        /// تولید گزارش آماری
        /// </summary>
        [HttpPost]
        public async Task<JsonResult> GenerateStatisticalReport([FromBody] StatisticalReportRequest request)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { ok = false, msg = "کاربر یافت نشد" });
                }

                var result = await _geoMapService.GenerateStatisticalReportAsync(request, currentUser.Id);

                if (result.IsSuccess)
                {
                    return Json(new { ok = true, data = result.Data });
                }
                else
                {
                    return Json(new { ok = false, msg = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating statistical report");
                return Json(new { ok = false, msg = "خطا در تولید گزارش آماری" });
            }
        }

        /// <summary>
        /// گزارش ساز
        /// </summary>
        public async Task<IActionResult> ReportBuilder()
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { ok = false, msg = "کاربر یافت نشد" });
                }

                ViewBag.CurrentUser = currentUser;
                return PartialView("_ReportBuilder");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading report builder");
                return PartialView("_ReportBuilder");
            }
        }

        /// <summary>
        /// ایمپورت فایل SHP
        /// </summary>
        public async Task<IActionResult> ImportShp()
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { ok = false, msg = "کاربر یافت نشد" });
                }

                ViewBag.CurrentUser = currentUser;
                return PartialView("_ImportShp");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading import shapefile");
                return PartialView("_ImportShp");
            }
        }

        /// <summary>
        /// انجام ایمپورت فایل Shapefile
        /// </summary>
        [HttpPost]
        public async Task<JsonResult> PerformImportShapefile([FromForm] ImportShapefileRequest request)
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { ok = false, msg = "کاربر یافت نشد" });
                }

                var result = await _geoMapService.ImportShapefileAsync(request, currentUser.Id);

                if (result.IsSuccess)
                {
                    return Json(new { ok = true, data = result.Data });
                }
                else
                {
                    return Json(new { ok = false, msg = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing shapefile");
                return Json(new { ok = false, msg = "خطا در ایمپورت فایل Shapefile" });
            }
        }

        /// <summary>
        /// ابزار چاپ
        /// </summary>
        public async Task<IActionResult> PrintTools()
        {
            try
            {
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser == null)
                {
                    return Json(new { ok = false, msg = "کاربر یافت نشد" });
                }

                ViewBag.CurrentUser = currentUser;
                return PartialView("_PrintTools");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading print tools");
                return PartialView("_PrintTools");
            }
        }
    }
}