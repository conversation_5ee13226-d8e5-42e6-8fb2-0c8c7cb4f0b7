using BaseGIS.Application.UseCases;
using BaseGIS.Core.Entities;
using BaseGIS.Core.Interfaces;
using BaseGIS.Infrastructure.Persistence;
using BaseGIS.Infrastructure.Repositories;
using BaseGIS.Web.Hubs;
using BaseGIS.Web.Services;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using System.Reflection;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Load SQL Server native assemblies
var nativeAssembliesPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SqlServerTypes");
if (Directory.Exists(nativeAssembliesPath))
{
    var files = Directory.GetFiles(nativeAssembliesPath, "*.dll");
    foreach (var file in files)
    {
        Assembly.LoadFrom(file);
    }
}

// اضافه کردن سرویس‌های MVC
builder.Services.AddControllersWithViews();

// ثبت DbContext
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"),
        sqlServerOptions =>
        {
            sqlServerOptions.UseNetTopologySuite();
            sqlServerOptions.EnableRetryOnFailure(
                maxRetryCount: 5,
                maxRetryDelay: TimeSpan.FromSeconds(30),
                errorNumbersToAdd: null);
        }));

// Add Identity
builder.Services.AddIdentity<ApplicationUser, ApplicationRole>(options =>
{
    options.Password.RequireDigit = false;
    options.Password.RequireLowercase = false;
    options.Password.RequireUppercase = false;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequiredLength = 4;
    options.SignIn.RequireConfirmedAccount = false;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders();


// Add JWT bearer authentication
builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = CookieAuthenticationDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = CookieAuthenticationDefaults.AuthenticationScheme;
    options.DefaultSignInScheme = "Cookies";
})
.AddCookie(options =>
{
    options.Cookie.Name = "BaseGISAuth";
    options.Cookie.SameSite = SameSiteMode.None;
    options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
    options.Cookie.HttpOnly = true;

    options.LoginPath = "/Account/Login";
    options.LogoutPath = "/Account/Logout";
    options.AccessDeniedPath = "/Account/AccessDenied";
    options.ExpireTimeSpan = TimeSpan.FromDays(14);
    options.SlidingExpiration = true;

})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = builder.Configuration["Jwt:Issuer"],
        ValidAudience = builder.Configuration["Jwt:Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"] ??
            throw new InvalidOperationException("Jwt:Key is not configured")))
    };
});

builder.Host.UseSerilog((ctx, lc) => lc
    .WriteTo.Console()
    .WriteTo.File("logs/webgis-.log", rollingInterval: RollingInterval.Day));

// ثبت Repositoryها
builder.Services.AddScoped<ITableInfoRepository, TableInfoRepository>();
builder.Services.AddScoped<IFieldInfoRepository, FieldInfoRepository>();
builder.Services.AddScoped<ISymbologyInfoRepository, SymbologyInfoRepository>();

// Add UserManager
builder.Services.AddScoped<UserManager<ApplicationUser>>();

// ثبت Use Caseها
builder.Services.AddScoped<CreateTableInfoUseCase>();
builder.Services.AddScoped<GetTableInfoUseCase>();
builder.Services.AddScoped<CreateFieldInfoUseCase>();
builder.Services.AddScoped<GetFieldInfoUseCase>();
builder.Services.AddScoped<CreateSymbologyInfoUseCase>();
builder.Services.AddScoped<GetSymbologyInfoUseCase>();

builder.Services.AddScoped<BaseGIS.Core.Services.IDdlService, BaseGIS.Core.Services.DdlService>();
builder.Services.AddHostedService<TileGenerationBackgroundService>();
builder.Services.AddSingleton<TileGenerationBackgroundService>();
builder.Services.AddHostedService<TileCleanupService>();

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", builder =>
    {
        builder.AllowAnyOrigin()
               .AllowAnyMethod()
               .AllowAnyHeader();
    });
});

builder.Services.AddMemoryCache();

// Add SignalR services
builder.Services.AddSignalR();

var app = builder.Build();

// Add lifetime logging
var lifetime = app.Services.GetRequiredService<IHostApplicationLifetime>();
var logger = app.Services.GetRequiredService<ILogger<Program>>();

lifetime.ApplicationStarted.Register(() =>
{
    logger.LogInformation("The application has started");
});

lifetime.ApplicationStopping.Register(() =>
{
    logger.LogInformation("The application is stopping");
});

lifetime.ApplicationStopped.Register(() =>
{
    logger.LogInformation("The application has stopped");
});

// Seed Data
try
{
    using (var scope = app.Services.CreateScope())
    {
        var services = scope.ServiceProvider;
        var dbContext = services.GetRequiredService<ApplicationDbContext>();
        var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
        var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();

        // Ensure database is created
        await dbContext.Database.EnsureCreatedAsync();

        // Now pass all required services to Initialize
        await BaseGIS.Infrastructure.Persistence.SeedData.Initialize(services, logger);
        logger.LogInformation("Database seeding completed");
    }
}
catch (Exception ex)
{
    logger.LogError(ex, "An error occurred while seeding the database");
    // Don't throw the exception - log it and continue
}

// تنظیمات Middleware
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseCors("AllowAll");
app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();
app.MapHub<FeaturesHub>("/featuresHub");

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();