﻿using BaseGIS.Core.Entities;
using BaseGIS.Web.Utilities;
using BaseGIS.Web.ViewModels;
using Mapsui.Styles;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;

public static class MapsuiLegendGenerator
{
    public static List<LegendItem> GenerateLegend(string json, TableInfo tableInfo, string symbologyFieldName)
    {
        var legendItems = new List<LegendItem>();

        if (string.IsNullOrEmpty(json))
        {
            Console.WriteLine("Symbology JSON is empty. Generating default legend item.");
            legendItems.Add(CreateDefaultLegendItem(tableInfo));
            return legendItems;
        }

        try
        {
            // 1. دی‌سیریالایز JSON به شیء Symbology
            var settings = new JsonSerializerSettings
            {
                TypeNameHandling = TypeNameHandling.Auto,
                NullValueHandling = NullValueHandling.Ignore
            };
            var symbologyDefinition = JsonConvert.DeserializeObject<Symbology>(json, settings);
            var symsObject = JsonConvert.DeserializeObject(json);

            if (symbologyDefinition == null || symbologyDefinition.Categories == null || !symbologyDefinition.Categories.Any())
            {
                Console.WriteLine("Deserialized symbology is empty or has no categories. Generating default legend item.");
                legendItems.Add(CreateDefaultLegendItem(tableInfo));
                return legendItems;
            }

            // 2. پردازش دسته‌بندی‌ها بر اساس نوع هندسه
            switch (symbologyDefinition.GeometryType)
            {
                case GeometryType.Point:
                    legendItems.AddRange(ProcessPointCategories(symbologyDefinition, symsObject, tableInfo));
                    break;
                case GeometryType.Polyline:
                    legendItems.AddRange(ProcessLineCategories(symbologyDefinition, symsObject, tableInfo));
                    break;
                case GeometryType.Polygon:
                    legendItems.AddRange(ProcessPolygonCategories(symbologyDefinition, symsObject, tableInfo));
                    break;
                default:
                    Console.WriteLine($"Unsupported geometry type: {symbologyDefinition.GeometryType}. Generating default legend item.");
                    legendItems.Add(CreateDefaultLegendItem(tableInfo));
                    break;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error generating legend from JSON: {ex.Message}");
            legendItems.Add(CreateDefaultLegendItem(tableInfo));
        }

        return legendItems;
    }

    private static IEnumerable<LegendItem> ProcessPointCategories(BaseGIS.Web.ViewModels.Symbology symbology, object symsObject, TableInfo tableInfo)
    {
        var legendItems = new List<LegendItem>();
        var categories = symbology.Categories;

        for (int i = 0; i < categories.Count; i++)
        {
            var catObject = ((JContainer)symsObject)["Categories"][i];
            var pointCat = JsonConvert.DeserializeObject<PointCategory>(catObject.ToString());

            if (pointCat.Symbol == null)
            {
                continue;
            }

            IStyle style = null;
            string label = GetLegendLabel(pointCat.FilterExpression, tableInfo.Name, i);

            switch (pointCat.Symbol.SymbolType)
            {
                case BaseGIS.Web.ViewModels.SymbolType.Simple:
                    var simpleSymbol = JsonConvert.DeserializeObject<SimpleSymbol>(catObject["Symbol"].ToString());
                    style = MapsuiSymbologyConverter.CreateMapsuiStyleFromDotSpatialSymbol(simpleSymbol);
                    break;
                case BaseGIS.Web.ViewModels.SymbolType.Character:
                    var charSymbol = JsonConvert.DeserializeObject<CharacterSymbol>(catObject["Symbol"].ToString());
                    style = MapsuiSymbologyConverter.CreateMapsuiStyleFromDotSpatialSymbol(charSymbol);
                    break;
                case BaseGIS.Web.ViewModels.SymbolType.Picture:
                    var pictureSymbol = JsonConvert.DeserializeObject<PictureSymbol>(catObject["Symbol"].ToString());
                    style = MapsuiSymbologyConverter.CreateMapsuiStyleFromDotSpatialSymbol(pictureSymbol);
                    break;
            }

            if (style != null)
            {
                legendItems.Add(RenderStyleToLegendItem(style, label));
            }
        }

        return legendItems.Any() ? legendItems : new List<LegendItem> { CreateDefaultLegendItem(tableInfo) };
    }

    private static IEnumerable<LegendItem> ProcessLineCategories(BaseGIS.Web.ViewModels.Symbology symbology, object symsObject, TableInfo tableInfo)
    {
        var legendItems = new List<LegendItem>();
        var categories = symbology.Categories;

        for (int i = 0; i < categories.Count; i++)
        {
            var catObject = ((JContainer)symsObject)["Categories"][i];
            var lineCat = JsonConvert.DeserializeObject<LineCategory>(catObject.ToString());

            if (lineCat.Outline == null)
            {
                continue;
            }

            var style = MapsuiSymbologyConverter.CreateMapsuiStyleFromLineCategory(lineCat);
            var label = GetLegendLabel(lineCat.FilterExpression, tableInfo.Name, i);

            if (style != null)
            {
                legendItems.Add(RenderStyleToLegendItem(style, label));
            }
        }

        return legendItems.Any() ? legendItems : new List<LegendItem> { CreateDefaultLegendItem(tableInfo) };
    }

    private static IEnumerable<LegendItem> ProcessPolygonCategories(BaseGIS.Web.ViewModels.Symbology symbology, object symsObject, TableInfo tableInfo)
    {
        var legendItems = new List<LegendItem>();
        var categories = symbology.Categories;

        for (int i = 0; i < categories.Count; i++)
        {
            var catObject = ((JContainer)symsObject)["Categories"][i];
            var polygonCat = JsonConvert.DeserializeObject<PolygonCategory>(catObject.ToString());

            if (polygonCat.Pattern == null)
            {
                continue;
            }

            IStyle style = null;
            string label = GetLegendLabel(polygonCat.FilterExpression, tableInfo.Name, i);

            switch (polygonCat.Pattern.PatType)
            {
                case PatType.Simple:
                    var simplePattern = JsonConvert.DeserializeObject<SimplePattern>(catObject["Pattern"].ToString());
                    style = MapsuiSymbologyConverter.GetPolygonSimplePattern(simplePattern);
                    break;
                case PatType.Gradient:
                    var gradientPattern = JsonConvert.DeserializeObject<GradientPattern>(catObject["Pattern"].ToString());
                    var firstColor = gradientPattern.Colors?.Length > 0
                        ? MapsuiSymbologyConverter.ToMapsuiColor(gradientPattern.Colors[0])
                        : Mapsui.Styles.Color.Gray;
                    style = new VectorStyle
                    {
                        Fill = new Brush(firstColor),
                        Outline = MapsuiSymbologyConverter.CreateMapsuiPenFromOutline(polygonCat.Outline)
                    };
                    break;
                case PatType.Hatch:
                    var hatchPattern = JsonConvert.DeserializeObject<HatchPattern>(catObject["Pattern"].ToString());
                    var hatchColor = MapsuiSymbologyConverter.HexToMapsuiColor(hatchPattern.ForeColor);
                    hatchColor.A = MapsuiSymbologyConverter.ConvertDotSpatialOpacityToMapsuiAlpha(hatchPattern.ForeColorOpacity);
                    style = new VectorStyle
                    {
                        Fill = new Brush(hatchColor),
                        Outline = MapsuiSymbologyConverter.CreateMapsuiPenFromOutline(polygonCat.Outline)
                    };
                    break;
                case PatType.Picture:
                    var picturePattern = JsonConvert.DeserializeObject<PicturePattern>(catObject["Pattern"].ToString());
                    style = MapsuiSymbologyConverter.CreateMapsuiStyleFromDotSpatialPattern(picturePattern);
                    break;
            }

            if (style != null)
            {
                legendItems.Add(RenderStyleToLegendItem(style, label));
            }
        }

        return legendItems.Any() ? legendItems : new List<LegendItem> { CreateDefaultLegendItem(tableInfo) };
    }

    private static LegendItem RenderStyleToLegendItem(IStyle style, string label)
    {
        try
        {
            // Mapsui برای تولید آیکون لجند از متد داخلی استفاده می‌کند
            using (var stream = new MemoryStream())
            {
                // فرض می‌کنیم Mapsui متدی برای تولید بیت‌مپ لجند دارد
                // اگر Mapsui متد CreateLegendBitmap ندارد، باید خودمان رندر کنیم
                var bitmap = RenderStyleToBitmap(style);
                bitmap.Save(stream, System.Drawing.Imaging.ImageFormat.Png);
                byte[] byteImage = stream.ToArray();
                string base64Image = Convert.ToBase64String(byteImage);

                return new LegendItem
                {
                    Label = label,
                    ImageData = "data:image/png;base64," + base64Image,
                    ImageWidth = 24,
                    ImageHeight = 24
                };
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error rendering legend item: {ex.Message}");
            return new LegendItem
            {
                Label = label,
                ImageData = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAKSURBVHgBA8gBAAD///8J9N3XAAAAAElFTkSuQmCC", // تصویر خاکستری پیش‌فرض
                ImageWidth = 24,
                ImageHeight = 24
            };
        }
    }

    private static System.Drawing.Bitmap RenderStyleToBitmap(IStyle style)
    {
        // ایجاد یک بوم خالی برای رندر استایل
        var bitmap = new System.Drawing.Bitmap(24, 24);
        using (var graphics = System.Drawing.Graphics.FromImage(bitmap))
        {
            graphics.Clear(System.Drawing.Color.Transparent);

            if (style is SymbolStyle symbolStyle)
            {
                var center = new System.Drawing.PointF(12, 12);
                float scale = (float)(symbolStyle.SymbolScale * 12);

                // تعریف points برای مثلث، که در هر دو بخش Fill و Outline استفاده می‌شود
                System.Drawing.PointF[] points = null;
                if (symbolStyle.SymbolType == Mapsui.Styles.SymbolType.Triangle)
                {
                    points = new[]
                    {
                        new System.Drawing.PointF(center.X, center.Y - scale),
                        new System.Drawing.PointF(center.X - scale, center.Y + scale),
                        new System.Drawing.PointF(center.X + scale, center.Y + scale)
                    };
                }

                // رندر پر کردن (Fill)
                if (symbolStyle.Fill != null)
                {
                    var brush = new System.Drawing.SolidBrush(
                        System.Drawing.Color.FromArgb(symbolStyle.Fill.Color.A,
                        symbolStyle.Fill.Color.R,
                        symbolStyle.Fill.Color.G,
                        symbolStyle.Fill.Color.B));

                    switch (symbolStyle.SymbolType)
                    {
                        case Mapsui.Styles.SymbolType.Ellipse:
                            graphics.FillEllipse(brush, center.X - scale, center.Y - scale, scale * 2, scale * 2);
                            break;
                        case Mapsui.Styles.SymbolType.Rectangle:
                            graphics.FillRectangle(brush, center.X - scale, center.Y - scale, scale * 2, scale * 2);
                            break;
                        case Mapsui.Styles.SymbolType.Triangle:
                            graphics.FillPolygon(brush, points);
                            break;
                    }
                    brush.Dispose();
                }

                // رندر خط مرزی (Outline)
                if (symbolStyle.Outline != null)
                {
                    var pen = new System.Drawing.Pen(
                        System.Drawing.Color.FromArgb(symbolStyle.Outline.Color.A,
                        symbolStyle.Outline.Color.R,
                        symbolStyle.Outline.Color.G,
                        symbolStyle.Outline.Color.B),
                        (float)symbolStyle.Outline.Width);

                    switch (symbolStyle.SymbolType)
                    {
                        case Mapsui.Styles.SymbolType.Ellipse:
                            graphics.DrawEllipse(pen, center.X - scale, center.Y - scale, scale * 2, scale * 2);
                            break;
                        case Mapsui.Styles.SymbolType.Rectangle:
                            graphics.DrawRectangle(pen, center.X - scale, center.Y - scale, scale * 2, scale * 2);
                            break;
                        case Mapsui.Styles.SymbolType.Triangle:
                            graphics.DrawPolygon(pen, points);
                            break;
                    }
                    pen.Dispose();
                }
            }
            else if (style is VectorStyle vectorStyle)
            {
                // رندر خط یا پلی‌گان
                if (vectorStyle.Fill != null)
                {
                    var brush = new System.Drawing.SolidBrush(
                        System.Drawing.Color.FromArgb(vectorStyle.Fill.Color.A, vectorStyle.Fill.Color.R,
                        vectorStyle.Fill.Color.G, vectorStyle.Fill.Color.B));
                    graphics.FillRectangle(brush, 0, 0, 24, 24);
                    brush.Dispose();
                }

                if (vectorStyle.Outline != null)
                {
                    var pen = new System.Drawing.Pen(
                        System.Drawing.Color.FromArgb(vectorStyle.Outline.Color.A, vectorStyle.Outline.Color.R,
                        vectorStyle.Outline.Color.G, vectorStyle.Outline.Color.B),
                        (float)vectorStyle.Outline.Width);
                    graphics.DrawRectangle(pen, 0, 0, 23, 23);
                    pen.Dispose();
                }

                if (vectorStyle.Line != null)
                {
                    var pen = new System.Drawing.Pen(
                        System.Drawing.Color.FromArgb(vectorStyle.Line.Color.A, vectorStyle.Line.Color.R,
                        vectorStyle.Line.Color.G, vectorStyle.Line.Color.B),
                        (float)vectorStyle.Line.Width);
                    graphics.DrawLine(pen, 0, 12, 24, 12);
                    pen.Dispose();
                }
            }
        }

        return bitmap;
    }

    private static LegendItem CreateDefaultLegendItem(TableInfo tableInfo)
    {
        var defaultStyle = MapsuiSymbologyConverter.CreateDefaultMapsuiStyle(tableInfo.DatasetType);
        return RenderStyleToLegendItem(defaultStyle, tableInfo.Name);
    }

    private static string GetLegendLabel(string filterExpression, string defaultLabel, int index)
    {
        if (string.IsNullOrEmpty(filterExpression))
        {
            return $"{defaultLabel} ({index + 1})";
        }

        // استخراج برچسب از FilterExpression
        if (MapsuiSymbologyConverter.TryParseQuantityRange(filterExpression, out double from, out double to))
        {
            return $"از {from} تا {to}";
        }

        var equalityMatch = Regex.Match(filterExpression, @"\[(.*?)\]\s*(=|<|>|!=)\s*['""]?([^'""\s]+)['""]?");
        if (equalityMatch.Success)
        {
            var value = equalityMatch.Groups[3].Value;
            var op = equalityMatch.Groups[2].Value;
            return op == "=" ? value : $"{op} {value}";
        }

        var likeMatch = Regex.Match(filterExpression, @"\[(.*?)\]\s*LIKE\s*['""]?(.+?)['""]?");
        if (likeMatch.Success)
        {
            return likeMatch.Groups[2].Value.Replace("%", "*");
        }

        return filterExpression;
    }
}
public class LegendItem
{
    public string Label { get; set; }
    public string ImageData { get; set; } // Base64 encoded PNG
    public int ImageWidth { get; set; }
    public int ImageHeight { get; set; }
}