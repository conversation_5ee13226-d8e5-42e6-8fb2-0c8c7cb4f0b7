﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BaseGIS.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateUserSelectTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "ValidationRule",
                table: "TableInfos",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "FieldName",
                table: "SymbologyInfos",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "FieldAlias",
                table: "SymbologyInfos",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.CreateTable(
                name: "UserSelects",
                columns: table => new
                {
                    ID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserID = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: false),
                    PageID = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Table = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    OIDs = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Date = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsHighlight = table.Column<bool>(type: "bit", nullable: false),
                    Where = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LayerId = table.Column<int>(type: "int", nullable: true),
                    SelectionType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    SelectionMetadata = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserSelects", x => x.ID);
                    table.ForeignKey(
                        name: "FK_UserSelects_TableInfos_LayerId",
                        column: x => x.LayerId,
                        principalTable: "TableInfos",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_UserSelects_LayerId",
                table: "UserSelects",
                column: "LayerId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UserSelects");

            migrationBuilder.AlterColumn<string>(
                name: "ValidationRule",
                table: "TableInfos",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "FieldName",
                table: "SymbologyInfos",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "FieldAlias",
                table: "SymbologyInfos",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);
        }
    }
}
