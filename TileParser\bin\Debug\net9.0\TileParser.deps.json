{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"TileParser/1.0.0": {"dependencies": {"Mapbox.VectorTile": "1.0.4-alpha2"}, "runtime": {"TileParser.dll": {}}}, "Mapbox.VectorTile/1.0.4-alpha2": {"runtime": {"lib/net35/Mapbox.VectorTile.ExtensionMethods.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.2.0"}, "lib/net35/Mapbox.VectorTile.Geometry.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.2.0"}, "lib/net35/Mapbox.VectorTile.PbfReader.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.2.0"}, "lib/net35/Mapbox.VectorTile.VectorTileReader.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.2.0"}}}}}, "libraries": {"TileParser/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Mapbox.VectorTile/1.0.4-alpha2": {"type": "package", "serviceable": true, "sha512": "sha512-fwK+WL1cueszQ+OO2m5g0HyaYf6ErsKH7dW6jVDc6qUKW8pMwBB0GaeHSCAd1QRFcUJ+/bGQM1sg4Xml85FHxg==", "path": "mapbox.vectortile/1.0.4-alpha2", "hashPath": "mapbox.vectortile.1.0.4-alpha2.nupkg.sha512"}}}