import { BaseComponent } from '../components/base-component.js';

class MapViewsComponent extends BaseComponent {
    getDefaultOptions() {
        return {
            ...super.getDefaultOptions(),
            api: {
                list: '/GeoMap/GetAllMapViews',
                get: '/GeoMap/GetMapView',
                save: '/GeoMap/SaveMapView',
                delete: '/GeoMap/DeleteMapView'
            },
            onMapViewLoad: null, // (mapView) => {}
            onMapViewEdit: null, // (mapView) => {}
            onMapViewDelete: null, // (id) => {}
            onMapViewSave: null // (data) => {}
        };
    }

    init() {
        this.render();
        this.loadMapViews();
    }

    render() {
        this.element.innerHTML = `
            <div class="views-toolbar mb-2 d-flex gap-2">
                <button type="button" class="btn btn-primary btn-sm" data-action="add">
                    <i class="fa fa-plus"></i> ذخیره نقشه جدید
                </button>
                <button type="button" class="btn btn-secondary btn-sm" data-action="refresh">
                    <i class="fa fa-refresh"></i> بروزرسانی
                </button>
            </div>
            <div id="map-views-list" class="views-list">
                <div class="loading-placeholder">
                    <i class="fa fa-spinner fa-spin"></i> در حال بارگذاری...
                </div>
            </div>
        `;
        this.element.querySelector('[data-action="add"]').onclick = () => this.showSaveDialog();
        this.element.querySelector('[data-action="refresh"]').onclick = () => this.loadMapViews();
    }

    async loadMapViews() {
        const listContainer = this.element.querySelector('#map-views-list');
        listContainer.innerHTML = `<div class="loading-placeholder"><i class="fa fa-spinner fa-spin"></i> در حال بارگذاری...</div>`;
        try {
            const response = await fetch(this.options.api.list);
            const result = await response.json();
            if (result.success) {
                this.displayMapViews(result.data);
            } else {
                this.displayMapViews([]);
                this.showToast(result.message || 'خطا در بارگذاری نقشه‌ها', 'danger');
            }
        } catch (error) {
            this.displayMapViews([]);
            this.showToast('خطا در بارگذاری نقشه‌ها', 'danger');
        }
    }

    displayMapViews(mapViews) {
        const container = this.element.querySelector('#map-views-list');
        if (!mapViews || mapViews.length === 0) {
            container.innerHTML = `
                <div class="no-views-message">
                    <i class="fa fa-map-o"></i><br>
                    هیچ نقشه ذخیره شده‌ای یافت نشد
                </div>
            `;
            return;
        }
        // گروه‌بندی بر اساس GroupName
        const groupedViews = mapViews.reduce((groups, view) => {
            const group = view.GroupName || 'بدون گروه';
            if (!groups[group]) groups[group] = [];
            groups[group].push(view);
            return groups;
        }, {});
        let html = '';
        Object.keys(groupedViews).sort().forEach(groupName => {
            html += `<div class="view-group">`;
            html += `<h6 class="view-group-title">${groupName}</h6>`;
            groupedViews[groupName].forEach(view => {
                html += this.createViewItemHtml(view);
            });
            html += `</div>`;
        });
        container.innerHTML = html;
        // اتصال رویدادها
        container.querySelectorAll('.btn-view-load').forEach(btn => {
            btn.onclick = e => this.loadMapView(btn.dataset.id);
        });
        container.querySelectorAll('.btn-view-edit').forEach(btn => {
            btn.onclick = e => this.showSaveDialog(btn.dataset.id);
        });
        container.querySelectorAll('.btn-view-delete').forEach(btn => {
            btn.onclick = e => this.deleteMapView(btn.dataset.id);
        });
    }

    createViewItemHtml(view) {
        const isPublic = view.Public ? '<i class="fa fa-globe text-success" title="عمومی"></i>' : '<i class="fa fa-lock text-muted" title="خصوصی"></i>';
        const hasZoom = view.Zoom ? '<i class="fa fa-search-plus text-info" title="دارای zoom"></i>' : '';
        return `
            <div class="view-item" data-view-id="${view.ID}">
                <div class="view-item-header">
                    <h6 class="view-item-title">${view.Name}</h6>
                    <div class="view-item-actions">
                        <button type="button" class="btn btn-primary btn-view-load" data-id="${view.ID}" title="بارگذاری نقشه">
                            <i class="fa fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-warning btn-view-edit" data-id="${view.ID}" title="ویرایش">
                            <i class="fa fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-danger btn-view-delete" data-id="${view.ID}" title="حذف">
                            <i class="fa fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="view-item-meta">
                    ${isPublic} ${hasZoom} توسط: ${view.UserName} | تاریخ: ${view.Date}
                </div>
                ${view.Description ? `<div class="view-item-description">${view.Description}</div>` : ''}
            </div>
        `;
    }

    async loadMapView(id) {
        try {
            this.showToast('در حال بارگذاری نقشه...', 'info');
            const response = await fetch(`${this.options.api.get}?id=${id}`);
            const result = await response.json();
            if (result.success) {
                if (typeof this.options.onMapViewLoad === 'function') {
                    this.options.onMapViewLoad(result.data);
                }
                this.showToast(`نقشه "${result.data.Name}" بارگذاری شد`, 'success');
            } else {
                this.showToast(result.message || 'خطا در بارگذاری نقشه', 'danger');
            }
        } catch (error) {
            this.showToast('خطا در بارگذاری نقشه', 'danger');
        }
    }

    async deleteMapView(id) {
        if (!confirm('آیا از حذف این نقشه اطمینان دارید؟')) return;
        try {
            const formData = new FormData();
            const token = document.querySelector('input[name="__RequestVerificationToken"]');
            if (token) formData.append('__RequestVerificationToken', token.value);
            const response = await fetch(`${this.options.api.delete}?id=${id}`, {
                method: 'POST',
                body: formData
            });
            const result = await response.json();
            if (result.success) {
                this.showToast(result.message, 'success');
                this.loadMapViews();
                if (typeof this.options.onMapViewDelete === 'function') {
                    this.options.onMapViewDelete(id);
                }
            } else {
                this.showToast(result.message || 'خطا در حذف نقشه', 'danger');
            }
        } catch (error) {
            this.showToast('خطا در حذف نقشه', 'danger');
        }
    }

    showSaveDialog(editId = null) {
        // اگر editId وجود دارد، اطلاعات را بارگذاری کن
        if (editId) {
            this.loadMapViewForEdit(editId);
        } else {
            this.renderSaveDialog();
        }
    }

    async loadMapViewForEdit(id) {
        try {
            const response = await fetch(`${this.options.api.get}?id=${id}`);
            const result = await response.json();
            if (result.success) {
                this.renderSaveDialog(result.data);
            } else {
                this.showToast(result.message || 'خطا در بارگذاری اطلاعات نقشه', 'danger');
            }
        } catch (error) {
            this.showToast('خطا در بارگذاری اطلاعات نقشه', 'danger');
        }
    }

    renderSaveDialog(mapView = null) {
        // ساختار modal ذخیره/ویرایش نقشه
        const isEdit = !!mapView;
        const modalId = 'saveMapViewModal';
        let modal = document.getElementById(modalId);
        if (modal) modal.remove();
        const html = `
            <div class="modal fade" id="${modalId}" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${isEdit ? 'ویرایش نقشه' : 'ذخیره نقشه جدید'}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="saveMapViewForm">
                                <input type="hidden" name="Id" value="${mapView ? mapView.ID : 0}">
                                <input type="hidden" name="__RequestVerificationToken" value="${document.querySelector('input[name=__RequestVerificationToken]')?.value || ''}">
                                <div class="mb-3">
                                    <label for="mapViewName" class="form-label">نام نقشه *</label>
                                    <input type="text" class="form-control" id="mapViewName" name="Name" required value="${mapView ? mapView.Name : ''}">
                                </div>
                                <div class="mb-3">
                                    <label for="mapViewDescription" class="form-label">توضیحات</label>
                                    <textarea class="form-control" id="mapViewDescription" name="Description" rows="3">${mapView ? mapView.Description || '' : ''}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="mapViewGroup" class="form-label">گروه</label>
                                    <input type="text" class="form-control" id="mapViewGroup" name="GroupName" placeholder="نام گروه (اختیاری)" value="${mapView ? mapView.GroupName || '' : ''}">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="saveCurrentExtent" name="Zoom" ${mapView && mapView.Zoom ? 'checked' : ''}>
                                        <label class="form-check-label" for="saveCurrentExtent">ذخیره محدوده فعلی نقشه</label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="makePublic" name="Public" ${mapView && mapView.Public ? 'checked' : ''}>
                                        <label class="form-check-label" for="makePublic">نقشه عمومی (قابل مشاهده برای همه کاربران)</label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                            <button type="button" class="btn btn-primary" id="btnSaveMapView">
                                <i class="fa fa-save"></i> ${isEdit ? 'بروزرسانی' : 'ذخیره'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', html);
        modal = document.getElementById(modalId);
        modal.querySelector('#btnSaveMapView').onclick = () => this.saveMapView();
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }

    async saveMapView() {
        try {
            const form = document.getElementById('saveMapViewForm');
            const formData = new FormData(form);
            // اضافه کردن اطلاعات لایه‌ها و گرافیک‌ها و BaseMapID از سایر ابزارها
            if (window.layerTreeComponent && typeof window.layerTreeComponent.getCurrentLayersState === 'function') {
                formData.append('Layers', JSON.stringify(window.layerTreeComponent.getCurrentLayersState()));
            }
            if (window.drawingTools && typeof window.drawingTools.getCurrentGraphicsState === 'function') {
                formData.append('Graphic', JSON.stringify(window.drawingTools.getCurrentGraphicsState()));
            }
            if (document.getElementById('saveCurrentExtent').checked && window.geoMapCore && window.geoMapCore.map) {
                const bounds = window.geoMapCore.map.getBounds();
                const bbox = {
                    xmin: bounds.getWest(),
                    ymin: bounds.getSouth(),
                    xmax: bounds.getEast(),
                    ymax: bounds.getNorth()
                };
                formData.append('BBX', JSON.stringify(bbox));
            }
            if (window.baseMapManager && typeof window.baseMapManager.getCurrentBaseMap === 'function') {
                formData.append('BaseMapID', window.baseMapManager.getCurrentBaseMap().index);
            }
            const response = await fetch(this.options.api.save, {
                method: 'POST',
                body: formData
            });
            const result = await response.json();
            if (result.success) {
                this.showToast(result.message, 'success');
                document.getElementById('saveMapViewModal')?.remove();
                this.loadMapViews();
                if (typeof this.options.onMapViewSave === 'function') {
                    this.options.onMapViewSave(result.data);
                }
            } else {
                this.showToast(result.message || 'خطا در ذخیره نقشه', 'danger');
            }
        } catch (error) {
            this.showToast('خطا در ذخیره نقشه', 'danger');
        }
    }

    showToast(message, type = 'info', duration = 3000) {
        if (window.showToast) {
            window.showToast(message, type, duration);
        } else {
            // fallback
            alert(message);
        }
    }
}

window.ComponentFactory.register('map-views', MapViewsComponent); 