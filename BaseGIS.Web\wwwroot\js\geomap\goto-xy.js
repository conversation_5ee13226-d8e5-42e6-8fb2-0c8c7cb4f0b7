/**
 * GoTo XY Component
 * کامپوننت رفتن به مختصات
 */

class GoToXYComponent extends BaseComponent {
    getDefaultOptions() {
        return {
            ...super.getDefaultOptions(),
            coordinateSystems: ['geographic', 'mercator', 'utm38', 'utm39', 'utm40', 'utm41'],
            defaultCoordinateSystem: 'geographic',
            markerStyle: {
                color: '#ff0000',
                fillColor: '#ff0000',
                fillOpacity: 0.8,
                radius: 8,
                weight: 2
            },
            markerDuration: 5000, // مدت نمایش مارکر (میلی‌ثانیه)
            zoomLevel: 15,
            showMarker: true,
            animateToLocation: true,
            onLocationFound: null,
            onLocationError: null
        };
    }

    beforeInit() {
        this.currentCoordinateSystem = this.options.defaultCoordinateSystem;
        this.map = window.map;
        this.currentMarker = null;
        this.markerTimeout = null;
    }

    render() {
        this.renderToolbar();
        this.renderDialog();
    }

    renderToolbar() {
        const toolbar = this.createToolbar();
        this.element.appendChild(toolbar);
    }

    createToolbar() {
        const toolbar = document.createElement('div');
        toolbar.className = 'goto-xy-toolbar';
        toolbar.innerHTML = `
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary goto-btn" 
                        title="رفتن به مختصات">
                    <i class="fas fa-location-arrow"></i>
                </button>
                <button type="button" class="btn btn-outline-secondary current-location-btn" 
                        title="موقعیت فعلی">
                    <i class="fas fa-crosshairs"></i>
                </button>
                <button type="button" class="btn btn-outline-info bookmark-btn" 
                        title="نشانک‌ها">
                    <i class="fas fa-bookmark"></i>
                </button>
            </div>
        `;
        return toolbar;
    }

    renderDialog() {
        const dialog = this.createGoToDialog();
        document.body.appendChild(dialog);

        const bookmarkDialog = this.createBookmarkDialog();
        document.body.appendChild(bookmarkDialog);
    }

    createGoToDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'goto-xy-dialog';
        dialog.id = 'gotoXYDialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-header">
                    <h5>رفتن به مختصات</h5>
                    <button type="button" class="btn-close" data-action="close"></button>
                </div>
                <div class="dialog-body">
                    <div class="goto-controls">
                        <div class="mb-3">
                            <label for="coordinateSystem" class="form-label">سیستم مختصات:</label>
                            <select class="form-select" id="coordinateSystem">
                                <option value="geographic">جغرافیایی (WGS84)</option>
                                <option value="mercator">مرکاتور</option>
                                <option value="utm38">UTM Zone 38N</option>
                                <option value="utm39">UTM Zone 39N</option>
                                <option value="utm40">UTM Zone 40N</option>
                                <option value="utm41">UTM Zone 41N</option>
                            </select>
                        </div>
                        
                        <div class="coordinate-inputs">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="xCoordinate" class="form-label" id="xLabel">طول جغرافیایی:</label>
                                    <input type="number" class="form-control" id="xCoordinate" 
                                           step="any" placeholder="مثال: 51.3890">
                                </div>
                                <div class="col-md-6">
                                    <label for="yCoordinate" class="form-label" id="yLabel">عرض جغرافیایی:</label>
                                    <input type="number" class="form-control" id="yCoordinate" 
                                           step="any" placeholder="مثال: 35.6892">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="zoomLevel" class="form-label">سطح زوم:</label>
                            <input type="range" class="form-range" id="zoomLevel" 
                                   min="1" max="20" value="${this.options.zoomLevel}">
                            <div class="d-flex justify-content-between">
                                <small>1</small>
                                <small id="zoomValue">${this.options.zoomLevel}</small>
                                <small>20</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showMarker" 
                                       ${this.options.showMarker ? 'checked' : ''}>
                                <label class="form-check-label" for="showMarker">
                                    نمایش مارکر در موقعیت
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="animateToLocation" 
                                       ${this.options.animateToLocation ? 'checked' : ''}>
                                <label class="form-check-label" for="animateToLocation">
                                    انیمیشن رفتن به موقعیت
                                </label>
                            </div>
                        </div>
                        
                        <div class="quick-locations">
                            <h6>موقعیت‌های سریع:</h6>
                            <div class="btn-group-vertical w-100" role="group">
                                <button type="button" class="btn btn-outline-secondary btn-sm" 
                                        data-location="tehran">تهران</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" 
                                        data-location="isfahan">اصفهان</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" 
                                        data-location="shiraz">شیراز</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" 
                                        data-location="mashhad">مشهد</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button type="button" class="btn btn-secondary" data-action="close">انصراف</button>
                    <button type="button" class="btn btn-success" data-action="save-bookmark">
                        <i class="fas fa-bookmark"></i> ذخیره نشانک
                    </button>
                    <button type="button" class="btn btn-primary" data-action="goto">
                        <i class="fas fa-location-arrow"></i> برو
                    </button>
                </div>
            </div>
        `;
        return dialog;
    }

    createBookmarkDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'goto-xy-dialog';
        dialog.id = 'bookmarkDialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-header">
                    <h5>نشانک‌های ذخیره شده</h5>
                    <button type="button" class="btn-close" data-action="close"></button>
                </div>
                <div class="dialog-body">
                    <div class="bookmark-controls">
                        <div class="mb-3">
                            <button type="button" class="btn btn-primary btn-sm w-100" 
                                    data-action="add-current-location">
                                <i class="fas fa-plus"></i> افزودن موقعیت فعلی
                            </button>
                        </div>
                        
                        <div class="bookmark-list" id="bookmarkList">
                            <div class="no-bookmarks text-center text-muted">
                                <i class="fas fa-bookmark fa-2x mb-2"></i>
                                <p>هیچ نشانکی ذخیره نشده است</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button type="button" class="btn btn-secondary" data-action="close">بستن</button>
                    <button type="button" class="btn btn-danger" data-action="clear-bookmarks">
                        <i class="fas fa-trash"></i> پاک کردن همه
                    </button>
                </div>
            </div>
        `;
        return dialog;
    }

    bindEvents() {
        super.bindEvents();

        // Toolbar events
        this.bindToolbarEvents();
        
        // Dialog events
        this.bindDialogEvents();
    }

    bindToolbarEvents() {
        // GoTo button
        this.find('.goto-btn')?.addEventListener('click', () => {
            this.showGoToDialog();
        });

        // Current location button
        this.find('.current-location-btn')?.addEventListener('click', () => {
            this.getCurrentLocation();
        });

        // Bookmark button
        this.find('.bookmark-btn')?.addEventListener('click', () => {
            this.showBookmarkDialog();
        });
    }

    bindDialogEvents() {
        // GoTo dialog events
        const gotoDialog = document.getElementById('gotoXYDialog');
        if (gotoDialog) {
            this.bindGoToDialogEvents(gotoDialog);
        }

        // Bookmark dialog events
        const bookmarkDialog = document.getElementById('bookmarkDialog');
        if (bookmarkDialog) {
            this.bindBookmarkDialogEvents(bookmarkDialog);
        }
    }

    bindGoToDialogEvents(dialog) {
        // Coordinate system change
        const systemSelect = dialog.querySelector('#coordinateSystem');
        if (systemSelect) {
            systemSelect.addEventListener('change', (e) => {
                this.currentCoordinateSystem = e.target.value;
                this.updateCoordinateLabels();
            });
        }

        // Zoom level change
        const zoomRange = dialog.querySelector('#zoomLevel');
        const zoomValue = dialog.querySelector('#zoomValue');
        if (zoomRange && zoomValue) {
            zoomRange.addEventListener('input', (e) => {
                zoomValue.textContent = e.target.value;
            });
        }

        // Quick location buttons
        dialog.querySelectorAll('[data-location]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const location = e.currentTarget.dataset.location;
                this.goToQuickLocation(location);
            });
        });

        // Action buttons
        dialog.querySelectorAll('[data-action]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.handleGoToAction(action);
            });
        });
    }

    bindBookmarkDialogEvents(dialog) {
        // Action buttons
        dialog.querySelectorAll('[data-action]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.handleBookmarkAction(action);
            });
        });
    }

    // ========================================
    // GoTo Operations
    // ========================================

    handleGoToAction(action) {
        switch (action) {
            case 'close':
                this.hideGoToDialog();
                break;
            case 'goto':
                this.goToCoordinates();
                break;
            case 'save-bookmark':
                this.saveCurrentAsBookmark();
                break;
        }
    }

    goToCoordinates() {
        const xInput = document.getElementById('xCoordinate');
        const yInput = document.getElementById('yCoordinate');

        if (!xInput || !yInput || !this.map) {
            this.showToast('خطا در دریافت مختصات', 'danger');
            return;
        }

        const x = parseFloat(xInput.value);
        const y = parseFloat(yInput.value);

        if (isNaN(x) || isNaN(y)) {
            this.showToast('لطفاً مختصات معتبر وارد کنید', 'warning');
            return;
        }

        try {
            const latlng = this.transformToLatLng(x, y, this.currentCoordinateSystem);
            const zoomLevel = parseInt(document.getElementById('zoomLevel')?.value) || this.options.zoomLevel;
            const showMarker = document.getElementById('showMarker')?.checked || false;
            const animate = document.getElementById('animateToLocation')?.checked || false;

            // Go to location
            if (animate) {
                this.map.flyTo(latlng, zoomLevel);
            } else {
                this.map.setView(latlng, zoomLevel);
            }

            // Show marker if requested
            if (showMarker) {
                this.showLocationMarker(latlng);
            }

            this.hideGoToDialog();
            this.showToast('با موفقیت به موقعیت رفتید', 'success');

            if (this.options.onLocationFound) {
                this.options.onLocationFound(latlng, { x, y, system: this.currentCoordinateSystem });
            }

            this.trigger('locationFound', { latlng, coordinates: { x, y, system: this.currentCoordinateSystem } });

        } catch (error) {
            console.error('Error going to coordinates:', error);
            this.showToast('خطا در رفتن به مختصات', 'danger');

            if (this.options.onLocationError) {
                this.options.onLocationError(error);
            }
        }
    }

    goToQuickLocation(locationName) {
        const locations = {
            tehran: { lat: 35.6892, lng: 51.3890, name: 'تهران' },
            isfahan: { lat: 32.6546, lng: 51.6680, name: 'اصفهان' },
            shiraz: { lat: 29.5918, lng: 52.5837, name: 'شیراز' },
            mashhad: { lat: 36.2605, lng: 59.6168, name: 'مشهد' }
        };

        const location = locations[locationName];
        if (!location) {
            this.showToast('موقعیت یافت نشد', 'warning');
            return;
        }

        const latlng = L.latLng(location.lat, location.lng);
        const zoomLevel = 12;

        this.map.flyTo(latlng, zoomLevel);
        this.showLocationMarker(latlng);

        this.hideGoToDialog();
        this.showToast(`به ${location.name} رفتید`, 'success');
    }

    getCurrentLocation() {
        if (!navigator.geolocation) {
            this.showToast('مرورگر شما از تعیین موقعیت پشتیبانی نمی‌کند', 'warning');
            return;
        }

        this.showToast('در حال تعیین موقعیت...', 'info');

        navigator.geolocation.getCurrentPosition(
            (position) => {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const latlng = L.latLng(lat, lng);

                this.map.flyTo(latlng, 15);
                this.showLocationMarker(latlng);

                this.showToast('موقعیت فعلی شما یافت شد', 'success');
            },
            (error) => {
                console.error('Geolocation error:', error);
                this.showToast('خطا در تعیین موقعیت', 'danger');
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            }
        );
    }

    showLocationMarker(latlng) {
        // Remove previous marker
        this.removeCurrentMarker();

        // Create new marker
        this.currentMarker = L.circleMarker(latlng, this.options.markerStyle).addTo(this.map);

        // Add popup
        this.currentMarker.bindPopup(`
            <div class="location-marker-popup">
                <strong>موقعیت انتخابی</strong><br>
                عرض: ${latlng.lat.toFixed(6)}<br>
                طول: ${latlng.lng.toFixed(6)}
            </div>
        `).openPopup();

        // Auto remove marker after specified duration
        if (this.options.markerDuration > 0) {
            this.markerTimeout = setTimeout(() => {
                this.removeCurrentMarker();
            }, this.options.markerDuration);
        }
    }

    removeCurrentMarker() {
        if (this.currentMarker && this.map) {
            this.map.removeLayer(this.currentMarker);
            this.currentMarker = null;
        }

        if (this.markerTimeout) {
            clearTimeout(this.markerTimeout);
            this.markerTimeout = null;
        }
    }

    // ========================================
    // Bookmark Operations
    // ========================================

    handleBookmarkAction(action) {
        switch (action) {
            case 'close':
                this.hideBookmarkDialog();
                break;
            case 'add-current-location':
                this.addCurrentLocationBookmark();
                break;
            case 'clear-bookmarks':
                this.clearAllBookmarks();
                break;
        }
    }

    saveCurrentAsBookmark() {
        const xInput = document.getElementById('xCoordinate');
        const yInput = document.getElementById('yCoordinate');

        if (!xInput || !yInput) return;

        const x = parseFloat(xInput.value);
        const y = parseFloat(yInput.value);

        if (isNaN(x) || isNaN(y)) {
            this.showToast('لطفاً مختصات معتبر وارد کنید', 'warning');
            return;
        }

        const name = prompt('نام نشانک را وارد کنید:');
        if (!name) return;

        const bookmark = {
            id: Date.now(),
            name: name.trim(),
            x: x,
            y: y,
            system: this.currentCoordinateSystem,
            date: new Date().toLocaleDateString('fa-IR')
        };

        this.saveBookmark(bookmark);
        this.showToast('نشانک ذخیره شد', 'success');
    }

    addCurrentLocationBookmark() {
        if (!this.map) return;

        const center = this.map.getCenter();
        const name = prompt('نام نشانک را وارد کنید:');
        if (!name) return;

        const bookmark = {
            id: Date.now(),
            name: name.trim(),
            x: center.lng,
            y: center.lat,
            system: 'geographic',
            date: new Date().toLocaleDateString('fa-IR')
        };

        this.saveBookmark(bookmark);
        this.refreshBookmarkList();
        this.showToast('نشانک ذخیره شد', 'success');
    }

    saveBookmark(bookmark) {
        const bookmarks = this.getBookmarks();
        bookmarks.push(bookmark);
        localStorage.setItem('gotoXYBookmarks', JSON.stringify(bookmarks));
    }

    getBookmarks() {
        try {
            const bookmarks = localStorage.getItem('gotoXYBookmarks');
            return bookmarks ? JSON.parse(bookmarks) : [];
        } catch {
            return [];
        }
    }

    deleteBookmark(bookmarkId) {
        const bookmarks = this.getBookmarks().filter(b => b.id !== bookmarkId);
        localStorage.setItem('gotoXYBookmarks', JSON.stringify(bookmarks));
        this.refreshBookmarkList();
        this.showToast('نشانک حذف شد', 'success');
    }

    clearAllBookmarks() {
        if (confirm('آیا از پاک کردن همه نشانک‌ها اطمینان دارید؟')) {
            localStorage.removeItem('gotoXYBookmarks');
            this.refreshBookmarkList();
            this.showToast('همه نشانک‌ها پاک شدند', 'success');
        }
    }

    refreshBookmarkList() {
        const container = document.getElementById('bookmarkList');
        if (!container) return;

        const bookmarks = this.getBookmarks();

        if (bookmarks.length === 0) {
            container.innerHTML = `
                <div class="no-bookmarks text-center text-muted">
                    <i class="fas fa-bookmark fa-2x mb-2"></i>
                    <p>هیچ نشانکی ذخیره نشده است</p>
                </div>
            `;
            return;
        }

        container.innerHTML = bookmarks.map(bookmark => `
            <div class="bookmark-item" data-bookmark-id="${bookmark.id}">
                <div class="bookmark-header">
                    <h6 class="bookmark-name">${bookmark.name}</h6>
                    <div class="bookmark-actions">
                        <button type="button" class="btn btn-primary btn-sm"
                                onclick="window.gotoXYComponent.goToBookmark(${bookmark.id})">
                            <i class="fas fa-location-arrow"></i>
                        </button>
                        <button type="button" class="btn btn-danger btn-sm"
                                onclick="window.gotoXYComponent.deleteBookmark(${bookmark.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="bookmark-details">
                    <small class="text-muted">
                        ${this.getCoordinateLabels(bookmark.system).x}: ${bookmark.x.toFixed(6)}<br>
                        ${this.getCoordinateLabels(bookmark.system).y}: ${bookmark.y.toFixed(6)}<br>
                        تاریخ: ${bookmark.date}
                    </small>
                </div>
            </div>
        `).join('');
    }

    goToBookmark(bookmarkId) {
        const bookmarks = this.getBookmarks();
        const bookmark = bookmarks.find(b => b.id === bookmarkId);

        if (!bookmark) {
            this.showToast('نشانک یافت نشد', 'warning');
            return;
        }

        try {
            const latlng = this.transformToLatLng(bookmark.x, bookmark.y, bookmark.system);
            this.map.flyTo(latlng, this.options.zoomLevel);
            this.showLocationMarker(latlng);

            this.hideBookmarkDialog();
            this.showToast(`به ${bookmark.name} رفتید`, 'success');

        } catch (error) {
            console.error('Error going to bookmark:', error);
            this.showToast('خطا در رفتن به نشانک', 'danger');
        }
    }

    // ========================================
    // Utility Methods
    // ========================================

    updateCoordinateLabels() {
        const xLabel = document.getElementById('xLabel');
        const yLabel = document.getElementById('yLabel');

        if (!xLabel || !yLabel) return;

        const labels = this.getCoordinateLabels(this.currentCoordinateSystem);
        xLabel.textContent = labels.x;
        yLabel.textContent = labels.y;
    }

    getCoordinateLabels(system) {
        const labels = {
            geographic: { x: 'طول جغرافیایی:', y: 'عرض جغرافیایی:' },
            mercator: { x: 'X (متر):', y: 'Y (متر):' },
            utm38: { x: 'Easting (UTM 38):', y: 'Northing (UTM 38):' },
            utm39: { x: 'Easting (UTM 39):', y: 'Northing (UTM 39):' },
            utm40: { x: 'Easting (UTM 40):', y: 'Northing (UTM 40):' },
            utm41: { x: 'Easting (UTM 41):', y: 'Northing (UTM 41):' }
        };

        return labels[system] || labels.geographic;
    }

    transformToLatLng(x, y, sourceSystem) {
        // Basic coordinate transformation back to lat/lng
        switch (sourceSystem) {
            case 'geographic':
                return L.latLng(y, x);
            case 'mercator':
                // Convert from Web Mercator
                const lng = x * 180 / 20037508.34;
                const lat = Math.atan(Math.exp(y * Math.PI / 20037508.34)) * 360 / Math.PI - 90;
                return L.latLng(lat, lng);
            case 'utm38':
            case 'utm39':
            case 'utm40':
            case 'utm41':
                // Simplified UTM conversion (would need proper proj4js implementation)
                const zone = parseInt(sourceSystem.replace('utm', ''));
                const lngApprox = x / (111320 * Math.cos(y / 110540 * Math.PI / 180)) - 180;
                const latApprox = y / 110540;
                return L.latLng(latApprox, lngApprox);
            default:
                return L.latLng(y, x);
        }
    }

    showToast(message, type = 'info', duration = 3000) {
        // Use existing toast system or create simple alert
        if (window.showToast) {
            window.showToast(message, type, duration);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    // ========================================
    // Dialog Management
    // ========================================

    showGoToDialog() {
        const dialog = document.getElementById('gotoXYDialog');
        if (dialog) {
            dialog.style.display = 'block';
            this.updateCoordinateLabels();
        }
    }

    hideGoToDialog() {
        const dialog = document.getElementById('gotoXYDialog');
        if (dialog) {
            dialog.style.display = 'none';
        }
    }

    showBookmarkDialog() {
        const dialog = document.getElementById('bookmarkDialog');
        if (dialog) {
            dialog.style.display = 'block';
            this.refreshBookmarkList();
        }
    }

    hideBookmarkDialog() {
        const dialog = document.getElementById('bookmarkDialog');
        if (dialog) {
            dialog.style.display = 'none';
        }
    }

    // ========================================
    // Public API
    // ========================================

    setCoordinateSystem(system) {
        if (this.options.coordinateSystems.includes(system)) {
            this.currentCoordinateSystem = system;
            const systemSelect = document.getElementById('coordinateSystem');
            if (systemSelect) {
                systemSelect.value = system;
            }
            this.updateCoordinateLabels();
        }
    }

    getCoordinateSystem() {
        return this.currentCoordinateSystem;
    }

    setCoordinates(x, y, system = null) {
        const xInput = document.getElementById('xCoordinate');
        const yInput = document.getElementById('yCoordinate');

        if (xInput) xInput.value = x;
        if (yInput) yInput.value = y;

        if (system) {
            this.setCoordinateSystem(system);
        }
    }

    getCoordinates() {
        const xInput = document.getElementById('xCoordinate');
        const yInput = document.getElementById('yCoordinate');

        return {
            x: parseFloat(xInput?.value) || 0,
            y: parseFloat(yInput?.value) || 0,
            system: this.currentCoordinateSystem
        };
    }

    // ========================================
    // Cleanup
    // ========================================

    destroy() {
        // Remove marker
        this.removeCurrentMarker();

        // Remove dialogs
        const gotoDialog = document.getElementById('gotoXYDialog');
        if (gotoDialog) {
            gotoDialog.remove();
        }

        const bookmarkDialog = document.getElementById('bookmarkDialog');
        if (bookmarkDialog) {
            bookmarkDialog.remove();
        }

        super.destroy();
    }
}

// Register component
window.ComponentFactory.register('goto-xy', GoToXYComponent);
