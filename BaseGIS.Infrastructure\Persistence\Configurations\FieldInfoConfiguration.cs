using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BaseGIS.Core.Entities;

namespace BaseGIS.Infrastructure.Persistence.Configurations
{
    public class FieldInfoConfiguration : IEntityTypeConfiguration<FieldInfo>
    {
        public void Configure(EntityTypeBuilder<FieldInfo> builder)
        {
            builder.HasKey(f => f.Id);
            builder.Property(f => f.Name).IsRequired().HasMaxLength(50);
            builder.Property(f => f.AliasName).IsRequired().HasMaxLength(100);
            builder.Property(f => f.UnitName).HasMaxLength(20);
            builder.Property(f => f.FieldType).IsRequired().HasMaxLength(20);
            builder.HasOne(f => f.TableInfo)
                   .WithMany(t => t.FieldInfos)
                   .HasForeignKey(f => f.TableInfoId);
            builder.HasMany(f => f.DomainInfos)
                   .WithOne(d => d.FieldInfo)
                   .HasForeignKey(d => d.FieldInfoId);
        }
    }
} 