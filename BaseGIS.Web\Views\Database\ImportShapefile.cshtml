﻿@{
    ViewData["Title"] = "وارد کردن شپ‌فایل";
    var tableId = ViewData["TableId"];
}

@section Styles {
    <link href="~/lib/sweetalert2/sweetalert2.min.css" rel="stylesheet" />
    <style>
        .swal2-popup {
            font-family: 'Shabnam', sans-serif;
            direction: rtl;
            text-align: right;
            border-radius: 8px;
        }

        .swal2-confirm {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .swal2-cancel {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }
    </style>
}

<div class="container-fluid">
    <div class="card shadow-sm">
        <div class="card-header">
            <h6 class="mb-0">وارد کردن شپ‌فایل</h6>
        </div>
        <div class="card-body">
            <form asp-action="ImportShapefile" method="post" enctype="multipart/form-data">
                <input type="hidden" name="tableId" value="@tableId" />
                <div class="form-group">
                    <label for="shapefile">فایل شپ‌فایل (ZIP)</label>
                    <input type="file" id="shapefile" name="shapefile" class="form-control" accept=".zip" required />
                </div>
                <button type="submit" class="btn btn-primary mt-3">آپلود و بررسی</button>
                <a asp-action="TableDetails" asp-route-id="@tableId" class="btn btn-secondary mt-3">بازگشت</a>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/lib/sweetalert2/sweetalert2.min.js"></script>
    <script>
        $(document).ready(function () {
        @if (TempData["ErrorMessage"] != null)
        {
            <text>
                    Swal.fire({
                        title: 'خطا!',
                        text: '@TempData["ErrorMessage"]',
                        icon: 'error',
                        confirmButtonText: 'باشه',
                        confirmButtonColor: 'var(--primary-color)'
                    });
            </text>
        }
        });
    </script>
}