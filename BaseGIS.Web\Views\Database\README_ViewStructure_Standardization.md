# استانداردسازی ساختار ویوها و کتابخانه‌ها

این سند تغییرات انجام شده برای یکسان‌سازی ساختار ویوهای LayerSetting، Insert، و SymbologyManager و استفاده از کتابخانه‌های libman را شرح می‌دهد.

## 🎯 هدف

1. **یکسان‌سازی ساختار HTML** در تمام ویوها
2. **استفاده از libman** به جای CDN برای کتابخانه‌ها
3. **بهبود عملکرد** و **قابلیت اطمینان**
4. **سازگاری بهتر** با محیط آفلاین

## 📋 تغییرات انجام شده

### 1. **ساختار HTML استاندارد**

تمام ویوها حالا از ساختار یکسان استفاده می‌کنند:

```html
<!-- MAIN CONTENT -->
<div>
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6"><h5 class="mb-0">عنوان صفحه</h5></div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="#">مدیریت داده</a></li>
                        <li class="breadcrumb-item active" aria-current="page">عنوان صفحه</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->
    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <!-- محتوای صفحه -->
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</div>
<!-- END MAIN CONTENT -->
```

### 2. **کتابخانه‌های اضافه شده به libman**

```json
{
  "library": "smartwizard@6.0.5",
  "provider": "unpkg",
  "destination": "wwwroot/lib/smartwizard/",
  "files": [
    "dist/css/smart_wizard_all.min.css",
    "dist/js/jquery.smartWizard.min.js"
  ]
},
{
  "library": "select2@4.1.0-rc.0",
  "destination": "wwwroot/lib/select2/",
  "files": [
    "css/select2.min.css",
    "js/select2.min.js",
    "js/i18n/fa.js"
  ]
},
{
  "library": "dropzone@5.9.3",
  "destination": "wwwroot/lib/dropzone/",
  "files": [
    "min/dropzone.min.css",
    "min/dropzone.min.js"
  ]
}
```

### 3. **به‌روزرسانی مراجع کتابخانه‌ها**

#### **قبل (CDN):**
```html
<script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/min/dropzone.min.js"></script>
<script src="https://unpkg.com/smartwizard@6/dist/js/jquery.smartWizard.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
```

#### **بعد (libman):**
```html
<script src="~/lib/dropzone/min/dropzone.min.js"></script>
<script src="~/lib/smartwizard/dist/js/jquery.smartWizard.min.js"></script>
<script src="~/lib/select2/js/select2.min.js"></script>
```

## 📁 فایل‌های تغییر یافته

### 1. **LayerSetting.cshtml**
- ✅ ساختار HTML استاندارد
- ✅ FancyTree از libman
- ✅ کامنت‌های مناسب

### 2. **Insert.cshtml**
- ✅ ساختار HTML استاندارد
- ✅ SmartWizard از libman
- ✅ Select2 از libman
- ✅ Dropzone از libman

### 3. **SymbologyManager.cshtml**
- ✅ ساختار HTML استاندارد
- ✅ FancyTree از libman
- ✅ درخت لایه‌ها بهبود یافته

### 4. **libman.json**
- ✅ کتابخانه‌های جدید اضافه شده
- ✅ مسیرهای صحیح تنظیم شده

## 🎨 ویژگی‌های بهبود یافته

### **LayerSetting:**
- 🌳 درخت لایه‌ها با FancyTree
- 🔍 جستجوی پیشرفته
- ✏️ دکمه‌های ویرایش

### **Insert:**
- 🧙‍♂️ ویزارد قدم به قدم
- 📁 آپلود فایل با Dropzone
- 🎯 انتخاب لایه با Select2

### **SymbologyManager:**
- 🎨 مدیریت سمبولوژی
- 🌳 درخت لایه‌ها یکسان
- 🔄 به‌روزرسانی خودکار

## 🚀 مزایای جدید

### **عملکرد:**
- ⚡ بارگذاری سریع‌تر (فایل‌های محلی)
- 📱 کار آفلاین
- 🔄 کش بهتر

### **نگهداری:**
- 🔧 مدیریت آسان‌تر نسخه‌ها
- 📦 کنترل بهتر وابستگی‌ها
- 🛡️ امنیت بیشتر

### **توسعه:**
- 🎯 کد یکسان و قابل پیش‌بینی
- 📚 مستندسازی بهتر
- 🔍 دیباگ آسان‌تر

## 📋 چک‌لیست تکمیل

### ✅ **انجام شده:**
- [x] یکسان‌سازی ساختار HTML
- [x] اضافه کردن کتابخانه‌ها به libman
- [x] به‌روزرسانی مراجع در ویوها
- [x] تست restore کتابخانه‌ها
- [x] مستندسازی تغییرات

### 🔄 **در صورت نیاز:**
- [ ] تست عملکرد در مرورگرهای مختلف
- [ ] بهینه‌سازی CSS سفارشی
- [ ] اضافه کردن کتابخانه‌های بیشتر

## 🛠️ نحوه استفاده

### **Restore کتابخانه‌ها:**
```bash
cd BaseGIS.Web
libman restore
```

### **اضافه کردن کتابخانه جدید:**
```bash
libman install jquery@3.7.1 --destination wwwroot/lib/jquery/
```

### **به‌روزرسانی کتابخانه:**
```bash
libman update jquery
```

## 📝 نکات مهم

1. **همیشه از libman استفاده کنید** به جای CDN
2. **مسیرهای فایل‌ها را بررسی کنید** قبل از استفاده
3. **نسخه‌های کتابخانه‌ها را مدیریت کنید**
4. **تست کنید** بعد از هر تغییر

## 🔗 منابع

- [LibMan Documentation](https://docs.microsoft.com/en-us/aspnet/core/client-side/libman/)
- [SmartWizard](https://github.com/techlab/SmartWizard)
- [Select2](https://select2.org/)
- [Dropzone](https://www.dropzone.dev/)
- [FancyTree](https://github.com/mar10/fancytree)

این استانداردسازی باعث بهبود قابل توجه در عملکرد، نگهداری و توسعه پروژه خواهد شد.
