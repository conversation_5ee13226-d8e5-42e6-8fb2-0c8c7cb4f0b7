﻿@{
    var idRequest = Context.Request.Query["id"];
}
<!-- MAIN CONTENT -->
<div>
    <!--begin::App Content Header-->
    <div class="app-content-header">
        <!--begin::Container-->
        <div class="container-fluid">
            <!--begin::Row-->
            <div class="row">
                <div class="col-sm-6"><h5 class="mb-0">افزودن یکباره اطلاعات</h5></div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-end">
                        <li class="breadcrumb-item"><a href="#">مدیریت داده</a></li>
                        <li class="breadcrumb-item active" aria-current="page">افزودن یکباره اطلاعات</li>
                    </ol>
                </div>
            </div>
            <!--end::Row-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content Header-->
    <!--begin::App Content-->
    <div class="app-content">
        <!--begin::Container-->
        <div class="container-fluid">
             <div id="_Insert_List" class="col-12"> 
                <div class="loading-spinner" style="display: none; text-align: center;">
                    <i class="fa fa-spinner fa-spin"></i> در حال بارگذاری...
                </div>
            </div>
            <div id="_Insert_Wizard" class="col-12">
                <div class="loading-spinner" style="display: none; text-align: center;">
                    <i class="fa fa-spinner fa-spin"></i> در حال بارگذاری...
                </div>
            </div>
        </div>
        <!--end::Container-->
    </div>
    <!--end::App Content-->
</div>

@section Styles {
    <link href="~/lib/dropzone/min/dropzone.min.css" rel="stylesheet" />
    <link href="~/lib/smartwizard/dist/css/smart_wizard_all.min.css" rel="stylesheet" type="text/css" />
    <link href="~/lib/select2/css/select2.min.css" rel="stylesheet" />
    <style>
        .select2-container {
            width: 100% !important;
        }
        .select2-selection {
            border: 1px solid #ced4da !important;
            border-radius: 0.375rem !important;
            min-height: 38px !important;
        }
        .select2-selection__rendered {
            padding: 6px 12px !important;
            line-height: 1.5 !important;
        }
    <style>
        .loading-spinner {
            font-family: 'SamanFont', sans-serif;
            color: #007bff;
            padding: 20px;
        }
    </style>
}
@section Scripts {
    <script src="~/lib/dropzone/min/dropzone.min.js"></script>
    <script src="~/lib/smartwizard/dist/js/jquery.smartWizard.min.js" type="text/javascript"></script>
    <script src="~/lib/select2/js/select2.min.js"></script>
    <script src="~/lib/select2/js/i18n/fa.js"></script>
    <script type="text/javascript">
        Dropzone.autoDiscover = false;

        $(document).ready(function () {
            loadInsertList();
        @if (!string.IsNullOrEmpty(idRequest))
        {
            <text>loadInsertWizard('@idRequest');</text>
        }

            // تابع برای انتخاب آیتم
            window.onItemSelected = function (id) {
                if (id && id !== '0') {
                    $("#_Insert_Wizard").show();
                    loadInsertWizard(id);
                } else {
                    $("#_Insert_Wizard").hide();
                }
            };
        });

        function loadInsertList() {
            $("#_Insert_List .loading-spinner").show();
            $.ajax({
                url: '@Url.Action("_Insert_List", "Database")',
                type: 'GET',
                cache: false,
                success: function (data) {
                    $("#_Insert_List").html(data);
                },
                error: function (xhr, status, error) {
                    $("#_Insert_List").html("<p class='text-danger'>خطا در بارگذاری لیست: " + error + "</p>");
                },
                complete: function () {
                    $("#_Insert_List .loading-spinner").hide();
                }
            });
        }

        function loadInsertWizard(id) {
            $("#_Insert_Wizard .loading-spinner").show();
            $.ajax({
                url: '@Url.Action("_Insert_Wizard", "Database")',
                type: 'GET',
                data: { id: id },
                cache: false,
                success: function (data) {
                    $("#_Insert_Wizard").html(data);
                    initializeWizard();
                    setTimeout(initializeSelect2, 0); // اطمینان از اجرا پس از رندر
                },
                error: function (xhr, status, error) {
                    $("#_Insert_Wizard").html("<p class='text-danger'>خطا در بارگذاری ویزارد: " + error + "</p>");
                },
                complete: function () {
                    $("#_Insert_Wizard .loading-spinner").hide();
                }
            });
        }

        function initializeWizard() {
            if ($('#wizard').length) {
                $('#wizard').smartWizard({
                    theme: 'arrows',
                    lang: {
                        next: 'بعدی',
                        previous: 'قبلی'
                    },
                    justified: true,
                    enableUrlHash: false,
                    toolbar: {
                        showNextButton: true,
                        showPreviousButton: true
                    }
                });
            }
        }

        function initializeSelect2() {
            if (typeof $.fn.select2 === "function" && $('.select2').length > 0) {
                $('.select2').select2({
                    theme: 'bootstrap-5',
                    language: 'fa',
                    dir: 'rtl',
                    placeholder: 'انتخاب کنید',
                    allowClear: true,
                    width: '100%'
                });
            }
        }
    </script>
}