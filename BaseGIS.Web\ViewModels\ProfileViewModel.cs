﻿using System.ComponentModel.DataAnnotations;

namespace BaseGIS.Web.ViewModels
{
    public class ProfileViewModel
    {
        public string UserId { get; set; }
        public string Username { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }

        public string PhoneNumber { get; set; }

        public List<string> Roles { get; set; }

        // فیلدهای تغییر رمز
        [DataType(DataType.Password)]
        [Display(Name = "رمز فعلی")]
        public string CurrentPassword { get; set; }

        [DataType(DataType.Password)]
        [Display(Name = "رمز جدید")]
        [StringLength(100, ErrorMessage = "{0} باید حداقل {2} و حداکثر {1} کاراکتر باشد.", MinimumLength = 6)]
        public string NewPassword { get; set; }

        [DataType(DataType.Password)]
        [Display(Name = "تأیید رمز جدید")]
        [Compare("NewPassword", ErrorMessage = "رمز جدید و تأیید آن مطابقت ندارند.")]
        public string ConfirmPassword { get; set; }
    }
}
