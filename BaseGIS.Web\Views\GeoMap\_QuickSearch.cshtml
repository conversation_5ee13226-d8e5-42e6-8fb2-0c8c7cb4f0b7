<!--begin::Quick Search Content-->
<div class="container-fluid h-100">
    <div class="row h-100">
        <div class="col-12">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fa fa-bolt"></i> جستجوی سریع
                    </h5>
                </div>
                <div class="card-body">
                    <!-- کامپوننت ابزارهای جستجو -->
                    <div id="quick-search-tools"
                         data-component="search-tools"
                         data-search-types='["quick"]'
                         data-default-search-type="quick"
                         data-max-results="50"
                         data-show-results-on-map="true">

                        <!-- Fallback content for non-JS users -->
                        <div class="row">
                            <!-- فرم جستجو -->
                            <div class="col-md-4">
                            <div class="search-form">
                                <div class="mb-3">
                                    <label for="searchText" class="form-label">متن جستجو:</label>
                                    <input type="text" class="form-control" id="searchText" placeholder="متن مورد نظر را وارد کنید...">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="searchLayer" class="form-label">انتخاب لایه:</label>
                                    <select class="form-select" id="searchLayer">
                                        <option value="">همه لایه‌ها</option>
                                        <option value="buildings">ساختمان‌ها</option>
                                        <option value="roads">جاده‌ها</option>
                                        <option value="parks">پارک‌ها</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="searchField" class="form-label">فیلد جستجو:</label>
                                    <select class="form-select" id="searchField">
                                        <option value="">همه فیلدها</option>
                                        <option value="name">نام</option>
                                        <option value="code">کد</option>
                                        <option value="description">توضیحات</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="exactMatch">
                                        <label class="form-check-label" for="exactMatch">
                                            تطبیق دقیق
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="caseSensitive">
                                        <label class="form-check-label" for="caseSensitive">
                                            حساس به حروف کوچک و بزرگ
                                        </label>
                                    </div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-primary" onclick="performQuickSearch()">
                                        <i class="fa fa-search"></i> جستجو
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="clearQuickSearch()">
                                        <i class="fa fa-eraser"></i> پاک کردن
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- نتایج جستجو -->
                        <div class="col-md-8">
                            <div class="search-results">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0">نتایج جستجو</h6>
                                    <span id="searchResultCount" class="badge bg-info">0 نتیجه</span>
                                </div>
                                
                                <div id="searchResultsContainer" class="border rounded p-3" style="height: 400px; overflow-y: auto;">
                                    <div class="text-center text-muted">
                                        <i class="fa fa-search fa-3x mb-3"></i>
                                        <p>برای شروع جستجو، متن مورد نظر را وارد کرده و دکمه جستجو را کلیک کنید.</p>
                                    </div>
                                </div>

                                <!-- ابزارهای نتایج -->
                                <div class="mt-3">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="zoomToAllResults()">
                                            <i class="fa fa-expand"></i> نمایش همه
                                        </button>
                                        <button type="button" class="btn btn-outline-info btn-sm" onclick="exportResults()">
                                            <i class="fa fa-download"></i> خروجی
                                        </button>
                                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="selectAllResults()">
                                            <i class="fa fa-check-square"></i> انتخاب همه
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- End fallback content -->
                    </div>
                    <!-- End کامپوننت ابزارهای جستجو -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // مقداردهی کامپوننت جستجوی سریع
        initializeQuickSearchComponent();
    });

    /**
     * مقداردهی کامپوننت جستجوی سریع
     */
    function initializeQuickSearchComponent() {
        const quickSearchContainer = document.getElementById('quick-search-tools');

        if (quickSearchContainer && window.ComponentFactory) {
            // ایجاد کامپوننت جستجو
            const searchComponent = window.ComponentFactory.create('search-tools', quickSearchContainer, {
                searchTypes: ['quick'],
                defaultSearchType: 'quick',
                maxResults: 50,
                showResultsOnMap: true,
                onSearchComplete: function(searchType, results) {
                    console.log('Quick search completed:', results);

                    // اتصال به GeoMapCore برای نمایش نتایج روی نقشه
                    if (window.geoMapCore) {
                        window.geoMapCore.displaySearchResults(results, searchType);
                    }

                    // نمایش پیام موفقیت
                    if (window.toastr) {
                        toastr.success(`${results.length} نتیجه یافت شد`, 'جستجوی سریع');
                    }
                },
                onResultSelect: function(result, index) {
                    console.log('Quick search result selected:', result);

                    // زوم به نتیجه انتخاب شده
                    if (window.geoMapCore && result.geometry) {
                        window.geoMapCore.zoomToGeometry(result.geometry);
                    }

                    // هایلایت نتیجه
                    if (window.geoMapCore) {
                        window.geoMapCore.highlightFeature(result);
                    }
                }
            });

            // ذخیره مرجع کامپوننت برای استفاده بعدی
            window.quickSearchComponent = searchComponent;

            console.log('Quick search component initialized');
        } else {
            console.warn('ComponentFactory not available, falling back to legacy search');
            // در صورت عدم دسترسی به ComponentFactory، از کد قدیمی استفاده کن
            initializeLegacyQuickSearch();
        }
    }

    /**
     * مقداردهی جستجوی سریع قدیمی (fallback)
     */
    function initializeLegacyQuickSearch() {
        console.log('Initializing legacy quick search...');

        // اتصال رویداد Enter به فیلد جستجو
        const searchInput = document.getElementById('searchText');
        if (searchInput) {
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performLegacyQuickSearch();
                }
            });
        }
    }

    /**
     * انجام جستجوی سریع قدیمی (fallback)
     */
    function performLegacyQuickSearch() {
        const searchText = document.getElementById('searchText')?.value?.trim();
        const searchLayer = document.getElementById('searchLayer')?.value;
        const searchField = document.getElementById('searchField')?.value;
        const exactMatch = document.getElementById('exactMatch')?.checked;

        if (!searchText) {
            if (window.toastr) {
                toastr.warning('لطفاً متن جستجو را وارد کنید', 'هشدار');
            } else {
                alert('لطفاً متن جستجو را وارد کنید.');
            }
            return;
        }

        // نمایش loading
        const resultsContainer = document.getElementById('searchResultsContainer');
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">در حال جستجو...</span>
                    </div>
                    <p class="mt-2">در حال جستجو...</p>
                </div>
            `;
        }

        // ارسال درخواست جستجو به سرور
        const searchRequest = {
            searchTerm: searchText,
            layerId: searchLayer || null,
            fieldName: searchField || null,
            maxResults: 50,
            exactMatch: exactMatch || false
        };

        // ارسال درخواست به API
        fetch('/GeoMap/PerformQuickSearch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(searchRequest)
        })
        .then(response => response.json())
        .then(result => {
            if (result.ok) {
                displayLegacySearchResults(result.data);

                if (window.toastr) {
                    toastr.success(`${result.data.length} نتیجه یافت شد`, 'جستجوی سریع');
                }
            } else {
                throw new Error(result.msg);
            }
        })
        .catch(error => {
            console.error('Error performing quick search:', error);

            if (resultsContainer) {
                resultsContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fa fa-exclamation-triangle"></i>
                        خطا در انجام جستجو: ${error.message}
                    </div>
                `;
            }

            if (window.toastr) {
                toastr.error('خطا در انجام جستجو', 'خطا');
            }
        });
    }

    /**
     * نمایش نتایج جستجوی قدیمی (fallback)
     */
    function displayLegacySearchResults(results) {
        const resultsContainer = document.getElementById('searchResultsContainer');
        const resultCount = document.getElementById('searchResultCount');

        if (resultCount) {
            resultCount.textContent = `${results.length} نتیجه`;
        }

        if (!resultsContainer) return;

        if (results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fa fa-search fa-3x mb-3"></i>
                    <p>نتیجه‌ای یافت نشد.</p>
                </div>
            `;
            return;
        }

        let html = '<div class="list-group">';
        results.forEach((result, index) => {
            html += `
                <div class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${result.displayText || result.id}</h6>
                        <small class="text-muted">${result.layerName || 'نامشخص'}</small>
                    </div>
                    <p class="mb-1">
                        ${Object.entries(result.attributes || {}).slice(0, 2).map(([key, value]) =>
                            `<strong>${key}:</strong> ${value}`
                        ).join(' | ')}
                    </p>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="zoomToLegacyResult(${index})">
                            <i class="fa fa-search-plus"></i> نمایش
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="selectLegacyResult(${index})">
                            <i class="fa fa-check"></i> انتخاب
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="highlightLegacyResult(${index})">
                            <i class="fa fa-eye"></i> هایلایت
                        </button>
                    </div>
                </div>
            `;
        });
        html += '</div>';

        resultsContainer.innerHTML = html;

        // ذخیره نتایج برای استفاده در توابع دیگر
        window.legacySearchResults = results;
    }

    /**
     * پاک کردن جستجوی قدیمی (fallback)
     */
    function clearQuickSearch() {
        // پاک کردن فیلدهای فرم
        const searchText = document.getElementById('searchText');
        const searchLayer = document.getElementById('searchLayer');
        const searchField = document.getElementById('searchField');
        const exactMatch = document.getElementById('exactMatch');
        const caseSensitive = document.getElementById('caseSensitive');

        if (searchText) searchText.value = '';
        if (searchLayer) searchLayer.value = '';
        if (searchField) searchField.value = '';
        if (exactMatch) exactMatch.checked = false;
        if (caseSensitive) caseSensitive.checked = false;

        // پاک کردن نتایج
        window.legacySearchResults = [];

        const resultCount = document.getElementById('searchResultCount');
        const resultsContainer = document.getElementById('searchResultsContainer');

        if (resultCount) {
            resultCount.textContent = '0 نتیجه';
        }

        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fa fa-search fa-3x mb-3"></i>
                    <p>برای شروع جستجو، متن مورد نظر را وارد کرده و دکمه جستجو را کلیک کنید.</p>
                </div>
            `;
        }

        // پاک کردن نتایج از نقشه
        if (window.geoMapCore) {
            window.geoMapCore.clearSearchResults();
        }
    }

    /**
     * توابع مدیریت نتایج قدیمی (fallback)
     */
    function zoomToLegacyResult(index) {
        const results = window.legacySearchResults || [];
        const result = results[index];

        if (result && result.geometry && window.geoMapCore) {
            window.geoMapCore.zoomToGeometry(result.geometry);
        } else {
            console.log(`Zooming to result ${index}:`, result);
            if (window.toastr) {
                toastr.info('زوم به نتیجه انتخاب شده', 'اطلاع');
            }
        }
    }

    function selectLegacyResult(index) {
        const results = window.legacySearchResults || [];
        const result = results[index];

        if (result) {
            console.log(`Selecting result ${index}:`, result);

            // انتخاب نتیجه روی نقشه
            if (window.geoMapCore) {
                window.geoMapCore.selectFeature(result);
            }

            if (window.toastr) {
                toastr.success('نتیجه انتخاب شد', 'موفقیت');
            }
        }
    }

    function highlightLegacyResult(index) {
        const results = window.legacySearchResults || [];
        const result = results[index];

        if (result) {
            console.log(`Highlighting result ${index}:`, result);

            // هایلایت نتیجه روی نقشه
            if (window.geoMapCore) {
                window.geoMapCore.highlightFeature(result);
            }

            if (window.toastr) {
                toastr.info('نتیجه هایلایت شد', 'اطلاع');
            }
        }
    }

    /**
     * توابع مدیریت کلی نتایج
     */
    function zoomToAllResults() {
        const results = window.legacySearchResults || [];

        if (results.length > 0) {
            console.log('Zooming to all results:', results);

            if (window.geoMapCore) {
                window.geoMapCore.zoomToAllResults(results);
            }

            if (window.toastr) {
                toastr.info('نمایش همه نتایج', 'اطلاع');
            }
        }
    }

    function exportResults() {
        const results = window.legacySearchResults || [];

        if (results.length > 0) {
            console.log('Exporting results:', results);

            // تبدیل نتایج به CSV
            const csvContent = convertResultsToCSV(results);
            downloadCSV(csvContent, 'search_results.csv');

            if (window.toastr) {
                toastr.success('نتایج صادر شد', 'موفقیت');
            }
        }
    }

    function selectAllResults() {
        const results = window.legacySearchResults || [];

        if (results.length > 0) {
            console.log('Selecting all results:', results);

            if (window.geoMapCore) {
                window.geoMapCore.selectAllFeatures(results);
            }

            if (window.toastr) {
                toastr.success('همه نتایج انتخاب شدند', 'موفقیت');
            }
        }
    }

    /**
     * توابع کمکی
     */
    function convertResultsToCSV(results) {
        if (!results || results.length === 0) return '';

        const headers = ['ID', 'Display Text', 'Layer Name'];
        const rows = results.map(result => [
            result.id || '',
            result.displayText || '',
            result.layerName || ''
        ]);

        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        return csvContent;
    }

    function downloadCSV(content, filename) {
        const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');

        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    // توابع سراسری برای دسترسی از HTML
    window.performQuickSearch = performLegacyQuickSearch;
    window.clearQuickSearch = clearQuickSearch;
    window.zoomToResult = zoomToLegacyResult;
    window.selectResult = selectLegacyResult;
    window.highlightResult = highlightLegacyResult;
    window.zoomToAllResults = zoomToAllResults;
    window.exportResults = exportResults;
    window.selectAllResults = selectAllResults;
</script>
<!--end::Quick Search Content-->
