<!--begin::Report Builder Content-->
<div class="container-fluid h-100">
    <div class="row h-100">
        <div class="col-12">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fa fa-cogs"></i> گزارش ساز
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- ابزارهای گزارش ساز -->
                        <div class="col-md-3">
                            <div class="report-builder-tools">
                                <div class="mb-4">
                                    <h6 class="border-bottom pb-2">منابع داده</h6>
                                    <div class="list-group" id="dataSourcesList">
                                        <div class="list-group-item list-group-item-action" draggable="true" data-type="layer" data-id="buildings">
                                            <i class="fa fa-building"></i> ساختمان‌ها
                                        </div>
                                        <div class="list-group-item list-group-item-action" draggable="true" data-type="layer" data-id="roads">
                                            <i class="fa fa-road"></i> جاده‌ها
                                        </div>
                                        <div class="list-group-item list-group-item-action" draggable="true" data-type="layer" data-id="parks">
                                            <i class="fa fa-tree"></i> پارک‌ها
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <h6 class="border-bottom pb-2">اجزای گزارش</h6>
                                    <div class="list-group" id="reportComponentsList">
                                        <div class="list-group-item list-group-item-action" draggable="true" data-type="component" data-id="title">
                                            <i class="fa fa-heading"></i> عنوان
                                        </div>
                                        <div class="list-group-item list-group-item-action" draggable="true" data-type="component" data-id="table">
                                            <i class="fa fa-table"></i> جدول
                                        </div>
                                        <div class="list-group-item list-group-item-action" draggable="true" data-type="component" data-id="chart">
                                            <i class="fa fa-chart-bar"></i> نمودار
                                        </div>
                                        <div class="list-group-item list-group-item-action" draggable="true" data-type="component" data-id="map">
                                            <i class="fa fa-map"></i> نقشه
                                        </div>
                                        <div class="list-group-item list-group-item-action" draggable="true" data-type="component" data-id="text">
                                            <i class="fa fa-align-left"></i> متن
                                        </div>
                                        <div class="list-group-item list-group-item-action" draggable="true" data-type="component" data-id="image">
                                            <i class="fa fa-image"></i> تصویر
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <h6 class="border-bottom pb-2">عملیات</h6>
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-success btn-sm" onclick="previewReport()">
                                            <i class="fa fa-eye"></i> پیش‌نمایش
                                        </button>
                                        <button type="button" class="btn btn-primary btn-sm" onclick="saveReport()">
                                            <i class="fa fa-save"></i> ذخیره گزارش
                                        </button>
                                        <button type="button" class="btn btn-info btn-sm" onclick="loadReport()">
                                            <i class="fa fa-folder-open"></i> بارگذاری گزارش
                                        </button>
                                        <button type="button" class="btn btn-secondary btn-sm" onclick="clearReportBuilder()">
                                            <i class="fa fa-eraser"></i> پاک کردن
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- طراح گزارش -->
                        <div class="col-md-6">
                            <div class="report-designer">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0">طراح گزارش</h6>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-secondary" onclick="toggleGrid()" id="gridToggle">
                                            <i class="fa fa-th"></i> شبکه
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="zoomIn()">
                                            <i class="fa fa-search-plus"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="zoomOut()">
                                            <i class="fa fa-search-minus"></i>
                                        </button>
                                    </div>
                                </div>

                                <div id="reportCanvas" class="report-canvas border rounded" style="height: 500px; position: relative; overflow: auto; background: white;">
                                    <div class="drop-zone text-center text-muted d-flex align-items-center justify-content-center h-100">
                                        <div>
                                            <i class="fa fa-plus-circle fa-3x mb-3"></i>
                                            <p>اجزای گزارش را از سمت چپ به اینجا بکشید</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تنظیمات جزء -->
                        <div class="col-md-3">
                            <div class="component-properties">
                                <h6 class="border-bottom pb-2">تنظیمات جزء</h6>
                                <div id="propertiesPanel">
                                    <div class="text-center text-muted">
                                        <i class="fa fa-cog fa-3x mb-3"></i>
                                        <p>برای تنظیم ویژگی‌ها، یک جزء را انتخاب کنید.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal پیش‌نمایش گزارش -->
<div class="modal fade" id="reportPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">پیش‌نمایش گزارش</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="reportPreviewContent" style="min-height: 400px;">
                    <!-- محتوای پیش‌نمایش اینجا نمایش داده می‌شود -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" onclick="exportReportToPDF()">
                    <i class="fa fa-file-pdf"></i> خروجی PDF
                </button>
                <button type="button" class="btn btn-primary" onclick="printReport()">
                    <i class="fa fa-print"></i> چاپ
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">بستن</button>
            </div>
        </div>
    </div>
</div>

<style>
    .report-canvas {
        background-image:
            linear-gradient(rgba(0,0,0,.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0,0,0,.1) 1px, transparent 1px);
        background-size: 20px 20px;
    }

    .report-component {
        position: absolute;
        border: 2px dashed #ccc;
        padding: 10px;
        background: rgba(255, 255, 255, 0.9);
        cursor: move;
        min-width: 100px;
        min-height: 50px;
    }

    .report-component:hover {
        border-color: #007bff;
    }

    .report-component.selected {
        border-color: #007bff;
        border-style: solid;
    }

    .drop-zone.drag-over {
        background-color: rgba(0, 123, 255, 0.1);
        border: 2px dashed #007bff;
    }

    .list-group-item[draggable="true"] {
        cursor: grab;
    }

    .list-group-item[draggable="true"]:active {
        cursor: grabbing;
    }

    .resize-handle {
        position: absolute;
        width: 10px;
        height: 10px;
        background: #007bff;
        border: 1px solid white;
    }

    .resize-handle.nw { top: -5px; left: -5px; cursor: nw-resize; }
    .resize-handle.ne { top: -5px; right: -5px; cursor: ne-resize; }
    .resize-handle.sw { bottom: -5px; left: -5px; cursor: sw-resize; }
    .resize-handle.se { bottom: -5px; right: -5px; cursor: se-resize; }
</style>

<script>
    // متغیرهای گزارش ساز
    let reportComponents = [];
    let selectedComponent = null;
    let draggedElement = null;
    let componentCounter = 0;
    let gridEnabled = true;

    // مقداردهی اولیه
    document.addEventListener('DOMContentLoaded', function() {
        initializeReportBuilder();
    });

    /**
     * مقداردهی گزارش ساز
     */
    function initializeReportBuilder() {
        setupDragAndDrop();
        setupCanvasEvents();
    }

    /**
     * تنظیم Drag and Drop
     */
    function setupDragAndDrop() {
        // تنظیم عناصر قابل کشیدن
        document.querySelectorAll('[draggable="true"]').forEach(element => {
            element.addEventListener('dragstart', handleDragStart);
        });

        // تنظیم منطقه Drop
        const canvas = document.getElementById('reportCanvas');
        canvas.addEventListener('dragover', handleDragOver);
        canvas.addEventListener('drop', handleDrop);
        canvas.addEventListener('dragleave', handleDragLeave);
    }

    /**
     * شروع کشیدن
     */
    function handleDragStart(e) {
        draggedElement = e.target;
        e.dataTransfer.effectAllowed = 'copy';
        e.dataTransfer.setData('text/html', e.target.outerHTML);
    }

    /**
     * کشیدن روی Canvas
     */
    function handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'copy';
        e.currentTarget.classList.add('drag-over');
    }

    /**
     * خروج از Canvas
     */
    function handleDragLeave(e) {
        e.currentTarget.classList.remove('drag-over');
    }

    /**
     * رها کردن در Canvas
     */
    function handleDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('drag-over');

        if (!draggedElement) return;

        const rect = e.currentTarget.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        const componentType = draggedElement.dataset.type;
        const componentId = draggedElement.dataset.id;

        createReportComponent(componentType, componentId, x, y);
        draggedElement = null;
    }

    /**
     * ایجاد جزء گزارش
     */
    function createReportComponent(type, id, x, y) {
        componentCounter++;
        const componentKey = `${id}_${componentCounter}`;

        const component = {
            key: componentKey,
            type: type,
            id: id,
            x: snapToGrid(x),
            y: snapToGrid(y),
            width: getDefaultWidth(id),
            height: getDefaultHeight(id),
            properties: getDefaultProperties(id)
        };

        reportComponents.push(component);
        renderComponent(component);
        selectComponent(component);
    }

    /**
     * رندر کردن جزء
     */
    function renderComponent(component) {
        const canvas = document.getElementById('reportCanvas');

        // حذف پیام خالی
        const dropZone = canvas.querySelector('.drop-zone');
        if (dropZone) {
            dropZone.remove();
        }

        const element = document.createElement('div');
        element.className = 'report-component';
        element.id = component.key;
        element.style.left = component.x + 'px';
        element.style.top = component.y + 'px';
        element.style.width = component.width + 'px';
        element.style.height = component.height + 'px';

        element.innerHTML = getComponentHTML(component);

        // اضافه کردن دسته‌های تغییر اندازه
        element.appendChild(createResizeHandle('nw'));
        element.appendChild(createResizeHandle('ne'));
        element.appendChild(createResizeHandle('sw'));
        element.appendChild(createResizeHandle('se'));

        // رویدادها
        element.addEventListener('click', () => selectComponent(component));
        element.addEventListener('mousedown', startDrag);

        canvas.appendChild(element);
    }

    /**
     * دریافت HTML جزء
     */
    function getComponentHTML(component) {
        switch (component.id) {
            case 'title':
                return `<h4 contenteditable="true">${component.properties.text}</h4>`;
            case 'table':
                return `<div class="text-center"><i class="fa fa-table fa-2x"></i><br>جدول داده</div>`;
            case 'chart':
                return `<div class="text-center"><i class="fa fa-chart-bar fa-2x"></i><br>نمودار</div>`;
            case 'map':
                return `<div class="text-center"><i class="fa fa-map fa-2x"></i><br>نقشه</div>`;
            case 'text':
                return `<p contenteditable="true">${component.properties.text}</p>`;
            case 'image':
                return `<div class="text-center"><i class="fa fa-image fa-2x"></i><br>تصویر</div>`;
            default:
                return `<div>${component.id}</div>`;
        }
    }

    /**
     * ایجاد دسته تغییر اندازه
     */
    function createResizeHandle(position) {
        const handle = document.createElement('div');
        handle.className = `resize-handle ${position}`;
        handle.addEventListener('mousedown', startResize);
        return handle;
    }

    /**
     * انتخاب جزء
     */
    function selectComponent(component) {
        // حذف انتخاب قبلی
        document.querySelectorAll('.report-component').forEach(el => {
            el.classList.remove('selected');
        });

        // انتخاب جزء جدید
        const element = document.getElementById(component.key);
        if (element) {
            element.classList.add('selected');
            selectedComponent = component;
            showComponentProperties(component);
        }
    }

    /**
     * نمایش ویژگی‌های جزء
     */
    function showComponentProperties(component) {
        const panel = document.getElementById('propertiesPanel');

        let html = `
            <div class="mb-3">
                <label class="form-label">نوع جزء:</label>
                <input type="text" class="form-control" value="${getComponentDisplayName(component.id)}" readonly>
            </div>
            <div class="row mb-3">
                <div class="col-6">
                    <label class="form-label">X:</label>
                    <input type="number" class="form-control" value="${component.x}" onchange="updateComponentProperty('x', this.value)">
                </div>
                <div class="col-6">
                    <label class="form-label">Y:</label>
                    <input type="number" class="form-control" value="${component.y}" onchange="updateComponentProperty('y', this.value)">
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-6">
                    <label class="form-label">عرض:</label>
                    <input type="number" class="form-control" value="${component.width}" onchange="updateComponentProperty('width', this.value)">
                </div>
                <div class="col-6">
                    <label class="form-label">ارتفاع:</label>
                    <input type="number" class="form-control" value="${component.height}" onchange="updateComponentProperty('height', this.value)">
                </div>
            </div>
        `;

        // ویژگی‌های خاص هر جزء
        if (component.id === 'title' || component.id === 'text') {
            html += `
                <div class="mb-3">
                    <label class="form-label">متن:</label>
                    <textarea class="form-control" rows="3" onchange="updateComponentProperty('text', this.value)">${component.properties.text}</textarea>
                </div>
            `;
        }

        html += `
            <div class="d-grid gap-2">
                <button type="button" class="btn btn-danger btn-sm" onclick="deleteComponent()">
                    <i class="fa fa-trash"></i> حذف جزء
                </button>
            </div>
        `;

        panel.innerHTML = html;
    }

    /**
     * به‌روزرسانی ویژگی جزء
     */
    function updateComponentProperty(property, value) {
        if (!selectedComponent) return;

        if (property === 'text') {
            selectedComponent.properties[property] = value;
        } else {
            selectedComponent[property] = parseInt(value);
        }

        // به‌روزرسانی نمایش
        const element = document.getElementById(selectedComponent.key);
        if (element) {
            if (property === 'x') element.style.left = value + 'px';
            if (property === 'y') element.style.top = value + 'px';
            if (property === 'width') element.style.width = value + 'px';
            if (property === 'height') element.style.height = value + 'px';
            if (property === 'text') element.innerHTML = getComponentHTML(selectedComponent);
        }
    }

    /**
     * حذف جزء
     */
    function deleteComponent() {
        if (!selectedComponent) return;

        const element = document.getElementById(selectedComponent.key);
        if (element) {
            element.remove();
        }

        reportComponents = reportComponents.filter(c => c.key !== selectedComponent.key);
        selectedComponent = null;

        document.getElementById('propertiesPanel').innerHTML = `
            <div class="text-center text-muted">
                <i class="fa fa-cog fa-3x mb-3"></i>
                <p>برای تنظیم ویژگی‌ها، یک جزء را انتخاب کنید.</p>
            </div>
        `;

        // اگر هیچ جزئی نمانده، نمایش پیام خالی
        if (reportComponents.length === 0) {
            const canvas = document.getElementById('reportCanvas');
            canvas.innerHTML = `
                <div class="drop-zone text-center text-muted d-flex align-items-center justify-content-center h-100">
                    <div>
                        <i class="fa fa-plus-circle fa-3x mb-3"></i>
                        <p>اجزای گزارش را از سمت چپ به اینجا بکشید</p>
                    </div>
                </div>
            `;
        }
    }

    /**
     * تنظیم رویدادهای Canvas
     */
    function setupCanvasEvents() {
        const canvas = document.getElementById('reportCanvas');
        canvas.addEventListener('click', function(e) {
            if (e.target === canvas) {
                // کلیک روی فضای خالی - حذف انتخاب
                document.querySelectorAll('.report-component').forEach(el => {
                    el.classList.remove('selected');
                });
                selectedComponent = null;
                document.getElementById('propertiesPanel').innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fa fa-cog fa-3x mb-3"></i>
                        <p>برای تنظیم ویژگی‌ها، یک جزء را انتخاب کنید.</p>
                    </div>
                `;
            }
        });
    }

    /**
     * تنظیم شبکه
     */
    function snapToGrid(value) {
        if (!gridEnabled) return value;
        return Math.round(value / 20) * 20;
    }

    /**
     * تغییر وضعیت شبکه
     */
    function toggleGrid() {
        gridEnabled = !gridEnabled;
        const button = document.getElementById('gridToggle');
        button.classList.toggle('active', gridEnabled);
    }

    // توابع کمکی
    function getDefaultWidth(id) {
        const widths = { title: 300, table: 400, chart: 300, map: 350, text: 250, image: 200 };
        return widths[id] || 200;
    }

    function getDefaultHeight(id) {
        const heights = { title: 50, table: 200, chart: 250, map: 300, text: 100, image: 150 };
        return heights[id] || 100;
    }

    function getDefaultProperties(id) {
        if (id === 'title') return { text: 'عنوان گزارش' };
        if (id === 'text') return { text: 'متن توضیحی' };
        return {};
    }

    function getComponentDisplayName(id) {
        const names = {
            title: 'عنوان', table: 'جدول', chart: 'نمودار',
            map: 'نقشه', text: 'متن', image: 'تصویر'
        };
        return names[id] || id;
    }

    // توابع عملیاتی
    function previewReport() {
        const modal = new bootstrap.Modal(document.getElementById('reportPreviewModal'));

        // تولید پیش‌نمایش
        let previewHTML = '<div style="background: white; padding: 20px; position: relative;">';
        reportComponents.forEach(component => {
            previewHTML += `
                <div style="position: absolute; left: ${component.x}px; top: ${component.y}px; width: ${component.width}px; height: ${component.height}px;">
                    ${getComponentHTML(component)}
                </div>
            `;
        });
        previewHTML += '</div>';

        document.getElementById('reportPreviewContent').innerHTML = previewHTML;
        modal.show();
    }

    function saveReport() { alert('ذخیره گزارش'); }
    function loadReport() { alert('بارگذاری گزارش'); }
    function clearReportBuilder() {
        reportComponents = [];
        selectedComponent = null;
        componentCounter = 0;

        const canvas = document.getElementById('reportCanvas');
        canvas.innerHTML = `
            <div class="drop-zone text-center text-muted d-flex align-items-center justify-content-center h-100">
                <div>
                    <i class="fa fa-plus-circle fa-3x mb-3"></i>
                    <p>اجزای گزارش را از سمت چپ به اینجا بکشید</p>
                </div>
            </div>
        `;

        document.getElementById('propertiesPanel').innerHTML = `
            <div class="text-center text-muted">
                <i class="fa fa-cog fa-3x mb-3"></i>
                <p>برای تنظیم ویژگی‌ها، یک جزء را انتخاب کنید.</p>
            </div>
        `;

        setupDragAndDrop();
    }

    function zoomIn() { alert('بزرگ‌نمایی'); }
    function zoomOut() { alert('کوچک‌نمایی'); }
    function exportReportToPDF() { alert('خروجی PDF'); }
    function printReport() { alert('چاپ گزارش'); }

    // توابع Drag و Resize (ساده‌سازی شده)
    function startDrag(e) {
        if (e.target.classList.contains('resize-handle')) return;
        // پیاده‌سازی drag
    }

    function startResize(e) {
        e.stopPropagation();
        // پیاده‌سازی resize
    }
</script>
<!--end::Report Builder Content-->