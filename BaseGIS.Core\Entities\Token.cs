﻿using System.ComponentModel.DataAnnotations;

namespace BaseGIS.Core.Entities
{
    public class Token
    {
        [Key]
        public int ID { get; set; }

        public string TokenString { get; set; }
        public ApplicationUser User { get; set; }

        public DateTime Expire { get; set; }

        public string IP { get; set; }

        public bool IsDynamic { get; set; }

        public bool IsRequiredMapRequestID { get; set; }

        public bool IsHash { get; set; }



    }
}
