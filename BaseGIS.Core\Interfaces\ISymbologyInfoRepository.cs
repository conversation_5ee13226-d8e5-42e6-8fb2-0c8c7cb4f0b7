using BaseGIS.Core.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BaseGIS.Core.Interfaces
{
    public interface ISymbologyInfoRepository
    {
        Task<SymbologyInfo> GetByIdAsync(int id);
        Task<List<SymbologyInfo>> GetByTableInfoIdAsync(int tableInfoId);
        Task AddAsync(SymbologyInfo symbologyInfo);
        Task UpdateAsync(SymbologyInfo symbologyInfo);
        Task DeleteAsync(int id);
    }
}