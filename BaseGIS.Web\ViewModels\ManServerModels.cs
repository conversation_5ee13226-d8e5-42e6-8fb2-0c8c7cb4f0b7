﻿namespace BaseGIS.Web.ViewModels
{
    public enum GeometryType
    {
        Point,
        Polyline,
        Polygon
    }
    public class Extent
    {
        public double XMin { get; set; }
        public double YMin { get; set; }
        public double XMax { get; set; }
        public double YMax { get; set; }
        public SpatialReference SpatialReference { get; set; }
        public Extent()
        { }

        public Extent(double _xmin, double _ymin, double _xmax, double _ymax, int _wkid, int _latestWkid)
        {
            XMin = _xmin;
            YMin = _ymin;
            XMax = _xmax;
            YMax = _ymax;
            SpatialReference = new SpatialReference();
            SpatialReference.wkid = _wkid;
            SpatialReference.latestWkid = _latestWkid;
        }

    }
    public class SpatialReference
    {
        public int wkid { get; set; }
        public int latestWkid { get; set; }
    }


    public class NodeTree
    {
        public bool active { get; set; }
        public bool checkbox { get; set; }
        public bool expanded { get; set; }

        public bool focus { get; set; }

        public bool folder { get; set; }

        public string key { get; set; }

        public bool lazy { get; set; }

        public string icon { get; set; }

        public bool selected { get; set; }

        public string title { get; set; }

        public string tooltip { get; set; }

        public object data { get; set; }

        public int iconWidth { get; set; }
        public int iconHeight { get; set; }
    }
    public class IdentifyResult
    {
        public int id { get; set; }
        public string GCode { get; set; }
        public int layerId { get; set; }
        public string layerName { get; set; }
        public string value { get; set; }
        public string displayFieldName { get; set; }
        public string content { get; set; }
        public string geometryType { get; set; }
        public Geometry geometry { get; set; }
        public Dictionary<string, object> attributes { get; set; }
        public Properties properties { get; set; }
    }

    public class Geometry
    {
        public SpatialReference spatialReference { get; set; }
        public object rings { get; set; }
        //public List<List<List<double>>> rings { get; set; }
        public List<List<List<double>>> paths { get; set; }
        public List<List<double>> points { get; set; }

        public double x { get; set; }
        public double y { get; set; }
    }

    public class Attributes
    {

    }

    public class Properties
    {

    }
}
