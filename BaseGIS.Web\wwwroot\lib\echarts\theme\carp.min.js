!function(o,e){"function"==typeof define&&define.amd?define(["exports","echarts"],e):"object"==typeof exports&&"string"!=typeof exports.nodeName?e(0,require("echarts/lib/echarts")):e(0,o.echarts)}(this,function(o,e){e?e.registerTheme("carp",{color:["#f0d8A8","#3d1c00","#86b8b1","#f2d694","#fa2a00","#ff8066","#ffd5cc","#f9edd2"],title:{textStyle:{fontWeight:"normal",color:"#f0d8A8"}},visualMap:{color:["#f0d8A8","#3d1c00"]},toolbox:{color:["#f0d8A8","#f0d8A8","#f0d8A8","#f0d8A8"]},tooltip:{backgroundColor:"rgba(0,0,0,0.5)",axisPointer:{type:"line",lineStyle:{color:"#f0d8A8",type:"dashed"},crossStyle:{color:"#f0d8A8"},shadowStyle:{color:"rgba(200,200,200,0.3)"}}},dataZoom:{dataBackgroundColor:"#eee",fillerColor:"rgba(200,200,200,0.2)",handleColor:"#f0d8A8"},timeline:{lineStyle:{color:"#f0dba8"},controlStyle:{color:"#f0dba8",borderColor:"#f0dba8"}},candlestick:{itemStyle:{color:"#3d1c00",color0:"#86b8b1"},lineStyle:{width:1,color:"#fa2a00",color0:"#f2d694"},areaStyle:{color:"#f0d8A8",color0:"#86b8b1"}},map:{itemStyle:{color:"#ddd"},areaStyle:{color:"#86b8b1"},label:{color:"#c12e34"}},graph:{itemStyle:{color:"#3d1c00"},linkStyle:{color:"#f0d8A8"}},gauge:{axisLine:{lineStyle:{color:[[.2,"#3d1c00"],[.8,"#f0d8A8"],[1,"#fa2a00"]],width:8}}}}):"undefined"!=typeof console&&console&&console.error&&console.error("ECharts is not Loaded")});