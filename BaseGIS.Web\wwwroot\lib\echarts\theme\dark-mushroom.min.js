!function(e,o){"function"==typeof define&&define.amd?define(["exports","echarts"],o):"object"==typeof exports&&"string"!=typeof exports.nodeName?o(0,require("echarts/lib/echarts")):o(0,e.echarts)}(this,function(e,o){var l,r,t;o?((t={color:r=["#cc0e00","#ff1a0a","#ff8880","#ffc180","#ffc2b0","#ffffff","#ff8880","#ffe6e6"],backgroundColor:"#333",tooltip:{axisPointer:{lineStyle:{color:l="#eee"},crossStyle:{color:l}}},legend:{textStyle:{color:l}},title:{textStyle:{color:l}},toolbox:{iconStyle:{borderColor:l}},dataZoom:{dataBackgroundColor:"#eee",fillerColor:"rgba(200,200,200,0.2)",handleColor:"#cc0e00"},timeline:{itemStyle:{color:r[1]},lineStyle:{color:l},controlStyle:{color:l,borderColor:l},label:{color:l}},timeAxis:(t=function(){return{axisLine:{lineStyle:{color:l}},axisTick:{lineStyle:{color:l}},axisLabel:{color:l},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:l}}}})(),logAxis:t(),valueAxis:t(),categoryAxis:t(),line:{symbol:"circle"},graph:{color:r},gauge:{axisLine:{lineStyle:{color:[[.2,"#ff1a0a"],[.8,"#cc0e00"],[1,"#ffc2b0"]],width:8}}}}).categoryAxis.splitLine.show=!1,o.registerTheme("dark-mushroom",t)):"undefined"!=typeof console&&console&&console.error&&console.error("ECharts is not Loaded")});