using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using BaseGIS.Core.Entities;

namespace BaseGIS.Infrastructure.Persistence.Configurations
{
    public class TableInfoConfiguration : IEntityTypeConfiguration<TableInfo>
    {
        public void Configure(EntityTypeBuilder<TableInfo> builder)
        {
            builder.HasKey(t => t.Id);
            builder.Property(t => t.Name).IsRequired().HasMaxLength(50);
            builder.Property(t => t.AliasName).IsRequired().HasMaxLength(100);
            builder.Property(t => t.DatasetType).IsRequired().HasMaxLength(20);
            builder.Property(t => t.ShortName).IsRequired().HasMaxLength(3);
            builder.HasOne(t => t.GroupInfo)
                   .WithMany(g => g.TableInfos)
                   .HasForeignKey(t => t.GroupInfoId);
            builder.HasMany(t => t.FieldInfos)
                   .WithOne(f => f.TableInfo)
                   .HasForeignKey(f => f.TableInfoId);
        }
    }
} 