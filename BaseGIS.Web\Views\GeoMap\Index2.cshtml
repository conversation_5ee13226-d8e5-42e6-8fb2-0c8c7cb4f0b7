﻿@model IEnumerable<BaseGIS.Core.Entities.GroupInfo>
@{
    ViewData["Title"] = "نقشه";
    Layout = "~/Views/Shared/_LayoutMap.cshtml";
}

<div class="map-container">
    <!-- سایدبار -->
    <div id="sidebar">
        <div class="sidebar-wrapper">
            <div class="active-layer-container">
                <label for="activeLayerInput" class="form-label">لایه فعال</label>
                <input type="text" id="activeLayerInput" class="form-control" readonly value="هیچ لایه‌ای انتخاب نشده">
            </div>
            <div id="toc"></div>
        </div>
    </div>
    <!-- نقشه -->
    <div id="map" class="map">
        <span class="btn-Sidebar">
            <a id="btnLeftSidebar" href="#" onclick="animateSidebar();" title="باز/بستن سایدبار"><i class="fa fa-angle-double-right fa-2x"></i></a>
        </span>
    </div>
</div>

<!-- مدال تنظیمات لایه -->
<div class="modal fade" id="layerSettingsModal" tabindex="-1" aria-labelledby="layerSettingsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="layerSettingsModalLabel">تنظیمات لایه</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-tabs" id="layerSettingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab" aria-controls="settings" aria-selected="true">تنظیمات</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="symbology-tab" data-bs-toggle="tab" data-bs-target="#symbology" type="button" role="tab" aria-controls="symbology" aria-selected="false">سیمبولوژی</button>
                    </li>
                </ul>
                <div class="tab-content" id="layerSettingsTabContent">
                    <div class="tab-pane fade show active" id="settings" role="tabpanel" aria-labelledby="settings-tab">
                        <div class="mt-3">
                            <button id="zoomToLayerBtn" class="btn btn-primary mb-3">بزرگنمایی به لایه</button>
                            <div class="mb-3">
                                <label for="opacitySlider" class="form-label">شفافیت لایه</label>
                                <input type="range" class="form-range" id="opacitySlider" min="0" max="1" step="0.1" value="0.5">
                            </div>
                            <div class="mb-3">
                                <label for="labelFieldSelect" class="form-label">فیلد برچسب</label>
                                <select class="form-select" id="labelFieldSelect">
                                    <option value="">بدون برچسب</option>
                                </select>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="showLabelsCheckbox">
                                <label class="form-check-label" for="showLabelsCheckbox">نمایش برچسب‌ها</label>
                            </div>
                            <button id="applySettingsBtn" class="btn btn-success">اعمال</button>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="symbology" role="tabpanel" aria-labelledby="symbology-tab">
                        <div class="mt-3">
                            <h6>سیمبولوژی‌های موجود</h6>
                            <div id="symbologyList" class="list-group"></div>
                            <button id="applySymbologyBtn" class="btn btn-success mt-3">اعمال</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">بستن</button>
            </div>
        </div>
    </div>
</div>

<!-- مدال Go to XY -->
<div class="modal fade" id="gotoXYModal" tabindex="-1" aria-labelledby="gotoXYModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="gotoXYModalLabel">رفتن به مختصات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="lngInput" class="form-label">طول جغرافیایی (Longitude)</label>
                    <input type="number" class="form-control" id="lngInput" step="0.0001" placeholder="مثال: 51.3890">
                </div>
                <div class="mb-3">
                    <label for="latInput" class="form-label">عرض جغرافیایی (Latitude)</label>
                    <input type="number" class="form-control" id="latInput" step="0.0001" placeholder="مثال: 35.6892">
                </div>
                <div class="mb-3">
                    <label for="zoomInput" class="form-label">سطح زوم (اختیاری)</label>
                    <input type="number" class="form-control" id="zoomInput" min="0" max="22" step="1" placeholder="مثال: 12">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">بستن</button>
                <button type="button" class="btn btn-primary" id="gotoXYBtn">برو</button>
            </div>
        </div>
    </div>
</div>
@section Styles {
    <link rel="stylesheet" href="~/lib/mapbox-gl/dist/mapbox-gl.css" />
    <link rel="stylesheet" href="~/lib/mapbox-gl-draw/dist/mapbox-gl-draw.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jstree/3.3.16/themes/default/style.min.css" />
    <link rel="stylesheet" href="~/lib/sweetalert2/sweetalert2.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        .map-container {
            display: flex;
            height: calc(100vh - 50px);
            direction: rtl;
        }
         
        #sidebar {
            width: 250px;
            height: 100%;
            transition: width 0.35s ease;
            overflow: hidden;
            background: #fff;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
            
        }

            #sidebar.hidden {
                width: 0;
                display: block;
            }

        .sidebar-wrapper {
            width: 100%;
            height: 100%;
        }

        .active-layer-container {
            padding: 10px;
            border-bottom: 1px solid #ddd;
            background: #f8f9fa;
        }

        #activeLayerInput {
            background-color: #fff;
            cursor: default;
        }

        #toc {
            max-height: calc(100vh - 150px); /* تنظیم ارتفاع با توجه به Textbox */
            overflow-y: auto;
            padding: 10px;
            background-color: var(--background-color);
        }

        #map {
            width: calc(100% - 250px);
            height: 100%;
            position: relative;
            order: 1;
        }

            #map.full-width {
                width: 100%;
            }

        .btn-Sidebar {
            position: absolute;
            z-index: 1000;
            top: 10px;
            border-radius: 5px 0 0 5px;
            height: 34px;
            width: 34px;
            background: white;
            padding: 4px;
            text-align: center;
            line-height: 28px;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            transition: right 0.35s ease;
        }

        #sidebar.hidden + #map .btn-Sidebar {
            right: 0;
        }

        .btn-Sidebar a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .mapboxgl-ctrl-group {
            direction: ltr;
        }

        .mapboxgl-ctrl-top-right {
            right: 4px;
            top: 40px !important;
        }

        .mapboxgl-ctrl-group button {
            width: 30px;
            height: 30px;
            font-size: 16px;
            line-height: 30px;
            text-align: center;
            border-bottom: 1px solid #ddd;
        }

            .mapboxgl-ctrl-group button:last-child {
                border-bottom: none;
            }

            .mapboxgl-ctrl-group button:hover {
                background: #f4f4f4;
            }

            .mapboxgl-ctrl-group button.disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

        .list-group-item-action:hover {
            cursor: pointer;
            background-color: #f8f9fa;
        }

        .symbology-item.active {
            background-color: #e9ecef;
        }

        .symbology-preview {
            width: 20px;
            height: 20px;
            display: inline-block;
            vertical-align: middle;
            margin-right: 10px;
        }

        .jstree-node {
            font-size: 0.9rem;
        }

        .jstree-anchor {
            padding: 0.2rem 0.5rem;
        }

        .mapboxgl-ctrl-custom {
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
            margin: 10px;
        }

            .mapboxgl-ctrl-custom button {
                width: 30px;
                height: 30px;
                border: none;
                background: #fff;
                cursor: pointer;
                display: block;
                font-size: 16px;
                line-height: 30px;
                text-align: center;
                border-bottom: 1px solid #ddd;
            }

                .mapboxgl-ctrl-custom button:last-child {
                    border-bottom: none;
                }

                .mapboxgl-ctrl-custom button:hover {
                    background: #f4f4f4;
                }

        @@media (max-width: 768px) {
            #sidebar

        {
            width: 200px;
        }

        #map {
            width: calc(100% - 200px);
        }

        #sidebar.hidden + #map {
            width: 100%;
        }

        .btn-Sidebar {
            right: 200px;
        }

        }
    </style>
}
@section Scripts {
    <script src="~/lib/mapbox-gl/dist/mapbox-gl.js"></script>
    <script src="~/lib/mapbox-gl-draw/dist/mapbox-gl-draw.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jstree/3.3.16/jstree.min.js"></script>
    <script src="~/lib/sweetalert2/sweetalert2.min.js"></script>
    <script src="~/lib/signalr/dist/browser/signalr.min.js"></script>
    <script src="~/lib/wellknown/wellknown.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        var baseUrl = window.location.origin;
        var map;

        $(document).ready(function () {
            // تنظیم نقشه
            mapboxgl.accessToken = 'none';
            map = new mapboxgl.Map({
                container: 'map',
                style: {
                    version: 8,
                    sources: { 'osm': { type: 'raster', tiles: ['/proxy/osm/{z}/{x}/{y}.png'], tileSize: 256, attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors' } },
                    layers: [{ id: 'osm', type: 'raster', source: 'osm', minzoom: 0, maxzoom: 22 }]
                },
                center: [51.3890, 35.6892],
                zoom: 10
            });

            // افزودن کنترل ناوبری
            var navControl = new mapboxgl.NavigationControl({
                showCompass: true,
                showZoom: true
            });
            map.addControl(navControl, 'top-left');

            // کنترل سفارشی برای Go to XY و Home
            var CustomControl = function () {
                this.onAdd = function (map) {
                    this.map = map;
                    this.container = document.createElement('div');
                    this.container.className = 'mapboxgl-ctrl mapboxgl-ctrl-group mapboxgl-ctrl-custom';
                    var gotoXYBtn = document.createElement('button');
                    gotoXYBtn.innerHTML = '📍';
                    gotoXYBtn.title = 'رفتن به مختصات';
                    gotoXYBtn.onclick = function () {
                        var modal = new bootstrap.Modal(document.getElementById('gotoXYModal'));
                        modal.show();
                    };
                    this.container.appendChild(gotoXYBtn);
                    var homeBtn = document.createElement('button');
                    homeBtn.innerHTML = '🏠';
                    homeBtn.title = 'بازگشت به موقعیت اولیه';
                    homeBtn.onclick = function () {
                        map.flyTo({
                            center: [51.3890, 35.6892],
                            zoom: 10,
                            essential: true
                        });
                    };
                    this.container.appendChild(homeBtn);
                    return this.container;
                };
                this.onRemove = function () {
                    this.container.parentNode.removeChild(this.container);
                    this.map = undefined;
                };
            };
            map.addControl(new CustomControl(), 'top-left');

            // افزودن کنترل‌های ویرایش (Mapbox Draw) و دکمه نمایش نسخه‌ها
            var draw = new MapboxDraw({
                displayControlsDefault: false,
                controls: {
                    point: true,
                    line_string: true,
                    polygon: true,
                    trash: true
                }
            });

            // کنترل سفارشی برای دکمه نمایش نسخه‌ها
            var VersionsControl = function () {
                this.onAdd = function (map) {
                    this.map = map;
                    this.container = document.createElement('div');
                    this.container.className = 'mapboxgl-ctrl mapboxgl-ctrl-group';
                    var versionsBtn = document.createElement('button');
                    versionsBtn.id = 'showVersionsBtn';
                    versionsBtn.innerHTML = '📜';
                    versionsBtn.title = 'نمایش نسخه‌ها';
                    versionsBtn.className = 'disabled';
                    versionsBtn.disabled = true;
                    versionsBtn.onclick = function () {
                        if (activeTableId && selectedFeatureId) {
                            $.ajax({
                                url: `/Feature/Versions/${activeTableId}/${selectedFeatureId}`,
                                type: 'GET',
                                success: function(response) {
                                    if (response.success) showVersionsModal(response.data);
                                    else Swal.fire({ title: 'خطا', text: response.message, icon: 'error', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' });
                                },
                                error: function() { Swal.fire({ title: 'خطا', text: 'خطا در دریافت نسخه‌ها.', icon: 'error', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' }); }
                            });
                        }
                    };
                    this.container.appendChild(versionsBtn);
                    return this.container;
                };
                this.onRemove = function () {
                    this.container.parentNode.removeChild(this.container);
                    this.map = undefined;
                };
            };

            map.addControl(draw, 'top-right');
            map.addControl(new VersionsControl(), 'top-right');

            // مدیریت دکمه Go to XY
            $('#gotoXYBtn').on('click', function () {
                var lng = parseFloat($('#lngInput').val());
                var lat = parseFloat($('#latInput').val());
                var zoom = parseInt($('#zoomInput').val()) || map.getZoom();
                if (isNaN(lng) || isNaN(lat)) {
                    showToast('لطفاً مختصات معتبر وارد کنید.', 'error');
                    return;
                }
                if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
                    showToast('مختصات خارج از محدوده مجاز است.', 'error');
                    return;
                }
                map.flyTo({
                    center: [lng, lat],
                    zoom: zoom,
                    essential: true
                });
                $('#gotoXYModal').modal('hide');
            });

            var sources = {};
            var layers = {};
            var activeTableId = null;
            var selectedFeatureId = null;
            var currentLayerId = null;

            var treeData = [
        @foreach (var group in Model)
        {
            <text>
                            {
                                id: '<EMAIL>',
                                text: '@Html.Raw(group.Name)',
                                type: 'group',
                                children: [
                @foreach (var table in group.TableInfos.Where(t => t.FieldInfos.Any(f => f.FieldType.ToLower().Contains("geometry"))))
                {
                    <text>
                                                {
                                                    id: '<EMAIL>',
                                                    text: '@Html.Raw(table.Name)',
                                                    type: 'table',
                                                    data: { tableId: @table.Id, tableName: '@table.Name' }
                                                },
                    </text>
                }
                                ]
                            },
            </text>
        }
            ];

            $('#toc').jstree({
                core: {
                    data: treeData,
                    themes: { name: 'default', dots: true, icons: true },
                    multiple: true
                },
                checkbox: {
                    keep_selected_style: false,
                    three_state: false,
                    cascade: 'none'
                },
                types: {
                    group: { icon: 'jstree-folder' },
                    table: { icon: 'jstree-layer' }
                },
                plugins: ['checkbox', 'types', 'dnd']
            }).on('ready.jstree', function (e, data) {
                console.log('jsTree initialized:', treeData);
            }).on('changed.jstree', function (e, data) {
                if (data.action === 'select_node' && data.node.data && data.node.data.tableId) {
                    var node = data.node;
                    toggleLayer(node.data.tableId, node.data.tableName, true);
                    activeTableId = node.data.tableId;
                    $('#activeLayerInput').val(node.data.tableName);
                    $('#showVersionsBtn').prop('disabled', true).addClass('disabled');
                    selectedFeatureId = null;
                    loadFeatures(node.data.tableId);
                } else if (data.action === 'deselect_node' && data.node.data && data.node.data.tableId) {
                    var node = data.node;
                    toggleLayer(node.data.tableId, node.data.tableName, false);
                    activeTableId = null;
                    $('#activeLayerInput').val('هیچ لایه‌ای انتخاب نشده');
                    $('#showVersionsBtn').prop('disabled', true).addClass('disabled');
                    selectedFeatureId = null;
                    draw.deleteAll();
                }
            }).on('check_node.jstree uncheck_node.jstree', function (e, data) {
                if (data.node.data && data.node.data.tableId) {
                    toggleLayer(data.node.data.tableId, data.node.data.tableName, e.type === 'check_node');
                }
            }).on('dblclick.jstree', function (e, data) {
                e.preventDefault();
                var instance = $('#toc').jstree(true);
                var node = data.node || instance.get_node(e.target);
                if (node && node.type === 'table' && node.data) {
                    currentLayerId = node.data.tableId;
                    showLayerSettingsModal(node.data.tableId, node.data.tableName || node.text);
                    console.log('Double-clicked node:', node);
                } else {
                    console.log('No valid table node found for double-click:', node);
                }
            });

            function loadFeatures(tableId) {
                $.get(`/Feature/GetFeatures/${tableId}`, function(response) {
                    if (response.success) {
                        draw.deleteAll();
                        draw.add({ type: 'FeatureCollection', features: response.data.map(f => ({ id: f.id, geometry: wktToGeoJson(f.geometry), properties: f.properties })) });
                    }
                });
            }

            function loadPropertyFields(tableId, callback) {
                $.get(`/Feature/GetPropertyFields/${tableId}`, function(response) {
                    if (response.success) {
                        $('#labelFieldSelect').empty().append('<option value="">بدون برچسب</option>');
                        response.data.forEach(field => {
                            $('#labelFieldSelect').append(`<option value="${field}">${field}</option>`);
                        });
                        if (callback) callback();
                    }
                });
            }

            function showLayerSettingsModal(tableId, tableName) {
                $('#layerSettingsModalLabel').text(`تنظیمات لایه: ${tableName}`);
                if (!tableId || tableId === 0) {
                    showToast('شناسه جدول نامعتبر است.', 'error');
                    console.error('Invalid tableId:', tableId);
                    return;
                }
                var layerType = map.getLayer(`fill-${tableId}`) ? 'fill' : map.getLayer(`circle-${tableId}`) ? 'circle' : map.getLayer(`line-${tableId}`) ? 'line' : null;
                if (layerType) {
                    var opacityProperty = layerType === 'fill' ? 'fill-opacity' : layerType === 'circle' ? 'circle-opacity' : 'line-opacity';
                    var currentOpacity = map.getPaintProperty(`${layerType}-${tableId}`, opacityProperty) || 0.5;
                    $('#opacitySlider').val(currentOpacity);
                }
                var labelLayer = map.getLayer(`label-${tableId}`);
                var showLabels = labelLayer != null;
                $('#showLabelsCheckbox').prop('checked', showLabels);
                if (showLabels) {
                    var currentField = map.getLayoutProperty(`label-${tableId}`, 'text-field');
                    if (currentField && currentField[0] === 'get') {
                        var field = currentField[1];
                        loadPropertyFields(tableId, function() { $('#labelFieldSelect').val(field); });
                    } else {
                        loadPropertyFields(tableId);
                    }
                } else {
                    loadPropertyFields(tableId);
                }
                $.ajax({
                    url: `/Feature/GetSymbologies`,
                    type: 'GET',
                    data: { tableId: tableId },
                    success: function(response) {
                        console.log('Symbologies response:', response);
                        $('#symbologyList').empty();
                        if (response.success && response.data && response.data.length > 0) {
                            response.data.forEach(function(s) {
                                var style = JSON.parse(s.json);
                                var previewHtml = '';
                                var displayName = s.name || s.type;
                                if (s.type === 'Simple') {
                                    var color = style.Color || '#FF0000';
                                    previewHtml = `<span class="symbology-preview" style="background-color: ${color};"></span>`;
                                } else if (s.type === 'Unique') {
                                    var firstValue = style.Values && style.Values.length > 0 ? style.Values[0] : null;
                                    var color = firstValue ? firstValue.Color || '#FF0000' : '#FF0000';
                                    displayName += ` (${s.fieldName || 'مقادیر یکتا'})`;
                                    previewHtml = `<span class="symbology-preview" style="background-color: ${color};"></span>`;
                                } else if (s.type === 'Classified') {
                                    var firstClass = style.Classes && style.Classes.length > 0 ? style.Classes[0] : null;
                                    var color = firstClass ? firstClass.Color || '#FF0000' : '#FF0000';
                                    displayName += ` (${s.fieldName || 'کلاسه‌بندی'})`;
                                    previewHtml = `<span class="symbology-preview" style="background-color: ${color};"></span>`;
                                }
                                var encodedJson = encodeURIComponent(JSON.stringify(style));
                                var defaultBadge = s.isDefault ? '<span class="badge bg-success ms-2">پیش‌فرض</span>' : '';
                                var item = $(`<a href="#" class="list-group-item list-group-item-action symbology-item" data-id="${s.id}" data-type="${s.type}" data-json="${encodedJson}" data-field="${s.fieldName || ''}">${previewHtml}${displayName}${defaultBadge}</a>`);
                                if (s.isDefault) item.addClass('active');
                                $('#symbologyList').append(item);
                            });
                            $('.symbology-item').off('click').on('click', function(e) {
                                e.preventDefault();
                                $('.symbology-item').removeClass('active');
                                $(this).addClass('active');
                            });
                        } else {
                            $('#symbologyList').append('<p>سیمبولوژی‌ای تعریف نشده است.</p>');
                            showToast('هیچ سیمبولوژی‌ای یافت نشد.', 'warning');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error fetching symbologies:', xhr.responseText, status, error);
                        showToast('خطا در بارگذاری سیمبولوژی‌ها.', 'error');
                        $('#symbologyList').empty().append('<p>خطا در بارگذاری سیمبولوژی‌ها.</p>');
                    }
                });
                var modal = new bootstrap.Modal(document.getElementById('layerSettingsModal'));
                modal.show();
            }

            function updateLabelLayer() {
                if (currentLayerId) {
                    var showLabels = $('#showLabelsCheckbox').is(':checked');
                    var field = $('#labelFieldSelect').val();
                    var geometryType = map.getLayer(`circle-${currentLayerId}`) ? 'point' : map.getLayer(`line-${currentLayerId}`) ? 'line' : 'fill';
                    if (showLabels && field) {
                        if (!layers[`label-${currentLayerId}`]) {
                            layers[`label-${currentLayerId}`] = {
                                id: `label-${currentLayerId}`,
                                type: 'symbol',
                                source: `source-${currentLayerId}`,
                                'source-layer': 'layer0',
                                minzoom: 10,
                                layout: {
                                    'text-field': ['get', field],
                                    'text-font': ['Open Sans Regular'],
                                    'text-size': 12,
                                    'text-anchor': geometryType === 'point' ? 'top' : 'center',
                                    'text-offset': geometryType === 'point' ? [0, 1] : [0, 0]
                                },
                                paint: {
                                    'text-color': '#000000',
                                    'text-halo-color': '#FFFFFF',
                                    'text-halo-width': 1
                                }
                            };
                            map.addLayer(layers[`label-${currentLayerId}`]);
                        } else {
                            map.setLayoutProperty(`label-${currentLayerId}`, 'text-field', ['get', field]);
                            map.setLayoutProperty(`label-${currentLayerId}`, 'text-anchor', geometryType === 'point' ? 'top' : 'center');
                            map.setLayoutProperty(`label-${currentLayerId}`, 'text-offset', geometryType === 'point' ? [0, 1] : [0, 0]);
                            map.setLayoutProperty(`label-${currentLayerId}`, 'visibility', 'visible');
                        }
                    } else if (layers[`label-${currentLayerId}`]) {
                        map.setLayoutProperty(`label-${currentLayerId}`, 'visibility', 'none');
                    }
                }
            }

            $('#showLabelsCheckbox').on('change', updateLabelLayer);
            $('#labelFieldSelect').on('change', updateLabelLayer);

            $('#applySettingsBtn').on('click', function() {
                showToast('تنظیمات اعمال شد.', 'success');
                $('#layerSettingsModal').modal('hide');
            });

            $('#applySymbologyBtn').on('click', function() {
                var selectedItem = $('.symbology-item.active');
                if (selectedItem.length > 0) {
                    var type = selectedItem.data('type');
                    var encodedJson = selectedItem.data('json');
                    var field = selectedItem.data('field');
                    var layerType = map.getLayer(`fill-${currentLayerId}`) ? 'fill' : map.getLayer(`circle-${currentLayerId}`) ? 'circle' : map.getLayer(`line-${currentLayerId}`) ? 'line' : null;
                    if (!layerType) {
                        showToast('نوع لایه نامعتبر است.', 'error');
                        return;
                    }
                    try {
                        var json = JSON.parse(decodeURIComponent(encodedJson));
                        applySymbology(currentLayerId, layerType, type, json, field);
                        showToast('سیمبولوژی اعمال شد.', 'success');
                    } catch (e) {
                        console.error('JSON parse error:', e, 'Encoded JSON:', encodedJson);
                        showToast('خطا در تجزیه داده‌های سیمبولوژی.', 'error');
                    }
                } else {
                    showToast('لطفاً یک سیمبولوژی انتخاب کنید.', 'warning');
                }
            });

            $('#zoomToLayerBtn').on('click', function() {
                if (currentLayerId) {
                    $.get(`/Feature/GetFeatures/${currentLayerId}`, function(response) {
                        if (response.success && response.data.length > 0) {
                            var coordinates = [];
                            response.data.forEach(f => {
                                var geom = wktToGeoJson(f.geometry);
                                if (geom.coordinates) {
                                    if (geom.type === 'Point') coordinates.push(geom.coordinates);
                                    else if (geom.type === 'Polygon') geom.coordinates[0].forEach(c => coordinates.push(c));
                                    else if (geom.type === 'LineString') geom.coordinates.forEach(c => coordinates.push(c));
                                }
                            });
                            if (coordinates.length > 0) {
                                var bounds = coordinates.reduce(function(bounds, coord) {
                                    return bounds.extend(coord);
                                }, new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]));
                                map.fitBounds(bounds, { padding: 50 });
                            }
                        }
                    });
                }
            });

            $('#opacitySlider').on('input', function() {
                if (currentLayerId) {
                    var layerType = map.getLayer(`fill-${currentLayerId}`) ? 'fill' : map.getLayer(`circle-${currentLayerId}`) ? 'circle' : map.getLayer(`line-${currentLayerId}`) ? 'line' : null;
                    if (layerType) {
                        map.setPaintProperty(`${layerType}-${currentLayerId}`, layerType === 'fill' ? 'fill-opacity' : layerType === 'circle' ? 'circle-opacity' : 'line-opacity', parseFloat($(this).val()));
                    }
                }
            });

            map.on('click', function(e) {
                if (activeTableId) {
                    var features = map.queryRenderedFeatures(e.point, { layers: [`fill-${activeTableId}`, `circle-${activeTableId}`, `line-${activeTableId}`] });
                    if (features.length > 0) {
                        selectedFeatureId = features[0].properties.id;
                        $('#showVersionsBtn').prop('disabled', false).removeClass('disabled');
                    } else {
                        selectedFeatureId = null;
                        $('#showVersionsBtn').prop('disabled', true).addClass('disabled');
                    }
                }
            });

            function applySymbology(tableId, layerType, symType, json, field) {
                var colorProp = layerType === 'fill' ? 'fill-color' : layerType === 'circle' ? 'circle-color' : 'line-color';
                var opacityProp = layerType === 'fill' ? 'fill-opacity' : layerType === 'circle' ? 'circle-opacity' : 'line-opacity';
                if (symType === 0) {
                    var color = json.Color || '#FF0000';
                    var opacity = 1 - (json.Transparency || 0) / 100.0;
                    if (layerType === 'circle') {
                        map.setPaintProperty(`${layerType}-${tableId}`, colorProp, color);
                        map.setPaintProperty(`${layerType}-${tableId}`, opacityProp, opacity);
                        map.setPaintProperty(`${layerType}-${tableId}`, 'circle-radius', json.Size || 5);
                    } else if (layerType === 'line') {
                        map.setPaintProperty(`${layerType}-${tableId}`, colorProp, color);
                        map.setPaintProperty(`${layerType}-${tableId}`, opacityProp, opacity);
                        map.setPaintProperty(`${layerType}-${tableId}`, 'line-width', json.Size || 2);
                    } else if (layerType === 'fill') {
                        map.setPaintProperty(`${layerType}-${tableId}`, colorProp, color);
                        map.setPaintProperty(`${layerType}-${tableId}`, opacityProp, opacity);
                        map.setPaintProperty(`${layerType}-${tableId}`, 'fill-outline-color', color);
                    }
                } else if (symType === 1) {
                    var cases = ['case'];
                    if (json.Values && json.Values.length > 0) {
                        json.Values.forEach(v => {
                            cases.push(['==', ['get', field], v.Value || '']);
                            cases.push(v.Color || '#FF0000');
                        });
                        cases.push(getRandomColor());
                    } else {
                        cases.push('#FFFFFF');
                    }
                    map.setPaintProperty(`${layerType}-${tableId}`, colorProp, cases);
                    var firstOpacity = json.Values && json.Values.length > 0 ? (1 - (json.Values[0].Transparency || 0) / 100.0) : 0.5;
                    map.setPaintProperty(`${layerType}-${tableId}`, opacityProp, firstOpacity);
                    if (layerType === 'circle') {
                        map.setPaintProperty(`${layerType}-${tableId}`, 'circle-radius', json.Values?.[0]?.Size || 5);
                    } else if (layerType === 'line') {
                        map.setPaintProperty(`${layerType}-${tableId}`, 'line-width', json.Values?.[0]?.Size || 2);
                    }
                } else if (symType === 2) {
                    var cases = ['step', ['to-number', ['get', field], 0]];
                    if (json.Classes && json.Classes.length > 0) {
                        json.Classes.forEach(cls => {
                            cases.push(cls.Color || '#FF0000');
                            cases.push(cls.To || Number.MAX_VALUE);
                        });
                    } else {
                        cases.push('#FFFFFF');
                    }
                    map.setPaintProperty(`${layerType}-${tableId}`, colorProp, cases);
                    var firstOpacity = json.Classes && json.Classes.length > 0 ? (1 - (json.Classes[0].Transparency || 0) / 100.0) : 0.5;
                    map.setPaintProperty(`${layerType}-${tableId}`, opacityProp, firstOpacity);
                    if (layerType === 'circle') {
                        map.setPaintProperty(`${layerType}-${tableId}`, 'circle-radius', json.Classes?.[0]?.Size || 5);
                    } else if (layerType === 'line') {
                        map.setPaintProperty(`${layerType}-${tableId}`, 'line-width', json.Classes?.[0]?.Size || 2);
                    }
                }
            }

            function toggleLayer(tableId, tableName, isChecked) {
                if (isChecked) {
                    var tile_url = `${baseUrl}/api/Tile/tile/${tableId}/{z}/{x}/{y}?t=${new Date().getTime()}`;
                    sources[tableId] = { type: 'vector', tiles: [tile_url] };
                    map.addSource(`source-${tableId}`, sources[tableId]);
                    $.ajax({
                        url: '/Feature/GetGeometryType',
                        type: 'GET',
                        data: { tableId: tableId },
                        cache: false,
                        success: function(response) {
                            if (response.success) {
                                var geometryType = response.geometryType.toLowerCase();
                                var layerType, paintProperties;
                                if (geometryType.includes('point')) {
                                    layerType = 'circle';
                                    paintProperties = { 'circle-color': '#FF0000', 'circle-radius': 5, 'circle-opacity': 0.8, 'circle-stroke-color': '#00FF00', 'circle-stroke-width': 1 };
                                } else if (geometryType.includes('linestring')) {
                                    layerType = 'line';
                                    paintProperties = { 'line-color': '#FF0000', 'line-width': 2, 'line-opacity': 0.8 };
                                } else {
                                    layerType = 'fill';
                                    paintProperties = { 'fill-color': '#FF0000', 'fill-opacity': 0.5, 'fill-outline-color': '#FF0000' };
                                }
                                layers[`${layerType}-${tableId}`] = {
                                    id: `${layerType}-${tableId}`,
                                    type: layerType,
                                    source: `source-${tableId}`,
                                    'source-layer': 'layer0',
                                    paint: paintProperties
                                };
                                map.addLayer(layers[`${layerType}-${tableId}`]);
                                $.ajax({
                                    url: '/Feature/GetSymbologies',
                                    type: 'GET',
                                    data: { tableId: tableId },
                                    cache: false,
                                    success: function(response) {
                                        if (response.success && response.data.length > 0) {
                                            var defaultSym = response.data.find(s => s.isDefault);
                                            if (defaultSym) {
                                                var json = JSON.parse(defaultSym.json);
                                                var field = defaultSym.fieldName;
                                                applySymbology(tableId, layerType, defaultSym.type, json, field);
                                            }
                                        }
                                    },
                                    error: function(xhr, status, error) {
                                        showToast('خطا در بارگذاری سیمبولوژی‌ها.', 'error');
                                    }
                                });
                            } else {
                                map.removeSource(`source-${tableId}`);
                                showToast(response.message, 'error');
                            }
                        },
                        error: function() {
                            map.removeSource(`source-${tableId}`);
                            showToast('خطا در دریافت نوع هندسه.', 'error');
                        }
                    });
                } else {
                    var layerType = map.getLayer(`fill-${tableId}`) ? 'fill' : map.getLayer(`circle-${tableId}`) ? 'circle' : map.getLayer(`line-${tableId}`) ? 'line' : null;
                    if (layerType && layers[`${layerType}-${tableId}`]) {
                        map.removeLayer(`${layerType}-${tableId}`);
                        delete layers[`${layerType}-${tableId}`];
                    }
                    if (layers[`label-${tableId}`]) {
                        map.removeLayer(`label-${tableId}`);
                        delete layers[`label-${tableId}`];
                    }
                    if (sources[tableId]) {
                        map.removeSource(`source-${tableId}`);
                        delete sources[tableId];
                    }
                }
            }

            map.on('draw.create', function(e) {
                if (!activeTableId) {
                    Swal.fire({ title: 'خطا', text: 'لطفاً ابتدا یک جدول از TOC انتخاب کنید.', icon: 'error', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' });
                    draw.deleteAll();
                    return;
                }
                var feature = e.features[0];
                showPropertiesForm(feature, function(updatedFeature) { saveFeature(updatedFeature, activeTableId); });
            });

            map.on('draw.update', function(e) {
                var feature = e.features[0];
                if (feature.id) showPropertiesForm(feature, function(updatedFeature) { updateFeature(updatedFeature); });
            });

            map.on('draw.delete', function(e) {
                var feature = e.features[0];
                if (feature.id) deleteFeature(feature.id, activeTableId);
            });

            function showPropertiesForm(feature, callback) {
                Swal.fire({
                    title: 'ویژگی‌های فیچر',
                    html: `<input id="name" class="swal2-input" placeholder="نام" value="${feature.properties?.name || ''}"><input id="status" class="swal2-input" placeholder="وضعیت" value="${feature.properties?.status || ''}">`,
                    confirmButtonText: 'ذخیره',
                    preConfirm: () => {
                        feature.properties = { name: document.getElementById('name').value, status: document.getElementById('status').value };
                        callback(feature);
                    }
                });
            }

            function saveFeature(feature, tableId) {
                $.ajax({
                    url: `/Feature/AddFeature/${tableId}`,
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ Geometry: JSON.stringify(feature.geometry), Properties: feature.properties }),
                    headers: { 'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val() },
                    success: function(response) {
                        if (response.success) {
                            feature.id = response.featureId;
                            draw.add(feature);
                            Swal.fire({ title: 'فیچر اضافه شد', text: 'فیچر با موفقیت ذخیره شد.', icon: 'success', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' });
                        } else {
                            draw.delete(feature.id);
                            Swal.fire({ title: 'خطا', text: response.message, icon: 'error', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' });
                        }
                    },
                    error: function() {
                        draw.delete(feature.id);
                        Swal.fire({ title: 'خطا', text: 'خطا در ذخیره فیچر.', icon: 'error', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' });
                    }
                });
            }

            function updateFeature(feature) {
                $.ajax({
                    url: `/Feature/UpdateFeature/${activeTableId}/${feature.id}`,
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ Geometry: JSON.stringify(feature.geometry), Properties: feature.properties }),
                    headers: { 'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val() },
                    success: function(response) {
                        if (response.success) Swal.fire({ title: 'فیچر آپدیت شد', text: 'فیچر با موفقیت به‌روزرسانی شد.', icon: 'success', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' });
                        else Swal.fire({ title: 'خطا', text: response.message, icon: 'error', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' });
                    },
                    error: function() { Swal.fire({ title: 'خطا', text: 'خطا در آپدیت فیچر.', icon: 'error', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' }); }
                });
            }

            function deleteFeature(featureId, tableId) {
                $.ajax({
                    url: `/Feature/DeleteFeature/${tableId}/${featureId}`,
                    type: 'POST',
                    headers: { 'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val() },
                    success: function(response) {
                        if (response.success) Swal.fire({ title: 'فیچر حذف شد', text: 'فیچر با موفقیت حذف شد.', icon: 'success', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' });
                        else Swal.fire({ title: 'خطا', text: response.message, icon: 'error', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' });
                    },
                    error: function() { Swal.fire({ title: 'خطا', text: 'خطا در حذف فیچر.', icon: 'error', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' }); }
                });
            }

            function showVersionsModal(versions) {
                var html = '<table class="table table-striped"><thead><tr><th>نسخه</th><th>نوع تغییر</th><th>تاریخ</th><th>عملیات</th></tr></thead><tbody>';
                versions.forEach(function(v) {
                    html += `<tr><td>${v.versionNumber}</td><td>${v.changeType}</td><td>${new Date(v.dateTime).toLocaleString('fa-IR')}</td><td><button class="btn btn-sm btn-outline-primary compare-btn" data-table-id="${activeTableId}" data-feature-id="${selectedFeatureId}" data-version="${v.versionNumber}">مقایسه</button><button class="btn btn-sm btn-outline-secondary patch-btn" data-table-id="${activeTableId}" data-feature-id="${selectedFeatureId}" data-version="${v.versionNumber}">بازگردانی</button></td></tr>`;
                });
                html += '</tbody></table>';
                Swal.fire({ title: 'نسخه‌های فیچر', html: html, showConfirmButton: false, showCloseButton: true, width: '800px' });
                $('.compare-btn').on('click', function() {
                    var tableId = $(this).data('table-id');
                    var featureId = $(this).data('feature-id');
                    var version = $(this).data('version');
                    var fromVersion = version - 1 > 0 ? version - 1 : version;
                    $.ajax({
                        url: `/Feature/Diff/${tableId}/${featureId}?fromVersion=${fromVersion}&toVersion=${version}`,
                        type: 'GET',
                        success: function(response) {
                            if (response.success) {
                                var sourceId = `diff-${tableId}-${featureId}`;
                                if (map.getSource(sourceId)) map.removeSource(sourceId);
                                map.addSource(sourceId, { type: 'geojson', data: { type: 'Feature', geometry: wktToGeoJson(response.data.diffWkt) } });
                                map.addLayer({ id: `diff-layer-${tableId}-${featureId}`, type: 'fill', source: sourceId, paint: { 'fill-color': '#ff00ff', 'fill-opacity': 0.3 } });
                                Swal.fire({ title: 'تفاوت نسخه‌ها', text: `تفاوت بین نسخه ${fromVersion} و ${version} نمایش داده شد.`, icon: 'info', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' });
                            } else Swal.fire({ title: 'خطا', text: response.message, icon: 'error', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' });
                        },
                        error: function() { Swal.fire({ title: 'خطا', text: 'خطا در دریافت تفاوت نسخه‌ها.', icon: 'error', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' }); }
                    });
                });
                $('.patch-btn').on('click', function() {
                    var tableId = $(this).data('table-id');
                    var featureId = $(this).data('feature-id');
                    var baseVersion = $(this).data('version');
                    $.ajax({
                        url: `/Feature/GetFeature/${tableId}/${featureId}`,
                        type: 'GET',
                        success: function(response) {
                            if (response.success) {
                                var currentFeature = response.data;
                                $.ajax({
                                    url: `/Feature/Versions/${tableId}/${featureId}`,
                                    type: 'GET',
                                    success: function(vResponse) {
                                        if (vResponse.success) {
                                            var version = vResponse.data.find(v => v.versionNumber === baseVersion);
                                            if (version) {
                                                $.ajax({
                                                    url: `/Feature/Patch/${tableId}/${featureId}?baseVersion=${baseVersion}`,
                                                    type: 'POST',
                                                    contentType: 'application/json',
                                                    data: JSON.stringify({ Geometry: wktToGeoJson(version.geometryWKT), Properties: currentFeature.properties }),
                                                    headers: { 'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val() },
                                                    success: function(pResponse) {
                                                        if (pResponse.success) Swal.fire({ title: 'بازگردانی موفق', text: `فیچر به نسخه ${baseVersion} بازگردانده شد.`, icon: 'success', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' });
                                                        else Swal.fire({ title: 'خطا', text: pResponse.message, icon: 'error', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' });
                                                    },
                                                    error: function() { Swal.fire({ title: 'خطا', text: 'خطا در بازگردانی فیچر.', icon: 'error', confirmButtonText: 'باشه', confirmButtonColor: 'var(--primary-color)' }); }
                                                });
                                            }
                                        }
                                    }
                                });
                            }
                        }
                    });
                });
            }

            var connection = new signalR.HubConnectionBuilder().withUrl('/featuresHub').build();
            function refreshLayer(tableId) {
                if (sources[tableId]) {
                    sources[tableId].tiles = [`${baseUrl}/api/Tile/tile/${tableId}/{z}/{x}/{y}?t=${new Date().getTime()}`];
                    map.getSource(`source-${tableId}`).tiles = sources[tableId].tiles;
                    map.getSource(`source-${tableId}`).load();
                }
            }
            connection.on('FeatureAdded', function(tableId) { refreshLayer(tableId); loadFeatures(tableId); });
            connection.on('FeatureUpdated', function(tableId, featureId) { refreshLayer(tableId); loadFeatures(tableId); });
            connection.on('FeatureDeleted', function(tableId, featureId) { refreshLayer(tableId); loadFeatures(tableId); });
            connection.start().catch(function(err) { console.error(err.toString()); });

            function getRandomColor() {
                var letters = '0123456789ABCDEF';
                var color = '#';
                for (var i = 0; i < 6; i++) color += letters[Math.floor(Math.random() * 16)];
                return color;
            }

            function wktToGeoJson(wkt) { return wellknown.parse(wkt); }

            function showToast(message, type) {
                Swal.fire({ toast: true, position: 'top-end', icon: type, title: message, showConfirmButton: false, timer: 3000, timerProgressBar: true });
            }
        });

        function animateSidebar() {
            $("#sidebar").animate({
                width: "toggle"
            }, 350, function () {
                if ($("#sidebar").is(":visible")) {
                    $("#btnLeftSidebar > i").removeClass("fa-angle-double-left fa-2x").addClass("fa-angle-double-right fa-2x");
                    $("#map").removeClass("full-width");
                } else {
                    $("#btnLeftSidebar > i").removeClass("fa-angle-double-right fa-2x").addClass("fa-angle-double-left fa-2x");
                    $("#map").addClass("full-width");
                }
                map.resize();
            });
        }
    </script>
}